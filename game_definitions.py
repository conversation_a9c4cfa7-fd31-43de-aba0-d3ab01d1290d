"""
植物大战僵尸游戏参数定义
包含内存地址、数据结构、游戏对象等定义
"""

from dataclasses import dataclass
from typing import Dict, List, Any
from enum import Enum

class GameVersion(Enum):
    """游戏版本枚举"""
    ORIGINAL = "original"
    GOTY = "goty"
    STEAM = "steam"

@dataclass
class MemoryAddress:
    """内存地址数据类"""
    name: str
    address: int
    offsets: List[int]
    data_type: str
    description: str
    category: str

@dataclass
class GameEntity:
    """游戏实体数据类"""
    name: str
    base_address: int
    size: int
    fields: Dict[str, Any]

class PVZDefinitions:
    """植物大战僵尸游戏定义类"""
    
    def __init__(self, version: GameVersion = GameVersion.ORIGINAL):
        self.version = version
        self.process_name = "PlantsVsZombies.exe"
        
        # 基础地址偏移（根据游戏版本可能不同）
        self.base_offsets = {
            GameVersion.ORIGINAL: {
                "game_base": 0x6A9EC0,
                "board_base": 0x768,
                "plant_array": 0xAC,
                "zombie_array": 0x90,
                "projectile_array": 0x120,
                "coin_array": 0xE4,
                "lawn_mower_array": 0x100,
            }
        }
        
        # 游戏状态地址
        self.game_addresses = {
            "sun_count": MemoryAddress(
                name="sun_count",
                address=0x6A9EC0,
                offsets=[0x768, 0x5560],
                data_type="int32",
                description="阳光数量",
                category="resources"
            ),
            "money": MemoryAddress(
                name="money",
                address=0x6A9EC0,
                offsets=[0x82C, 0x28],
                data_type="int32",
                description="金钱数量",
                category="resources"
            ),
            "level": MemoryAddress(
                name="level",
                address=0x6A9EC0,
                offsets=[0x768, 0x160],
                data_type="int32",
                description="当前关卡",
                category="game_state"
            ),
            "wave_count": MemoryAddress(
                name="wave_count",
                address=0x6A9EC0,
                offsets=[0x768, 0x557C],
                data_type="int32",
                description="当前波数",
                category="game_state"
            ),
            "game_paused": MemoryAddress(
                name="game_paused",
                address=0x6A9EC0,
                offsets=[0x768, 0x164],
                data_type="bool",
                description="游戏是否暂停",
                category="game_state"
            ),
            "plant_count": MemoryAddress(
                name="plant_count",
                address=0x6A9EC0,
                offsets=[0x768, 0xB0],
                data_type="int32",
                description="植物数量",
                category="entities"
            ),
            "zombie_count": MemoryAddress(
                name="zombie_count",
                address=0x6A9EC0,
                offsets=[0x768, 0x94],
                data_type="int32",
                description="僵尸数量",
                category="entities"
            ),
        }
        
        # 植物数据结构定义
        self.plant_structure = {
            "x": {"offset": 0x08, "type": "int32", "description": "X坐标"},
            "y": {"offset": 0x0C, "type": "int32", "description": "Y坐标"},
            "type": {"offset": 0x24, "type": "int32", "description": "植物类型"},
            "health": {"offset": 0x40, "type": "int32", "description": "生命值"},
            "max_health": {"offset": 0x44, "type": "int32", "description": "最大生命值"},
            "state": {"offset": 0x3C, "type": "int32", "description": "植物状态"},
            "animation_frame": {"offset": 0x94, "type": "int32", "description": "动画帧"},
            "visible": {"offset": 0x18, "type": "bool", "description": "是否可见"},
        }
        
        # 僵尸数据结构定义
        self.zombie_structure = {
            "x": {"offset": 0x08, "type": "float", "description": "X坐标"},
            "y": {"offset": 0x0C, "type": "float", "description": "Y坐标"},
            "type": {"offset": 0x24, "type": "int32", "description": "僵尸类型"},
            "health": {"offset": 0xC8, "type": "int32", "description": "生命值"},
            "max_health": {"offset": 0xCC, "type": "int32", "description": "最大生命值"},
            "state": {"offset": 0x28, "type": "int32", "description": "僵尸状态"},
            "speed": {"offset": 0x34, "type": "float", "description": "移动速度"},
            "visible": {"offset": 0x18, "type": "bool", "description": "是否可见"},
            "has_item": {"offset": 0xD0, "type": "int32", "description": "携带物品"},
        }
        
        # 子弹数据结构定义
        self.projectile_structure = {
            "x": {"offset": 0x08, "type": "float", "description": "X坐标"},
            "y": {"offset": 0x0C, "type": "float", "description": "Y坐标"},
            "type": {"offset": 0x38, "type": "int32", "description": "子弹类型"},
            "damage": {"offset": 0x40, "type": "int32", "description": "伤害值"},
            "speed": {"offset": 0x44, "type": "float", "description": "移动速度"},
            "visible": {"offset": 0x18, "type": "bool", "description": "是否可见"},
        }
        
        # 植物类型定义
        self.plant_types = {
            0: "豌豆射手",
            1: "向日葵",
            2: "樱桃炸弹",
            3: "坚果墙",
            4: "土豆雷",
            5: "寒冰射手",
            6: "大嘴花",
            7: "双发射手",
            8: "小喷菇",
            9: "阳光菇",
            10: "大喷菇",
            11: "墓碑吞噬者",
            12: "魅惑菇",
            13: "胆小菇",
            14: "寒冰菇",
            15: "毁灭菇",
            16: "睡莲",
            17: "窝瓜",
            18: "三线射手",
            19: "缠绕海草",
            20: "火爆辣椒",
            21: "地刺",
            22: "火炬树桩",
            23: "高坚果",
            24: "海蘑菇",
            25: "路灯花",
            26: "仙人掌",
            27: "三叶草",
            28: "裂荚射手",
            29: "杨桃",
            30: "南瓜头",
            31: "磁力菇",
            32: "卷心菜投手",
            33: "花盆",
            34: "玉米投手",
            35: "咖啡豆",
            36: "大蒜",
            37: "叶子保护伞",
            38: "金盏花",
            39: "西瓜投手",
            40: "机枪射手",
            41: "双子向日葵",
            42: "忧郁菇",
            43: "香蒲",
            44: "冰瓜",
            45: "吸金磁",
            46: "地刺王",
            47: "玉米加农炮",
            48: "模仿者",
        }
        
        # 僵尸类型定义
        self.zombie_types = {
            0: "普通僵尸",
            1: "旗帜僵尸",
            2: "路障僵尸",
            3: "撑杆僵尸",
            4: "铁桶僵尸",
            5: "读报僵尸",
            6: "铁栅门僵尸",
            7: "橄榄球僵尸",
            8: "舞蹈僵尸",
            9: "伴舞僵尸",
            10: "鸭子救生圈僵尸",
            11: "潜水僵尸",
            12: "雪橇车僵尸",
            13: "雪橇僵尸小队",
            14: "海豚骑士僵尸",
            15: "玩偶匣僵尸",
            16: "气球僵尸",
            17: "挖地僵尸",
            18: "跳跳僵尸",
            19: "雪人僵尸",
            20: "蹦极僵尸",
            21: "扶梯僵尸",
            22: "投石车僵尸",
            23: "巨人僵尸",
            24: "小鬼僵尸",
            25: "僵王博士",
        }
        
        # 游戏状态定义
        self.game_states = {
            0: "主菜单",
            1: "选择关卡",
            2: "游戏中",
            3: "暂停",
            4: "胜利",
            5: "失败",
            6: "商店",
            7: "禅境花园",
        }
    
    def get_address_by_name(self, name: str) -> MemoryAddress:
        """根据名称获取内存地址"""
        return self.game_addresses.get(name)
    
    def get_addresses_by_category(self, category: str) -> List[MemoryAddress]:
        """根据分类获取内存地址列表"""
        return [addr for addr in self.game_addresses.values() if addr.category == category]
    
    def get_plant_type_name(self, plant_type: int) -> str:
        """获取植物类型名称"""
        return self.plant_types.get(plant_type, f"未知植物({plant_type})")
    
    def get_zombie_type_name(self, zombie_type: int) -> str:
        """获取僵尸类型名称"""
        return self.zombie_types.get(zombie_type, f"未知僵尸({zombie_type})")
    
    def get_all_categories(self) -> List[str]:
        """获取所有分类"""
        categories = set()
        for addr in self.game_addresses.values():
            categories.add(addr.category)
        return sorted(list(categories))
