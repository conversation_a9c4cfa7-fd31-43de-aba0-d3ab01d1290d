!(function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={exports:{},id:r,loaded:!1};return e[r].call(o.exports,o,o.exports,t),o.loaded=!0,o.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(module,exports,__webpack_require__){var opt=__webpack_require__(1),util=__webpack_require__(2),report=__webpack_require__(8),type=["click","mouseover"],startTime=new Date,_xla={push:function(e){"pv"===e.type||"pageview"===e.type?report({ii:e.type,pu:e.url||location.href,force:e.force}):"event"===e.type?report({ii:e.type,cg:e.category,at:e.action,ed:e.extdata,force:e.force}):"tp"===e.type?report({ii:e.type,tp:e.tp,force:e.force}):"globalExtData"===e.type?opt.extend(e.data):"config"===e.type&&opt.config(e.appid,e.secret,e.event,e.force)}};if(util.documentReady(function(){for(var i=0;i<opt.event.length;i++)util.delegate(document,opt.event[i],"[xla-action="+opt.event[i]+"]",function(){_xla.push({type:"event",category:this.getAttribute("xla-category"),action:this.getAttribute("xla-action"),extdata:eval("("+this.getAttribute("xla-extdata")+")")})})}),util.on(window,"unload",function(){_xla.push({type:"tp",tp:new Date-startTime,force:!0})}),window.xla instanceof Array)for(var i=0;i<window.xla.length;i++)_xla.push(window.xla[i]);_xla.push({type:"pv"}),window.xla=_xla},function(e,t,n){var r=n(2),o=e.exports={v:"1.0.1",furl:r.getParameter("debug")?"//test-etl-xlmc-ssl.xunlei.com/api/stat/rt/js":"//etl-xlmc-ssl.xunlei.com/api/stat/rt/js",appid:null,s:null,comm:{ai:null,dm:window.realdomain||document.domain,ul:document.URL,tt:document.title,sn:window.screen.width+"x"+window.screen.height,cd:window.screen.colorDepth,rr:document.referrer,rf:r.getReferfrom()||"",ua:navigator.userAgent,lg:navigator.language,ci:r.getCookieId()},extend:function(e){return o.comm=r.deepExtend({},o.comm,e),o.comm},config:function(e,t,n,r){this.appid=e,this.comm.ai=e,this.s=t,"boolean"==typeof r&&(this.force=r),n&&(this.event=this.event.concat(n))},event:["click"],debug:r.getParameter("debug"),force:!0}},function(e,t,n){var r=n(3),o=n(1),i=n(7),a=e.exports={getReferfrom:function(){var e=a.getParameter("referfrom");return e?i.set(document.domain,"XLA_RF"+o.appid,e,24):e=i.get("XLA_RF"+o.appid),e},getCookieId:function(){var e=i.get("XLA_CI");if(!e){var t=Math.random(),n=navigator.appName+"_"+navigator.appVersion+"_"+navigator.userAgent+"_"+navigator.appCodeName+"_"+navigator.platform,o=(new Date).valueOf();e=r(o+n+t),i.set(document.domain,"XLA_CI",e,24)}return e},getParameter:function(e){var t=new RegExp("[?&]"+e+"=([^&#]+)","g");try{var n=t.exec(document.location.href)}catch(e){}return null!=n?decodeURIComponent(n[1]):null},deepExtend:function(e){e=e||{};for(var t=1;t<arguments.length;t++){var n=arguments[t];if(n)for(var r in n)n.hasOwnProperty(r)&&("object"==typeof n[r]&&null!==n[r]?e[r]=this.deepExtend(e[r],n[r]):e[r]=n[r])}return e},print:{msg:function(e){window.console&&window.console.info&&console.info(e)},err:function(e){window.console&&window.console.error&&console.error(e)}},on:function(e,t,n){e.addEventListener?e.addEventListener(t,n):e.attachEvent("on"+t,function(){n.call(e)})},delegate:function(e,t,n,r){for(var o=this,i=t.split(","),a=0;a<i.length;a++)this.on(e,i[a],function(e){e=e||window.event;var t=e.target||e.srcElement;o.bubble(t,n,r)})},bubble:function(e,t,n){var r=t.substr(1,t.length-2).split("="),o=r[0],i=r[1];e&&e.getAttribute&&e.getAttribute(o)===i&&n.call(e),e&&e!==document&&this.bubble(e.parentNode,t,n)},imageBeacon:function(e,t,n){var r=new Image;r.onload=t,r.onerror=n, e = 'http:' + e, r.src=e},documentReady:function(e){"loading"!==document.readyState?e():window.addEventListener?window.addEventListener("DOMContentLoaded",e):window.attachEvent("onreadystatechange",function(){"loading"!=document.readyState&&e()})}}},function(e,t,n){!function(){var t=n(4),r=n(5).utf8,o=n(6),i=n(5).bin,a=function(e,n){e.constructor==String?e=n&&"binary"===n.encoding?i.stringToBytes(e):r.stringToBytes(e):o(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||(e=e.toString());for(var u=t.bytesToWords(e),c=8*e.length,f=1732584193,p=-271733879,s=-1732584194,l=271733878,g=0;g<u.length;g++)u[g]=16711935&(u[g]<<8|u[g]>>>24)|4278255360&(u[g]<<24|u[g]>>>8);u[c>>>5]|=128<<c%32,u[(c+64>>>9<<4)+14]=c;for(var d=a._ff,v=a._gg,h=a._hh,y=a._ii,g=0;g<u.length;g+=16){var m=f,x=p,b=s,w=l;f=d(f,p,s,l,u[g+0],7,-680876936),l=d(l,f,p,s,u[g+1],12,-389564586),s=d(s,l,f,p,u[g+2],17,606105819),p=d(p,s,l,f,u[g+3],22,-1044525330),f=d(f,p,s,l,u[g+4],7,-176418897),l=d(l,f,p,s,u[g+5],12,1200080426),s=d(s,l,f,p,u[g+6],17,-1473231341),p=d(p,s,l,f,u[g+7],22,-45705983),f=d(f,p,s,l,u[g+8],7,1770035416),l=d(l,f,p,s,u[g+9],12,-1958414417),s=d(s,l,f,p,u[g+10],17,-42063),p=d(p,s,l,f,u[g+11],22,-1990404162),f=d(f,p,s,l,u[g+12],7,1804603682),l=d(l,f,p,s,u[g+13],12,-40341101),s=d(s,l,f,p,u[g+14],17,-1502002290),p=d(p,s,l,f,u[g+15],22,1236535329),f=v(f,p,s,l,u[g+1],5,-165796510),l=v(l,f,p,s,u[g+6],9,-1069501632),s=v(s,l,f,p,u[g+11],14,643717713),p=v(p,s,l,f,u[g+0],20,-373897302),f=v(f,p,s,l,u[g+5],5,-701558691),l=v(l,f,p,s,u[g+10],9,38016083),s=v(s,l,f,p,u[g+15],14,-660478335),p=v(p,s,l,f,u[g+4],20,-405537848),f=v(f,p,s,l,u[g+9],5,568446438),l=v(l,f,p,s,u[g+14],9,-1019803690),s=v(s,l,f,p,u[g+3],14,-187363961),p=v(p,s,l,f,u[g+8],20,1163531501),f=v(f,p,s,l,u[g+13],5,-1444681467),l=v(l,f,p,s,u[g+2],9,-51403784),s=v(s,l,f,p,u[g+7],14,1735328473),p=v(p,s,l,f,u[g+12],20,-1926607734),f=h(f,p,s,l,u[g+5],4,-378558),l=h(l,f,p,s,u[g+8],11,-2022574463),s=h(s,l,f,p,u[g+11],16,1839030562),p=h(p,s,l,f,u[g+14],23,-35309556),f=h(f,p,s,l,u[g+1],4,-1530992060),l=h(l,f,p,s,u[g+4],11,1272893353),s=h(s,l,f,p,u[g+7],16,-155497632),p=h(p,s,l,f,u[g+10],23,-1094730640),f=h(f,p,s,l,u[g+13],4,681279174),l=h(l,f,p,s,u[g+0],11,-358537222),s=h(s,l,f,p,u[g+3],16,-722521979),p=h(p,s,l,f,u[g+6],23,76029189),f=h(f,p,s,l,u[g+9],4,-640364487),l=h(l,f,p,s,u[g+12],11,-421815835),s=h(s,l,f,p,u[g+15],16,530742520),p=h(p,s,l,f,u[g+2],23,-995338651),f=y(f,p,s,l,u[g+0],6,-198630844),l=y(l,f,p,s,u[g+7],10,1126891415),s=y(s,l,f,p,u[g+14],15,-1416354905),p=y(p,s,l,f,u[g+5],21,-57434055),f=y(f,p,s,l,u[g+12],6,1700485571),l=y(l,f,p,s,u[g+3],10,-1894986606),s=y(s,l,f,p,u[g+10],15,-1051523),p=y(p,s,l,f,u[g+1],21,-2054922799),f=y(f,p,s,l,u[g+8],6,1873313359),l=y(l,f,p,s,u[g+15],10,-30611744),s=y(s,l,f,p,u[g+6],15,-1560198380),p=y(p,s,l,f,u[g+13],21,1309151649),f=y(f,p,s,l,u[g+4],6,-145523070),l=y(l,f,p,s,u[g+11],10,-1120210379),s=y(s,l,f,p,u[g+2],15,718787259),p=y(p,s,l,f,u[g+9],21,-343485551),f=f+m>>>0,p=p+x>>>0,s=s+b>>>0,l=l+w>>>0}return t.endian([f,p,s,l])};a._ff=function(e,t,n,r,o,i,a){var u=e+(t&n|~t&r)+(o>>>0)+a;return(u<<i|u>>>32-i)+t},a._gg=function(e,t,n,r,o,i,a){var u=e+(t&r|n&~r)+(o>>>0)+a;return(u<<i|u>>>32-i)+t},a._hh=function(e,t,n,r,o,i,a){var u=e+(t^n^r)+(o>>>0)+a;return(u<<i|u>>>32-i)+t},a._ii=function(e,t,n,r,o,i,a){var u=e+(n^(t|~r))+(o>>>0)+a;return(u<<i|u>>>32-i)+t},a._blocksize=16,a._digestsize=16,e.exports=function(e,n){if(void 0===e||null===e)throw new Error("Illegal argument "+e);var r=t.wordsToBytes(a(e,n));return n&&n.asBytes?r:n&&n.asString?i.bytesToString(r):t.bytesToHex(r)}}()},function(e,t){!function(){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var o=e[r]<<16|e[r+1]<<8|e[r+2],i=0;i<4;i++)8*r+6*i<=8*e.length?n.push(t.charAt(o>>>6*(3-i)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,o=0;r<e.length;o=++r%4)0!=o&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*o+8)-1)<<2*o|t.indexOf(e.charAt(r))>>>6-2*o);return n}};e.exports=n}()},function(e,t){var n={utf8:{stringToBytes:function(e){return n.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(n.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}};e.exports=n},function(e,t){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}function r(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}e.exports=function(e){return null!=e&&(n(e)||r(e)||!!e._isBuffer)}},function(e,t){e.exports={get:function(e){for(var t,n={},r=document.cookie.split("; "),o=0;o<r.length;o++)t=r[o].split("="),n[t[0]]=t[1];return e?n[e]:n},set:function(e,t,n,r){if(r){var o=new Date((new Date).getTime()+36e5*r);document.cookie=t+"="+escape(n)+";path=/;domain="+e+";expires="+o.toGMTString()}else document.cookie=t+"="+escape(n)+";path=/;domain="+e}}},function(e,t,n){function r(e){a.imageBeacon(e,function(){i.debug&&a.print.msg("Report success: "+e+":)")},function(){i.debug&&a.print.err("Report error: "+e+":(")})}var o=n(3),i=n(1),a=n(2),u="JSON"in window?JSON:n(9),c=[],f=function(e){var t,n,r,c,f=i.furl;return t=1===e.length?a.deepExtend({},i.comm,e[0]):a.deepExtend({},i.comm,{il:e}),t=u.stringify(t),r="appId="+i.appid+"log="+t+"v="+i.v,c=o(r+i.s),n="appId="+i.appid+"&log="+encodeURIComponent(t)+"&v="+i.v+"&sig="+c,f+"?"+n},p="",s=8192;e.exports=function(e){var t;t=e.force===!1?e.force:i.force,delete e.force,c.push(a.deepExtend({},e,{tm:Math.round((new Date).getTime()/1e3)})),i.debug&&a.print.msg("Report has been push into queue: "+u.stringify(e));var n=f(c);n.length>s?1===c.length?(a.print.err("Report error: your single report is too long."),c=[]):(r(p),c.splice(0,c.length-1),t&&r(f(c))):t?(r(n),c=[]):p=n}},function(e,t,n){var r=n(10),o=n(11);r.decycle=o.decycle,r.retrocycle=o.retrocycle,e.exports=r},function(module,exports,__webpack_require__){!function(JSON){"use strict";function f(e){return e<10?"0"+e:e}function quote(e){return escapable.lastIndex=0,escapable.test(e)?'"'+e.replace(escapable,function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var n,r,o,i,a,u=gap,c=t[e];switch(c&&"object"==typeof c&&"function"==typeof c.toJSON&&(c=c.toJSON(e)),"function"==typeof rep&&(c=rep.call(t,e,c)),typeof c){case"string":return quote(c);case"number":return isFinite(c)?String(c):"null";case"boolean":case"null":return String(c);case"object":if(!c)return"null";if(gap+=indent,a=[],"[object Array]"===Object.prototype.toString.apply(c)){for(i=c.length,n=0;n<i;n+=1)a[n]=str(n,c)||"null";return o=0===a.length?"[]":gap?"[\n"+gap+a.join(",\n"+gap)+"\n"+u+"]":"["+a.join(",")+"]",gap=u,o}if(rep&&"object"==typeof rep)for(i=rep.length,n=0;n<i;n+=1)"string"==typeof rep[n]&&(r=rep[n],o=str(r,c),o&&a.push(quote(r)+(gap?": ":":")+o));else for(r in c)Object.prototype.hasOwnProperty.call(c,r)&&(o=str(r,c),o&&a.push(quote(r)+(gap?": ":":")+o));return o=0===a.length?"{}":gap?"{\n"+gap+a.join(",\n"+gap)+"\n"+u+"}":"{"+a.join(",")+"}",gap=u,o}}var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;"function"!=typeof JSON.stringify&&(JSON.stringify=function(e,t,n){var r;if(gap="",indent="","number"==typeof n)for(r=0;r<n;r+=1)indent+=" ";else"string"==typeof n&&(indent=n);if(rep=t,t&&"function"!=typeof t&&("object"!=typeof t||"number"!=typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){function walk(e,t){var n,r,o=e[t];if(o&&"object"==typeof o)for(n in o)Object.prototype.hasOwnProperty.call(o,n)&&(r=walk(o,n),void 0!==r?o[n]=r:delete o[n]);return reviver.call(e,t,o)}var j;if(text=String(text),cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}(exports)},function(module,exports,__webpack_require__){!function(exports){"function"!=typeof exports.decycle&&(exports.decycle=function(e){"use strict";var t=[],n=[];return function e(r,o){var i,a,u;switch(typeof r){case"object":if(!r)return null;for(i=0;i<t.length;i+=1)if(t[i]===r)return{$ref:n[i]};if(t.push(r),n.push(o),"[object Array]"===Object.prototype.toString.apply(r))for(u=[],i=0;i<r.length;i+=1)u[i]=e(r[i],o+"["+i+"]");else{u={};for(a in r)Object.prototype.hasOwnProperty.call(r,a)&&(u[a]=e(r[a],o+"["+JSON.stringify(a)+"]"))}return u;case"number":case"string":case"boolean":return r}}(e,"$")}),"function"!=typeof exports.retrocycle&&(exports.retrocycle=function retrocycle($){"use strict";var px=/^\$(?:\[(?:\d+|\"(?:[^\\\"\u0000-\u001f]|\\([\\\"\/bfnrt]|u[0-9a-zA-Z]{4}))*\")\])*$/;return function rez(value){var i,item,name,path;if(value&&"object"==typeof value)if("[object Array]"===Object.prototype.toString.apply(value))for(i=0;i<value.length;i+=1)item=value[i],item&&"object"==typeof item&&(path=item.$ref,"string"==typeof path&&px.test(path)?value[i]=eval(path):rez(item));else for(name in value)"object"==typeof value[name]&&(item=value[name],item&&(path=item.$ref,"string"==typeof path&&px.test(path)?value[name]=eval(path):rez(item)))}($),$})}(exports)}]));// eslint-disable-line
// # sourceMappingURL=xla.min.js.map
