module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(r,a,function(t){return e[t]}.bind(null,a));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=109)}([function(e,t,n){"use strict";Object.defineProperty(t,"LEVEL",{value:Symbol.for("level")}),Object.defineProperty(t,"MESSAGE",{value:Symbol.for("message")}),Object.defineProperty(t,"SPLAT",{value:Symbol.for("splat")}),Object.defineProperty(t,"configs",{value:n(128)})},function(e,t){e.exports=require("util")},function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(e){var t="function"==typeof Map?new Map:void 0;return(i=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return o(e,arguments,l(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),s(r,e)})(e)}function o(e,t,n){return(o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var a=new(Function.bind.apply(e,r));return n&&s(a,n.prototype),a}).apply(null,arguments)}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function l(e){return(l=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var u=function(e){function t(e){var n,i,o;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),i=this,n=!(o=l(t).call(this,"Format functions must be synchronous taking a two arguments: (info, opts)\nFound: ".concat(e.toString().split("\n")[0],"\n")))||"object"!==r(o)&&"function"!=typeof o?a(i):o,Error.captureStackTrace(a(a(n)),t),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&s(e,t)}(t,i(Error)),t}();e.exports=function(e){if(e.length>2)throw new u(e);function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.options=e}function n(e){return new t(e)}return t.prototype.transform=e,n.Format=t,n}},function(e,t,n){"use strict";class r extends Error{constructor(e){super(`Format functions must be synchronous taking a two arguments: (info, opts)\nFound: ${e.toString().split("\n")[0]}\n`),Error.captureStackTrace(this,r)}}e.exports=(e=>{if(e.length>2)throw new r(e);function t(e={}){this.options=e}function n(e){return new t(e)}return t.prototype.transform=e,n.Format=t,n})},function(e,t,n){"use strict";const r=t.format=n(3);function a(e,t){t=t||e,Object.defineProperty(r,e,{get:()=>n(132)(`./${t}.js`),configurable:!0})}t.levels=n(30),a("align"),a("errors"),a("cli"),a("combine"),a("colorize"),a("json"),a("label"),a("logstash"),a("metadata"),a("ms"),a("padLevels","pad-levels"),a("prettyPrint","pretty-print"),a("printf"),a("simple"),a("splat"),a("timestamp"),a("uncolorize")},function(e,t,n){try{var r=n(1);if("function"!=typeof r.inherits)throw"";e.exports=r.inherits}catch(t){e.exports=n(147)}},function(e,t){e.exports=require("os")},function(e,t){e.exports=r,r.default=r,r.stable=i,r.stableStringify=i;var n=[];function r(e,t,r){!function e(t,r,a,i){var o;if("object"==typeof t&&null!==t){for(o=0;o<a.length;o++)if(a[o]===t)return i[r]="[Circular]",void n.push([i,r,t]);if(a.push(t),Array.isArray(t))for(o=0;o<t.length;o++)e(t[o],o,a,t);else{var s=Object.keys(t);for(o=0;o<s.length;o++){var l=s[o];e(t[l],l,a,t)}}a.pop()}}(e,"",[],void 0);for(var a=JSON.stringify(e,t,r);0!==n.length;){var i=n.pop();i[0][i[1]]=i[2]}return a}function a(e,t){return e<t?-1:e>t?1:0}function i(e,t,r){for(var i=function e(t,r,i,o){var s;if("object"==typeof t&&null!==t){for(s=0;s<i.length;s++)if(i[s]===t)return o[r]="[Circular]",void n.push([o,r,t]);if("function"==typeof t.toJSON)return;if(i.push(t),Array.isArray(t))for(s=0;s<t.length;s++)e(t[s],s,i,t);else{var l={},u=Object.keys(t).sort(a);for(s=0;s<u.length;s++){var c=u[s];e(t[c],c,i,t),l[c]=t[c]}if(void 0===o)return l;n.push([o,r,t]),o[r]=l}i.pop()}}(e,"",[],void 0)||e,o=JSON.stringify(i,t,r);0!==n.length;){var s=n.pop();s[0][s[1]]=s[2]}return o}},function(e,t,n){"use strict";const r=n(1),a=n(146),{LEVEL:i}=n(0),o=e.exports=function(e={}){a.call(this,{objectMode:!0,highWaterMark:e.highWaterMark}),this.format=e.format,this.level=e.level,this.handleExceptions=e.handleExceptions,this.silent=e.silent,e.log&&(this.log=e.log),e.logv&&(this.logv=e.logv),e.close&&(this.close=e.close),this.once("pipe",e=>{this.levels=e.levels,this.parent=e}),this.once("unpipe",e=>{e===this.parent&&(this.parent=null,this.close&&this.close())})};r.inherits(o,a),o.prototype._write=function(e,t,n){if(this.silent||!0===e.exception&&!this.handleExceptions)return n(null);const r=this.level||this.parent&&this.parent.level;if(!r||this.levels[r]>=this.levels[e[i]]){if(e&&!this.format)return this.log(e,n);let t,r;try{r=this.format.transform(Object.assign({},e),this.format.options)}catch(e){t=e}if(t||!r){if(n(),t)throw t;return}return this.log(r,n)}return n(null)},o.prototype._writev=function(e,t){if(this.logv){const n=e.filter(this._accept,this);return n.length?this.logv(n,t):t(null)}for(let n=0;n<e.length;n++){if(!this._accept(e[n]))continue;if(e[n].chunk&&!this.format){this.log(e[n].chunk,e[n].callback);continue}let r,a;try{a=this.format.transform(Object.assign({},e[n].chunk),this.format.options)}catch(e){r=e}if(r||!a){if(e[n].callback(),r)throw t(null),r}else this.log(a,e[n].callback)}return t(null)},o.prototype._accept=function(e){const t=e.chunk;if(this.silent)return!1;const n=this.level||this.parent&&this.parent.level;return!(!0!==t.exception&&n&&!(this.levels[n]>=this.levels[t[i]])||!this.handleExceptions&&!0===t.exception)},o.prototype._nop=function(){}},function(e,t,n){"use strict";const r={};function a(e,t,n){n||(n=Error);class a extends n{constructor(e,n,r){super(function(e,n,r){return"string"==typeof t?t:t(e,n,r)}(e,n,r))}}a.prototype.name=n.name,a.prototype.code=e,r[e]=a}function i(e,t){if(Array.isArray(e)){const n=e.length;return e=e.map(e=>String(e)),n>2?`one of ${t} ${e.slice(0,n-1).join(", ")}, or `+e[n-1]:2===n?`one of ${t} ${e[0]} or ${e[1]}`:`of ${t} ${e[0]}`}return`of ${t} ${String(e)}`}a("ERR_INVALID_OPT_VALUE",function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'},TypeError),a("ERR_INVALID_ARG_TYPE",function(e,t,n){let r;var a,o;let s;if("string"==typeof t&&(a="not ",t.substr(!o||o<0?0:+o,a.length)===a)?(r="must not be",t=t.replace(/^not /,"")):r="must be",function(e,t,n){return(void 0===n||n>e.length)&&(n=e.length),e.substring(n-t.length,n)===t}(e," argument"))s=`The ${e} ${r} ${i(t,"type")}`;else{s=`The "${e}" ${function(e,t,n){return"number"!=typeof n&&(n=0),!(n+t.length>e.length)&&-1!==e.indexOf(t,n)}(e,".")?"property":"argument"} ${r} ${i(t,"type")}`}return s+=`. Received type ${typeof n}`},TypeError),a("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),a("ERR_METHOD_NOT_IMPLEMENTED",function(e){return"The "+e+" method is not implemented"}),a("ERR_STREAM_PREMATURE_CLOSE","Premature close"),a("ERR_STREAM_DESTROYED",function(e){return"Cannot call "+e+" after a stream was destroyed"}),a("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),a("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),a("ERR_STREAM_WRITE_AFTER_END","write after end"),a("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),a("ERR_UNKNOWN_ENCODING",function(e){return"Unknown encoding: "+e},TypeError),a("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),e.exports.codes=r},function(e,t,n){"use strict";var r=Object.keys||function(e){var t=[];for(var n in e)t.push(n);return t};e.exports=u;var a=n(93),i=n(97);n(5)(u,a);for(var o=r(i.prototype),s=0;s<o.length;s++){var l=o[s];u.prototype[l]||(u.prototype[l]=i.prototype[l])}function u(e){if(!(this instanceof u))return new u(e);a.call(this,e),i.call(this,e),this.allowHalfOpen=!0,e&&(!1===e.readable&&(this.readable=!1),!1===e.writable&&(this.writable=!1),!1===e.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",c)))}function c(){this._writableState.ended||process.nextTick(f,this)}function f(e){e.end()}Object.defineProperty(u.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(u.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(u.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(u.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}})},function(e,t){e.exports=require("path")},function(e,t){e.exports=require("events")},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var a=n(19),i=n(0),o=i.LEVEL,s=i.MESSAGE;a.enabled=!0;var l=/\s+/,u=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t.colors&&this.addColors(t.colors),this.options=t}var t,n,i;return t=e,i=[{key:"addColors",value:function(t){var n=Object.keys(t).reduce(function(e,n){return e[n]=l.test(t[n])?t[n].split(l):t[n],e},{});return e.allColors=Object.assign({},e.allColors||{},n),e.allColors}}],(n=[{key:"addColors",value:function(t){return e.addColors(t)}},{key:"colorize",value:function(t,n,r){if(void 0===r&&(r=n),!Array.isArray(e.allColors[t]))return a[e.allColors[t]](r);for(var i=0,o=e.allColors[t].length;i<o;i++)r=a[e.allColors[t][i]](r);return r}},{key:"transform",value:function(e,t){return t.all&&"string"==typeof e[s]&&(e[s]=this.colorize(e[o],e.level,e[s])),(t.level||t.all||!t.message)&&(e.level=this.colorize(e[o],e.level)),(t.all||t.message)&&(e.message=this.colorize(e[o],e.level,e.message)),e}}])&&r(t.prototype,n),i&&r(t,i),e}();e.exports=function(e){return new u(e)},e.exports.Colorizer=e.exports.Format=u},function(e,t){e.exports=require("buffer")},function(e,t,n){var r=n(22);"disable"===Object({PROCESS_NAME:"main"}).READABLE_STREAM&&r?(e.exports=r.Readable,Object.assign(e.exports,r),e.exports.Stream=r):((t=e.exports=n(93)).Stream=r||t,t.Readable=t,t.Writable=n(97),t.Duplex=n(10),t.Transform=n(99),t.PassThrough=n(186),t.finished=n(51),t.pipeline=n(187))},function(e,t){e.exports=require("fs")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(16),a=n(11),i=n(1),o=n(117),{combine:s,timestamp:l,label:u,printf:c}=o.format;var f;!function(e){e[e.Error=0]="Error",e[e.Warn=1]="Warn",e[e.Info=2]="Info",e[e.Verbose=3]="Verbose",e[e.Debug=4]="Debug",e[e.Silly=5]="Silly"}(f=t.LogLevel||(t.LogLevel={}));const h=["error","warn","info","verbose","debug","silly"];class d{constructor(e){this.keyword="",this.keyword=e}static start(e){d.logger||(d.logger=o.createLogger()),e&&d.configure(e)}static configure(e){do{if(!e||!d.logger)break;let t=null,n=null;d.label=e.label;let i=void 0;if("string"==typeof e.options?(i=e.options,n=d.readOptions(i)):n=e.options,n&&d.label&&(t=n[d.label]||{}),!t)break;if("file"===t.type){if(!t.logDir){let e=void 0;if(e=i?a.resolve(i,".."):__dirname,!r.existsSync(e))break;e=a.join(e,"log"),r.existsSync(e)||r.mkdirSync(e),e=a.join(e,d.label),r.existsSync(e)||r.mkdirSync(e),t.logDir=e}t.logName=t.logName||d.logName}!0&&t.level&&t.level>f.Info&&(t.level=f.Info);const p=c(({level:e,message:t,label:n,timestamp:r})=>`[${r}] [${e.toLocaleUpperCase()}] ${n} - ${t}`),m="file"===t.type?new o.transports.File({dirname:t.logDir,filename:t.logName}):new o.transports.Console;let g="silly";void 0!==t.level&&null!==t.level&&(g=h[t.level]),d.logger.configure({format:s(u({label:d.label}),l({format:"YYYY-MM-DD HH:mm:ss.SSS"}),p),transports:m,level:g})}while(0)}static getLogger(e){return new d(e)}error(...e){if(d.logger){e=[`[${this.keyword}]`,...e];let t=d.stringify(...e);d.logger.error(t)}}warn(...e){if(d.logger){e=[`[${this.keyword}]`,...e];let t=d.stringify(...e);d.logger.warn(t)}}info(...e){if(d.logger){e=[`[${this.keyword}]`,...e];let t=d.stringify(...e);d.logger.info(t)}}verbose(...e){if(d.logger){e=[`[${this.keyword}]`,...e];let t=d.stringify(...e);d.logger.verbose(t)}}debug(...e){if(d.logger){e=[`[${this.keyword}]`,...e];let t=d.stringify(...e);d.logger.debug(t)}}silly(...e){if(d.logger){e=[`[${this.keyword}]`,...e];let t=d.stringify(...e);d.logger.silly(t)}}static readOptions(e){let t=null;try{let n=r.readFileSync(e);t=JSON.parse(n.toString())}catch(e){console.log(e)}return t}static stringify(...e){return e.map(e=>"string"==typeof e?e:i.inspect(e)).join(" ").replace(/%/g,"%%")}static get logName(){const e=new Date,t=e.getFullYear(),n=e.getMonth()+1,r=e.getDate(),a=e.getHours(),i=e.getMinutes(),o=e.getSeconds();let s=t+"-";return n<10&&(s+="0"),s+=n+"-",r<10&&(s+="0"),s+=r+"-[",a<10&&(s+="0"),s+=a+"-",i<10&&(s+="0"),s+=i+"-",o<10&&(s+="0"),s+=o,`${s+="]"}.log`}}d.logger=null,d.label=void 0,t.default=d},function(e,t,n){"use strict";const r=n(19),{LEVEL:a,MESSAGE:i}=n(0);r.enabled=!0;const o=/\s+/;class s{constructor(e={}){e.colors&&this.addColors(e.colors),this.options=e}static addColors(e){const t=Object.keys(e).reduce((t,n)=>(t[n]=o.test(e[n])?e[n].split(o):e[n],t),{});return s.allColors=Object.assign({},s.allColors||{},t),s.allColors}addColors(e){return s.addColors(e)}colorize(e,t,n){if(void 0===n&&(n=t),!Array.isArray(s.allColors[e]))return r[s.allColors[e]](n);for(let t=0,a=s.allColors[e].length;t<a;t++)n=r[s.allColors[e][t]](n);return n}transform(e,t){return t.all&&"string"==typeof e[i]&&(e[i]=this.colorize(e[a],e.level,e[i])),(t.level||t.all||!t.message)&&(e.level=this.colorize(e[a],e.level)),(t.all||t.message)&&(e.message=this.colorize(e[a],e.level,e.message)),e}}e.exports=(e=>new s(e)),e.exports.Colorizer=e.exports.Format=s},function(e,t,n){var r=n(118);e.exports=r},function(e,t,n){"use strict";var r=n(13).Colorizer;e.exports=function(e){return r.addColors(e.colors||e),e}},function(e,t,n){"use strict";function r(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=n(0),o=i.configs,s=i.LEVEL,l=i.MESSAGE,u=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{levels:o.npm.levels};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.paddings=e.paddingForLevels(t.levels,t.filler),this.options=t}var t,n,i;return t=e,i=[{key:"getLongestLevel",value:function(e){var t=Object.keys(e).map(function(e){return e.length});return Math.max.apply(Math,r(t))}},{key:"paddingForLevel",value:function(e,t,n){var r=n+1-e.length,a=Math.floor(r/t.length);return"".concat(t).concat(t.repeat(a)).slice(0,r)}},{key:"paddingForLevels",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ",r=e.getLongestLevel(t);return Object.keys(t).reduce(function(t,a){return t[a]=e.paddingForLevel(a,n,r),t},{})}}],(n=[{key:"transform",value:function(e,t){return e.message="".concat(this.paddings[e[s]]).concat(e.message),e[l]&&(e[l]="".concat(this.paddings[e[s]]).concat(e[l])),e}}])&&a(t.prototype,n),i&&a(t,i),e}();e.exports=function(e){return new u(e)},e.exports.Padder=e.exports.Format=u},function(e,t){e.exports=require("stream")},function(e,t,n){"use strict";"undefined"==typeof process||!process.version||0===process.version.indexOf("v0.")||0===process.version.indexOf("v1.")&&0!==process.version.indexOf("v1.8.")?e.exports={nextTick:function(e,t,n,r){if("function"!=typeof e)throw new TypeError('"callback" argument must be a function');var a,i,o=arguments.length;switch(o){case 0:case 1:return process.nextTick(e);case 2:return process.nextTick(function(){e.call(null,t)});case 3:return process.nextTick(function(){e.call(null,t,n)});case 4:return process.nextTick(function(){e.call(null,t,n,r)});default:for(a=new Array(o-1),i=0;i<a.length;)a[i++]=arguments[i];return process.nextTick(function(){e.apply(null,a)})}}}:e.exports=process},function(e,t,n){var r=n(14),a=r.Buffer;function i(e,t){for(var n in e)t[n]=e[n]}function o(e,t,n){return a(e,t,n)}a.from&&a.alloc&&a.allocUnsafe&&a.allocUnsafeSlow?e.exports=r:(i(r,t),t.Buffer=o),i(a,o),o.from=function(e,t,n){if("number"==typeof e)throw new TypeError("Argument must not be a number");return a(e,t,n)},o.alloc=function(e,t,n){if("number"!=typeof e)throw new TypeError("Argument must be a number");var r=a(e);return void 0!==t?"string"==typeof n?r.fill(t,n):r.fill(t):r.fill(0),r},o.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return a(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return r.SlowBuffer(e)}},function(e,t,n){"use strict";var r=n(23),a=Object.keys||function(e){var t=[];for(var n in e)t.push(n);return t};e.exports=f;var i=n(46);i.inherits=n(5);var o=n(148),s=n(77);i.inherits(f,o);for(var l=a(s.prototype),u=0;u<l.length;u++){var c=l[u];f.prototype[c]||(f.prototype[c]=s.prototype[c])}function f(e){if(!(this instanceof f))return new f(e);o.call(this,e),s.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",h)}function h(){this.allowHalfOpen||this._writableState.ended||r.nextTick(d,this)}function d(e){e.end()}Object.defineProperty(f.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(f.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),f.prototype._destroy=function(e,t){this.push(null),this.end(),r.nextTick(t,e)}},function(e,t,n){var r=n(154),a=n(86);e.exports=function(e){return null!=e&&a(e.length)&&!r(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isAsync=void 0;var r,a=n(157),i=(r=a)&&r.__esModule?r:{default:r};var o="function"==typeof Symbol;function s(e){return o&&"AsyncFunction"===e[Symbol.toStringTag]}t.default=function(e){return s(e)?(0,i.default)(e):e},t.isAsync=s},function(e,t,n){"use strict";var r=n(188),a=n(196),i=n(198),o=n(1),s=n(201).isatty(1),l=process.stdout;function u(e,t){return a(e)?((t=t||{}).colors="colors"in t?t.colors:s,t.ansi=t.colors?i(e,r(e)):e,t.stream=t.stream||l,Array.isArray(t.stream)||(t.stream=[t.stream]),function(e){e instanceof Error&&(e=e.stack||e.message||e),e=[t.ansi," ",e].join(""),e=o.format.apply(this,[e].concat(Array.prototype.slice.call(arguments,1)))+"\n",t.stream.forEach(function(t){t.write(e)})}):function(){}}u.to=function(e){return l=e,u},e.exports=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(112);class a{get pluginName(){return r.name}get binName(){return"VipPluginController"}get pluginVersion(){return r.version}}t.PluginHelper=a,t.default=new a},function(e,t,n){"use strict";const{Colorizer:r}=n(18);e.exports=(e=>(r.addColors(e.colors||e),e))},function(e,t,n){"use strict";const{configs:r,LEVEL:a,MESSAGE:i}=n(0);class o{constructor(e={levels:r.npm.levels}){this.paddings=o.paddingForLevels(e.levels,e.filler),this.options=e}static getLongestLevel(e){const t=Object.keys(e).map(e=>e.length);return Math.max(...t)}static paddingForLevel(e,t,n){const r=n+1-e.length,a=Math.floor(r/t.length);return`${t}${t.repeat(a)}`.slice(0,r)}static paddingForLevels(e,t=" "){const n=o.getLongestLevel(e);return Object.keys(e).reduce((e,r)=>(e[r]=o.paddingForLevel(r,t,n),e),{})}transform(e,t){return e.message=`${this.paddings[e[a]]}${e.message}`,e[i]&&(e[i]=`${this.paddings[e[a]]}${e[i]}`),e}}e.exports=(e=>new o(e)),e.exports.Padder=e.exports.Format=o},function(e,t,n){"use strict";const r=n(3),{MESSAGE:a}=n(0),i=n(7);function o(e,t){return t instanceof Buffer?t.toString("base64"):t}e.exports=r((e,t={})=>(e[a]=i(e,t.replacer||o,t.space),e))},function(e,t,n){"use strict";var r=n(2);e.exports=r(function(e){return e.message="\t".concat(e.message),e})},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var a=n(13).Colorizer,i=n(21).Padder,o=n(0),s=o.configs,l=o.MESSAGE,u=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t.levels||(t.levels=s.npm.levels),this.colorizer=new a(t),this.padder=new i(t),this.options=t}var t,n,o;return t=e,(n=[{key:"transform",value:function(e,t){return this.colorizer.transform(this.padder.transform(e,t),t),e[l]="".concat(e.level,":").concat(e.message),e}}])&&r(t.prototype,n),o&&r(t,o),e}();e.exports=function(e){return new u(e)},e.exports.Format=u},function(e,t,n){"use strict";var r=n(2);function a(e){if(e.every(i))return function(t){for(var n=t,r=0;r<e.length;r++)if(!(n=e[r].transform(n,e[r].options)))return!1;return n}}function i(e){if("function"!=typeof e.transform)throw new Error(["No transform function found on format. Did you create a format instance?","const myFormat = format(formatFn);","const instance = myFormat();"].join("\n"));return!0}e.exports=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=r(a(t)),o=i();return o.Format=i.Format,o},e.exports.cascade=a},function(e,t,n){"use strict";var r=n(2),a=n(0).MESSAGE,i=n(7);function o(e,t){return t instanceof Buffer?t.toString("base64"):t}e.exports=r(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e[a]=i(e,t.replacer||o,t.space),e})},function(e,t,n){"use strict";var r=n(2);e.exports=r(function(e,t){return t.message?(e.message="[".concat(t.label,"] ").concat(e.message),e):(e.label=t.label,e)})},function(e,t,n){"use strict";var r=n(2),a=n(0).MESSAGE,i=n(7);e.exports=r(function(e){var t={};return e.message&&(t["@message"]=e.message,delete e.message),e.timestamp&&(t["@timestamp"]=e.timestamp,delete e.timestamp),t["@fields"]=e,e[a]=i(t),e})},function(e,t,n){"use strict";var r=n(2);function a(e,t,n){var r,a,i,o=t.reduce(function(t,n){return t[n]=e[n],delete e[n],t},{}),s=Object.keys(e).reduce(function(t,n){return t[n]=e[n],delete e[n],t},{});return Object.assign(e,o,(i=s,(a=n)in(r={})?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i,r)),e}e.exports=r(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="metadata";t.key&&(n=t.key);var r=[];return t.fillExcept||t.fillWith||(r.push("level"),r.push("message")),t.fillExcept&&(r=t.fillExcept),r.length>0?a(e,r,n):t.fillWith?function(e,t,n){return e[n]=t.reduce(function(t,n){return t[n]=e[n],delete e[n],t},{}),e}(e,t.fillWith,n):e})},function(e,t,n){"use strict";var r=n(1).inspect,a=n(2),i=n(0),o=i.LEVEL,s=i.MESSAGE,l=i.SPLAT;e.exports=a(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=Object.assign({},e);return delete n[o],delete n[s],delete n[l],e[s]=r(n,!1,t.depth||null,t.colorize),e})},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var a=n(0).MESSAGE,i=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.template=t}var t,n,i;return t=e,(n=[{key:"transform",value:function(e){return e[a]=this.template(e),e}}])&&r(t.prototype,n),i&&r(t,i),e}();e.exports=function(e){return new i(e)},e.exports.Printf=e.exports.Format=i},function(e,t,n){"use strict";var r=n(2),a=n(0).MESSAGE,i=n(7);e.exports=r(function(e){var t=i(Object.assign({},e,{level:void 0,message:void 0,splat:void 0})),n=e.padding&&e.padding[e.level]||"";return e[a]="{}"!==t?"".concat(e.level,":").concat(n," ").concat(e.message," ").concat(t):"".concat(e.level,":").concat(n," ").concat(e.message),e})},function(e,t,n){"use strict";function r(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=n(1),o=n(0).SPLAT,s=/%[scdjifoO%]/g,l=/%%/g,u=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.options=t}var t,n,u;return t=e,(n=[{key:"_splat",value:function(e,t){var n=e.message,a=e[o]||e.splat||[],s=n.match(l),u=s&&s.length||0,c=t.length-u-a.length,f=c<0?a.splice(c,-1*c):[],h=f.length;if(h)for(var d=0;d<h;d++)Object.assign(e,f[d]);return e.message=i.format.apply(i,[n].concat(r(a))),e}},{key:"transform",value:function(e){var t=e.message,n=e[o]||e.splat;if(!n||!n.length)return e;var r=t&&t.match&&t.match(s);if(!r&&(n||n.length)){var a=n.length>1?n.splice(0):n,i=a.length;if(i)for(var l=0;l<i;l++)Object.assign(e,a[l]);return e}return r?this._splat(e,r):e}}])&&a(t.prototype,n),u&&a(t,u),e}();e.exports=function(e){return new u(e)}},function(e,t,n){"use strict";var r=n(69),a=n(2);e.exports=a(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t.format&&(e.timestamp="function"==typeof t.format?t.format():r.format(new Date,t.format)),e.timestamp||(e.timestamp=(new Date).toISOString()),t.alias&&(e[t.alias]=e.timestamp),e})},function(e,t,n){"use strict";var r=n(19),a=n(2),i=n(0).MESSAGE;e.exports=a(function(e,t){return!1!==t.level&&(e.level=r.strip(e.level)),!1!==t.message&&(e.message=r.strip(e.message)),!1!==t.raw&&e[i]&&(e[i]=r.strip(e[i])),e})},function(e,t){function n(e){return Object.prototype.toString.call(e)}t.isArray=function(e){return Array.isArray?Array.isArray(e):"[object Array]"===n(e)},t.isBoolean=function(e){return"boolean"==typeof e},t.isNull=function(e){return null===e},t.isNullOrUndefined=function(e){return null==e},t.isNumber=function(e){return"number"==typeof e},t.isString=function(e){return"string"==typeof e},t.isSymbol=function(e){return"symbol"==typeof e},t.isUndefined=function(e){return void 0===e},t.isRegExp=function(e){return"[object RegExp]"===n(e)},t.isObject=function(e){return"object"==typeof e&&null!==e},t.isDate=function(e){return"[object Date]"===n(e)},t.isError=function(e){return"[object Error]"===n(e)||e instanceof Error},t.isFunction=function(e){return"function"==typeof e},t.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},t.isBuffer=Buffer.isBuffer},function(e,t){e.exports=function(){}},function(e,t,n){var r=n(82),a=n(155),i=n(156),o="[object Null]",s="[object Undefined]",l=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?s:o:l&&l in Object(e)?a(e):i(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){t|=0;for(var n=Math.max(e.length-t,0),r=Array(n),a=0;a<n;a++)r[a]=e[t+a];return r},e.exports=t.default},function(e,t){e.exports=function(e){return null!=e&&"object"==typeof e}},function(e,t,n){"use strict";var r=n(9).codes.ERR_STREAM_PREMATURE_CLOSE;function a(){}e.exports=function e(t,n,i){if("function"==typeof n)return e(t,null,n);n||(n={}),i=function(e){var t=!1;return function(){if(!t){t=!0;for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];e.apply(this,r)}}}(i||a);var o=n.readable||!1!==n.readable&&t.readable,s=n.writable||!1!==n.writable&&t.writable,l=function(){t.writable||c()},u=t._writableState&&t._writableState.finished,c=function(){s=!1,u=!0,o||i.call(t)},f=t._readableState&&t._readableState.endEmitted,h=function(){o=!1,f=!0,s||i.call(t)},d=function(e){i.call(t,e)},p=function(){var e;return o&&!f?(t._readableState&&t._readableState.ended||(e=new r),i.call(t,e)):s&&!u?(t._writableState&&t._writableState.ended||(e=new r),i.call(t,e)):void 0},m=function(){t.req.on("finish",c)};return function(e){return e.setHeader&&"function"==typeof e.abort}(t)?(t.on("complete",c),t.on("abort",p),t.req?m():t.on("request",m)):s&&!t._writableState&&(t.on("end",l),t.on("close",l)),t.on("end",h),t.on("finish",c),!1!==n.error&&t.on("error",d),t.on("close",p),function(){t.removeListener("complete",c),t.removeListener("abort",p),t.removeListener("request",m),t.req&&t.req.removeListener("finish",c),t.removeListener("end",l),t.removeListener("close",l),t.removeListener("finish",c),t.removeListener("end",h),t.removeListener("error",d),t.removeListener("close",p)}}},function(e,t,n){"use strict";const r=n(4),{configs:a}=n(0);t.cli=r.levels(a.cli),t.npm=r.levels(a.npm),t.syslog=r.levels(a.syslog),t.addColors=r.levels},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){(0,r.default)(e,(0,a.default)((0,i.default)(t)),n)};var r=o(n(208)),a=o(n(209)),i=o(n(27));function o(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(a,i){function o(e){try{l(r.next(e))}catch(e){i(e)}}function s(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?a(e.value):new n(function(t){t(e.value)}).then(o,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const a=n(12),i=n(113),o=n(57),s=n(55);let l=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends a.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singleton=!1,this.retCallbackMap={},this.eventCallbackMap={},this.contextCallbackMap={}}start(e,t){do{if(this.singleton)break;if(this.singleton=!0,global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e)throw new Error("no suitable context for client, please specify context with start function");if(e.name===o.serverContextName)throw new Error("client context must difference from server");this.client=new i.Client({context:e,socketPrex:t}),this.client.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(e.name,[e.__context].concat(e.args));else if("call_client_by_id"===e.action)this.callFunctionById(e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter({s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.emit("stat_call_function_back",this.client.getContext(),e);const n=this.retCallbackMap[t.rid].callback;n&&(t.success?n(null,t.data):n(new Error(t.error),t.data)),delete this.retCallbackMap[t.rid]}}),this.client.on("end",()=>{this.client.isInprocess()||(s.information("server is ended, and this client is try to exit"),process.exit(0))}),this.registry(e.name)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return new Promise((t,n)=>{do{if(!e){t(!1);break}let n=this.generateId();const r={action:"check_server_function_exist",method:e,rid:n};let a=(e,n)=>{t(!e&&n)};this.retCallbackMap[n]=Object.assign({callback:a},r),this.sendAdapter(r)}while(0)})})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return new Promise((n,r)=>{do{if(!this.client){let e="client doesn't ready";s.error("callServerFunction error",e),n([null,e]);break}this.emit("stat_call_function",this.client.getContext(),e);let r=this.generateId();if(t)for(let e=0;e<t.length;e++)t[e]=this.convertFunction2Id(t[e]);const a={rid:r,method:e,args:t};let i=(e,t)=>{e?(s.error("callServerFunction error",e.message),n([null,e.message])):n([t,void 0])};this.retCallbackMap[r]=Object.assign({callback:i},a),this.sendAdapter(a)}while(0)})}isRemoteClientExist(e){return new Promise((t,n)=>{do{if(!e){t([!1,"remote client alias is not specifed"]);break}if(e===this.client.getContext().name){t([!0,"self is exist"]);break}let n=this.generateId();const r={dst:e,action:"check_client_exist",rid:n};let a=(e,n)=>{t(e?[!1,e.message]:[n,"sucesss"])};this.retCallbackMap[n]=Object.assign({callback:a},r),this.sendAdapter(r)}while(0)})}checkRemoteFunction(e,t){return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.client.getContext().name;if(r===e){n(!(!this.apis||!this.apis[t]));break}let a=this.generateId();const i={action:"check_client_function_exist",method:t,rid:a,src:r,dst:e};let o=(e,t)=>{n(!e&&t)};this.retCallbackMap[a]=Object.assign({callback:o},i),this.sendAdapter(i)}while(0)})}callRemoteClientFunction(e,t,...n){return e?new Promise((r,a)=>{if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2Id(n[e]);let i=this.generateId();const o={src:this.client.getContext().name,dst:e,action:"call_remote_client_api",method:t,args:n,rid:i};this.retCallbackMap[i]=Object.assign({callback:(e,t)=>{e?(s.information("callRemoteClientFunction",e.message),r([null,e.message])):r([t,void 0])}},o),this.sendAdapter(o)}):this.callServerFunction(t,...n)}callRemoteContextById(e,t,...n){this.sendAdapter({dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){let n=this.generateId();return this.eventCallbackMap.hasOwnProperty(e)||(this.eventCallbackMap[e]={}),o.isObjectEmpty(this.eventCallbackMap[e])&&this.sendAdapter({action:"attach_event",name:e}),this.eventCallbackMap[e][n]=t,n}detachServerEvent(e,t){do{if(!this.eventCallbackMap.hasOwnProperty(e))break;delete this.eventCallbackMap[e][t],o.isObjectEmpty(this.eventCallbackMap[e])&&this.sendAdapter({action:"detach_event",name:e})}while(0)}broadcastEvent(e,...t){this.sendAdapter({action:"broadcast",name:e,args:t})}registry(e){return new Promise((t,n)=>{do{if(!e){t(!1);break}let n=this.generateId();const r={alias:e,action:"register",rid:n};let a=(n,r)=>{n?(s.error("register error",n.message),t(r)):t(e)};this.retCallbackMap[n]=Object.assign({callback:a},r),this.sendAdapter(r)}while(0)})}getNow(){return Date.now()}sendAdapter(e){do{if(!e)break;let t=this.getNow();if(e.timestamp?e.timestamp=[...e.timestamp].concat(t):e.timestamp=[].concat(t),!e.__context){let t=this.client.getContext();t&&(e=Object.assign({__context:t},e))}this.client.isInprocess()?(s.information("send to server in process"),global.__xdasIPCServer.emit("message",e,this.client)):this.client.send(e)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}fireServerEvent(e,...t){do{if(!this.eventCallbackMap.hasOwnProperty(e))break;let n=this.eventCallbackMap[e];for(let e in n){let r=n[e];r&&r.apply(null,...t)}}while(0)}callFunctionById(e,t,...n){let r=void 0;do{const a=this.contextCallbackMap[e];if(!a)break;let i=void 0,o=void 0;try{i=a.apply(null,...n)}catch(e){o=e.message;break}if(void 0===t||null===t)break;if(r={s_rid:t,action:"call_client_by_id_callback",success:!1},void 0!==o){r.error=o;break}if(i&&i.then){i.then(e=>{r.data=this.convertFunction2Id(e),r.success=!0,this.sendAdapter(r)}).catch(e=>{r.error=e.message,this.sendAdapter(r)}),r=void 0;break}r.success=!0,r.data=this.convertFunction2Id(i)}while(0);r&&this.sendAdapter(r)}convertFunction2Id(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t=n}else if(e&&"object"==typeof e)for(let t in e){let n=e[t];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,e[t]=r}else n&&"object"==typeof n&&(e[t]=this.convertFunction2Id(n))}return t}callRegisterFunction(e){let t=void 0;do{if(!e)break;let n=e.method;if(!n)break;let r=this.getNow();if(t={s_rid:e.s_rid,action:"remote_client_callback",success:!1,rid:e.rid,method:e.method,src:e.src,timestamp:e.timestamp?e.timestamp.concat(r):[].concat(r)},!this.apis||!this.apis[n]){t.error=`callRegisterFunction ${n} is undefined`;break}let a=void 0;try{a=this.apis[n].apply(null,[e.src].concat(e.args))}catch(e){t.error=e.message;break}if(a&&a.then){a.then(e=>{t.data=this.convertFunction2Id(e),t.success=!0,this.sendAdapter(t)}).catch(e=>{t.error=e.message,this.sendAdapter(t)}),t=void 0;break}t.success=!0,t.data=this.convertFunction2Id(a)}while(0);s.information("callRegisterFunction",t),t&&this.sendAdapter(t)}}),l=global.__xdasIPCClienInstance,t.client=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},function(e,t){e.exports=require("string_decoder")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(6),a=n(11);t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=a.basename(process.execPath,".exe"));let i=a.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(i="\\\\.\\pipe\\"+(i=(i=i.replace(/^\//,"")).replace(/\//g,"-"))),i},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},function(e,t,n){"use strict";const r=n(3);e.exports=r(e=>(e.message=`\t${e.message}`,e))},function(e,t,n){"use strict";const{Colorizer:r}=n(18),{Padder:a}=n(31),{configs:i,MESSAGE:o}=n(0);class s{constructor(e={}){e.levels||(e.levels=i.npm.levels),this.colorizer=new r(e),this.padder=new a(e),this.options=e}transform(e,t){return this.colorizer.transform(this.padder.transform(e,t),t),e[o]=`${e.level}:${e.message}`,e}}e.exports=(e=>new s(e)),e.exports.Format=s},function(e,t,n){"use strict";const r=n(3);function a(e){if(e.every(i))return t=>{let n=t;for(let t=0;t<e.length;t++)if(!(n=e[t].transform(n,e[t].options)))return!1;return n}}function i(e){if("function"!=typeof e.transform)throw new Error(["No transform function found on format. Did you create a format instance?","const myFormat = format(formatFn);","const instance = myFormat();"].join("\n"));return!0}e.exports=((...e)=>{const t=r(a(e)),n=t();return n.Format=t.Format,n}),e.exports.cascade=a},function(e,t,n){"use strict";const r=n(3);e.exports=r((e,t)=>t.message?(e.message=`[${t.label}] ${e.message}`,e):(e.label=t.label,e))},function(e,t,n){"use strict";const r=n(3),{MESSAGE:a}=n(0),i=n(7);e.exports=r(e=>{const t={};return e.message&&(t["@message"]=e.message,delete e.message),e.timestamp&&(t["@timestamp"]=e.timestamp,delete e.timestamp),t["@fields"]=e,e[a]=i(t),e})},function(e,t,n){"use strict";const r=n(3);var a,i;e.exports=r((e,t={})=>{let n="metadata";t.key&&(n=t.key);let r=[];return t.fillExcept||t.fillWith||(r.push("level"),r.push("message")),t.fillExcept&&(r=t.fillExcept),r.length>0?function(e,t,n){const r=t.reduce((t,n)=>(t[n]=e[n],delete e[n],t),{}),a=Object.keys(e).reduce((t,n)=>(t[n]=e[n],delete e[n],t),{});return Object.assign(e,r,{[n]:a}),e}(e,r,n):t.fillWith?(e=e,i=t.fillWith,e[n]=i.reduce((t,n)=>(t[n]=e[n],delete e[n],t),{}),e):e})},function(e,t,n){"use strict";const r=n(1).inspect,a=n(3),{LEVEL:i,MESSAGE:o,SPLAT:s}=n(0);e.exports=a((e,t={})=>{const n=Object.assign({},e);return delete n[i],delete n[o],delete n[s],e[o]=r(n,!1,t.depth||null,t.colorize),e})},function(e,t,n){"use strict";const{MESSAGE:r}=n(0);class a{constructor(e){this.template=e}transform(e){return e[r]=this.template(e),e}}e.exports=(e=>new a(e)),e.exports.Printf=e.exports.Format=a},function(e,t,n){"use strict";const r=n(3),{MESSAGE:a}=n(0),i=n(7);e.exports=r(e=>{const t=i(Object.assign({},e,{level:void 0,message:void 0,splat:void 0})),n=e.padding&&e.padding[e.level]||"";return e[a]="{}"!==t?`${e.level}:${n} ${e.message} ${t}`:`${e.level}:${n} ${e.message}`,e})},function(e,t,n){"use strict";const r=n(1),{SPLAT:a}=n(0),i=/%[scdjifoO%]/g,o=/%%/g;e.exports=(e=>new class{constructor(e){this.options=e}_splat(e,t){const n=e.message,i=e[a]||e.splat||[],s=n.match(o),l=s&&s.length||0,u=t.length-l-i.length,c=u<0?i.splice(u,-1*u):[],f=c.length;if(f)for(let t=0;t<f;t++)Object.assign(e,c[t]);return e.message=r.format(n,...i),e}transform(e){const t=e.message,n=e[a]||e.splat;if(!n||!n.length)return e;const r=t&&t.match&&t.match(i);if(!r&&(n||n.length)){const t=n.length>1?n.splice(0):n,r=t.length;if(r)for(let n=0;n<r;n++)Object.assign(e,t[n]);return e}return r?this._splat(e,r):e}}(e))},function(e,t,n){"use strict";const r=n(69),a=n(3);e.exports=a((e,t={})=>(t.format&&(e.timestamp="function"==typeof t.format?t.format():r.format(new Date,t.format)),e.timestamp||(e.timestamp=(new Date).toISOString()),t.alias&&(e[t.alias]=e.timestamp),e))},function(e,t,n){var r;!function(a){"use strict";var i={},o=/d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|([HhMsDm])\1?|[aA]|"[^"]*"|'[^']*'/g,s=/\d\d?/,l=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,u=/\[([^]*?)\]/gm,c=function(){};function f(e,t){for(var n=[],r=0,a=e.length;r<a;r++)n.push(e[r].substr(0,t));return n}function h(e){return function(t,n,r){var a=r[e].indexOf(n.charAt(0).toUpperCase()+n.substr(1).toLowerCase());~a&&(t.month=a)}}function d(e,t){for(e=String(e),t=t||2;e.length<t;)e="0"+e;return e}var p=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],m=["January","February","March","April","May","June","July","August","September","October","November","December"],g=f(m,3),v=f(p,3);i.i18n={dayNamesShort:v,dayNames:p,monthNamesShort:g,monthNames:m,amPm:["am","pm"],DoFn:function(e){return e+["th","st","nd","rd"][e%10>3?0:(e-e%10!=10)*e%10]}};var b={D:function(e){return e.getDate()},DD:function(e){return d(e.getDate())},Do:function(e,t){return t.DoFn(e.getDate())},d:function(e){return e.getDay()},dd:function(e){return d(e.getDay())},ddd:function(e,t){return t.dayNamesShort[e.getDay()]},dddd:function(e,t){return t.dayNames[e.getDay()]},M:function(e){return e.getMonth()+1},MM:function(e){return d(e.getMonth()+1)},MMM:function(e,t){return t.monthNamesShort[e.getMonth()]},MMMM:function(e,t){return t.monthNames[e.getMonth()]},YY:function(e){return String(e.getFullYear()).substr(2)},YYYY:function(e){return d(e.getFullYear(),4)},h:function(e){return e.getHours()%12||12},hh:function(e){return d(e.getHours()%12||12)},H:function(e){return e.getHours()},HH:function(e){return d(e.getHours())},m:function(e){return e.getMinutes()},mm:function(e){return d(e.getMinutes())},s:function(e){return e.getSeconds()},ss:function(e){return d(e.getSeconds())},S:function(e){return Math.round(e.getMilliseconds()/100)},SS:function(e){return d(Math.round(e.getMilliseconds()/10),2)},SSS:function(e){return d(e.getMilliseconds(),3)},a:function(e,t){return e.getHours()<12?t.amPm[0]:t.amPm[1]},A:function(e,t){return e.getHours()<12?t.amPm[0].toUpperCase():t.amPm[1].toUpperCase()},ZZ:function(e){var t=e.getTimezoneOffset();return(t>0?"-":"+")+d(100*Math.floor(Math.abs(t)/60)+Math.abs(t)%60,4)}},y={D:[s,function(e,t){e.day=t}],Do:[new RegExp(s.source+l.source),function(e,t){e.day=parseInt(t,10)}],M:[s,function(e,t){e.month=t-1}],YY:[s,function(e,t){var n=+(""+(new Date).getFullYear()).substr(0,2);e.year=""+(t>68?n-1:n)+t}],h:[s,function(e,t){e.hour=t}],m:[s,function(e,t){e.minute=t}],s:[s,function(e,t){e.second=t}],YYYY:[/\d{4}/,function(e,t){e.year=t}],S:[/\d/,function(e,t){e.millisecond=100*t}],SS:[/\d{2}/,function(e,t){e.millisecond=10*t}],SSS:[/\d{3}/,function(e,t){e.millisecond=t}],d:[s,c],ddd:[l,c],MMM:[l,h("monthNamesShort")],MMMM:[l,h("monthNames")],a:[l,function(e,t,n){var r=t.toLowerCase();r===n.amPm[0]?e.isPm=!1:r===n.amPm[1]&&(e.isPm=!0)}],ZZ:[/([\+\-]\d\d:?\d\d|Z)/,function(e,t){"Z"===t&&(t="+00:00");var n,r=(t+"").match(/([\+\-]|\d\d)/gi);r&&(n=60*r[1]+parseInt(r[2],10),e.timezoneOffset="+"===r[0]?n:-n)}]};y.dd=y.d,y.dddd=y.ddd,y.DD=y.D,y.mm=y.m,y.hh=y.H=y.HH=y.h,y.MM=y.M,y.ss=y.s,y.A=y.a,i.masks={default:"ddd MMM DD YYYY HH:mm:ss",shortDate:"M/D/YY",mediumDate:"MMM D, YYYY",longDate:"MMMM D, YYYY",fullDate:"dddd, MMMM D, YYYY",shortTime:"HH:mm",mediumTime:"HH:mm:ss",longTime:"HH:mm:ss.SSS"},i.format=function(e,t,n){var r=n||i.i18n;if("number"==typeof e&&(e=new Date(e)),"[object Date]"!==Object.prototype.toString.call(e)||isNaN(e.getTime()))throw new Error("Invalid Date in fecha.format");var a=[];return(t=(t=(t=i.masks[t]||t||i.masks.default).replace(u,function(e,t){return a.push(t),"??"})).replace(o,function(t){return t in b?b[t](e,r):t.slice(1,t.length-1)})).replace(/\?\?/g,function(){return a.shift()})},i.parse=function(e,t,n){var r=n||i.i18n;if("string"!=typeof t)throw new Error("Invalid format in fecha.parse");if(t=i.masks[t]||t,e.length>1e3)return!1;var a=!0,s={};if(t.replace(o,function(t){if(y[t]){var n=y[t],i=e.search(n[0]);~i?e.replace(n[0],function(t){return n[1](s,t,r),e=e.substr(i+t.length),t}):a=!1}return y[t]?"":t.slice(1,t.length-1)}),!a)return!1;var l,u=new Date;return!0===s.isPm&&null!=s.hour&&12!=+s.hour?s.hour=+s.hour+12:!1===s.isPm&&12==+s.hour&&(s.hour=0),null!=s.timezoneOffset?(s.minute=+(s.minute||0)-+s.timezoneOffset,l=new Date(Date.UTC(s.year||u.getFullYear(),s.month||0,s.day||1,s.hour||0,s.minute||0,s.second||0,s.millisecond||0))):l=new Date(s.year||u.getFullYear(),s.month||0,s.day||1,s.hour||0,s.minute||0,s.second||0,s.millisecond||0),l},e.exports?e.exports=i:void 0===(r=function(){return i}.call(t,n,t,e))||(e.exports=r)}()},function(e,t,n){"use strict";const r=n(19),a=n(3),{MESSAGE:i}=n(0);e.exports=a((e,t)=>(!1!==t.level&&(e.level=r.strip(e.level)),!1!==t.message&&(e.message=r.strip(e.message)),!1!==t.raw&&e[i]&&(e[i]=r.strip(e[i])),e))},function(e,t,n){"use strict";var r=t.format=n(2);t.levels=n(20),Object.defineProperty(r,"align",{value:n(33)}),Object.defineProperty(r,"cli",{value:n(34)}),Object.defineProperty(r,"combine",{value:n(35)}),Object.defineProperty(r,"colorize",{value:n(13)}),Object.defineProperty(r,"json",{value:n(36)}),Object.defineProperty(r,"label",{value:n(37)}),Object.defineProperty(r,"logstash",{value:n(38)}),Object.defineProperty(r,"metadata",{value:n(39)}),Object.defineProperty(r,"padLevels",{value:n(21)}),Object.defineProperty(r,"prettyPrint",{value:n(40)}),Object.defineProperty(r,"printf",{value:n(41)}),Object.defineProperty(r,"simple",{value:n(42)}),Object.defineProperty(r,"splat",{value:n(43)}),Object.defineProperty(r,"timestamp",{value:n(44)}),Object.defineProperty(r,"uncolorize",{value:n(45)})},function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var a=n(2),i=n(0),o=i.LEVEL,s=i.MESSAGE;e.exports=a(function(e,t){var n=t.stack;if(e instanceof Error){var a,i=Object.assign({},e,(r(a={level:e.level},o,e[o]||e.level),r(a,"message",e.message),r(a,s,e[s]||e.message),a));return n&&(i.stack=e.stack),i}if(!(e.message instanceof Error))return e;Object.assign(e,e.message);var l=e.message;return e.message=l.message,e[s]=l.message,n&&(e.stack=l.stack),e})},function(e,t,n){"use strict";var r=t.format=n(2);function a(e,t){t=t||e,Object.defineProperty(r,e,{get:function(){return n(134)("./".concat(t,".js"))},configurable:!0})}t.levels=n(20),a("align"),a("errors"),a("cli"),a("combine"),a("colorize"),a("json"),a("label"),a("logstash"),a("metadata"),a("ms"),a("padLevels","pad-levels"),a("prettyPrint","pretty-print"),a("printf"),a("simple"),a("splat"),a("timestamp"),a("uncolorize")},function(e,t,n){"use strict";var r=n(2),a=n(75);e.exports=r(function(e){var t=+new Date;return(void 0).diff=t-((void 0).prevTime||t),(void 0).prevTime=t,e.ms="+".concat(a((void 0).diff)),e})},function(e,t){var n=1e3,r=60*n,a=60*r,i=24*a,o=7*i,s=365.25*i;function l(e,t,n,r){var a=t>=1.5*n;return Math.round(e/n)+" "+r+(a?"s":"")}e.exports=function(e,t){t=t||{};var u=typeof e;if("string"===u&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!t)return;var l=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return l*s;case"weeks":case"week":case"w":return l*o;case"days":case"day":case"d":return l*i;case"hours":case"hour":case"hrs":case"hr":case"h":return l*a;case"minutes":case"minute":case"mins":case"min":case"m":return l*r;case"seconds":case"second":case"secs":case"sec":case"s":return l*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return l;default:return}}(e);if("number"===u&&isFinite(e))return t.long?function(e){var t=Math.abs(e);if(t>=i)return l(e,t,i,"day");if(t>=a)return l(e,t,a,"hour");if(t>=r)return l(e,t,r,"minute");if(t>=n)return l(e,t,n,"second");return e+" ms"}(e):function(e){var t=Math.abs(e);if(t>=i)return Math.round(e/i)+"d";if(t>=a)return Math.round(e/a)+"h";if(t>=r)return Math.round(e/r)+"m";if(t>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},function(e,t,n){"use strict";const{format:r}=n(1);t.warn={deprecated:e=>()=>{throw new Error(r("{ %s } was removed in winston@3.0.0.",e))},useFormat:e=>()=>{throw new Error([r("{ %s } was removed in winston@3.0.0.",e),"Use a custom winston.format = winston.format(function) instead."].join("\n"))},forFunctions(e,n,r){r.forEach(r=>{e[r]=t.warn[n](r)})},moved(e,t,n){function a(){return()=>{throw new Error([r("winston.%s was moved in winston@3.0.0.",n),r("Use a winston.%s instead.",t)].join("\n"))}}Object.defineProperty(e,n,{get:a,set:a})},forProperties(e,n,r){r.forEach(r=>{const a=t.warn[n](r);Object.defineProperty(e,r,{get:a,set:a})})}}},function(e,t,n){"use strict";var r=n(23);function a(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,n){var r=e.entry;e.entry=null;for(;r;){var a=r.callback;t.pendingcb--,a(n),r=r.next}t.corkedRequestsFree?t.corkedRequestsFree.next=e:t.corkedRequestsFree=e}(t,e)}}e.exports=g;var i,o=!process.browser&&["v0.10","v0.9."].indexOf(process.version.slice(0,5))>-1?setImmediate:r.nextTick;g.WritableState=m;var s=n(46);s.inherits=n(5);var l={deprecate:n(78)},u=n(79),c=n(24).Buffer,f=global.Uint8Array||function(){};var h,d=n(80);function p(){}function m(e,t){i=i||n(25),e=e||{};var s=t instanceof i;this.objectMode=!!e.objectMode,s&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var l=e.highWaterMark,u=e.writableHighWaterMark,c=this.objectMode?16:16384;this.highWaterMark=l||0===l?l:s&&(u||0===u)?u:c,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var f=!1===e.decodeStrings;this.decodeStrings=!f,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var n=e._writableState,a=n.sync,i=n.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(n),t)!function(e,t,n,a,i){--t.pendingcb,n?(r.nextTick(i,a),r.nextTick(_,e,t),e._writableState.errorEmitted=!0,e.emit("error",a)):(i(a),e._writableState.errorEmitted=!0,e.emit("error",a),_(e,t))}(e,n,a,t,i);else{var s=w(n);s||n.corked||n.bufferProcessing||!n.bufferedRequest||y(e,n),a?o(b,e,n,s,i):b(e,n,s,i)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new a(this)}function g(e){if(i=i||n(25),!(h.call(g,this)||this instanceof i))return new g(e);this._writableState=new m(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),u.call(this)}function v(e,t,n,r,a,i,o){t.writelen=r,t.writecb=o,t.writing=!0,t.sync=!0,n?e._writev(a,t.onwrite):e._write(a,i,t.onwrite),t.sync=!1}function b(e,t,n,r){n||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,r(),_(e,t)}function y(e,t){t.bufferProcessing=!0;var n=t.bufferedRequest;if(e._writev&&n&&n.next){var r=t.bufferedRequestCount,i=new Array(r),o=t.corkedRequestsFree;o.entry=n;for(var s=0,l=!0;n;)i[s]=n,n.isBuf||(l=!1),n=n.next,s+=1;i.allBuffers=l,v(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new a(t),t.bufferedRequestCount=0}else{for(;n;){var u=n.chunk,c=n.encoding,f=n.callback;if(v(e,t,!1,t.objectMode?1:u.length,u,c,f),n=n.next,t.bufferedRequestCount--,t.writing)break}null===n&&(t.lastBufferedRequest=null)}t.bufferedRequest=n,t.bufferProcessing=!1}function w(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function E(e,t){e._final(function(n){t.pendingcb--,n&&e.emit("error",n),t.prefinished=!0,e.emit("prefinish"),_(e,t)})}function _(e,t){var n=w(t);return n&&(!function(e,t){t.prefinished||t.finalCalled||("function"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,r.nextTick(E,e,t)):(t.prefinished=!0,e.emit("prefinish")))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),n}s.inherits(g,u),m.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(m.prototype,"buffer",{get:l.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(h=Function.prototype[Symbol.hasInstance],Object.defineProperty(g,Symbol.hasInstance,{value:function(e){return!!h.call(this,e)||this===g&&(e&&e._writableState instanceof m)}})):h=function(e){return e instanceof this},g.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},g.prototype.write=function(e,t,n){var a,i=this._writableState,o=!1,s=!i.objectMode&&(a=e,c.isBuffer(a)||a instanceof f);return s&&!c.isBuffer(e)&&(e=function(e){return c.from(e)}(e)),"function"==typeof t&&(n=t,t=null),s?t="buffer":t||(t=i.defaultEncoding),"function"!=typeof n&&(n=p),i.ended?function(e,t){var n=new Error("write after end");e.emit("error",n),r.nextTick(t,n)}(this,n):(s||function(e,t,n,a){var i=!0,o=!1;return null===n?o=new TypeError("May not write null values to stream"):"string"==typeof n||void 0===n||t.objectMode||(o=new TypeError("Invalid non-string/buffer chunk")),o&&(e.emit("error",o),r.nextTick(a,o),i=!1),i}(this,i,e,n))&&(i.pendingcb++,o=function(e,t,n,r,a,i){if(!n){var o=function(e,t,n){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=c.from(t,n));return t}(t,r,a);r!==o&&(n=!0,a="buffer",r=o)}var s=t.objectMode?1:r.length;t.length+=s;var l=t.length<t.highWaterMark;l||(t.needDrain=!0);if(t.writing||t.corked){var u=t.lastBufferedRequest;t.lastBufferedRequest={chunk:r,encoding:a,isBuf:n,callback:i,next:null},u?u.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else v(e,t,!1,s,r,a,i);return l}(this,i,s,e,t,n)),o},g.prototype.cork=function(){this._writableState.corked++},g.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||y(this,e))},g.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(g.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),g.prototype._write=function(e,t,n){n(new Error("_write() is not implemented"))},g.prototype._writev=null,g.prototype.end=function(e,t,n){var a=this._writableState;"function"==typeof e?(n=e,e=null,t=null):"function"==typeof t&&(n=t,t=null),null!==e&&void 0!==e&&this.write(e,t),a.corked&&(a.corked=1,this.uncork()),a.ending||a.finished||function(e,t,n){t.ending=!0,_(e,t),n&&(t.finished?r.nextTick(n):e.once("finish",n));t.ended=!0,e.writable=!1}(this,a,n)},Object.defineProperty(g.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),g.prototype.destroy=d.destroy,g.prototype._undestroy=d.undestroy,g.prototype._destroy=function(e,t){this.end(),t(e)}},function(e,t,n){e.exports=n(1).deprecate},function(e,t,n){e.exports=n(22)},function(e,t,n){"use strict";var r=n(23);function a(e,t){e.emit("error",t)}e.exports={destroy:function(e,t){var n=this,i=this._readableState&&this._readableState.destroyed,o=this._writableState&&this._writableState.destroyed;return i||o?(t?t(e):!e||this._writableState&&this._writableState.errorEmitted||r.nextTick(a,this,e),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(e){!t&&e?(r.nextTick(a,n,e),n._writableState&&(n._writableState.errorEmitted=!0)):t&&t(e)}),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},function(e,t,n){"use strict";var r=n(24).Buffer,a=r.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function i(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(r.isEncoding===a||!a(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=l,this.end=u,t=4;break;case"utf8":this.fillLast=s,t=4;break;case"base64":this.text=c,this.end=f,t=3;break;default:return this.write=h,void(this.end=d)}this.lastNeed=0,this.lastTotal=0,this.lastChar=r.allocUnsafe(t)}function o(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function s(e){var t=this.lastTotal-this.lastNeed,n=function(e,t,n){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==n?n:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function l(e,t){if((e.length-t)%2==0){var n=e.toString("utf16le",t);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function u(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,n)}return t}function c(e,t){var n=(e.length-t)%3;return 0===n?e.toString("base64",t):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-n))}function f(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function h(e){return e.toString(this.encoding)}function d(e){return e&&e.length?this.write(e):""}t.StringDecoder=i,i.prototype.write=function(e){if(0===e.length)return"";var t,n;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<e.length?t?t+this.text(e,n):this.text(e,n):t||""},i.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},i.prototype.text=function(e,t){var n=function(e,t,n){var r=t.length-1;if(r<n)return 0;var a=o(t[r]);if(a>=0)return a>0&&(e.lastNeed=a-1),a;if(--r<n||-2===a)return 0;if((a=o(t[r]))>=0)return a>0&&(e.lastNeed=a-2),a;if(--r<n||-2===a)return 0;if((a=o(t[r]))>=0)return a>0&&(2===a?a=0:e.lastNeed=a-3),a;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=n;var r=e.length-(n-this.lastNeed);return e.copy(this.lastChar,0,r),e.toString("utf8",t,r)},i.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},function(e,t,n){var r=n(83).Symbol;e.exports=r},function(e,t,n){var r=n(84),a="object"==typeof self&&self&&self.Object===Object&&self,i=r||a||Function("return this")();e.exports=i},function(e,t){var n="object"==typeof global&&global&&global.Object===Object&&global;e.exports=n},function(e,t){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},function(e,t){var n=9007199254740991;e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n,i){(0,r.default)(t)(e,(0,a.default)(n),i)};var r=i(n(161)),a=i(n(27));function i(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){if(null!==e){var t=e;e=null,t.apply(this,arguments)}}},e.exports=t.default},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){if(null===e)throw new Error("Callback was already called.");var t=e;e=null,t.apply(this,arguments)}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return function(n,r,a){return e(n,t,r,a)}},e.exports=t.default},function(e,t,n){"use strict";var r;e.exports=x,x.ReadableState=_;n(12).EventEmitter;var a=function(e,t){return e.listeners(t).length},i=n(94),o=n(14).Buffer,s=global.Uint8Array||function(){};var l,u=n(1);l=u&&u.debuglog?u.debuglog("stream"):function(){};var c,f,h=n(182),d=n(95),p=n(96).getHighWaterMark,m=n(9).codes,g=m.ERR_INVALID_ARG_TYPE,v=m.ERR_STREAM_PUSH_AFTER_EOF,b=m.ERR_METHOD_NOT_IMPLEMENTED,y=m.ERR_STREAM_UNSHIFT_AFTER_END_EVENT,w=n(183).emitExperimentalWarning;n(5)(x,i);var E=["error","close","destroy","pause","resume"];function _(e,t,a){r=r||n(10),e=e||{},"boolean"!=typeof a&&(a=t instanceof r),this.objectMode=!!e.objectMode,a&&(this.objectMode=this.objectMode||!!e.readableObjectMode),this.highWaterMark=p(this,e,"readableHighWaterMark",a),this.buffer=new h,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==e.emitClose,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(c||(c=n(98).StringDecoder),this.decoder=new c(e.encoding),this.encoding=e.encoding)}function x(e){if(r=r||n(10),!(this instanceof x))return new x(e);var t=this instanceof r;this._readableState=new _(e,this,t),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),i.call(this)}function F(e,t,n,r,a){l("readableAddChunk",t);var i,u=e._readableState;if(null===t)u.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,t.sync?j(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,M(e)))}(e,u);else if(a||(i=function(e,t){var n;r=t,o.isBuffer(r)||r instanceof s||"string"==typeof t||void 0===t||e.objectMode||(n=new g("chunk",["string","Buffer","Uint8Array"],t));var r;return n}(u,t)),i)e.emit("error",i);else if(u.objectMode||t&&t.length>0)if("string"==typeof t||u.objectMode||Object.getPrototypeOf(t)===o.prototype||(t=function(e){return o.from(e)}(t)),r)u.endEmitted?e.emit("error",new y):k(e,u,t,!0);else if(u.ended)e.emit("error",new v);else{if(u.destroyed)return!1;u.reading=!1,u.decoder&&!n?(t=u.decoder.write(t),u.objectMode||0!==t.length?k(e,u,t,!1):A(e,u)):k(e,u,t,!1)}else r||(u.reading=!1,A(e,u));return!u.ended&&(u.length<u.highWaterMark||0===u.length)}function k(e,t,n,r){t.flowing&&0===t.length&&!t.sync?(t.awaitDrain=0,e.emit("data",n)):(t.length+=t.objectMode?1:n.length,r?t.buffer.unshift(n):t.buffer.push(n),t.needReadable&&j(e)),A(e,t)}Object.defineProperty(x.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),x.prototype.destroy=d.destroy,x.prototype._undestroy=d.undestroy,x.prototype._destroy=function(e,t){t(e)},x.prototype.push=function(e,t){var n,r=this._readableState;return r.objectMode?n=!0:"string"==typeof e&&((t=t||r.defaultEncoding)!==r.encoding&&(e=o.from(e,t),t=""),n=!0),F(this,e,t,!1,n)},x.prototype.unshift=function(e){return F(this,e,null,!0,!1)},x.prototype.isPaused=function(){return!1===this._readableState.flowing},x.prototype.setEncoding=function(e){return c||(c=n(98).StringDecoder),this._readableState.decoder=new c(e),this._readableState.encoding=this._readableState.decoder.encoding,this};var S=8388608;function C(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=S?e=S:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function j(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(l("emitReadable",t.flowing),t.emittedReadable=!0,process.nextTick(M,e))}function M(e){var t=e._readableState;l("emitReadable_",t.destroyed,t.length,t.ended),t.destroyed||!t.length&&!t.ended||e.emit("readable"),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,P(e)}function A(e,t){t.readingMore||(t.readingMore=!0,process.nextTick(O,e,t))}function O(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){var n=t.length;if(l("maybeReadMore read 0"),e.read(0),n===t.length)break}t.readingMore=!1}function D(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function B(e){l("readable nexttick read 0"),e.read(0)}function R(e,t){l("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),P(e),t.flowing&&!t.reading&&e.read(0)}function P(e){var t=e._readableState;for(l("flow",t.flowing);t.flowing&&null!==e.read(););}function T(e,t){return 0===t.length?null:(t.objectMode?n=t.buffer.shift():!e||e>=t.length?(n=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):n=t.buffer.consume(e,t.decoder),n);var n}function L(e){var t=e._readableState;l("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,process.nextTick(N,t,e))}function N(e,t){l("endReadableNT",e.endEmitted,e.length),e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function I(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}x.prototype.read=function(e){l("read",e),e=parseInt(e,10);var t=this._readableState,n=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&((0!==t.highWaterMark?t.length>=t.highWaterMark:t.length>0)||t.ended))return l("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?L(this):j(this),null;if(0===(e=C(e,t))&&t.ended)return 0===t.length&&L(this),null;var r,a=t.needReadable;return l("need readable",a),(0===t.length||t.length-e<t.highWaterMark)&&l("length less than watermark",a=!0),t.ended||t.reading?l("reading or ended",a=!1):a&&(l("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=C(n,t))),null===(r=e>0?T(e,t):null)?(t.needReadable=!0,e=0):(t.length-=e,t.awaitDrain=0),0===t.length&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&L(this)),null!==r&&this.emit("data",r),r},x.prototype._read=function(e){this.emit("error",new b("_read()"))},x.prototype.pipe=function(e,t){var n=this,r=this._readableState;switch(r.pipesCount){case 0:r.pipes=e;break;case 1:r.pipes=[r.pipes,e];break;default:r.pipes.push(e)}r.pipesCount+=1,l("pipe count=%d opts=%j",r.pipesCount,t);var i=(!t||!1!==t.end)&&e!==process.stdout&&e!==process.stderr?s:m;function o(t,a){l("onunpipe"),t===n&&a&&!1===a.hasUnpiped&&(a.hasUnpiped=!0,l("cleanup"),e.removeListener("close",d),e.removeListener("finish",p),e.removeListener("drain",u),e.removeListener("error",h),e.removeListener("unpipe",o),n.removeListener("end",s),n.removeListener("end",m),n.removeListener("data",f),c=!0,!r.awaitDrain||e._writableState&&!e._writableState.needDrain||u())}function s(){l("onend"),e.end()}r.endEmitted?process.nextTick(i):n.once("end",i),e.on("unpipe",o);var u=function(e){return function(){var t=e._readableState;l("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&a(e,"data")&&(t.flowing=!0,P(e))}}(n);e.on("drain",u);var c=!1;function f(t){l("ondata");var a=e.write(t);l("dest.write",a),!1===a&&((1===r.pipesCount&&r.pipes===e||r.pipesCount>1&&-1!==I(r.pipes,e))&&!c&&(l("false write response, pause",r.awaitDrain),r.awaitDrain++),n.pause())}function h(t){l("onerror",t),m(),e.removeListener("error",h),0===a(e,"error")&&e.emit("error",t)}function d(){e.removeListener("finish",p),m()}function p(){l("onfinish"),e.removeListener("close",d),m()}function m(){l("unpipe"),n.unpipe(e)}return n.on("data",f),function(e,t,n){if("function"==typeof e.prependListener)return e.prependListener(t,n);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(n):e._events[t]=[n,e._events[t]]:e.on(t,n)}(e,"error",h),e.once("close",d),e.once("finish",p),e.emit("pipe",n),r.flowing||(l("pipe resume"),n.resume()),e},x.prototype.unpipe=function(e){var t=this._readableState,n={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,n),this);if(!e){var r=t.pipes,a=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var i=0;i<a;i++)r[i].emit("unpipe",this,{hasUnpiped:!1});return this}var o=I(t.pipes,e);return-1===o?this:(t.pipes.splice(o,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,n),this)},x.prototype.on=function(e,t){var n=i.prototype.on.call(this,e,t),r=this._readableState;return"data"===e?(r.readableListening=this.listenerCount("readable")>0,!1!==r.flowing&&this.resume()):"readable"===e&&(r.endEmitted||r.readableListening||(r.readableListening=r.needReadable=!0,r.flowing=!1,r.emittedReadable=!1,l("on readable",r.length,r.reading),r.length?j(this):r.reading||process.nextTick(B,this))),n},x.prototype.addListener=x.prototype.on,x.prototype.removeListener=function(e,t){var n=i.prototype.removeListener.call(this,e,t);return"readable"===e&&process.nextTick(D,this),n},x.prototype.removeAllListeners=function(e){var t=i.prototype.removeAllListeners.apply(this,arguments);return"readable"!==e&&void 0!==e||process.nextTick(D,this),t},x.prototype.resume=function(){var e=this._readableState;return e.flowing||(l("resume"),e.flowing=!e.readableListening,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,process.nextTick(R,e,t))}(this,e)),e.paused=!1,this},x.prototype.pause=function(){return l("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(l("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},x.prototype.wrap=function(e){var t=this,n=this._readableState,r=!1;for(var a in e.on("end",function(){if(l("wrapped end"),n.decoder&&!n.ended){var e=n.decoder.end();e&&e.length&&t.push(e)}t.push(null)}),e.on("data",function(a){(l("wrapped data"),n.decoder&&(a=n.decoder.write(a)),!n.objectMode||null!==a&&void 0!==a)&&((n.objectMode||a&&a.length)&&(t.push(a)||(r=!0,e.pause())))}),e)void 0===this[a]&&"function"==typeof e[a]&&(this[a]=function(t){return function(){return e[t].apply(e,arguments)}}(a));for(var i=0;i<E.length;i++)e.on(E[i],this.emit.bind(this,E[i]));return this._read=function(t){l("wrapped _read",t),r&&(r=!1,e.resume())},this},"function"==typeof Symbol&&(x.prototype[Symbol.asyncIterator]=function(){return w("Readable[Symbol.asyncIterator]"),void 0===f&&(f=n(185)),f(this)}),Object.defineProperty(x.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(x.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(x.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}}),x._fromList=T,Object.defineProperty(x.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}})},function(e,t,n){e.exports=n(22)},function(e,t,n){"use strict";function r(e,t){i(e,t),a(e)}function a(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function i(e,t){e.emit("error",t)}e.exports={destroy:function(e,t){var n=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(t?t(e):!e||this._writableState&&this._writableState.errorEmitted||process.nextTick(i,this,e),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(e){!t&&e?(process.nextTick(r,n,e),n._writableState&&(n._writableState.errorEmitted=!0)):t?(process.nextTick(a,n),t(e)):process.nextTick(a,n)}),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},function(e,t,n){"use strict";var r=n(9).codes.ERR_INVALID_OPT_VALUE;e.exports={getHighWaterMark:function(e,t,n,a){var i=function(e,t,n){return null!=e.highWaterMark?e.highWaterMark:t?e[n]:null}(t,a,n);if(null!=i){if(!isFinite(i)||Math.floor(i)!==i||i<0)throw new r(a?n:"highWaterMark",i);return Math.floor(i)}return e.objectMode?16:16384}}},function(e,t,n){"use strict";function r(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,n){var r=e.entry;e.entry=null;for(;r;){var a=r.callback;t.pendingcb--,a(n),r=r.next}t.corkedRequestsFree.next=e}(t,e)}}var a;e.exports=x,x.WritableState=_;var i={deprecate:n(78)},o=n(94),s=n(14).Buffer,l=global.Uint8Array||function(){};var u,c=n(95),f=n(96).getHighWaterMark,h=n(9).codes,d=h.ERR_INVALID_ARG_TYPE,p=h.ERR_METHOD_NOT_IMPLEMENTED,m=h.ERR_MULTIPLE_CALLBACK,g=h.ERR_STREAM_CANNOT_PIPE,v=h.ERR_STREAM_DESTROYED,b=h.ERR_STREAM_NULL_VALUES,y=h.ERR_STREAM_WRITE_AFTER_END,w=h.ERR_UNKNOWN_ENCODING;function E(){}function _(e,t,i){a=a||n(10),e=e||{},"boolean"!=typeof i&&(i=t instanceof a),this.objectMode=!!e.objectMode,i&&(this.objectMode=this.objectMode||!!e.writableObjectMode),this.highWaterMark=f(this,e,"writableHighWaterMark",i),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var o=!1===e.decodeStrings;this.decodeStrings=!o,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var n=e._writableState,r=n.sync,a=n.writecb;if("function"!=typeof a)throw new m;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(n),t)!function(e,t,n,r,a){--t.pendingcb,n?(process.nextTick(a,r),process.nextTick(M,e,t),e._writableState.errorEmitted=!0,e.emit("error",r)):(a(r),e._writableState.errorEmitted=!0,e.emit("error",r),M(e,t))}(e,n,r,t,a);else{var i=C(n)||e.destroyed;i||n.corked||n.bufferProcessing||!n.bufferedRequest||S(e,n),r?process.nextTick(k,e,n,i,a):k(e,n,i,a)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==e.emitClose,this.bufferedRequestCount=0,this.corkedRequestsFree=new r(this)}function x(e){var t=this instanceof(a=a||n(10));if(!t&&!u.call(x,this))return new x(e);this._writableState=new _(e,this,t),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),o.call(this)}function F(e,t,n,r,a,i,o){t.writelen=r,t.writecb=o,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new v("write")):n?e._writev(a,t.onwrite):e._write(a,i,t.onwrite),t.sync=!1}function k(e,t,n,r){n||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,r(),M(e,t)}function S(e,t){t.bufferProcessing=!0;var n=t.bufferedRequest;if(e._writev&&n&&n.next){var a=t.bufferedRequestCount,i=new Array(a),o=t.corkedRequestsFree;o.entry=n;for(var s=0,l=!0;n;)i[s]=n,n.isBuf||(l=!1),n=n.next,s+=1;i.allBuffers=l,F(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new r(t),t.bufferedRequestCount=0}else{for(;n;){var u=n.chunk,c=n.encoding,f=n.callback;if(F(e,t,!1,t.objectMode?1:u.length,u,c,f),n=n.next,t.bufferedRequestCount--,t.writing)break}null===n&&(t.lastBufferedRequest=null)}t.bufferedRequest=n,t.bufferProcessing=!1}function C(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function j(e,t){e._final(function(n){t.pendingcb--,n&&e.emit("error",n),t.prefinished=!0,e.emit("prefinish"),M(e,t)})}function M(e,t){var n=C(t);return n&&(!function(e,t){t.prefinished||t.finalCalled||("function"!=typeof e._final||t.destroyed?(t.prefinished=!0,e.emit("prefinish")):(t.pendingcb++,t.finalCalled=!0,process.nextTick(j,e,t)))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),n}n(5)(x,o),_.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(_.prototype,"buffer",{get:i.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(u=Function.prototype[Symbol.hasInstance],Object.defineProperty(x,Symbol.hasInstance,{value:function(e){return!!u.call(this,e)||this===x&&(e&&e._writableState instanceof _)}})):u=function(e){return e instanceof this},x.prototype.pipe=function(){this.emit("error",new g)},x.prototype.write=function(e,t,n){var r,a=this._writableState,i=!1,o=!a.objectMode&&(r=e,s.isBuffer(r)||r instanceof l);return o&&!s.isBuffer(e)&&(e=function(e){return s.from(e)}(e)),"function"==typeof t&&(n=t,t=null),o?t="buffer":t||(t=a.defaultEncoding),"function"!=typeof n&&(n=E),a.ending?function(e,t){var n=new y;e.emit("error",n),process.nextTick(t,n)}(this,n):(o||function(e,t,n,r){var a;return null===n?a=new b:"string"==typeof n||t.objectMode||(a=new d("chunk",["string","Buffer"],n)),!a||(e.emit("error",a),process.nextTick(r,a),!1)}(this,a,e,n))&&(a.pendingcb++,i=function(e,t,n,r,a,i){if(!n){var o=function(e,t,n){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=s.from(t,n));return t}(t,r,a);r!==o&&(n=!0,a="buffer",r=o)}var l=t.objectMode?1:r.length;t.length+=l;var u=t.length<t.highWaterMark;u||(t.needDrain=!0);if(t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:r,encoding:a,isBuf:n,callback:i,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else F(e,t,!1,l,r,a,i);return u}(this,a,o,e,t,n)),i},x.prototype.cork=function(){this._writableState.corked++},x.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||S(this,e))},x.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new w(e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(x.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(x.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),x.prototype._write=function(e,t,n){n(new p("_write()"))},x.prototype._writev=null,x.prototype.end=function(e,t,n){var r=this._writableState;return"function"==typeof e?(n=e,e=null,t=null):"function"==typeof t&&(n=t,t=null),null!==e&&void 0!==e&&this.write(e,t),r.corked&&(r.corked=1,this.uncork()),r.ending||function(e,t,n){t.ending=!0,M(e,t),n&&(t.finished?process.nextTick(n):e.once("finish",n));t.ended=!0,e.writable=!1}(this,r,n),this},Object.defineProperty(x.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(x.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),x.prototype.destroy=c.destroy,x.prototype._undestroy=c.undestroy,x.prototype._destroy=function(e,t){t(e)}},function(e,t,n){"use strict";var r=n(184).Buffer,a=r.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function i(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(r.isEncoding===a||!a(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=l,this.end=u,t=4;break;case"utf8":this.fillLast=s,t=4;break;case"base64":this.text=c,this.end=f,t=3;break;default:return this.write=h,void(this.end=d)}this.lastNeed=0,this.lastTotal=0,this.lastChar=r.allocUnsafe(t)}function o(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function s(e){var t=this.lastTotal-this.lastNeed,n=function(e,t,n){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==n?n:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function l(e,t){if((e.length-t)%2==0){var n=e.toString("utf16le",t);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function u(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,n)}return t}function c(e,t){var n=(e.length-t)%3;return 0===n?e.toString("base64",t):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-n))}function f(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function h(e){return e.toString(this.encoding)}function d(e){return e&&e.length?this.write(e):""}t.StringDecoder=i,i.prototype.write=function(e){if(0===e.length)return"";var t,n;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<e.length?t?t+this.text(e,n):this.text(e,n):t||""},i.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},i.prototype.text=function(e,t){var n=function(e,t,n){var r=t.length-1;if(r<n)return 0;var a=o(t[r]);if(a>=0)return a>0&&(e.lastNeed=a-1),a;if(--r<n||-2===a)return 0;if((a=o(t[r]))>=0)return a>0&&(e.lastNeed=a-2),a;if(--r<n||-2===a)return 0;if((a=o(t[r]))>=0)return a>0&&(2===a?a=0:e.lastNeed=a-3),a;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=n;var r=e.length-(n-this.lastNeed);return e.copy(this.lastChar,0,r),e.toString("utf8",t,r)},i.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},function(e,t,n){"use strict";e.exports=u;var r=n(9).codes,a=r.ERR_METHOD_NOT_IMPLEMENTED,i=r.ERR_MULTIPLE_CALLBACK,o=r.ERR_TRANSFORM_ALREADY_TRANSFORMING,s=r.ERR_TRANSFORM_WITH_LENGTH_0,l=n(10);function u(e){if(!(this instanceof u))return new u(e);l.call(this,e),this._transformState={afterTransform:function(e,t){var n=this._transformState;n.transforming=!1;var r=n.writecb;if(null===r)return this.emit("error",new i);n.writechunk=null,n.writecb=null,null!=t&&this.push(t),r(e);var a=this._readableState;a.reading=!1,(a.needReadable||a.length<a.highWaterMark)&&this._read(a.highWaterMark)}.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",c)}function c(){var e=this;"function"!=typeof this._flush||this._readableState.destroyed?f(this,null,null):this._flush(function(t,n){f(e,t,n)})}function f(e,t,n){if(t)return e.emit("error",t);if(null!=n&&e.push(n),e._writableState.length)throw new s;if(e._transformState.transforming)throw new o;return e.push(null)}n(5)(u,l),u.prototype.push=function(e,t){return this._transformState.needTransform=!1,l.prototype.push.call(this,e,t)},u.prototype._transform=function(e,t,n){n(new a("_transform()"))},u.prototype._write=function(e,t,n){var r=this._transformState;if(r.writecb=n,r.writechunk=e,r.writeencoding=t,!r.transforming){var a=this._readableState;(r.needTransform||a.needReadable||a.length<a.highWaterMark)&&this._read(a.highWaterMark)}},u.prototype._read=function(e){var t=this._transformState;null===t.writechunk||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))},u.prototype._destroy=function(e,t){l.prototype._destroy.call(this,e,function(e){t(e)})}},function(e,t,n){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},function(e,t,n){var r=n(100),a={};for(var i in r)r.hasOwnProperty(i)&&(a[r[i]]=i);var o=e.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var s in o)if(o.hasOwnProperty(s)){if(!("channels"in o[s]))throw new Error("missing channels property: "+s);if(!("labels"in o[s]))throw new Error("missing channel labels property: "+s);if(o[s].labels.length!==o[s].channels)throw new Error("channel and label counts mismatch: "+s);var l=o[s].channels,u=o[s].labels;delete o[s].channels,delete o[s].labels,Object.defineProperty(o[s],"channels",{value:l}),Object.defineProperty(o[s],"labels",{value:u})}o.rgb.hsl=function(e){var t,n,r=e[0]/255,a=e[1]/255,i=e[2]/255,o=Math.min(r,a,i),s=Math.max(r,a,i),l=s-o;return s===o?t=0:r===s?t=(a-i)/l:a===s?t=2+(i-r)/l:i===s&&(t=4+(r-a)/l),(t=Math.min(60*t,360))<0&&(t+=360),n=(o+s)/2,[t,100*(s===o?0:n<=.5?l/(s+o):l/(2-s-o)),100*n]},o.rgb.hsv=function(e){var t,n,r,a,i,o=e[0]/255,s=e[1]/255,l=e[2]/255,u=Math.max(o,s,l),c=u-Math.min(o,s,l),f=function(e){return(u-e)/6/c+.5};return 0===c?a=i=0:(i=c/u,t=f(o),n=f(s),r=f(l),o===u?a=r-n:s===u?a=1/3+t-r:l===u&&(a=2/3+n-t),a<0?a+=1:a>1&&(a-=1)),[360*a,100*i,100*u]},o.rgb.hwb=function(e){var t=e[0],n=e[1],r=e[2];return[o.rgb.hsl(e)[0],100*(1/255*Math.min(t,Math.min(n,r))),100*(r=1-1/255*Math.max(t,Math.max(n,r)))]},o.rgb.cmyk=function(e){var t,n=e[0]/255,r=e[1]/255,a=e[2]/255;return[100*((1-n-(t=Math.min(1-n,1-r,1-a)))/(1-t)||0),100*((1-r-t)/(1-t)||0),100*((1-a-t)/(1-t)||0),100*t]},o.rgb.keyword=function(e){var t=a[e];if(t)return t;var n,i,o,s=1/0;for(var l in r)if(r.hasOwnProperty(l)){var u=r[l],c=(i=e,o=u,Math.pow(i[0]-o[0],2)+Math.pow(i[1]-o[1],2)+Math.pow(i[2]-o[2],2));c<s&&(s=c,n=l)}return n},o.keyword.rgb=function(e){return r[e]},o.rgb.xyz=function(e){var t=e[0]/255,n=e[1]/255,r=e[2]/255;return[100*(.4124*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.3576*(n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92)+.1805*(r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92)),100*(.2126*t+.7152*n+.0722*r),100*(.0193*t+.1192*n+.9505*r)]},o.rgb.lab=function(e){var t=o.rgb.xyz(e),n=t[0],r=t[1],a=t[2];return r/=100,a/=108.883,n=(n/=95.047)>.008856?Math.pow(n,1/3):7.787*n+16/116,[116*(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116)-16,500*(n-r),200*(r-(a=a>.008856?Math.pow(a,1/3):7.787*a+16/116))]},o.hsl.rgb=function(e){var t,n,r,a,i,o=e[0]/360,s=e[1]/100,l=e[2]/100;if(0===s)return[i=255*l,i,i];t=2*l-(n=l<.5?l*(1+s):l+s-l*s),a=[0,0,0];for(var u=0;u<3;u++)(r=o+1/3*-(u-1))<0&&r++,r>1&&r--,i=6*r<1?t+6*(n-t)*r:2*r<1?n:3*r<2?t+(n-t)*(2/3-r)*6:t,a[u]=255*i;return a},o.hsl.hsv=function(e){var t=e[0],n=e[1]/100,r=e[2]/100,a=n,i=Math.max(r,.01);return n*=(r*=2)<=1?r:2-r,a*=i<=1?i:2-i,[t,100*(0===r?2*a/(i+a):2*n/(r+n)),100*((r+n)/2)]},o.hsv.rgb=function(e){var t=e[0]/60,n=e[1]/100,r=e[2]/100,a=Math.floor(t)%6,i=t-Math.floor(t),o=255*r*(1-n),s=255*r*(1-n*i),l=255*r*(1-n*(1-i));switch(r*=255,a){case 0:return[r,l,o];case 1:return[s,r,o];case 2:return[o,r,l];case 3:return[o,s,r];case 4:return[l,o,r];case 5:return[r,o,s]}},o.hsv.hsl=function(e){var t,n,r,a=e[0],i=e[1]/100,o=e[2]/100,s=Math.max(o,.01);return r=(2-i)*o,n=i*s,[a,100*(n=(n/=(t=(2-i)*s)<=1?t:2-t)||0),100*(r/=2)]},o.hwb.rgb=function(e){var t,n,r,a,i,o,s,l=e[0]/360,u=e[1]/100,c=e[2]/100,f=u+c;switch(f>1&&(u/=f,c/=f),n=1-c,r=6*l-(t=Math.floor(6*l)),0!=(1&t)&&(r=1-r),a=u+r*(n-u),t){default:case 6:case 0:i=n,o=a,s=u;break;case 1:i=a,o=n,s=u;break;case 2:i=u,o=n,s=a;break;case 3:i=u,o=a,s=n;break;case 4:i=a,o=u,s=n;break;case 5:i=n,o=u,s=a}return[255*i,255*o,255*s]},o.cmyk.rgb=function(e){var t=e[0]/100,n=e[1]/100,r=e[2]/100,a=e[3]/100;return[255*(1-Math.min(1,t*(1-a)+a)),255*(1-Math.min(1,n*(1-a)+a)),255*(1-Math.min(1,r*(1-a)+a))]},o.xyz.rgb=function(e){var t,n,r,a=e[0]/100,i=e[1]/100,o=e[2]/100;return n=-.9689*a+1.8758*i+.0415*o,r=.0557*a+-.204*i+1.057*o,t=(t=3.2406*a+-1.5372*i+-.4986*o)>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,r=r>.0031308?1.055*Math.pow(r,1/2.4)-.055:12.92*r,[255*(t=Math.min(Math.max(0,t),1)),255*(n=Math.min(Math.max(0,n),1)),255*(r=Math.min(Math.max(0,r),1))]},o.xyz.lab=function(e){var t=e[0],n=e[1],r=e[2];return n/=100,r/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116)-16,500*(t-n),200*(n-(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116))]},o.lab.xyz=function(e){var t,n,r,a=e[0],i=e[1],o=e[2];t=i/500+(n=(a+16)/116),r=n-o/200;var s=Math.pow(n,3),l=Math.pow(t,3),u=Math.pow(r,3);return n=s>.008856?s:(n-16/116)/7.787,t=l>.008856?l:(t-16/116)/7.787,r=u>.008856?u:(r-16/116)/7.787,[t*=95.047,n*=100,r*=108.883]},o.lab.lch=function(e){var t,n=e[0],r=e[1],a=e[2];return(t=360*Math.atan2(a,r)/2/Math.PI)<0&&(t+=360),[n,Math.sqrt(r*r+a*a),t]},o.lch.lab=function(e){var t,n=e[0],r=e[1];return t=e[2]/360*2*Math.PI,[n,r*Math.cos(t),r*Math.sin(t)]},o.rgb.ansi16=function(e){var t=e[0],n=e[1],r=e[2],a=1 in arguments?arguments[1]:o.rgb.hsv(e)[2];if(0===(a=Math.round(a/50)))return 30;var i=30+(Math.round(r/255)<<2|Math.round(n/255)<<1|Math.round(t/255));return 2===a&&(i+=60),i},o.hsv.ansi16=function(e){return o.rgb.ansi16(o.hsv.rgb(e),e[2])},o.rgb.ansi256=function(e){var t=e[0],n=e[1],r=e[2];return t===n&&n===r?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(r/255*5)},o.ansi16.rgb=function(e){var t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),[t=t/10.5*255,t,t];var n=.5*(1+~~(e>50));return[(1&t)*n*255,(t>>1&1)*n*255,(t>>2&1)*n*255]},o.ansi256.rgb=function(e){if(e>=232){var t=10*(e-232)+8;return[t,t,t]}var n;return e-=16,[Math.floor(e/36)/5*255,Math.floor((n=e%36)/6)/5*255,n%6/5*255]},o.rgb.hex=function(e){var t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},o.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];var n=t[0];3===t[0].length&&(n=n.split("").map(function(e){return e+e}).join(""));var r=parseInt(n,16);return[r>>16&255,r>>8&255,255&r]},o.rgb.hcg=function(e){var t,n,r=e[0]/255,a=e[1]/255,i=e[2]/255,o=Math.max(Math.max(r,a),i),s=Math.min(Math.min(r,a),i),l=o-s;return t=l<1?s/(1-l):0,n=l<=0?0:o===r?(a-i)/l%6:o===a?2+(i-r)/l:4+(r-a)/l+4,n/=6,[360*(n%=1),100*l,100*t]},o.hsl.hcg=function(e){var t=e[1]/100,n=e[2]/100,r=1,a=0;return(r=n<.5?2*t*n:2*t*(1-n))<1&&(a=(n-.5*r)/(1-r)),[e[0],100*r,100*a]},o.hsv.hcg=function(e){var t=e[1]/100,n=e[2]/100,r=t*n,a=0;return r<1&&(a=(n-r)/(1-r)),[e[0],100*r,100*a]},o.hcg.rgb=function(e){var t=e[0]/360,n=e[1]/100,r=e[2]/100;if(0===n)return[255*r,255*r,255*r];var a,i=[0,0,0],o=t%1*6,s=o%1,l=1-s;switch(Math.floor(o)){case 0:i[0]=1,i[1]=s,i[2]=0;break;case 1:i[0]=l,i[1]=1,i[2]=0;break;case 2:i[0]=0,i[1]=1,i[2]=s;break;case 3:i[0]=0,i[1]=l,i[2]=1;break;case 4:i[0]=s,i[1]=0,i[2]=1;break;default:i[0]=1,i[1]=0,i[2]=l}return a=(1-n)*r,[255*(n*i[0]+a),255*(n*i[1]+a),255*(n*i[2]+a)]},o.hcg.hsv=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t),r=0;return n>0&&(r=t/n),[e[0],100*r,100*n]},o.hcg.hsl=function(e){var t=e[1]/100,n=e[2]/100*(1-t)+.5*t,r=0;return n>0&&n<.5?r=t/(2*n):n>=.5&&n<1&&(r=t/(2*(1-n))),[e[0],100*r,100*n]},o.hcg.hwb=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t);return[e[0],100*(n-t),100*(1-n)]},o.hwb.hcg=function(e){var t=e[1]/100,n=1-e[2]/100,r=n-t,a=0;return r<1&&(a=(n-r)/(1-r)),[e[0],100*r,100*a]},o.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},o.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},o.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},o.gray.hsl=o.gray.hsv=function(e){return[0,0,e[0]]},o.gray.hwb=function(e){return[0,100,e[0]]},o.gray.cmyk=function(e){return[0,0,0,e[0]]},o.gray.lab=function(e){return[e[0],0,0]},o.gray.hex=function(e){var t=255&Math.round(e[0]/100*255),n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n},o.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}},function(e,t,n){"use strict";var r=e.exports=function(e){return null!==e&&"object"==typeof e&&"function"==typeof e.pipe};r.writable=function(e){return r(e)&&!1!==e.writable&&"function"==typeof e._write&&"object"==typeof e._writableState},r.readable=function(e){return r(e)&&!1!==e.readable&&"function"==typeof e._read&&"object"==typeof e._readableState},r.duplex=function(e){return r.writable(e)&&r.readable(e)},r.transform=function(e){return r.duplex(e)&&"function"==typeof e._transform&&"object"==typeof e._transformState}},function(e,t,n){"use strict";const{LEVEL:r}=n(0),a=n(52),i=n(207),o=n(28)("winston:create-logger");e.exports=function(e={}){e.levels=e.levels||a.npm.levels;class t extends i{constructor(e){super(e)}}const n=new t(e);return Object.keys(e.levels).forEach(function(e){o('Define prototype method for "%s"',e),"log"!==e?(t.prototype[e]=function(...t){const a=this||n;if(1===t.length){const[i]=t,o=i&&i.message&&i||{message:i};return o.level=o[r]=e,a._addDefaultMeta(o),a.write(o),this||n}return 0===t.length?(a.log(e,""),a):a.log(e,...t)},t.prototype[function(e){return"is"+e.charAt(0).toUpperCase()+e.slice(1)+"Enabled"}(e)]=function(){return(this||n).isLevelEnabled(e)}):console.warn('Level "log" not defined: conflicts with the method "log". Use a different level name.')}),n}},function(e,t,n){"use strict";const r=n(6),a=n(53),i=n(28)("winston:exception"),o=n(105),s=n(106),l=n(107);e.exports=class{constructor(e){if(!e)throw new Error("Logger is required to handle exceptions");this.logger=e,this.handlers=new Map}handle(...e){e.forEach(e=>{if(Array.isArray(e))return e.forEach(e=>this._addHandler(e));this._addHandler(e)}),this.catcher||(this.catcher=this._uncaughtException.bind(this),process.on("uncaughtException",this.catcher))}unhandle(){this.catcher&&(process.removeListener("uncaughtException",this.catcher),this.catcher=!1,Array.from(this.handlers.values()).forEach(e=>this.logger.unpipe(e)))}getAllInfo(e){let{message:t}=e;return t||"string"!=typeof e||(t=e),{error:e,level:"error",message:[`uncaughtException: ${t||"(no error message)"}`,e.stack||"  No stack trace"].join("\n"),stack:e.stack,exception:!0,date:(new Date).toString(),process:this.getProcessInfo(),os:this.getOsInfo(),trace:this.getTrace(e)}}getProcessInfo(){return{pid:process.pid,uid:process.getuid?process.getuid():null,gid:process.getgid?process.getgid():null,cwd:process.cwd(),execPath:process.execPath,version:process.version,argv:process.argv,memoryUsage:process.memoryUsage()}}getOsInfo(){return{loadavg:r.loadavg(),uptime:r.uptime()}}getTrace(e){return(e?s.parse(e):s.get()).map(e=>({column:e.getColumnNumber(),file:e.getFileName(),function:e.getFunctionName(),line:e.getLineNumber(),method:e.getMethodName(),native:e.isNative()}))}_addHandler(e){if(!this.handlers.has(e)){e.handleExceptions=!0;const t=new l(e);this.handlers.set(e,t),this.logger.pipe(t)}}_uncaughtException(e){const t=this.getAllInfo(e),n=this._getExceptionHandlers();let r,s="function"==typeof this.logger.exitOnError?this.logger.exitOnError(e):this.logger.exitOnError;function l(){i("doExit",s),i("process._exiting",process._exiting),s&&!process._exiting&&(r&&clearTimeout(r),process.exit(1))}if(!n.length&&s&&(console.warn("winston: exitOnError cannot be true with no exception handlers."),console.warn("winston: not exiting process."),s=!1),!n||0===n.length)return process.nextTick(l);a(n,(e,t)=>{const n=o(t),r=e.transport||e;function a(e){return()=>{i(e),n()}}r._ending=!0,r.once("finish",a("finished")),r.once("error",a("error"))},()=>s&&l()),this.logger.log(t),s&&(r=setTimeout(l,3e3))}_getExceptionHandlers(){return this.logger.transports.filter(e=>{return(e.transport||e).handleExceptions})}}},function(e,t,n){"use strict";e.exports=function(e){var t,n=0;function r(){return n?t:(n=1,t=e.apply(this,arguments),e=null,t)}return r.displayName=e.displayName||e.name||r.displayName||r.name,r}},function(e,t){function n(e){for(var t in e)this[t]=e[t]}t.get=function(e){var n=Error.stackTraceLimit;Error.stackTraceLimit=1/0;var r={},a=Error.prepareStackTrace;Error.prepareStackTrace=function(e,t){return t},Error.captureStackTrace(r,e||t.get);var i=r.stack;return Error.prepareStackTrace=a,Error.stackTraceLimit=n,i},t.parse=function(e){if(!e.stack)return[];var t=this;return e.stack.split("\n").slice(1).map(function(e){if(e.match(/^\s*[-]{4,}$/))return t._createParsedCallSite({fileName:e,lineNumber:null,functionName:null,typeName:null,methodName:null,columnNumber:null,native:null});var n=e.match(/at (?:(.+)\s+\()?(?:(.+?):(\d+)(?::(\d+))?|([^)]+))\)?/);if(n){var r=null,a=null,i=null,o=null,s=null,l="native"===n[5];if(n[1]){var u=(i=n[1]).lastIndexOf(".");if("."==i[u-1]&&u--,u>0){r=i.substr(0,u),a=i.substr(u+1);var c=r.indexOf(".Module");c>0&&(i=i.substr(c+1),r=r.substr(0,c))}o=null}a&&(o=r,s=a),"<anonymous>"===a&&(s=null,i=null);var f={fileName:n[2]||null,lineNumber:parseInt(n[3],10)||null,functionName:i,typeName:o,methodName:s,columnNumber:parseInt(n[4],10)||null,native:l};return t._createParsedCallSite(f)}}).filter(function(e){return!!e})};["this","typeName","functionName","methodName","fileName","lineNumber","columnNumber","function","evalOrigin"].forEach(function(e){n.prototype[e]=null,n.prototype["get"+e[0].toUpperCase()+e.substr(1)]=function(){return this[e]}}),["topLevel","eval","native","constructor"].forEach(function(e){n.prototype[e]=!1,n.prototype["is"+e[0].toUpperCase()+e.substr(1)]=function(){return this[e]}}),t._createParsedCallSite=function(e){return new n(e)}},function(e,t,n){"use strict";const{Writable:r}=n(15);e.exports=class extends r{constructor(e){if(super({objectMode:!0}),!e)throw new Error("ExceptionStream requires a TransportStream instance.");this.handleExceptions=!0,this.transport=e}_write(e,t,n){return e.exception?this.transport.log(e,n):(n(),!0)}}},function(e,t,n){"use strict";const r=n(6),a=n(53),i=n(28)("winston:rejection"),o=n(105),s=n(106),l=n(107);e.exports=class{constructor(e){if(!e)throw new Error("Logger is required to handle rejections");this.logger=e,this.handlers=new Map}handle(...e){e.forEach(e=>{if(Array.isArray(e))return e.forEach(e=>this._addHandler(e));this._addHandler(e)}),this.catcher||(this.catcher=this._unhandledRejection.bind(this),process.on("unhandledRejection",this.catcher))}unhandle(){this.catcher&&(process.removeListener("unhandledRejection",this.catcher),this.catcher=!1,Array.from(this.handlers.values()).forEach(e=>this.logger.unpipe(e)))}getAllInfo(e){let{message:t}=e;return t||"string"!=typeof e||(t=e),{error:e,level:"error",message:[`unhandledRejection: ${t||"(no error message)"}`,e.stack||"  No stack trace"].join("\n"),stack:e.stack,exception:!0,date:(new Date).toString(),process:this.getProcessInfo(),os:this.getOsInfo(),trace:this.getTrace(e)}}getProcessInfo(){return{pid:process.pid,uid:process.getuid?process.getuid():null,gid:process.getgid?process.getgid():null,cwd:process.cwd(),execPath:process.execPath,version:process.version,argv:process.argv,memoryUsage:process.memoryUsage()}}getOsInfo(){return{loadavg:r.loadavg(),uptime:r.uptime()}}getTrace(e){return(e?s.parse(e):s.get()).map(e=>({column:e.getColumnNumber(),file:e.getFileName(),function:e.getFunctionName(),line:e.getLineNumber(),method:e.getMethodName(),native:e.isNative()}))}_addHandler(e){if(!this.handlers.has(e)){e.handleExceptions=!0;const t=new l(e);this.handlers.set(e,t),this.logger.pipe(t)}}_unhandledRejection(e){const t=this.getAllInfo(e),n=this._getRejectionHandlers();let r,s="function"==typeof this.logger.exitOnError?this.logger.exitOnError(e):this.logger.exitOnError;function l(){i("doExit",s),i("process._exiting",process._exiting),s&&!process._exiting&&(r&&clearTimeout(r),process.exit(1))}if(!n.length&&s&&(console.warn("winston: exitOnError cannot be true with no rejection handlers."),console.warn("winston: not exiting process."),s=!1),!n||0===n.length)return process.nextTick(l);a(n,(e,t)=>{const n=o(t),r=e.transport||e;function a(e){return()=>{i(e),n()}}r._ending=!0,r.once("finish",a("finished")),r.once("error",a("error"))},()=>s&&l()),this.logger.log(t),s&&(r=setTimeout(l,3e3))}_getRejectionHandlers(){return this.logger.transports.filter(e=>{return(e.transport||e).handleRejections})}}},function(e,t,n){n(110),e.exports=n(111)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(11);global.__rootDir=r.join(__dirname,"../")},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(a,i){function o(e){try{l(r.next(e))}catch(e){i(e)}}function s(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?a(e.value):new n(function(t){t(e.value)}).then(o,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const a=n(29),i=a.default.pluginName,o=n(54);o.client.start({name:i,version:""},"thunder"),n(116);const s=n(17).default.getLogger("VipPluginController:main");s.info("init"),function(){return r(this,void 0,void 0,function*(){s.info("SetPluginStatus","VipRenewButton","ready"),o.client.callServerFunction("SetPluginStatus","VipRenewButton","ready"),yield function(){return r(this,void 0,void 0,function*(){s.info("trackInit");let e=new Map,t=yield o.client.callServerFunction("IsLogined"),n=!1,r=0;if(t)try{let e=JSON.parse(yield o.client.callServerFunction("GetUserInfo",2));c(e)&&(r=Number(e.vasType).valueOf(),n=Boolean("1"===e.isVip))}catch(e){s.info("trackInit err",e)}e.set("is_login",t?1:0),e.set("is_vip",n?1:0),e.set("vip_type",r),e.set("plugin_name",i),e.set("plugin_version",a.default.pluginVersion);let u={attribute1:"plugin_init",extData:e};yield l.StatUtilitiesNS.trackEvent("xlx_vip_event",u)})}()})}().catch();const l=n(213),u=n(214),{isDef:c}=u.ThunderUtil},function(e){e.exports=JSON.parse('{"name":"vip-plugin-controller","version":"2.0.1","author":"Xunlei","license":"","description":"","main":"2.0.1/index.js","clear":true}')},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(114),a=n(12),i=n(55),o=n(115),s=n(57);t.Client=class extends a.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=e.context),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer)this.inprocess=!0;else{let t=s.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new o.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("end",()=>{i.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{i.error(e.message)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){i.error(e.message)}else i.information("This socket has been ended by the other party")}}},function(e,t){e.exports=require("net")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(56),a=n(12);t.Parser=class extends a.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),a=JSON.parse(e);this.emit("message",a),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(16),a=n(11),i=n(17),o=n(29);let s=a.join(__rootDir,o.default.pluginVersion,"log-options.json");if(r.existsSync(s)){const e={label:"main",options:s};i.default.start(e)}},function(e,t,n){"use strict";const r=n(4),{warn:a}=n(76),i=t;i.version=n(143).version,i.transports=n(144),i.config=n(52),i.addColors=r.levels,i.format=r.format,i.createLogger=n(103),i.ExceptionHandler=n(104),i.RejectionHandler=n(108),i.Container=n(212),i.Transport=n(8),i.loggers=new i.Container;const o=i.createLogger();Object.keys(i.config.npm.levels).concat(["log","query","stream","add","remove","clear","profile","startTimer","handleExceptions","unhandleExceptions","handleRejections","unhandleRejections","configure"]).forEach(e=>i[e]=((...t)=>o[e](...t))),Object.defineProperty(i,"level",{get:()=>o.level,set(e){o.level=e}}),Object.defineProperty(i,"exceptions",{get:()=>o.exceptions}),["exitOnError"].forEach(e=>{Object.defineProperty(i,e,{get:()=>o[e],set(t){o[e]=t}})}),Object.defineProperty(i,"default",{get:()=>({exceptionHandlers:o.exceptionHandlers,rejectionHandlers:o.rejectionHandlers,transports:o.transports})}),a.deprecated(i,"setLevels"),a.forFunctions(i,"useFormat",["cli"]),a.forProperties(i,"useFormat",["padLevels","stripColors"]),a.forFunctions(i,"deprecated",["addRewriter","addFilter","clone","extend"]),a.forProperties(i,"deprecated",["emitErrs","levelLength"]),a.moved(i,"createLogger","Logger")},function(e,t,n){var r={};e.exports=r,r.themes={};var a=n(1),i=r.styles=n(119),o=Object.defineProperties,s=new RegExp(/[\r\n]+/g);r.supportsColor=n(120).supportsColor,void 0===r.enabled&&(r.enabled=!1!==r.supportsColor()),r.enable=function(){r.enabled=!0},r.disable=function(){r.enabled=!1},r.stripColors=r.strip=function(e){return(""+e).replace(/\x1B\[\d+m/g,"")};r.stylize=function(e,t){return r.enabled?i[t].open+e+i[t].close:e+""};var l=/[|\\{}()[\]^$+*?.]/g;function u(e){var t=function e(){return function(){var e=Array.prototype.slice.call(arguments).map(function(e){return void 0!==e&&e.constructor===String?e:a.inspect(e)}).join(" ");if(!r.enabled||!e)return e;var t=-1!=e.indexOf("\n"),n=this._styles,o=n.length;for(;o--;){var l=i[n[o]];e=l.open+e.replace(l.closeRe,l.open)+l.close,t&&(e=e.replace(s,function(e){return l.close+e+l.open}))}return e}.apply(e,arguments)};return t._styles=e,t.__proto__=h,t}var c,f=(c={},i.grey=i.gray,Object.keys(i).forEach(function(e){i[e].closeRe=new RegExp(function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(l,"\\$&")}(i[e].close),"g"),c[e]={get:function(){return u(this._styles.concat(e))}}}),c),h=o(function(){},f);r.setTheme=function(e){if("string"!=typeof e)for(var t in e)!function(t){r[t]=function(n){if("object"==typeof e[t]){var a=n;for(var i in e[t])a=r[e[t][i]](a);return a}return r[e[t]](n)}}(t);else console.log("colors.setTheme now only accepts an object, not a string.  If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));")};var d=function(e,t){var n=t.split("");return(n=n.map(e)).join("")};for(var p in r.trap=n(122),r.zalgo=n(123),r.maps={},r.maps.america=n(124)(r),r.maps.zebra=n(125)(r),r.maps.rainbow=n(126)(r),r.maps.random=n(127)(r),r.maps)!function(e){r[e]=function(t){return d(r.maps[e],t)}}(p);o(r,function(){var e={};return Object.keys(f).forEach(function(t){e[t]={get:function(){return u([t])}}}),e}())},function(e,t){var n={};e.exports=n;var r={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],grey:[90,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],blackBG:[40,49],redBG:[41,49],greenBG:[42,49],yellowBG:[43,49],blueBG:[44,49],magentaBG:[45,49],cyanBG:[46,49],whiteBG:[47,49]};Object.keys(r).forEach(function(e){var t=r[e],a=n[e]=[];a.open="["+t[0]+"m",a.close="["+t[1]+"m"})},function(e,t,n){"use strict";var r=n(6),a=n(121),i=Object({PROCESS_NAME:"main"}),o=void 0;function s(e){return function(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}(function(e){if(!1===o)return 0;if(a("color=16m")||a("color=full")||a("color=truecolor"))return 3;if(a("color=256"))return 2;if(e&&!e.isTTY&&!0!==o)return 0;var t=o?1:0;if("win32"===process.platform){var n=r.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(n[0])>=10&&Number(n[2])>=10586?Number(n[2])>=14931?3:2:1}if("CI"in i)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(function(e){return e in i})||"codeship"===i.CI_NAME?1:t;if("TEAMCITY_VERSION"in i)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(i.TEAMCITY_VERSION)?1:0;if("TERM_PROGRAM"in i){var s=parseInt((i.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(i.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Hyper":return 3;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(i.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(i.TERM)?1:"COLORTERM"in i?1:(i.TERM,t)}(e))}a("no-color")||a("no-colors")||a("color=false")?o=!1:(a("color")||a("colors")||a("color=true")||a("color=always"))&&(o=!0),"FORCE_COLOR"in i&&(o=0===i.FORCE_COLOR.length||0!==parseInt(i.FORCE_COLOR,10)),e.exports={supportsColor:s,stdout:s(process.stdout),stderr:s(process.stderr)}},function(e,t,n){"use strict";e.exports=function(e,t){var n=(t=t||process.argv).indexOf("--"),r=/^-{1,2}/.test(e)?"":"--",a=t.indexOf(r+e);return-1!==a&&(-1===n||a<n)}},function(e,t){e.exports=function(e,t){var n="",r={a:["@","Ą","Ⱥ","Ʌ","Δ","Λ","Д"],b:["ß","Ɓ","Ƀ","ɮ","β","฿"],c:["©","Ȼ","Ͼ"],d:["Ð","Ɗ","Ԁ","ԁ","Ԃ","ԃ"],e:["Ë","ĕ","Ǝ","ɘ","Σ","ξ","Ҽ","੬"],f:["Ӻ"],g:["ɢ"],h:["Ħ","ƕ","Ң","Һ","Ӈ","Ԋ"],i:["༏"],j:["Ĵ"],k:["ĸ","Ҡ","Ӄ","Ԟ"],l:["Ĺ"],m:["ʍ","Ӎ","ӎ","Ԡ","ԡ","൩"],n:["Ñ","ŋ","Ɲ","Ͷ","Π","Ҋ"],o:["Ø","õ","ø","Ǿ","ʘ","Ѻ","ם","۝","๏"],p:["Ƿ","Ҏ"],q:["্"],r:["®","Ʀ","Ȑ","Ɍ","ʀ","Я"],s:["§","Ϟ","ϟ","Ϩ"],t:["Ł","Ŧ","ͳ"],u:["Ʊ","Ս"],v:["ט"],w:["Ш","Ѡ","Ѽ","൰"],x:["Ҳ","Ӿ","Ӽ","ӽ"],y:["¥","Ұ","Ӌ"],z:["Ƶ","ɀ"]};return(e=(e=e||"Run the trap, drop the bass").split("")).forEach(function(e){e=e.toLowerCase();var t=r[e]||[" "],a=Math.floor(Math.random()*t.length);n+=void 0!==r[e]?r[e][a]:e}),n}},function(e,t){e.exports=function(e,t){e=e||"   he is here   ";var n={up:["̍","̎","̄","̅","̿","̑","̆","̐","͒","͗","͑","̇","̈","̊","͂","̓","̈","͊","͋","͌","̃","̂","̌","͐","̀","́","̋","̏","̒","̓","̔","̽","̉","ͣ","ͤ","ͥ","ͦ","ͧ","ͨ","ͩ","ͪ","ͫ","ͬ","ͭ","ͮ","ͯ","̾","͛","͆","̚"],down:["̖","̗","̘","̙","̜","̝","̞","̟","̠","̤","̥","̦","̩","̪","̫","̬","̭","̮","̯","̰","̱","̲","̳","̹","̺","̻","̼","ͅ","͇","͈","͉","͍","͎","͓","͔","͕","͖","͙","͚","̣"],mid:["̕","̛","̀","́","͘","̡","̢","̧","̨","̴","̵","̶","͜","͝","͞","͟","͠","͢","̸","̷","͡"," ҉"]},r=[].concat(n.up,n.down,n.mid);function a(e){return Math.floor(Math.random()*e)}function i(e){var t=!1;return r.filter(function(n){t=n===e}),t}return function(e,t){var r,o,s="";for(o in(t=t||{}).up=void 0===t.up||t.up,t.mid=void 0===t.mid||t.mid,t.down=void 0===t.down||t.down,t.size=void 0!==t.size?t.size:"maxi",e=e.split(""))if(!i(o)){switch(s+=e[o],r={up:0,down:0,mid:0},t.size){case"mini":r.up=a(8),r.mid=a(2),r.down=a(8);break;case"maxi":r.up=a(16)+3,r.mid=a(4)+1,r.down=a(64)+3;break;default:r.up=a(8)+1,r.mid=a(6)/2,r.down=a(8)+1}var l=["up","mid","down"];for(var u in l)for(var c=l[u],f=0;f<=r[c];f++)t[c]&&(s+=n[c][a(n[c].length)])}return s}(e,t)}},function(e,t){e.exports=function(e){return function(t,n,r){if(" "===t)return t;switch(n%3){case 0:return e.red(t);case 1:return e.white(t);case 2:return e.blue(t)}}}},function(e,t){e.exports=function(e){return function(t,n,r){return n%2==0?t:e.inverse(t)}}},function(e,t){e.exports=function(e){var t=["red","yellow","green","blue","magenta"];return function(n,r,a){return" "===n?n:e[t[r++%t.length]](n)}}},function(e,t){e.exports=function(e){var t=["underline","inverse","grey","yellow","red","green","blue","white","cyan","magenta"];return function(n,r,a){return" "===n?n:e[t[Math.round(Math.random()*(t.length-2))]](n)}}},function(e,t,n){"use strict";Object.defineProperty(t,"cli",{value:n(129)}),Object.defineProperty(t,"npm",{value:n(130)}),Object.defineProperty(t,"syslog",{value:n(131)})},function(e,t,n){"use strict";t.levels={error:0,warn:1,help:2,data:3,info:4,debug:5,prompt:6,verbose:7,input:8,silly:9},t.colors={error:"red",warn:"yellow",help:"cyan",data:"grey",info:"green",debug:"blue",prompt:"grey",verbose:"cyan",input:"grey",silly:"magenta"}},function(e,t,n){"use strict";t.levels={error:0,warn:1,info:2,http:3,verbose:4,debug:5,silly:6},t.colors={error:"red",warn:"yellow",info:"green",http:"green",verbose:"cyan",debug:"blue",silly:"magenta"}},function(e,t,n){"use strict";t.levels={emerg:0,alert:1,crit:2,error:3,warning:4,notice:5,info:6,debug:7},t.colors={emerg:"red",alert:"yellow",crit:"red",error:"red",warning:"red",notice:"yellow",info:"green",debug:"blue"}},function(e,t,n){var r={"./align.js":58,"./browser.js":133,"./cli.js":59,"./colorize.js":18,"./combine.js":60,"./dist/align.js":33,"./dist/browser.js":71,"./dist/cli.js":34,"./dist/colorize.js":13,"./dist/combine.js":35,"./dist/errors.js":72,"./dist/format.js":2,"./dist/index.js":73,"./dist/json.js":36,"./dist/label.js":37,"./dist/levels.js":20,"./dist/logstash.js":38,"./dist/metadata.js":39,"./dist/ms.js":74,"./dist/pad-levels.js":21,"./dist/pretty-print.js":40,"./dist/printf.js":41,"./dist/simple.js":42,"./dist/splat.js":43,"./dist/timestamp.js":44,"./dist/uncolorize.js":45,"./errors.js":135,"./examples/combine.js":136,"./examples/filter.js":137,"./examples/invalid.js":138,"./examples/metadata.js":139,"./examples/padLevels.js":140,"./examples/volume.js":141,"./format.js":3,"./index.js":4,"./json.js":32,"./label.js":61,"./levels.js":30,"./logstash.js":62,"./metadata.js":63,"./ms.js":142,"./pad-levels.js":31,"./pretty-print.js":64,"./printf.js":65,"./simple.js":66,"./splat.js":67,"./timestamp.js":68,"./uncolorize.js":70};function a(e){var t=i(e);return n(t)}function i(e){if(!n.o(r,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return r[e]}a.keys=function(){return Object.keys(r)},a.resolve=i,e.exports=a,a.id=132},function(e,t,n){"use strict";const r=t.format=n(3);t.levels=n(30),Object.defineProperty(r,"align",{value:n(58)}),Object.defineProperty(r,"cli",{value:n(59)}),Object.defineProperty(r,"combine",{value:n(60)}),Object.defineProperty(r,"colorize",{value:n(18)}),Object.defineProperty(r,"json",{value:n(32)}),Object.defineProperty(r,"label",{value:n(61)}),Object.defineProperty(r,"logstash",{value:n(62)}),Object.defineProperty(r,"metadata",{value:n(63)}),Object.defineProperty(r,"padLevels",{value:n(31)}),Object.defineProperty(r,"prettyPrint",{value:n(64)}),Object.defineProperty(r,"printf",{value:n(65)}),Object.defineProperty(r,"simple",{value:n(66)}),Object.defineProperty(r,"splat",{value:n(67)}),Object.defineProperty(r,"timestamp",{value:n(68)}),Object.defineProperty(r,"uncolorize",{value:n(70)})},function(e,t,n){var r={"./align.js":33,"./browser.js":71,"./cli.js":34,"./colorize.js":13,"./combine.js":35,"./errors.js":72,"./format.js":2,"./index.js":73,"./json.js":36,"./label.js":37,"./levels.js":20,"./logstash.js":38,"./metadata.js":39,"./ms.js":74,"./pad-levels.js":21,"./pretty-print.js":40,"./printf.js":41,"./simple.js":42,"./splat.js":43,"./timestamp.js":44,"./uncolorize.js":45};function a(e){var t=i(e);return n(t)}function i(e){if(!n.o(r,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return r[e]}a.keys=function(){return Object.keys(r)},a.resolve=i,e.exports=a,a.id=134},function(e,t,n){"use strict";const r=n(3),{LEVEL:a,MESSAGE:i}=n(0);e.exports=r((e,{stack:t})=>{if(e instanceof Error){const n=Object.assign({},e,{level:e.level,[a]:e[a]||e.level,message:e.message,[i]:e[i]||e.message});return t&&(n.stack=e.stack),n}if(!(e.message instanceof Error))return e;Object.assign(e,e.message);const n=e.message;return e.message=n.message,e[i]=n.message,t&&(e.stack=n.stack),e})},function(e,t,n){const{format:r}=n(4),{combine:a,timestamp:i,label:o}=r,s=a(o({label:"right meow!"}),i()).transform({level:"info",message:"What time is the testing at?"});console.dir(s)},function(e,t,n){const{format:r}=n(4),{combine:a,timestamp:i,label:o}=r,s=r((e,t)=>!e.private&&e)();console.dir(s.transform({level:"error",message:"Public error to share"})),console.dir(s.transform({level:"error",private:!0,message:"This is super secret - hide it."}));const l=r.combine(r(e=>!1)(),r(e=>{throw new Error("Never reached")})());console.dir(l.transform({level:"info",message:"wow such testing"}))},function(e,t,n){const{format:r}=n(4);r(function(e,t,n,r){return e})},function(e,t,n){const{format:r}=n(4),{combine:a,json:i,metadata:o,timestamp:s}=r,l=a(s(),o(),i()).transform({level:"info",message:"This should be a message.",application:"Microsoft Office",store:"Big Box Store",purchaseAmount:"9.99"});console.dir(l);const u=a(s(),o({fillExcept:["message","level","timestamp"]}),i()).transform({level:"info",message:"This should have attached metadata",category:"movies",subCategory:"action"});console.dir(u);const c=a(s(),o({fillWith:["publisher","author","book"],key:"bookInfo"}),i()).transform({level:"debug",message:"This message should be outside of the bookInfo object",publisher:"Lorem Press",author:"Albert Einstein",book:"4D Chess for Dummies",label:"myCustomLabel"});console.dir(c);const f=a(s(),o({fillWith:["publisher","author","book"],key:"bookInfo"}),o({fillWith:["purchasePrice","purchaseDate","transactionId"],key:"transactionInfo"}),o({fillExcept:["level","message","label","timestamp"]}),i()).transform({level:"debug",message:"This message should be outside of the bookInfo object",publisher:"Lorem Press",author:"Albert Einstein",book:"4D Chess for Dummies",label:"myCustomLabel",purchasePrice:"9.99",purchaseDate:"2.10.2018",transactionId:"123ABC"});console.dir(f)},function(e,t,n){const{format:r}=n(4),{combine:a,padLevels:i,simple:o}=r,{MESSAGE:s}=n(0),l=a(i({levels:{error:0,warn:1,info:2,http:3,verbose:4,debug:5,silly:6}}),o()),u=l.transform({level:"info",message:"This is an info level message."}),c=l.transform({level:"error",message:"This is an error level message."}),f=l.transform({level:"verbose",message:"This is a verbose level message."});console.dir(u[s]),console.dir(c[s]),console.dir(f[s])},function(e,t,n){const{format:r}=n(4),a=r((e,t)=>(t.yell?e.message=e.message.toUpperCase():t.whisper&&(e.message=e.message.toLowerCase()),e)),i=a({yell:!0});console.dir(i.transform({level:"info",message:"sorry for making you YELL in your head!"},i.options));const o=a({whisper:!0});console.dir(o.transform({level:"info",message:"WHY ARE THEY MAKING US YELL SO MUCH!"},o.options))},function(e,t,n){"use strict";const r=n(3),a=n(75);e.exports=r(e=>{const t=+new Date;return this.diff=t-(this.prevTime||t),this.prevTime=t,e.ms=`+${a(this.diff)}`,e})},function(e){e.exports=JSON.parse('{"_from":"winston@^3.2.1","_id":"winston@3.2.1","_inBundle":false,"_integrity":"sha1-YwYTd5dsc1hAKL4kkKGEYFX3fwc=","_location":"/winston","_phantomChildren":{"inherits":"2.0.4","util-deprecate":"1.0.2"},"_requested":{"type":"range","registry":true,"raw":"winston@^3.2.1","name":"winston","escapedName":"winston","rawSpec":"^3.2.1","saveSpec":null,"fetchSpec":"^3.2.1"},"_requiredBy":["/@xunlei/winston-easy-logger"],"_resolved":"http://xnpm.repo.xunlei.cn/winston/-/winston-3.2.1.tgz","_shasum":"63061377976c73584028be2490a1846055f77f07","_spec":"winston@^3.2.1","_where":"D:\\\\work\\\\thunderx_plugin\\\\vip_plugin_controller\\\\node_modules\\\\@xunlei\\\\winston-easy-logger","author":{"name":"Charlie Robbins","email":"<EMAIL>"},"browser":"./dist/winston","bugs":{"url":"https://github.com/winstonjs/winston/issues"},"bundleDependencies":false,"dependencies":{"async":"^2.6.1","diagnostics":"^1.1.1","is-stream":"^1.1.0","logform":"^2.1.1","one-time":"0.0.4","readable-stream":"^3.1.1","stack-trace":"0.0.x","triple-beam":"^1.3.0","winston-transport":"^4.3.0"},"deprecated":false,"description":"A logger for just about everything.","devDependencies":{"@babel/cli":"^7.2.3","@babel/core":"^7.2.2","@babel/preset-env":"^7.3.1","@types/node":"^10.12.19","abstract-winston-transport":">= 0.5.1","assume":"^2.1.0","colors":"^1.3.3","cross-spawn-async":"^2.2.5","eslint-config-populist":"^4.2.0","hock":"^1.3.3","mocha":"^5.2.0","nyc":"^13.1.0","rimraf":"^2.6.3","split2":"^3.1.0","std-mocks":"^1.0.1","through2":"^3.0.0","winston-compat":"^0.1.4"},"engines":{"node":">= 6.4.0"},"homepage":"https://github.com/winstonjs/winston#readme","keywords":["winston","logger","logging","logs","sysadmin","bunyan","pino","loglevel","tools","json","stream"],"license":"MIT","main":"./lib/winston","maintainers":[{"name":"Jarrett Cruger","email":"<EMAIL>"},{"name":"Chris Alderson","email":"<EMAIL>"},{"name":"David Hyde","email":"<EMAIL>"}],"name":"winston","repository":{"type":"git","url":"git+https://github.com/winstonjs/winston.git"},"scripts":{"build":"rimraf dist && babel lib -d dist","lint":"populist lib/*.js lib/winston/*.js lib/winston/**/*.js","prepublishOnly":"npm run build","pretest":"npm run lint","test":"nyc --reporter=text --reporter lcov npm run test:mocha","test:mocha":"mocha test/*.test.js test/**/*.test.js --exit"},"types":"./index.d.ts","version":"3.2.1"}')},function(e,t,n){"use strict";Object.defineProperty(t,"Console",{configurable:!0,enumerable:!0,get:()=>n(145)}),Object.defineProperty(t,"File",{configurable:!0,enumerable:!0,get:()=>n(151)}),Object.defineProperty(t,"Http",{configurable:!0,enumerable:!0,get:()=>n(203)}),Object.defineProperty(t,"Stream",{configurable:!0,enumerable:!0,get:()=>n(206)})},function(e,t,n){"use strict";const r=n(6),{LEVEL:a,MESSAGE:i}=n(0),o=n(8);e.exports=class extends o{constructor(e={}){super(e),this.name=e.name||"console",this.stderrLevels=this._stringArrayToSet(e.stderrLevels),this.consoleWarnLevels=this._stringArrayToSet(e.consoleWarnLevels),this.eol=e.eol||r.EOL,this.setMaxListeners(30)}log(e,t){return setImmediate(()=>this.emit("logged",e)),this.stderrLevels[e[a]]?(console._stderr?console._stderr.write(`${e[i]}${this.eol}`):console.error(e[i]),void(t&&t())):this.consoleWarnLevels[e[a]]?(console._stderr?console._stderr.write(`${e[i]}${this.eol}`):console.warn(e[i]),void(t&&t())):(console._stdout?console._stdout.write(`${e[i]}${this.eol}`):console.log(e[i]),void(t&&t()))}_stringArrayToSet(e,t){if(!e)return{};if(t=t||"Cannot make set from type other than Array of string elements",!Array.isArray(e))throw new Error(t);return e.reduce((e,n)=>{if("string"!=typeof n)throw new Error(t);return e[n]=!0,e},{})}}},function(e,t,n){var r=n(22),a=n(77);"disable"===Object({PROCESS_NAME:"main"}).READABLE_STREAM?e.exports=r&&r.Writable||a:e.exports=a},function(e,t){"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}}},function(e,t,n){"use strict";var r=n(23);e.exports=b;var a,i=n(149);b.ReadableState=v;n(12).EventEmitter;var o=function(e,t){return e.listeners(t).length},s=n(79),l=n(24).Buffer,u=global.Uint8Array||function(){};var c=n(46);c.inherits=n(5);var f=n(1),h=void 0;h=f&&f.debuglog?f.debuglog("stream"):function(){};var d,p=n(150),m=n(80);c.inherits(b,s);var g=["error","close","destroy","pause","resume"];function v(e,t){a=a||n(25),e=e||{};var r=t instanceof a;this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var i=e.highWaterMark,o=e.readableHighWaterMark,s=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:r&&(o||0===o)?o:s,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new p,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(d||(d=n(81).StringDecoder),this.decoder=new d(e.encoding),this.encoding=e.encoding)}function b(e){if(a=a||n(25),!(this instanceof b))return new b(e);this._readableState=new v(e,this),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),s.call(this)}function y(e,t,n,r,a){var i,o=e._readableState;null===t?(o.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,x(e)}(e,o)):(a||(i=function(e,t){var n;r=t,l.isBuffer(r)||r instanceof u||"string"==typeof t||void 0===t||e.objectMode||(n=new TypeError("Invalid non-string/buffer chunk"));var r;return n}(o,t)),i?e.emit("error",i):o.objectMode||t&&t.length>0?("string"==typeof t||o.objectMode||Object.getPrototypeOf(t)===l.prototype||(t=function(e){return l.from(e)}(t)),r?o.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):w(e,o,t,!0):o.ended?e.emit("error",new Error("stream.push() after EOF")):(o.reading=!1,o.decoder&&!n?(t=o.decoder.write(t),o.objectMode||0!==t.length?w(e,o,t,!1):k(e,o)):w(e,o,t,!1))):r||(o.reading=!1));return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(o)}function w(e,t,n,r){t.flowing&&0===t.length&&!t.sync?(e.emit("data",n),e.read(0)):(t.length+=t.objectMode?1:n.length,r?t.buffer.unshift(n):t.buffer.push(n),t.needReadable&&x(e)),k(e,t)}Object.defineProperty(b.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),b.prototype.destroy=m.destroy,b.prototype._undestroy=m.undestroy,b.prototype._destroy=function(e,t){this.push(null),t(e)},b.prototype.push=function(e,t){var n,r=this._readableState;return r.objectMode?n=!0:"string"==typeof e&&((t=t||r.defaultEncoding)!==r.encoding&&(e=l.from(e,t),t=""),n=!0),y(this,e,t,!1,n)},b.prototype.unshift=function(e){return y(this,e,null,!0,!1)},b.prototype.isPaused=function(){return!1===this._readableState.flowing},b.prototype.setEncoding=function(e){return d||(d=n(81).StringDecoder),this._readableState.decoder=new d(e),this._readableState.encoding=e,this};var E=8388608;function _(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=E?e=E:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function x(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(h("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?r.nextTick(F,e):F(e))}function F(e){h("emit readable"),e.emit("readable"),M(e)}function k(e,t){t.readingMore||(t.readingMore=!0,r.nextTick(S,e,t))}function S(e,t){for(var n=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(h("maybeReadMore read 0"),e.read(0),n!==t.length);)n=t.length;t.readingMore=!1}function C(e){h("readable nexttick read 0"),e.read(0)}function j(e,t){t.reading||(h("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),M(e),t.flowing&&!t.reading&&e.read(0)}function M(e){var t=e._readableState;for(h("flow",t.flowing);t.flowing&&null!==e.read(););}function A(e,t){return 0===t.length?null:(t.objectMode?n=t.buffer.shift():!e||e>=t.length?(n=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):n=function(e,t,n){var r;e<t.head.data.length?(r=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):r=e===t.head.data.length?t.shift():n?function(e,t){var n=t.head,r=1,a=n.data;e-=a.length;for(;n=n.next;){var i=n.data,o=e>i.length?i.length:e;if(o===i.length?a+=i:a+=i.slice(0,e),0===(e-=o)){o===i.length?(++r,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=i.slice(o));break}++r}return t.length-=r,a}(e,t):function(e,t){var n=l.allocUnsafe(e),r=t.head,a=1;r.data.copy(n),e-=r.data.length;for(;r=r.next;){var i=r.data,o=e>i.length?i.length:e;if(i.copy(n,n.length-e,0,o),0===(e-=o)){o===i.length?(++a,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=i.slice(o));break}++a}return t.length-=a,n}(e,t);return r}(e,t.buffer,t.decoder),n);var n}function O(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,r.nextTick(D,t,e))}function D(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function B(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}b.prototype.read=function(e){h("read",e),e=parseInt(e,10);var t=this._readableState,n=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return h("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?O(this):x(this),null;if(0===(e=_(e,t))&&t.ended)return 0===t.length&&O(this),null;var r,a=t.needReadable;return h("need readable",a),(0===t.length||t.length-e<t.highWaterMark)&&h("length less than watermark",a=!0),t.ended||t.reading?h("reading or ended",a=!1):a&&(h("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=_(n,t))),null===(r=e>0?A(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&O(this)),null!==r&&this.emit("data",r),r},b.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},b.prototype.pipe=function(e,t){var n=this,a=this._readableState;switch(a.pipesCount){case 0:a.pipes=e;break;case 1:a.pipes=[a.pipes,e];break;default:a.pipes.push(e)}a.pipesCount+=1,h("pipe count=%d opts=%j",a.pipesCount,t);var s=(!t||!1!==t.end)&&e!==process.stdout&&e!==process.stderr?u:b;function l(t,r){h("onunpipe"),t===n&&r&&!1===r.hasUnpiped&&(r.hasUnpiped=!0,h("cleanup"),e.removeListener("close",g),e.removeListener("finish",v),e.removeListener("drain",c),e.removeListener("error",m),e.removeListener("unpipe",l),n.removeListener("end",u),n.removeListener("end",b),n.removeListener("data",p),f=!0,!a.awaitDrain||e._writableState&&!e._writableState.needDrain||c())}function u(){h("onend"),e.end()}a.endEmitted?r.nextTick(s):n.once("end",s),e.on("unpipe",l);var c=function(e){return function(){var t=e._readableState;h("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&o(e,"data")&&(t.flowing=!0,M(e))}}(n);e.on("drain",c);var f=!1;var d=!1;function p(t){h("ondata"),d=!1,!1!==e.write(t)||d||((1===a.pipesCount&&a.pipes===e||a.pipesCount>1&&-1!==B(a.pipes,e))&&!f&&(h("false write response, pause",n._readableState.awaitDrain),n._readableState.awaitDrain++,d=!0),n.pause())}function m(t){h("onerror",t),b(),e.removeListener("error",m),0===o(e,"error")&&e.emit("error",t)}function g(){e.removeListener("finish",v),b()}function v(){h("onfinish"),e.removeListener("close",g),b()}function b(){h("unpipe"),n.unpipe(e)}return n.on("data",p),function(e,t,n){if("function"==typeof e.prependListener)return e.prependListener(t,n);e._events&&e._events[t]?i(e._events[t])?e._events[t].unshift(n):e._events[t]=[n,e._events[t]]:e.on(t,n)}(e,"error",m),e.once("close",g),e.once("finish",v),e.emit("pipe",n),a.flowing||(h("pipe resume"),n.resume()),e},b.prototype.unpipe=function(e){var t=this._readableState,n={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,n),this);if(!e){var r=t.pipes,a=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var i=0;i<a;i++)r[i].emit("unpipe",this,n);return this}var o=B(t.pipes,e);return-1===o?this:(t.pipes.splice(o,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,n),this)},b.prototype.on=function(e,t){var n=s.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var a=this._readableState;a.endEmitted||a.readableListening||(a.readableListening=a.needReadable=!0,a.emittedReadable=!1,a.reading?a.length&&x(this):r.nextTick(C,this))}return n},b.prototype.addListener=b.prototype.on,b.prototype.resume=function(){var e=this._readableState;return e.flowing||(h("resume"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,r.nextTick(j,e,t))}(this,e)),this},b.prototype.pause=function(){return h("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(h("pause"),this._readableState.flowing=!1,this.emit("pause")),this},b.prototype.wrap=function(e){var t=this,n=this._readableState,r=!1;for(var a in e.on("end",function(){if(h("wrapped end"),n.decoder&&!n.ended){var e=n.decoder.end();e&&e.length&&t.push(e)}t.push(null)}),e.on("data",function(a){(h("wrapped data"),n.decoder&&(a=n.decoder.write(a)),!n.objectMode||null!==a&&void 0!==a)&&((n.objectMode||a&&a.length)&&(t.push(a)||(r=!0,e.pause())))}),e)void 0===this[a]&&"function"==typeof e[a]&&(this[a]=function(t){return function(){return e[t].apply(e,arguments)}}(a));for(var i=0;i<g.length;i++)e.on(g[i],this.emit.bind(this,g[i]));return this._read=function(t){h("wrapped _read",t),r&&(r=!1,e.resume())},this},Object.defineProperty(b.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),b._fromList=A},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";var r=n(24).Buffer,a=n(1);e.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,n=""+t.data;t=t.next;)n+=e+t.data;return n},e.prototype.concat=function(e){if(0===this.length)return r.alloc(0);if(1===this.length)return this.head.data;for(var t,n,a,i=r.allocUnsafe(e>>>0),o=this.head,s=0;o;)t=o.data,n=i,a=s,t.copy(n,a),s+=o.data.length,o=o.next;return i},e}(),a&&a.inspect&&a.inspect.custom&&(e.exports.prototype[a.inspect.custom]=function(){var e=a.inspect({length:this.length});return this.constructor.name+" "+e})},function(e,t,n){"use strict";const r=n(16),a=n(11),i=n(152),o=n(181),{MESSAGE:s}=n(0),{Stream:l,PassThrough:u}=n(15),c=n(8),f=n(28)("winston:file"),h=n(6),d=n(202);e.exports=class extends c{constructor(e={}){function t(t,...n){n.slice(1).forEach(n=>{if(e[n])throw new Error(`Cannot set ${n} and ${t} together`)})}if(super(e),this.name=e.name||"file",this._stream=new u,this._stream.setMaxListeners(30),this._onError=this._onError.bind(this),e.filename||e.dirname)t("filename or dirname","stream"),this._basename=this.filename=e.filename?a.basename(e.filename):"winston.log",this.dirname=e.dirname||a.dirname(e.filename),this.options=e.options||{flags:"a"};else{if(!e.stream)throw new Error("Cannot log to file without filename or stream.");console.warn("options.stream will be removed in winston@4. Use winston.transports.Stream"),t("stream","filename","maxsize"),this._dest=this._stream.pipe(this._setupStream(e.stream)),this.dirname=a.dirname(this._dest.path)}this.maxsize=e.maxsize||null,this.rotationFormat=e.rotationFormat||!1,this.zippedArchive=e.zippedArchive||!1,this.maxFiles=e.maxFiles||null,this.eol=e.eol||h.EOL,this.tailable=e.tailable||!1,this._size=0,this._pendingSize=0,this._created=0,this._drain=!1,this._opening=!1,this._ending=!1,this.dirname&&this._createLogDirIfNotExist(this.dirname),this.open()}finishIfEnding(){this._ending&&(this._opening?this.once("open",()=>{this._stream.once("finish",()=>this.emit("finish")),setImmediate(()=>this._stream.end())}):(this._stream.once("finish",()=>this.emit("finish")),setImmediate(()=>this._stream.end())))}log(e,t=(()=>{})){if(this.silent)return t(),!0;if(this._drain)return void this._stream.once("drain",()=>{this._drain=!1,this.log(e,t)});if(this._rotate)return void this._stream.once("rotate",()=>{this._rotate=!1,this.log(e,t)});const n=`${e[s]}${this.eol}`,r=Buffer.byteLength(n);this._pendingSize+=r,this._opening&&!this.rotatedWhileOpening&&this._needsNewFile(this._size+this._pendingSize)&&(this.rotatedWhileOpening=!0);const a=this._stream.write(n,function(){this._size+=r,this._pendingSize-=r,f("logged %s %s",this._size,n),this.emit("logged",e),this._opening||this._needsNewFile()&&(this._rotate=!0,this._endStream(()=>this._rotateFile()))}.bind(this));return a?t():(this._drain=!0,this._stream.once("drain",()=>{this._drain=!1,t()})),f("written",a,this._drain),this.finishIfEnding(),a}query(e,t){"function"==typeof e&&(t=e,e={}),e=function(e){(e=e||{}).rows=e.rows||e.limit||10,e.start=e.start||0,e.until=e.until||new Date,"object"!=typeof e.until&&(e.until=new Date(e.until));e.from=e.from||e.until-864e5,"object"!=typeof e.from&&(e.from=new Date(e.from));return e.order=e.order||"desc",e.fields=e.fields,e}(e);const n=a.join(this.dirname,this.filename);let i="",o=[],s=0;const l=r.createReadStream(n,{encoding:"utf8"});function u(t,n){try{const r=JSON.parse(t);(function(t){if(!t)return;if("object"!=typeof t)return;const n=new Date(t.timestamp);if(e.from&&n<e.from||e.until&&n>e.until||e.level&&e.level!==t.level)return;return!0})(r)&&function(t){if(e.rows&&o.length>=e.rows&&"desc"!==e.order)return void(l.readable&&l.destroy());e.fields&&(t=e.fields.reduce((e,n)=>(e[n]=t[n],e),{}));"desc"===e.order&&o.length>=e.rows&&o.shift();o.push(t)}(r)}catch(e){n||l.emit("error",e)}}l.on("error",e=>{if(l.readable&&l.destroy(),t)return"ENOENT"!==e.code?t(e):t(null,o)}),l.on("data",t=>{const n=(t=(i+t).split(/\n+/)).length-1;let r=0;for(;r<n;r++)(!e.start||s>=e.start)&&u(t[r]),s++;i=t[n]}),l.on("close",()=>{i&&u(i,!0),"desc"===e.order&&(o=o.reverse()),t&&t(null,o)})}stream(e={}){const t=a.join(this.dirname,this.filename),n=new l,r={file:t,start:e.start};return n.destroy=d(r,(e,t)=>{if(e)return n.emit("error",e);try{n.emit("data",t),t=JSON.parse(t),n.emit("log",t)}catch(e){n.emit("error",e)}}),n}open(){this.filename&&(this._opening||(this._opening=!0,this.stat((e,t)=>{if(e)return this.emit("error",e);f("stat done: %s { size: %s }",this.filename,t),this._size=t,this._dest=this._createStream(this._stream),this._opening=!1,this.once("open",()=>{this._stream.eventNames().includes("rotate")?this._stream.emit("rotate"):this._rotate=!1})})))}stat(e){const t=this._getFile(),n=a.join(this.dirname,t);r.stat(n,(r,a)=>r&&"ENOENT"===r.code?(f("ENOENT ok",n),this.filename=t,e(null,0)):r?(f(`err ${r.code} ${n}`),e(r)):!a||this._needsNewFile(a.size)?this._incFile(()=>this.stat(e)):(this.filename=t,void e(null,a.size)))}close(e){this._stream&&this._stream.end(()=>{e&&e(),this.emit("flush"),this.emit("closed")})}_needsNewFile(e){return e=e||this._size,this.maxsize&&e>=this.maxsize}_onError(e){this.emit("error",e)}_setupStream(e){return e.on("error",this._onError),e}_cleanupStream(e){return e.removeListener("error",this._onError),e}_rotateFile(){this._incFile(()=>this.open())}_endStream(e=(()=>{})){this._dest?(this._stream.unpipe(this._dest),this._dest.end(()=>{this._cleanupStream(this._dest),e()})):e()}_createStream(e){const t=a.join(this.dirname,this.filename);f("create stream start",t,this.options);const n=r.createWriteStream(t,this.options).on("error",e=>f(e)).on("close",()=>f("close",n.path,n.bytesWritten)).on("open",()=>{f("file open ok",t),this.emit("open",t),e.pipe(n),this.rotatedWhileOpening&&(this._stream=new u,this._stream.setMaxListeners(30),this._rotateFile(),this.rotatedWhileOpening=!1,this._cleanupStream(n),e.end())});if(f("create stream ok",t),this.zippedArchive){const e=o.createGzip();return e.pipe(n),e}return n}_incFile(e){f("_incFile",this.filename);const t=a.extname(this._basename),n=a.basename(this._basename,t);this.tailable?this._checkMaxFilesTailable(t,n,e):(this._created+=1,this._checkMaxFilesIncrementing(t,n,e))}_getFile(){const e=a.extname(this._basename),t=a.basename(this._basename,e),n=this.rotationFormat?this.rotationFormat():this._created,r=!this.tailable&&this._created?`${t}${n}${e}`:`${t}${e}`;return this.zippedArchive&&!this.tailable?`${r}.gz`:r}_checkMaxFilesIncrementing(e,t,n){if(!this.maxFiles||this._created<this.maxFiles)return setImmediate(n);const i=this._created-this.maxFiles,o=`${t}${0!==i?i:""}${e}${this.zippedArchive?".gz":""}`,s=a.join(this.dirname,o);r.unlink(s,n)}_checkMaxFilesTailable(e,t,n){const o=[];if(!this.maxFiles)return;const s=this.zippedArchive?".gz":"";for(let n=this.maxFiles-1;n>1;n--)o.push(function(n,i){let o=`${t}${n-1}${e}${s}`;const l=a.join(this.dirname,o);r.exists(l,u=>{if(!u)return i(null);o=`${t}${n}${e}${s}`,r.rename(l,a.join(this.dirname,o),i)})}.bind(this,n));i(o,()=>{r.rename(a.join(this.dirname,`${t}${e}`),a.join(this.dirname,`${t}1${e}${s}`),n)})}_createLogDirIfNotExist(e){r.existsSync(e)||r.mkdirSync(e,{recursive:!0})}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){(0,r.default)(a.default,e,t)};var r=i(n(153)),a=i(n(160));function i(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){n=n||r.default;var s=(0,a.default)(t)?[]:{};e(t,function(e,t,n){(0,o.default)(e)(function(e,r){arguments.length>2&&(r=(0,i.default)(arguments,1)),s[t]=r,n(e)})},function(e){n(e,s)})};var r=s(n(47)),a=s(n(26)),i=s(n(49)),o=s(n(27));function s(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},function(e,t,n){var r=n(48),a=n(85),i="[object AsyncFunction]",o="[object Function]",s="[object GeneratorFunction]",l="[object Proxy]";e.exports=function(e){if(!a(e))return!1;var t=r(e);return t==o||t==s||t==i||t==l}},function(e,t,n){var r=n(82),a=Object.prototype,i=a.hasOwnProperty,o=a.toString,s=r?r.toStringTag:void 0;e.exports=function(e){var t=i.call(e,s),n=e[s];try{e[s]=void 0;var r=!0}catch(e){}var a=o.call(e);return r&&(t?e[s]=n:delete e[s]),a}},function(e,t){var n=Object.prototype.toString;e.exports=function(e){return n.call(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return(0,a.default)(function(t,n){var a;try{a=e.apply(this,t)}catch(e){return n(e)}(0,r.default)(a)&&"function"==typeof a.then?a.then(function(e){s(n,null,e)},function(e){s(n,e.message?e:new Error(e))}):n(null,a)})};var r=o(n(85)),a=o(n(158)),i=o(n(159));function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t,n){try{e(t,n)}catch(e){(0,i.default)(l,e)}}function l(e){throw e}e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){var t=(0,i.default)(arguments),n=t.pop();e.call(this,t,n)}};var r,a=n(49),i=(r=a)&&r.__esModule?r:{default:r};e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hasNextTick=t.hasSetImmediate=void 0,t.fallback=u,t.wrap=c;var r,a=n(49),i=(r=a)&&r.__esModule?r:{default:r};var o,s=t.hasSetImmediate="function"==typeof setImmediate&&setImmediate,l=t.hasNextTick="object"==typeof process&&"function"==typeof process.nextTick;function u(e){setTimeout(e,0)}function c(e){return function(t){var n=(0,i.default)(arguments,1);e(function(){t.apply(null,n)})}}o=s?setImmediate:l?process.nextTick:u,t.default=c(o)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=i(n(87)),a=i(n(92));function i(e){return e&&e.__esModule?e:{default:e}}t.default=(0,a.default)(r.default,1),e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t,n,l){if(l=(0,a.default)(l||r.default),e<=0||!t)return l(null);var u=(0,i.default)(t),c=!1,f=0,h=!1;function d(e,t){if(f-=1,e)c=!0,l(e);else{if(t===s.default||c&&f<=0)return c=!0,l(null);h||p()}}function p(){for(h=!0;f<e&&!c;){var t=u();if(null===t)return c=!0,void(f<=0&&l(null));f+=1,n(t.value,t.key,(0,o.default)(d))}h=!1}p()}};var r=l(n(47)),a=l(n(88)),i=l(n(162)),o=l(n(90)),s=l(n(91));function l(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if((0,r.default)(e))return function(e){var t=-1,n=e.length;return function(){return++t<n?{value:e[t],key:t}:null}}(e);var t=(0,a.default)(e);return t?function(e){var t=-1;return function(){var n=e.next();return n.done?null:(t++,{value:n.value,key:t})}}(t):(n=e,o=(0,i.default)(n),s=-1,l=o.length,function(){var e=o[++s];return s<l?{value:n[e],key:e}:null});var n,o,s,l};var r=o(n(26)),a=o(n(163)),i=o(n(164));function o(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return r&&e[r]&&e[r]()};var r="function"==typeof Symbol&&Symbol.iterator;e.exports=t.default},function(e,t,n){var r=n(165),a=n(177),i=n(26);e.exports=function(e){return i(e)?r(e):a(e)}},function(e,t,n){var r=n(166),a=n(167),i=n(169),o=n(170),s=n(172),l=n(173),u=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=i(e),c=!n&&a(e),f=!n&&!c&&o(e),h=!n&&!c&&!f&&l(e),d=n||c||f||h,p=d?r(e.length,String):[],m=p.length;for(var g in e)!t&&!u.call(e,g)||d&&("length"==g||f&&("offset"==g||"parent"==g)||h&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||s(g,m))||p.push(g);return p}},function(e,t){e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},function(e,t,n){var r=n(168),a=n(50),i=Object.prototype,o=i.hasOwnProperty,s=i.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(e){return a(e)&&o.call(e,"callee")&&!s.call(e,"callee")};e.exports=l},function(e,t,n){var r=n(48),a=n(50),i="[object Arguments]";e.exports=function(e){return a(e)&&r(e)==i}},function(e,t){var n=Array.isArray;e.exports=n},function(e,t,n){(function(e){var r=n(83),a=n(171),i=t&&!t.nodeType&&t,o=i&&"object"==typeof e&&e&&!e.nodeType&&e,s=o&&o.exports===i?r.Buffer:void 0,l=(s?s.isBuffer:void 0)||a;e.exports=l}).call(this,n(89)(e))},function(e,t){e.exports=function(){return!1}},function(e,t){var n=9007199254740991,r=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var a=typeof e;return!!(t=null==t?n:t)&&("number"==a||"symbol"!=a&&r.test(e))&&e>-1&&e%1==0&&e<t}},function(e,t,n){var r=n(174),a=n(175),i=n(176),o=i&&i.isTypedArray,s=o?a(o):r;e.exports=s},function(e,t,n){var r=n(48),a=n(86),i=n(50),o={};o["[object Float32Array]"]=o["[object Float64Array]"]=o["[object Int8Array]"]=o["[object Int16Array]"]=o["[object Int32Array]"]=o["[object Uint8Array]"]=o["[object Uint8ClampedArray]"]=o["[object Uint16Array]"]=o["[object Uint32Array]"]=!0,o["[object Arguments]"]=o["[object Array]"]=o["[object ArrayBuffer]"]=o["[object Boolean]"]=o["[object DataView]"]=o["[object Date]"]=o["[object Error]"]=o["[object Function]"]=o["[object Map]"]=o["[object Number]"]=o["[object Object]"]=o["[object RegExp]"]=o["[object Set]"]=o["[object String]"]=o["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&a(e.length)&&!!o[r(e)]}},function(e,t){e.exports=function(e){return function(t){return e(t)}}},function(e,t,n){(function(e){var r=n(84),a=t&&!t.nodeType&&t,i=a&&"object"==typeof e&&e&&!e.nodeType&&e,o=i&&i.exports===a&&r.process,s=function(){try{var e=i&&i.require&&i.require("util").types;return e||o&&o.binding&&o.binding("util")}catch(e){}}();e.exports=s}).call(this,n(89)(e))},function(e,t,n){var r=n(178),a=n(179),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return a(e);var t=[];for(var n in Object(e))i.call(e,n)&&"constructor"!=n&&t.push(n);return t}},function(e,t){var n=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||n)}},function(e,t,n){var r=n(180)(Object.keys,Object);e.exports=r},function(e,t){e.exports=function(e,t){return function(n){return e(t(n))}}},function(e,t){e.exports=require("zlib")},function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var a=n(14).Buffer,i=n(1).inspect,o=i&&i.custom||"inspect";e.exports=function(){function e(){this.head=null,this.tail=null,this.length=0}var t=e.prototype;return t.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},t.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},t.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},t.clear=function(){this.head=this.tail=null,this.length=0},t.join=function(e){if(0===this.length)return"";for(var t=this.head,n=""+t.data;t=t.next;)n+=e+t.data;return n},t.concat=function(e){if(0===this.length)return a.alloc(0);for(var t,n,r,i=a.allocUnsafe(e>>>0),o=this.head,s=0;o;)t=o.data,n=i,r=s,a.prototype.copy.call(t,n,r),s+=o.data.length,o=o.next;return i},t.consume=function(e,t){var n;return e<this.head.data.length?(n=this.head.data.slice(0,e),this.head.data=this.head.data.slice(e)):n=e===this.head.data.length?this.shift():t?this._getString(e):this._getBuffer(e),n},t.first=function(){return this.head.data},t._getString=function(e){var t=this.head,n=1,r=t.data;for(e-=r.length;t=t.next;){var a=t.data,i=e>a.length?a.length:e;if(i===a.length?r+=a:r+=a.slice(0,e),0===(e-=i)){i===a.length?(++n,t.next?this.head=t.next:this.head=this.tail=null):(this.head=t,t.data=a.slice(i));break}++n}return this.length-=n,r},t._getBuffer=function(e){var t=a.allocUnsafe(e),n=this.head,r=1;for(n.data.copy(t),e-=n.data.length;n=n.next;){var i=n.data,o=e>i.length?i.length:e;if(i.copy(t,t.length-e,0,o),0===(e-=o)){o===i.length?(++r,n.next?this.head=n.next:this.head=this.tail=null):(this.head=n,n.data=i.slice(o));break}++r}return this.length-=r,t},t[o]=function(e,t){return i(this,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){r(e,t,n[t])})}return e}({},t,{depth:0,customInspect:!1}))},e}()},function(e,t,n){"use strict";var r=new Set;e.exports.emitExperimentalWarning=process.emitWarning?function(e){if(!r.has(e)){var t=e+" is an experimental feature. This feature could change at any time";r.add(e),process.emitWarning(t,"ExperimentalWarning")}}:function(){}},function(e,t,n){var r=n(14),a=r.Buffer;function i(e,t){for(var n in e)t[n]=e[n]}function o(e,t,n){return a(e,t,n)}a.from&&a.alloc&&a.allocUnsafe&&a.allocUnsafeSlow?e.exports=r:(i(r,t),t.Buffer=o),i(a,o),o.from=function(e,t,n){if("number"==typeof e)throw new TypeError("Argument must not be a number");return a(e,t,n)},o.alloc=function(e,t,n){if("number"!=typeof e)throw new TypeError("Argument must be a number");var r=a(e);return void 0!==t?"string"==typeof n?r.fill(t,n):r.fill(t):r.fill(0),r},o.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return a(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return r.SlowBuffer(e)}},function(e,t,n){"use strict";var r;function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var i=n(51),o=Symbol("lastResolve"),s=Symbol("lastReject"),l=Symbol("error"),u=Symbol("ended"),c=Symbol("lastPromise"),f=Symbol("handlePromise"),h=Symbol("stream");function d(e,t){return{value:e,done:t}}function p(e){var t=e[o];if(null!==t){var n=e[h].read();null!==n&&(e[c]=null,e[o]=null,e[s]=null,t(d(n,!1)))}}var m=Object.getPrototypeOf(function(){}),g=Object.setPrototypeOf((a(r={get stream(){return this[h]},next:function(){var e=this,t=this[l];if(null!==t)return Promise.reject(t);if(this[u])return Promise.resolve(d(void 0,!0));if(this[h].destroyed)return new Promise(function(t,n){process.nextTick(function(){e[l]?n(e[l]):t(d(void 0,!0))})});var n,r=this[c];if(r)n=new Promise(function(e,t){return function(n,r){e.then(function(){t[u]?n(d(void 0,!0)):t[f](n,r)},r)}}(r,this));else{var a=this[h].read();if(null!==a)return Promise.resolve(d(a,!1));n=new Promise(this[f])}return this[c]=n,n}},Symbol.asyncIterator,function(){return this}),a(r,"return",function(){var e=this;return new Promise(function(t,n){e[h].destroy(null,function(e){e?n(e):t(d(void 0,!0))})})}),r),m);e.exports=function(e){var t,n=Object.create(g,(a(t={},h,{value:e,writable:!0}),a(t,o,{value:null,writable:!0}),a(t,s,{value:null,writable:!0}),a(t,l,{value:null,writable:!0}),a(t,u,{value:e._readableState.endEmitted,writable:!0}),a(t,f,{value:function(e,t){var r=n[h].read();r?(n[c]=null,n[o]=null,n[s]=null,e(d(r,!1))):(n[o]=e,n[s]=t)},writable:!0}),t));return n[c]=null,i(e,function(e){if(e&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code){var t=n[s];return null!==t&&(n[c]=null,n[o]=null,n[s]=null,t(e)),void(n[l]=e)}var r=n[o];null!==r&&(n[c]=null,n[o]=null,n[s]=null,r(d(void 0,!0))),n[u]=!0}),e.on("readable",function(e){process.nextTick(p,e)}.bind(null,n)),n}},function(e,t,n){"use strict";e.exports=a;var r=n(99);function a(e){if(!(this instanceof a))return new a(e);r.call(this,e)}n(5)(a,r),a.prototype._transform=function(e,t,n){n(null,e)}},function(e,t,n){"use strict";var r;var a=n(9).codes,i=a.ERR_MISSING_ARGS,o=a.ERR_STREAM_DESTROYED;function s(e){if(e)throw e}function l(e){e()}function u(e,t){return e.pipe(t)}e.exports=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var c,f=function(e){return e.length?"function"!=typeof e[e.length-1]?s:e.pop():s}(t);if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new i("streams");var h=t.map(function(e,a){var i=a<t.length-1;return function(e,t,a,i){i=function(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}(i);var s=!1;e.on("close",function(){s=!0}),void 0===r&&(r=n(51)),r(e,{readable:t,writable:a},function(e){if(e)return i(e);s=!0,i()});var l=!1;return function(t){if(!s&&!l)return l=!0,function(e){return e.setHeader&&"function"==typeof e.abort}(e)?e.abort():"function"==typeof e.destroy?e.destroy():void i(t||new o("pipe"))}}(e,i,a>0,function(e){c||(c=e),e&&h.forEach(l),i||(h.forEach(l),f(c))})});return t.reduce(u)}},function(e,t,n){"use strict";var r=n(189),a=n(195);e.exports=function(e,t){var n=e.split(t||":"),i=a(n[0]);if(!n.length)return i;for(var o=0,s=n.length-1;o<s;o++)i=r(i).mix(r(a(n[o+1]))).saturate(1).hex();return i}},function(e,t,n){"use strict";var r=n(190),a=n(193),i=[].slice,o=["keyword","gray","hex"],s={};Object.keys(a).forEach(function(e){s[i.call(a[e].labels).sort().join("")]=e});var l={};function u(e,t){if(!(this instanceof u))return new u(e,t);if(t&&t in o&&(t=null),t&&!(t in a))throw new Error("Unknown model: "+t);var n,c;if(e)if(e instanceof u)this.model=e.model,this.color=e.color.slice(),this.valpha=e.valpha;else if("string"==typeof e){var f=r.get(e);if(null===f)throw new Error("Unable to parse color from string: "+e);this.model=f.model,c=a[this.model].channels,this.color=f.value.slice(0,c),this.valpha="number"==typeof f.value[c]?f.value[c]:1}else if(e.length){this.model=t||"rgb",c=a[this.model].channels;var d=i.call(e,0,c);this.color=h(d,c),this.valpha="number"==typeof e[c]?e[c]:1}else if("number"==typeof e)e&=16777215,this.model="rgb",this.color=[e>>16&255,e>>8&255,255&e],this.valpha=1;else{this.valpha=1;var p=Object.keys(e);"alpha"in e&&(p.splice(p.indexOf("alpha"),1),this.valpha="number"==typeof e.alpha?e.alpha:0);var m=p.sort().join("");if(!(m in s))throw new Error("Unable to parse color from object: "+JSON.stringify(e));this.model=s[m];var g=a[this.model].labels,v=[];for(n=0;n<g.length;n++)v.push(e[g[n]]);this.color=h(v)}else this.model="rgb",this.color=[0,0,0],this.valpha=1;if(l[this.model])for(c=a[this.model].channels,n=0;n<c;n++){var b=l[this.model][n];b&&(this.color[n]=b(this.color[n]))}this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze&&Object.freeze(this)}function c(e,t,n){return(e=Array.isArray(e)?e:[e]).forEach(function(e){(l[e]||(l[e]=[]))[t]=n}),e=e[0],function(r){var a;return arguments.length?(n&&(r=n(r)),(a=this[e]()).color[t]=r,a):(a=this[e]().color[t],n&&(a=n(a)),a)}}function f(e){return function(t){return Math.max(0,Math.min(e,t))}}function h(e,t){for(var n=0;n<t;n++)"number"!=typeof e[n]&&(e[n]=0);return e}u.prototype={toString:function(){return this.string()},toJSON:function(){return this[this.model]()},string:function(e){var t=this.model in r.to?this:this.rgb(),n=1===(t=t.round("number"==typeof e?e:1)).valpha?t.color:t.color.concat(this.valpha);return r.to[t.model](n)},percentString:function(e){var t=this.rgb().round("number"==typeof e?e:1),n=1===t.valpha?t.color:t.color.concat(this.valpha);return r.to.rgb.percent(n)},array:function(){return 1===this.valpha?this.color.slice():this.color.concat(this.valpha)},object:function(){for(var e={},t=a[this.model].channels,n=a[this.model].labels,r=0;r<t;r++)e[n[r]]=this.color[r];return 1!==this.valpha&&(e.alpha=this.valpha),e},unitArray:function(){var e=this.rgb().color;return e[0]/=255,e[1]/=255,e[2]/=255,1!==this.valpha&&e.push(this.valpha),e},unitObject:function(){var e=this.rgb().object();return e.r/=255,e.g/=255,e.b/=255,1!==this.valpha&&(e.alpha=this.valpha),e},round:function(e){return e=Math.max(e||0,0),new u(this.color.map(function(e){return function(t){return function(e,t){return Number(e.toFixed(t))}(t,e)}}(e)).concat(this.valpha),this.model)},alpha:function(e){return arguments.length?new u(this.color.concat(Math.max(0,Math.min(1,e))),this.model):this.valpha},red:c("rgb",0,f(255)),green:c("rgb",1,f(255)),blue:c("rgb",2,f(255)),hue:c(["hsl","hsv","hsl","hwb","hcg"],0,function(e){return(e%360+360)%360}),saturationl:c("hsl",1,f(100)),lightness:c("hsl",2,f(100)),saturationv:c("hsv",1,f(100)),value:c("hsv",2,f(100)),chroma:c("hcg",1,f(100)),gray:c("hcg",2,f(100)),white:c("hwb",1,f(100)),wblack:c("hwb",2,f(100)),cyan:c("cmyk",0,f(100)),magenta:c("cmyk",1,f(100)),yellow:c("cmyk",2,f(100)),black:c("cmyk",3,f(100)),x:c("xyz",0,f(100)),y:c("xyz",1,f(100)),z:c("xyz",2,f(100)),l:c("lab",0,f(100)),a:c("lab",1),b:c("lab",2),keyword:function(e){return arguments.length?new u(e):a[this.model].keyword(this.color)},hex:function(e){return arguments.length?new u(e):r.to.hex(this.rgb().round().color)},rgbNumber:function(){var e=this.rgb().color;return(255&e[0])<<16|(255&e[1])<<8|255&e[2]},luminosity:function(){for(var e=this.rgb().color,t=[],n=0;n<e.length;n++){var r=e[n]/255;t[n]=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4)}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast:function(e){var t=this.luminosity(),n=e.luminosity();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)},level:function(e){var t=this.contrast(e);return t>=7.1?"AAA":t>=4.5?"AA":""},isDark:function(){var e=this.rgb().color;return(299*e[0]+587*e[1]+114*e[2])/1e3<128},isLight:function(){return!this.isDark()},negate:function(){for(var e=this.rgb(),t=0;t<3;t++)e.color[t]=255-e.color[t];return e},lighten:function(e){var t=this.hsl();return t.color[2]+=t.color[2]*e,t},darken:function(e){var t=this.hsl();return t.color[2]-=t.color[2]*e,t},saturate:function(e){var t=this.hsl();return t.color[1]+=t.color[1]*e,t},desaturate:function(e){var t=this.hsl();return t.color[1]-=t.color[1]*e,t},whiten:function(e){var t=this.hwb();return t.color[1]+=t.color[1]*e,t},blacken:function(e){var t=this.hwb();return t.color[2]+=t.color[2]*e,t},grayscale:function(){var e=this.rgb().color,t=.3*e[0]+.59*e[1]+.11*e[2];return u.rgb(t,t,t)},fade:function(e){return this.alpha(this.valpha-this.valpha*e)},opaquer:function(e){return this.alpha(this.valpha+this.valpha*e)},rotate:function(e){var t=this.hsl(),n=t.color[0];return n=(n=(n+e)%360)<0?360+n:n,t.color[0]=n,t},mix:function(e,t){var n=e.rgb(),r=this.rgb(),a=void 0===t?.5:t,i=2*a-1,o=n.alpha()-r.alpha(),s=((i*o==-1?i:(i+o)/(1+i*o))+1)/2,l=1-s;return u.rgb(s*n.red()+l*r.red(),s*n.green()+l*r.green(),s*n.blue()+l*r.blue(),n.alpha()*a+r.alpha()*(1-a))}},Object.keys(a).forEach(function(e){if(-1===o.indexOf(e)){var t=a[e].channels;u.prototype[e]=function(){if(this.model===e)return new u(this);if(arguments.length)return new u(arguments,e);var n,r="number"==typeof arguments[t]?t:this.valpha;return new u((n=a[this.model][e].raw(this.color),Array.isArray(n)?n:[n]).concat(r),e)},u[e]=function(n){return"number"==typeof n&&(n=h(i.call(arguments),t)),new u(n,e)}}}),e.exports=u},function(e,t,n){var r=n(100),a=n(191),i={};for(var o in r)r.hasOwnProperty(o)&&(i[r[o]]=o);var s=e.exports={to:{},get:{}};function l(e,t,n){return Math.min(Math.max(t,e),n)}function u(e){var t=e.toString(16).toUpperCase();return t.length<2?"0"+t:t}s.get=function(e){var t,n;switch(e.substring(0,3).toLowerCase()){case"hsl":t=s.get.hsl(e),n="hsl";break;case"hwb":t=s.get.hwb(e),n="hwb";break;default:t=s.get.rgb(e),n="rgb"}return t?{model:n,value:t}:null},s.get.rgb=function(e){if(!e)return null;var t,n,a,i=[0,0,0,1];if(t=e.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)){for(a=t[2],t=t[1],n=0;n<3;n++){var o=2*n;i[n]=parseInt(t.slice(o,o+2),16)}a&&(i[3]=Math.round(parseInt(a,16)/255*100)/100)}else if(t=e.match(/^#([a-f0-9]{3,4})$/i)){for(a=(t=t[1])[3],n=0;n<3;n++)i[n]=parseInt(t[n]+t[n],16);a&&(i[3]=Math.round(parseInt(a+a,16)/255*100)/100)}else if(t=e.match(/^rgba?\(\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)){for(n=0;n<3;n++)i[n]=parseInt(t[n+1],0);t[4]&&(i[3]=parseFloat(t[4]))}else{if(!(t=e.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)))return(t=e.match(/(\D+)/))?"transparent"===t[1]?[0,0,0,0]:(i=r[t[1]])?(i[3]=1,i):null:null;for(n=0;n<3;n++)i[n]=Math.round(2.55*parseFloat(t[n+1]));t[4]&&(i[3]=parseFloat(t[4]))}for(n=0;n<3;n++)i[n]=l(i[n],0,255);return i[3]=l(i[3],0,1),i},s.get.hsl=function(e){if(!e)return null;var t=e.match(/^hsla?\(\s*([+-]?(?:\d*\.)?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/);if(t){var n=parseFloat(t[4]);return[(parseFloat(t[1])+360)%360,l(parseFloat(t[2]),0,100),l(parseFloat(t[3]),0,100),l(isNaN(n)?1:n,0,1)]}return null},s.get.hwb=function(e){if(!e)return null;var t=e.match(/^hwb\(\s*([+-]?\d*[\.]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/);if(t){var n=parseFloat(t[4]);return[(parseFloat(t[1])%360+360)%360,l(parseFloat(t[2]),0,100),l(parseFloat(t[3]),0,100),l(isNaN(n)?1:n,0,1)]}return null},s.to.hex=function(){var e=a(arguments);return"#"+u(e[0])+u(e[1])+u(e[2])+(e[3]<1?u(Math.round(255*e[3])):"")},s.to.rgb=function(){var e=a(arguments);return e.length<4||1===e[3]?"rgb("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+")":"rgba("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+", "+e[3]+")"},s.to.rgb.percent=function(){var e=a(arguments),t=Math.round(e[0]/255*100),n=Math.round(e[1]/255*100),r=Math.round(e[2]/255*100);return e.length<4||1===e[3]?"rgb("+t+"%, "+n+"%, "+r+"%)":"rgba("+t+"%, "+n+"%, "+r+"%, "+e[3]+")"},s.to.hsl=function(){var e=a(arguments);return e.length<4||1===e[3]?"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)":"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+e[3]+")"},s.to.hwb=function(){var e=a(arguments),t="";return e.length>=4&&1!==e[3]&&(t=", "+e[3]),"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+t+")"},s.to.keyword=function(e){return i[e.slice(0,3)]}},function(e,t,n){"use strict";var r=n(192),a=Array.prototype.concat,i=Array.prototype.slice,o=e.exports=function(e){for(var t=[],n=0,o=e.length;n<o;n++){var s=e[n];r(s)?t=a.call(t,i.call(s)):t.push(s)}return t};o.wrap=function(e){return function(){return e(o(arguments))}}},function(e,t){e.exports=function(e){return!(!e||"string"==typeof e)&&(e instanceof Array||Array.isArray(e)||e.length>=0&&(e.splice instanceof Function||Object.getOwnPropertyDescriptor(e,e.length-1)&&"String"!==e.constructor.name))}},function(e,t,n){var r=n(101),a=n(194),i={};Object.keys(r).forEach(function(e){i[e]={},Object.defineProperty(i[e],"channels",{value:r[e].channels}),Object.defineProperty(i[e],"labels",{value:r[e].labels});var t=a(e);Object.keys(t).forEach(function(n){var r=t[n];i[e][n]=function(e){var t=function(t){if(void 0===t||null===t)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var n=e(t);if("object"==typeof n)for(var r=n.length,a=0;a<r;a++)n[a]=Math.round(n[a]);return n};return"conversion"in e&&(t.conversion=e.conversion),t}(r),i[e][n].raw=function(e){var t=function(t){return void 0===t||null===t?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(r)})}),e.exports=i},function(e,t,n){var r=n(101);function a(e){var t=function(){for(var e={},t=Object.keys(r),n=t.length,a=0;a<n;a++)e[t[a]]={distance:-1,parent:null};return e}(),n=[e];for(t[e].distance=0;n.length;)for(var a=n.pop(),i=Object.keys(r[a]),o=i.length,s=0;s<o;s++){var l=i[s],u=t[l];-1===u.distance&&(u.distance=t[a].distance+1,u.parent=a,n.unshift(l))}return t}function i(e,t){return function(n){return t(e(n))}}function o(e,t){for(var n=[t[e].parent,e],a=r[t[e].parent][e],o=t[e].parent;t[o].parent;)n.unshift(t[o].parent),a=i(r[t[o].parent][o],a),o=t[o].parent;return a.conversion=n,a}e.exports=function(e){for(var t=a(e),n={},r=Object.keys(t),i=r.length,s=0;s<i;s++){var l=r[s];null!==t[l].parent&&(n[l]=o(l,t))}return n}},function(e,t,n){"use strict";e.exports=function(e){for(var t=0,n=0;t<e.length;n=e.charCodeAt(t++)+((n<<5)-n));var r=Math.floor(Math.abs(1e4*Math.sin(n)%1*16777216)).toString(16);return"#"+Array(6-r.length+1).join("0")+r}},function(e,t,n){"use strict";var r=n(197);e.exports=function(e,t){var n,a=r(),i=0;for(t=t||["diagnostics","debug"];i<t.length&&!(n=a[t[i]]);i++);if(!n)return!1;for(t=n.split(/[\s,]+/),i=0;i<t.length;i++)if("-"!==(n=t[i].replace("*",".*?")).charAt(0)){if(new RegExp("^"+n+"$").test(e))return!0}else if(new RegExp("^"+n.substr(1)+"$").test(e))return!1;return!1}},function(e,t,n){"use strict";var r=Object.prototype.hasOwnProperty;function a(e){if(e=e||{},"object"==typeof process&&a.merge(e,Object({PROCESS_NAME:"main"})),"undefined"!=typeof window){if("string"===window.name&&window.name.length&&a.merge(e,a.parse(window.name)),window.localStorage)try{a.merge(e,a.parse(window.localStorage.env||window.localStorage.debug))}catch(e){}"object"==typeof window.location&&"string"==typeof window.location.hash&&window.location.hash.length&&a.merge(e,a.parse("#"===window.location.hash.charAt(0)?window.location.hash.slice(1):window.location.hash))}var t,n;for(t in e)(n=t.toLowerCase())in e||(e[n]=e[t]);return e}a.merge=function(e,t){for(var n in t)r.call(t,n)&&(e[n]=t[n]);return e},a.parse=function(e){var t,n=/([^=?&]+)=([^&]*)/g,r={};if(!e)return r;for(;t=n.exec(e);r[decodeURIComponent(t[1])]=decodeURIComponent(t[2]));return r.env||r},e.exports=a},function(e,t,n){"use strict";var r=n(199);function a(e,t){return t?new a(e).style(t):this instanceof a?void(this.text=e):new a(e)}a.prototype.prefix="[",a.prototype.suffix="m",a.prototype.hex=function(e){3===(e="#"===e[0]?e.substring(1):e).length&&((e=e.split(""))[5]=e[2],e[4]=e[2],e[3]=e[1],e[2]=e[1],e[1]=e[0],e=e.join(""));var t=e.substring(0,2),n=e.substring(2,4),r=e.substring(4,6);return[parseInt(t,16),parseInt(n,16),parseInt(r,16)]},a.prototype.rgb=function(e,t,n){var r=e/255*5,a=t/255*5,i=n/255*5;return this.ansi(r,a,i)},a.prototype.ansi=function(e,t,n){return 16+36*Math.round(e)+6*Math.round(t)+Math.round(n)},a.prototype.reset=function(){return this.prefix+"39;49"+this.suffix},a.prototype.style=function(e){return/^#?(?:[0-9a-fA-F]{3}){1,2}$/.test(e)||(e=r(e)),this.prefix+"38;5;"+this.rgb.apply(this,this.hex(e))+this.suffix+this.text+this.reset()},e.exports=a},function(e,t,n){var r=n(200),a=r.filter(function(e){return!!e.css}),i=r.filter(function(e){return!!e.vga});e.exports=function(t){var n=e.exports.get(t);return n&&n.value},e.exports.get=function(e){return e=(e=e||"").trim().toLowerCase(),r.filter(function(t){return t.name.toLowerCase()===e}).pop()},e.exports.all=e.exports.get.all=function(){return r},e.exports.get.css=function(e){return e?(e=(e=e||"").trim().toLowerCase(),a.filter(function(t){return t.name.toLowerCase()===e}).pop()):a},e.exports.get.vga=function(e){return e?(e=(e=e||"").trim().toLowerCase(),i.filter(function(t){return t.name.toLowerCase()===e}).pop()):i}},function(e,t){e.exports=[{value:"#B0171F",name:"indian red"},{value:"#DC143C",css:!0,name:"crimson"},{value:"#FFB6C1",css:!0,name:"lightpink"},{value:"#FFAEB9",name:"lightpink 1"},{value:"#EEA2AD",name:"lightpink 2"},{value:"#CD8C95",name:"lightpink 3"},{value:"#8B5F65",name:"lightpink 4"},{value:"#FFC0CB",css:!0,name:"pink"},{value:"#FFB5C5",name:"pink 1"},{value:"#EEA9B8",name:"pink 2"},{value:"#CD919E",name:"pink 3"},{value:"#8B636C",name:"pink 4"},{value:"#DB7093",css:!0,name:"palevioletred"},{value:"#FF82AB",name:"palevioletred 1"},{value:"#EE799F",name:"palevioletred 2"},{value:"#CD6889",name:"palevioletred 3"},{value:"#8B475D",name:"palevioletred 4"},{value:"#FFF0F5",name:"lavenderblush 1"},{value:"#FFF0F5",css:!0,name:"lavenderblush"},{value:"#EEE0E5",name:"lavenderblush 2"},{value:"#CDC1C5",name:"lavenderblush 3"},{value:"#8B8386",name:"lavenderblush 4"},{value:"#FF3E96",name:"violetred 1"},{value:"#EE3A8C",name:"violetred 2"},{value:"#CD3278",name:"violetred 3"},{value:"#8B2252",name:"violetred 4"},{value:"#FF69B4",css:!0,name:"hotpink"},{value:"#FF6EB4",name:"hotpink 1"},{value:"#EE6AA7",name:"hotpink 2"},{value:"#CD6090",name:"hotpink 3"},{value:"#8B3A62",name:"hotpink 4"},{value:"#872657",name:"raspberry"},{value:"#FF1493",name:"deeppink 1"},{value:"#FF1493",css:!0,name:"deeppink"},{value:"#EE1289",name:"deeppink 2"},{value:"#CD1076",name:"deeppink 3"},{value:"#8B0A50",name:"deeppink 4"},{value:"#FF34B3",name:"maroon 1"},{value:"#EE30A7",name:"maroon 2"},{value:"#CD2990",name:"maroon 3"},{value:"#8B1C62",name:"maroon 4"},{value:"#C71585",css:!0,name:"mediumvioletred"},{value:"#D02090",name:"violetred"},{value:"#DA70D6",css:!0,name:"orchid"},{value:"#FF83FA",name:"orchid 1"},{value:"#EE7AE9",name:"orchid 2"},{value:"#CD69C9",name:"orchid 3"},{value:"#8B4789",name:"orchid 4"},{value:"#D8BFD8",css:!0,name:"thistle"},{value:"#FFE1FF",name:"thistle 1"},{value:"#EED2EE",name:"thistle 2"},{value:"#CDB5CD",name:"thistle 3"},{value:"#8B7B8B",name:"thistle 4"},{value:"#FFBBFF",name:"plum 1"},{value:"#EEAEEE",name:"plum 2"},{value:"#CD96CD",name:"plum 3"},{value:"#8B668B",name:"plum 4"},{value:"#DDA0DD",css:!0,name:"plum"},{value:"#EE82EE",css:!0,name:"violet"},{value:"#FF00FF",vga:!0,name:"magenta"},{value:"#FF00FF",vga:!0,css:!0,name:"fuchsia"},{value:"#EE00EE",name:"magenta 2"},{value:"#CD00CD",name:"magenta 3"},{value:"#8B008B",name:"magenta 4"},{value:"#8B008B",css:!0,name:"darkmagenta"},{value:"#800080",vga:!0,css:!0,name:"purple"},{value:"#BA55D3",css:!0,name:"mediumorchid"},{value:"#E066FF",name:"mediumorchid 1"},{value:"#D15FEE",name:"mediumorchid 2"},{value:"#B452CD",name:"mediumorchid 3"},{value:"#7A378B",name:"mediumorchid 4"},{value:"#9400D3",css:!0,name:"darkviolet"},{value:"#9932CC",css:!0,name:"darkorchid"},{value:"#BF3EFF",name:"darkorchid 1"},{value:"#B23AEE",name:"darkorchid 2"},{value:"#9A32CD",name:"darkorchid 3"},{value:"#68228B",name:"darkorchid 4"},{value:"#4B0082",css:!0,name:"indigo"},{value:"#8A2BE2",css:!0,name:"blueviolet"},{value:"#9B30FF",name:"purple 1"},{value:"#912CEE",name:"purple 2"},{value:"#7D26CD",name:"purple 3"},{value:"#551A8B",name:"purple 4"},{value:"#9370DB",css:!0,name:"mediumpurple"},{value:"#AB82FF",name:"mediumpurple 1"},{value:"#9F79EE",name:"mediumpurple 2"},{value:"#8968CD",name:"mediumpurple 3"},{value:"#5D478B",name:"mediumpurple 4"},{value:"#483D8B",css:!0,name:"darkslateblue"},{value:"#8470FF",name:"lightslateblue"},{value:"#7B68EE",css:!0,name:"mediumslateblue"},{value:"#6A5ACD",css:!0,name:"slateblue"},{value:"#836FFF",name:"slateblue 1"},{value:"#7A67EE",name:"slateblue 2"},{value:"#6959CD",name:"slateblue 3"},{value:"#473C8B",name:"slateblue 4"},{value:"#F8F8FF",css:!0,name:"ghostwhite"},{value:"#E6E6FA",css:!0,name:"lavender"},{value:"#0000FF",vga:!0,css:!0,name:"blue"},{value:"#0000EE",name:"blue 2"},{value:"#0000CD",name:"blue 3"},{value:"#0000CD",css:!0,name:"mediumblue"},{value:"#00008B",name:"blue 4"},{value:"#00008B",css:!0,name:"darkblue"},{value:"#000080",vga:!0,css:!0,name:"navy"},{value:"#191970",css:!0,name:"midnightblue"},{value:"#3D59AB",name:"cobalt"},{value:"#4169E1",css:!0,name:"royalblue"},{value:"#4876FF",name:"royalblue 1"},{value:"#436EEE",name:"royalblue 2"},{value:"#3A5FCD",name:"royalblue 3"},{value:"#27408B",name:"royalblue 4"},{value:"#6495ED",css:!0,name:"cornflowerblue"},{value:"#B0C4DE",css:!0,name:"lightsteelblue"},{value:"#CAE1FF",name:"lightsteelblue 1"},{value:"#BCD2EE",name:"lightsteelblue 2"},{value:"#A2B5CD",name:"lightsteelblue 3"},{value:"#6E7B8B",name:"lightsteelblue 4"},{value:"#778899",css:!0,name:"lightslategray"},{value:"#708090",css:!0,name:"slategray"},{value:"#C6E2FF",name:"slategray 1"},{value:"#B9D3EE",name:"slategray 2"},{value:"#9FB6CD",name:"slategray 3"},{value:"#6C7B8B",name:"slategray 4"},{value:"#1E90FF",name:"dodgerblue 1"},{value:"#1E90FF",css:!0,name:"dodgerblue"},{value:"#1C86EE",name:"dodgerblue 2"},{value:"#1874CD",name:"dodgerblue 3"},{value:"#104E8B",name:"dodgerblue 4"},{value:"#F0F8FF",css:!0,name:"aliceblue"},{value:"#4682B4",css:!0,name:"steelblue"},{value:"#63B8FF",name:"steelblue 1"},{value:"#5CACEE",name:"steelblue 2"},{value:"#4F94CD",name:"steelblue 3"},{value:"#36648B",name:"steelblue 4"},{value:"#87CEFA",css:!0,name:"lightskyblue"},{value:"#B0E2FF",name:"lightskyblue 1"},{value:"#A4D3EE",name:"lightskyblue 2"},{value:"#8DB6CD",name:"lightskyblue 3"},{value:"#607B8B",name:"lightskyblue 4"},{value:"#87CEFF",name:"skyblue 1"},{value:"#7EC0EE",name:"skyblue 2"},{value:"#6CA6CD",name:"skyblue 3"},{value:"#4A708B",name:"skyblue 4"},{value:"#87CEEB",css:!0,name:"skyblue"},{value:"#00BFFF",name:"deepskyblue 1"},{value:"#00BFFF",css:!0,name:"deepskyblue"},{value:"#00B2EE",name:"deepskyblue 2"},{value:"#009ACD",name:"deepskyblue 3"},{value:"#00688B",name:"deepskyblue 4"},{value:"#33A1C9",name:"peacock"},{value:"#ADD8E6",css:!0,name:"lightblue"},{value:"#BFEFFF",name:"lightblue 1"},{value:"#B2DFEE",name:"lightblue 2"},{value:"#9AC0CD",name:"lightblue 3"},{value:"#68838B",name:"lightblue 4"},{value:"#B0E0E6",css:!0,name:"powderblue"},{value:"#98F5FF",name:"cadetblue 1"},{value:"#8EE5EE",name:"cadetblue 2"},{value:"#7AC5CD",name:"cadetblue 3"},{value:"#53868B",name:"cadetblue 4"},{value:"#00F5FF",name:"turquoise 1"},{value:"#00E5EE",name:"turquoise 2"},{value:"#00C5CD",name:"turquoise 3"},{value:"#00868B",name:"turquoise 4"},{value:"#5F9EA0",css:!0,name:"cadetblue"},{value:"#00CED1",css:!0,name:"darkturquoise"},{value:"#F0FFFF",name:"azure 1"},{value:"#F0FFFF",css:!0,name:"azure"},{value:"#E0EEEE",name:"azure 2"},{value:"#C1CDCD",name:"azure 3"},{value:"#838B8B",name:"azure 4"},{value:"#E0FFFF",name:"lightcyan 1"},{value:"#E0FFFF",css:!0,name:"lightcyan"},{value:"#D1EEEE",name:"lightcyan 2"},{value:"#B4CDCD",name:"lightcyan 3"},{value:"#7A8B8B",name:"lightcyan 4"},{value:"#BBFFFF",name:"paleturquoise 1"},{value:"#AEEEEE",name:"paleturquoise 2"},{value:"#AEEEEE",css:!0,name:"paleturquoise"},{value:"#96CDCD",name:"paleturquoise 3"},{value:"#668B8B",name:"paleturquoise 4"},{value:"#2F4F4F",css:!0,name:"darkslategray"},{value:"#97FFFF",name:"darkslategray 1"},{value:"#8DEEEE",name:"darkslategray 2"},{value:"#79CDCD",name:"darkslategray 3"},{value:"#528B8B",name:"darkslategray 4"},{value:"#00FFFF",name:"cyan"},{value:"#00FFFF",css:!0,name:"aqua"},{value:"#00EEEE",name:"cyan 2"},{value:"#00CDCD",name:"cyan 3"},{value:"#008B8B",name:"cyan 4"},{value:"#008B8B",css:!0,name:"darkcyan"},{value:"#008080",vga:!0,css:!0,name:"teal"},{value:"#48D1CC",css:!0,name:"mediumturquoise"},{value:"#20B2AA",css:!0,name:"lightseagreen"},{value:"#03A89E",name:"manganeseblue"},{value:"#40E0D0",css:!0,name:"turquoise"},{value:"#808A87",name:"coldgrey"},{value:"#00C78C",name:"turquoiseblue"},{value:"#7FFFD4",name:"aquamarine 1"},{value:"#7FFFD4",css:!0,name:"aquamarine"},{value:"#76EEC6",name:"aquamarine 2"},{value:"#66CDAA",name:"aquamarine 3"},{value:"#66CDAA",css:!0,name:"mediumaquamarine"},{value:"#458B74",name:"aquamarine 4"},{value:"#00FA9A",css:!0,name:"mediumspringgreen"},{value:"#F5FFFA",css:!0,name:"mintcream"},{value:"#00FF7F",css:!0,name:"springgreen"},{value:"#00EE76",name:"springgreen 1"},{value:"#00CD66",name:"springgreen 2"},{value:"#008B45",name:"springgreen 3"},{value:"#3CB371",css:!0,name:"mediumseagreen"},{value:"#54FF9F",name:"seagreen 1"},{value:"#4EEE94",name:"seagreen 2"},{value:"#43CD80",name:"seagreen 3"},{value:"#2E8B57",name:"seagreen 4"},{value:"#2E8B57",css:!0,name:"seagreen"},{value:"#00C957",name:"emeraldgreen"},{value:"#BDFCC9",name:"mint"},{value:"#3D9140",name:"cobaltgreen"},{value:"#F0FFF0",name:"honeydew 1"},{value:"#F0FFF0",css:!0,name:"honeydew"},{value:"#E0EEE0",name:"honeydew 2"},{value:"#C1CDC1",name:"honeydew 3"},{value:"#838B83",name:"honeydew 4"},{value:"#8FBC8F",css:!0,name:"darkseagreen"},{value:"#C1FFC1",name:"darkseagreen 1"},{value:"#B4EEB4",name:"darkseagreen 2"},{value:"#9BCD9B",name:"darkseagreen 3"},{value:"#698B69",name:"darkseagreen 4"},{value:"#98FB98",css:!0,name:"palegreen"},{value:"#9AFF9A",name:"palegreen 1"},{value:"#90EE90",name:"palegreen 2"},{value:"#90EE90",css:!0,name:"lightgreen"},{value:"#7CCD7C",name:"palegreen 3"},{value:"#548B54",name:"palegreen 4"},{value:"#32CD32",css:!0,name:"limegreen"},{value:"#228B22",css:!0,name:"forestgreen"},{value:"#00FF00",vga:!0,name:"green 1"},{value:"#00FF00",vga:!0,css:!0,name:"lime"},{value:"#00EE00",name:"green 2"},{value:"#00CD00",name:"green 3"},{value:"#008B00",name:"green 4"},{value:"#008000",vga:!0,css:!0,name:"green"},{value:"#006400",css:!0,name:"darkgreen"},{value:"#308014",name:"sapgreen"},{value:"#7CFC00",css:!0,name:"lawngreen"},{value:"#7FFF00",name:"chartreuse 1"},{value:"#7FFF00",css:!0,name:"chartreuse"},{value:"#76EE00",name:"chartreuse 2"},{value:"#66CD00",name:"chartreuse 3"},{value:"#458B00",name:"chartreuse 4"},{value:"#ADFF2F",css:!0,name:"greenyellow"},{value:"#CAFF70",name:"darkolivegreen 1"},{value:"#BCEE68",name:"darkolivegreen 2"},{value:"#A2CD5A",name:"darkolivegreen 3"},{value:"#6E8B3D",name:"darkolivegreen 4"},{value:"#556B2F",css:!0,name:"darkolivegreen"},{value:"#6B8E23",css:!0,name:"olivedrab"},{value:"#C0FF3E",name:"olivedrab 1"},{value:"#B3EE3A",name:"olivedrab 2"},{value:"#9ACD32",name:"olivedrab 3"},{value:"#9ACD32",css:!0,name:"yellowgreen"},{value:"#698B22",name:"olivedrab 4"},{value:"#FFFFF0",name:"ivory 1"},{value:"#FFFFF0",css:!0,name:"ivory"},{value:"#EEEEE0",name:"ivory 2"},{value:"#CDCDC1",name:"ivory 3"},{value:"#8B8B83",name:"ivory 4"},{value:"#F5F5DC",css:!0,name:"beige"},{value:"#FFFFE0",name:"lightyellow 1"},{value:"#FFFFE0",css:!0,name:"lightyellow"},{value:"#EEEED1",name:"lightyellow 2"},{value:"#CDCDB4",name:"lightyellow 3"},{value:"#8B8B7A",name:"lightyellow 4"},{value:"#FAFAD2",css:!0,name:"lightgoldenrodyellow"},{value:"#FFFF00",vga:!0,name:"yellow 1"},{value:"#FFFF00",vga:!0,css:!0,name:"yellow"},{value:"#EEEE00",name:"yellow 2"},{value:"#CDCD00",name:"yellow 3"},{value:"#8B8B00",name:"yellow 4"},{value:"#808069",name:"warmgrey"},{value:"#808000",vga:!0,css:!0,name:"olive"},{value:"#BDB76B",css:!0,name:"darkkhaki"},{value:"#FFF68F",name:"khaki 1"},{value:"#EEE685",name:"khaki 2"},{value:"#CDC673",name:"khaki 3"},{value:"#8B864E",name:"khaki 4"},{value:"#F0E68C",css:!0,name:"khaki"},{value:"#EEE8AA",css:!0,name:"palegoldenrod"},{value:"#FFFACD",name:"lemonchiffon 1"},{value:"#FFFACD",css:!0,name:"lemonchiffon"},{value:"#EEE9BF",name:"lemonchiffon 2"},{value:"#CDC9A5",name:"lemonchiffon 3"},{value:"#8B8970",name:"lemonchiffon 4"},{value:"#FFEC8B",name:"lightgoldenrod 1"},{value:"#EEDC82",name:"lightgoldenrod 2"},{value:"#CDBE70",name:"lightgoldenrod 3"},{value:"#8B814C",name:"lightgoldenrod 4"},{value:"#E3CF57",name:"banana"},{value:"#FFD700",name:"gold 1"},{value:"#FFD700",css:!0,name:"gold"},{value:"#EEC900",name:"gold 2"},{value:"#CDAD00",name:"gold 3"},{value:"#8B7500",name:"gold 4"},{value:"#FFF8DC",name:"cornsilk 1"},{value:"#FFF8DC",css:!0,name:"cornsilk"},{value:"#EEE8CD",name:"cornsilk 2"},{value:"#CDC8B1",name:"cornsilk 3"},{value:"#8B8878",name:"cornsilk 4"},{value:"#DAA520",css:!0,name:"goldenrod"},{value:"#FFC125",name:"goldenrod 1"},{value:"#EEB422",name:"goldenrod 2"},{value:"#CD9B1D",name:"goldenrod 3"},{value:"#8B6914",name:"goldenrod 4"},{value:"#B8860B",css:!0,name:"darkgoldenrod"},{value:"#FFB90F",name:"darkgoldenrod 1"},{value:"#EEAD0E",name:"darkgoldenrod 2"},{value:"#CD950C",name:"darkgoldenrod 3"},{value:"#8B6508",name:"darkgoldenrod 4"},{value:"#FFA500",name:"orange 1"},{value:"#FF8000",css:!0,name:"orange"},{value:"#EE9A00",name:"orange 2"},{value:"#CD8500",name:"orange 3"},{value:"#8B5A00",name:"orange 4"},{value:"#FFFAF0",css:!0,name:"floralwhite"},{value:"#FDF5E6",css:!0,name:"oldlace"},{value:"#F5DEB3",css:!0,name:"wheat"},{value:"#FFE7BA",name:"wheat 1"},{value:"#EED8AE",name:"wheat 2"},{value:"#CDBA96",name:"wheat 3"},{value:"#8B7E66",name:"wheat 4"},{value:"#FFE4B5",css:!0,name:"moccasin"},{value:"#FFEFD5",css:!0,name:"papayawhip"},{value:"#FFEBCD",css:!0,name:"blanchedalmond"},{value:"#FFDEAD",name:"navajowhite 1"},{value:"#FFDEAD",css:!0,name:"navajowhite"},{value:"#EECFA1",name:"navajowhite 2"},{value:"#CDB38B",name:"navajowhite 3"},{value:"#8B795E",name:"navajowhite 4"},{value:"#FCE6C9",name:"eggshell"},{value:"#D2B48C",css:!0,name:"tan"},{value:"#9C661F",name:"brick"},{value:"#FF9912",name:"cadmiumyellow"},{value:"#FAEBD7",css:!0,name:"antiquewhite"},{value:"#FFEFDB",name:"antiquewhite 1"},{value:"#EEDFCC",name:"antiquewhite 2"},{value:"#CDC0B0",name:"antiquewhite 3"},{value:"#8B8378",name:"antiquewhite 4"},{value:"#DEB887",css:!0,name:"burlywood"},{value:"#FFD39B",name:"burlywood 1"},{value:"#EEC591",name:"burlywood 2"},{value:"#CDAA7D",name:"burlywood 3"},{value:"#8B7355",name:"burlywood 4"},{value:"#FFE4C4",name:"bisque 1"},{value:"#FFE4C4",css:!0,name:"bisque"},{value:"#EED5B7",name:"bisque 2"},{value:"#CDB79E",name:"bisque 3"},{value:"#8B7D6B",name:"bisque 4"},{value:"#E3A869",name:"melon"},{value:"#ED9121",name:"carrot"},{value:"#FF8C00",css:!0,name:"darkorange"},{value:"#FF7F00",name:"darkorange 1"},{value:"#EE7600",name:"darkorange 2"},{value:"#CD6600",name:"darkorange 3"},{value:"#8B4500",name:"darkorange 4"},{value:"#FFA54F",name:"tan 1"},{value:"#EE9A49",name:"tan 2"},{value:"#CD853F",name:"tan 3"},{value:"#CD853F",css:!0,name:"peru"},{value:"#8B5A2B",name:"tan 4"},{value:"#FAF0E6",css:!0,name:"linen"},{value:"#FFDAB9",name:"peachpuff 1"},{value:"#FFDAB9",css:!0,name:"peachpuff"},{value:"#EECBAD",name:"peachpuff 2"},{value:"#CDAF95",name:"peachpuff 3"},{value:"#8B7765",name:"peachpuff 4"},{value:"#FFF5EE",name:"seashell 1"},{value:"#FFF5EE",css:!0,name:"seashell"},{value:"#EEE5DE",name:"seashell 2"},{value:"#CDC5BF",name:"seashell 3"},{value:"#8B8682",name:"seashell 4"},{value:"#F4A460",css:!0,name:"sandybrown"},{value:"#C76114",name:"rawsienna"},{value:"#D2691E",css:!0,name:"chocolate"},{value:"#FF7F24",name:"chocolate 1"},{value:"#EE7621",name:"chocolate 2"},{value:"#CD661D",name:"chocolate 3"},{value:"#8B4513",name:"chocolate 4"},{value:"#8B4513",css:!0,name:"saddlebrown"},{value:"#292421",name:"ivoryblack"},{value:"#FF7D40",name:"flesh"},{value:"#FF6103",name:"cadmiumorange"},{value:"#8A360F",name:"burntsienna"},{value:"#A0522D",css:!0,name:"sienna"},{value:"#FF8247",name:"sienna 1"},{value:"#EE7942",name:"sienna 2"},{value:"#CD6839",name:"sienna 3"},{value:"#8B4726",name:"sienna 4"},{value:"#FFA07A",name:"lightsalmon 1"},{value:"#FFA07A",css:!0,name:"lightsalmon"},{value:"#EE9572",name:"lightsalmon 2"},{value:"#CD8162",name:"lightsalmon 3"},{value:"#8B5742",name:"lightsalmon 4"},{value:"#FF7F50",css:!0,name:"coral"},{value:"#FF4500",name:"orangered 1"},{value:"#FF4500",css:!0,name:"orangered"},{value:"#EE4000",name:"orangered 2"},{value:"#CD3700",name:"orangered 3"},{value:"#8B2500",name:"orangered 4"},{value:"#5E2612",name:"sepia"},{value:"#E9967A",css:!0,name:"darksalmon"},{value:"#FF8C69",name:"salmon 1"},{value:"#EE8262",name:"salmon 2"},{value:"#CD7054",name:"salmon 3"},{value:"#8B4C39",name:"salmon 4"},{value:"#FF7256",name:"coral 1"},{value:"#EE6A50",name:"coral 2"},{value:"#CD5B45",name:"coral 3"},{value:"#8B3E2F",name:"coral 4"},{value:"#8A3324",name:"burntumber"},{value:"#FF6347",name:"tomato 1"},{value:"#FF6347",css:!0,name:"tomato"},{value:"#EE5C42",name:"tomato 2"},{value:"#CD4F39",name:"tomato 3"},{value:"#8B3626",name:"tomato 4"},{value:"#FA8072",css:!0,name:"salmon"},{value:"#FFE4E1",name:"mistyrose 1"},{value:"#FFE4E1",css:!0,name:"mistyrose"},{value:"#EED5D2",name:"mistyrose 2"},{value:"#CDB7B5",name:"mistyrose 3"},{value:"#8B7D7B",name:"mistyrose 4"},{value:"#FFFAFA",name:"snow 1"},{value:"#FFFAFA",css:!0,name:"snow"},{value:"#EEE9E9",name:"snow 2"},{value:"#CDC9C9",name:"snow 3"},{value:"#8B8989",name:"snow 4"},{value:"#BC8F8F",css:!0,name:"rosybrown"},{value:"#FFC1C1",name:"rosybrown 1"},{value:"#EEB4B4",name:"rosybrown 2"},{value:"#CD9B9B",name:"rosybrown 3"},{value:"#8B6969",name:"rosybrown 4"},{value:"#F08080",css:!0,name:"lightcoral"},{value:"#CD5C5C",css:!0,name:"indianred"},{value:"#FF6A6A",name:"indianred 1"},{value:"#EE6363",name:"indianred 2"},{value:"#8B3A3A",name:"indianred 4"},{value:"#CD5555",name:"indianred 3"},{value:"#A52A2A",css:!0,name:"brown"},{value:"#FF4040",name:"brown 1"},{value:"#EE3B3B",name:"brown 2"},{value:"#CD3333",name:"brown 3"},{value:"#8B2323",name:"brown 4"},{value:"#B22222",css:!0,name:"firebrick"},{value:"#FF3030",name:"firebrick 1"},{value:"#EE2C2C",name:"firebrick 2"},{value:"#CD2626",name:"firebrick 3"},{value:"#8B1A1A",name:"firebrick 4"},{value:"#FF0000",vga:!0,name:"red 1"},{value:"#FF0000",vga:!0,css:!0,name:"red"},{value:"#EE0000",name:"red 2"},{value:"#CD0000",name:"red 3"},{value:"#8B0000",name:"red 4"},{value:"#8B0000",css:!0,name:"darkred"},{value:"#800000",vga:!0,css:!0,name:"maroon"},{value:"#8E388E",name:"sgi beet"},{value:"#7171C6",name:"sgi slateblue"},{value:"#7D9EC0",name:"sgi lightblue"},{value:"#388E8E",name:"sgi teal"},{value:"#71C671",name:"sgi chartreuse"},{value:"#8E8E38",name:"sgi olivedrab"},{value:"#C5C1AA",name:"sgi brightgray"},{value:"#C67171",name:"sgi salmon"},{value:"#555555",name:"sgi darkgray"},{value:"#1E1E1E",name:"sgi gray 12"},{value:"#282828",name:"sgi gray 16"},{value:"#515151",name:"sgi gray 32"},{value:"#5B5B5B",name:"sgi gray 36"},{value:"#848484",name:"sgi gray 52"},{value:"#8E8E8E",name:"sgi gray 56"},{value:"#AAAAAA",name:"sgi lightgray"},{value:"#B7B7B7",name:"sgi gray 72"},{value:"#C1C1C1",name:"sgi gray 76"},{value:"#EAEAEA",name:"sgi gray 92"},{value:"#F4F4F4",name:"sgi gray 96"},{value:"#FFFFFF",vga:!0,css:!0,name:"white"},{value:"#F5F5F5",name:"white smoke"},{value:"#F5F5F5",name:"gray 96"},{value:"#DCDCDC",css:!0,name:"gainsboro"},{value:"#D3D3D3",css:!0,name:"lightgrey"},{value:"#C0C0C0",vga:!0,css:!0,name:"silver"},{value:"#A9A9A9",css:!0,name:"darkgray"},{value:"#808080",vga:!0,css:!0,name:"gray"},{value:"#696969",css:!0,name:"dimgray"},{value:"#696969",name:"gray 42"},{value:"#000000",vga:!0,css:!0,name:"black"},{value:"#FCFCFC",name:"gray 99"},{value:"#FAFAFA",name:"gray 98"},{value:"#F7F7F7",name:"gray 97"},{value:"#F2F2F2",name:"gray 95"},{value:"#F0F0F0",name:"gray 94"},{value:"#EDEDED",name:"gray 93"},{value:"#EBEBEB",name:"gray 92"},{value:"#E8E8E8",name:"gray 91"},{value:"#E5E5E5",name:"gray 90"},{value:"#E3E3E3",name:"gray 89"},{value:"#E0E0E0",name:"gray 88"},{value:"#DEDEDE",name:"gray 87"},{value:"#DBDBDB",name:"gray 86"},{value:"#D9D9D9",name:"gray 85"},{value:"#D6D6D6",name:"gray 84"},{value:"#D4D4D4",name:"gray 83"},{value:"#D1D1D1",name:"gray 82"},{value:"#CFCFCF",name:"gray 81"},{value:"#CCCCCC",name:"gray 80"},{value:"#C9C9C9",name:"gray 79"},{value:"#C7C7C7",name:"gray 78"},{value:"#C4C4C4",name:"gray 77"},{value:"#C2C2C2",name:"gray 76"},{value:"#BFBFBF",name:"gray 75"},{value:"#BDBDBD",name:"gray 74"},{value:"#BABABA",name:"gray 73"},{value:"#B8B8B8",name:"gray 72"},{value:"#B5B5B5",name:"gray 71"},{value:"#B3B3B3",name:"gray 70"},{value:"#B0B0B0",name:"gray 69"},{value:"#ADADAD",name:"gray 68"},{value:"#ABABAB",name:"gray 67"},{value:"#A8A8A8",name:"gray 66"},{value:"#A6A6A6",name:"gray 65"},{value:"#A3A3A3",name:"gray 64"},{value:"#A1A1A1",name:"gray 63"},{value:"#9E9E9E",name:"gray 62"},{value:"#9C9C9C",name:"gray 61"},{value:"#999999",name:"gray 60"},{value:"#969696",name:"gray 59"},{value:"#949494",name:"gray 58"},{value:"#919191",name:"gray 57"},{value:"#8F8F8F",name:"gray 56"},{value:"#8C8C8C",name:"gray 55"},{value:"#8A8A8A",name:"gray 54"},{value:"#878787",name:"gray 53"},{value:"#858585",name:"gray 52"},{value:"#828282",name:"gray 51"},{value:"#7F7F7F",name:"gray 50"},{value:"#7D7D7D",name:"gray 49"},{value:"#7A7A7A",name:"gray 48"},{value:"#787878",name:"gray 47"},{value:"#757575",name:"gray 46"},{value:"#737373",name:"gray 45"},{value:"#707070",name:"gray 44"},{value:"#6E6E6E",name:"gray 43"},{value:"#666666",name:"gray 40"},{value:"#636363",name:"gray 39"},{value:"#616161",name:"gray 38"},{value:"#5E5E5E",name:"gray 37"},{value:"#5C5C5C",name:"gray 36"},{value:"#595959",name:"gray 35"},{value:"#575757",name:"gray 34"},{value:"#545454",name:"gray 33"},{value:"#525252",name:"gray 32"},{value:"#4F4F4F",name:"gray 31"},{value:"#4D4D4D",name:"gray 30"},{value:"#4A4A4A",name:"gray 29"},{value:"#474747",name:"gray 28"},{value:"#454545",name:"gray 27"},{value:"#424242",name:"gray 26"},{value:"#404040",name:"gray 25"},{value:"#3D3D3D",name:"gray 24"},{value:"#3B3B3B",name:"gray 23"},{value:"#383838",name:"gray 22"},{value:"#363636",name:"gray 21"},{value:"#333333",name:"gray 20"},{value:"#303030",name:"gray 19"},{value:"#2E2E2E",name:"gray 18"},{value:"#2B2B2B",name:"gray 17"},{value:"#292929",name:"gray 16"},{value:"#262626",name:"gray 15"},{value:"#242424",name:"gray 14"},{value:"#212121",name:"gray 13"},{value:"#1F1F1F",name:"gray 12"},{value:"#1C1C1C",name:"gray 11"},{value:"#1A1A1A",name:"gray 10"},{value:"#171717",name:"gray 9"},{value:"#141414",name:"gray 8"},{value:"#121212",name:"gray 7"},{value:"#0F0F0F",name:"gray 6"},{value:"#0D0D0D",name:"gray 5"},{value:"#0A0A0A",name:"gray 4"},{value:"#080808",name:"gray 3"},{value:"#050505",name:"gray 2"},{value:"#030303",name:"gray 1"},{value:"#F5F5F5",css:!0,name:"whitesmoke"}]},function(e,t){e.exports=require("tty")},function(e,t,n){"use strict";const r=n(16),{StringDecoder:a}=n(56),{Stream:i}=n(15);function o(){}e.exports=((e,t)=>{const n=Buffer.alloc(65536),s=new a("utf8"),l=new i;let u="",c=0,f=0;return-1===e.start&&delete e.start,l.readable=!0,l.destroy=(()=>{l.destroyed=!0,l.emit("end"),l.emit("close")}),r.open(e.file,"a+","0644",(a,i)=>{if(a)return t?t(a):l.emit("error",a),void l.destroy();!function a(){if(!l.destroyed)return r.read(i,n,0,n.length,c,(r,i)=>{if(r)return t?t(r):l.emit("error",r),void l.destroy();if(!i)return u&&((null==e.start||f>e.start)&&(t?t(null,u):l.emit("line",u)),f++,u=""),setTimeout(a,1e3);let o=s.write(n.slice(0,i));t||l.emit("data",o);const h=(o=(u+o).split(/\n+/)).length-1;let d=0;for(;d<h;d++)(null==e.start||f>e.start)&&(t?t(null,o[d]):l.emit("line",o[d])),f++;return u=o[h],c+=i,a()});r.close(i,o)}()}),t?l.destroy:l})},function(e,t,n){"use strict";const r=n(204),a=n(205),{Stream:i}=n(15),o=n(8);e.exports=class extends o{constructor(e={}){super(e),this.name=e.name||"http",this.ssl=!!e.ssl,this.host=e.host||"localhost",this.port=e.port,this.auth=e.auth,this.path=e.path||"",this.agent=e.agent,this.headers=e.headers||{},this.headers["content-type"]="application/json",this.port||(this.port=this.ssl?443:80)}log(e,t){this._request(e,(t,n)=>{n&&200!==n.statusCode&&(t=new Error(`Invalid HTTP Status Code: ${n.statusCode}`)),t?this.emit("warn",t):this.emit("logged",e)}),t&&setImmediate(t)}query(e,t){"function"==typeof e&&(t=e,e={}),(e={method:"query",params:this.normalizeQuery(e)}).params.path&&(e.path=e.params.path,delete e.params.path),e.params.auth&&(e.auth=e.params.auth,delete e.params.auth),this._request(e,(e,n,r)=>{if(n&&200!==n.statusCode&&(e=new Error(`Invalid HTTP Status Code: ${n.statusCode}`)),e)return t(e);if("string"==typeof r)try{r=JSON.parse(r)}catch(e){return t(e)}t(null,r)})}stream(e={}){const t=new i;(e={method:"stream",params:e}).params.path&&(e.path=e.params.path,delete e.params.path),e.params.auth&&(e.auth=e.params.auth,delete e.params.auth);let n="";const r=this._request(e);return t.destroy=(()=>r.destroy()),r.on("data",e=>{const r=(e=(n+e).split(/\n+/)).length-1;let a=0;for(;a<r;a++)try{t.emit("log",JSON.parse(e[a]))}catch(e){t.emit("error",e)}n=e[r]}),r.on("error",e=>t.emit("error",e)),t}_request(e,t){const n=(e=e||{}).auth||this.auth,i=e.path||this.path||"";delete e.auth,delete e.path;const o=(this.ssl?a:r).request({method:"POST",host:this.host,port:this.port,path:`/${i.replace(/^\//,"")}`,headers:this.headers,auth:n?`${n.username}:${n.password}`:"",agent:this.agent});o.on("error",t),o.on("response",e=>e.on("end",()=>t(null,e)).resume()),o.end(Buffer.from(JSON.stringify(e),"utf8"))}}},function(e,t){e.exports=require("http")},function(e,t){e.exports=require("https")},function(e,t,n){"use strict";const r=n(102),{MESSAGE:a}=n(0),i=n(6),o=n(8);e.exports=class extends o{constructor(e={}){if(super(e),!e.stream||!r(e.stream))throw new Error("options.stream is required.");this._stream=e.stream,this._stream.setMaxListeners(1/0),this.isObjectMode=e.stream._writableState.objectMode,this.eol=e.eol||i.EOL}log(e,t){if(setImmediate(()=>this.emit("logged",e)),this.isObjectMode)return this._stream.write(e),void(t&&t());this._stream.write(`${e[a]}${this.eol}`),t&&t()}}},function(e,t,n){"use strict";const{Stream:r,Transform:a}=n(15),i=n(53),{LEVEL:o,SPLAT:s}=n(0),l=n(102),u=n(104),c=n(108),f=n(210),h=n(211),{warn:d}=n(76),p=n(52),m=/%[scdjifoO%]/g;class g extends a{constructor(e){super({objectMode:!0}),this.configure(e)}child(e){const t=this;return Object.create(t,{write:{value:function(n){const r=Object.assign({},e,n);n instanceof Error&&(r.stack=n.stack,r.message=n.message),t.write(r)}}})}configure({silent:e,format:t,defaultMeta:r,levels:a,level:i="info",exitOnError:o=!0,transports:s,colors:l,emitErrs:f,formatters:h,padLevels:d,rewriters:m,stripColors:g,exceptionHandlers:v,rejectionHandlers:b}={}){if(this.transports.length&&this.clear(),this.silent=e,this.format=t||this.format||n(32)(),this.defaultMeta=r||null,this.levels=a||this.levels||p.npm.levels,this.level=i,this.exceptions=new u(this),this.rejections=new c(this),this.profilers={},this.exitOnError=o,s&&(s=Array.isArray(s)?s:[s]).forEach(e=>this.add(e)),l||f||h||d||m||g)throw new Error(["{ colors, emitErrs, formatters, padLevels, rewriters, stripColors } were removed in winston@3.0.0.","Use a custom winston.format(function) instead.","See: https://github.com/winstonjs/winston/tree/master/UPGRADE-3.0.md"].join("\n"));v&&this.exceptions.handle(v),b&&this.rejections.handle(b)}isLevelEnabled(e){const t=v(this.levels,e);if(null===t)return!1;const n=v(this.levels,this.level);return null!==n&&(this.transports&&0!==this.transports.length?-1!==this.transports.findIndex(e=>{let r=v(this.levels,e.level);return null===r&&(r=n),r>=t}):n>=t)}log(e,t,...n){if(1===arguments.length)return e[o]=e.level,this._addDefaultMeta(e),this.write(e),this;if(2===arguments.length)return t&&"object"==typeof t?(t[o]=t.level=e,this._addDefaultMeta(t),this.write(t),this):(this.write({[o]:e,level:e,message:t}),this);const[r]=n;if("object"==typeof r&&null!==r){if(!(t&&t.match&&t.match(m))){const a=Object.assign({},this.defaultMeta,r,{[o]:e,[s]:n,level:e,message:t});return r.message&&(a.message+=`${r.message}`),r.stack&&(a.stack=r.stack),this.write(a),this}}return this.write(Object.assign({},this.defaultMeta,{[o]:e,[s]:n,level:e,message:t})),this}_transform(e,t,n){if(this.silent)return n();e[o]||(e[o]=e.level),this.levels[e[o]]||0===this.levels[e[o]]||console.error("[winston] Unknown logger level: %s",e[o]),this._readableState.pipes||console.error("[winston] Attempt to write logs with no transports %j",e);try{this.push(this.format.transform(e,this.format.options))}catch(e){throw e}finally{n()}}_final(e){const t=this.transports.slice();i(t,(e,t)=>{if(!e||e.finished)return setImmediate(t);e.once("finish",t),e.end()},e)}add(e){const t=!l(e)||e.log.length>2?new f({transport:e}):e;if(!t._writableState||!t._writableState.objectMode)throw new Error("Transports must WritableStreams in objectMode. Set { objectMode: true }.");return this._onEvent("error",t),this._onEvent("warn",t),this.pipe(t),e.handleExceptions&&this.exceptions.handle(),e.handleRejections&&this.rejections.handle(),this}remove(e){let t=e;return(!l(e)||e.log.length>2)&&(t=this.transports.filter(t=>t.transport===e)[0]),t&&this.unpipe(t),this}clear(){return this.unpipe(),this}close(){return this.clear(),this.emit("close"),this}setLevels(){d.deprecated("setLevels")}query(e,t){"function"==typeof e&&(t=e,e={}),e=e||{};const n={},r=Object.assign({},e.query||{});i(this.transports.filter(e=>!!e.query),function(t,a){!function(t,n){e.query&&"function"==typeof t.formatQuery&&(e.query=t.formatQuery(r)),t.query(e,(r,a)=>{if(r)return n(r);"function"==typeof t.formatResults&&(a=t.formatResults(a,e.format)),n(null,a)})}(t,(e,r)=>{a&&((r=e||r)&&(n[t.name]=r),a()),a=null})},()=>t(null,n))}stream(e={}){const t=new r,n=[];return t._streams=n,t.destroy=(()=>{let e=n.length;for(;e--;)n[e].destroy()}),this.transports.filter(e=>!!e.stream).forEach(r=>{const a=r.stream(e);a&&(n.push(a),a.on("log",e=>{e.transport=e.transport||[],e.transport.push(r.name),t.emit("log",e)}),a.on("error",e=>{e.transport=e.transport||[],e.transport.push(r.name),t.emit("error",e)}))}),t}startTimer(){return new h(this)}profile(e,...t){const n=Date.now();if(this.profilers[e]){const r=this.profilers[e];delete this.profilers[e],"function"==typeof t[t.length-2]&&(console.warn("Callback function no longer supported as of winston@3.0.0"),t.pop());const a="object"==typeof t[t.length-1]?t.pop():{};return a.level=a.level||"info",a.durationMs=n-r,a.message=a.message||e,this.write(a)}return this.profilers[e]=n,this}handleExceptions(...e){console.warn("Deprecated: .handleExceptions() will be removed in winston@4. Use .exceptions.handle()"),this.exceptions.handle(...e)}unhandleExceptions(...e){console.warn("Deprecated: .unhandleExceptions() will be removed in winston@4. Use .exceptions.unhandle()"),this.exceptions.unhandle(...e)}cli(){throw new Error(["Logger.cli() was removed in winston@3.0.0","Use a custom winston.formats.cli() instead.","See: https://github.com/winstonjs/winston/tree/master/UPGRADE-3.0.md"].join("\n"))}_onEvent(e,t){t["__winston"+e]||(t["__winston"+e]=function(n){this.emit(e,n,t)}.bind(this),t.on(e,t["__winston"+e]))}_addDefaultMeta(e){this.defaultMeta&&Object.assign(e,this.defaultMeta)}}function v(e,t){const n=e[t];return n||0===n?n:null}Object.defineProperty(g.prototype,"transports",{configurable:!1,enumerable:!0,get(){const{pipes:e}=this._readableState;return Array.isArray(e)?e:[e].filter(Boolean)}}),e.exports=g},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){((0,r.default)(e)?h:d)(e,(0,c.default)(t),n)};var r=f(n(26)),a=f(n(91)),i=f(n(87)),o=f(n(92)),s=f(n(47)),l=f(n(88)),u=f(n(90)),c=f(n(27));function f(e){return e&&e.__esModule?e:{default:e}}function h(e,t,n){n=(0,l.default)(n||s.default);var r=0,i=0,o=e.length;function c(e,t){e?n(e):++i!==o&&t!==a.default||n(null)}for(0===o&&n(null);r<o;r++)t(e[r],r,(0,u.default)(c))}var d=(0,o.default)(i.default,1/0);e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t,n,r){return e(t,r)}},e.exports=t.default},function(e,t,n){"use strict";const r=n(1),{LEVEL:a}=n(0),i=n(8),o=e.exports=function(e={}){if(i.call(this,e),!e.transport||"function"!=typeof e.transport.log)throw new Error("Invalid transport, must be an object with a log method.");this.transport=e.transport,this.level=this.level||e.transport.level,this.handleExceptions=this.handleExceptions||e.transport.handleExceptions,this._deprecated(),this.transport.__winstonError||(this.transport.__winstonError=function(e){this.emit("error",e,this.transport)}.bind(this),this.transport.on("error",this.transport.__winstonError))};r.inherits(o,i),o.prototype._write=function(e,t,n){if(this.silent||!0===e.exception&&!this.handleExceptions)return n(null);(!this.level||this.levels[this.level]>=this.levels[e[a]])&&this.transport.log(e[a],e.message,e,this._nop),n(null)},o.prototype._writev=function(e,t){for(let t=0;t<e.length;t++)this._accept(e[t])&&(this.transport.log(e[t].chunk[a],e[t].chunk.message,e[t].chunk,this._nop),e[t].callback());return t(null)},o.prototype._deprecated=function(){console.error([`${this.transport.name} is a legacy winston transport. Consider upgrading: `,"- Upgrade docs: https://github.com/winstonjs/winston/blob/master/UPGRADE-3.0.md"].join("\n"))},o.prototype.close=function(){this.transport.close&&this.transport.close(),this.transport.__winstonError&&(this.transport.removeListener("error",this.transport.__winstonError),this.transport.__winstonError=null)}},function(e,t,n){"use strict";e.exports=class{constructor(e){if(!e)throw new Error("Logger is required for profiling.");this.logger=e,this.start=Date.now()}done(...e){"function"==typeof e[e.length-1]&&(console.warn("Callback function no longer supported as of winston@3.0.0"),e.pop());const t="object"==typeof e[e.length-1]?e.pop():{};return t.level=t.level||"info",t.durationMs=Date.now()-this.start,this.logger.write(t)}}},function(e,t,n){"use strict";const r=n(103);e.exports=class{constructor(e={}){this.loggers=new Map,this.options=e}add(e,t){if(!this.loggers.has(e)){const n=(t=Object.assign({},t||this.options)).transports||this.options.transports;t.transports=n?n.slice():[];const a=r(t);a.on("close",()=>this._delete(e)),this.loggers.set(e,a)}return this.loggers.get(e)}get(e,t){return this.add(e,t)}has(e){return!!this.loggers.has(e)}close(e){if(e)return this._removeLogger(e);this.loggers.forEach((e,t)=>this._removeLogger(t))}_removeLogger(e){if(!this.loggers.has(e))return;this.loggers.get(e).close(),this._delete(e)}_delete(e){this.loggers.delete(e)}}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(a,i){function o(e){try{l(r.next(e))}catch(e){i(e)}}function s(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?a(e.value):new n(function(t){t(e.value)}).then(o,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const a=n(29),i=n(54),o=n(17).default.getLogger("stat-utilities");!function(e){e.trackEvent=function(e,t){return r(this,void 0,void 0,function*(){let n=(t=t||{}).attribute1||"",r=t.attribute2||"",s=t.extData||new Map;s.set("plugin_version",a.default.pluginVersion);let l=function(e){let t="";return e.forEach((e,n)=>{""!==t&&(t+=","),t=t+n+"="+e}),t}(s);o.info("key",e),o.info("attribute1",n),o.info("attribute2",r),o.info("extData",l),l=encodeURIComponent(l),o.info("encode extData",l),yield i.client.callServerFunction("TrackEvent",e,n,r,0,0,0,0,l)})}}(t.StatUtilitiesNS||(t.StatUtilitiesNS={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(17).default.getLogger("Thunder.Util");function a(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){e.formatSize=function(e,t){t=t||2;let n="0B";if("number"==typeof e&&e>0){let r=["B","KB","MB","GB","TB"],a=0,i=e;for(;i>=1e3&&!(a>=4);)i/=1024,a+=1;n=-1===String(i).indexOf(".")?i+r[a]:i.toFixed(t)+r[a]}return n},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){r.warn(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",r=!1,i=[],o=e.length;for(let s=0;s<o;s++){let l=e[s];if('"'!==l&&"'"!==l||(""===n?(r=!0,n=l):n===l&&(r=!1,n=""))," "!==l||r||s===o-1){if(s===o-1){let n=e.substring(t);""!==n.trim()&&i.push(a(n))}}else{let n=e.substring(t,s);""!==n.trim()&&i.push(a(n)),t=s+1}}return i},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,r)=>{e+=0===r?"?":"&",e+=`${n}=${t[n]}`}),e},e.getQueryString=function(e,t){return e&&t&&e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:""},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),r=t.split("."),a=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(r[e])>0){a=1;break}if(Number(n[e])-Number(r[e])<0){a=-1;break}}return a}}(t.ThunderUtil||(t.ThunderUtil={}))}]);
//# sourceMappingURL=index.js.map