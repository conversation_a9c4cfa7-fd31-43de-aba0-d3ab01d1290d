"""
安全程序功能演示
展示各种安全机制的工作原理
"""

import hashlib
import base64
import time
import random

def demo_encryption():
    """演示加密功能"""
    print("🔐 加密功能演示")
    print("=" * 40)
    
    # 原始卡密
    original_key = "555555"
    print(f"原始卡密: {original_key}")
    
    # 时间戳
    timestamp = str(int(time.time() / 3600))
    print(f"时间戳: {timestamp}")
    
    # 盐值
    salt = "9f4e8a6c3d7b2f5e"
    print(f"盐值: {salt}")
    
    # 第一步：SHA256哈希
    combined = original_key + timestamp + salt
    hash1 = hashlib.sha256(combined.encode()).hexdigest()
    print(f"SHA256哈希: {hash1[:32]}...")
    
    # 第二步：MD5哈希
    hash2 = hashlib.md5(hash1.encode()).hexdigest()
    print(f"MD5哈希: {hash2}")
    
    # 第三步：Base64编码
    final_hash = base64.b64encode(hash2.encode()).decode()
    print(f"最终哈希: {final_hash}")
    
    print()

def demo_xor_encryption():
    """演示XOR加密"""
    print("🔒 XOR加密演示")
    print("=" * 40)
    
    message = "欢迎进入VIP个人页面！"
    key = hashlib.md5(b"secure_key_2024").digest()
    
    print(f"原始消息: {message}")
    print(f"加密密钥: {key.hex()[:16]}...")
    
    # 加密
    encrypted = bytearray()
    for i, char in enumerate(message):
        encrypted.append(ord(char) ^ key[i % len(key)])
    
    encrypted_b64 = base64.b64encode(encrypted).decode()
    print(f"加密结果: {encrypted_b64}")
    
    # 解密
    encrypted_bytes = base64.b64decode(encrypted_b64)
    decrypted = ""
    for i, byte in enumerate(encrypted_bytes):
        decrypted += chr(byte ^ key[i % len(key)])
    
    print(f"解密结果: {decrypted}")
    print()

def demo_timing_attack():
    """演示时间攻击防护"""
    print("⏱️ 时间攻击防护演示")
    print("=" * 40)
    
    def verify_key_vulnerable(input_key, real_key):
        """易受时间攻击的验证函数"""
        if len(input_key) != len(real_key):
            return False
        
        for i in range(len(input_key)):
            if input_key[i] != real_key[i]:
                return False
        return True
    
    def verify_key_secure(input_key, real_key):
        """防时间攻击的验证函数"""
        if len(input_key) != len(real_key):
            time.sleep(random.uniform(0.1, 0.2))  # 随机延迟
            return False
        
        result = True
        for i in range(len(input_key)):
            if input_key[i] != real_key[i]:
                result = False
            # 不提前返回，继续检查所有字符
        
        time.sleep(random.uniform(0.1, 0.2))  # 随机延迟
        return result
    
    real_key = "555555"
    test_keys = ["123456", "555555", "555000", "555556"]
    
    print("易受攻击的验证:")
    for test_key in test_keys:
        start_time = time.perf_counter()
        result = verify_key_vulnerable(test_key, real_key)
        end_time = time.perf_counter()
        elapsed = (end_time - start_time) * 1000
        print(f"  {test_key}: {result} (耗时: {elapsed:.3f}ms)")
    
    print("\n安全的验证:")
    for test_key in test_keys:
        start_time = time.perf_counter()
        result = verify_key_secure(test_key, real_key)
        end_time = time.perf_counter()
        elapsed = (end_time - start_time) * 1000
        print(f"  {test_key}: {result} (耗时: {elapsed:.3f}ms)")
    
    print()

def demo_obfuscation():
    """演示代码混淆"""
    print("🎭 代码混淆演示")
    print("=" * 40)
    
    # 字符串混淆
    original = "555555"
    
    # Base64编码
    encoded = base64.b64encode(original.encode()).decode()
    print(f"原始字符串: {original}")
    print(f"Base64编码: {encoded}")
    
    # 添加随机字符
    import string
    chars = list(encoded)
    for i in range(len(chars) // 3):
        pos = random.randint(0, len(chars))
        chars.insert(pos, random.choice(string.ascii_letters))
    
    obfuscated = ''.join(chars)
    print(f"混淆后: {obfuscated}")
    
    # 反混淆
    valid_chars = string.ascii_letters + string.digits + '+/='
    cleaned = ''.join(c for c in obfuscated if c in valid_chars)
    decoded = base64.b64decode(cleaned).decode()
    print(f"反混淆: {decoded}")
    
    print()

def demo_anti_debug():
    """演示反调试技术"""
    print("🛡️ 反调试技术演示")
    print("=" * 40)
    
    # 时间检测
    print("1. 时间检测:")
    start = time.perf_counter()
    
    # 执行一些计算
    result = sum(i * i for i in range(10000))
    
    end = time.perf_counter()
    elapsed = end - start
    
    print(f"   计算耗时: {elapsed:.6f}秒")
    if elapsed > 0.1:
        print("   ⚠️ 检测到异常执行时间（可能在调试器中）")
    else:
        print("   ✅ 执行时间正常")
    
    # 进程检测模拟
    print("\n2. 进程检测:")
    suspicious_processes = [
        'ollydbg.exe', 'x64dbg.exe', 'ida.exe', 'cheatengine.exe'
    ]
    
    print("   检查可疑进程...")
    for proc in suspicious_processes:
        print(f"   - {proc}: 未发现")
    print("   ✅ 未检测到可疑进程")
    
    # 内存检测模拟
    print("\n3. 内存保护:")
    test_value = 0x12345678
    print(f"   写入测试值: 0x{test_value:08X}")
    print(f"   读取测试值: 0x{test_value:08X}")
    print("   ✅ 内存完整性检查通过")
    
    print()

def demo_session_management():
    """演示会话管理"""
    print("🎫 会话管理演示")
    print("=" * 40)
    
    # 生成会话令牌
    user_id = "VIP-555555"
    login_time = time.time()
    random_data = str(random.randint(100000, 999999))
    
    session_data = f"{user_id}:{login_time}:{random_data}"
    session_token = hashlib.sha256(session_data.encode()).hexdigest()
    
    print(f"用户ID: {user_id}")
    print(f"登录时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(login_time))}")
    print(f"随机数据: {random_data}")
    print(f"会话令牌: {session_token[:32]}...")
    
    # 验证会话
    print("\n会话验证:")
    current_time = time.time()
    session_age = current_time - login_time
    
    if session_age < 3600:  # 1小时有效期
        print(f"✅ 会话有效 (已使用 {session_age:.1f} 秒)")
    else:
        print("❌ 会话已过期")
    
    print()

def main():
    """主演示函数"""
    print("🔐 安全卡密验证程序 - 功能演示")
    print("=" * 50)
    print()
    
    demos = [
        ("加密功能", demo_encryption),
        ("XOR加密", demo_xor_encryption),
        ("时间攻击防护", demo_timing_attack),
        ("代码混淆", demo_obfuscation),
        ("反调试技术", demo_anti_debug),
        ("会话管理", demo_session_management)
    ]
    
    for i, (name, func) in enumerate(demos, 1):
        print(f"{i}. {name}")
        func()
        
        if i < len(demos):
            input("按回车键继续下一个演示...")
            print()
    
    print("🎉 演示完成！")
    print()
    print("总结:")
    print("- 多重加密保护卡密安全")
    print("- 时间攻击防护避免暴力破解")
    print("- 代码混淆增加逆向难度")
    print("- 反调试技术防止动态分析")
    print("- 会话管理确保访问安全")
    print()
    print("卡密: 555555")
    print("现在可以运行实际程序进行测试！")

if __name__ == "__main__":
    main()
