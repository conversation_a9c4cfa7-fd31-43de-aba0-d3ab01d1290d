"""
植物大战僵尸内存工具测试脚本
用于测试各个模块的基本功能
"""

import sys
import time
import traceback
from typing import List, Dict, Any

def test_imports():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入...")
    
    try:
        from memory_engine import MemoryEngine
        print("✓ memory_engine 导入成功")
    except Exception as e:
        print(f"✗ memory_engine 导入失败: {e}")
        return False
    
    try:
        from game_definitions import PVZDefinitions, GameVersion
        print("✓ game_definitions 导入成功")
    except Exception as e:
        print(f"✗ game_definitions 导入失败: {e}")
        return False
    
    try:
        from cheat_functions import CheatManager
        print("✓ cheat_functions 导入成功")
    except Exception as e:
        print(f"✗ cheat_functions 导入失败: {e}")
        return False
    
    try:
        from utils import logger, config_manager, DataFormatter
        print("✓ utils 导入成功")
    except Exception as e:
        print(f"✗ utils 导入失败: {e}")
        return False
    
    try:
        import tkinter as tk
        print("✓ tkinter 导入成功")
    except Exception as e:
        print(f"✗ tkinter 导入失败: {e}")
        return False
    
    try:
        import psutil
        print("✓ psutil 导入成功")
    except Exception as e:
        print(f"✗ psutil 导入失败: {e}")
        return False
    
    print("所有模块导入成功！")
    return True

def test_memory_engine():
    """测试内存引擎"""
    print("\n" + "=" * 50)
    print("测试内存引擎...")
    
    try:
        from memory_engine import MemoryEngine
        
        engine = MemoryEngine()
        print("✓ MemoryEngine 实例创建成功")
        
        # 测试进程查找（不会真正附加）
        result = engine.find_process("nonexistent.exe")
        if not result:
            print("✓ 进程查找功能正常（未找到不存在的进程）")
        
        # 测试地址格式化
        from utils import DataFormatter
        addr_str = DataFormatter.format_address(0x12345678)
        if addr_str == "0x12345678":
            print("✓ 地址格式化功能正常")
        
        print("内存引擎基础功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 内存引擎测试失败: {e}")
        traceback.print_exc()
        return False

def test_game_definitions():
    """测试游戏定义"""
    print("\n" + "=" * 50)
    print("测试游戏定义...")
    
    try:
        from game_definitions import PVZDefinitions, GameVersion
        
        game_def = PVZDefinitions()
        print("✓ PVZDefinitions 实例创建成功")
        
        # 测试地址获取
        sun_addr = game_def.get_address_by_name("sun_count")
        if sun_addr and sun_addr.name == "sun_count":
            print("✓ 地址获取功能正常")
        
        # 测试植物类型名称
        plant_name = game_def.get_plant_type_name(0)
        if plant_name == "豌豆射手":
            print("✓ 植物类型名称获取正常")
        
        # 测试僵尸类型名称
        zombie_name = game_def.get_zombie_type_name(0)
        if zombie_name == "普通僵尸":
            print("✓ 僵尸类型名称获取正常")
        
        # 测试分类获取
        categories = game_def.get_all_categories()
        if isinstance(categories, list) and len(categories) > 0:
            print(f"✓ 分类获取正常，共 {len(categories)} 个分类")
        
        print("游戏定义功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 游戏定义测试失败: {e}")
        traceback.print_exc()
        return False

def test_utils():
    """测试工具函数"""
    print("\n" + "=" * 50)
    print("测试工具函数...")
    
    try:
        from utils import logger, config_manager, DataFormatter, ValidationHelper
        
        # 测试日志
        logger.info("这是一条测试日志")
        print("✓ 日志功能正常")
        
        # 测试配置管理
        test_value = config_manager.get("window.width", 800)
        if isinstance(test_value, int):
            print("✓ 配置管理功能正常")
        
        # 测试数据格式化
        formatted_addr = DataFormatter.format_address(0xABCDEF)
        formatted_num = DataFormatter.format_number(12345)
        formatted_time = DataFormatter.format_time(125.5)
        
        if all([formatted_addr, formatted_num, formatted_time]):
            print("✓ 数据格式化功能正常")
        
        # 测试验证功能
        if ValidationHelper.is_valid_address(0x400000):
            print("✓ 地址验证功能正常")
        
        if ValidationHelper.is_valid_process_name("test.exe"):
            print("✓ 进程名验证功能正常")
        
        print("工具函数测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 工具函数测试失败: {e}")
        traceback.print_exc()
        return False

def test_config_file():
    """测试配置文件"""
    print("\n" + "=" * 50)
    print("测试配置文件...")
    
    try:
        import os
        import json
        
        # 检查配置文件是否存在
        if os.path.exists("config.json"):
            print("✓ 配置文件存在")
            
            # 尝试加载配置文件
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)
            
            # 检查必要的配置项
            required_keys = ["window", "game", "cheats", "ui"]
            for key in required_keys:
                if key in config:
                    print(f"✓ 配置项 '{key}' 存在")
                else:
                    print(f"✗ 配置项 '{key}' 缺失")
                    return False
            
            print("配置文件测试通过！")
            return True
        else:
            print("✗ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 配置文件测试失败: {e}")
        traceback.print_exc()
        return False

def test_gui_creation():
    """测试GUI创建（不显示窗口）"""
    print("\n" + "=" * 50)
    print("测试GUI创建...")
    
    try:
        import tkinter as tk
        from memory_tool import MemoryToolGUI
        
        # 创建一个隐藏的根窗口进行测试
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 尝试创建GUI实例（但不运行主循环）
        # 这里我们只测试类的导入和基本初始化
        print("✓ GUI类导入成功")
        
        root.destroy()
        print("✓ GUI基础测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        traceback.print_exc()
        return False

def test_process_detection():
    """测试进程检测功能"""
    print("\n" + "=" * 50)
    print("测试进程检测...")
    
    try:
        import psutil
        
        # 获取当前运行的进程列表
        processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                processes.append(proc.info['name'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        print(f"✓ 检测到 {len(processes)} 个运行中的进程")
        
        # 检查是否能找到常见的系统进程
        common_processes = ['explorer.exe', 'winlogon.exe', 'csrss.exe']
        found_count = 0
        for proc_name in common_processes:
            if proc_name in processes:
                found_count += 1
        
        if found_count > 0:
            print(f"✓ 成功检测到 {found_count} 个系统进程")
        
        print("进程检测功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 进程检测测试失败: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("植物大战僵尸内存工具 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("内存引擎", test_memory_engine),
        ("游戏定义", test_game_definitions),
        ("工具函数", test_utils),
        ("配置文件", test_config_file),
        ("GUI创建", test_gui_creation),
        ("进程检测", test_process_detection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n❌ {test_name} 测试出现异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具可以正常使用。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
        return False

if __name__ == "__main__":
    try:
        success = run_all_tests()
        
        print("\n" + "=" * 50)
        if success:
            print("测试结果: ✅ 工具功能正常")
            print("\n可以运行以下命令启动工具:")
            print("python memory_tool.py")
            print("或双击 start_memory_tool.bat")
        else:
            print("测试结果: ❌ 发现问题")
            print("\n请检查错误信息并修复相关问题")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生未预期的错误: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
