"""
工具函数模块
包含各种辅助功能和实用工具
"""

import json
import os
import time
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime

class Logger:
    """简单的日志记录器"""
    
    def __init__(self, log_file: str = "memory_tool.log"):
        self.log_file = log_file
        self.lock = threading.Lock()
    
    def log(self, level: str, message: str):
        """记录日志"""
        with self.lock:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] [{level}] {message}\n"
            
            try:
                with open(self.log_file, "a", encoding="utf-8") as f:
                    f.write(log_entry)
            except Exception as e:
                print(f"日志记录失败: {e}")
    
    def info(self, message: str):
        """记录信息日志"""
        self.log("INFO", message)
        print(f"[INFO] {message}")
    
    def warning(self, message: str):
        """记录警告日志"""
        self.log("WARNING", message)
        print(f"[WARNING] {message}")
    
    def error(self, message: str):
        """记录错误日志"""
        self.log("ERROR", message)
        print(f"[ERROR] {message}")
    
    def debug(self, message: str):
        """记录调试日志"""
        self.log("DEBUG", message)
        print(f"[DEBUG] {message}")

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "window": {
                "width": 800,
                "height": 600,
                "title": "植物大战僵尸内存工具"
            },
            "game": {
                "process_name": "PlantsVsZombies.exe",
                "auto_attach": True,
                "scan_interval": 100
            },
            "cheats": {
                "default_sun_amount": 9990,
                "default_money_amount": 99999,
                "auto_enable": []
            },
            "ui": {
                "theme": "default",
                "font_size": 10,
                "show_tooltips": True
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    loaded_config = json.load(f)
                    # 合并默认配置和加载的配置
                    self._merge_config(default_config, loaded_config)
                    return default_config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        return default_config
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key_path: str, default=None):
        """获取配置值"""
        keys = key_path.split(".")
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any):
        """设置配置值"""
        keys = key_path.split(".")
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def _merge_config(self, default: Dict, loaded: Dict):
        """合并配置"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value

class DataFormatter:
    """数据格式化工具"""
    
    @staticmethod
    def format_address(address: int) -> str:
        """格式化内存地址"""
        return f"0x{address:08X}"
    
    @staticmethod
    def format_bytes(data: bytes, max_length: int = 16) -> str:
        """格式化字节数据"""
        if len(data) > max_length:
            data = data[:max_length]
        
        hex_str = " ".join(f"{b:02X}" for b in data)
        ascii_str = "".join(chr(b) if 32 <= b <= 126 else "." for b in data)
        
        return f"{hex_str} | {ascii_str}"
    
    @staticmethod
    def format_number(value: Any) -> str:
        """格式化数字"""
        if isinstance(value, int):
            return f"{value:,}"
        elif isinstance(value, float):
            return f"{value:.2f}"
        else:
            return str(value)
    
    @staticmethod
    def format_time(seconds: float) -> str:
        """格式化时间"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = seconds % 60
            return f"{minutes}分{secs:.1f}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}小时{minutes}分"

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.operation_times = {}
        self.operation_counts = {}
    
    def start_operation(self, operation_name: str):
        """开始操作计时"""
        self.operation_times[operation_name] = time.time()
    
    def end_operation(self, operation_name: str):
        """结束操作计时"""
        if operation_name in self.operation_times:
            elapsed = time.time() - self.operation_times[operation_name]
            
            if operation_name not in self.operation_counts:
                self.operation_counts[operation_name] = {"total_time": 0, "count": 0}
            
            self.operation_counts[operation_name]["total_time"] += elapsed
            self.operation_counts[operation_name]["count"] += 1
            
            del self.operation_times[operation_name]
            return elapsed
        return 0
    
    def get_average_time(self, operation_name: str) -> float:
        """获取操作平均时间"""
        if operation_name in self.operation_counts:
            stats = self.operation_counts[operation_name]
            if stats["count"] > 0:
                return stats["total_time"] / stats["count"]
        return 0
    
    def get_statistics(self) -> Dict[str, Dict[str, float]]:
        """获取性能统计"""
        stats = {}
        for op_name, op_stats in self.operation_counts.items():
            stats[op_name] = {
                "total_time": op_stats["total_time"],
                "count": op_stats["count"],
                "average_time": self.get_average_time(op_name)
            }
        return stats
    
    def reset(self):
        """重置统计"""
        self.start_time = time.time()
        self.operation_times.clear()
        self.operation_counts.clear()

class ValidationHelper:
    """验证辅助工具"""
    
    @staticmethod
    def is_valid_address(address: int) -> bool:
        """验证内存地址是否有效"""
        # 基本的地址范围检查
        return 0x400000 <= address <= 0x7FFFFFFF
    
    @staticmethod
    def is_valid_process_name(name: str) -> bool:
        """验证进程名是否有效"""
        if not name or not isinstance(name, str):
            return False
        return name.endswith(".exe") and len(name) > 4
    
    @staticmethod
    def is_valid_number_range(value: Any, min_val: float = None, max_val: float = None) -> bool:
        """验证数字是否在指定范围内"""
        try:
            num_value = float(value)
            if min_val is not None and num_value < min_val:
                return False
            if max_val is not None and num_value > max_val:
                return False
            return True
        except (ValueError, TypeError):
            return False

class ThreadSafeDict:
    """线程安全的字典"""
    
    def __init__(self):
        self._dict = {}
        self._lock = threading.RLock()
    
    def get(self, key, default=None):
        """获取值"""
        with self._lock:
            return self._dict.get(key, default)
    
    def set(self, key, value):
        """设置值"""
        with self._lock:
            self._dict[key] = value
    
    def delete(self, key):
        """删除键"""
        with self._lock:
            if key in self._dict:
                del self._dict[key]
    
    def keys(self):
        """获取所有键"""
        with self._lock:
            return list(self._dict.keys())
    
    def values(self):
        """获取所有值"""
        with self._lock:
            return list(self._dict.values())
    
    def items(self):
        """获取所有键值对"""
        with self._lock:
            return list(self._dict.items())
    
    def clear(self):
        """清空字典"""
        with self._lock:
            self._dict.clear()
    
    def copy(self):
        """复制字典"""
        with self._lock:
            return self._dict.copy()

# 全局实例
logger = Logger()
config_manager = ConfigManager()
performance_monitor = PerformanceMonitor()

def format_exception(e: Exception) -> str:
    """格式化异常信息"""
    import traceback
    return f"{type(e).__name__}: {str(e)}\n{traceback.format_exc()}"

def safe_execute(func, *args, **kwargs):
    """安全执行函数"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"执行函数 {func.__name__} 时发生错误: {format_exception(e)}")
        return None
