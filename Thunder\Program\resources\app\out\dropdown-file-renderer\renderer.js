module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1002)}({0:function(e,t,n){"use strict";function r(e,t,n,r,o,i,s,a){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),i&&(c._scopeId="data-v-"+i),s?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},c._ssrRegister=l):o&&(l=a?function(){o.call(this,this.$root.$options.shadowRoot)}:o),l)if(c.functional){c._injectStyles=l;var d=c.render;c.render=function(e,t){return l.call(t),d(e,t)}}else{var u=c.beforeCreate;c.beforeCreate=u?[].concat(u,l):[l]}return{exports:e,options:c}}n.d(t,"a",function(){return r})},1:function(e,t,n){e.exports=n(9)(137)},1002:function(e,t,n){n(49),e.exports=n(1003)},1003:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(38);let o=String(Math.random()).replace(/\D/,"");n(4).client.start({name:`dropdownFileRendererContext${o}`}),r.CommonIPCRenderer.rendererCommunicator.initialize("dropdownFileRendererContext"),r.CommonIPCRenderer.rendererCommunicator.connect();const i=n(52),s=n(31);n(61);const a=n(1004),l=n(62);n(65);const c=n(1).default.getLogger("dropdown-file-renderer");i.PerformanceMonitorStatNS.init("dropdown-file-renderer"),new s.default({components:{App:a.default},render:e=>e("app")}).$mount("#app"),l.ThunderToolsNS.enableDevTools().catch(e=>{c.warning(e)})},1004:function(e,t,n){"use strict";n.r(t);var r=n(573),o=n(491);for(var i in o)"default"!==i&&function(e){n.d(t,e,function(){return o[e]})}(i);n(157),n(234),n(185),n(1005);var s=n(0),a=Object(s.a)(o.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\dropdown-file-renderer\\app.vue",t.default=a.exports},1005:function(e,t,n){"use strict";var r=n(1303);n.n(r).a},1078:function(e,t){},11:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(2),i=n(8),s=n(1).default.getLogger("XLStat");let a=i.default(o.join(__rootDir,"../bin/ThunderHelper.node"));function l(e=""){let t;if("string"==typeof e)t=e;else if(c(e)&&"object"==typeof e){let n=[];for(let t in e)c(e[t])&&n.push(t+"="+encodeURIComponent(e[t]));t=n.join(",")}return t}function c(e){return void 0!==e&&null!==e}!function(e){let t=null;function n(){return t||(t=a.xlstat4),t}function o(e,t="",o="",i=0,a=0,c=0,d=0,u="",f=0){return r(this,void 0,void 0,function*(){let r=0;do{if(void 0===e){r=1;break}let h=l(u);r="browser"===process.type?yield new Promise(s=>{r=n().asyncTrackEvent(e,t,o,i,a,c,d,h,f,e=>{s(e)})}):n().trackEvent(e,t,o,i,a,c,d,h,f),s.information(e,t,o,i,a,c,d,h,f)}while(0);return r})}function i(e,t=0){do{if(void 0===e)break;"browser"!==process.type&&n().trackClick(e,t)}while(0)}e.asyncTrackEvent=o,e.trackEvent=function(e,t="",n="",r=0,i=0,s=0,a=0,l="",c=0){o(e,t,n,r,i,s,a,l,c).catch()},e.trackEventEx=function(e,t="",n="",r=0){o(e,t,"",0,0,0,0,n,r).catch()},e.trackClick=i,e.trackShow=function(e,t=0){i(e,t)},e.setUserID=function(e=0,t=0){"browser"!==process.type&&n().setUserID(e,t)},e.initParam=function(e){return r(this,void 0,void 0,function*(){let t=-1;return t="browser"===process.type?yield new Promise(t=>{n().asyncInitParam(e,(e,n)=>{t(e?n:-1)})}):yield new Promise(t=>{n().initParamRemote(e,e=>{t(e)})})})},e.asyncUninit=function(e){return r(this,void 0,void 0,function*(){"browser"===process.type&&(yield new Promise(t=>{n().asyncUninit(e,()=>{t()})}))})},e.uninit=function(){"browser"===process.type&&n().waitFinish()}}(t.XLStatNS||(t.XLStatNS={}))},1161:function(e,t){},1163:function(e,t){},12:function(e,t){e.exports=require("events")},13:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const r=n(2);let o,i=console;function s(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,o=(new Error).stack.split("\n").slice(e+1);o.shift();let i=[];return o.forEach((e,o)=>{let s=t.exec(e)||n.exec(e),a={};s&&5===s.length&&(a.method=s[1],a.path=s[2],a.line=s[3],a.pos=s[4],a.file=r.basename(a.path),i.push(a))}),i}if(o="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return s(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){i.time(...e)},t.timeEnd=function(...e){i.timeEnd(...e)},t.trace=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),i.trace(o,...e)},t.info=function(...e){let t=s(),n="anonymous";t[0]&&t[0].method&&(n=n),i.info(o,"["+n+"]",e.join(","))},t.warn=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),i.warn("<WARN>"+o,"["+n+"]",e.join(","))},t.error=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),i.error("<ERROR>"+o,"["+n+"]",e.join(","))},t.log=function(...e){i.log(o,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},1303:function(e,t){},135:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(4),i=n(7),s=n(1).default.getLogger("path-selector"),a="PathAndCategory",l="historyDownloadPaths",c=8;function d(e){return r(this,void 0,void 0,function*(){s.information("SetConfigValue",e),yield o.client.callServerFunction("SetConfigValue",a,l,e),yield o.client.callServerFunction("SaveConfig")})}!function(e){function t(){return r(this,void 0,void 0,function*(){return yield o.client.callServerFunction("GetConfigValue",a,l,[])})}e.getMaxHistoryPathsLen=function(){return c},e.getHistoryPaths=t,e.addHistoryPaths=function(e){return r(this,void 0,void 0,function*(){let n=yield t();do{if(void 0===e||null===e||""===e)break;if("\\"===e[e.length-1]&&(e=e.slice(0,e.length-1)),n.includes(e))break;n.length>=c&&n.splice(0,1),n.push(e),d(n).catch()}while(0)})},e.deleteHistoryPath=function(e){return r(this,void 0,void 0,function*(){let n=yield t();do{if(void 0===e||null===e||""===e)break;"\\"===e[e.length-1]&&(e=e.slice(0,e.length-1));let t=n.indexOf(e);if(-1===t)break;n.splice(t,1),d(n).catch()}while(0)})},e.clearHistoryPaths=function(){return r(this,void 0,void 0,function*(){d([]).catch()})},e.getLogicHistoryPaths=function(e=!0,n=!0,o){return r(this,void 0,void 0,function*(){let r=[],s=yield i.asyncRemoteCall.getApp(),a=yield s.getPath("desktop"),l=yield s.getPath("documents"),c=[a,l];r.push({dir:a,alias:"桌面",canDelete:!1},{dir:l,alias:"我的文档",canDelete:!1});let d=yield t();if(void 0!==d){let e=d.indexOf(a);-1!==e&&d.splice(e,1);let t=d.indexOf(l);if(-1!==t&&d.splice(t,1),d.length>0)for(let e of d)"\\"!==e[e.length-1]&&(e+="\\"),-1===c.indexOf(e)&&(c.push(e),r.push({dir:e,alias:e,canDelete:n}))}if(e){let e="私人空间";c.includes(e)||r.push({dir:e,alias:e,canDelete:!1})}if(o)for(let e=o.length-1;e>=0;e--){let t=o[e];"\\"!==t[t.length-1]&&(t+="\\");let i=c.indexOf(t);-1!==i?r.unshift(r.splice(i,1)[0]):r.unshift({dir:t,alias:t,canDelete:n})}return r})},e.getCloudHistoryPath=function(){}}(t.HistoryPathsNS||(t.HistoryPathsNS={}))},14:function(e,t){e.exports=require("os")},157:function(e,t,n){"use strict";var r=n(1078);n.n(r).a},18:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(21),i=n(2),s=n(6),a=n(60),l=s.promisify,c=n(1).default.getLogger("Thunder.base.fs-utilities");!function(e){function t(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(o.access);try{yield n(e),t=!0}catch(e){c.information(e)}}return t})}function s(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(o.mkdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function d(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(o.rmdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function u(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(o.unlink);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function f(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(o.readdir);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function h(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(o.lstat);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function _(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let r=i.join(e,t),o=yield h(r);n=null!==o&&o.isDirectory()?yield p(r):yield u(r)}return n})}function p(e){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e){if(yield t(e)){n=!0;let t=(yield f(e))||[];for(let r=0;r<t.length;r++)n=(yield _(e,t[r]))&&n;(n||0===t.length)&&(n=(yield d(e))&&n)}}return n})}function m(e){return r(this,void 0,void 0,function*(){let n=!1;return c.information("mkdirsAW",e),void 0!==e&&((yield t(e))?n=!0:i.dirname(e)===e?n=!1:(yield m(i.dirname(e)))&&(n=yield s(e))),n})}function g(e,n){return r(this,void 0,void 0,function*(){let r;if(e.toLowerCase()!==n.toLowerCase()&&(yield t(e))){let t=o.createReadStream(e),i=o.createWriteStream(n);r=new Promise(e=>{t.pipe(i).on("finish",()=>{e(!0)})})}else r=new Promise(e=>{e(!1)});return r})}e.readFileAW=function(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(o.readFile);try{t=yield n(e)}catch(e){c.warning(e)}}return t})},e.readLineAw=function(e){return r(this,void 0,void 0,function*(){let n=null;do{if(!e)break;if(!t(e))break;n=yield new Promise(t=>{let n=[];const r=o.createReadStream(e),i=a.createInterface({input:r});i.on("line",e=>{n.push(e)}),i.on("close",()=>{t(n)})})}while(0);return n})},e.writeFileAW=function(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const r=l(o.writeFile);try{yield r(e,t),n=!0}catch(e){c.warning(e)}}return n})},e.existsAW=t,e.dirExistsAW=function(e){return r(this,void 0,void 0,function*(){let n=!1;do{if(!(n=yield t(e)))break;let r=yield h(e);if(!r)break;n=r.isDirectory()}while(0);return n})},e.mkdirAW=s,e.rmdirAW=d,e.unlinkAW=u,e.readdirAW=f,e.lstatAW=h,e.rmdirsAW=p,e.mkdirsAW=m,e.renameAW=function(e,t){return r(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=l(o.rename);try{yield n(e,t)}catch(e){c.warning(e)}}})},e.copyFileAW=g,e.copyDirsAW=function e(n,o){return r(this,void 0,void 0,function*(){let r=!1,s=yield h(n);if(s.isDirectory()){r=yield m(o);let a=(yield f(n))||[];for(let l=0;l<a.length;l++){let c=i.join(n,a[l]),d=i.join(o,a[l]);(r=yield t(c))&&(r=(s=yield h(c)).isDirectory()?yield e(c,d):yield g(c,d))}}return r})},e.mkdtempAW=function(){return r(this,void 0,void 0,function*(){let e=!1;const t=l(o.mkdtemp),r=(yield Promise.resolve().then(()=>n(14))).tmpdir();try{e=yield t(`${r}${i.sep}`)}catch(e){c.warning(e)}return e})},e.deleteEmptySubDirs=function(e,n){return r(this,void 0,void 0,function*(){let r=!0;e=i.normalize(e),n=i.normalize(n),e.length>3&&"\\"===e[e.length-1]&&(e=e.slice(0,e.length-1)),n.length>3&&"\\"===n[n.length-1]&&(n=n.slice(0,n.length-1));do{if(0!==e.indexOf(n)){r=!1;break}let o=e;for(;o!==n;){if((yield t(o))&&!(yield d(o))){r=!1;break}o=i.dirname(o)}}while(0);return r})},e.getFileSize=function e(n){return r(this,void 0,void 0,function*(){let r=0;do{if(!n)break;if(!(yield t(n)))break;let o=yield h(n);if(o)if(o.isDirectory()){let t=yield f(n);for(let o=0;o<t.length;o++)r+=(yield e(i.join(n,t[o])))}else r=o.size}while(0);return r})},e.isDirectoryEmptyAW=function(e,n=!0){return r(this,void 0,void 0,function*(){let r=!0;do{if(!e){r=!1;break}if(!(yield t(e))){r=n;break}let o=yield h(e);if(!o){r=!1;break}if(!o.isDirectory()){r=!1;break}if((yield f(e)).length>0){r=!1;break}}while(0);return r})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},185:function(e,t,n){"use strict";var r=n(1163);n.n(r).a},2:function(e,t){e.exports=require("path")},20:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.channelRMNewTaskReadyForSetTaskData="RM_NEWTASK_READYRORSETTASKDATA",e.channelRMNewTaskSetTaskData="RM_NEWTASK_SETTASKDATA",e.channelRMPreNewTaskSetTaskData="RM_PRENEWTASK_SETTASKDATA",e.channelRMNewTaskCreateNewTask="RM_NEWTASK_CREATENEWTASK",e.channelRMNewTaskClose="RM_NEWTASK_CLOSE",e.channelRMPreNewTaskClose="RM_PRENEWTASK_CLOSE",e.channelRMNewTaskSetBTInfo="RM_NEWTASK_SETBTINFO",e.channelRMNewTaskDownloadTorrent="RM_NEW_TASK_DOWNLOAD_TORRENT",e.channelRMNewTaskCreateBtTask="RM_NEWTASK_CRATEBTTASK",e.channelRMNewTaskCancleMagnet="RM_NEWTASK_CANCLE_MAGNET",e.channelRMImportTorrent="RM_NEWTASK_IMPORT_TORRENT",e.channelRMGetConfigValueResolve="RM_GET_CONFIG_VALUE_RESOLVE",e.channelRMGetConfigValueReject="RM_GET_CONFIG_VALUE_REJECT",e.channelRMSetConfigValueReject="RM_SET_CONFIG_VALUE_REJECT",e.channelMRTrayMenuClick="MR_TRAY_MENU_CLICK",e.channelMRNewTaskMagnetTaskCreated="MR_NEW_TASK_MAGNET_TASK_CREATED",e.channelMRNewTaskDownloadTorrentResult="MR_NEW_TASK_DOWNLOAD_TORRENT_RESULT",e.channelMRNewTaskCreateNewTaskResult="MR_NEWTASK_CREATENEWTASK_RESULT",e.channelMRNewTaskCreateBtTaskResult="RM_NEWTASK_CRATEBTTASK_RESULT",e.channelMRGetConfigValue="MR_GET_CONFIG_VALUE",e.channelMRSetConfigValue="MR_SET_CONFIG_VALUE",e.channelRMCommitPlanTask="RM_PLANTASK_COMMIT",e.channelRMPerformePlanTask="RM_PLANTASK_PERFORME",e.channelRMProcessSend="RM_RENDER_PROCESS_INFO",e.channelRMGetPrivateSpaceInfo="RM_RENDER_GET_PRIVATE_SPACE_INFO",e.channelMRGetPrivateSpaceInfoResult="MR_RENDER_GET_PRIVATE_SPACE_INFO_RESULT",e.channelRMFileCopy="RM_FILE_COPY",e.channelRMFileMove="RM_FILE_MOVE",e.channelMRFileCopyResult="MR_FILE_COPY_RESULT",e.channelMRFileMoveResult="MR_FILE_MOVE_RESULT",e.channelRMGetSutitleByCid="RM_RENDER_GET_SUBTITLE_BY_CID",e.channelMRGetSutitleByCidResult="MR_RENDER_GET_SUBTITLE_BY_CID_RESULT",e.channelRMGetSutitleByName="RM_RENDER_GET_SUBTITLE_BY_NAME",e.channelMRGetSutitleByNameResult="MR_RENDER_GET_SUBTITLE_BY_NAME_RESULT",e.channelRMDownloadSutitle="RM_RENDER_DOWNLOAD_SUBTITLE",e.channelMRDownloadSutitleSuc="MR_RENDER_DOWNLOAD_SUBTITLE_SUCCESS",e.channelMRDownloadSutitleFail="MR_RENDER_DOWNLOAD_SUBTITLE_FAIL",e.channelRMGetDisplayName="RM_RENDER_GET_SUBTITLE_DISPLAYNAME",e.channelMRGetDisplayNameResult="MR_RENDER_GET_SUBTITLE_DISPLAYNAME_RESULT",e.channelMRBringWindowToTop="MR_RENDER_BRING_WINDOW_TO_TOP",e.channelRMDownloadXmp="RM_RENDER_DOWNLOAD_XMP",e.channelRMXmpFixBoxCreated="RM_RENDER_XMPFIXBOX_CREATED",e.channelMRFixXmpSuc="MR_RENDER_FIX_XMP_SUC",e.channelMRFixXMPFail="MR_RENDER_FIX_XMP_FAIL",e.channelRMDownloadXmpEx="RM_RENDER_DOWNLOAD_XMP_EX",e.channelRMSetPosition="RM_SET_POSITION",e.channelRMSetFoucs="RM_SET_FOCUS",e.channelRMCreateAddressDropWnd="RM_CREATE_ADDRESS_DROPWND",e.channelRMRefreshAddressDropWnd="RM_REFRESH_ADDRESS_DROPWND",e.channelRMSelectAddressDropItem="RM_SELECT_ADDRESS_DROPITEM",e.channelRMCreateSearchWindow="RM_CREATE_SEARCH_WINDOW",e.channelRMShowSearchWindow="RM_SHOW_SEARCH_WINDOW",e.channelRMAddressKeyDown="RM_ADDRESS_BAR_KEYDOWN",e.channelMRFWAddressKeyDown="MR_ADDRESS_FW_BAR_KEYDOWN",e.channelMRFWSelectAddressDropItem="MR_FW_SELECT_ADDRESS_DROPITEM",e.channelRMAddressDropWndKeyDown="RM_ADDRESS_DROPWND_KEYDOWN",e.channelRMClickMouse="RM_CLICK_MOUSE",e.channelMRSearchBarFocusChange="MR_SEARCHBAR_FOCUS_CHANGE",e.channelMRFWAddressDropWndKeyDown="MR_FW_ADDRESS_DROPWND_KEYDOWN",e.channelMRClickAddressDropItem="MR_CLICK_ADDRESS_DROPITEM",e.channelMRMainWndMax="MR_MAINWINDOW_MAX",e.channelMRMainWndMin="MR_MAINWINDOW_MIN",e.channelMRMainWndRestore="MR_MAINWINDOW_RESTORE",e.channelRMGetBrowserStartType="RM_GET_BROWSER_START_TYPE",e.channelMRGetBrowserStartTypeResult="MR_GET_BROWSER_START_TYPE_RESULT",e.channelRMExecute="RM_SHELL_EXECUTE",e.channelMRExecuteResult="MR_SHELL_EXECUTE_RESULT",e.channelMRAdTipsClick="MR_AD_TIPS_CLICK",e.channelMRNotificationMsg="MR_NOTIFICATION_MSG",e.channelRMSetProgressBar="RM_SET_PROGRESS_BAR",e.channelRMRoundWindow="RM_ROUND_WINDOW",e.channelMRShowOrHideWindow="MR_SHOW_OR_HIDE_WINDOW",e.channelMRSuspensionWindowShowOrHide="MR_SUSPENSION_WINDOW_SHOW_OR_HIDE",e.channelRMConfigInitFinished="RM_CONFIG_INIT_FINISHED",e.channelRMConfigValueChanged="RM_CONFIG_VALUE_CHANGED",e.channelRMIndividuationBrowserMsg="RM_INDIVIDUATION_BROWSER_MSG",e.channelMRIndividuationBrowserMsg="MR_INDIVIDUATION_BROWSER_MSG",e.channelRMSetEnvironmentVariable="RM_SET_ENVIRONMENT_VARIABLE",e.channelMREmbedPlayerPos="MR_EMBED_PLAYER_POSITION",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},21:function(e,t){e.exports=require("fs")},22:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=function(...e){},t.error=function(...e){},t.warning=function(...e){},t.critical=function(...e){},t.verbose=function(...e){},"development"===process.env.LOGGER_ENV&&(t.information=function(...e){console.log("information",e)},t.error=function(...e){console.log("error",e)},t.warning=function(...e){console.log("warning",e)},t.critical=function(...e){console.log("critical",e)},t.verbose=function(...e){console.log("verbose",e)})},23:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.msgIPCCommunicatorForward="ipc_communicator_forward",e.msgIPCSendToMain="ipc_send_to_main",e.msgIPCSendToRenderer="ipc_send_to_renderer",e.msgIPCRendererConnect="ipc_renderer_connect",e.msgIPCRendererDisconnect="ipc_renderer_disconnect",e.msgNCCallNativeFunction="nc_call_native_function",e.msgNCCheckNativeFunction="nc_check_native_function",e.msgNCCallJsFunctionById="nc_call_js_function_by_id",e.msgNCCallJsFunctionByName="nc_call_js_function_by_name",e.msgNCNativeFireEvent="nc_native_fire_event",e.msgNCNativeCallReady="nc_native_call_ready"}(t.CommonIPCMessage||(t.CommonIPCMessage={}))},234:function(e,t,n){"use strict";var r=n(1161);n.n(r).a},28:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),o=n(6),i=n(22),s=n(23);!function(e){e.mainProcessContext="main-process",e.mainRendererContext="main-renderer",e.mainPageWebviewRendererContext="main-page-webview-renderer",e.newTaskRendererContext="new-task-renderer",e.preNewTaskRendererContext="pre-new-task-renderer",e.loginRendererContext="login-renderer";class t{constructor(){this.isConnected=!1,this.isOnIPCEvent=!1,this.rendererInfos=[],this.listeners=new Map,t.intervalIPCModuleMsgs=[s.CommonIPCMessage.msgIPCRendererConnect,s.CommonIPCMessage.msgIPCRendererDisconnect]}onMessage(e,t){do{if(!o.isString(e)||0===e.length){i.error("msgName is null");break}if(o.isNullOrUndefined(t)){i.error("listener is null");break}this.listeners.has(e)?this.listeners.get(e).push(t):this.listeners.set(e,[t])}while(0)}getCommunicatorInfo(){return this.currInfo}getAllRenderer(){return this.rendererInfos}getCommunicatorInfoById(e){for(let t of this.rendererInfos)if(t.id===e)return t;return null}getCommunicatorInfoByContext(e){for(let t of this.rendererInfos)if(t.context===e)return t;return null}startListenIPCMessage(e){this.isOnIPCEvent||(this.isOnIPCEvent=!0,e&&this.ListenSendToMainMsg(),this.ListenSendToRendererMsg(e))}ListenSendToMainMsg(){r.ipcMain.on(s.CommonIPCMessage.msgIPCSendToMain,(e,t)=>{let n=void 0;do{if(o.isNullOrUndefined(t)){i.error("msgInfo is empty");break}if(!this.isConnected){i.warning("hasnot been connected yet");break}let r=t.msg.name;if(this.isIPCModuleIntervalMsg(r)){i.information(`IPC module interval msg : ${r}`);let o=this.handleIPCModuleIntervalMsg(e.sender,t);if(n=o[1],!o[0])break;i.information("need to dispatch msg:"+r)}o.isNullOrUndefined(n)?n=this.NotifyListener(t):this.NotifyListener(t)}while(0);o.isNullOrUndefined(n)||(e.returnValue=n),t=null})}ListenSendToRendererMsg(e){(e?r.ipcMain:r.ipcRenderer).on(s.CommonIPCMessage.msgIPCSendToRenderer,(t,n)=>{let r=void 0;do{if(o.isNullOrUndefined(n)){i.error("msgInfo is empty");break}if(!this.isConnected){i.warning("hasnot been connected yet");break}let s=n.msg.name;if(this.isIPCModuleIntervalMsg(s)){i.information(`IPC module interval msg : ${s}`);let e=this.handleIPCModuleIntervalMsg(t.sender,n);if(r=e[1],!e[0])break;i.information("need to dispatch msg:"+s)}e?(i.information("is main, handle forward msg"),this.handleForwardRendererToRendererMsg(n)):(i.information("is renderer, handle business msg"),o.isNullOrUndefined(r)?r=this.NotifyListener(n):this.NotifyListener(n))}while(0);o.isNullOrUndefined(r)||(t.returnValue=r),n=null})}isIPCModuleIntervalMsg(e){for(let n of t.intervalIPCModuleMsgs)if(e===n)return!0;return!1}handleIPCModuleIntervalMsg(e,t){let n=[!1,void 0];do{let r=t.msg.name;if(r===s.CommonIPCMessage.msgIPCRendererConnect){n=[!0,this.handleRendererConnectMsg(e,t)];break}if(r===s.CommonIPCMessage.msgIPCRendererDisconnect){n=[!0,this.handleRendererDisconnectMsg(e,t)];break}}while(0);return n}handleRendererConnectMsg(e,t){i.verbose(e),i.verbose(t)}handleRendererDisconnectMsg(e,t){i.verbose(e),i.verbose(t)}handleForwardRendererToRendererMsg(e){this.sendForwardRendererToRendererMsg(e)}sendForwardRendererToRendererMsg(e){i.verbose(e)}NotifyListener(e){let t=void 0,n=e.msg.name;if(this.listeners.has(n)){let r=this.listeners.get(n),o=!0;for(let n of r)o?(o=!1,t=n(e)):n(e)}return t}}e.Communicator=t}(t.CommonIPCBase||(t.CommonIPCBase={}))},29:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(14),o=n(2);t.getDefaultPrex=function(){return o.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=o.basename(process.execPath,".exe"));let i=o.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(i="\\\\.\\pipe\\"+(i=(i=i.replace(/^\//,"")).replace(/\//g,"-"))),i},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},3:function(e,t){e.exports=require("electron")},30:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},31:function(e,t,n){e.exports=n(9)(45)},32:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),o=n(8).default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(){let e=!0;{0;let t=r.resolve("C:\\ETW_LOG\\log.ini");e="1"===o.readINI(t,"Log","enable")}return e}e.isDevToolsEnable=function(){return t()},e.isLogEnable=t,e.getLogOutput=function(){let e=process.env.TL_OUTPUT;do{if(e&&""!==e)break;let t=r.resolve("C:\\ETW_LOG\\log.ini");e=o.readINI(t,"Log","output")}while(0);return e}}(t.DevEnvHelperNS||(t.DevEnvHelperNS={}))},33:function(e,t){e.exports=require("net")},35:function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(r||(r={})),t.default=r},36:function(e,t,n){"use strict";var r;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(r||(r={})),e.exports=r},38:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),o=n(6),i=n(22),s=n(23),a=n(28);!function(e){class t extends a.CommonIPCBase.Communicator{constructor(){super()}initialize(e){this.currInfo={id:void 0,context:e,isMainCommunicator:!1}}connect(){this.isConnected?i.warning("has been connected"):(this.sendConnectMsgToMain(),this.isConnected=!0,this.startListenIPCMessage(!1))}disconnect(){this.isConnected?(this.isConnected=!1,this.sendDisconnectMsgToMain()):i.warning("hasnot been connected yet")}sendMessageToMain(e){this.sendIPCMsgToMain(e)}sendMessageToMainSync(e){return this.sendIPCMsgToMain(e,!0)}sendMessageToRenderer(e,t){this.sendIPCMsgToRenderer(e,t)}handleRendererConnectMsg(e,t){do{if(o.isNullOrUndefined(t)){i.error("msgInfo is null");break}let e=t.msg.args[0];if(o.isNullOrUndefined(e)){i.error("connectRendererInfo is null");break}i.information(`Renderer: new renderer will connect, id = ${e.id}, context = ${e.context}`),this.rendererInfos.push(e)}while(0)}handleRendererDisconnectMsg(e,t){do{if(o.isNullOrUndefined(t)){i.error("msgInfo is null");break}let e=t.msg.args[0];if(o.isNullOrUndefined(e)){i.error("disconnectRendererInfo is null");break}i.information(`renderer will disconnect, id = ${e.id}, context = ${e.context}`);for(let t=0;t<this.rendererInfos.length;++t)if(this.rendererInfos[t]===e){this.rendererInfos.splice(t,1);break}}while(0)}sendConnectMsgToMain(){let e=this.sendMessageToMainSync({name:s.CommonIPCMessage.msgIPCRendererConnect,args:[]});this.currInfo.id=e[0],this.rendererInfos=e[1]}sendDisconnectMsgToMain(){this.sendMessageToMain({name:s.CommonIPCMessage.msgIPCRendererDisconnect,args:[]})}sendIPCMsgToMain(e,t=!1){let n=void 0;do{if(o.isNullOrUndefined(e)){i.error("msg is null");break}n=(t?r.ipcRenderer.sendSync:r.ipcRenderer.send)(s.CommonIPCMessage.msgIPCSendToMain,{msg:e,senderInfo:this.currInfo})}while(0);return n}sendIPCMsgToRenderer(e,t){do{if(o.isNullOrUndefined(e)){i.error("rendererId is null");break}if(o.isNullOrUndefined(t)){i.error("msg is null");break}let n=[e].concat(t.args);t.args=n,r.ipcRenderer.send(s.CommonIPCMessage.msgIPCSendToRenderer,{msg:t,senderInfo:this.currInfo})}while(0)}}e.RendererCommunicator=t,e.rendererCommunicator=new t}(t.CommonIPCRenderer||(t.CommonIPCRenderer={}))},39:function(e,t){e.exports=require("crypto")},4:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(12),i=n(50),s=n(29),a=n(30);function l(e){a.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let c=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends o.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,r){do{if(t||(t=s.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(r)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))r("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{r("error",e)}),e.on("connect",()=>{r("connect")}),e.on("end",()=>{let t=e.isInprocess();r("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===s.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let o=new i.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=o,n||(this.client=o),o.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(o,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(o,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(o,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(o,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===o&&this.emit("stat_call_function_back",o.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let r=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,r));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),o.on("error",e=>{r&&r("error",e),this.emit("socket-error",e,o.getContext(),n,o.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),o.isInprocess()?(this.connectedMap[t.toLowerCase()]=o,r&&r("connect"),this.emit("connect",o.getContext(),n,!0)):o.on("connect",()=>{this.connectedMap[t.toLowerCase()]=o,r&&r("connect"),this.emit("connect",o.getContext(),n,!1)}),o.on("end",()=>{let e=o.isInprocess();a.information("server is ended, and this client emit end",t,n,e),r&&r("end",o.getContext(),n,e),this.emit("end",o.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(o)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return r(this,void 0,void 0,function*(){let r=null,o=yield this.crossCallServerFunctionEx(e,t,...n);return o&&(r=o[0]),r})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(r,t,...n)}}crossIsRemoteClientExist(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(r,t,n)}})}crossCallRemoteClientFunction(e,t,n,...r){{if(!e)throw new Error("An argument for 'productId' was not provided");let o=this.singletonMap[e.toLowerCase()];if(!o)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(o,t,n,...r)}}crossAttachServerEvent(e,t,n){let r=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let o=this.singletonMap[e.toLowerCase()];if(!o)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");r=this.internalAttachServerEvent(o,t,n)}return r}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(r,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(r,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.generateId();const o={alias:t,action:"register",rid:r};let i=(e,r)=>{e?(a.error("register error",e.message),n(r)):n(t)};this.retCallbackMap[r]=Object.assign({callback:i},o),this.sendAdapter(e,o)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(a.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===s.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,r)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let r=this.generateId();const o={action:"check_server_function_exist",method:t,rid:r};let i=(e,t)=>{n(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:i},o),this.sendAdapter(e,o)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((r,o)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let o=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const i={rid:o,method:t,args:n};let s=(t,n)=>{t?(a.error("callServerFunction error",t,e.getContext()),r([null,t])):r([n,void 0])};this.retCallbackMap[o]=Object.assign({callback:s},i),this.sendAdapter(e,i)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,r)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let r=this.generateId();const o={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:r};let i=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[r]=Object.assign({callback:i},o),this.sendAdapter(e,o)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((r,o)=>{do{if(!e){r(!1);break}if(!t){r(!1);break}if(!n){r(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){r(!(!this.apis||!this.apis[n]));break}let o=this.generateId();const i={action:"check_client_function_exist",method:n,rid:o,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let s=(e,t)=>{r(!e&&t)};this.retCallbackMap[o]=Object.assign({callback:s},i),this.sendAdapter(e,i)}while(0)})}internalCallRemoteClientFunction(e,t,n,...r){return new Promise((o,i)=>{do{if(!e){o([null,"client doesn't ready"]);break}if(!t){o([null,"remote client alias is not specifed"]);break}if(!n){o([null,"funcName is not specifed"]);break}let i=(e,t)=>{e?(a.information("callRemoteClientFunction",e.message),o([null,e])):o([t,void 0])};if(r)for(let e=0;e<r.length;e++)r[e]=this.convertFunction2IdEx(r[e]);let s=this.generateId();const l={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:r,rid:s};this.retCallbackMap[s]=Object.assign({callback:i},l),this.sendAdapter(e,l)}while(0)})}internalAttachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(r)||(this.eventCallbackMaps[r]={}),this.eventCallbackMaps[r].hasOwnProperty(t)||(this.eventCallbackMaps[r][t]={}),s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let o=this.generateId();return this.eventCallbackMaps[r][t][o]=n,o}internalDetachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;delete this.eventCallbackMaps[r][t][n],s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;let e=this.eventCallbackMaps[r][t];for(let t in e){let r=e[t];r&&r.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...r){let o=void 0,i=!1;do{const s=this.contextCallbackMap[t];if(!s){a.error("the context function has been freeer",t),o={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let l=void 0,c=void 0;try{l=s.apply(null,...r)}catch(e){c=e.message;break}if(void 0===n||null===n)break;if(o={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==c){o.error=c;break}if(l&&l.then){l.then(t=>{o.data=this.convertFunction2IdEx(t),o.success=!0,this.sendAdapter(e,o)}).catch(t=>{o.error=t instanceof Error?t.message:t,this.sendAdapter(e,o)}),i=!0;break}o.success=!0,o.data=this.convertFunction2IdEx(l)}while(0);!i&&o&&this.sendAdapter(e,o)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":r}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let r=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(r){n=((...e)=>{this.callRemoteContextById(t,r,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:r},l);break}for(let n in e){let r=e[n];e[n]=this.decodeParameter(r,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,r=!1;do{if(!t)break;let o=t.method;if(!o)break;let i=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(i):[].concat(i)},!this.apis||!this.apis[o]){n.error=`callRegisterFunction ${o} is undefined`;break}let s=void 0,a=this.decodeParameter(t.args,t.src);try{s=this.apis[o].apply(null,[t.src].concat(a))}catch(e){n.error=e.message;break}if(s&&s.then){s.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),r=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(s)}while(0);a.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(e,n)}}),c=global.__xdasIPCClienInstance,t.client=c},41:function(e,t){e.exports=require("buffer")},42:function(e,t,n){"use strict";const r=n(13);if("renderer"===process.type){if(r.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(53);e.require=t.remoteRequire,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,r)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,r)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(r.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(57);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},47:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(51),o=n(12);t.Parser=class extends o.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),o=JSON.parse(e);this.emit("message",o),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},49:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),o=n(6),i=n(1),s=n(8),a=n(32),l=n(3),c=n(20),d=s.default(r.join(__rootDir,"../bin/ThunderHelper.node"));function u(){"console"===process.env.TL_OUTPUT?i.default.outputLogger=i.outputLoggerConsole:i.default.outputLogger=function(){function e(e){return function(...t){d.printEtwLog(e,function(...e){return e.map(e=>o.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[i.LogLevel.Critical]:e(i.LogLevel.Critical),[i.LogLevel.Error]:e(i.LogLevel.Error),[i.LogLevel.Warning]:e(i.LogLevel.Warning),[i.LogLevel.Information]:e(i.LogLevel.Information),[i.LogLevel.Verbose]:e(i.LogLevel.Verbose)}}()}function f(){let e=a.DevEnvHelperNS.isLogEnable();"1"===process.env.TL_ENABLE!==e&&(process.env.TL_ENABLE=e?"1":"0",i.default.enableLogger=e,d.enableETWLogger(e));let t=a.DevEnvHelperNS.getLogOutput();t&&t!==process.env.TL_OUTPUT&&(process.env.TL_OUTPUT=t,u())}process.env.TL_ENABLE="0",i.default.enableLogger="1"===process.env.TL_ENABLE,u(),f(),"browser"===process.type?l.ipcMain.on(c.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{l.BrowserWindow.getAllWindows().forEach(e=>{e.isDestroyed()||e.webContents.send(c.ThunderChannelList.channelMRUpdateLogEnviroment)}),f()}):"renderer"===process.type&&l.ipcRenderer.on(c.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{f()})},491:function(e,t,n){"use strict";n.r(t);var r=n(492),o=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);t.default=o.a},492:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,s=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),s=n(7),a=n(5),l=n(1),c=n(11),d=n(135),u=n(4);n(64).FixTextScale.fixZoomFactory();const f=l.default.getLogger("path-selector");let h=class extends a.Vue{constructor(){super(...arguments),this.items=[],this.panel="",this.from="",this.enableDelete=!0}adjustHeight(e){this.$nextTick(()=>o(this,void 0,void 0,function*(){let t=49+30*e,n=document.querySelector(".td-dropdown-menu");n&&(t=n.getBoundingClientRect().height);let r=yield s.asyncRemoteCall.getCurrentWindow();if(r){let e=yield r.getSize();window.resizeTo(e[0],t)}}))}get maxVisibleCount(){return this.items.length<=6?this.items.length:6}get disabled(){let e=!0;for(let t of this.items)if(t.canDelete){e=!1;break}return e}handleInputChange(e,t,n=!1){return o(this,void 0,void 0,function*(){let r=yield s.asyncRemoteCall.getCurrentWindow();i.ipcRenderer.send("dropdown-file-window-select",r.id,e,t,n,this.from)})}created(){return o(this,void 0,void 0,function*(){i.ipcRenderer.on("dropdown-file-window-created",(e,t)=>{this.panel=t}),i.ipcRenderer.on("dropdown-file-window-update-options",(e,t,n,r)=>{this.items=t,this.from=n,this.enableDelete=r})})}handleSelect(e){f.information("select",e),this.handleInputChange(e.dir,e.id,!0)}handleDelete(e){let t=`source=Thunder,panel=${this.panel},button=delete_one`;f.information("trackEvent","core_event","create_task_panel_click",t),c.XLStatNS.trackEvent("core_event","create_task_panel_click","",0,0,0,0,t),f.information("delete",e);for(let t=0;t<this.items.length;t++)if(this.items[t].alias===e.alias){this.items.splice(t,1),"logic"===this.from?d.HistoryPathsNS.deleteHistoryPath(e.dir):"cloud"===this.from&&u.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcDeleteRecentFolder",[e.id]);break}}handleClear(){return o(this,void 0,void 0,function*(){if(!this.disabled){let e=`source=Thunder,panel=${this.panel},button=delete_all`;f.information("trackEvent","core_event","create_task_panel_click",e),c.XLStatNS.trackEvent("core_event","create_task_panel_click","",0,0,0,0,e),f.information("clear"),"logic"===this.from?d.HistoryPathsNS.clearHistoryPaths():"cloud"===this.from&&u.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcClearRecentFolder"),this.items=this.items.filter(e=>!e.canDelete)}let e=yield s.asyncRemoteCall.getCurrentWindow();i.ipcRenderer.send("dropdown-file-window-click-hide",e.id)})}};r([a.Watch("maxVisibleCount",{immediate:!0})],h.prototype,"adjustHeight",null),h=r([a.Component],h),t.default=h},5:function(e,t,n){e.exports=n(9)(213)},50:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(33),o=n(12),i=n(30),s=n(47),a=n(29);t.Client=class extends o.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=a.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new s.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{i.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){i.error(e.message)}else i.information("This socket has been ended by the other party",this.context&&this.context.name)}}},51:function(e,t){e.exports=require("string_decoder")},52:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(14),i=(n(21),n(2)),s=n(6);let a=null;const l=n(11),c=n(3),d=n(1),u=n(8),f=n(20),h=n(7),_="xdas_profile_stat";let p="",m=void 0,g=null,R=void 0,v=null,b=u.default(i.join(__rootDir,"../bin/ThunderHelper.node")),w=new Set;function C(){return r(this,void 0,void 0,function*(){return new Promise(e=>r(this,void 0,void 0,function*(){void 0===R&&(null===v&&(v=new Promise(e=>{e(R=function(e){let t="";if(0===e.length&&"renderer"===process.type){let e=i.normalize(decodeURIComponent(window.location.href)),n=e.indexOf(process.resourcesPath);n=n>-1?n+process.resourcesPath.length+1:n;let r=e.length-1,o=e.indexOf("?"),s=e.indexOf("#");r=o>-1?Math.min(o-1,r):r,r=s>-1?Math.min(s-1,r):r,n>-1&&r>=n&&(t=e.substr(n,r-n+1))}return 0===t.length&&(t=0!==e.length?e:process.type),t=t.replace(/\||,|;/g,"_")}(""))})),R=yield v),e(R)}))})}function E(e){let t="";do{if(null===e||void 0===e)break;switch(typeof e){case"string":t=e;break;case"object":t=s.inspect(e)||"";break;case"number":case"boolean":t=e.toString()||""}}while(0);return t}function y(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function M(e){return r(this,void 0,void 0,function*(){return new Promise(t=>r(this,void 0,void 0,function*(){let r=void 0;null===a&&(a=yield Promise.resolve().then(()=>n(39)));let o=a.createHash("md5");null!==o&&(r=o.update(e).digest("hex")),t(r)}))})}function O(){return new Promise(e=>r(this,void 0,void 0,function*(){let t="";t=void 0===m?"browser"===process.type?function(){if(void 0===m){let e=process.argv.length,t=process.argv;for(let n=0;n<e;n++){let e=t[n];if(e.startsWith("-StartType:")){m=e.substring("-StartType:".length);break}}void 0===m&&(m="")}return m}():yield function(){return r(this,void 0,void 0,function*(){return null===g&&(g=new Promise(e=>{c.ipcRenderer.send(f.ThunderChannelList.channelRMGetBrowserStartType),c.ipcRenderer.once(f.ThunderChannelList.channelMRGetBrowserStartTypeResult,(t,n)=>{m=n,e(n),g=null})})),g})}():m,e(t)}))}function S(e,t,n,o){return r(this,void 0,void 0,function*(){let i=E(t),s=E(n),a=yield M(s),c=function(e){let t=new RegExp(y("file:///"),"g"),n=new RegExp(y(process.resourcesPath+"\\"),"g"),r=new RegExp(y(encodeURI(process.resourcesPath.replace(/\\/g,"/")+"/")),"g");return e.replace(t,"").replace(n,"").replace(r,"")}(E(o)),d=yield M(c),u=`${e}:${a}:${d}`;w.has(u)||(w.add(u),l.XLStatNS.trackEvent(_,"uncaught_exception","",0,0,0,0,`type=${e},business_name=${yield C()},code=${i},message_hash=${a},message=${encodeURI(s)},stack_hash=${d},stack=${encodeURI(c)}`)),function(e,t,n,o){return r(this,void 0,void 0,function*(){})}().catch()})}function I(e){console.error(e);let t=e||{};S("unhandledRejection",t.code,t instanceof Error?t.message:t,t.stack).catch()}!function(e){e.init=function(e){p=e},e.trackColdStartUpEvent=function(e){return r(this,void 0,void 0,function*(){let t=b.iSColdStartUp()?1:0,n=o.release(),r=b.performanceMonitorReporter.getProcessUptime(),i=yield O(),s=`key=${e},start_type=${i},cold_start_up=${t},os_version=${n},cost_time=${r}`;l.XLStatNS.trackEvent(_,"cold_start_up","",0,0,0,0,s)})}}(t.PerformanceMonitorStatNS||(t.PerformanceMonitorStatNS={})),function(){return r(this,void 0,void 0,function*(){if(process.on("uncaughtException",e=>{console.error(e);let t=e||{};S("uncaughtException",t.code,t.message,t.stack).catch()}),"browser"===process.type)process.on("unhandledRejection",(e,t)=>{I(e)}),c.ipcMain.on(f.ThunderChannelList.channelRMGetBrowserStartType,function(e){return r(this,void 0,void 0,function*(){let t=yield O();e.sender.send(f.ThunderChannelList.channelMRGetBrowserStartTypeResult,t)})});else if("browser"!==process.type){window.addEventListener("unhandledrejection",e=>{I(e&&e.reason||{})});let e=yield h.asyncRemoteCall.getCurrentWebContents();null!==e&&void 0!==e&&e.once("did-finish-load",()=>{(function(){return r(this,void 0,void 0,function*(){do{if("browser"===process.type)break;if(null===window.performance.timing||void 0===window.performance.timing)break;let e=b.iSColdStartUp()?1:0,t=o.release(),n=window.performance.timing,r=n.loadEventEnd-n.navigationStart,i=n.fetchStart-n.navigationStart,s=n.domainLookupEnd-n.domainLookupStart,a=n.connectEnd-n.connectStart,c=n.responseStart-n.requestStart,d=n.responseEnd-n.responseStart,u=n.domComplete-n.domLoading,f=yield O();l.XLStatNS.trackEvent(_,"page_load_time","",0,0,0,0,`start_type=${f},cold_start_up=${e},os_version=${t},load_time=${r},before_fetch_time=${i},domin_lookup_time=${s},connect_time=${a},first_response_time=${c},responseTime=${d},domTime=${u},process=${p}`)}while(0)})})().catch()})}d.default.hook("beforeLog",(e,t,...n)=>{e===d.LogLevel.Critical&&l.XLStatNS.trackEvent(_,"critical_error","",0,0,0,0,`module_name=${t},messages=${n}`)})})}().catch()},53:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const r=n(3),o=n(41),i=n(54),s=n(55),a=n(35),l=n(56),c=n(13),d=n(36),u=r.ipcRenderer,f=process.electronBinding("v8_util"),h=new i.default,_=f.createIDWeakMap(),p=f.getHiddenValue(global,"contextId");class m{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function g(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let r=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:g(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(r))return{type:"buffer",value:o.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(d.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(f.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:f.getHiddenValue(e,"__remote_id__")};let r={type:e instanceof m?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)r.members.push({name:t,value:n(e[t])});return t.delete(e),r}if("function"==typeof e){return{type:"function",id:h.add(e),location:f.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function R(e,t,n){d.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function v(e,t,n,r=!1){const o=t=>{if(e.hasOwnProperty(t.name)&&!r)return;let n,o={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(_.has(t.value.refId)&&(n=_.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=E(t.value,e,t.name);o.get=(()=>n),o.set=(e=>n=e)}else"get"===t.type&&(o.get=(()=>n),t.writable&&(o.set=(e=>{n=e})),n=E(t.value));Object.defineProperty(e,t.name,o)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)o(n[t])}}function b(e,t,n){if(n){let t=E(n);Object.setPrototypeOf(e,t)}}function w(e,t){f.setHiddenValue(e,"__remote_id__",t)}function C(e){return f.getHiddenValue(e,"__remote_id__")}function E(e,t,n){const r={value:()=>e.value,array:()=>e.members.map(e=>E(e)),buffer:()=>o.Buffer.from(e.value),promise:()=>Promise.resolve({then:E(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:r,value:o}=e.members[n];t[r]=o}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let i;return e.type in r?i=r[e.type]():e.refId?_.has(e.refId)?(f.addRemoteObjectRef(p,e.refId),i=_.get(e.refId)):(c.warn("[metaToValue] refId point to null"+e.refId),i="function"===e.type?()=>{}:{}):e.id?_.has(e.id)?(f.addRemoteObjectRef(p,e.id),v(i=_.get(e.id),e.id,e.members,!0),b(i,e.id,e.proto)):(i="function"===e.type?t?function(e,t,n){if(_.has(n.id))return _.get(n.id);let r=C(e),o=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(o,"name",{value:t,writable:!1,enumerable:!0});let i=new Proxy(o,{apply:(e,n,o)=>new Promise((e,i)=>{let c=C(n);if(c||(c=C(n.__remoteObj_)),!c)throw Error("is this function was a bound function?");let d=a.default.browser.memberCall,f=l.default(d),h=g(o);u.send(d,p,f,c,r,t,h),s.default.add(f,t=>{try{R(E(t),e,i)}catch(e){i(e)}})}),construct:(e,n,o)=>new Promise((e,o)=>{let i=a.default.browser.memberConstruct,c=l.default(i);u.send(i,p,c,r,t,g(n)),s.default.add(c,t=>{try{let n=E(t);e(n)}catch(e){o(e)}})})});return f.setHiddenValue(o,"__remote_id__",n.id),i}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return w(n,t),new Proxy(n,{apply:(e,n,r)=>new Promise((e,o)=>{let i=a.default.browser.functionCall,c=l.default(i),d=C(n);u.send(i,p,c,d,t,g(r)),s.default.add(c,t=>{try{R(E(t),e,o)}catch(e){o(e)}})}),construct:(e,n,r)=>new Promise((e,r)=>{let o=a.default.browser.construct,i=l.default(o);u.send(o,p,i,t,g(n)),s.default.add(i,t=>{try{let n=E(t);e(n)}catch(e){r(e)}})})})}(e):{},f.setRemoteObjectFreer(i,p,e.id),_.set(e.id,i),f.setHiddenValue(i,"__remote_id__",e.id),f.addRemoteObjectRef(p,e.id),function(e){let t=C(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,r){if("function"==typeof r)throw new Error("set a function to a remote member is dangerous");return new Promise((o,i)=>{let c=a.default.browser.memberSet,d=l.default(c),f=g([r]);u.send(c,p,d,t,n,f),s.default.add(d,t=>{try{let s=E(t);e[n]=r,o(s)}catch(e){i(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((r,o)=>{let i=a.default.browser.memberGet,c=l.default(i);u.send(i,p,c,t,n),s.default.add(c,t=>{try{const i=E(t);e[n]=i,r(i)}catch(e){o(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let r=a.default.browser.sync,o=l.default(r);u.send(r,p,o,t),s.default.add(o,r=>{try{if(r.id!==t)throw Error("SYNC_RETURN: remote id not match");let o=E(r);e(o)}catch(e){n(e)}})})}}})}(i),v(i,e.id,e.members),b(i,e.id,e.proto),Object.defineProperty(i.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=i[e],r=i.__proto__;for(;r;){if(Object.prototype.hasOwnProperty.call(r,e)){delete r[e];break}r=r.__proto__}Object.defineProperty(i,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):c.error("no id of meta:",e),i}t.Interest=m;class y{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,r)=>{let o=this.getChannel(e),i=l.default(o);u.send(o,p,i,t),s.default.add(i,e=>{try{let t=E(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){r(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=a.default.browser.require;break;case"builtin":t=a.default.browser.builtIn;break;case"global":t=a.default.browser.global;break;case"current_window":t=a.default.browser.currentWindow;break;case"current_web_contents":t=a.default.browser.currentWebContents;break;case"client_web_contents":t=a.default.browser.clientWebContents;break;case"web_contents":t=a.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function M(e,t,n){try{s.default.invoke(t,n).remove(t)}finally{s.default.remove(t)}}u.on(a.default.renderer.requireReturn,M),u.on(a.default.renderer.getBuiltInReturn,M),u.on(a.default.renderer.getGlobalReturn,M),u.on(a.default.renderer.currentWindowReturn,M),u.on(a.default.renderer.currentWebContentsReturn,M),u.on(a.default.renderer.functionCallReturn,M),u.on(a.default.renderer.constructReturn,M),u.on(a.default.renderer.memberCallReturn,M),u.on(a.default.renderer.memberSetReturn,M),u.on(a.default.renderer.memberGetReturn,M),u.on(a.default.renderer.memberConstructReturn,M),u.on(a.default.renderer.callback,(e,t,n)=>{h.apply(t,E(n))}),u.on(a.default.renderer.syncReturn,M),u.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{c.info("[RELEASE_CALLBACK]: callbackId:",t),h.remove(t)}),process.on("exit",()=>{u.send(a.default.browser.contextRelease)});const O=["__resolve","__isResolved"],S=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],I=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function T(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(I(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(I(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(I(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(I(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(I(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(I(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,r)=>(I(e),Reflect.defineProperty(e.__remoteObj_,n,r)),get:(t,n,r)=>{if("string"==typeof n){if(String.prototype.includes.call(S,n)){return e[n]}if(String.prototype.includes.call(O,n)){return e[n]}}return I(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,r,o)=>(I(e),Reflect.set(e.__remoteObj_,n,r,o)),ownKeys:t=>(I(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,r)=>{I(e),Reflect.apply(e.__remoteObj_,n,r)},construct:(t,n,r)=>{if(I(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,r)=>{let o=a.default.browser.construct,i=l.default(o),c=f.getHiddenValue(e.__remoteObj_,"__remote_id__");u.send(o,p,i,c,g(n)),s.default.add(i,e=>{try{t(E(e))}catch(e){r(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=C(e);t&&w(n,t)}}),n}t.remoteRequire=function(e){return T(new y("module",e))},t.getBuiltin=function(e){return T(new y("builtin",e))},t.getGlobal=function(e){return T(new y("global",e))},t.getCurrentWindow=function(){return T(new y("current_window"))},t.getCurrentWebContents=function(){return T(new y("current_web_contents"))},t.getWebContents=function(){return T(new y("web_contents"))}},54:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=r.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,o=(new Error).stack;let i,s=n.exec(o);for(;null!==s;){const e=s[1];if(!e.includes("native")&&!e.includes("electron.asar")){i=/([^/^)]*)\)?$/gi.exec(e)[1];break}s=n.exec(o)}return this.callbacks[t]=e,r.setHiddenValue(e,"__remote_callback_id__",t),r.setHiddenValue(e,"__remote_call_location__",i),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(r.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}},55:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13);var o;!function(e){let t={};e.add=function(e,n,r){t[e]={func:n,thisArg:r}},e.invoke=function(n,...o){let i=t[n];return i?i.thisArg?i.func.apply(i.thisArg,...o):i.func(...o):r.error(`Cannot invoke function by unrecognize id. ${n}`),e},e.remove=function(e){delete t[e]}}(o||(o={})),t.default=o},56:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=0;t.default=function(e){return e?e.concat(".").concat(String(++r)):String(++r)}},57:function(e,t,n){"use strict";const r=n(3),o=n(58),i=n(35),s=n(59),a=n(13),l=n(36),c=r.ipcMain,d=process.electronBinding("v8_util");let u=d.createDoubleIDWeakMap();const f=new o.default;function h(e,t,n,r,o,i){let s,l=!1,c=null,d=!1;do{try{s=t[r]}catch(e){l=!0}if(l)try{s=n.value[r],l=!1,n.meta.extendedMemberNames.push(r),d=!0}catch(e){a.error(`property ${r} untouchable, even try root[name]`)}if(l)break;let o=Object.getOwnPropertyDescriptor(t,r);if(void 0===o){a.warn(`descriptor of property ${r} is undefined`);break}c={name:r,enumerable:o.enumerable,writable:!1,type:"get"},void 0===o.get&&"function"==typeof s?c.type="method":((o.set||o.writable)&&(c.writable=!0),c.type="get"),d?(c.configurable=!0,c.value=m(e,s,i,!1,null)):c.value=m(e,s,i,!1,n)}while(0);return c}function _(e,t,n,r=null){let o=Object.getOwnPropertyNames(t);"function"==typeof t&&(o=o.filter(function(e){return!String.prototype.includes.call(s.propertiesOfFunction,e)}));let i=[];do{if(0===o.length)break;let s=o.length;for(let a=0;a<s;a++){let s=h(e,t,n,o[a],0,r);s&&i.push(s)}}while(0);return i}function p(e,t,n,r=null){let o=null,i=Object.getPrototypeOf(t);return o=null===i||i===Object.prototype||i===Function.prototype?null:m(e,i,r,!1,n)}function m(e,t,n=null,r=!1,o=null){n=null===n?{}:n;const i={type:typeof t};"object"===i.type&&(i.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":l.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&d.getHiddenValue(e,"simple")&&(n="value"),n}(t,r));do{if("object"===i.type||"function"===i.type){let r=f.getIdOfObject(t);if(r&&n[r]){i.refId=r,f.add(e,t);break}}"array"===i.type?i.members=t.map(t=>m(e,t,n)):"object"===i.type||"function"===i.type?(null==o&&(i.extendedMemberNames=[],o={value:t,meta:i}),i.name=t.constructor?t.constructor.name:"",i.id=f.add(e,t),n[i.id]=!0,i.members=_(e,t,o,n),i.proto=p(e,t,o,n)):"buffer"===i.type?i.value=Buffer.from(t):"promise"===i.type?(t.then(function(){},function(){}),i.then=m(e,function(e,n){t.then(e,n)})):"error"===i.type?(i.members=g(t),i.members.push({name:"name",value:t.name})):"date"===i.type?i.value=t.getTime():(i.type="value",i.value=t)}while(0);return i}function g(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function R(e,t,n,o){const s=function(o){let l,c,h=0,_=0;switch(o.type){case"value":return o.value;case"remote-object":return f.get(o.id);case"array":return R(e,t,n,o.value);case"buffer":return Buffer.from(o.value);case"date":return new Date(o.value);case"promise":return Promise.resolve({then:s(o.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:o.name}),h=0,_=(c=o.members).length;h<_;h++)e[(l=c[h]).name]=s(l.value);return e}case"function":{const s=e.id,l=[n,o.id];if(a.info("renderer function id:"+l),u.has(l))return u.get(l);let c=function(...t){a.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||s!==e.id?function(e,t,n){let o="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof r.webContents.constructor){const{sender:e}=t[0],r=e.eventNames().filter(t=>{let r=e.listeners(t),o=!1;return r.forEach(e=>{e===n&&(o=!0)}),o});r.length>0&&(o+=`\nRemote event names: ${r.join(", ")}`,r.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}a.warn(o)}(o,n,c):e.send(i.default.renderer.callback,o.id,m(e,n))};return Object.defineProperty(c,"length",{value:o.length}),d.setRemoteCallbackFreer(c,t,n,o.id,e),u.set(l,c),c}default:throw new TypeError(`Unknown type: ${o.type}`)}};return o.map(s)}function v(e,t,n,r){let o,i;try{return t.apply(n,r)}catch(e){return i=t.name,new Error(`Could not call remote function '${o=null!=i?i:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function b(e){return{type:"exception",message:e.message,stack:e.stack||e}}function w(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var C;!function(e){const t=(e,t,...n)=>{const r=e.sender;r.isDestroyed()?a.warn("webcontext is destroyed."):r.send(t,...n)};e.startServer=function(){c.on(i.default.browser.require,(e,n,r,o)=>{a.info(`[REQUIRE] module=${o} `);let s=process.mainModule.require(o),l=m(e.sender,s);t(e,i.default.renderer.requireReturn,r,l)}),c.on(i.default.browser.builtIn,(e,n,o,s)=>{a.info(`[BUILTIN]: property=${s} contextId=${n}`);let l=r[s],c=m(e.sender,l);a.info(`[BUILTIN]: returns remoteId:${c.id}, type: ${typeof l}`),t(e,i.default.renderer.getBuiltInReturn,o,c)}),c.on(i.default.browser.global,(e,n,r,o)=>{a.info(`[GLOBAL]: proerty:${o} contextId=${n}`);let s,l=global[o];s=m(e.sender,l),a.info(`[GLOBAL]: returns remoteid=${s.id}, obj=`+typeof l),t(e,i.default.renderer.getGlobalReturn,r,s)}),c.on(i.default.browser.currentWindow,(e,n,r,o)=>{a.info(`[CURRENT_WINDOW]: property=${o} contextId=${n}`);let s=e.sender.getOwnerBrowserWindow.call(e.sender),l=m(e.sender,s);a.info(`[CURRENT_WINDOW]: returns remoteid=${l.id}, obj=`+s),t(e,i.default.renderer.currentWindowReturn,r,l)}),c.on(i.default.browser.currentWebContents,(e,n,r,o)=>{t(e,i.default.renderer.currentWebContentsReturn,r,m(e.sender,e.sender))}),c.on(i.default.browser.webContents,(e,n,o,s)=>{a.info(`[WebContents]: proerty:${s} contextId=${n}`);let l,c=r.webContents;l=m(e.sender,c),a.info(`[WebContents]: returns remoteid=${l.id}, obj=`+typeof c),t(e,i.default.renderer.webContentsReturn,o,l)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(r=>{n?e.once(r,t[r]):e.on(r,t[r])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};c.on(i.default.browser.construct,(n,r,o,s,l)=>{let c,d=null;try{a.info(`[CONSTRUCTOR]: remoteId=${s} `);let u=l.length>0?l[l.length-1]:null;l=R(n.sender,n.frameId,r,l);let h=f.get(s);null==h&&w(`Cannot call constructor on missing remote object ${s}`),u&&"interest"===u.type&&(d=l.pop());let _=new(Function.prototype.bind.apply(h,[null,...l]));_&&d&&e(_,d),c=m(n.sender,_,null,!1),a.info(`[CONSTRUCTOR]: returns remoteId =${c.id} name=${h.name} `)}catch(e){c=b(e)}finally{t(n,i.default.renderer.constructReturn,o,c)}}),c.on(i.default.browser.functionCall,function(e,n,r,o,s,l){let c;try{a.info(`[FUNCTION_CALL]: remoteId=${s}`),l=R(e.sender,e.frameId,n,l);let d=f.get(s);if(null==d)a.error(`Cannot call function on missing remote object ${s}`),c=m(e.sender,void 0);else{let t=o?f.get(o):global;if(t){let n=v(0,d,t,l);c=m(e.sender,n)}else a.error(`Cannot call function(${s}) on missing context(${o})`),c=m(e.sender,void 0)}a.info(`[FUNCTION_CALL]: name=${d.name}`)}catch(e){c=b(e)}finally{t(e,i.default.renderer.functionCallReturn,r,c)}}),c.on(i.default.browser.memberCall,function(e,n,r,o,s,l,c){let d;a.info(`[MEMBER_CALL]: thisArg=${o}, remoteId=${s}, method=${l}, args count=${c.length}`);try{c=R(e.sender,e.frameId,n,c);let u=f.get(s);null==u&&w(`Cannot call function '${l}' on missing remote object ${s}`);let h=o?f.get(o):u;if(h){let t=v(0,u[l],h,c);d=m(e.sender,t),a.info("[MEMBER_CALL]: return="+t)}else d=m(e.sender,void 0)}catch(e){d=b(e)}finally{t(e,i.default.renderer.memberCallReturn,r,d)}}),c.on(i.default.browser.memberGet,function(e,n,r,o,s){let l;try{a.info(`[MEMBER_GET]: remoteId=${o}, property=`,s);let n=f.get(o);null==n&&w(`Cannot get property '${Object.toString.call(s)}' on missing remote object ${o}`);let c=n[s];l=m(e.sender,c)}catch(e){l=b(e)}finally{t(e,i.default.renderer.memberGetReturn,r,l)}}),c.on(i.default.browser.memberSet,function(e,n,r,o,s,l){try{a.info(`[MEMBER_SET]: remoteId=${o}, property=`+s),l=R(e.sender,e.frameId,n,l);let c=f.get(o);null==c&&w(`Cannot set property '${Object.toString.call(s)}' on missing remote object ${o}`),c[s]=l[0],t(e,i.default.renderer.memberSetReturn,r,{type:"value",value:!0})}catch(n){t(e,i.default.renderer.memberSetReturn,r,b(n))}}),c.on(i.default.browser.memberConstruct,function(n,r,o,s,l,c){let d,u=null;try{a.info(`[MEMBER_CONSTRUCTOR]: regId=${s}, method=${l}`);let h=c.length>0?c[c.length-1]:null;c=R(n.sender,n.frameId,r,c);let _=f.get(s);null==_&&w(`Cannot call constructor '${l}' on missing remote object ${s}`),h&&"interest"===h.type&&(u=c.pop());let p=_[l],g=new(Function.prototype.bind.apply(p,[null,...c]));g&&u&&e(g,u),d=m(n.sender,g)}catch(e){d=b(e)}finally{t(n,i.default.renderer.memberConstructReturn,o,d)}}),c.on(i.default.browser.sync,function(e,n,r,o){let s=f.get(o);t(e,i.default.renderer.syncReturn,r,m(e.sender,s))}),c.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=f.get(t);if(r.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let r=n.name;r||(r=n.constructor?n.constructor.name:""),f.remove(e.sender.id,t)}else t<0&&a.warn("remote id reference to nothing:",t)}),c.on(i.default.browser.contextRelease,e=>{f.clear(e.sender.id)})},e.getObjectRegistry=function(){return f}}(C||(C={})),e.exports=C},573:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"xly-drop-file td-dropdown-menu"},[n("ul",e._l(e.items,function(t){return n("li",{key:t.alias,staticClass:"td-dropdown-menu__item",on:{click:function(n){e.handleSelect(t)}}},[n("span",[e._v(e._s(t.alias))]),e._v(" "),t.canDelete?n("a",{staticClass:"xly-drop-file__close",attrs:{href:"javascript:;"},on:{click:function(n){n.stopPropagation(),e.handleDelete(t)}}},[n("i",{staticClass:"td-icon-close"})]):e._e()])})),e._v(" "),n("a",{directives:[{name:"show",rawName:"v-show",value:!1!==e.enableDelete,expression:"enableDelete !== false"}],staticClass:"xly-drop-file__link",class:{"is-disabled":e.disabled},attrs:{href:"javascript:;"},on:{click:e.handleClear}},[e._v("清除历史下载目录")])])},o=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o})},58:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13),o=-1*Math.pow(2,31),i=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),r=e.id;let o=this.owners.get(r);return o||(o=new Set,this.owners.set(r,o),this.registerDeleteListener(e,r)),o.has(n)||(o.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return i.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=i.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=o)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),i.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(i.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),o=(i,s)=>{s===n&&(r.info("render-view-deleted: processid="+n),(()=>{r.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",o),this.clear(t))};e.on("render-view-deleted",o)}}},59:function(e,t,n){"use strict";var r;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(r||(r={})),e.exports=r},6:function(e,t){e.exports=require("util")},60:function(e,t){e.exports=require("readline")},61:function(e,t,n){e.exports=n(9)(216)},62:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(7),i=n(18),s=n(4),a=n(32);!function(e){function t(e,t){return r(this,void 0,void 0,function*(){if(null!==e){let n=e.webContents;(yield n.isDevToolsOpened())?yield n.closeDevTools():yield n.openDevTools(t)}})}e.openDevTool=t,e.enableDevTools=function(e){return r(this,void 0,void 0,function*(){window.addEventListener("keyup",n=>r(this,void 0,void 0,function*(){if("F12"===n.key&&n.ctrlKey)a.DevEnvHelperNS.isLogEnable()&&(yield t(yield o.asyncRemoteCall.getCurrentWindow(),e));else if(("t"===n.key||"T"===n.key)&&n.altKey&&a.DevEnvHelperNS.isLogEnable()){let e=document.getElementById("DevProcessPid");if(e)document.body.removeChild(e);else{(e=document.createElement("p")).id="DevProcessPid",e.style.position="absolute",e.style.left="0px",e.style.top="0px",e.style.width="100%",e.style.zIndex="10000",e.style.color="rgb(255,0,0)",document.body.appendChild(e);let t="process.pid:"+process.pid;t+="\r\nlocation.href:"+location.href,t+="\r\nprocess.argv:"+process.argv,e.innerText=t}}}),!0)})},e.enableDragOpenFile=function(e){void 0===e&&(e=!1),document.addEventListener("dragover",e=>(e.preventDefault(),e.stopPropagation(),!1),!0),document.addEventListener("drop",e=>r(this,void 0,void 0,function*(){e.preventDefault(),e.stopPropagation();let t=e.dataTransfer,n=t.files,r=t.items;if(void 0!==r&&null!==r&&r.length>0)for(let e=0;e<r.length;e++){let t=r[e];"string"===t.kind&&"text/uri-list"===t.type?t.getAsString(e=>{s.client.callServerFunction("DropOpenUrl",e).catch()}):t.kind}if(void 0!==n&&null!==n&&n.length>0)for(let e=0;e<n.length;e++){let t=n[e].path;void 0!==t&&null!==t&&""!==t&&(yield i.FileSystemAWNS.existsAW(t))&&s.client.callServerFunction("DropOpenFile",t).catch()}return!1}),!0)}}(t.ThunderToolsNS||(t.ThunderToolsNS={}))},64:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.getTextScale=function(){return 1},e.fixWindowSize=function(e,t){return r(this,void 0,void 0,function*(){window.resizeTo(e,t)})},e.autoFixWindowSize=function(){},e.fixZoomFactory=function(){}}(t.FixTextScale||(t.FixTextScale={}))},65:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(4),o=n(1).default.getLogger("common/skin"),i=!0;let s=null;function a(e){if(!i||null===e||void 0===e)return;let t=localStorage.getItem("skin_body_classes");s&&s.classes===e.classes||(localStorage.removeItem("skin_body_classes"),e&&e.classes&&(document.body.classList.add(e.classes),localStorage.setItem("skin_body_classes",e.classes)),s&&s.classes?document.body.classList.remove(s.classes):t!==e.classes&&document.body.classList.remove(t),s=Object.freeze(Object.assign({},e)))}r.client.callServerFunction("GetSkinInfo").then(a).catch(e=>{o.warning(e)}),r.client.attachServerEvent("OnChangeSkin",(e,...t)=>{a(t[0])})},7:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(1).default.getLogger("async-remote-call"),i=n(42),s=n(12),a=n(6);t.asyncRemoteCall=new class extends s.EventEmitter{constructor(){super(),this.mapObj=new Map,this.mapObjIniting=new Map,"renderer"!==process.type&&o.warning('can not import "renderer-process-call" module in non-renderer process',process.type)}getAppName(){return r(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}getAppVersion(){return r(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return r(this,void 0,void 0,function*(){return i.global.process.__resolve()})}getIpcMain(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getBrowserWindow(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getWebContents(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("webContents")})}getGlobalShortcut(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield i.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emit("OnInitCurrentWebContents",e),this.listeners("OnInitCurrentWebContents").forEach(e=>{this.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield i.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emit("OnInitCurrentWindow",e),this.listeners("OnInitCurrentWindow").forEach(e=>{this.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return r(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return a.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>r(this,void 0,void 0,function*(){this.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield i.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emit(e,t),this.listeners(e).forEach(t=>{this.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}},8:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},9:function(e,t){e.exports=vendor_0aff229d1d3a2d2be355}});
//# sourceMappingURL=renderer.js.map