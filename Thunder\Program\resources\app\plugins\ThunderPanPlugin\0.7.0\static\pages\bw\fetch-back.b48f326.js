/*! For license information please see ../../LICENSES */
(window.webpackJsonp=window.webpackJsonp||[]).push([[11],{1329:function(t,e,o){"use strict";o.d(e,"a",(function(){return i}));var r=o(1332);function i(t){return Object(r.createDecorator)((function(e,o){if(!e.methods)throw new Error("This decorator must be used on a vue component method.");var r="number"==typeof t?t:t.time,i=e.methods[o],a=0,l=function(){a&&(clearTimeout(a),a=0)};e.methods[o]=function(){for(var t=this,e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];l(),a=setTimeout((function(){a=0,i.apply(t,e)}),r)}}))}},1332:function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,i=(r=o(5))&&"object"==typeof r&&"default"in r?r.default:r,a="undefined"!=typeof Reflect&&Reflect.defineMetadata;function l(t,e,o){(o?Reflect.getOwnMetadataKeys(e,o):Reflect.getOwnMetadataKeys(e)).forEach((function(r){var i=o?Reflect.getOwnMetadata(r,e,o):Reflect.getOwnMetadata(r,e);o?Reflect.defineMetadata(r,i,t,o):Reflect.defineMetadata(r,i,t)}))}var n={__proto__:[]}instanceof Array;var d=["data","beforeCreate","created","beforeMount","mounted","beforeDestroy","destroyed","beforeUpdate","updated","activated","deactivated","render","errorCaptured"];function s(t,e){void 0===e&&(e={}),e.name=e.name||t._componentTag||t.name;var o=t.prototype;Object.getOwnPropertyNames(o).forEach((function(t){if("constructor"!==t)if(d.indexOf(t)>-1)e[t]=o[t];else{var r=Object.getOwnPropertyDescriptor(o,t);void 0!==r.value?"function"==typeof r.value?(e.methods||(e.methods={}))[t]=r.value:(e.mixins||(e.mixins=[])).push({data:function(){var e;return(e={})[t]=r.value,e}}):(r.get||r.set)&&((e.computed||(e.computed={}))[t]={get:r.get,set:r.set})}})),(e.mixins||(e.mixins=[])).push({data:function(){return function(t,e){var o=e.prototype._init;e.prototype._init=function(){var e=this,o=Object.getOwnPropertyNames(t);if(t.$options.props)for(var r in t.$options.props)t.hasOwnProperty(r)||o.push(r);o.forEach((function(o){"_"!==o.charAt(0)&&Object.defineProperty(e,o,{get:function(){return t[o]},set:function(e){t[o]=e},configurable:!0})}))};var r=new e;e.prototype._init=o;var i={};return Object.keys(r).forEach((function(t){void 0!==r[t]&&(i[t]=r[t])})),i}(this,t)}});var r=t.__decorators__;r&&(r.forEach((function(t){return t(e)})),delete t.__decorators__);var s,c,p=Object.getPrototypeOf(t.prototype),x=p instanceof i?p.constructor:i,u=x.extend(e);return function(t,e,o){Object.getOwnPropertyNames(e).forEach((function(r){if("prototype"!==r){var i=Object.getOwnPropertyDescriptor(t,r);if(!i||i.configurable){var a,l,d=Object.getOwnPropertyDescriptor(e,r);if(!n){if("cid"===r)return;var s=Object.getOwnPropertyDescriptor(o,r);if(a=d.value,l=typeof a,null!=a&&("object"===l||"function"===l)&&s&&s.value===d.value)return}0,Object.defineProperty(t,r,d)}}}))}(u,t,x),a&&(l(s=u,c=t),Object.getOwnPropertyNames(c.prototype).forEach((function(t){l(s.prototype,c.prototype,t)})),Object.getOwnPropertyNames(c).forEach((function(t){l(s,c,t)}))),u}function c(t){return"function"==typeof t?s(t):function(e){return s(e,t)}}c.registerHooks=function(t){d.push.apply(d,t)},e.default=c,e.createDecorator=function(t){return function(e,o,r){var i="function"==typeof e?e:e.constructor;i.__decorators__||(i.__decorators__=[]),"number"!=typeof r&&(r=void 0),i.__decorators__.push((function(e){return t(e,o,r)}))}},e.mixins=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return i.extend({mixins:t})}},1356:function(t,e,o){var r=o(1420);"string"==typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);(0,o(90).default)("1244996a",r,!0,{sourceMap:!1})},1419:function(t,e,o){"use strict";var r=o(1356);o.n(r).a},1420:function(t,e,o){(e=o(89)(!1)).push([t.i,'body .xly-dialog-task{display:flex;flex-direction:column;flex-shrink:0;width:100%;height:100%}body .xly-dialog-task .td-dropdown-group .td-button:first-child{text-indent:36px}body .xly-dialog-task .td-dropdown-group .td-icon-arrow-drop:before{font-family:"xly-icon";content:"\\e7c7"}body .xly-dialog-task .td-tooltip-wrapper{display:block;width:100%}body .xly-dialog-task .xly-icon-arrow-right{margin-left:-5px;color:var(--color-secondary);cursor:pointer}body .xly-dialog-task .td-dialog__body,body .xly-dialog-task .xly-file-list{display:flex;flex-direction:column;flex:1;flex-basis:0}body .xly-dialog-task .xly-file-list{position:relative;border:1px solid var(--color-border);border-radius:4px}body .xly-dialog-task .xly-file-list .td-table{display:flex;flex-direction:column;margin:0;width:100%;height:100%}body .xly-dialog-task .xly-file-list .td-table__body-wrapper{flex:1;flex-basis:0;height:unset;max-height:unset}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar{width:0;background:transparent}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar:horizontal{height:0}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar-thumb{border-radius:2px;width:0;background:var(--color-scrollbar)}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar-thumb{border-right:0}body .xly-dialog-task .xly-file-list .td-icon-arrow-drop{position:relative;left:-2px}body .xly-dialog-task .xly-file-list td .td-table__text{color:var(--color-secondary);padding-left:5px}body .xly-dialog-task .xly-file-list .td-table tr th{height:23px}body .xly-dialog-task .xly-file-list .td-table tr th:first-child .td-table__text{padding-left:0}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text{position:relative;justify-content:center}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text .td-icon-sequence{position:absolute;top:4px;right:-2px}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text:after,body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text:before{position:absolute;top:7px;left:-5px;width:1px;height:10px;background:var(--color-border);content:""}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text:after{left:auto;right:-5px}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(3){padding-left:20px}body .xly-dialog-task .xly-file-list tr td:nth-child(2) .td-table__text{position:relative;display:block;padding-left:0;max-width:46px;height:28px;line-height:28px;text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}body .xly-dialog-task .xly-file-list .td-tree-node__content{height:28px;cursor:pointer}body .xly-dialog-task .xly-file-list .td-checkbox__inner,body .xly-dialog-task .xly-file-list .td-tree-node__content .td-tree-node__label{cursor:pointer}body .xly-dialog-task .td-dropdown-group .td-button--large:last-child:before{opacity:.2}body .xly-dialog-task .td-button--large{font-size:14px}body .xly-dialog-task .td-table__body-wrapper{height:128px}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar{width:6px;background:transparent}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar:horizontal{height:6px}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-task .td-select__drop:hover{color:var(--color-primary)}body .xly-dialog-task .td-select__choose{width:36px}body .xly-dialog-task .td-select__choose:before{display:none}body .xly-dialog-task .td-dialog__footer{margin-top:30px}body .xly-dialog-task .td-loading.is-stop .td-loading-bar__inner{animation:none}body .xly-dialog-task .td-loading-bar{height:4px}body .xly-dialog-task .xly-icon-resize{position:absolute;right:0;bottom:0;color:var(--color-primary-gray-2);cursor:nwse-resize}.xly-dialog-task__button{display:flex;align-items:center;width:100%;height:40px;background:var(--color-primary);border-radius:4px}.xly-dialog-task__button.is-disabled{background:var(--background-main)}.xly-dialog-task__button.is-disabled .xly-button-down,.xly-dialog-task__button.is-disabled .xly-button-wait{color:var(--color-disabled);background:var(--background-main);cursor:default}.xly-dialog-task__button .xly-line{margin:0;height:16px;opacity:.5}.xly-dialog-task__button .xly-button-down{flex:1;border-radius:4px 0 0 4px}.xly-dialog-task__button .xly-button-wait{width:40px;border-radius:0 4px 4px 0}body .xly-dialog-resource{position:relative;display:flex;height:32px;margin-bottom:20px}.xly-dialog-resource__name{display:flex;align-items:center;width:335px}.xly-dialog-resource__text{-webkit-line-clamp:2;display:-webkit-box;margin-left:12px;font-size:13px;word-break:break-all;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;white-space:normal;line-height:1.3}.xly-dialog-resource__text.is-fail{color:var(--color-danger)}.xly-dialog-resource__text.is-more{display:flex;width:100%;word-break:keep-all}.xly-dialog-resource__text.is-more span{margin-right:6px;max-width:250px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}.xly-dialog-resource__rename{display:inline-flex;align-items:center;margin:0 8px 0 6px;width:16px;height:17px;color:var(--color-primary-gray-2)}.xly-dialog-resource__rename:hover{color:var(--color-primary)}.xly-dialog-resource__size{position:absolute;right:0;display:flex;align-items:center;color:var(--color-secondary);line-height:30px}.xly-dialog-resource__size i{margin-left:10px;cursor:pointer}.xly-dialog-resource__size i:hover{color:var(--color-primary)}.xly-dialog-resource__operate{display:flex;align-items:center}body .xly-dialog-resource .td-select,body .xly-dialog-resource .td-textarea{margin-left:12px;flex:1}body .xly-dialog-resource .td-textarea__inner{height:44px}body .xly-dialog-resource .td-dropdown-menu{display:none}body .xly-dialog-resource .xly-down-setting{top:0}body .xly-dialog-tasks{border:1px solid var(--color-border)}.xly-dialog-tasks__list{height:70px;overflow-y:auto;padding:5px 4px 0;font-size:0}.xly-dialog-tasks__list::-webkit-scrollbar{width:6px;background:transparent}.xly-dialog-tasks__list::-webkit-scrollbar:horizontal{height:6px}.xly-dialog-tasks__list::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}.xly-dialog-tasks__list::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}.xly-dialog-tasks__list::-webkit-scrollbar-corner{background:transparent}.xly-dialog-tasks__size{height:30px;text-indent:10px;line-height:30px;background:var(--background-main);border-radius:0 0 4px 4px}body .xly-tag-tasks{display:inline-block;width:54px;height:20px;margin:6px 0 0 6px;font-size:12px;line-height:18px;color:var(--color-primary);text-align:center;background:var(--background-secondary);border:1px solid var(--color-primary);border-radius:2px;box-sizing:border-box;overflow:hidden}body .xly-tag-tasks.is-disabled{color:var(--color-secondary);background:var(--background-main);border-color:var(--background-main)}body .xly-dialog-link{position:relative;z-index:2}.xly-dialog-link__add{display:flex;align-items:center;padding-left:12px;height:30px;color:var(--color-primary);border:1px solid var(--color-border);border-radius:4px;cursor:pointer}.xly-dialog-link__add i{margin-right:8px}.xly-dialog-link__add:hover{border-color:var(--color-primary)}.xly-dialog-link__content{position:absolute;top:0;left:0;width:100%;height:0;background:var(--background-module);border-radius:4px;box-shadow:0 0 10px 0 var(--color-shadow);z-index:1;overflow:hidden;transition:height .4s}.xly-dialog-link__content .td-textarea__inner{padding:12px;height:127px;border:unset;white-space:nowrap}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar{width:8px;background:transparent}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar:horizontal{height:8px}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-thumb{width:6px;background:var(--color-scrollbar)}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-corner{background:transparent}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-thumb{border:0;border-radius:2px;cursor:default}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-track{background:var(--background-module)}.xly-dialog-link__content.is-show{height:164px}.xly-dialog-link__operate{display:flex;justify-content:space-between;align-items:center;padding:0 12px;height:36px;border-top:1px solid var(--color-border)}.xly-dialog-link__operate a{color:var(--color-secondary)}body .xly-down-setting{position:absolute;top:23px;left:-1px;width:calc(100% + 2px);height:250px;z-index:1;padding:12px 12px 0;background:var(--background-module);box-shadow:0 0 20px var(--color-shadow);box-sizing:border-box;border-radius:4px}.xly-down-setting__item{display:flex;justify-content:space-between;margin-bottom:8px}.xly-down-setting__item .td-input__inner{width:90px}.xly-down-setting__item--link:first-child .td-input__label{display:block;margin-bottom:6px}.xly-down-setting__item--link .td-input{width:100%}.xly-down-setting__item--link .td-input__inner{display:block;width:100%}.xly-down-setting__operate{display:flex;justify-content:space-between;align-items:center;height:36px}.xly-down-setting__operate a{font-size:14px;color:var(--color-primary)}body .xly-dialog-normal{position:relative;display:flex;flex-direction:column;margin-top:12px;flex:1;flex-basis:0;border:1px solid var(--color-border);border-radius:4px}.xly-dialog-normal__title{display:flex;line-height:23px;align-items:center;box-sizing:border-box;color:var(--color-secondary)}.xly-dialog-normal__title .xly-dialog-normal__name{text-indent:60px}.xly-dialog-normal__title .xly-dialog-normal__type:after,.xly-dialog-normal__title .xly-dialog-normal__type:before{position:absolute;top:7px;left:0;width:1px;height:10px;background:var(--color-border);content:""}.xly-dialog-normal__title .xly-dialog-normal__type:after{left:auto;right:0}.xly-dialog-normal__title .xly-dialog-normal__size{text-indent:20px}.xly-dialog-normal__name{display:flex;align-items:center;width:calc(100% - 149px)}.xly-dialog-normal__name .td-checkbox{overflow:hidden}.xly-dialog-normal__name .td-checkbox__label{display:none}.xly-dialog-normal__type{position:relative;width:50px;display:block;text-align:center;white-space:nowrap;overflow:hidden;color:var(--color-secondary)}.xly-dialog-normal__size{width:100px;position:relative;color:var(--color-secondary);text-indent:5px}.xly-dialog-normal__item{display:flex;height:32px;line-height:32px;padding-left:13px}.xly-dialog-normal__item .td-dropdown-menu__item{padding:0 0 0 8px}.xly-dialog-normal__choose{position:absolute;right:14px;color:var(--color-secondary)}.xly-dialog-normal__choose:hover{color:var(--color-primary)}.xly-dialog-normal__content{flex:1;flex-basis:0;overflow-y:hidden}.xly-dialog-normal__content::-webkit-scrollbar{width:6px;background:transparent}.xly-dialog-normal__content::-webkit-scrollbar:horizontal{height:6px}.xly-dialog-normal__content::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}.xly-dialog-normal__content::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}.xly-dialog-normal__content::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-normal .xly-dialog__down-unordered{overflow-y:auto;height:100%}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar{width:6px;background:transparent}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar:horizontal{height:6px}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-normal .xly-dialog-normal__label{max-width:calc(100% - 80px);margin-left:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}.xly-dialog-normal__group{position:relative;display:flex;align-items:center;height:40px;padding:0 8px 0 14px}.xly-dialog-normal__group .td-input{margin-left:7px}.xly-dialog-normal__group .td-input .td-input__inner{width:180px}.xly-dialog-normal__group .td-input .td-input__label{display:none}.xly-dialog-normal__group .xly-dialog-normal__label{max-width:calc(100% - 220px)!important}.xly-dialog-normal__text{position:absolute;right:14px;line-height:40px;color:var(--color-secondary)}body .xly-dialog-normal .xly-icon-type{margin-left:15px}.xly-dialog-normal__label{max-width:100%}body .xly-dialog-normal .td-select{margin-left:7px}body .xly-dialog-normal .td-select-group{width:220px}body .xly-dialog-normal .td-input__inner{padding:6px 10px}body .xly-dialog-back{width:450px}body .xly-dialog-back .td-dialog__footer{margin-top:12px}body .xly-dialog-back .xly-dialog-resource__name{width:290px}body .xly-dialog-acquire{padding:0 20px 20px;width:500px;height:150px}body .xly-dialog-acquire .td-dialog__footer{margin-top:14px}.xly-guide-tips{position:absolute;display:flex;align-items:center;left:40px;bottom:27px;padding:0 6px 0 12px;height:30px;color:#fff;background:#ff9700;box-shadow:0 0 10px 0 rgba(26,26,26,.2);border-radius:4px}.xly-guide-tips [class^=xly-icon-]{margin-right:6px;font-size:24px}.xly-guide-tips .td-icon-close{margin-left:6px;opacity:.8;cursor:pointer}.xly-guide-tips .td-icon-close:hover{opacity:1}.xly-guide-tips:after{position:absolute;top:100%;left:10px;width:12px;height:6px;background:#ff9700;-webkit-clip-path:polygon(0 0,100% 0,0 100%);clip-path:polygon(0 0,100% 0,0 100%);content:""}.xly-guide-tag{position:absolute;bottom:0;left:82px;width:calc(100% - 83px);cursor:default;padding:4px 0}.xly-guide-tag span{display:inline-flex;align-items:center;padding:0 8px;height:22px;color:#f99300;background:#fff2ce;border-radius:2px;cursor:pointer}.xly-guide-tag span:before{top:11px;left:-8px;position:absolute;width:0;height:0;font-size:0;overflow:hidden;border-color:transparent #fff2ce transparent transparent;border-style:dashed;border-width:3px 4px;content:""}.xly-guide-tag.is-login{width:calc(100% - 205px)}',""]),t.exports=e},1512:function(t,e,o){"use strict";o.r(e);o(10),o(11),o(32),o(20),o(105),o(12),o(367),o(30),o(61);var r=o(9),i=(o(173),o(7),o(33)),a=o(40),l=o(70),n=o(71),d=o(37),s=(o(4),o(29)),c=o(18),p=o(3),x=o(82),u=o.n(x),b=o(2),g=o(1),y=o(286),h=o(136),f=o(1329);function _(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var o,r=Object(d.a)(t);if(e){var i=Object(d.a)(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return Object(n.a)(this,o)}}var k=function(t,e,o,r){var i,a=arguments.length,l=a<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,o):r;if("object"===("undefined"==typeof Reflect?"undefined":Object(s.a)(Reflect))&&"function"==typeof Reflect.decorate)l=Reflect.decorate(t,e,o,r);else for(var n=t.length-1;n>=0;n--)(i=t[n])&&(l=(a<3?i(l):a>3?i(e,o,l):i(e,o))||l);return a>3&&l&&Object.defineProperty(e,o,l),l},v=function(t,e,o,r){return new(o||(o=Promise))((function(i,a){function l(t){try{d(r.next(t))}catch(t){a(t)}}function n(t){try{d(r.throw(t))}catch(t){a(t)}}function d(t){var e;t.done?i(t.value):(e=t.value,e instanceof o?e:new o((function(t){t(e)}))).then(l,n)}d((r=r.apply(t,e||[])).next())}))},m=function(t){Object(l.a)(o,t);var e=_(o);function o(){var t;return Object(i.a)(this,o),(t=e.apply(this,arguments)).visible=!0,t.selectedPath="",t.useDefault=!1,t.options=[],t.files=[],t.freeSpace=0,t.shareUserId="",t.shareId="",t.from="",t.diskTips="",t}return Object(a.a)(o,[{key:"created",value:function(){var t;return v(this,void 0,void 0,regeneratorRuntime.mark((function e(){var o,i,a,l,n,d=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Object(b.registerFunctions)(p.c,{addMoreFile:function(t,e){(e=e.filter((function(t){return d.files.findIndex((function(e){return e.id===t.id}))>-1}))).length>0&&(d.files=d.files.concat(e))}}),o=this.$route.query,this.shareId=null==o?void 0:o.shareId,this.shareUserId=null==o?void 0:o.shareUserId,this.from=null==o?void 0:o.from,e.next=7,this.$store.dispatch("retrieval-list/getCategoryData");case 7:return e.next=9,Object(b.getConfig)("ThunderPanPlugin","useDefault",!1);case 9:return i=e.sent,e.t0=b.getConfig,e.t1="",e.next=14,this.getLargestPartition();case 14:return e.t2=e.sent,e.t3=e.t1.concat.call(e.t1,e.t2,"迅雷云盘"),e.next=18,(0,e.t0)("ThunderPanPlugin","defaultDownloadPath",e.t3);case 18:return a=e.sent,e.next=21,Object(b.getConfig)("ThunderPanPlugin","downloadPaths",[]);case 21:return l=e.sent,(i||0===l.length)&&(l=[a].concat(Object(r.a)(l))),this.options=l,this.selectedPath=l[0],e.next=27,p.a.callRemoteClientFunction("ThunderPanPluginWebview","GetFetchBackFiles");case 27:if(e.t5=t=e.sent,e.t4=null===e.t5,e.t4){e.next=31;break}e.t4=void 0===t;case 31:if(!e.t4){e.next=35;break}e.t6=void 0,e.next=36;break;case 35:e.t6=t[0];case 36:n=e.t6,this.files=this.files.concat(n),this.checkVolume(!0);case 39:case"end":return e.stop()}}),e,this)})))}},{key:"mounted",value:function(){this.$eventTrack("getback_pop_show",{from:this.from,share_userid:this.shareUserId||"",share_id:this.shareId||""})}},{key:"onFetchBack",value:function(){return v(this,void 0,void 0,regeneratorRuntime.mark((function t(){var e=this;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(0!==this.files.length){t.next=2;break}return t.abrupt("return");case 2:return this.useDefault&&Object(b.setConfig)("ThunderPanPlugin","defaultDownloadPath",this.selectedPath),t.next=5,this.checkVolume(!0);case 5:this.fileSize<=this.freeSpace&&(this.clickEventTrack("getback_now"),this.options=Array.from(new Set([this.selectedPath].concat(Object(r.a)(this.options)))).slice(0,3),Object(b.setConfig)("ThunderPanPlugin","downloadPaths",this.options),this.$store.dispatch("retrieval-list/ipcStartRetrieval",{defaultPath:this.selectedPath,shareId:this.shareId,shareUserId:this.shareUserId,from:this.from}).then((function(){})).then((function(){e.files.length>1?h.default.dir:e.files[0].icon_link;e.closeWindow()})));case 6:case"end":return t.stop()}}),t,this)})))}},{key:"choosePath",value:function(){var t,e;return v(this,void 0,void 0,regeneratorRuntime.mark((function o(){var i,a,l;return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,u.a.electron.dialog.__resolve();case 2:return i=o.sent,o.next=5,u.a.getCurrentWindow().__resolve();case 5:return a=o.sent,o.next=8,i.showOpenDialog(a,{tite:"下载文件到",properties:["openDirectory"]});case 8:if(o.t2=t=o.sent,o.t1=null===o.t2,o.t1){o.next=12;break}o.t1=void 0===t;case 12:if(!o.t1){o.next=16;break}o.t3=void 0,o.next=17;break;case 16:o.t3=t.filePaths;case 17:if(o.t4=e=o.t3,o.t0=null===o.t4,o.t0){o.next=21;break}o.t0=void 0===e;case 21:if(!o.t0){o.next=25;break}o.t5=void 0,o.next=26;break;case 25:o.t5=e[0];case 26:(l=o.t5)&&(this.selectedPath=l,this.options=Array.from(new Set([l].concat(Object(r.a)(this.options)))).slice(0,3),Object(b.setConfig)("ThunderPanPlugin","downloadPaths",this.options));case 28:case"end":return o.stop()}}),o,this)})))}},{key:"clickEventTrack",value:function(t){this.$eventTrack("getback_pop_click",{clickid:t,from:this.from,share_userid:this.shareUserId||"",share_id:this.shareId||""})}},{key:"onUseDefaultChange",value:function(t){Object(b.setConfig)("ThunderPanPlugin","useDefault",t)}},{key:"selectedPathChange",value:function(){return v(this,void 0,void 0,regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.checkVolume();case 2:case"end":return t.stop()}}),t,this)})))}},{key:"checkVolume",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return v(this,void 0,void 0,regeneratorRuntime.mark((function e(){var o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(y.getFreePartitionSpace)(this.selectedPath,t);case 3:o=e.sent,this.freeSpace=o,this.diskTips=o<0?"不合法的存储路径":"",e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error(e.t0);case 11:case"end":return e.stop()}}),e,this,[[0,8]])})))}},{key:"closeWindow",value:function(){window.close()}},{key:"getLargestPartition",value:function(){return v(this,void 0,void 0,regeneratorRuntime.mark((function t(){var e,o,i,a;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(y.getLogicalDriveStrings)();case 2:return e=t.sent,t.next=5,Promise.all(e.map((function(t){return Object(y.getFreePartitionSpace)(t)})));case 5:return o=t.sent,i=Math.max.apply(Math,Object(r.a)(o)),a=o.findIndex((function(t){return t===i})),t.abrupt("return",e[a]);case 9:case"end":return t.stop()}}),t)})))}},{key:"fileSize",get:function(){for(var t=0,e=0;e<this.files.length;e++){if("drive#folder"===this.files[e].kind)return"";t+=Number(this.files[e].size)||0}return t}},{key:"filename",get:function(){return this.files.map((function(t){return t.name||""})).join(",")}},{key:"fileIconStyle",get:function(){var t,e,o=(null===(e=null===(t=this.files)||void 0===t?void 0:t[0])||void 0===e?void 0:e.icon_link)||"",r={};return o&&(r.background="url(".concat(o,") 0% 0% / cover no-repeat")),r}},{key:"noSpace",get:function(){return this.freeSpace<this.fileSizef}},{key:"isPathValid",get:function(){return this.freeSpace>=0}}]),o}(c.Vue);k([Object(c.Watch)("useDefault")],m.prototype,"onUseDefaultChange",null),k([Object(c.Watch)("selectedPath"),Object(f.a)({time:200})],m.prototype,"selectedPathChange",null);var w=m=k([Object(c.Component)({head:{title:"下载"},methods:{formatSize2:g.formatSize2}})],m),P=(o(280),o(1419),o(72)),O=Object(P.a)(w,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("td-dialog",{attrs:{"custom-class":"xly-dialog-task  xly-dialog-back",visible:t.visible},on:{"update:visible":function(e){t.visible=e},ok:function(e){t.visible=!1},close:t.closeWindow}},[o("h2",{staticStyle:{"-webkit-app-region":"drag"},attrs:{slot:"header"},slot:"header"},[t._v("\n    下载文件\n  ")]),t._v(" "),o("div",{staticClass:"xly-dialog-resource"},[o("i",{staticClass:"xly-icon-type",style:t.fileIconStyle}),t._v(" "),o("div",{staticClass:"xly-dialog-resource__name"},[o("p",{class:{"xly-dialog-resource__text":!0,"is-more":t.files.length>0}},[o("span",[t._v(" "+t._s(t.filename)+" ")]),t._v("\n        "+t._s(t.files.length>1?"等"+t.files.length+"个文件":"")+"\n      ")])]),t._v(" "),o("span",{staticClass:"xly-dialog-resource__size"},[t._v(t._s(""===t.fileSize?"":t.formatSize2(t.fileSize)))])]),t._v(" "),o("div",{staticClass:"xly-dialog-site"},[o("p",{staticClass:"xly-dialog-site__title"},[t._v("\n      下载到：\n    ")]),t._v(" "),o("td-tooltip",{attrs:{placement:"top",visible:!!t.diskTips,content:t.diskTips}},[o("td-select",{staticClass:"is-checked",attrs:{placeholder:"请选择下载路径",options:t.options,editable:""},on:{focus:function(e){t.diskTips=""}},model:{value:t.selectedPath,callback:function(e){t.selectedPath=e},expression:"selectedPath"}},[o("a",{staticClass:"xly-select__file",attrs:{slot:"suffix",href:"javascript:;"},on:{click:t.choosePath},slot:"suffix"},[o("i",{staticClass:"xly-icon-file"})]),t._v(" "),t.noSpace&&t.isPathValid?o("span",{staticClass:"xly-select__size is-warn",attrs:{slot:"suffix"},slot:"suffix"},[t._v("空间不足")]):t.isPathValid&&t.freeSpace!==1/0?o("span",{staticClass:"xly-select__size",attrs:{slot:"suffix"},slot:"suffix"},[t._v("剩余"+t._s(t.formatSize2(t.freeSpace)))]):t._e()])],1),t._v(" "),o("td-checkbox",{staticClass:"xly-dialog-site__checkbox",on:{input:function(e){return t.clickEventTrack("default_path")}},model:{value:t.useDefault,callback:function(e){t.useDefault=e},expression:"useDefault"}},[t._v("\n      默认此路径为下载路径\n    ")])],1),t._v(" "),o("td-button",{attrs:{slot:"footer",size:"large",disabled:t.noSpace},on:{click:t.onFetchBack},slot:"footer"},[t._v("\n    立即下载\n  ")])],1)}),[],!1,null,null,null);e.default=O.exports}}]);
//# sourceMappingURL=fetch-back.b48f326.js.map