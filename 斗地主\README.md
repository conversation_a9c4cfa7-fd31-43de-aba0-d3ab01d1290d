# 斗地主游戏

一个使用Godot 4.4.1引擎和GDScript语言开发的简单斗地主游戏。

## 功能特点

- ✅ 完整的54张牌（包括大小王）
- ✅ 3个玩家（1个人类玩家 + 2个AI）
- ✅ 鼠标左键选牌交互
- ✅ 基本的出牌规则
- ✅ 自动发牌和洗牌
- ✅ 简单的AI对手
- ✅ 游戏状态管理

## 如何运行

1. 确保已安装Godot 4.4.1或更高版本
2. 在Godot编辑器中打开项目
3. 运行主场景 `scenes/Main.tscn`

## 游戏操作

### 鼠标操作
- **左键点击卡牌**：选择/取消选择卡牌
- 选中的卡牌会向上移动并高亮显示

### 键盘操作
- **空格键**：出选中的牌
- **P键**：过牌
- **R键**：重新开始游戏
- **H键**：显示帮助信息

### UI按钮
- **出牌按钮**：出选中的牌
- **过牌按钮**：跳过当前回合
- **重新开始按钮**：开始新游戏

## 游戏规则

1. **发牌**：每人17张牌，剩余3张作为地主牌
2. **叫地主**：随机选择一个玩家作为地主，获得3张底牌
3. **出牌**：地主先出牌，其他玩家必须压过上一手牌
4. **获胜**：最先出完手牌的玩家获胜

## 项目结构

```
├── scenes/
│   ├── Main.tscn          # 主游戏场景
│   ├── Card.tscn          # 卡牌场景
│   └── SimpleGame.tscn    # 简化测试场景
├── scripts/
│   ├── GameManager.gd     # 游戏管理器
│   ├── CardManager.gd     # 卡牌管理器
│   ├── Player.gd          # 玩家类
│   ├── Card.gd            # 卡牌类
│   ├── UIManager.gd       # UI管理器
│   └── SimpleGame.gd      # 简化游戏脚本
├── assets/
│   └── README.md          # 素材说明
└── project.godot          # Godot项目配置
```

## 开发说明

### 核心类说明

- **Card**: 表示单张扑克牌，包含花色、点数、交互逻辑
- **Player**: 表示玩家，包含手牌管理、AI逻辑
- **CardManager**: 管理整副牌，负责发牌、洗牌、规则判断
- **GameManager**: 游戏主控制器，管理游戏流程和状态
- **UIManager**: 用户界面管理，处理显示和交互

### 扩展建议

1. **素材资源**：添加卡牌图片和音效
2. **高级AI**：实现更智能的AI算法
3. **网络对战**：支持在线多人游戏
4. **动画效果**：添加卡牌移动和特效动画
5. **计分系统**：实现积分和排行榜

## 素材资源

游戏目前使用简单的文本显示卡牌。要添加图片素材：

1. 将卡牌图片放入 `assets/cards/` 文件夹
2. 按照命名规则：`花色_点数.png`（如：`spades_3.png`）
3. 大小王：`joker_small.png`, `joker_big.png`

推荐的开源素材库：
- [OpenGameArt.org](https://opengameart.org/)
- [Freesound.org](https://freesound.org/) (音效)
- [Pixabay](https://pixabay.com/)

## 许可证

本项目仅供学习和参考使用。
