# 板子控制脚本
extends CharacterBody2D

# 板子移动速度
var speed = 400

# 处理输入和移动
func _physics_process(_delta):
	# 初始化速度向量
	var paddle_velocity = Vector2.ZERO
	
	# 检测按键输入
	if Input.is_key_pressed(KEY_A):
		paddle_velocity.x = -speed
	elif Input.is_key_pressed(KEY_D):
		paddle_velocity.x = speed
	
	# 设置板子速度并移动
	set_velocity(paddle_velocity)
	move_and_slide()
	
	# 限制板子在屏幕范围内
	var screen_size = get_viewport_rect().size
	position.x = clamp(position.x, 50, screen_size.x - 50)
