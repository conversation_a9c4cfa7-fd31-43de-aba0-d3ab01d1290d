!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=88)}([function(e,t,n){"use strict";var r,o,i,a;a=function(){var e,t,n,r,o,i,a,s,c,u,l,f,p=p||(e=Math,t=Object.create||function(e){var t;return h.prototype=e,t=new h,h.prototype=null,t},r=(n={}).lib={},o=r.Base={extend:function(e){var n=t(this);return e&&n.mixIn(e),n.hasOwnProperty("init")&&this.init!==n.init||(n.init=function(){n.$super.init.apply(this,arguments)}),(n.init.prototype=n).$super=this,n},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},i=r.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||s).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,o=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<o;i++){var a=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=a<<24-(r+i)%4*8}else for(i=0;i<o;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=o.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],o=function(t){t=t;var n=987654321,r=4294967295;return function(){var o=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return o/=4294967296,(o+=.5)*(.5<e.random()?1:-1)}},a=0;a<t;a+=4){var s=o(4294967296*(n||e.random()));n=987654071*s(),r.push(4294967296*s()|0)}return new i.init(r,t)}}),a=n.enc={},s=a.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new i.init(n,t/2)}},c=a.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new i.init(n,t)}},u=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},l=r.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new i.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,o=n.sigBytes,a=this.blockSize,s=o/(4*a),c=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*a,u=e.min(4*c,o);if(c){for(var l=0;l<c;l+=a)this._doProcessBlock(r,l);var f=r.splice(0,c);n.sigBytes-=u}return new i.init(f,u)},clone:function(){var e=o.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),r.Hasher=l.extend({cfg:o.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){l.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new f.HMAC.init(e,n).finalize(t)}}}),f=n.algo={},n);function h(){}return p},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a():(o=[],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){e.lib.Cipher||function(){var t=e,n=t.lib,r=n.Base,o=n.WordArray,i=n.BufferedBlockAlgorithm,a=t.enc,s=(a.Utf8,a.Base64),c=t.algo.EvpKDF,u=n.Cipher=i.extend({cfg:r.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(e){return{encrypt:function(t,n,r){return l(n).encrypt(e,t,n,r)},decrypt:function(t,n,r){return l(n).decrypt(e,t,n,r)}}}});function l(e){return"string"==typeof e?S:m}n.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var f,p=t.mode={},h=n.BlockCipherMode=r.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),d=p.CBC=((f=h.extend()).Encryptor=f.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize;y.call(this,e,t,r),n.encryptBlock(e,t),this._prevBlock=e.slice(t,t+r)}}),f.Decryptor=f.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,o=e.slice(t,t+r);n.decryptBlock(e,t),y.call(this,e,t,r),this._prevBlock=o}}),f);function y(e,t,n){var r=this._iv;if(r){var o=r;this._iv=void 0}else o=this._prevBlock;for(var i=0;i<n;i++)e[t+i]^=o[i]}var v=(t.pad={}).Pkcs7={pad:function(e,t){for(var n=4*t,r=n-e.sigBytes%n,i=r<<24|r<<16|r<<8|r,a=[],s=0;s<r;s+=4)a.push(i);var c=o.create(a,r);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},g=(n.BlockCipher=u.extend({cfg:u.cfg.extend({mode:d,padding:v}),reset:function(){u.reset.call(this);var e=this.cfg,t=e.iv,n=e.mode;if(this._xformMode==this._ENC_XFORM_MODE)var r=n.createEncryptor;else r=n.createDecryptor,this._minBufferSize=1;this._mode&&this._mode.__creator==r?this._mode.init(this,t&&t.words):(this._mode=r.call(n,this,t&&t.words),this._mode.__creator=r)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){e.pad(this._data,this.blockSize);var t=this._process(!0)}else t=this._process(!0),e.unpad(t);return t},blockSize:4}),n.CipherParams=r.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}})),b=(t.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,n=e.salt;if(n)var r=o.create([1398893684,1701076831]).concat(n).concat(t);else r=t;return r.toString(s)},parse:function(e){var t=s.parse(e),n=t.words;if(1398893684==n[0]&&1701076831==n[1]){var r=o.create(n.slice(2,4));n.splice(0,4),t.sigBytes-=16}return g.create({ciphertext:t,salt:r})}},m=n.SerializableCipher=r.extend({cfg:r.extend({format:b}),encrypt:function(e,t,n,r){r=this.cfg.extend(r);var o=e.createEncryptor(n,r),i=o.finalize(t),a=o.cfg;return g.create({ciphertext:i,key:n,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(n,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),_=(t.kdf={}).OpenSSL={execute:function(e,t,n,r){r=r||o.random(8);var i=c.create({keySize:t+n}).compute(e,r),a=o.create(i.words.slice(t),4*n);return i.sigBytes=4*t,g.create({key:i,iv:a,salt:r})}},S=n.PasswordBasedCipher=m.extend({cfg:m.cfg.extend({kdf:_}),encrypt:function(e,t,n,r){var o=(r=this.cfg.extend(r)).kdf.execute(n,e.keySize,e.ivSize);r.iv=o.iv;var i=m.encrypt.call(this,e,t,o.key,r);return i.mixIn(o),i},decrypt:function(e,t,n,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var o=r.kdf.execute(n,e.keySize,e.ivSize,t.salt);return r.iv=o.iv,m.decrypt.call(this,e,t,o.key,r)}})}()},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(3)):(o=[n(0),n(3)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=n(15),i=n(33),a=Object.prototype.toString;function s(e){return"[object Array]"===a.call(e)}function c(e){return null!==e&&"object"===(void 0===e?"undefined":r(e))}function u(e){return"[object Function]"===a.call(e)}function l(e,t){if(null!=e)if("object"!==(void 0===e?"undefined":r(e))&&(e=[e]),s(e))for(var n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:s,isArrayBuffer:function(e){return"[object ArrayBuffer]"===a.call(e)},isBuffer:i,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:c,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===a.call(e)},isFile:function(e){return"[object File]"===a.call(e)},isBlob:function(e){return"[object Blob]"===a.call(e)},isFunction:u,isStream:function(e){return c(e)&&u(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:l,merge:function e(){var t={};function n(n,o){"object"===r(t[o])&&"object"===(void 0===n?"undefined":r(n))?t[o]=e(t[o],n):t[o]=n}for(var o=0,i=arguments.length;o<i;o++)l(arguments[o],n);return t},extend:function(e,t,n){return l(t,(function(t,r){e[r]=n&&"function"==typeof t?o(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n,r,o,i,a,s;return r=(n=(t=e).lib).Base,o=n.WordArray,a=(i=t.algo).MD5,s=i.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:a,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,r=n.hasher.create(),i=o.create(),a=i.words,s=n.keySize,c=n.iterations;a.length<s;){u&&r.update(u);var u=r.update(e).finalize(t);r.reset();for(var l=1;l<c;l++)u=r.finalize(u),r.reset();i.concat(u)}return i.sigBytes=4*s,i}}),t.EvpKDF=function(e,t,n){return s.create(n).compute(e,t)},e.EvpKDF},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(13),n(14)):(o=[n(0),n(13),n(14)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t;return t=e.lib.WordArray,e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],i=0;i<n;i+=3)for(var a=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)o.push(r.charAt(a>>>6*(3-s)&63));var c=r.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var n=e.length,r=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<r.length;i++)o[r.charCodeAt(i)]=i}var a=r.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(n=s)}return function(e,n,r){for(var o=[],i=0,a=0;a<n;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2,c=r[e.charCodeAt(a)]>>>6-a%4*2;o[i>>>2]|=(s|c)<<24-i%4*8,i++}return t.create(o,i)}(e,n,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.enc.Base64},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(t){var n=e,r=n.lib,o=r.WordArray,i=r.Hasher,a=n.algo,s=[];!function(){for(var e=0;e<64;e++)s[e]=4294967296*t.abs(t.sin(e+1))|0}();var c=a.MD5=i.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,o=e[r];e[r]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var i=this._hash.words,a=e[t+0],c=e[t+1],h=e[t+2],d=e[t+3],y=e[t+4],v=e[t+5],g=e[t+6],b=e[t+7],m=e[t+8],_=e[t+9],S=e[t+10],k=e[t+11],w=e[t+12],x=e[t+13],C=e[t+14],A=e[t+15],P=i[0],B=i[1],O=i[2],E=i[3];P=u(P,B,O,E,a,7,s[0]),E=u(E,P,B,O,c,12,s[1]),O=u(O,E,P,B,h,17,s[2]),B=u(B,O,E,P,d,22,s[3]),P=u(P,B,O,E,y,7,s[4]),E=u(E,P,B,O,v,12,s[5]),O=u(O,E,P,B,g,17,s[6]),B=u(B,O,E,P,b,22,s[7]),P=u(P,B,O,E,m,7,s[8]),E=u(E,P,B,O,_,12,s[9]),O=u(O,E,P,B,S,17,s[10]),B=u(B,O,E,P,k,22,s[11]),P=u(P,B,O,E,w,7,s[12]),E=u(E,P,B,O,x,12,s[13]),O=u(O,E,P,B,C,17,s[14]),P=l(P,B=u(B,O,E,P,A,22,s[15]),O,E,c,5,s[16]),E=l(E,P,B,O,g,9,s[17]),O=l(O,E,P,B,k,14,s[18]),B=l(B,O,E,P,a,20,s[19]),P=l(P,B,O,E,v,5,s[20]),E=l(E,P,B,O,S,9,s[21]),O=l(O,E,P,B,A,14,s[22]),B=l(B,O,E,P,y,20,s[23]),P=l(P,B,O,E,_,5,s[24]),E=l(E,P,B,O,C,9,s[25]),O=l(O,E,P,B,d,14,s[26]),B=l(B,O,E,P,m,20,s[27]),P=l(P,B,O,E,x,5,s[28]),E=l(E,P,B,O,h,9,s[29]),O=l(O,E,P,B,b,14,s[30]),P=f(P,B=l(B,O,E,P,w,20,s[31]),O,E,v,4,s[32]),E=f(E,P,B,O,m,11,s[33]),O=f(O,E,P,B,k,16,s[34]),B=f(B,O,E,P,C,23,s[35]),P=f(P,B,O,E,c,4,s[36]),E=f(E,P,B,O,y,11,s[37]),O=f(O,E,P,B,b,16,s[38]),B=f(B,O,E,P,S,23,s[39]),P=f(P,B,O,E,x,4,s[40]),E=f(E,P,B,O,a,11,s[41]),O=f(O,E,P,B,d,16,s[42]),B=f(B,O,E,P,g,23,s[43]),P=f(P,B,O,E,_,4,s[44]),E=f(E,P,B,O,w,11,s[45]),O=f(O,E,P,B,A,16,s[46]),P=p(P,B=f(B,O,E,P,h,23,s[47]),O,E,a,6,s[48]),E=p(E,P,B,O,b,10,s[49]),O=p(O,E,P,B,C,15,s[50]),B=p(B,O,E,P,v,21,s[51]),P=p(P,B,O,E,w,6,s[52]),E=p(E,P,B,O,d,10,s[53]),O=p(O,E,P,B,S,15,s[54]),B=p(B,O,E,P,c,21,s[55]),P=p(P,B,O,E,m,6,s[56]),E=p(E,P,B,O,A,10,s[57]),O=p(O,E,P,B,g,15,s[58]),B=p(B,O,E,P,x,21,s[59]),P=p(P,B,O,E,y,6,s[60]),E=p(E,P,B,O,k,10,s[61]),O=p(O,E,P,B,h,15,s[62]),B=p(B,O,E,P,_,21,s[63]),i[0]=i[0]+P|0,i[1]=i[1]+B|0,i[2]=i[2]+O|0,i[3]=i[3]+E|0},_doFinalize:function(){var e=this._data,n=e.words,r=8*this._nDataBytes,o=8*e.sigBytes;n[o>>>5]|=128<<24-o%32;var i=t.floor(r/4294967296),a=r;n[15+(64+o>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(64+o>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),e.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,c=s.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,n,r,o,i,a){var s=e+(t&n|~t&r)+o+a;return(s<<i|s>>>32-i)+t}function l(e,t,n,r,o,i,a){var s=e+(t&r|n&~r)+o+a;return(s<<i|s>>>32-i)+t}function f(e,t,n,r,o,i,a){var s=e+(t^n^r)+o+a;return(s<<i|s>>>32-i)+t}function p(e,t,n,r,o,i,a){var s=e+(n^(t|~r))+o+a;return(s<<i|s>>>32-i)+t}n.MD5=i._createHelper(c),n.HmacMD5=i._createHmacHelper(c)}(Math),e.MD5},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getGBObjName=s,t.init=function(e){if(null===o){var t=s(i=e);o={},a()[t]=o}},t.hasAttr=function(e){return void 0!==c()[e]},t.setAttr=function(e,t){c()[e]=t},t.getAttr=function(e){return c()[e]},t.getEnvType=function(){return i},t.gbAttrNames={config:"config",stat:"stat",platformInfo:"platformInfo",innerQuickLogin:"innerQuickLogin",clientFeatureApi:"clientFeatureApi"};var r=t.gbEnvTypes={insideIframe:"insideIframe",outsideIframe:"outsideIframe",pluginIndex:"pluginIndex"},o=null,i=void 0;function a(){return window||global}function s(e){var t="";switch(e){case r.insideIframe:case r.outsideIframe:case r.node:t=e;break;default:t="default"}return t+"WebSdkGlobalObject_CA7FFF8A_0F5B_4654_822B_98B9E74F23DD"}function c(){if(null===o){var e=a();o=e[s(r.insideIframe)]||e[s(r.outsideIframe)]||e[s(r.pluginIndex)]}return o}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GBHelper=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var o=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(6));function i(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this._attrName=e,this._target=void 0}t.GBHelper=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(i,[{key:"getTarget",value:function(){return void 0===this._target&&(o.hasAttr(this._attrName)?this._target=o.getAttr(this._attrName):this.setTarget({})),this._target}},{key:"setTarget",value:function(e){o.setAttr(this._attrName,e),this._target=o.getAttr(this._attrName)}},{key:"hasAttr",value:function(e){return void 0!==this.getTarget()[e]}},{key:"setAttr",value:function(e,t){this.getTarget()[e]=t}},{key:"getAttr",value:function(e){return this.getTarget()[e]}}]),i)},function(e,t,n){"use strict";var r=n(2),o=n(36),i=n(38),a=n(39),s=n(40),c=n(16);e.exports=function(e){return new Promise((function(t,u){var l=e.data,f=e.headers;r.isFormData(l)&&delete f["Content-Type"];var p=new XMLHttpRequest;if(e.auth){var h=e.auth.username||"",d=e.auth.password||"";f.Authorization="Basic "+btoa(h+":"+d)}if(p.open(e.method.toUpperCase(),i(e.url,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,p.onreadystatechange=function(){if(p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in p?a(p.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:n,config:e,request:p};o(t,u,r),p=null}},p.onerror=function(){u(c("Network Error",e,null,p)),p=null},p.ontimeout=function(){u(c("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",p)),p=null},r.isStandardBrowserEnv()){var y=n(41),v=(e.withCredentials||s(e.url))&&e.xsrfCookieName?y.read(e.xsrfCookieName):void 0;v&&(f[e.xsrfHeaderName]=v)}if("setRequestHeader"in p&&r.forEach(f,(function(e,t){void 0===l&&"content-type"===t.toLowerCase()?delete f[t]:p.setRequestHeader(t,e)})),e.withCredentials&&(p.withCredentials=!0),e.responseType)try{p.responseType=e.responseType}catch(h){if("json"!==e.responseType)throw h}"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){p&&(p.abort(),u(e),p=null)})),void 0===l&&(l=null),p.send(l)}))}},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n,r,o;return t=e.lib,n=t.Base,r=t.WordArray,(o=e.x64={}).Word=n.extend({init:function(e,t){this.high=e,this.low=t}}),o.WordArray=n.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,n=[],o=0;o<t;o++){var i=e[o];n.push(i.high),n.push(i.low)}return r.create(n,this.sigBytes)},clone:function(){for(var e=n.clone.call(this),t=e.words=this.words.slice(0),r=t.length,o=0;o<r;o++)t[o]=t[o].clone();return e}}),e},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r=n(2),o=n(35),i={"Content-Type":"application/x-www-form-urlencoded"};function a(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var s,c={adapter:("undefined"==typeof XMLHttpRequest&&"undefined"==typeof process||(s=n(8)),s),transformRequest:[function(e,t){return o(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(a(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(a(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return 200<=e&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(e){c.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){c.headers[e]=r.merge(i)})),e.exports=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function o(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";do{if(null==e)break;switch(void 0===e?"undefined":r(e)){case"string":t=e;break;case"number":case"boolean":t=e.toString()}}while(0);return t}t.forceToString=o,t.forceJsonSimpleValueToString=function e(t){var n={};do{if(null==t){n={};break}switch(void 0===t?"undefined":r(t)){case"object":for(var i in t)n[i]=e(t[i]);break;case"string":case"number":case"boolean":n=o(t,"")}}while(0);return n},t.combineJsonObject=function e(t,n){return function(t,n){var o=t;for(var i in n){var a=n[i];if("object"===(void 0===a?"undefined":r(a))){var s=o[i];o[i]=null==s?a:e(o[i],a)}else void 0!==a&&(o[i]=a)}return o}(JSON.parse(JSON.stringify(t)),JSON.parse(JSON.stringify(n)))},t.dateToTimeString=function(e){return e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()},t.parseJson=function(e){var t=null;try{t=JSON.parse(e)}catch(e){t=null}return t},t.stringifyJson=function(e){var t=void 0;try{t=JSON.stringify(e)}catch(e){t=void 0}return t}},function(e,t){e.exports=require("events")},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n,r,o,i,a,s;return n=(t=e).lib,r=n.WordArray,o=n.Hasher,i=t.algo,a=[],s=i.SHA1=o.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],s=n[3],c=n[4],u=0;u<80;u++){if(u<16)a[u]=0|e[t+u];else{var l=a[u-3]^a[u-8]^a[u-14]^a[u-16];a[u]=l<<1|l>>>31}var f=(r<<5|r>>>27)+c+a[u];f+=u<20?1518500249+(o&i|~o&s):u<40?1859775393+(o^i^s):u<60?(o&i|o&s|i&s)-1894007588:(o^i^s)-899497514,c=s,s=i,i=o<<30|o>>>2,o=r,r=f}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+s|0,n[4]=n[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(64+r>>>9<<4)]=Math.floor(n/4294967296),t[15+(64+r>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}}),t.SHA1=o._createHelper(s),t.HmacSHA1=o._createHmacHelper(s),e.SHA1},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n;t=e.lib.Base,n=e.enc.Utf8,e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=n.parse(t));var r=e.blockSize,o=4*r;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),a=this._iKey=t.clone(),s=i.words,c=a.words,u=0;u<r;u++)s[u]^=1549556828,c[u]^=909522486;i.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},function(e,t,n){"use strict";var r=n(37);e.exports=function(e,t,n,o,i){var a=new Error(e);return r(a,t,n,o,i)}},function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(83),o=n(100);t.getSockPath=function(e){var t=r.tmpdir(),n=e;e||(n=o.basename(process.execPath,".exe"));var i=o.join(t,n+"-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock");return"win32"===process.platform&&(i="\\\\.\\pipe\\"+(i=(i=i.replace(/^\//,"")).replace(/\//g,"-"))),i},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.isObjectEmpty=function(e){var t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.gbAttrNames={otherInfo:"otherInfo",platformInfo:"platformInfo"},t.gbOtherInfoAttrNames={showLoginWndSource:"showLoginWndSource"},t.gbPlatformInfoAttrNames={deviceSign:"deviceSign"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MonitorStatServer=t.businessFlowKeys=t.monitorStatActions=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var o=n(29),i=t.monitorStatActions={request:"request",pagePerformance:"pagePerformance",businessFlow:"businessFlow",initEnv:"initEnv"};function a(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),this._statServer=new o.StatServer(e,"websdk-monitor2")}t.businessFlowKeys={loadLoginPluginFile:"loadLoginPluginFile",loadLoginPlugin:"loadLoginPlugin",openLoginWnd:"openLoginWnd",initLoginWnd:"initLoginWnd",closeLoginWnd:"closeLoginWnd"},t.MonitorStatServer=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(a,[{key:"setPublicData",value:function(e){return this._statServer.setPublicData(e)}},{key:"stat",value:function(e){return this._statServer.stat(e)}},{key:"statRequest",value:function(e){var t={type:i.request,extData1:e};return this.stat(t)}},{key:"statPagePerformance",value:function(e){var t={type:i.pagePerformance,extData2:e};return this.stat(t)}},{key:"statBusinessFlow",value:function(e){var t={type:i.businessFlow,extData3:e};return this.stat(t)}},{key:"statInitEnv",value:function(e){var t={type:i.initEnv,extData4:e};return this.stat(t)}}]),a)},function(e,t){e.exports=require("electron")},function(e,t,n){"use strict";e.exports=n(32)},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(t){var n=e,r=n.lib,o=r.WordArray,i=r.Hasher,a=n.algo,s=[],c=[];!function(){function e(e){for(var n=t.sqrt(e),r=2;r<=n;r++)if(!(e%r))return;return 1}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,o=0;o<64;)e(r)&&(o<8&&(s[o]=n(t.pow(r,.5))),c[o]=n(t.pow(r,1/3)),o++),r++}();var u=[],l=a.SHA256=i.extend({_doReset:function(){this._hash=new o.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],a=n[3],s=n[4],l=n[5],f=n[6],p=n[7],h=0;h<64;h++){if(h<16)u[h]=0|e[t+h];else{var d=u[h-15],y=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,v=u[h-2],g=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;u[h]=y+u[h-7]+g+u[h-16]}var b=r&o^r&i^o&i,m=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),_=p+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&f)+c[h]+u[h];p=f,f=l,l=s,s=a+_|0,a=i,i=o,o=r,r=_+(m+b)|0}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+p|0},_doFinalize:function(){var e=this._data,n=e.words,r=8*this._nDataBytes,o=8*e.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(64+o>>>9<<4)]=t.floor(r/4294967296),n[15+(64+o>>>9<<4)]=r,e.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});n.SHA256=i._createHelper(l),n.HmacSHA256=i._createHmacHelper(l)}(Math),e.SHA256},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(){var t=e,n=t.lib.Hasher,r=t.x64,o=r.Word,i=r.WordArray,a=t.algo;function s(){return o.create.apply(o,arguments)}var c=[s(1116352408,3609767458),s(1899447441,602891725),s(3049323471,3964484399),s(3921009573,2173295548),s(961987163,4081628472),s(1508970993,3053834265),s(2453635748,2937671579),s(2870763221,3664609560),s(3624381080,2734883394),s(310598401,1164996542),s(607225278,1323610764),s(1426881987,3590304994),s(1925078388,4068182383),s(2162078206,991336113),s(2614888103,633803317),s(3248222580,3479774868),s(3835390401,2666613458),s(4022224774,944711139),s(264347078,2341262773),s(604807628,2007800933),s(770255983,1495990901),s(1249150122,1856431235),s(1555081692,3175218132),s(1996064986,2198950837),s(2554220882,3999719339),s(2821834349,766784016),s(2952996808,2566594879),s(3210313671,3203337956),s(3336571891,1034457026),s(3584528711,2466948901),s(113926993,3758326383),s(338241895,168717936),s(666307205,1188179964),s(773529912,1546045734),s(1294757372,1522805485),s(1396182291,2643833823),s(1695183700,2343527390),s(1986661051,1014477480),s(2177026350,1206759142),s(2456956037,344077627),s(2730485921,1290863460),s(2820302411,3158454273),s(3259730800,3505952657),s(3345764771,106217008),s(3516065817,3606008344),s(3600352804,1432725776),s(4094571909,1467031594),s(275423344,851169720),s(430227734,3100823752),s(506948616,1363258195),s(659060556,3750685593),s(883997877,3785050280),s(958139571,3318307427),s(1322822218,3812723403),s(1537002063,2003034995),s(1747873779,3602036899),s(1955562222,1575990012),s(2024104815,1125592928),s(2227730452,2716904306),s(2361852424,442776044),s(2428436474,593698344),s(2756734187,3733110249),s(3204031479,2999351573),s(3329325298,3815920427),s(3391569614,3928383900),s(3515267271,566280711),s(3940187606,3454069534),s(4118630271,4000239992),s(116418474,1914138554),s(174292421,2731055270),s(289380356,3203993006),s(460393269,320620315),s(685471733,587496836),s(852142971,1086792851),s(1017036298,365543100),s(1126000580,2618297676),s(1288033470,3409855158),s(1501505948,4234509866),s(1607167915,987167468),s(1816402316,1246189591)],u=[];!function(){for(var e=0;e<80;e++)u[e]=s()}();var l=a.SHA512=n.extend({_doReset:function(){this._hash=new i.init([new o.init(1779033703,4089235720),new o.init(3144134277,2227873595),new o.init(1013904242,4271175723),new o.init(2773480762,1595750129),new o.init(1359893119,2917565137),new o.init(2600822924,725511199),new o.init(528734635,4215389547),new o.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],a=n[3],s=n[4],l=n[5],f=n[6],p=n[7],h=r.high,d=r.low,y=o.high,v=o.low,g=i.high,b=i.low,m=a.high,_=a.low,S=s.high,k=s.low,w=l.high,x=l.low,C=f.high,A=f.low,P=p.high,B=p.low,O=h,E=d,N=y,M=v,j=g,I=b,L=m,D=_,F=S,T=k,R=w,H=x,U=C,z=A,W=P,J=B,q=0;q<80;q++){var V=u[q];if(q<16)var K=V.high=0|e[t+2*q],X=V.low=0|e[t+2*q+1];else{var G=u[q-15],Q=G.high,Y=G.low,$=(Q>>>1|Y<<31)^(Q>>>8|Y<<24)^Q>>>7,Z=(Y>>>1|Q<<31)^(Y>>>8|Q<<24)^(Y>>>7|Q<<25),ee=u[q-2],te=ee.high,ne=ee.low,re=(te>>>19|ne<<13)^(te<<3|ne>>>29)^te>>>6,oe=(ne>>>19|te<<13)^(ne<<3|te>>>29)^(ne>>>6|te<<26),ie=u[q-7],ae=ie.high,se=ie.low,ce=u[q-16],ue=ce.high,le=ce.low;K=(K=(K=$+ae+((X=Z+se)>>>0<Z>>>0?1:0))+re+((X+=oe)>>>0<oe>>>0?1:0))+ue+((X+=le)>>>0<le>>>0?1:0),V.high=K,V.low=X}var fe,pe=F&R^~F&U,he=T&H^~T&z,de=O&N^O&j^N&j,ye=E&M^E&I^M&I,ve=(O>>>28|E<<4)^(O<<30|E>>>2)^(O<<25|E>>>7),ge=(E>>>28|O<<4)^(E<<30|O>>>2)^(E<<25|O>>>7),be=(F>>>14|T<<18)^(F>>>18|T<<14)^(F<<23|T>>>9),me=(T>>>14|F<<18)^(T>>>18|F<<14)^(T<<23|F>>>9),_e=c[q],Se=_e.high,ke=_e.low,we=W+be+((fe=J+me)>>>0<J>>>0?1:0),xe=ge+ye;W=U,J=z,U=R,z=H,R=F,H=T,F=L+(we=(we=(we=we+pe+((fe+=he)>>>0<he>>>0?1:0))+Se+((fe+=ke)>>>0<ke>>>0?1:0))+K+((fe+=X)>>>0<X>>>0?1:0))+((T=D+fe|0)>>>0<D>>>0?1:0)|0,L=j,D=I,j=N,I=M,N=O,M=E,O=we+(ve+de+(xe>>>0<ge>>>0?1:0))+((E=fe+xe|0)>>>0<fe>>>0?1:0)|0}d=r.low=d+E,r.high=h+O+(d>>>0<E>>>0?1:0),v=o.low=v+M,o.high=y+N+(v>>>0<M>>>0?1:0),b=i.low=b+I,i.high=g+j+(b>>>0<I>>>0?1:0),_=a.low=_+D,a.high=m+L+(_>>>0<D>>>0?1:0),k=s.low=k+T,s.high=S+F+(k>>>0<T>>>0?1:0),x=l.low=x+H,l.high=w+R+(x>>>0<H>>>0?1:0),A=f.low=A+z,f.high=C+U+(A>>>0<z>>>0?1:0),B=p.low=B+J,p.high=P+W+(B>>>0<J>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[30+(128+r>>>10<<5)]=Math.floor(n/4294967296),t[31+(128+r>>>10<<5)]=n,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});t.SHA512=n._createHelper(l),t.HmacSHA512=n._createHmacHelper(l)}(),e.SHA512},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(9)):(o=[n(0),n(9)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(50),o=n(51),i=(a.prototype.get=function(){return this.requestServer},a);function a(){this.requestServer=null,this.requestServer=new r.RequestServer(new o.HttpRequest)}t.RequestServerLoader=i,t.requestServerLoader=new i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=function(){},t.error=function(){},t.warning=function(){},t.critical=function(){},t.verbose=function(){}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.gbConfig=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var o=n(6),i=n(7),a="sdkVersion",s="appId",c="appName",u="hostCategoryAppName",l="appVersion",f="analysisServer",p="syncPC",h="clientFeature",d=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(y,[{key:"setSDKVersion",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(a,t)}},{key:"getSDKVersion",value:function(){return this._gbHelper.getAttr(a)}},{key:"setAppId",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s,t)}},{key:"getAppId",value:function(){return this._gbHelper.getAttr(s)}},{key:"setAppName",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(c,t)}},{key:"getAppName",value:function(){return this._gbHelper.getAttr(c)}},{key:"setHostCategoryAppName",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(u,t)}},{key:"getHostCategoryAppName",value:function(){return this._gbHelper.getAttr(u)}},{key:"setAppVersion",value:function(e){var t="string"==typeof e&&0<e.length?e:"NONE";this._gbHelper.setAttr(l,t)}},{key:"getAppVersion",value:function(){return this._gbHelper.getAttr(l)}},{key:"setHL",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr("hL",t)}},{key:"getHL",value:function(){return this._gbHelper.getAttr("hL")}},{key:"setAnalysisServer",value:function(e){var t=e;this._gbHelper.setAttr(f,t)}},{key:"getAnalysisServer",value:function(){return this._gbHelper.getAttr(f)}},{key:"setSyncPC",value:function(e){var t=!0===e;this._gbHelper.setAttr(p,t)}},{key:"getSyncPC",value:function(){return this._gbHelper.getAttr(p)}},{key:"setClientFeature",value:function(e){var t=!0===e;this._gbHelper.setAttr(h,t)}},{key:"getClientFeature",value:function(){return this._gbHelper.getAttr(h)}}]),y);function y(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,y),this._gbHelper=new i.GBHelper(o.gbAttrNames.config)}t.gbConfig=new d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StatServer=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var o=n(11),i=n(26),a=n(53),s=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(6));function c(e,t){var n=2<arguments.length&&void 0!==arguments[2]&&arguments[2];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c),this._topic=t,this._preUrl=(e||"https://xluser-test-ssl.n0808.com")+"/analysis-report/v1/"+t+"?msg=",this._needEncrypt=!!n,this._requestServer=i.requestServerLoader.get(),this._publicData={}}t.StatServer=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(c,[{key:"_report",value:function(e){var t,n=JSON.stringify(e),r=a.base64ServerLoader.get().encode(n),o=(r=(r=r.replace(/\+/g,"-")).replace(/\//g,"_")).length,i=0;2<=o&&"="===r.charAt(o-1)&&(i="="===r.charAt(o-2)?2:1),t=r.substr(0,o-i);var c=this._preUrl+t,u=!0;if(s.getEnvType()!==s.gbEnvTypes.pluginIndex){var l=l||parent.xdas;l&&"function"==typeof l.fireStatEvent&&(u=!1,l.fireStatEvent(c))}u&&this._requestServer.get(c,null,null,null).catch((function(e){}))}},{key:"setPublicData",value:function(e){this._publicData=e}},{key:"stat",value:function(e){var t=e||{},n=(0,o.combineJsonObject)({reportTime:(0,o.dateToTimeString)(new Date)},this._publicData);n=(0,o.combineJsonObject)(n,t),this._report(n)}}]),c)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.gbStat=t.gbStatAttrNames=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var o=n(6),i=n(7);t.gbStatAttrNames={monitor:"monitor",userBehaviors:"userBehaviors"};var a=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(s,[{key:"hasAttr",value:function(e){return this._gbHelper.hasAttr(e)}},{key:"setAttr",value:function(e,t){this._gbHelper.setAttr(e,t)}},{key:"getAttr",value:function(e){return this._gbHelper.getAttr(e)}}]),s);function s(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),this._gbHelper=new i.GBHelper(o.gbAttrNames.stat)}t.gbStat=new a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(20),o=n(7),i=n(28),a=new o.GBHelper(r.gbAttrNames.platformInfo);t.getPublicData=function(){return{appId:i.gbConfig.getAppId(),appName:i.gbConfig.getAppName(),appVersion:i.gbConfig.getAppVersion(),deviceSign:a.getAttr(r.gbPlatformInfoAttrNames.deviceSign),sdkVersion:i.gbConfig.getSDKVersion(),platform:function(e){var t="";if("string"==typeof e&&0<e.length){var n=e.split("-");n&&0<n.length&&(t=n[0])}return t}(i.gbConfig.getHostCategoryAppName())}},t.getStatServerUrl=function(){return"https://analysis-acc-ssl.xunlei.com"}},function(e,t,n){"use strict";var r=n(2),o=n(15),i=n(34),a=n(10);function s(e){var t=new i(e),n=o(i.prototype.request,t);return r.extend(n,i.prototype,t),r.extend(n,t),n}var c=s(a);c.Axios=i,c.create=function(e){return s(r.merge(a,e))},c.Cancel=n(18),c.CancelToken=n(47),c.isCancel=n(17),c.all=function(e){return Promise.all(e)},c.spread=n(48),e.exports=c,e.exports.default=c},function(e,t,n){"use strict";e.exports=function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}},function(e,t,n){"use strict";var r=n(10),o=n(2),i=n(42),a=n(43);function s(e){this.defaults=e,this.interceptors={request:new i,response:new i}}s.prototype.request=function(e,t){"string"==typeof e&&(e=o.merge({url:arguments[0]},t)),(e=o.merge(r,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var n=[a,void 0],i=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){n.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){n.push(e.fulfilled,e.rejected)}));n.length;)i=i.then(n.shift(),n.shift());return i},o.forEach(["delete","get","head","options"],(function(e){s.prototype[e]=function(t,n){return this.request(o.merge(n||{},{method:e,url:t}))}})),o.forEach(["post","put","patch"],(function(e){s.prototype[e]=function(t,n,r){return this.request(o.merge(r||{},{method:e,url:t,data:n}))}})),e.exports=s},function(e,t,n){"use strict";var r=n(2);e.exports=function(e,t){r.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))}},function(e,t,n){"use strict";var r=n(16);e.exports=function(e,t,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},function(e,t,n){"use strict";e.exports=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e}},function(e,t,n){"use strict";var r=n(2);function o(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var i;if(n)i=n(t);else if(r.isURLSearchParams(t))i=t.toString();else{var a=[];r.forEach(t,(function(e,t){null!=e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),a.push(o(t)+"="+o(e))})))})),i=a.join("&")}return i&&(e+=(-1===e.indexOf("?")?"?":"&")+i),e}},function(e,t,n){"use strict";var r=n(2),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,i,a={};return e&&r.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=r.trim(e.substr(0,i)).toLowerCase(),n=r.trim(e.substr(i+1)),t){if(a[t]&&0<=o.indexOf(t))return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([n]):a[t]?a[t]+", "+n:n}})),a}},function(e,t,n){"use strict";var r,o,i,a=n(2);function s(e){var t=e;return o&&(i.setAttribute("href",t),t=i.href),i.setAttribute("href",t),{href:i.href,protocol:i.protocol?i.protocol.replace(/:$/,""):"",host:i.host,search:i.search?i.search.replace(/^\?/,""):"",hash:i.hash?i.hash.replace(/^#/,""):"",hostname:i.hostname,port:i.port,pathname:"/"===i.pathname.charAt(0)?i.pathname:"/"+i.pathname}}e.exports=a.isStandardBrowserEnv()?(o=/(msie|trident)/i.test(navigator.userAgent),i=document.createElement("a"),r=s(window.location.href),function(e){var t=a.isString(e)?s(e):e;return t.protocol===r.protocol&&t.host===r.host}):function(){return!0}},function(e,t,n){"use strict";var r=n(2);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,o,i,a){var s=[];s.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),r.isString(o)&&s.push("path="+o),r.isString(i)&&s.push("domain="+i),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t,n){"use strict";var r=n(2);function o(){this.handlers=[]}o.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){r.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},function(e,t,n){"use strict";var r=n(2),o=n(44),i=n(17),a=n(10),s=n(45),c=n(46);function u(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return u(e),e.baseURL&&!s(e.url)&&(e.url=c(e.baseURL,e.url)),e.headers=e.headers||{},e.data=o(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||a.adapter)(e).then((function(t){return u(e),t.data=o(t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(u(e),t&&t.response&&(t.response.data=o(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},function(e,t,n){"use strict";var r=n(2);e.exports=function(e,t,n){return r.forEach(n,(function(n){e=n(e,t)})),e}},function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,n){"use strict";var r=n(18);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new r(e),t(n.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function o(e,t){return t>e.length?new Array(t-e.length+1).join("0")+e:e}Object.defineProperty(t,"__esModule",{value:!0}),t.dateToDateString=function(e){return e.getFullYear().toString()+o((e.getMonth()+1).toString(),2)+o(e.getDate().toString(),2)},t.parseJson=function(e){var t=null;try{t=JSON.parse(e)}catch(e){t=null}return t},t.stringifyJson=function(e){var t=void 0;try{t=JSON.stringify(e)}catch(e){t=void 0}return t},t.forceGetTypeValue=function(e,t,n){return(void 0===t?"undefined":r(t))===e?t:n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),this._requester=e}t.RequestServer=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(o,[{key:"get",value:function(e,t,n,r){return this._request("GET",e,t,n,r)}},{key:"post",value:function(e,t,n,r){return this._request("POST",e,t,n,r)}},{key:"_request",value:function(e,t,n,r,o){var i=this;return new Promise((function(a,s){var c=t,u=null,l=o||{};"GET"===e?r&&(c+=i._toUrlParams(r)):u=l.stringifyJsonDatas?JSON.stringify(r):r,i._requester[e.toLocaleLowerCase()](c,n,u).then((function(e){var t=e;if(l.parseJsonResult&&"string"==typeof e.data)try{t.data=JSON.parse(e.data)}catch(e){s(e)}a(t)})).catch((function(e){s(e)}))}))}},{key:"_toUrlParams",value:function(e){var t="";if(e){var n=[];for(var r in e)n.push(r+"="+e[r]);t=n.join("&")}return t}}]),o)},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=r(n(23));o.default.defaults.adapter=n(8);var i=(a.prototype.get=function(e,t,n){return void 0===n&&(n=null),this._request("GET",e,t,null)},a.prototype.post=function(e,t,n){return this._request("POST",e,t,n)},a.prototype._request=function(e,t,n,r){return void 0===r&&(r=null),new Promise((function(i,a){var s={method:e,url:t,headers:n,data:r};o.default(s).then((function(e){var t={data:e.data,status:e.status};i(t)})).catch((function(e){a(e)}))}))},a);function a(){}t.HttpRequest=i},function(e,t){e.exports=require("net")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=(o.prototype.encode=function(e){return new Buffer(e).toString("base64")},o);function o(){}var i=(a.prototype.get=function(){return this.base64Server},a);function a(){this.base64Server=null,this.base64Server=new r}t.Base64ServerLoader=i,t.base64ServerLoader=new i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(30),o=n(31),i=n(21),a=(s.prototype.setPublicData=function(){this.get().setPublicData(o.getPublicData())},s.prototype.get=function(){if(null===this.monitorStatServer){var e=r.gbStatAttrNames.monitor;if(!r.gbStat.hasAttr(e)){var t=new i.MonitorStatServer(o.getStatServerUrl());r.gbStat.setAttr(e,t)}this.monitorStatServer=r.gbStat.getAttr(e)}return this.monitorStatServer},s);function s(){this.monitorStatServer=null,this.monitorStatServer=null}t.MonitorStatServerLoader=a,t.monitorStatServerLoader=new a},,,,,,function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,n=t.init;(t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,r=[],o=0;o<t;o++)r[o>>>2]|=e[o]<<24-o%4*8;n.call(this,r,t)}else n.apply(this,arguments)}).prototype=t}}(),e.lib.WordArray},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(){var t=e.lib.WordArray,n=e.enc;function r(e){return e<<8&4278255360|e>>>8&16711935}n.Utf16=n.Utf16BE={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o+=2){var i=t[o>>>2]>>>16-o%4*8&65535;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var n=e.length,r=[],o=0;o<n;o++)r[o>>>1]|=e.charCodeAt(o)<<16-o%2*16;return t.create(r,2*n)}},n.Utf16LE={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],i=0;i<n;i+=2){var a=r(t[i>>>2]>>>16-i%4*8&65535);o.push(String.fromCharCode(a))}return o.join("")},parse:function(e){for(var n=e.length,o=[],i=0;i<n;i++)o[i>>>1]|=r(e.charCodeAt(i)<<16-i%2*16);return t.create(o,2*n)}}}(),e.enc.Utf16},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n,r,o,i;return n=(t=e).lib.WordArray,r=t.algo,o=r.SHA256,i=r.SHA224=o.extend({_doReset:function(){this._hash=new n.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=o._doFinalize.call(this);return e.sigBytes-=4,e}}),t.SHA224=o._createHelper(i),t.HmacSHA224=o._createHmacHelper(i),e.SHA224},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(24)):(o=[n(0),n(24)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n,r,o,i,a,s;return n=(t=e).x64,r=n.Word,o=n.WordArray,i=t.algo,a=i.SHA512,s=i.SHA384=a.extend({_doReset:function(){this._hash=new o.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var e=a._doFinalize.call(this);return e.sigBytes-=16,e}}),t.SHA384=a._createHelper(s),t.HmacSHA384=a._createHmacHelper(s),e.SHA384},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(9),n(25)):(o=[n(0),n(9),n(25)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(t){var n=e,r=n.lib,o=r.WordArray,i=r.Hasher,a=n.x64.Word,s=n.algo,c=[],u=[],l=[];!function(){for(var e=1,t=0,n=0;n<24;n++){c[e+5*t]=(n+1)*(n+2)/2%64;var r=(2*e+3*t)%5;e=t%5,t=r}for(e=0;e<5;e++)for(t=0;t<5;t++)u[e+5*t]=t+(2*e+3*t)%5*5;for(var o=1,i=0;i<24;i++){for(var s=0,f=0,p=0;p<7;p++){if(1&o){var h=(1<<p)-1;h<32?f^=1<<h:s^=1<<h-32}128&o?o=o<<1^113:o<<=1}l[i]=a.create(s,f)}}();var f=[];!function(){for(var e=0;e<25;e++)f[e]=a.create()}();var p=s.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var n=this._state,r=this.blockSize/2,o=0;o<r;o++){var i=e[t+2*o],a=e[t+2*o+1];i=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(B=n[o]).high^=a,B.low^=i}for(var s=0;s<24;s++){for(var p=0;p<5;p++){for(var h=0,d=0,y=0;y<5;y++)h^=(B=n[p+5*y]).high,d^=B.low;var v=f[p];v.high=h,v.low=d}for(p=0;p<5;p++){var g=f[(p+4)%5],b=f[(p+1)%5],m=b.high,_=b.low;for(h=g.high^(m<<1|_>>>31),d=g.low^(_<<1|m>>>31),y=0;y<5;y++)(B=n[p+5*y]).high^=h,B.low^=d}for(var S=1;S<25;S++){var k=(B=n[S]).high,w=B.low,x=c[S];x<32?(h=k<<x|w>>>32-x,d=w<<x|k>>>32-x):(h=w<<x-32|k>>>64-x,d=k<<x-32|w>>>64-x);var C=f[u[S]];C.high=h,C.low=d}var A=f[0],P=n[0];for(A.high=P.high,A.low=P.low,p=0;p<5;p++)for(y=0;y<5;y++){var B=n[S=p+5*y],O=f[S],E=f[(p+1)%5+5*y],N=f[(p+2)%5+5*y];B.high=O.high^~E.high&N.high,B.low=O.low^~E.low&N.low}B=n[0];var M=l[s];B.high^=M.high,B.low^=M.low}},_doFinalize:function(){var e=this._data,n=e.words,r=(this._nDataBytes,8*e.sigBytes),i=32*this.blockSize;n[r>>>5]|=1<<24-r%32,n[(t.ceil((1+r)/i)*i>>>5)-1]|=128,e.sigBytes=4*n.length,this._process();for(var a=this._state,s=this.cfg.outputLength/8,c=s/8,u=[],l=0;l<c;l++){var f=a[l],p=f.high,h=f.low;p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8),h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),u.push(h),u.push(p)}return new o.init(u,s)},clone:function(){for(var e=i.clone.call(this),t=e._state=this._state.slice(0),n=0;n<25;n++)t[n]=t[n].clone();return e}});n.SHA3=i._createHelper(p),n.HmacSHA3=i._createHmacHelper(p)}(Math),e.SHA3},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(9)):(o=[n(0),n(9)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){function t(e,t,n){return e^t^n}function n(e,t,n){return e&t|~e&n}function r(e,t,n){return(e|~t)^n}function o(e,t,n){return e&n|t&~n}function i(e,t,n){return e^(t|~n)}function a(e,t){return e<<t|e>>>32-t}var s,c,u,l,f,p,h,d,y,v,g,b;return Math,c=(s=e).lib,u=c.WordArray,l=c.Hasher,f=s.algo,p=u.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),h=u.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),d=u.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),y=u.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),v=u.create([0,1518500249,1859775393,2400959708,2840853838]),g=u.create([1352829926,1548603684,1836072691,2053994217,0]),b=f.RIPEMD160=l.extend({_doReset:function(){this._hash=u.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,s){for(var c=0;c<16;c++){var u=s+c,l=e[u];e[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}var f,b,m,_,S,k,w,x,C,A,P,B=this._hash.words,O=v.words,E=g.words,N=p.words,M=h.words,j=d.words,I=y.words;for(k=f=B[0],w=b=B[1],x=m=B[2],C=_=B[3],A=S=B[4],c=0;c<80;c+=1)P=f+e[s+N[c]]|0,P+=c<16?t(b,m,_)+O[0]:c<32?n(b,m,_)+O[1]:c<48?r(b,m,_)+O[2]:c<64?o(b,m,_)+O[3]:i(b,m,_)+O[4],P=(P=a(P|=0,j[c]))+S|0,f=S,S=_,_=a(m,10),m=b,b=P,P=k+e[s+M[c]]|0,P+=c<16?i(w,x,C)+E[0]:c<32?o(w,x,C)+E[1]:c<48?r(w,x,C)+E[2]:c<64?n(w,x,C)+E[3]:t(w,x,C)+E[4],P=(P=a(P|=0,I[c]))+A|0,k=A,A=C,C=a(x,10),x=w,w=P;P=B[1]+m+C|0,B[1]=B[2]+_+A|0,B[2]=B[3]+S+k|0,B[3]=B[4]+f+w|0,B[4]=B[0]+b+x|0,B[0]=P},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;t[r>>>5]|=128<<24-r%32,t[14+(64+r>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(t.length+1),this._process();for(var o=this._hash,i=o.words,a=0;a<5;a++){var s=i[a];i[a]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return o},clone:function(){var e=l.clone.call(this);return e._hash=this._hash.clone(),e}}),s.RIPEMD160=l._createHelper(b),s.HmacRIPEMD160=l._createHmacHelper(b),e.RIPEMD160},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0)):(o=[n(0)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n,r,o,i,a,s,c;return r=(n=(t=e).lib).Base,o=n.WordArray,a=(i=t.algo).SHA1,s=i.HMAC,c=i.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:a,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,r=s.create(n.hasher,e),i=o.create(),a=o.create([1]),c=i.words,u=a.words,l=n.keySize,f=n.iterations;c.length<l;){var p=r.update(t).finalize(a);r.reset();for(var h=p.words,d=h.length,y=p,v=1;v<f;v++){y=r.finalize(y),r.reset();for(var g=y.words,b=0;b<d;b++)h[b]^=g[b]}i.concat(p),u[0]++}return i.sigBytes=4*l,i}}),t.PBKDF2=function(e,t,n){return c.create(n).compute(e,t)},e.PBKDF2},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(13),n(14)):(o=[n(0),n(13),n(14)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){function t(e,t,n,r){var o=this._iv;if(o){var i=o.slice(0);this._iv=void 0}else i=this._prevBlock;r.encryptBlock(i,0);for(var a=0;a<n;a++)e[t+a]^=i[a]}var n;return e.mode.CFB=((n=e.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,n){var r=this._cipher,o=r.blockSize;t.call(this,e,n,o,r),this._prevBlock=e.slice(n,n+o)}}),n.Decryptor=n.extend({processBlock:function(e,n){var r=this._cipher,o=r.blockSize,i=e.slice(n,n+o);t.call(this,e,n,o,r),this._prevBlock=i}}),n),e.mode.CFB},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n;return e.mode.CTR=(n=(t=e.lib.BlockCipherMode.extend()).Encryptor=t.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,o=this._iv,i=this._counter;o&&(i=this._counter=o.slice(0),this._iv=void 0);var a=i.slice(0);n.encryptBlock(a,0),i[r-1]=i[r-1]+1|0;for(var s=0;s<r;s++)e[t+s]^=a[s]}}),t.Decryptor=n,t),e.mode.CTR},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){function t(e){if(255==(e>>24&255)){var t=e>>16&255,n=e>>8&255,r=255&e;255===t?(t=0,255===n?(n=0,255===r?r=0:++r):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=r}else e+=1<<24;return e}var n,r;return e.mode.CTRGladman=(r=(n=e.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,n){var r,o=this._cipher,i=o.blockSize,a=this._iv,s=this._counter;a&&(s=this._counter=a.slice(0),this._iv=void 0),0===((r=s)[0]=t(r[0]))&&(r[1]=t(r[1]));var c=s.slice(0);o.encryptBlock(c,0);for(var u=0;u<i;u++)e[n+u]^=c[u]}}),n.Decryptor=r,n),e.mode.CTRGladman},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n;return e.mode.OFB=(n=(t=e.lib.BlockCipherMode.extend()).Encryptor=t.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,o=this._iv,i=this._keystream;o&&(i=this._keystream=o.slice(0),this._iv=void 0),n.encryptBlock(i,0);for(var a=0;a<r;a++)e[t+a]^=i[a]}}),t.Decryptor=n,t),e.mode.OFB},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t;return e.mode.ECB=((t=e.lib.BlockCipherMode.extend()).Encryptor=t.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),t.Decryptor=t.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),t),e.mode.ECB},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return e.pad.AnsiX923={pad:function(e,t){var n=e.sigBytes,r=4*t,o=r-n%r,i=n+o-1;e.clamp(),e.words[i>>>2]|=o<<24-i%4*8,e.sigBytes+=o},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Ansix923},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return e.pad.Iso10126={pad:function(t,n){var r=4*n,o=r-t.sigBytes%r;t.concat(e.lib.WordArray.random(o-1)).concat(e.lib.WordArray.create([o<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Iso10126},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return e.pad.Iso97971={pad:function(t,n){t.concat(e.lib.WordArray.create([2147483648],1)),e.pad.ZeroPadding.pad(t,n)},unpad:function(t){e.pad.ZeroPadding.unpad(t),t.sigBytes--}},e.pad.Iso97971},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return e.pad.ZeroPadding={pad:function(e,t){var n=4*t;e.clamp(),e.sigBytes+=n-(e.sigBytes%n||n)},unpad:function(e){for(var t=e.words,n=e.sigBytes-1;!(t[n>>>2]>>>24-n%4*8&255);)n--;e.sigBytes=n+1}},e.pad.ZeroPadding},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return e.pad.NoPadding={pad:function(){},unpad:function(){}},e.pad.NoPadding},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){var t,n;return t=e.lib.CipherParams,n=e.enc.Hex,e.format.Hex={stringify:function(e){return e.ciphertext.toString(n)},parse:function(e){var r=n.parse(e);return t.create({ciphertext:r})}},e.format.Hex},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(1)):(o=[n(0),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(){var t=e,n=t.lib.BlockCipher,r=t.algo,o=[],i=[],a=[],s=[],c=[],u=[],l=[],f=[],p=[],h=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var n=0,r=0;for(t=0;t<256;t++){var d=r^r<<1^r<<2^r<<3^r<<4;d=d>>>8^255&d^99,o[n]=d;var y=e[i[d]=n],v=e[y],g=e[v],b=257*e[d]^16843008*d;a[n]=b<<24|b>>>8,s[n]=b<<16|b>>>16,c[n]=b<<8|b>>>24,u[n]=b,b=16843009*g^65537*v^257*y^16843008*n,l[d]=b<<24|b>>>8,f[d]=b<<16|b>>>16,p[d]=b<<8|b>>>24,h[d]=b,n?(n=y^e[e[e[g^y]]],r^=e[e[r]]):n=r=1}}();var d=[0,1,2,4,8,16,32,64,128,27,54],y=r.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4,r=4*(1+(this._nRounds=6+n)),i=this._keySchedule=[],a=0;a<r;a++)if(a<n)i[a]=t[a];else{var s=i[a-1];a%n?6<n&&a%n==4&&(s=o[s>>>24]<<24|o[s>>>16&255]<<16|o[s>>>8&255]<<8|o[255&s]):(s=o[(s=s<<8|s>>>24)>>>24]<<24|o[s>>>16&255]<<16|o[s>>>8&255]<<8|o[255&s],s^=d[a/n|0]<<24),i[a]=i[a-n]^s}for(var c=this._invKeySchedule=[],u=0;u<r;u++)a=r-u,s=u%4?i[a]:i[a-4],c[u]=u<4||a<=4?s:l[o[s>>>24]]^f[o[s>>>16&255]]^p[o[s>>>8&255]]^h[o[255&s]]}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,s,c,u,o)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,l,f,p,h,i),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,r,o,i,a,s){for(var c=this._nRounds,u=e[t]^n[0],l=e[t+1]^n[1],f=e[t+2]^n[2],p=e[t+3]^n[3],h=4,d=1;d<c;d++){var y=r[u>>>24]^o[l>>>16&255]^i[f>>>8&255]^a[255&p]^n[h++],v=r[l>>>24]^o[f>>>16&255]^i[p>>>8&255]^a[255&u]^n[h++],g=r[f>>>24]^o[p>>>16&255]^i[u>>>8&255]^a[255&l]^n[h++],b=r[p>>>24]^o[u>>>16&255]^i[l>>>8&255]^a[255&f]^n[h++];u=y,l=v,f=g,p=b}y=(s[u>>>24]<<24|s[l>>>16&255]<<16|s[f>>>8&255]<<8|s[255&p])^n[h++],v=(s[l>>>24]<<24|s[f>>>16&255]<<16|s[p>>>8&255]<<8|s[255&u])^n[h++],g=(s[f>>>24]<<24|s[p>>>16&255]<<16|s[u>>>8&255]<<8|s[255&l])^n[h++],b=(s[p>>>24]<<24|s[u>>>16&255]<<16|s[l>>>8&255]<<8|s[255&f])^n[h++],e[t]=y,e[t+1]=v,e[t+2]=g,e[t+3]=b},keySize:8});t.AES=n._createHelper(y)}(),e.AES},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(4),n(5),n(3),n(1)):(o=[n(0),n(4),n(5),n(3),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(){var t=e,n=t.lib,r=n.WordArray,o=n.BlockCipher,i=t.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],s=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],u=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],f=i.DES=o.extend({_doReset:function(){for(var e=this._key.words,t=[],n=0;n<56;n++){var r=a[n]-1;t[n]=e[r>>>5]>>>31-r%32&1}for(var o=this._subKeys=[],i=0;i<16;i++){var u=o[i]=[],l=c[i];for(n=0;n<24;n++)u[n/6|0]|=t[(s[n]-1+l)%28]<<31-n%6,u[4+(n/6|0)]|=t[28+(s[n+24]-1+l)%28]<<31-n%6;for(u[0]=u[0]<<1|u[0]>>>31,n=1;n<7;n++)u[n]=u[n]>>>4*(n-1)+3;u[7]=u[7]<<5|u[7]>>>27}var f=this._invSubKeys=[];for(n=0;n<16;n++)f[n]=o[15-n]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,n){this._lBlock=e[t],this._rBlock=e[t+1],p.call(this,4,252645135),p.call(this,16,65535),h.call(this,2,858993459),h.call(this,8,16711935),p.call(this,1,1431655765);for(var r=0;r<16;r++){for(var o=n[r],i=this._lBlock,a=this._rBlock,s=0,c=0;c<8;c++)s|=u[c][((a^o[c])&l[c])>>>0];this._lBlock=a,this._rBlock=i^s}var f=this._lBlock;this._lBlock=this._rBlock,this._rBlock=f,p.call(this,1,1431655765),h.call(this,8,16711935),h.call(this,2,858993459),p.call(this,16,65535),p.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function p(e,t){var n=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=n,this._lBlock^=n<<e}function h(e,t){var n=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=n,this._rBlock^=n<<e}t.DES=o._createHelper(f);var d=i.TripleDES=o.extend({_doReset:function(){var e=this._key.words;this._des1=f.createEncryptor(r.create(e.slice(0,2))),this._des2=f.createEncryptor(r.create(e.slice(2,4))),this._des3=f.createEncryptor(r.create(e.slice(4,6)))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=o._createHelper(d)}(),e.TripleDES},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(4),n(5),n(3),n(1)):(o=[n(0),n(4),n(5),n(3),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return function(){var t=e,n=t.lib.StreamCipher,r=t.algo,o=r.RC4=n.extend({_doReset:function(){for(var e=this._key,t=e.words,n=e.sigBytes,r=this._S=[],o=0;o<256;o++)r[o]=o;o=0;for(var i=0;o<256;o++){var a=o%n,s=t[a>>>2]>>>24-a%4*8&255;i=(i+r[o]+s)%256;var c=r[o];r[o]=r[i],r[i]=c}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=i.call(this)},keySize:8,ivSize:0});function i(){for(var e=this._S,t=this._i,n=this._j,r=0,o=0;o<4;o++){n=(n+e[t=(t+1)%256])%256;var i=e[t];e[t]=e[n],e[n]=i,r|=e[(e[t]+e[n])%256]<<24-8*o}return this._i=t,this._j=n,r}t.RC4=n._createHelper(o);var a=r.RC4Drop=o.extend({cfg:o.cfg.extend({drop:192}),_doReset:function(){o._doReset.call(this);for(var e=this.cfg.drop;0<e;e--)i.call(this)}});t.RC4Drop=n._createHelper(a)}(),e.RC4},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(4),n(5),n(3),n(1)):(o=[n(0),n(4),n(5),n(3),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){function t(){for(var e=this._X,t=this._C,n=0;n<8;n++)a[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<a[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<a[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<a[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<a[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<a[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<a[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<a[6]>>>0?1:0)|0,this._b=t[7]>>>0<a[7]>>>0?1:0,n=0;n<8;n++){var r=e[n]+t[n],o=65535&r,i=r>>>16,c=((o*o>>>17)+o*i>>>15)+i*i,u=((4294901760&r)*r|0)+((65535&r)*r|0);s[n]=c^u}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}var n,r,o,i,a,s,c;return r=(n=e).lib.StreamCipher,o=n.algo,i=[],a=[],s=[],c=o.Rabbit=r.extend({_doReset:function(){for(var e=this._key.words,n=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var o=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(r=this._b=0;r<4;r++)t.call(this);for(r=0;r<8;r++)i[r]^=o[r+4&7];if(n){var a=n.words,s=a[0],c=a[1],u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),f=u>>>16|4294901760&l,p=l<<16|65535&u;for(i[0]^=u,i[1]^=f,i[2]^=l,i[3]^=p,i[4]^=u,i[5]^=f,i[6]^=l,i[7]^=p,r=0;r<4;r++)t.call(this)}},_doProcessBlock:function(e,n){var r=this._X;t.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var o=0;o<4;o++)i[o]=16711935&(i[o]<<8|i[o]>>>24)|4278255360&(i[o]<<24|i[o]>>>8),e[n+o]^=i[o]},blockSize:4,ivSize:2}),n.Rabbit=r._createHelper(c),e.Rabbit},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(4),n(5),n(3),n(1)):(o=[n(0),n(4),n(5),n(3),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";var r,o,i,a;a=function(e){function t(){for(var e=this._X,t=this._C,n=0;n<8;n++)a[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<a[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<a[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<a[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<a[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<a[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<a[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<a[6]>>>0?1:0)|0,this._b=t[7]>>>0<a[7]>>>0?1:0,n=0;n<8;n++){var r=e[n]+t[n],o=65535&r,i=r>>>16,c=((o*o>>>17)+o*i>>>15)+i*i,u=((4294901760&r)*r|0)+((65535&r)*r|0);s[n]=c^u}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}var n,r,o,i,a,s,c;return r=(n=e).lib.StreamCipher,o=n.algo,i=[],a=[],s=[],c=o.RabbitLegacy=r.extend({_doReset:function(){for(var e=this._key.words,n=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],o=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]],i=this._b=0;i<4;i++)t.call(this);for(i=0;i<8;i++)o[i]^=r[i+4&7];if(n){var a=n.words,s=a[0],c=a[1],u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),f=u>>>16|4294901760&l,p=l<<16|65535&u;for(o[0]^=u,o[1]^=f,o[2]^=l,o[3]^=p,o[4]^=u,o[5]^=f,o[6]^=l,o[7]^=p,i=0;i<4;i++)t.call(this)}},_doProcessBlock:function(e,n){var r=this._X;t.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var o=0;o<4;o++)i[o]=16711935&(i[o]<<8|i[o]>>>24)|4278255360&(i[o]<<24|i[o]>>>8),e[n+o]^=i[o]},blockSize:4,ivSize:2}),n.RabbitLegacy=r._createHelper(c),e.RabbitLegacy},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(4),n(5),n(3),n(1)):(o=[n(0),n(4),n(5),n(3),n(1)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t){e.exports=require("os")},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Object.defineProperty(t,"__esModule",{value:!0});var o=n(99),i=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,n(12).EventEmitter),function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(a,[{key:"encode",value:function(e){return JSON.stringify(e)+"\n"}},{key:"feed",value:function(e){for(var t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);0<=r;){var o=t.slice(n,r),i=JSON.parse(o);this.emit("message",i),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}]),a);function a(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a);var e=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(a.__proto__||Object.getPrototypeOf(a)).call(this));return e.decoder=new o.StringDecoder("utf8"),e.jsonBuffer="",e}t.Parser=i},,,,function(e,t,n){"use strict";var r=this&&this.__assign||Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},o=this&&this.__awaiter||function(e,t,n,r){return new(n=n||Promise)((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n((function(t){t(e.value)})).then(a,s)}c((r=r.apply(e,t||[])).next())}))},i=this&&this.__generator||function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=0<(o=a.trys).length&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t};Object.defineProperty(t,"__esModule",{value:!0});var c=a(n(23)),u=a(n(89)),l=a(n(90)),f=a(n(93)),p=a(n(83)),h=n(49),d=n(11),y=s(n(6)),v=n(26),g=n(11),b=n(94),m=n(95),_=n(101),S=a(n(12));y.init(y.gbEnvTypes.pluginIndex);var k=n(20),w=n(7),x=n(28),C=n(21),A=n(54),P=n(103);x.gbConfig.setSDKVersion("v4.5.11");var B=A.monitorStatServerLoader.get();B.statBusinessFlow({key:C.businessFlowKeys.loadLoginPluginFile});var O,E,N=P.userBehaviorsStatServerLoader.get();(E=O=O||{})[E.GET_DEVICEID_OF_WEBSDK_PLUGIN=0]="GET_DEVICEID_OF_WEBSDK_PLUGIN",c.default.defaults.adapter=n(8),global.AsyncGetNativeCallModuleObj((function(e){var t=n(22).remote.app.getAppPath();t=t.replace(/\\/g,"/");var a=!1,s={},y={},E="",M=[],j={},I=!0,L="",D=!1,F="",T="",R=void 0,H=0,U=!1,z={},W=(new Date).getTime(),J=!1,q={xbaseLogoutUrlMap:new Map,xbaseLogoutInfo:{needRunLogout:!0},xbaseLogoutTabIndex:0,funcPromiseMap:new Map,eventEmitter:new S.default,updateMessageIdMap:new Map,enableOauthLogout:!0},V=["login","login2","login3"],K=["channel","channel2","channel3"],X=!1,G={},Q={appid:"",appName:"",deviceModel:"",deviceName:p.default.hostname(),OSVersion:"",netWorkType:"NONE",providerName:"NONE",sdkVersion:x.gbConfig.getSDKVersion(),clientVersion:"",protocolVersion:"301",devicesign:"",platformVersion:"0",fromPlatformVersion:"0",format:"json",timestamp:(new Date).getTime(),creditkey:""};function Y(t,n,r){de("memory","showLoginWndAction",JSON.stringify({from:r}),(function(){e.nativeCall.CallNativeFunction("CreateLoginWnd",!0,n,r,600,440)}))}function $(){return o(this,void 0,void 0,(function(){return i(this,(function(t){return[2,new Promise((function(t,n){pe("memory","initconfig",(function(n){null!==n&&(q.enableOauthLogout=n.enableOauthLogout,Q.appid=n.appid,Q.appName=n.appName,Q.deviceModel="0"===n.platformVersion?"PC":"LINUX",Q.platformVersion=n.platformVersion,Q.fromPlatformVersion=n.platformVersion,Q.clientVersion=n.clientVersion,Q.OSVersion=n.osversion,de("memory","appinfo",JSON.stringify(n)),x.gbConfig.setAppId(Q.appid),x.gbConfig.setAppName(n.package),x.gbConfig.setHostCategoryAppName(Q.appName),x.gbConfig.setAppVersion(Q.clientVersion),e.nativeCall.CallNativeFunction("GetLoginDeviceID",(function(e,r){var o=l.default(r+n.package+n.appid+n.appkey);E="div101."+r+u.default(o),de("memory","deviceid",JSON.stringify({id:E})),de("file","deviceid",JSON.stringify({id:E})),new w.GBHelper(k.gbAttrNames.platformInfo).setAttr(k.gbPlatformInfoAttrNames.deviceSign,h.forceGetTypeValue("string",E,"")),P.userBehaviorsStatServerLoader.setPublicData({}),A.monitorStatServerLoader.setPublicData(),B.statBusinessFlow({key:C.businessFlowKeys.loadLoginPlugin}),q.eventEmitter.emit("deviceIdChanged"),t(E)})))}))}))]}))}))}function Z(t,n,o,i){if(void 0===t&&(t=0),void 0===n&&(n=null),void 0===o&&(o=!1),D)"function"==typeof i&&i();else if(U&&0===t)"function"==typeof i&&i();else{var a=null!==n?n:{type:0,mode:"other",beginTime:(new Date).getTime(),isAuto:1};if(3<=t)return I=U=!1,ee({data:{errorCode:"-1",errorDesc:"网络超时，请重试"}},void 0,"0","autoLoginFail",a),void("function"==typeof i&&i());if(o&&z&&"number"==typeof z.expires_in&&0<z.expires_in){if(W+z.expires_in>(new Date).getTime())return I=!0,F="1",T=(s=z).sessionID,de("memory","userinfo",JSON.stringify(s)),me(s),void("function"==typeof i&&i());z={}}U=!0;var u="https://"+V[t]+".xunlei.com/xluser.core.login/v3/loginkey",l=r({},Q,{devicesign:E,userName:j.userid,loginKey:j.loginkey});e.nativeCall.CallNativeFunction("NativeFireEvent","onLoginBefore",j.usernick,j.userid,(function(e,t){})),c.default({url:u,method:"post",data:l}).then((function(e){200==e.status?(U=!1,"0"==e.data.errorCode?(W=(new Date).getTime(),z=function(e){var t=e,n={errorCode:"blogresult",errorDesc:"errdesc",userID:"userid",loginKey:"loginkey",nickName:"usernick",sessionID:"sessionid",userName:"usrname",userNewNo:"usernewno",account:"score",verifyType:"VERIFY_KEY"};for(var r in n)null!=e[r]&&(t[n[r]]=e[r]);return t}(e.data),I=!0,F="1",T=(s=z).sessionID,N.statLoginResult(!0,a),de("memory","userinfo",JSON.stringify(s)),me(s)):(e.data&&(a.errorCode=d.forceToString(e.data.errorCode,"")),I=!0,ee(e,j.userid,"1","autoLoginFail",a)),"function"==typeof i&&i()):Z(t+1,a,o,i)})).catch((function(e){Z(t+1,a,o,i)}))}}function ee(t,n,r,o,i){void 0===r&&(r="0"),D=!1,N.statLoginResult(!1,i),ke(),e.nativeCall.CallNativeFunction("NativeFireEvent","onLoginFailed",r,"fail",(function(e,t){})),de("memory","userinfo",JSON.stringify({errno:t.data.errorCode,loginType:o,userid:n,errmsg:t.data.errorDesc}))}function te(){pe("file","users",(function(e){var t={},n=!(M=[]);if(e&&0<e.length){for(var r=!1,o=j,i=0;i<e.length;i++)e[i].userid==s.userid||e[i].uName==s.uName?(j=e[i],r=!0):e[i].uName&&M.push(e[i]);j.usernick=y.nickName,j.loginkey=s.loginkey,j.userid=s.userid,j.show=!1!==o.show,s.uName&&(j.uName=s.uName),"1"!==F&&(j.xl_autologin=s.xl_autologin),r?"string"==typeof o.nearlyThirdType&&(j.nearlyThirdType=o.nearlyThirdType,t.nearlyThirdType=o.nearlyThirdType):"3"===F&&(j.nearlyThirdType=R||"",t.nearlyThirdType=R||""),n=!0}else"0"!=F&&"3"!==F||(j.usernick=y.nickName,"3"===F&&(j.nearlyThirdType=R||"",t.nearlyThirdType=R||""),n=!0);de("file","lastLoginInfo",JSON.stringify(t),(function(){n&&(M.unshift(j),de("file","users",JSON.stringify(M),(function(){})))}))}))}var ne=null;function re(){J||(J=!0,Se({type:"ping",data:{}},0,(function(e){if(e){if(200!=e.errorCode)return oe();(e.messages||[]).map((function(t){"session_timeout"==t.type?(D=!1,oe(),le({type:"ping",data:e.errorDesc})):"kickout"==t.type&&(oe(),fe({type:"ping",data:{sessionID:t.data.sessionID||""}}))}))}J=!1})))}function oe(){ne&&(clearInterval(ne),ne=null)}function ie(t,n){if(void 0===t&&(t=0),void 0===n&&(n=!0),3<=t)return ae();var o="https://"+V[t]+".xunlei.com/xluser.core.login/v3/getuserinfo",i=r({},Q,{devicesign:E,userID:s.userid,sessionID:s.sessionid,vasid:"2,14,33,34,35"});c.default({url:o,method:"post",data:i}).then((function(r){200==r.status?"0"==r.data.errorCode?(D=!0,y=r.data,de("memory","allUserInfo",JSON.stringify(y)),n&&(I=!0,L=s.userid,D=!0,X||e.nativeCall.CallNativeFunction("NativeFireEvent","onLoginSuc",s.userid,s.sessionid,(function(){})),e.nativeCall.CallNativeFunction("NativeFireEvent","onGetUserInfoFinished",(function(t,n){X=!1,e.nativeCall.CallNativeFunction("CloseLoginWnd","suc")})),oe(),ne=setInterval(re,3e5)),n&&te(),n||ue("vipinfo",G)):ae("1"):ie(t+1)})).catch((function(e){ie(t+1)}))}function ae(t){void 0===t&&(t="0"),e.nativeCall.CallNativeFunction("NativeFireEvent","onGetUserInfoFailed",t,"fail",(function(e,t){}))}function se(t){return void 0===t&&(t=0),o(this,void 0,void 0,(function(){var n=this;return i(this,(function(a){return[2,new Promise((function(a){return o(n,void 0,void 0,(function(){var n,o;return i(this,(function(i){switch(i.label){case 0:return D?(D=!1,de("memory","allUserInfo",JSON.stringify(null)),j.show&&"string"!=typeof j.nearlyThirdType||pe("file","users",(function(e){if(e&&0<e.length){for(var t=e.length,n=[],r=0;r<t;r++){var o=e[r];o.userid!==j.userid&&o.uName!==j.uName&&n.push(o)}de("file","users",JSON.stringify(n),(function(){}))}})),oe(),[4,b.awaitWrap(ke())]):(a(),[2]);case 1:return i.sent(),n="https://"+V[t]+".xunlei.com/xluser.core.login/v3/logout",o=r({},Q,{devicesign:E,userID:j.userid,sessionID:T}),c.default({url:n,method:"post",data:o}).then((function(t){e.nativeCall.CallNativeFunction("NativeFireEvent","onLogout",L,(function(e,t){})),a()})).catch((function(t){e.nativeCall.CallNativeFunction("NativeFireEvent","onLogout",L,(function(e,t){})),a()})),[2]}}))}))}))]}))}))}function ce(e){if(""!=e.data){var t=JSON.parse(e.data);if(s.userid==t.uid){var n=Ae(t.type,t.id);switch(t.type){case"userinfo":case"vipinfo":case"sessioninfo":n&&Pe(t.type,t.id),_.client.broadcastEvent("SyncMsgAck",{thunderXLoginEventId:t.id,thunderXLoginEventType:t.type,thunderXLoginDevicesign:h.forceGetTypeValue("string",E,""),thunderXLoginUserId:t.uid,thunderXLoginTimeStamp:new Date,thunderXLoginSource:"OldMessage",thunderXLoginIsNewEventId:n})}switch(t.type){case"userinfo":n&&(y=r({},y,t.data),de("memory","allUserInfo",JSON.stringify(y)),ue(t.type,t.data),te());break;case"vipinfo":if(n)return G=t.data,ie(0,!1);break;case"sessioninfo":n&&t.data.sessionID==s.sessionid&&("timeout"==t.data.status&&le(t),"kickout"==t.data.status&&fe(t))}}}}function ue(t,n){e.nativeCall.CallNativeFunction("NativeFireEvent","onLoginUpdate",t,JSON.stringify(n),(function(e,t){}))}function le(t){D=!1,ue(t.type,t.data),de("memory","userinfo",JSON.stringify({userid:"",sessionid:""})),e.nativeCall.CallNativeFunction("NativeFireEvent","onLogout",s.userid,(function(e,t){})),Z()}function fe(t){return o(this,void 0,void 0,(function(){return i(this,(function(n){switch(n.label){case 0:return D=!1,ue(t.type,t.data),de("memory","userinfo",JSON.stringify({userid:"",sessionid:"",type:"sessionOut"})),[4,b.awaitWrap(we(s.userid))];case 1:return n.sent(),[4,b.awaitWrap(ke())];case 2:return n.sent(),e.nativeCall.CallNativeFunction("NativeFireEvent","onLogout",s.userid,(function(e,t){Y(0,q.loginUrl,"sessionOut")})),[2]}}))}))}function pe(t,n,r){e.nativeCall.CallNativeFunction("LoadLoginData",t,n,(function(e,t){var n=t?JSON.parse(t):null;r&&r(n)}))}function he(t,n){return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,new Promise((function(r){e.nativeCall.CallNativeFunction("LoadLoginData",t,n,(function(e,t){r({errorCode:e,value:t})}))}))]}))}))}function de(t,n,r,o){void 0===o&&(o=function(){}),e.nativeCall.CallNativeFunction("SaveLoginData",t,n,r,(function(e,t){o&&o()}))}function ye(t,n,r){return o(this,void 0,void 0,(function(){return i(this,(function(o){return[2,new Promise((function(o){e.nativeCall.CallNativeFunction("SaveLoginData",t,n,r,(function(e,t){o({errorCode:e,value:t})}))}))]}))}))}function ve(t,n,r,a){return o(this,void 0,void 0,(function(){return i(this,(function(o){return[2,new Promise((function(o){var i={url:n,name:t,hideUI:r,from:"user-plugin",business:a};e.nativeCall.CallNativeFunction("OpenNewTab",i.url,JSON.stringify(i),(function(e,t){o({errorCode:e,value:t})}))}))]}))}))}function ge(t,n){return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,new Promise((function(r){var o=new Promise((function(i){e.nativeCall.CallNativeFunction("AttachTabEvent",n,t,(function(e){t===e&&i()}),(function(e,t){r({errorCode:e,cookie:t,eventPromise:o})}))}))}))]}))}))}function be(t){return o(this,void 0,void 0,(function(){return i(this,(function(n){return[2,new Promise((function(n){e.nativeCall.CallNativeFunction("DetachTabEvent",t,(function(e){n({errorCode:e,value:""})}))}))]}))}))}function me(t){return o(this,void 0,void 0,(function(){return i(this,(function(n){switch(n.label){case 0:return null!=typeof t.loginType&&delete t.loginType,null!=typeof t.otherLogin&&delete t.otherLogin,t.deviceid=E,q.xbaseLogoutInfo.needRunLogout=!0,[4,xe()];case 1:return n.sent(),de("memory","userinfo",JSON.stringify(t)),t.vipList&&(X=!0,de("memory","allUserInfo",JSON.stringify(t)),e.nativeCall.CallNativeFunction("CloseLoginWnd","suc"),e.nativeCall.CallNativeFunction("NativeFireEvent","onLoginSuc",t.userid,t.sessionid,(function(){}))),ie(),[2]}}))}))}function _e(e,t){T=e.sessionID||e.sessionid,j={userid:e.userid,usernick:e.usernick,loginkey:e.loginkey,uName:e.uName||"",xl_autologin:e.xl_autologin,show:"boolean"!=typeof e.show||e.show},7==e.LOGIN_TYPE&&(j.show=!1),"string"==typeof t&&(j.show=!1,j.nearlyThirdType=t)}e.nativeCall.AttachNativeEvent("OnMessageChannelUserLogin",(function(){e.nativeCall.CallNativeFunction("MessageChannelRegisterService","10000")})),e.nativeCall.AttachNativeEvent("OnMessageChannelServiceCallback",(function(e,t){if("10000"===e)for(var n="string"==typeof t?JSON.parse(t):{},r=0;r<n.length;r++)ce(n[r])})),e.nativeCall.AttachNativeEvent("onUserDetailInfoChange",(function(e){!function(e){if(e&&""!=e){for(var t=JSON.parse(e),n=["nickName","sex","birthday","province","city"],o={},i=0;i<n.length;i++){var a=n[i];t[a]&&(o[a]=t[a])}y=r({},y,o),de("memory","allUserInfo",JSON.stringify(y)),s.usernick=y.nickName,de("memory","userinfo",JSON.stringify(s)),te()}}(e)})),e.nativeCall.AttachNativeEvent("onChangeLoginData",(function(t,n,r){if("memory"==t&&"userinfo"==n){var u=r?JSON.parse(r):{};switch((void 0!==u.loginType&&null!==u.loginType&&"3"!=u.loginType||s.otherLogin)&&(z={}),(s=u).loginType){case"0":F="0",_e(s),me(s);break;case"1":se();break;case"2":se(0).then((function(){Y(0,q.loginUrl,"switchAccount")})).catch((function(){Y(0,q.loginUrl,"switchAccount")}));break;case"3":I=!(D=!1),function(e){try{c.default.request({method:"options",url:"https://xluser-ssl.xunlei.com/xluser.core.login"}).then((function(t){var n=!1;t&&200<=t.status&&t.status<=300&&(n=!0),e(n)})).catch((function(t){e(!1)}))}catch(t){e(!1)}}((function(e){Z(0,null,e)}));break;case 5:F="3",_e(s),e.nativeCall.CallNativeFunction("CloseLoginWnd","suc"),me(s);break;case"6":F="3",me(s);break;case"7":F="3",_e(s),e.nativeCall.CallNativeFunction("CloseLoginWnd","suc"),me(s);break;case"kickout":fe({type:s.loginType,data:""});break;case"timeout":le({type:s.loginType,data:""});break;case"thirdFail":case"autoLoginFail":!function(e){o(this,void 0,void 0,(function(){var t,n;return i(this,(function(r){switch(r.label){case 0:return t="autoLoginFail"===e.loginType,n=!1,"12"!=e.errno?[3,2]:(t&&(n=!0),a=!0,[4,$()]);case 1:return r.sent(),a=!1,[3,3];case 2:"14"!=e.errno&&"15"!=e.errno||t&&(n=!0),r.label=3;case 3:return n?[4,b.awaitWrap(we(e.userid))]:[3,5];case 4:r.sent(),r.label=5;case 5:return I&&Y(0,q.loginUrl,e.loginType),[2]}}))}))}(s)}if(s.otherLogin){F="3";var l={type:0,mode:d.forceToString(R,""),beginTime:H,isAuto:0};N.statLoginResult(!0,l),_e(s,R),e.nativeCall.CallNativeFunction("CloseLoginWnd","suc"),me(s)}}"memory"==t&&"channel"==n&&Se(r?JSON.parse(r):{})}));function Se(e,t,n){if(void 0===t&&(t=0),!D||!s.sessionID)return n&&n(!1);if(3<=t)return n&&n(!1);var o,i,a,u="https://"+K[t]+"-account-ssl.xunlei.com/channel/put?userid="+j.userid+"&version=100",l={sessionID:s.sessionID,devicesign:E,appName:Q.appName,protocolVersion:Q.protocolVersion,platformVersion:Q.platformVersion,clientVersion:Q.clientVersion,businessType:Q.appid,lastSequence:"-1",accept:[]},p=r({},l,{messages:[{type:e.type,sequence:0,needAck:1,createTime:(new Date).getTime(),overdueTime:0,data:e.data}]}),h=(o=s.secureKey,i=p,a=f.default.enc.Hex.parse(o.split(".")[1]),f.default.AES.encrypt(JSON.stringify(i),a,{iv:a,mode:f.default.mode.CBC,padding:f.default.pad.Pkcs7}).toString());c.default({url:u,method:"post",data:h}).then((function(r){if(200==r.status){var o=(a=s.secureKey,c=r.data,u=f.default.enc.Hex.parse(a.split(".")[1]),l=function(e,t){var n=e.split("");return Array.from({length:Math.ceil(n.length/t)},(function(e,r){return n.slice(r*t,r*t+t)}))}(f.default.AES.decrypt(c,u,{iv:u,mode:f.default.mode.CBC,padding:f.default.pad.Pkcs7}).toString(),2),p="",l.map((function(e){var t="0x"+e.join(",").replace(",","");p+=String.fromCharCode(parseInt(t))})),p),i=JSON.parse(o);i.errorCode?n&&n(i):n&&n(!1)}else Se(e,t+1,n);var a,c,u,l,p})).catch((function(r){Se(e,t+1,n)}))}function ke(){return o(this,void 0,void 0,(function(){var t,n,r,a,s,c,u,l,f,p;return i(this,(function(h){switch(h.label){case 0:return q.enableOauthLogout&&q.xbaseLogoutInfo.needRunLogout?(t="WebSDK-XbaseLogout-Tab-{B284B653-0A8C-4E31-8CA6-A41679F30323}"+q.xbaseLogoutTabIndex,n=q.xbaseLogoutUrlMap,q.xbaseLogoutTabIndex++,q.xbaseLogoutUrlMap=new Map,[4,Ce(new Map)]):[3,18];case 1:h.sent(),n.has("")||n.set("","https://i.xunlei.com/xluser/oauth.html?sign_out=true"),r=n.size,a=Array.from(n.keys()),s=0,h.label=2;case 2:return s<r?(c=a[s],"string"==typeof(u=n.get(a[s]))&&0<u.length?0!==c.length?[3,7]:[4,ge(t,"OnCloseBrowser")]:[3,14]):[3,15];case 3:return l=h.sent(),[4,b.awaitWrap(ve(t,u,!0,"xbase-sign-out"))];case 4:return h.sent(),[4,Promise.race([l.eventPromise,Oe(5e3)])];case 5:return h.sent(),[4,b.awaitWrap(be(l.cookie||0))];case 6:return h.sent(),[3,14];case 7:return[4,ge(t,"OnLoadEnd")];case 8:return f=h.sent(),[4,ge(t,"OnLoadError")];case 9:return p=h.sent(),[4,b.awaitWrap(ve(t,u,!0,"xbase-sign-out"))];case 10:return h.sent(),[4,Promise.race([f.eventPromise,p.eventPromise,Oe(3e3)])];case 11:return h.sent(),[4,b.awaitWrap(be(f.cookie||0))];case 12:return h.sent(),[4,b.awaitWrap(be(p.cookie||0))];case 13:h.sent(),h.label=14;case 14:return s++,[3,2];case 15:return[4,b.awaitWrap(function(t){return o(this,void 0,void 0,(function(){return i(this,(function(n){return[2,new Promise((function(n){e.nativeCall.CallNativeFunction("CloseTabByName",t,(function(e,t){n({errorCode:e,value:t})}))}))]}))}))}(t))];case 16:return h.sent(),q.xbaseLogoutInfo.needRunLogout=!1,[4,xe()];case 17:h.sent(),h.label=18;case 18:return[2]}}))}))}function we(e){return o(this,void 0,void 0,(function(){var t,n,r,o,a,s;return i(this,(function(i){switch(i.label){case 0:return"string"==typeof e&&0<e.length?[4,b.awaitWrap(he("file","users"))]:[3,3];case 1:if(!(t=i.sent())[1])return[3,3];if(n=t[1],r=g.parseJson(n.value||""),Array.isArray(r))for(o=r.length,a=0;a<o;a++)if(s=r[a],e===s.userid){s.loginkey="";break}return[4,b.awaitWrap(ye("file","users",JSON.stringify(r)))];case 2:i.sent(),i.label=3;case 3:return[2]}}))}))}function xe(){return o(this,void 0,void 0,(function(){return i(this,(function(e){switch(e.label){case 0:return[4,b.awaitWrap(ye("file","xbaseLogouInfo",JSON.stringify(q.xbaseLogoutInfo)))];case 1:return e.sent(),[2]}}))}))}function Ce(e){return o(this,void 0,void 0,(function(){var t;return i(this,(function(n){switch(n.label){case 0:return t=[],e.forEach((function(e,n){if("string"==typeof n){var r={clientId:n,url:e};t.push(r)}})),[4,b.awaitWrap(ye("file","logoutUrls",JSON.stringify(t)))];case 1:return n.sent(),[2]}}))}))}function Ae(e,t){var n=!0,r=q.updateMessageIdMap.get(e);if(r)for(var o=0,i=r.length;o<i;o++)if(r[o]===t){n=!1;break}return n}function Pe(e,t){var n=q.updateMessageIdMap.get(e);void 0===n&&(n=new Array,q.updateMessageIdMap.set(e,n)),n.push(t),10<n.length&&n.shift()}function Be(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return o(this,void 0,void 0,(function(){var t,n;return i(this,(function(r){switch(r.label){case 0:t=void 0,r.label=1;case 1:return e?"string"==typeof E&&0<E.length?(t=E,[3,4]):(void 0===(n=q.funcPromiseMap.get(O.GET_DEVICEID_OF_WEBSDK_PLUGIN))&&(n=new Promise((function(e){q.eventEmitter.once("deviceIdChanged",(function(){e(E),q.funcPromiseMap.delete(O.GET_DEVICEID_OF_WEBSDK_PLUGIN)}))})),q.funcPromiseMap.set(O.GET_DEVICEID_OF_WEBSDK_PLUGIN,n)),[4,n]):[3,4];case 2:t=r.sent(),r.label=3;case 3:r.label=4;case 4:return[2,t]}}))}))}function Oe(e){return o(this,void 0,void 0,(function(){return i(this,(function(t){return[2,new Promise((function(t){var n=window.setTimeout((function(){window.clearTimeout(n),t()}),e)}))]}))}))}e.nativeCall.AttachNativeEvent("onShowLoginWnd",(function(e,t){a||t&&Y(0,q.loginUrl,e)})),e.nativeCall.AttachNativeEvent("onLoginWndClose",(function(e,t){"third"===e&&(R=t&&"string"==typeof t.thirdType?t.thirdType:void 0,H=t&&"number"==typeof t.clickTime?t.clickTime:0)})),e.nativeCall.AttachNativeEvent("onFireLoginEvent",(function(e,t){switch(e){case"checkLoginValid":re();break;case"remoteStat":!function(e){if("string"==typeof e&&0<e.length){var t=v.requestServerLoader.get();t&&t.get(e,null,null,null).catch((function(e){}))}}(t);break;case"setXbaseLogoutUrl":!function(e){o(this,void 0,void 0,(function(){var t;return i(this,(function(n){switch(n.label){case 0:return"string"==typeof e&&0<e.length?((t=g.parseJson(e))&&void 0===t.clientId&&("string"==typeof t.url&&0<t.url.length?q.xbaseLogoutUrlMap.set(t.clientId||"",t.url):q.xbaseLogoutUrlMap.delete("")),[4,Ce(new Map(q.xbaseLogoutUrlMap))]):[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}}))}))}(t)}})),function(){var n,c=this;a=!0,n={GetDeviceIdOfWebSDKPlugin:Be},m.server.registerFunctions(n),_.client.attachServerEvent("SyncMessage-/user/me/info",(function(e,t){do{if("string"!=typeof t||0===t.length)break;var n=g.parseJson(t);if("object"!=typeof n)break;var o=Ae(n.type,n.id);switch(n.type){case"userinfo":case"vipinfo":case"sessioninfo":o&&Pe(n.type,n.id),_.client.broadcastEvent("SyncMsgAck",{thunderXLoginEventId:n.id,thunderXLoginEventType:n.type,thunderXLoginDevicesign:h.forceGetTypeValue("string",E,""),thunderXLoginUserId:n.data.userID,thunderXLoginTimeStamp:new Date,thunderXLoginSource:"SyncMessage",thunderXLoginIsNewEventId:o})}switch(n.type){case"userinfo":o&&n.data&&(y=r({},y,n.data),de("memory","allUserInfo",JSON.stringify(y)),ue(n.type,n.data),te());break;case"vipinfo":o&&n.data&&(G=n.data,ie(0,!1));break;case"sessioninfo":if(o&&n.data){var i=n.data;i.sessionID==s.sessionid&&("timeout"==i.status&&le(n),"kickout"==i.status&&fe(n))}}}while(0)})),$().then((function(){return o(c,void 0,void 0,(function(){var n=this;return i(this,(function(r){return q.loginUrl=t+"/plugins/ThunderXLogin/xlx.html?platformVersion="+Q.platformVersion,function(){return o(this,void 0,void 0,(function(){var e,t,n;return i(this,(function(r){switch(r.label){case 0:return[4,b.awaitWrap(he("file","xbaseLogouInfo"))];case 1:return(e=r.sent())[1]&&"string"==typeof(t=e[1]).value&&0<t.value.length&&(n=g.parseJson(t.value||""))&&(q.xbaseLogoutInfo.needRunLogout=!0===n.needRunLogout),[2]}}))}))}().then((function(){(function(){return o(this,void 0,void 0,(function(){var e,t,n,r,o,a;return i(this,(function(i){switch(i.label){case 0:return[4,b.awaitWrap(he("file","logoutUrls"))];case 1:if((e=i.sent())[1]&&"string"==typeof(t=e[1]).value&&0<t.value.length&&(n=g.parseJson(t.value||""),Array.isArray(n)))for(r=n.length,o=0;o<r;o++)(a=n[o])&&"string"==typeof a.clientId&&"string"==typeof a.url&&0<a.url.length&&q.xbaseLogoutUrlMap.set(a.clientId,a.url);return[2]}}))}))})().then((function(){(function(){return o(this,void 0,void 0,(function(){return i(this,(function(e){return[2,new Promise((function(e,t){var n={isAutoLogin:!1};pe("file","users",(function(t){t&&0<t.length?(M=function(e){for(var t=[],n=0;n<e.length;n++)e[n].userid&&t.push(e[n]);for(n=0;n<e.length;n++){for(var r=!1,o=0;o<t.length;o++)if(e[n].uName==t[o].uName){r=!0;break}r||t.push(e[n])}return t}(t),de("file","users",JSON.stringify(M)),(j=M[0]).xl_autologin&&j.userid&&j.loginkey?(I=!1,Z(0,null,!(n.isAutoLogin=!0),(function(){e(n)}))):e(n)):e(n)}))}))]}))}))})().then((function(t){return o(n,void 0,void 0,(function(){return i(this,(function(n){switch(n.label){case 0:return!t||t.isAutoLogin?[3,2]:[4,ke()];case 1:n.sent(),n.label=2;case 2:return a=!1,e.nativeCall.CallNativeFunction("NativeFireEvent","onLoginPluginInited",(function(e,t){})),[2]}}))}))})).catch((function(e){}))})).catch((function(e){}))})).catch((function(e){})),[2]}))}))})).catch((function(e){}))}()}))},function(e,t,n){"use strict";function r(e){return function(e){for(var t="0123456789abcdef",n="",r=0;4*e.length>r;r++)n+=t.charAt(e[r>>2]>>r%4*8+4&15)+t.charAt(e[r>>2]>>r%4*8&15);return n}(function(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var n=1732584193,r=-271733879,o=-1732584194,l=271733878,f=0;e.length>f;f+=16){var p=n,h=r,d=o,y=l;n=i(n,r,o,l,e[f+0],7,-680876936),l=i(l,n,r,o,e[f+1],12,-389564586),o=i(o,l,n,r,e[f+2],17,606105819),r=i(r,o,l,n,e[f+3],22,-1044525330),n=i(n,r,o,l,e[f+4],7,-176418897),l=i(l,n,r,o,e[f+5],12,1200080426),o=i(o,l,n,r,e[f+6],17,-1473231341),r=i(r,o,l,n,e[f+7],22,-45705983),n=i(n,r,o,l,e[f+8],7,1770035416),l=i(l,n,r,o,e[f+9],12,-1958414417),o=i(o,l,n,r,e[f+10],17,-42063),r=i(r,o,l,n,e[f+11],22,-1990404162),n=i(n,r,o,l,e[f+12],7,1804603682),l=i(l,n,r,o,e[f+13],12,-40341101),o=i(o,l,n,r,e[f+14],17,-1502002290),n=a(n,r=i(r,o,l,n,e[f+15],22,1236535329),o,l,e[f+1],5,-165796510),l=a(l,n,r,o,e[f+6],9,-1069501632),o=a(o,l,n,r,e[f+11],14,643717713),r=a(r,o,l,n,e[f+0],20,-373897302),n=a(n,r,o,l,e[f+5],5,-701558691),l=a(l,n,r,o,e[f+10],9,38016083),o=a(o,l,n,r,e[f+15],14,-660478335),r=a(r,o,l,n,e[f+4],20,-405537848),n=a(n,r,o,l,e[f+9],5,568446438),l=a(l,n,r,o,e[f+14],9,-1019803690),o=a(o,l,n,r,e[f+3],14,-187363961),r=a(r,o,l,n,e[f+8],20,1163531501),n=a(n,r,o,l,e[f+13],5,-1444681467),l=a(l,n,r,o,e[f+2],9,-51403784),o=a(o,l,n,r,e[f+7],14,1735328473),n=s(n,r=a(r,o,l,n,e[f+12],20,-1926607734),o,l,e[f+5],4,-378558),l=s(l,n,r,o,e[f+8],11,-2022574463),o=s(o,l,n,r,e[f+11],16,1839030562),r=s(r,o,l,n,e[f+14],23,-35309556),n=s(n,r,o,l,e[f+1],4,-1530992060),l=s(l,n,r,o,e[f+4],11,1272893353),o=s(o,l,n,r,e[f+7],16,-155497632),r=s(r,o,l,n,e[f+10],23,-1094730640),n=s(n,r,o,l,e[f+13],4,681279174),l=s(l,n,r,o,e[f+0],11,-358537222),o=s(o,l,n,r,e[f+3],16,-722521979),r=s(r,o,l,n,e[f+6],23,76029189),n=s(n,r,o,l,e[f+9],4,-640364487),l=s(l,n,r,o,e[f+12],11,-421815835),o=s(o,l,n,r,e[f+15],16,530742520),n=c(n,r=s(r,o,l,n,e[f+2],23,-995338651),o,l,e[f+0],6,-198630844),l=c(l,n,r,o,e[f+7],10,1126891415),o=c(o,l,n,r,e[f+14],15,-1416354905),r=c(r,o,l,n,e[f+5],21,-57434055),n=c(n,r,o,l,e[f+12],6,1700485571),l=c(l,n,r,o,e[f+3],10,-1894986606),o=c(o,l,n,r,e[f+10],15,-1051523),r=c(r,o,l,n,e[f+1],21,-2054922799),n=c(n,r,o,l,e[f+8],6,1873313359),l=c(l,n,r,o,e[f+15],10,-30611744),o=c(o,l,n,r,e[f+6],15,-1560198380),r=c(r,o,l,n,e[f+13],21,1309151649),n=c(n,r,o,l,e[f+4],6,-145523070),l=c(l,n,r,o,e[f+11],10,-1120210379),o=c(o,l,n,r,e[f+2],15,718787259),r=c(r,o,l,n,e[f+9],21,-343485551),n=u(n,p),r=u(r,h),o=u(o,d),l=u(l,y)}return[n,r,o,l]}(function(e){for(var t=[],n=0;8*e.length>n;n+=8)t[n>>5]|=(255&e.charCodeAt(n/8))<<n%32;return t}(e),8*e.length))}function o(e,t,n,r,o,i){return u((a=u(u(t,e),u(r,i)))<<(s=o)|a>>>32-s,n);var a,s}function i(e,t,n,r,i,a,s){return o(t&n|~t&r,e,t,i,a,s)}function a(e,t,n,r,i,a,s){return o(t&r|n&~r,e,t,i,a,s)}function s(e,t,n,r,i,a,s){return o(t^n^r,e,t,i,a,s)}function c(e,t,n,r,i,a,s){return o(n^(t|~r),e,t,i,a,s)}function u(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}Object.defineProperty(t,"__esModule",{value:!0}),window.md5=r,t.default=r},function(module,exports,__webpack_require__){"use strict";(function(module){var __WEBPACK_AMD_DEFINE_RESULT__,_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(){function t(e){e?(f[0]=f[16]=f[1]=f[2]=f[3]=f[4]=f[5]=f[6]=f[7]=f[8]=f[9]=f[10]=f[11]=f[12]=f[13]=f[14]=f[15]=0,this.blocks=f):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}var h="object"==("undefined"==typeof window?"undefined":_typeof(window))?window:{},s=!h.JS_SHA1_NO_NODE_JS&&"object"==("undefined"==typeof process?"undefined":_typeof(process))&&process.versions&&process.versions.node;s&&(h=global);var i=!h.JS_SHA1_NO_COMMON_JS&&"object"==_typeof(module)&&module.exports,e=__webpack_require__(92),r="0123456789abcdef".split(""),o=[-2147483648,8388608,32768,128],n=[24,16,8,0],a=["hex","array","digest","arrayBuffer"],f=[],u=function(e){return function(n){return new t(!0).update(n)[e]()}},c=function(){var e=u("hex");s&&(e=p(e)),e.create=function(){return new t},e.update=function(t){return e.create().update(t)};for(var n=0;n<a.length;++n){var r=a[n];e[r]=u(r)}return e},p=function p(t){var h=eval("require('crypto')"),s=eval("require('buffer').Buffer"),i=function(e){if("string"==typeof e)return h.createHash("sha1").update(e,"utf8").digest("hex");if(e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(void 0===e.length)return t(e);return h.createHash("sha1").update(new s(e)).digest("hex")};return i};t.prototype.update=function(e){if(!this.finalized){var t="string"!=typeof e;t&&e.constructor===h.ArrayBuffer&&(e=new Uint8Array(e));for(var r,o,i=0,a=e.length||0,s=this.blocks;i<a;){if(this.hashed&&(this.hashed=!1,s[0]=this.block,s[16]=s[1]=s[2]=s[3]=s[4]=s[5]=s[6]=s[7]=s[8]=s[9]=s[10]=s[11]=s[12]=s[13]=s[14]=s[15]=0),t)for(o=this.start;i<a&&o<64;++i)s[o>>2]|=e[i]<<n[3&o++];else for(o=this.start;i<a&&o<64;++i)(r=e.charCodeAt(i))<128?s[o>>2]|=r<<n[3&o++]:(r<2048?s[o>>2]|=(192|r>>6)<<n[3&o++]:(r<55296||57344<=r?s[o>>2]|=(224|r>>12)<<n[3&o++]:(r=65536+((1023&r)<<10|1023&e.charCodeAt(++i)),s[o>>2]|=(240|r>>18)<<n[3&o++],s[o>>2]|=(128|r>>12&63)<<n[3&o++]),s[o>>2]|=(128|r>>6&63)<<n[3&o++]),s[o>>2]|=(128|63&r)<<n[3&o++]);this.lastByteIndex=o,this.bytes+=o-this.start,64<=o?(this.block=s[16],this.start=o-64,this.hash(),this.hashed=!0):this.start=o}return 4294967295<this.bytes&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},t.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=o[3&t],this.block=e[16],56<=t&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},t.prototype.hash=function(){var e,t,n=this.h0,r=this.h1,o=this.h2,i=this.h3,a=this.h4,s=this.blocks;for(e=16;e<80;++e)t=s[e-3]^s[e-8]^s[e-14]^s[e-16],s[e]=t<<1|t>>>31;for(e=0;e<20;e+=5)n=(t=(r=(t=(o=(t=(i=(t=(a=(t=n<<5|n>>>27)+(r&o|~r&i)+a+1518500249+s[e]<<0)<<5|a>>>27)+(n&(r=r<<30|r>>>2)|~n&o)+i+1518500249+s[e+1]<<0)<<5|i>>>27)+(a&(n=n<<30|n>>>2)|~a&r)+o+1518500249+s[e+2]<<0)<<5|o>>>27)+(i&(a=a<<30|a>>>2)|~i&n)+r+1518500249+s[e+3]<<0)<<5|r>>>27)+(o&(i=i<<30|i>>>2)|~o&a)+n+1518500249+s[e+4]<<0,o=o<<30|o>>>2;for(;e<40;e+=5)n=(t=(r=(t=(o=(t=(i=(t=(a=(t=n<<5|n>>>27)+(r^o^i)+a+1859775393+s[e]<<0)<<5|a>>>27)+(n^(r=r<<30|r>>>2)^o)+i+1859775393+s[e+1]<<0)<<5|i>>>27)+(a^(n=n<<30|n>>>2)^r)+o+1859775393+s[e+2]<<0)<<5|o>>>27)+(i^(a=a<<30|a>>>2)^n)+r+1859775393+s[e+3]<<0)<<5|r>>>27)+(o^(i=i<<30|i>>>2)^a)+n+1859775393+s[e+4]<<0,o=o<<30|o>>>2;for(;e<60;e+=5)n=(t=(r=(t=(o=(t=(i=(t=(a=(t=n<<5|n>>>27)+(r&o|r&i|o&i)+a-1894007588+s[e]<<0)<<5|a>>>27)+(n&(r=r<<30|r>>>2)|n&o|r&o)+i-1894007588+s[e+1]<<0)<<5|i>>>27)+(a&(n=n<<30|n>>>2)|a&r|n&r)+o-1894007588+s[e+2]<<0)<<5|o>>>27)+(i&(a=a<<30|a>>>2)|i&n|a&n)+r-1894007588+s[e+3]<<0)<<5|r>>>27)+(o&(i=i<<30|i>>>2)|o&a|i&a)+n-1894007588+s[e+4]<<0,o=o<<30|o>>>2;for(;e<80;e+=5)n=(t=(r=(t=(o=(t=(i=(t=(a=(t=n<<5|n>>>27)+(r^o^i)+a-899497514+s[e]<<0)<<5|a>>>27)+(n^(r=r<<30|r>>>2)^o)+i-899497514+s[e+1]<<0)<<5|i>>>27)+(a^(n=n<<30|n>>>2)^r)+o-899497514+s[e+2]<<0)<<5|o>>>27)+(i^(a=a<<30|a>>>2)^n)+r-899497514+s[e+3]<<0)<<5|r>>>27)+(o^(i=i<<30|i>>>2)^a)+n-899497514+s[e+4]<<0,o=o<<30|o>>>2;this.h0=this.h0+n<<0,this.h1=this.h1+r<<0,this.h2=this.h2+o<<0,this.h3=this.h3+i<<0,this.h4=this.h4+a<<0},t.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,o=this.h3,i=this.h4;return r[e>>28&15]+r[e>>24&15]+r[e>>20&15]+r[e>>16&15]+r[e>>12&15]+r[e>>8&15]+r[e>>4&15]+r[15&e]+r[t>>28&15]+r[t>>24&15]+r[t>>20&15]+r[t>>16&15]+r[t>>12&15]+r[t>>8&15]+r[t>>4&15]+r[15&t]+r[n>>28&15]+r[n>>24&15]+r[n>>20&15]+r[n>>16&15]+r[n>>12&15]+r[n>>8&15]+r[n>>4&15]+r[15&n]+r[o>>28&15]+r[o>>24&15]+r[o>>20&15]+r[o>>16&15]+r[o>>12&15]+r[o>>8&15]+r[o>>4&15]+r[15&o]+r[i>>28&15]+r[i>>24&15]+r[i>>20&15]+r[i>>16&15]+r[i>>12&15]+r[i>>8&15]+r[i>>4&15]+r[15&i]},t.prototype.toString=t.prototype.hex,t.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,r=this.h3,o=this.h4;return[e>>24&255,e>>16&255,e>>8&255,255&e,t>>24&255,t>>16&255,t>>8&255,255&t,n>>24&255,n>>16&255,n>>8&255,255&n,r>>24&255,r>>16&255,r>>8&255,255&r,o>>24&255,o>>16&255,o>>8&255,255&o]},t.prototype.array=t.prototype.digest,t.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(20),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),e};var y=c();i?module.exports=y:(h.sha1=y,e&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return y}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))}()}).call(this,__webpack_require__(91)(module))},function(e,t,n){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t){(function(t){e.exports=t}).call(this,{})},function(e,t,n){"use strict";var r,o,i,a;a=function(e){return e},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)?e.exports=t=a(n(0),n(9),n(60),n(61),n(4),n(5),n(13),n(24),n(62),n(25),n(63),n(64),n(65),n(14),n(66),n(3),n(1),n(67),n(68),n(69),n(70),n(71),n(72),n(73),n(74),n(75),n(76),n(77),n(78),n(79),n(80),n(81),n(82)):(o=[n(0),n(9),n(60),n(61),n(4),n(5),n(13),n(24),n(62),n(25),n(63),n(64),n(65),n(14),n(66),n(3),n(1),n(67),n(68),n(69),n(70),n(71),n(72),n(73),n(74),n(75),n(76),n(77),n(78),n(79),n(80),n(81),n(82)],void 0===(i="function"==typeof(r=a)?r.apply(t,o):r)||(e.exports=i))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.awaitWrap=function(e){return e.then((function(e){return[null,e]})).catch((function(e){return[e,null]}))}},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}Object.defineProperty(t,"__esModule",{value:!0});var i,a=n(12),s=n(96),c=n(19),u=n(27),l=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(f,a.EventEmitter),function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(f,[{key:"registerFunctions",value:function(e){do{if(!e)break;var t=void 0;for(var n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error("try to coverd function "+t);this.apis=Object.assign({},this.apis,e)}while(0)}},{key:"start",value:function(e,t){var n=this;if(this.singleton)throw new Error("try to start server duplicate");this.singleton=!0,this.server=new s.Server,global.__xdasIPCServer=this.server,this.server.listen(void 0,t),e&&this.registerFunctions(e),this.server.on("connect",(function(e){n.emit("connect",e)})),this.server.on("message",(function(e,t){function r(){if("attach_event"===e.action)return n.attachEvent(e.name,t),"break";if("detach_event"===e.action)return n.detachEvent(e.name,t),"break";if("broadcast"===e.action){if(e.__context&&e.__context.name===c.serverContextName)throw new Error("client context must difference from server");return n.forwardEvent.apply(n,[e.name,e.__context,e.timestamp].concat(o(e.args))),"break"}if("call_remote_client_api"===e.action)return n.callClientFunction(e,t),"break";if("call_remote_context_by_id"===e.action){if(e.dst&&n.clientAliasMap.hasOwnProperty(e.dst)){var r=n.clientAliasMap[e.dst];r&&n.callClientFunctionById.apply(n,[r,e.rid].concat(o(e.args)))}return"break"}if("call_client_by_id_callback"===e.action)return n.fireRetCallback(e),"break";if("check_client_function_callback"===e.action)return n.fireRetCallback(e),"break";if("remote_client_callback"===e.action)return n.reply2Client(e,t),"break";if("check_client_function_exist"===e.action)return n.checkClientFunction(e.dst,e.method).then((function(r){var o=n.getNow();n.sendAdapter(t,{success:!0,rid:e.rid,method:e.method,data:r,timestamp:e.timestamp?e.timestamp.concat(o):[].concat(o)})})),"break";var i=n.getNow(),a={success:!1,rid:e.rid,method:e.method,timestamp:e.timestamp?e.timestamp.concat(i):[].concat(i)},s=e.method;do{if("register"===e.action){n.registerAlias(e,t,a),n.sendAdapter(t,a);break}if("check_server_function_exist"===e.action){a.success=!0,a.data=!(!n.apis||!n.apis[s]),n.sendAdapter(t,a);break}if("check_client_exist"===e.action){a.success=!0,a.data=!1,e.dst&&n.clientAliasMap.hasOwnProperty(e.dst)&&(a.data=!0),n.sendAdapter(t,a);break}if(!s)break;if(!n.apis||!n.apis[s]){a.error=e.__context+" try to call method '"+s+"' which is undefined",n.sendAdapter(t,a);break}var u=void 0;try{u=n.apis[s].apply(null,[t,e.__context].concat(e.args))}catch(r){a.error=r.message,n.sendAdapter(t,a);break}if(u&&u.then){u.then((function(e){a.data=e,a.success=!0,n.sendAdapter(t,a)})).catch((function(e){a.error=e.message,n.sendAdapter(t,a)}));break}a.success=!0,a.data=u,n.sendAdapter(t,a)}while(0)}do{if("break"===r())break}while(0)})),this.server.on("end",(function(e){for(var t in n.emit("end",e),n.clientAliasMap)n.clientAliasMap[t]===e&&delete n.clientAliasMap[t];for(var r in n.eventMap)n.detachEvent(r,e)}))}},{key:"fireClientEvent",value:function(e){for(var t=arguments.length,n=Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.forwardEvent.apply(this,[e,{name:c.serverContextName},void 0].concat(n))}},{key:"callClientFunctionById",value:function(e,t){for(var n=arguments.length,r=Array(2<n?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];this.sendAdapter(e,{action:"call_client_by_id",rid:t,args:r})}},{key:"callClientFunctionByIdAw",value:function(e,t){for(var n=arguments.length,r=Array(2<n?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];var i=this;return new Promise((function(n,o){var a=i.generateId(),s={s_rid:a,action:"call_client_by_id",rid:t,args:r};i.retCallbackMap[a]=Object.assign({callback:function(e,t){n(e?[null,e.message]:[t])}},s),i.sendAdapter(e,s)}))}},{key:"checkClientFunction",value:function(e,t){var n=this;return new Promise((function(r,o){do{if(!e){r(!1);break}var i=void 0;if("string"==typeof e){if(!n.clientAliasMap.hasOwnProperty(e)){r(!1);break}i=n.clientAliasMap[e]}else i=e;if(!i){r(!1);break}var a=n.generateId(),s={s_rid:a,action:"check_client_function",method:t};n.retCallbackMap[a]=Object.assign({callback:function(e,t){r(!e&&t)}},s),n.sendAdapter(i,s)}while(0)}))}},{key:"callClientFunctionByName",value:function(e,t){for(var n=arguments.length,r=Array(2<n?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];var i=this;return new Promise((function(n,o){do{if(!e){n([null,null]);break}var a=void 0;if("string"==typeof e){if(!i.clientAliasMap.hasOwnProperty(e)){n([null,null]);break}a=i.clientAliasMap[e]}else a=e;if(!a){n([null,null]);break}var s=i.generateId(),u={s_rid:s,action:"call_client_api",args:r,src:c.serverContextName,method:t};i.retCallbackMap[s]=Object.assign({callback:function(e,t,r){n(e?[null,r,e.message]:[t,r])}},u),i.sendAdapter(a,u)}while(0)}))}},{key:"isRemoteClientExist",value:function(e){var t=!1;return this.clientAliasMap.hasOwnProperty(e)&&(t=!0),t}},{key:"registerAlias",value:function(e,t,n){do{if(!e)break;if(!e.alias){n.error="register alias can not be empty";break}if(this.clientAliasMap.hasOwnProperty(e.alias)){if(this.clientAliasMap[e.alias]===t){n.data=e.alias,n.success=!0;break}n.error="register failed cause alias is in use";break}var r=void 0;for(var o in this.clientAliasMap)if(this.clientAliasMap[o]===t){r=o;break}if(r){n.data=r,n.error="register failed cause already register by another alias "+r;break}this.clientAliasMap[e.alias]=t,n.data=e.alias,n.success=!0;break}while(0)}},{key:"callClientFunction",value:function(e,t){do{if(!e)break;var n=e.dst,r={rid:e.rid,method:e.method};if(!this.clientAliasMap.hasOwnProperty(n)){var o="call remote function but dst client is not start or ended";u.error(o),r.error=o,r.action="remote_client_callback",r.success=!1,this.sendAdapter(t,r);break}var i=this.generateId(),a=this.clientAliasMap[n];r=Object.assign({s_rid:i,action:"call_client_api",args:e.args,src:e.src},r),this.retCallbackMap[i]=Object.assign({remote:t},r),this.sendAdapter(a,r)}while(0)}},{key:"fireRetCallback",value:function(e){do{var t=e.s_rid;if(null==t)break;var n=this.retCallbackMap[t];if(!n)break;var r=n.callback;if(!r)break;e.success?r(null,e.data):r(new Error(e.error),e.data),delete this.retCallbackMap[t]}while(0)}},{key:"reply2Client",value:function(e,t){do{var n=e.s_rid;if(null==n)break;var r=this.retCallbackMap[n];if(!r)break;if(e.src===c.serverContextName){var o=r.callback;if(!o)break;e.success?o(null,e.data,t):o(new Error(e.error),e.data,t)}else{var i=r.remote;if(!i)break;this.sendAdapter(i,e)}delete this.retCallbackMap[n]}while(0)}},{key:"getNow",value:function(){return Date.now()}},{key:"sendAdapter",value:function(e,t){var n=this.getNow();t.timestamp=t.timestamp?t.timestamp.concat(n):[].concat(n),e.isInprocess()?(u.information("send to client in process"),e.emit("message",t)):e.send(t)}},{key:"forwardEvent",value:function(e,t,n){for(var r=arguments.length,o=Array(3<r?r-3:0),i=3;i<r;i++)o[i-3]=arguments[i];do{if(!this.eventMap.hasOwnProperty(e))break;var a=this.eventMap[e],s=!0,c=!1,u=void 0;try{for(var l,f=a[Symbol.iterator]();!(s=(l=f.next()).done);s=!0){var p=l.value;this.sendAdapter(p,{action:"fire_event",name:e,args:o,__context:t,timestamp:n})}}catch(e){c=!0,u=e}finally{try{!s&&f.return&&f.return()}finally{if(c)throw u}}}while(0)}},{key:"attachEvent",value:function(e,t){this.eventMap[e]||(this.eventMap[e]=[]);var n=this.eventMap[e],r=!1,o=!0,i=!1,a=void 0;try{for(var s,c=n[Symbol.iterator]();!(o=(s=c.next()).done);o=!0)if(s.value===t){u.information("server attachEvent attached",e),r=!0;break}}catch(e){i=!0,a=e}finally{try{!o&&c.return&&c.return()}finally{if(i)throw a}}r||(this.eventMap[e].push(t),u.information("server attachEvent ",e))}},{key:"detachEvent",value:function(e,t){do{if(!this.eventMap.hasOwnProperty(e))break;for(var n=this.eventMap[e],r=0;r<n.length;r++)if(n[r]===t){this.eventMap[e].splice(r,1),u.information("server detachEvent current eventMap",this.eventMap);break}}while(0)}},{key:"generateId",value:function(){return this.rid++}}]),f);function f(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f);var e=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(f.__proto__||Object.getPrototypeOf(f)).call(this));return e.rid=0,e.apis={},e.eventMap={},e.clientAliasMap={},e.retCallbackMap={},e.singleton=!1,e}global.__xdasIPCServerInstance||(global.__xdasIPCServerInstance=new l),i=global.__xdasIPCServerInstance,t.server=i},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Object.defineProperty(t,"__esModule",{value:!0});var o=n(97),i=n(52),a=n(12),s=n(98),c=n(19),u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(l,a.EventEmitter),function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(l,[{key:"listen",value:function(e,t){var n=c.getSockPath(t);o.existsSync(n)&&o.unlinkSync(n),this.server.listen(n,e)}},{key:"handleConnection",value:function(e){var t=this,n=new s.Client({socket:e});n.on("message",(function(e){t.handleRequest(e,n)})),n.on("end",(function(){t.emit("end",n)})),this.emit("connect",n)}},{key:"handleRequest",value:function(e,t){this.emit("message",e,t)}}]),l);function l(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l);var e=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(l.__proto__||Object.getPrototypeOf(l)).call(this));return e.server=i.createServer((function(t){return e.handleConnection(t)})),e}t.Server=u},function(e,t){e.exports=require("fs")},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Object.defineProperty(t,"__esModule",{value:!0});var o=n(52),i=n(12),a=n(27),s=n(84),c=n(19),u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(l,i.EventEmitter),function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(l,[{key:"isInprocess",value:function(){return this.inprocess}},{key:"getContext",value:function(){return this.context}},{key:"bind",value:function(){var e=this,t=new s.Parser,n=this.socket;n.on("data",(function(e){t.feed(e)})),n.on("end",(function(){a.information("socket is ended"),e.socket=null,e.emit("end")})),n.on("error",(function(e){a.error(e.message)})),t.on("message",(function(t){e.emit("message",t)})),this.parser=t}},{key:"send",value:function(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){a.error(e.message)}else a.information("This socket has been ended by the other party")}}]),l);function l(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),e=e||{};var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(l.__proto__||Object.getPrototypeOf(l)).call(this));if(t.inprocess=!1,t.context=void 0,e.context&&(t.context=e.context),e.socket)t.socket=e.socket,t.bind();else if(global.__xdasIPCServer)t.inprocess=!0;else{var n=c.getSockPath(e.socketPrex);t.socket=o.connect(n),t.bind()}return t}t.Client=u},function(e,t){e.exports=require("string_decoder")},function(e,t){e.exports=require("path")},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}var a=function(e,t,n,r){return new(n=n||Promise)((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n((function(t){t(e.value)})).then(a,s)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});var s,c=n(12),u=n(102),l=n(19),f=n(27),p=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(h,c.EventEmitter),function(e,t,n){t&&o(e.prototype,t),n&&o(e,n)}(h,[{key:"start",value:function(e,t){var n=this;do{if(this.singleton)break;if(this.singleton=!0,global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:null==e&&(e=this.parseContext()),!e)throw new Error("no suitable context for client, please specify context with start function");if(e.name===l.serverContextName)throw new Error("client context must difference from server");this.client=new u.Client({context:e,socketPrex:t}),this.client.on("message",(function(e){if("fire_event"===e.action)n.fireServerEvent(e.name,[e.__context].concat(e.args));else if("call_client_by_id"===e.action)n.callFunctionById(e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)n.callRegisterFunction(e);else if("check_client_function"===e.action){var t=e.method,r=!0;t&&n.apis&&n.apis[t]||(r=!1),n.sendAdapter({s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:r})}else if(void 0!==e.success&&null!==e.success){var o=e;n.emit("stat_call_function_back",n.client.getContext(),e);var i=n.retCallbackMap[o.rid].callback;i&&(o.success?i(null,o.data):i(new Error(o.error),o.data)),delete n.retCallbackMap[o.rid]}})),this.client.on("end",(function(){n.client.isInprocess()||(f.information("server is ended, and this client is try to exit"),process.exit(0))})),this.registry(e.name)}while(0)}},{key:"registerFunctions",value:function(e){do{if(!e)break;var t=void 0;for(var n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error("try to coverd function "+t);this.apis=Object.assign({},this.apis,e)}while(0)}},{key:"checkServerFunction",value:function(e){return a(this,void 0,void 0,regeneratorRuntime.mark((function t(){var n=this;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,r){do{if(!e){t(!1);break}var o=n.generateId(),i={action:"check_server_function_exist",method:e,rid:o};n.retCallbackMap[o]=Object.assign({callback:function(e,n){t(!e&&n)}},i),n.sendAdapter(i)}while(0)})));case 1:case"end":return t.stop()}}),t,this)})))}},{key:"callServerFunction",value:function(e){for(var t=arguments.length,n=Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return a(this,void 0,void 0,regeneratorRuntime.mark((function t(){var r,o;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=null,t.next=3,this.callServerFunctionEx.apply(this,[e].concat(n));case 3:return(o=t.sent)&&(r=o[0]),t.abrupt("return",r);case 6:case"end":return t.stop()}}),t,this)})))}},{key:"callServerFunctionEx",value:function(e){for(var t=this,n=arguments.length,r=Array(1<n?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return new Promise((function(n,o){do{if(!t.client){var i="client doesn't ready";f.error("callServerFunction error",i),n([null,i]);break}t.emit("stat_call_function",t.client.getContext(),e);var a=t.generateId();if(r)for(var s=0;s<r.length;s++)r[s]=t.convertFunction2Id(r[s]);var c={rid:a,method:e,args:r};t.retCallbackMap[a]=Object.assign({callback:function(e,t){e?(f.error("callServerFunction error",e.message),n([null,e.message])):n([t,void 0])}},c),t.sendAdapter(c)}while(0)}))}},{key:"isRemoteClientExist",value:function(e){var t=this;return new Promise((function(n,r){do{if(!e){n([!1,"remote client alias is not specifed"]);break}if(e===t.client.getContext().name){n([!0,"self is exist"]);break}var o=t.generateId(),i={dst:e,action:"check_client_exist",rid:o};t.retCallbackMap[o]=Object.assign({callback:function(e,t){n(e?[!1,e.message]:[t,"sucesss"])}},i),t.sendAdapter(i)}while(0)}))}},{key:"checkRemoteFunction",value:function(e,t){var n=this;return new Promise((function(r,o){do{if(!t){r(!1);break}var i=n.client.getContext().name;if(i===e){r(!(!n.apis||!n.apis[t]));break}var a=n.generateId(),s={action:"check_client_function_exist",method:t,rid:a,src:i,dst:e};n.retCallbackMap[a]=Object.assign({callback:function(e,t){r(!e&&t)}},s),n.sendAdapter(s)}while(0)}))}},{key:"callRemoteClientFunction",value:function(e,t){for(var n=arguments.length,r=Array(2<n?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];var i=this;return e?new Promise((function(n,o){if(r)for(var a=0;a<r.length;a++)r[a]=i.convertFunction2Id(r[a]);var s=i.generateId(),c={src:i.client.getContext().name,dst:e,action:"call_remote_client_api",method:t,args:r,rid:s};i.retCallbackMap[s]=Object.assign({callback:function(e,t){e?(f.information("callRemoteClientFunction",e.message),n([null,e.message])):n([t,void 0])}},c),i.sendAdapter(c)})):this.callServerFunction.apply(this,[t].concat(r))}},{key:"callRemoteContextById",value:function(e,t){for(var n=arguments.length,r=Array(2<n?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];this.sendAdapter({dst:e,action:"call_remote_context_by_id",rid:t,args:r})}},{key:"attachServerEvent",value:function(e,t){var n=this.generateId();return this.eventCallbackMap.hasOwnProperty(e)||(this.eventCallbackMap[e]={}),l.isObjectEmpty(this.eventCallbackMap[e])&&this.sendAdapter({action:"attach_event",name:e}),this.eventCallbackMap[e][n]=t,n}},{key:"detachServerEvent",value:function(e,t){do{if(!this.eventCallbackMap.hasOwnProperty(e))break;delete this.eventCallbackMap[e][t],l.isObjectEmpty(this.eventCallbackMap[e])&&this.sendAdapter({action:"detach_event",name:e})}while(0)}},{key:"broadcastEvent",value:function(e){for(var t=arguments.length,n=Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.sendAdapter({action:"broadcast",name:e,args:n})}},{key:"registry",value:function(e){var t=this;return new Promise((function(n,r){do{if(!e){n(!1);break}var o=t.generateId(),i={alias:e,action:"register",rid:o};t.retCallbackMap[o]=Object.assign({callback:function(t,r){t?(f.error("register error",t.message),n(r)):n(e)}},i),t.sendAdapter(i)}while(0)}))}},{key:"getNow",value:function(){return Date.now()}},{key:"sendAdapter",value:function(e){do{if(!e)break;var t=this.getNow();if(e.timestamp?e.timestamp=[].concat(i(e.timestamp)).concat(t):e.timestamp=[].concat(t),!e.__context){var n=this.client.getContext();n&&(e=Object.assign({__context:n},e))}this.client.isInprocess()?(f.information("send to server in process"),global.__xdasIPCServer.emit("message",e,this.client)):this.client.send(e)}while(0)}},{key:"parseContext",value:function(){var e=void 0;do{for(var t="",n=0;n<process.argv.length;n++){var r=process.argv[n];if(0===r.indexOf("--xdas-plugin-name=",0)){t=r.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}},{key:"generateId",value:function(){return this.rid++}},{key:"fireServerEvent",value:function(e){for(var t=arguments.length,n=Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];do{if(!this.eventCallbackMap.hasOwnProperty(e))break;var o=this.eventCallbackMap[e];for(var i in o){var a=o[i];a&&a.apply.apply(a,[null].concat(n))}}while(0)}},{key:"callFunctionById",value:function(e,t){for(var n=this,r=void 0,o=!1,i=arguments.length,a=Array(2<i?i-2:0),s=2;s<i;s++)a[s-2]=arguments[s];do{var c=this.contextCallbackMap[e];if(!c)break;var u=void 0,l=void 0;try{u=c.apply.apply(c,[null].concat(a))}catch(e){l=e.message;break}if(null==t)break;if(r={s_rid:t,action:"call_client_by_id_callback",success:!1},void 0!==l){r.error=l;break}if(u&&u.then){u.then((function(e){r.data=n.convertFunction2Id(e),r.success=!0,n.sendAdapter(r)})).catch((function(e){r.error=e.message,n.sendAdapter(r)})),o=!0;break}r.success=!0,r.data=this.convertFunction2Id(u)}while(0);!o&&r&&this.sendAdapter(r)}},{key:"convertFunction2Id",value:function(e){var t=e;if("function"==typeof e){var n=this.generateId();this.contextCallbackMap[n]=e,t=n}else if(e&&"object"===(void 0===e?"undefined":r(e)))for(var o in e){var i=e[o];if("function"==typeof i){var a=this.generateId();this.contextCallbackMap[a]=i,e[o]=a}else i&&"object"===(void 0===i?"undefined":r(i))&&(e[o]=this.convertFunction2Id(i))}return t}},{key:"callRegisterFunction",value:function(e){var t=this,n=void 0,r=!1;do{if(!e)break;var o=e.method;if(!o)break;var i=this.getNow();if(n={s_rid:e.s_rid,action:"remote_client_callback",success:!1,rid:e.rid,method:e.method,src:e.src,timestamp:e.timestamp?e.timestamp.concat(i):[].concat(i)},!this.apis||!this.apis[o]){n.error="callRegisterFunction "+o+" is undefined";break}var a=void 0;try{a=this.apis[o].apply(null,[e.src].concat(e.args))}catch(e){n.error=e.message;break}if(a&&a.then){a.then((function(e){n.data=t.convertFunction2Id(e),n.success=!0,t.sendAdapter(n)})).catch((function(e){n.error=e.message,t.sendAdapter(n)})),r=!0;break}n.success=!0,n.data=this.convertFunction2Id(a)}while(0);f.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(n)}}]),h);function h(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h);var e=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(h.__proto__||Object.getPrototypeOf(h)).call(this));return e.rid=0,e.apis={},e.singleton=!1,e.retCallbackMap={},e.eventCallbackMap={},e.contextCallbackMap={},e}global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new p),s=global.__xdasIPCClienInstance,t.client=s},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Object.defineProperty(t,"__esModule",{value:!0});var o=n(52),i=n(12),a=n(27),s=n(84),c=n(19),u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(l,i.EventEmitter),function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(l,[{key:"isInprocess",value:function(){return this.inprocess}},{key:"getContext",value:function(){return this.context}},{key:"bind",value:function(){var e=this,t=new s.Parser,n=this.socket;n.on("data",(function(e){t.feed(e)})),n.on("end",(function(){a.information("socket is ended"),e.socket=null,e.emit("end")})),n.on("error",(function(e){a.error(e.message)})),t.on("message",(function(t){e.emit("message",t)})),this.parser=t}},{key:"send",value:function(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){a.error(e.message)}else a.information("This socket has been ended by the other party")}}]),l);function l(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),e=e||{};var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(l.__proto__||Object.getPrototypeOf(l)).call(this));if(t.inprocess=!1,t.context=void 0,e.context&&(t.context=e.context),e.socket)t.socket=e.socket,t.bind();else if(global.__xdasIPCServer)t.inprocess=!0;else{var n=c.getSockPath(e.socketPrex);t.socket=o.connect(n),t.bind()}return t}t.Client=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(11),o=n(30),i=n(31),a=n(104),s=(c.prototype.setPublicData=function(e){var t=e||{},n=r.combineJsonObject(i.getPublicData(),t);this.get().setPublicData(n)},c.prototype.get=function(){if(null===this.userBehaviorsStatServer){var e=o.gbStatAttrNames.userBehaviors;if(!o.gbStat.hasAttr(e)){var t=new a.UserBehaviorsStatServer(i.getStatServerUrl());o.gbStat.setAttr(e,t)}this.userBehaviorsStatServer=o.gbStat.getAttr(e)}return this.userBehaviorsStatServer},c);function c(){this.userBehaviorsStatServer=null,this.userBehaviorsStatServer=null}t.UserBehaviorsStatServerLoader=s,t.userBehaviorsStatServerLoader=new s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UserBehaviorsStatServer=t.userBehaviorsStatActions=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var o=n(29),i=n(11),a=t.userBehaviorsStatActions={loginPanelLoginClick:"loginPanelLoginClick",loginPanelPhoneClick:"loginPanelPhoneClick",loginPanelPhoneCode:"loginPanelPhoneCode",loginPanelHistorySelect:"loginPanelHistorySelect",loginPanelEmailShow:"loginPanelEmailShow",loginPanelEyeClick:"loginPanelEyeClick",loginPanelAccountClick:"loginPanelAccountClick",loginSuccess:"loginSuccess",loginFailure:"loginFailure",loginPanelForgetPasswordClick:"loginPanelForgetPasswordClick",loginPanelRegisterClick:"loginPanelRegisterClick",loginPanelQrClick:"loginPanelQrClick",loginPanelQrCodeShow:"loginPanelQrCodeShow",loginPanelQrCodeRefreshClick:"loginPanelQrCodeRefreshClick",loginPanelQrCodeRefresh:"loginPanelQrCodeRefresh",loginPanelQrCodeHover:"loginPanelQrCodeHover",loginPanelQrCodeAccoutClick:"loginPanelQrCodeAccoutClick",thrLoginClick:"thrLoginClick",websdkShow:"websdkShow",websdkClose:"websdkClose",registPanelShow:"registPanelShow",registPanelPhoneCode:"registPanelPhoneCode",registPanelRegistClick:"registPanelRegistClick",registPanelRegistSuccess:"registPanelRegistSuccess",registPanelRegistFail:"registPanelRegistFail",registPanelRegistAgree:"registPanelRegistAgree",registPanelLoginClick:"registPanelLoginClick",lastLoginType:"lastLoginType"};function s(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),this._statServer=new o.StatServer(e,"websdk-user-behaviors2")}t.UserBehaviorsStatServer=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(s,[{key:"setPublicData",value:function(e){return this._statServer.setPublicData(e)}},{key:"stat",value:function(e){return this._statServer.stat(e)}},{key:"statLoginResult",value:function(e,t){if(t){var n={extData:{}},r=n.extData,o=(new Date).getTime();switch("number"==typeof t.beginTime&&(o-=t.beginTime),r.costTime=o,t.type){case 0:n.action=e?a.loginSuccess:a.loginFailure,r.isAuto="number"==typeof t.isAuto?t.isAuto:0;break;case 1:n.action=e?a.registPanelRegistSuccess:a.registPanelRegistFail;break;default:n=null}null!==n&&(r.mode=(0,i.forceToString)(t.mode,""),e||void 0===t.errorCode||(r.errorCode=(0,i.forceToString)(t.errorCode,"")),this.stat(n))}}}]),s)}]);