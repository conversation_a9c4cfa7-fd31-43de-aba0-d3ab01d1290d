class_name CardManager
extends Node

# 卡牌管理器 - 负责创建、洗牌、发牌等操作

var all_cards: Array[Card] = []
var deck: Array[Card] = []
var landlord_cards: Array[Card] = []  # 地主牌（3张）

var card_scene = preload("res://scenes/Card.tscn")

signal deck_created()
signal cards_dealt()

func _ready():
	create_deck()

func create_deck():
	"""创建一副完整的扑克牌"""
	all_cards.clear()
	deck.clear()
	
	# 创建普通牌 (3-K, A, 2) × 4种花色
	for suit in [Card.Suit.SPADES, Card.Suit.HEARTS, Card.Suit.CLUBS, Card.Suit.DIAMONDS]:
		for rank in range(Card.Rank.THREE, Card.Rank.TWO + 1):
			var card = create_card(suit, rank)
			all_cards.append(card)
			deck.append(card)
	
	# 添加大小王
	var small_joker = create_card(Card.Suit.JOKER, Card.Rank.SMALL_JOKER)
	var big_joker = create_card(Card.Suit.JOKER, Card.Rank.BIG_JOKER)
	
	all_cards.append(small_joker)
	all_cards.append(big_joker)
	deck.append(small_joker)
	deck.append(big_joker)
	
	print("创建了 %d 张牌" % deck.size())
	deck_created.emit()

func create_card(suit: Card.Suit, rank: Card.Rank) -> Card:
	"""创建单张卡牌"""
	var card_instance = card_scene.instantiate()
	card_instance.suit = suit
	card_instance.rank = rank
	return card_instance

func shuffle_deck():
	"""洗牌"""
	deck.shuffle()
	print("洗牌完成")

func deal_cards(players: Array[Player]):
	"""发牌给玩家"""
	if players.size() != 3:
		print("错误：需要3个玩家")
		return
	
	shuffle_deck()
	
	# 每个玩家发17张牌
	var card_index = 0
	for i in range(17):
		for player in players:
			if card_index < deck.size():
				var card = deck[card_index]
				player.add_card(card)
				card_index += 1
	
	# 剩余3张作为地主牌
	landlord_cards.clear()
	for i in range(3):
		if card_index < deck.size():
			landlord_cards.append(deck[card_index])
			card_index += 1
	
	print("发牌完成，每人17张，地主牌3张")
	cards_dealt.emit()

func give_landlord_cards(landlord: Player):
	"""将地主牌给地主"""
	for card in landlord_cards:
		landlord.add_card(card)
	landlord_cards.clear()
	print("地主获得3张底牌")

func get_card_combinations(cards: Array[Card]) -> Dictionary:
	"""分析卡牌组合类型"""
	if cards.is_empty():
		return {"type": "none", "value": 0}
	
	var count = cards.size()
	var ranks = []
	for card in cards:
		ranks.append(card.get_card_value())
	
	ranks.sort()
	
	match count:
		1:
			return {"type": "single", "value": ranks[0]}
		2:
			if ranks[0] == ranks[1]:
				return {"type": "pair", "value": ranks[0]}
			else:
				return {"type": "invalid", "value": 0}
		3:
			if ranks[0] == ranks[1] and ranks[1] == ranks[2]:
				return {"type": "triple", "value": ranks[0]}
			else:
				return {"type": "invalid", "value": 0}
		4:
			if ranks[0] == ranks[1] and ranks[1] == ranks[2] and ranks[2] == ranks[3]:
				return {"type": "bomb", "value": ranks[0]}
			else:
				return {"type": "invalid", "value": 0}
		_:
			# 更复杂的组合（顺子、连对等）可以在这里添加
			return {"type": "complex", "value": ranks[0]}

func is_valid_combination(cards: Array[Card]) -> bool:
	"""检查卡牌组合是否有效"""
	var combo = get_card_combinations(cards)
	return combo["type"] != "invalid"

func can_beat(current_cards: Array[Card], previous_cards: Array[Card]) -> bool:
	"""检查当前出牌是否能压过上一手牌"""
	if previous_cards.is_empty():
		return true
	
	var current_combo = get_card_combinations(current_cards)
	var previous_combo = get_card_combinations(previous_cards)
	
	# 炸弹可以压过任何非炸弹牌型
	if current_combo["type"] == "bomb" and previous_combo["type"] != "bomb":
		return true
	
	# 同类型比较大小
	if current_combo["type"] == previous_combo["type"]:
		return current_combo["value"] > previous_combo["value"]
	
	return false

func reset_deck():
	"""重置牌堆"""
	deck = all_cards.duplicate()
	landlord_cards.clear()
	print("牌堆已重置")

func get_remaining_cards() -> int:
	"""获取剩余卡牌数量"""
	return deck.size()

func get_landlord_cards() -> Array[Card]:
	"""获取地主牌"""
	return landlord_cards.duplicate()

func create_card_display_text(cards: Array[Card]) -> String:
	"""创建卡牌显示文本"""
	if cards.is_empty():
		return ""
	
	var text_parts = []
	for card in cards:
		text_parts.append(card.get_card_display_text())
	
	return " ".join(text_parts)

func sort_cards_by_value(cards: Array[Card]) -> Array[Card]:
	"""按卡牌大小排序"""
	var sorted_cards = cards.duplicate()
	sorted_cards.sort_custom(func(a: Card, b: Card): return a.get_card_value() < b.get_card_value())
	return sorted_cards

func find_cards_by_rank(cards: Array[Card], rank: Card.Rank) -> Array[Card]:
	"""查找指定点数的卡牌"""
	var found_cards = []
	for card in cards:
		if card.rank == rank:
			found_cards.append(card)
	return found_cards

func has_bomb(cards: Array[Card]) -> bool:
	"""检查是否有炸弹"""
	var rank_counts = {}
	for card in cards:
		var rank = card.rank
		rank_counts[rank] = rank_counts.get(rank, 0) + 1
		if rank_counts[rank] >= 4:
			return true
	return false
