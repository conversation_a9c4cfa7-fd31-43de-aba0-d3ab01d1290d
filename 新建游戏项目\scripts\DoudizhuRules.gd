extends Node
class_name <PERSON><PERSON><PERSON><PERSON><PERSON>ules

# 斗地主游戏规则验证系统

# 牌型枚举
enum CardType {
	INVALID,        # 无效牌型
	SINGLE,         # 单张
	PAIR,           # 对子
	TRIPLE,         # 三张
	TRIPLE_SINGLE,  # 三带一
	TRIPLE_PAIR,    # 三带二
	STRAIGHT,       # 顺子
	PAIR_STRAIGHT,  # 连对
	TRIPLE_STRAIGHT,# 飞机
	BOMB,           # 炸弹
	ROCKET          # 火箭（双王）
}

# 分析牌型
static func analyze_cards(cards) -> Dictionary:
	if cards.size() == 0:
		return {"type": CardType.INVALID, "value": 0, "length": 0}
	
	# 统计每种牌的数量
	var rank_counts = {}
	for card in cards:
		var rank = card.rank
		if rank_counts.has(rank):
			rank_counts[rank] += 1
		else:
			rank_counts[rank] = 1
	
	var counts = rank_counts.values()
	counts.sort()
	var ranks = rank_counts.keys()
	ranks.sort_custom(func(a, b): return get_card_value(a) < get_card_value(b))
	
	# 分析牌型
	match cards.size():
		1:
			return analyze_single(cards[0])
		2:
			return analyze_pair(cards, rank_counts)
		3:
			return analyze_triple(cards, rank_counts)
		4:
			return analyze_four_cards(cards, rank_counts)
		5:
			return analyze_five_cards(cards, rank_counts)
		_:
			return analyze_complex(cards, rank_counts)

static func analyze_single(card) -> Dictionary:
	return {
		"type": CardType.SINGLE,
		"value": get_card_value(card.rank),
		"length": 1
	}

static func analyze_pair(cards, rank_counts) -> Dictionary:
	# 检查是否是火箭（双王）
	if cards.size() == 2:
		var has_small_joker = false
		var has_big_joker = false
		for card in cards:
			if card.rank == 16:  # SMALL_JOKER
				has_small_joker = true
			elif card.rank == 17:  # BIG_JOKER
				has_big_joker = true
		
		if has_small_joker and has_big_joker:
			return {"type": CardType.ROCKET, "value": 1000, "length": 2}
	
	# 检查是否是对子
	if rank_counts.size() == 1 and rank_counts.values()[0] == 2:
		var rank = rank_counts.keys()[0]
		return {
			"type": CardType.PAIR,
			"value": get_card_value(rank),
			"length": 2
		}
	
	return {"type": CardType.INVALID, "value": 0, "length": 0}

static func analyze_triple(cards, rank_counts) -> Dictionary:
	if rank_counts.size() == 1 and rank_counts.values()[0] == 3:
		var rank = rank_counts.keys()[0]
		return {
			"type": CardType.TRIPLE,
			"value": get_card_value(rank),
			"length": 3
		}
	
	return {"type": CardType.INVALID, "value": 0, "length": 0}

static func analyze_four_cards(cards, rank_counts) -> Dictionary:
	var counts = rank_counts.values()
	counts.sort()
	
	# 炸弹
	if counts == [4]:
		var rank = rank_counts.keys()[0]
		return {
			"type": CardType.BOMB,
			"value": get_card_value(rank),
			"length": 4
		}
	
	# 三带一
	if counts == [1, 3]:
		var triple_rank = null
		for rank in rank_counts.keys():
			if rank_counts[rank] == 3:
				triple_rank = rank
				break
		
		return {
			"type": CardType.TRIPLE_SINGLE,
			"value": get_card_value(triple_rank),
			"length": 4
		}
	
	return {"type": CardType.INVALID, "value": 0, "length": 0}

static func analyze_five_cards(cards, rank_counts) -> Dictionary:
	var counts = rank_counts.values()
	counts.sort()
	
	# 三带二
	if counts == [2, 3]:
		var triple_rank = null
		for rank in rank_counts.keys():
			if rank_counts[rank] == 3:
				triple_rank = rank
				break
		
		return {
			"type": CardType.TRIPLE_PAIR,
			"value": get_card_value(triple_rank),
			"length": 5
		}
	
	# 顺子
	if is_straight(rank_counts):
		var min_rank = rank_counts.keys().min()
		return {
			"type": CardType.STRAIGHT,
			"value": get_card_value(min_rank),
			"length": 5
		}
	
	return {"type": CardType.INVALID, "value": 0, "length": 0}

static func analyze_complex(cards, rank_counts) -> Dictionary:
	var counts = rank_counts.values()
	counts.sort()
	
	# 检查顺子
	if counts.all(func(x): return x == 1) and is_straight(rank_counts):
		var min_rank = rank_counts.keys().min()
		return {
			"type": CardType.STRAIGHT,
			"value": get_card_value(min_rank),
			"length": cards.size()
		}
	
	# 检查连对
	if counts.all(func(x): return x == 2) and is_consecutive_pairs(rank_counts):
		var min_rank = rank_counts.keys().min()
		return {
			"type": CardType.PAIR_STRAIGHT,
			"value": get_card_value(min_rank),
			"length": cards.size()
		}
	
	# 检查飞机
	var triple_count = 0
	for count in counts:
		if count == 3:
			triple_count += 1
	
	if triple_count >= 2 and is_consecutive_triples(rank_counts):
		var triple_ranks = []
		for rank in rank_counts.keys():
			if rank_counts[rank] == 3:
				triple_ranks.append(rank)
		triple_ranks.sort_custom(func(a, b): return get_card_value(a) < get_card_value(b))
		
		return {
			"type": CardType.TRIPLE_STRAIGHT,
			"value": get_card_value(triple_ranks[0]),
			"length": cards.size()
		}
	
	return {"type": CardType.INVALID, "value": 0, "length": 0}

static func is_straight(rank_counts) -> bool:
	if rank_counts.size() < 5:
		return false
	
	var ranks = rank_counts.keys()
	ranks.sort_custom(func(a, b): return get_card_value(a) < get_card_value(b))
	
	# 不能包含2和王
	for rank in ranks:
		if rank >= 15:  # TWO, SMALL_JOKER, BIG_JOKER
			return false
	
	# 检查是否连续
	for i in range(1, ranks.size()):
		if get_card_value(ranks[i]) != get_card_value(ranks[i-1]) + 1:
			return false
	
	return true

static func is_consecutive_pairs(rank_counts) -> bool:
	if rank_counts.size() < 3:
		return false
	
	var ranks = rank_counts.keys()
	ranks.sort_custom(func(a, b): return get_card_value(a) < get_card_value(b))
	
	# 不能包含2和王
	for rank in ranks:
		if rank >= 15:
			return false
	
	# 检查是否连续
	for i in range(1, ranks.size()):
		if get_card_value(ranks[i]) != get_card_value(ranks[i-1]) + 1:
			return false
	
	return true

static func is_consecutive_triples(rank_counts) -> bool:
	var triple_ranks = []
	for rank in rank_counts.keys():
		if rank_counts[rank] == 3:
			triple_ranks.append(rank)
	
	if triple_ranks.size() < 2:
		return false
	
	triple_ranks.sort_custom(func(a, b): return get_card_value(a) < get_card_value(b))
	
	# 不能包含2和王
	for rank in triple_ranks:
		if rank >= 15:
			return false
	
	# 检查是否连续
	for i in range(1, triple_ranks.size()):
		if get_card_value(triple_ranks[i]) != get_card_value(triple_ranks[i-1]) + 1:
			return false
	
	return true

static func get_card_value(rank) -> int:
	# 返回牌的大小值，用于比较
	if rank == 17:  # BIG_JOKER
		return 17
	elif rank == 16:  # SMALL_JOKER
		return 16
	elif rank == 15:  # TWO
		return 15
	elif rank == 14:  # ACE
		return 14
	else:
		return rank

# 比较两手牌的大小
static func can_beat(current_cards, last_cards) -> bool:
	if last_cards.size() == 0:
		return true  # 没有上家出牌，任何牌都可以出
	
	var current_type = analyze_cards(current_cards)
	var last_type = analyze_cards(last_cards)
	
	# 火箭可以打任何牌
	if current_type.type == CardType.ROCKET:
		return true
	
	# 炸弹可以打除火箭外的任何牌
	if current_type.type == CardType.BOMB and last_type.type != CardType.ROCKET:
		return true
	
	# 同类型牌型比较
	if current_type.type == last_type.type and current_type.length == last_type.length:
		return current_type.value > last_type.value
	
	return false
