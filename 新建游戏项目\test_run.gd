extends Node

# 详细的运行测试脚本
func _ready():
	print("=== 游戏运行测试开始 ===")

	# 测试1: 检查所有脚本是否能正常加载
	test_script_loading()

	# 测试2: 检查场景是否能正常实例化
	test_scene_instantiation()

	# 测试3: 测试主场景的节点路径
	test_main_scene_nodes()

	# 测试4: 测试游戏逻辑
	test_game_logic()

	print("=== 游戏运行测试完成 ===")

func test_script_loading():
	print("\n--- 测试脚本加载 ---")

	var scripts_to_test = [
		"res://scripts/Card.gd",
		"res://scripts/Deck.gd",
		"res://scripts/Player.gd",
		"res://scripts/GameManager.gd",
		"res://scripts/MainController.gd",
		"res://scripts/CardArea.gd",
		"res://scripts/AudioManager.gd",
		"res://scripts/GameStats.gd",
		"res://scripts/SaveSystem.gd",
		"res://scripts/GameConfig.gd"
	]

	for script_path in scripts_to_test:
		var script = load(script_path)
		if script:
			print("✓ ", script_path, " 加载成功")
		else:
			print("✗ ", script_path, " 加载失败")

func test_scene_instantiation():
	print("\n--- 测试场景实例化 ---")

	var scenes_to_test = [
		"res://scenes/Card.tscn",
		"res://scenes/Main.tscn"
	]

	for scene_path in scenes_to_test:
		var scene = load(scene_path)
		if scene:
			var instance = scene.instantiate()
			if instance:
				print("✓ ", scene_path, " 实例化成功")
				instance.queue_free()
			else:
				print("✗ ", scene_path, " 实例化失败")
		else:
			print("✗ ", scene_path, " 加载失败")

func test_main_scene_nodes():
	print("\n--- 测试主场景节点路径 ---")

	var main_scene = load("res://scenes/Main.tscn")
	if not main_scene:
		print("✗ 无法加载主场景")
		return

	var main_instance = main_scene.instantiate()
	if not main_instance:
		print("✗ 无法实例化主场景")
		return

	# 测试关键节点路径
	var node_paths = [
		"GameManager",
		"GameAreas/PlayerHandArea",
		"GameAreas/AI1HandArea",
		"GameAreas/AI2HandArea",
		"GameAreas/PlayArea",
		"UI/GameInfo/VBoxContainer/GameStateLabel",
		"UI/Controls/VBoxContainer/PlayButton"
	]

	for path in node_paths:
		var node = main_instance.get_node_or_null(path)
		if node:
			print("✓ 节点路径 ", path, " 存在")
		else:
			print("✗ 节点路径 ", path, " 不存在")

	main_instance.queue_free()

func test_game_logic():
	print("\n--- 测试游戏逻辑 ---")

	# 测试卡牌创建
	var CardScript = load("res://scripts/Card.gd")
	var card = CardScript.new()
	card.suit = 0  # SPADES
	card.rank = 14  # ACE
	print("✓ 卡牌创建成功: ", card.get_card_name())

	# 测试牌组
	var DeckScript = load("res://scripts/Deck.gd")
	var deck = DeckScript.new()
	deck.initialize_deck()
	print("✓ 牌组创建成功，共 ", deck.get_card_count(), " 张牌")

	# 测试玩家
	var PlayerScript = load("res://scripts/Player.gd")
	var player = PlayerScript.new()
	player.player_name = "测试玩家"
	print("✓ 玩家创建成功: ", player.player_name)
