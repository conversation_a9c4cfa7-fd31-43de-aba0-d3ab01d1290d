(window.webpackJsonp=window.webpackJsonp||[]).push([[15],{1370:function(e,t,i){var a=i(1445);"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,i(90).default)("57af285a",a,!0,{sourceMap:!1})},1444:function(e,t,i){"use strict";var a=i(1370);i.n(a).a},1445:function(e,t,i){(t=i(89)(!1)).push([e.i,'body .xly-dialog-share{padding:0;width:500px;height:300px}body .xly-dialog-share h2{padding:18px 18px 12px;border-bottom:1px solid var(--color-border);box-sizing:border-box}body .xly-dialog-share .td-select+.xly-dialog-share__label{margin-left:32px}body .xly-dialog-share .td-select-group{width:144px}body .xly-dialog-share .td-input{display:flex;align-items:center}body .xly-dialog-share .td-input__label{display:inline-block;margin-right:10px;width:48px;text-align:right}body .xly-dialog-share .td-dropdown-menu{max-height:86px;box-shadow:0 0 10px 0 rgba(26,26,26,.2);border:unset;overflow-y:auto}body .xly-dialog-share .td-dialog__body{display:grid;grid-template-columns:1fr 220px;grid-template-areas:"form code" "footer footer";height:190px}body .xly-dialog-share .xly-input--link{display:flex;align-items:center;flex:1}body .xly-dialog-share .xly-input--link .td-input__inner{flex:1}body .xly-dialog-share .xly-input--code .td-input__inner{width:72px}body .xly-dialog-share .xly-icon-success-fill{position:relative;top:3px;margin-right:6px;color:#40d17a;font-size:20px}body .xly-dialog-share h2{text-align:left}body .xly-dialog-share .xly-dialog__button .td-button{margin-left:12px}body .xly-dialog-share .xly-dialog__footer{padding:18px;grid-area:footer;box-sizing:border-box}.xly-dialog-share__name{display:inline-block;max-width:17em;vertical-align:top;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}.xly-dialog-share__form{grid-area:form;width:272px;padding:0 18px;margin-top:10px;box-sizing:border-box}.xly-dialog-share__form .xly-dialog-share__form-item{display:flex;align-items:center;flex-wrap:wrap;margin-bottom:12px}.xly-dialog-share__form .xly-dialog-share__code{padding-left:60px;margin-top:6px;width:100%;box-sizing:border-box}.xly-dialog-share__form .td-select:nth-child(0){margin-right:32px}.xly-dialog-share__form .td-button{display:block;margin:0 auto;width:150px;font-size:12px}.xly-dialog-share__form-button{position:relative;margin-top:25px}.xly-dialog-share__form-button p{position:absolute;top:-23px;left:0;display:flex;justify-content:center;align-items:center;width:100%;height:20px;color:#38b878;font-size:13px}.xly-dialog-share__code{position:relative;grid-area:code;padding-top:12px;text-align:center}.xly-dialog-share__code:before{position:absolute;top:50%;left:0;margin-top:-72px;width:1px;height:145px;border-left:1px dashed var(--color-border);content:""}.xly-dialog-share__code .xly-dialog-share__tips{margin:3px 0 0;width:100%;line-height:15px}.xly-dialog-share__code p{line-height:18px}.xly-dialog-share__code .td-button{margin-top:8px}.xly-dialog-share__code-img{margin:0 auto 3px;width:120px;height:120px}.xly-dialog-share__code-img img{width:100%;height:100%}.xly-dialog-share__fail{padding-top:60px;height:98px}.xly-dialog-share__fail i{color:var(--color-secondary);font-size:36px}.xly-dialog-share__label{width:60px}.xly-dialog-share__tips{margin:0 0 12px 27px;font-size:11px;color:var(--color-secondary)}body .xly-dialog-share .td-radio+.xly-dialog-share__tips{margin:3px 0 12px 80px}.xly-dialog-share__note{width:100%;line-height:36px;text-align:center;font-size:11px;color:var(--color-secondary);background:var(--background)}.xly-dialog-share__footer{padding-top:6px;width:100%}.xly-dialog-share__footer p{margin-top:12px;text-align:center;font-size:11px;color:var(--color-secondary)}',""]),e.exports=t},1514:function(e,t,i){"use strict";i.r(t);i(10),i(11),i(32),i(20),i(7),i(471);var a=i(33),s=i(40),r=i(70),n=i(71),o=i(37),l=(i(4),i(29)),d=i(18),c=i(13),h=i(230),p=i(298),u=i(1),x=i(2);function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,a=Object(o.a)(e);if(t){var s=Object(o.a)(this).constructor;i=Reflect.construct(a,arguments,s)}else i=a.apply(this,arguments);return Object(n.a)(this,i)}}var g=function(e,t,i,a){var s,r=arguments.length,n=r<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,i):a;if("object"===("undefined"==typeof Reflect?"undefined":Object(l.a)(Reflect))&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,i,a);else for(var o=e.length-1;o>=0;o--)(s=e[o])&&(n=(r<3?s(n):r>3?s(t,i,n):s(t,i))||n);return r>3&&n&&Object.defineProperty(t,i,n),n},f=function(e,t,i,a){return new(i||(i=Promise))((function(s,r){function n(e){try{l(a.next(e))}catch(e){r(e)}}function o(e){try{l(a.throw(e))}catch(e){r(e)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(n,o)}l((a=a.apply(e,t||[])).next())}))},y=function(e){Object(r.a)(i,e);var t=_(i);function i(){var e;return Object(a.a)(this,i),(e=t.apply(this,arguments)).isCreating=!1,e.isCreated=!1,e.visible=!0,e.timesOptions=Object.freeze(["不限","1次","2次","3次","4次","5次","6次","7次","8次","9次","10次","11次","12次","13次","14次","15次","16次","17次","18次","19次","20次"]),e.timesValue="不限",e.daysOptions=Object.freeze(["不限","1天","2天","3天","4天","5天","6天","7天"]),e.daysValue="不限",e.shareId="",e.shareLink="",e.shareCode="",e.tailText="",e.isGenerateQRCodeFail=!1,e.files=[],e.qrcode=null,e}return Object(s.a)(i,[{key:"createShareLink",value:function(){return f(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.$eventTrack("share_create_pop_click",{clickid:"create_share",num_limit:this.timesIndex,time_limit:this.daysIndex}),!0!==this.isCreating){e.next=3;break}return e.abrupt("return");case 3:return this.isCreating=!0,e.prev=4,e.next=7,Object(h.e)({file_ids:this.fileIds,share_to:"copy",title:"云盘资源分享",restore_limit:String(0===this.timesIndex?-1:this.timesIndex),expiration_days:String(0===this.daysIndex?-1:this.daysIndex)});case 7:if(t=e.sent,this.isCreated=!0,this.shareLink=t.share_url||"",this.shareCode=t.pass_code||"",this.shareId=t.share_id||"",this.copyIntoClipboard(this.shareLink,this.shareCode),e.prev=13,!this.shareId){e.next=18;break}return e.next=17,this.generateShareQRCode(this.shareId,this.shareCode);case 17:this.qrcode=e.sent;case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(13),this.$raven.error(e.t0),this.isGenerateQRCodeFail=!0;case 24:return e.prev=24,this.$eventTrack("share_finish",{result:"success",num_limit:this.timesIndex,time_limit:this.daysIndex,miniapp_result:this.isGenerateQRCodeFail?"fail":"success"}),e.finish(24);case 27:e.next=33;break;case 29:e.prev=29,e.t1=e.catch(4),this.$message({message:Object(u.getErrorDescription)(e.t1),type:"error",position:"middle",duration:5e3}),this.$eventTrack("share_finish",{result:"fail",num_limit:this.timesIndex,time_limit:this.daysIndex});case 33:return e.prev=33,this.isCreating=!1,e.finish(33);case 36:case"end":return e.stop()}}),e,this,[[4,29,33,36],[13,20,24,27]])})))}},{key:"mounted",value:function(){this.$eventTrack("share_create_pop_show")}},{key:"generateShareQRCode",value:function(e,t){return f(this,void 0,void 0,regeneratorRuntime.mark((function i(){var a;return regeneratorRuntime.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,Object(h.b)({page:"pages/share/index",scene:"".concat(e,"/").concat(t),width:120});case 2:return a=i.sent,i.abrupt("return",new Promise((function(e){var t=new FileReader;t.addEventListener("loadend",(function(){var i=t.result;e(i)})),a instanceof Blob&&t.readAsDataURL(a)})));case 4:case"end":return i.stop()}}),i)})))}},{key:"copyIntoClipboard",value:function(e,t){var i=this.tailText;p.clipboard.writeText("链接：".concat(e,"\n提取码：").concat(t,"\n").concat(i))}},{key:"copyImageIntoClipboard",value:function(){return f(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.$eventTrack("share_create_result_pop_click",{result:this.isCreated?"success":"fail",clickid:"copy_2dcode",num_limit:this.timesIndex,time_limit:this.daysIndex,miniapp_result:this.isGenerateQRCodeFail?"fail":"success"}),!this.qrcode){e.next=7;break}return e.next=4,p.nativeImage.createFromDataURL(this.qrcode);case 4:t=e.sent,p.clipboard.writeImage(t),this.$message({id:"sharecopy",unique:!0,message:"复制成功",type:"success",position:"middle",duration:5e3});case 7:case"end":return e.stop()}}),e,this)})))}},{key:"focusInput",value:function(e){this.$refs[e].select()}},{key:"onCopyClicked",value:function(){this.$eventTrack("share_create_result_pop_click",{result:this.isCreated?"success":"fail",clickid:"copy_link",num_limit:this.timesIndex,time_limit:this.daysIndex,miniapp_result:this.isGenerateQRCodeFail?"fail":"success"}),this.copyIntoClipboard(this.shareLink,this.shareCode),this.$message({id:"sharecopy",unique:!0,message:"复制成功",type:"success",position:"middle",duration:5e3})}},{key:"onGenerateRetryClick",value:function(){return f(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,this.$eventTrack("share_create_result_pop_click",{result:this.isCreated?"success":"fail",clickid:"retry",num_limit:this.timesIndex,time_limit:this.daysIndex,miniapp_result:this.isGenerateQRCodeFail?"fail":"success"}),this.isGenerateQRCodeFail=!1,!this.shareId||!this.shareCode){e.next=7;break}return e.next=6,this.generateShareQRCode(this.shareId,this.shareCode);case 6:this.qrcode=e.sent;case 7:e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),this.isGenerateQRCodeFail=!0;case 12:case"end":return e.stop()}}),e,this,[[0,9]])})))}},{key:"closeWindow",value:function(){this.isCreated?this.$eventTrack("share_create_result_pop_click",{result:this.isCreated?"success":"fail",clickid:"close",num_limit:this.timesIndex,time_limit:this.daysIndex,miniapp_result:this.isGenerateQRCodeFail?"fail":"success"}):this.$eventTrack("share_create_pop_click",{clickid:"close"}),window.close()}},{key:"created",value:function(){return f(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.client.callRemoteClientFunction("ThunderPanPluginWebview","GetShareFiles");case 3:return t=e.sent,e.next=6,Object(x.getRemoteGlobalConfigValue)("pan","share",{tailText:"复制这段内容后打开手机迅雷App，查看更方便"});case 6:i=e.sent,a=i.tailText,void 0===t[1]?this.files=t[0]||[]:console.error(t[1]),a&&(this.tailText=a),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0),this.$raven.error(e.t0);case 15:case"end":return e.stop()}}),e,this,[[0,12]])})))}},{key:"timesIndex",get:function(){return this.timesOptions.indexOf(this.timesValue)}},{key:"daysIndex",get:function(){return this.daysOptions.indexOf(this.daysValue)}},{key:"fileIds",get:function(){return this.files.map((function(e){return e.id||""}))}},{key:"fileNames",get:function(){return this.files.map((function(e){return e.name||""})).join("，")}}]),i}(d.Vue),m=y=g([Object(d.Component)({head:{title:"分享文件"}})],y),v=(i(280),i(1444),i(72)),b=Object(v.a)(m,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("td-dialog",{attrs:{"custom-class":"xly-dialog-share",visible:e.visible},on:{"update:visible":function(t){e.visible=t},ok:function(t){e.visible=!1},close:e.closeWindow}},[i("h2",{staticStyle:{"-webkit-app-region":"drag"},attrs:{slot:"header"},slot:"header"},[e._v("\n    分享文件："),i("span",{staticClass:"xly-dialog-share__name"},[e._v(e._s(e.fileNames))]),e._v(e._s("（共"+e.files.length+"项）")+"\n  ")]),e._v(" "),i("div",{staticClass:"xly-dialog-share__form"},[i("ul",[e.isCreated?[i("li",{staticClass:"xly-dialog-share__form-item"},[i("i",{staticClass:"xly-icon-success-fill"}),e._v(" 分享链接以及提取码已复制\n          "),i("span",{staticClass:"xly-dialog-share__tips"},[e._v("可通过QQ、微信、微博等分享给好友")])]),e._v(" "),i("li",{staticClass:"xly-dialog-share__form-item"},[i("td-input",{ref:"link",staticClass:"xly-input--link",attrs:{label:"链接",readonly:"readonly"},on:{focus:function(t){return e.focusInput("link")}},model:{value:e.shareLink,callback:function(t){e.shareLink=t},expression:"shareLink"}})],1),e._v(" "),i("li",{staticClass:"xly-dialog-share__form-item"},[i("td-input",{ref:"code",staticClass:"xly-input--code",attrs:{label:"提取码",readonly:"readonly"},on:{focus:function(t){return e.focusInput("code")}},model:{value:e.shareCode,callback:function(t){e.shareCode=t},expression:"shareCode"}})],1)]:[i("li",{staticClass:"xly-dialog-share__form-item"},[i("span",{staticClass:"xly-dialog-share__label"},[e._v("分享形式")]),e._v(" "),i("td-radio",[e._v("有提取码")]),e._v(" "),i("span",{staticClass:"xly-dialog-share__tips"},[e._v("仅限拥有提取码者查看，更安全")])],1),e._v(" "),i("li",{staticClass:"xly-dialog-share__form-item"},[i("span",{staticClass:"xly-dialog-share__label"},[e._v("提取次数")]),e._v(" "),i("td-select",{attrs:{disabled:e.isCreating,placeholder:"请选择",options:e.timesOptions},model:{value:e.timesValue,callback:function(t){e.timesValue=t},expression:"timesValue"}})],1),e._v(" "),i("li",{staticClass:"xly-dialog-share__form-item"},[i("span",{staticClass:"xly-dialog-share__label"},[e._v("提取期限")]),e._v(" "),i("td-select",{attrs:{disabled:e.isCreating,placeholder:"请选择",options:e.daysOptions},model:{value:e.daysValue,callback:function(t){e.daysValue=t},expression:"daysValue"}})],1)]],2),e._v(" "),e.isCreated?i("div",{staticClass:"xly-dialog-share__form-button"},[i("td-button",{on:{click:e.onCopyClicked}},[e._v("\n        复制链接及提取码\n      ")])],1):e._e()]),e._v(" "),e.isCreated?i("div",{staticClass:"xly-dialog-share__code"},[e.isGenerateQRCodeFail?[i("div",{staticClass:"xly-dialog-share__fail"},[i("i",{staticClass:"xly-icon-note"}),e._v(" "),i("p",[e._v("二维码生成失败")])]),e._v(" "),i("td-button",{attrs:{other:""},on:{click:e.onGenerateRetryClick}},[e._v("\n        重新生成二维码\n      ")])]:[i("div",{staticClass:"xly-dialog-share__code-img"},[i("img",{directives:[{name:"show",rawName:"v-show",value:e.qrcode,expression:"qrcode"}],staticStyle:{"user-select":"none",margin:"-1px"},attrs:{src:e.qrcode,draggable:"false"}})]),e._v(" "),i("p",[e._v("分享给好友，扫码即可查看")]),e._v(" "),i("p",{staticClass:"xly-dialog-share__tips"},[e._v("\n        已含提取码，扫码后无需再次输入\n      ")]),e._v(" "),i("td-button",{attrs:{other:"",disabled:e.isGenerateQRCodeFail},on:{click:e.copyImageIntoClipboard}},[e._v("\n        复制二维码\n      ")])]],2):e._e(),e._v(" "),e.isCreated?e._e():i("div",{staticClass:"xly-dialog__footer"},[i("div",{staticClass:"xly-dialog__button"},[i("td-button",{attrs:{status:e.isCreating,"status-icon":e.isCreating?"loading":""},on:{click:e.createShareLink}},[e._v("\n        "+e._s(e.isCreating?"创建中":"创建链接")+"\n      ")]),e._v(" "),i("td-button",{attrs:{other:""},on:{click:e.closeWindow}},[e._v("\n        取消\n      ")])],1)]),e._v(" "),i("p",{staticClass:"xly-dialog-share__note",attrs:{slot:"footer"},slot:"footer"},[e._v("\n    配合净网行动，迅雷严厉打击不良、色情低俗信息的传播，如发现，或将封号处理。\n  ")])])}),[],!1,null,null,null);t.default=b.exports}}]);