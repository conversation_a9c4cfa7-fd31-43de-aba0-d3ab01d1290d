!function(e){function t(n){if(r[n])return r[n].exports;var i=r[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var r={};t.m=e,t.c=r,t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t.h="076023064fa53798dcba",t.cn="innerMain",t(t.s=215)}([function(e,t,r){"use strict";t.__esModule=!0,t["default"]=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t){var r=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=r)},function(e,t){var r=e.exports={version:"2.6.11"};"number"==typeof __e&&(__e=r)},function(e,t,r){var n=r(34)("wks"),i=r(22),o=r(1).Symbol,a="function"==typeof o;(e.exports=function(e){return n[e]||(n[e]=a&&o[e]||(a?o:i)("Symbol."+e))}).store=n},function(e,t,r){e.exports=!r(14)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t,r){var n=r(6);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,r){var n=r(1),i=r(2),o=r(19),a=r(9),s=r(10),u=function(e,t,r){var c,l,f,d=e&u.F,p=e&u.G,_=e&u.S,h=e&u.P,g=e&u.B,m=e&u.W,v=p?i:i[t]||(i[t]={}),y=v.prototype,b=p?n:_?n[t]:(n[t]||{}).prototype;p&&(r=t);for(c in r)(l=!d&&b&&b[c]!==undefined)&&s(v,c)||(f=l?b[c]:r[c],v[c]=p&&"function"!=typeof b[c]?r[c]:g&&l?o(f,n):m&&b[c]==f?function(e){var t=function(t,r,n){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,r)}return new e(t,r,n)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(f):h&&"function"==typeof f?o(Function.call,f):f,h&&((v.virtual||(v.virtual={}))[c]=f,e&u.R&&y&&!y[c]&&a(y,c,f)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},function(e,t,r){var n=r(5),i=r(47),o=r(36),a=Object.defineProperty;t.f=r(4)?Object.defineProperty:function(e,t,r){if(n(e),t=o(t,!0),n(r),i)try{return a(e,t,r)}catch(s){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},function(e,t,r){var n=r(8),i=r(20);e.exports=r(4)?function(e,t,r){return n.f(e,t,i(1,r))}:function(e,t,r){return e[t]=r,e}},function(e,t){var r={}.hasOwnProperty;e.exports=function(e,t){return r.call(e,t)}},function(e,t,r){"use strict";(function(t){function n(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){return function(){return t.apply(e,Array.prototype.slice.call(arguments,0))}}function o(e,t){return Array.prototype.slice.call(e,t||0)}function a(e,t){u(e,function(e,r){return t(e,r),!1})}function s(e,t){var r=c(e)?[]:{};return u(e,function(e,n){return r[n]=t(e,n),!1}),r}function u(e,t){if(c(e)){for(var r=0;r<e.length;r++)if(t(e[r],r))return e[r]}else for(var n in e)if(e.hasOwnProperty(n)&&t(e[n],n))return e[n]}function c(e){return null!=e&&"function"!=typeof e&&"number"==typeof e.length}function l(e){return e&&"[object Function]"==={}.toString.call(e)}function f(e){return e&&"[object Object]"==={}.toString.call(e)}var d=r(95),p=n(d),_=r(56),h=n(_),g=function(){return h["default"]?h["default"]:function(e,t,r,n){for(var i=1;i<arguments.length;i++)a(Object(arguments[i]),function(t,r){e[r]=t});return e}}(),m=function(){if(p["default"])return function(e,t,r,n){var i=o(arguments,1);return g.apply(this,[(0,p["default"])(e)].concat(i))};var e=function(){};return function(t,r,n,i){var a=o(arguments,1);return e.prototype=t,g.apply(this,[new e].concat(a))}}(),v=function(){return String.prototype.trim?function(e){return String.prototype.trim.call(e)}:function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}}(),y="undefined"!=typeof window?window:t;e.exports={assign:g,create:m,trim:v,bind:i,slice:o,each:a,map:s,pluck:u,isList:c,isFunction:l,isObject:f,Global:y}}).call(t,r(55))},function(e,t,r){e.exports={"default":r(72),__esModule:!0}},function(e,t,r){var n=r(41),i=r(29);e.exports=function(e){return n(i(e))}},function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0;var i=r(112),o=n(i),a=r(114),s=n(a),u="function"==typeof s["default"]&&"symbol"==typeof o["default"]?function(e){return typeof e}:function(e){return e&&"function"==typeof s["default"]&&e.constructor===s["default"]&&e!==s["default"].prototype?"symbol":typeof e};t["default"]="function"==typeof s["default"]&&"symbol"===u(o["default"])?function(e){return void 0===e?"undefined":u(e)}:function(e){return e&&"function"==typeof s["default"]&&e.constructor===s["default"]&&e!==s["default"].prototype?"symbol":void 0===e?"undefined":u(e)}},function(e,t){e.exports=!0},function(e,t){var r={}.toString;e.exports=function(e){return r.call(e).slice(8,-1)}},function(e,t){e.exports={}},function(e,t,r){var n=r(21);e.exports=function(e,t,r){if(n(e),t===undefined)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,i){return e.call(t,r,n,i)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){var r=0,n=Math.random();e.exports=function(e){return"Symbol(".concat(e===undefined?"":e,")_",(++r+n).toString(36))}},function(e,t,r){"use strict";(function(e){function r(){return window||e}function n(e){var t="";switch(e){case l.insideIframe:case l.outsideIframe:case l.node:t=e;break;default:t="default"}return t+"WebSdkGlobalObject_CA7FFF8A_0F5B_4654_822B_98B9E74F23DD"}function i(e){if(null===f){d=e;var t=n(e);f={},r()[t]=f}}function o(){if(null===f){var e=r();f=e[n(l.insideIframe)]||e[n(l.outsideIframe)]||e[n(l.pluginIndex)]}return f}function a(e){return o()[e]!==undefined}function s(e,t){o()[e]=t}function u(e){return o()[e]}function c(){return d}t.__esModule=!0,t.getGBObjName=n,t.init=i,t.hasAttr=a,t.setAttr=s,t.getAttr=u,t.getEnvType=c;var l=(t.gbAttrNames={config:"config",stat:"stat",platformInfo:"platformInfo",innerQuickLogin:"innerQuickLogin",clientFeatureApi:"clientFeatureApi"},t.gbEnvTypes={insideIframe:"insideIframe",outsideIframe:"outsideIframe",pluginIndex:"pluginIndex"}),f=null,d=undefined}).call(t,r(55))},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e}function o(e,t){for(var r=-1,n=null==e?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}function a(e){return document.getElementById(e)}function s(){return(65536*(1+Math.random())|0).toString(16).substring(1)}function u(e){for(var t="",r=e/4,n=0;n<r;n++)t+=s();return t}function c(e,t){undefined;return t>e.length?new Array(t-e.length+1).join("0")+e:e}function l(e){return e.getFullYear().toString()+c((e.getMonth()+1).toString(),2)+c(e.getDate().toString(),2)}t.__esModule=!0,t.BrowserVersion=t.BrowserType=t.mThunder=t.isWeixin=t.isXlMac=t.isXlx=t.isXl9=t.isXlMB=t.isXlPC=t.isMobile=t.UA=undefined;var f=r(15),d=n(f),p=r(0),_=n(p);t.id=a,t.S4=s,t.Guid=u,t.dateToDateString=l;var h=r(62),g=function(){function e(){(0,_["default"])(this,e),this.every=this.genLoop(),this.some=this.genLoop(!1)}return e.prototype.forEach=function(e,t){return i(e,t)},e.prototype.map=function(e,t){return o(e,t)},e.prototype.genLoop=function(){var e=!(arguments.length>0&&arguments[0]!==undefined)||arguments[0];return function(t,r){for(var n=-1,i=null==t?0:t.length;++n<i;){var o=r(t[n],n,t);if(0==e&&1==o)return!0;if(1==e&&0==o)return!1}return e}},e.prototype.has=function(e,t){return null!=e&&hasOwnProperty.call(e,t)},e.prototype.isEmpty=function(e){return e!=undefined&&("string"!=typeof e||!e.length)},e.prototype.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},e.prototype.decode=function(e){try{e=decodeURIComponent(e)}catch(t){e=unescape(e)}return e},e.prototype.escape=function(e){return e.replace(/([.*+?\^${}()|\[\]\/\\])/g,"\\$1")},e.prototype.getCookie=function(e){var t=document.cookie.match(RegExp("(?:^|;\\s*)"+this.escape(e)+"=([^;]*)"));return t?t[1]:""},e}(),m=t.UA=navigator.userAgent.toLocaleLowerCase(),v=t.isMobile=/mobile|android|iphone|ipad|iemobile/i.test(m),y=(t.isXlPC=/[ ]thunder(x?)\/[\d.]*/.test(m)||!!window["native"]&&!v,t.isXlMB=(/thunder/.test(m)||"undefined"!=typeof XLJSWebViewBridgeExport)&&v,t.isXl9=(0,h.checkIsXl9)(),t.isXlx=(0,h.checkIsXlx)(),t.isXlMac=/\bmac\b/.test(m)&&!!window.WebViewJavascriptBridge,t.isWeixin=/micromessenger/.test(m),t.mThunder=function(){return"undefined"!=typeof window.webkit&&(0,d["default"])(window.webkit.messageHandlers)&&"undefined"!=typeof window.webkit.messageHandlers.XLJSWebViewBridgeExport?{type:"ios",event:"XLJSWebViewBridgeExport"}:"undefined"!=typeof window.webkit&&(0,d["default"])(window.webkit.messageHandlers)&&"undefined"!=typeof window.webkit.messageHandlers.XLJSWebViewBridge?{type:"ios",event:"XLJSWebViewBridge"}:"undefined"!=typeof window.XLJSWebViewBridgeExport?{type:"android",event:"XLJSWebViewBridgeExport"}:"undefined"!=typeof window.XLJSWebViewBridge&&{type:"android",event:"XLJSWebViewBridge"}},["Trident","Electron","Android.Thunder","IOS.Thunder","Thunder","MicroMessenger","360","MQQBrowser","QQBrowser","UCBrowser","UBrowser","Metasr","Edge","Firefox","Maxthon","Chrome","Safari","mobile"]),b=function(e,t){var r=navigator.mimeTypes;for(var n in r)if(r[n][e]==t)return!0;return!1},w=function(){for(var e=y.length-1,t=0;t<=e;t++){var r=y[t];if("360"!==r){if(m.indexOf(r.toLowerCase())>-1)return r}else if(b("type","application/vnd.chromium.remoting-viewer"))return r}return"unknown"}(),E=(t.BrowserType=function(){return(v?"Mobile-":"PC-")+("Trident"==w?"IE":w)}(),t.BrowserVersion=function(){var e=w.toLowerCase();if(e.indexOf("unknown")>0)return"unknown";if("trident"==e)return m.indexOf("gecko")>0?"IE/11":"IE/"+m.match(/msie (\d+)/)[1];"360"!=e&&"android.thunder"!=e||(e="chrome"),"ios.thunder"==e&&(e="mobile");var t=new RegExp(e+"[ /][\\d.]+"),r=m.match(t);return r&&r.length>=0?r[0]:"unknown"}(),new g);t["default"]=E},function(e,t,r){var n=r(48),i=r(35);e.exports=Object.keys||function(e){return n(e,i)}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,r){var n=r(8).f,i=r(10),o=r(3)("toStringTag");e.exports=function(e,t,r){e&&!i(e=r?e:e.prototype,o)&&n(e,o,{configurable:!0,value:t})}},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:function(e){return e},r={};return h["default"].forEach(e,function(e){var n=e.split("="),i=n.shift(),o=n.join("=");r[i]=t(o)}),r}function o(e){return"object"===("undefined"==typeof HTMLElement?"undefined":(0,d["default"])(HTMLElement))?e instanceof HTMLElement:e&&"object"===(void 0===e?"undefined":(0,d["default"])(e))&&null!==e&&1===e.nodeType&&"string"==typeof e.nodeName}function a(e,t){var r;r="string"==typeof e?w["class"](e):e,o(r)?r.style.display=t:h["default"].forEach(r,function(e){e.style.display=t})}function s(e){if(document.all)e.click();else{var t=document.createEvent("MouseEvent");t.initEvent("click",!0,!0),e.dispatchEvent(t)}}function u(e){return 0!==e.clientWidth&&0!==e.clientHeight&&0!==e.style.opacity&&"hidden"!==e.style.visibility}t.__esModule=!0,t.TIMEOUT=undefined;var c=r(0),l=n(c),f=r(15),d=n(f);t._display=a,t._click=s,t._visible=u;var p=r(97),_=r(24),h=n(_),g=r(96),m=n(g),v=void 0,y=!1,b=(t.TIMEOUT="TIMEOUT",function(){function e(){(0,l["default"])(this,e),this.id=_.id,this.binders=[],this.bind=this.genBind(_.id,window),this.unbind=this.genUnBind(_.id),this.loadScript=this.genLoadScript(document),this.getJson=this.genGetJson(window,document),this.loadStyle=this.genLoadScript(document,"link",{rel:"stylesheet",type:"text/css",media:"all"}),this.isJumpkey=function(e){return!(!e||!e.length||192!==e.length)},this["class"]=function(e){return document.getElementsByClassName(e)},this.text=function(e,t){if(e){var r=!1;if(e.innerText!==v)r="innerText";else if(e.textContent!==v)r="textContent";else{if(e.value===v)throw new Error("not support dom innerText or textContent");r="value"}return t===v?e[r]:e[r]=t}},this.checkCss=function(e){return"css"==e.split(".").pop().split("?")[0]},this.checkMobile=function(e){return/^1[\d]{10}$/.exec(e)},this.checkOverseasMobile=function(e){return/^00[0-9]{8,15}$/.exec(e)},this.checkPassword=function(e){return/^(?![\d]+$)(?![a-zA-Z]+$)(?![\`\-\=\[\]\\\;\'\,\.\/\~!@#$%^&*()_+{}|:"<>?]+$)[\da-zA-Z\`\-\=\[\]\\\;\'\,\.\/\~!@#$%^&*()_+{}|:"<>?]{6,16}$/.exec(e)},this.checkMail=function(e){return/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.exec(e)},this.trim=function(e){return e.replace(/(^\s*)|(\s*$)/g,"")},this.getConfig=function(e){if((e=e.toUpperCase())in p.CONFIG)return p.CONFIG[e]},this.isClientLogin=function(){return!y&&p.CONFIG.SET_ROOT_DOMAIN?parent.xlQuickLogin.isClientLogin:y}}return e.prototype.randString=function(){return Math.random().toString(36).replace(/[^a-z0-9]+/g,"")},e.prototype.isSessionid=function(e){return!(!e||!e.length)},e.prototype.getCookie=function(e){var t=!(arguments.length>1&&arguments[1]!==undefined)||arguments[1],r=document.cookie,n=i(r.split("; "),function(e){return t&&e!==v&&(e=h["default"].decode(e)),e});if(e){var o=n[e];return o==v?"":o}return n},e.prototype.setCookie=function(e,t,r){var n,i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:p.CONFIG.DOMAIN,o=arguments[4],a=arguments[5],r=!!r&&new Date((new Date).getTime()+r).toGMTString();n=e+"="+escape(t),n+="; path="+(o||"/"),n+="; domain="+i,a&&(n+="; secure"),r&&(n+="; expires="+r),document.cookie=n},e.prototype.delCookie=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:p.CONFIG.DOMAIN,r=arguments[2],n=arguments[3];this.setCookie(e,"",-6e4,t,r,n)},e.prototype.genBind=function(e,t){var r=this;return function(n,i,o,a){function s(e){e=e||t.event,e.target||(e.target=e.srcElement,e.preventDefault=function(){this.returnValue=!1},e.stopPropagation=function(){this.cancelBubble=!0}),!1===o.call(a||this,e)&&(e.preventDefault(),e.stopPropagation())}if("function"==typeof o){if("string"==typeof n&&(n=e(n)),!n)throw new Error("bind on an undefined target");var u=i.split(".").shift();r.binders.push({obj:n,handler:s,type:i}),n.attachEvent?n.attachEvent("on"+u,s):n.addEventListener&&n.addEventListener(u,s,!1)}}},e.prototype.genUnBind=function(e){var t=this;return function(r,n){if("string"==typeof r&&(r=e(r)),!r)throw new Error("unbind on an undefined target");var i,o,a,s,u,c;for(u=t.binders.length-1;u>=0;u--)i=t.binders[u],i.obj===r&&(o=i.type.split("."),a=o.shift(),s=o.length>0&&o.join("."),(i.type===n||n===a||!1!==s&&s===n)&&(t.binders.splice(u,1),c=i,r.detachEvent?r.detachEvent("on"+a,i.handler):r.removeEventListener&&r.removeEventListener(a,i.handler,!1)));return c}},e.prototype.genLoadScript=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"script",r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{type:"text/javascript",language:"javascript"};return function(n,i,o){var a=e.createElement(t),s=!1;a.href=n,a.src=n;for(var u in r)a[u]=r[u],a.setAttribute(u,r[u]);for(var c in o)a[c]=o[c],a.setAttribute(c,o[c]);return a.onload=a.onreadystatechange=function(){s||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(s=!0,"function"==typeof i&&i())},e.getElementsByTagName("head")[0].appendChild(a),a}},e.prototype.getUrlParams=function(e){return i(e.substring(e.indexOf("?")+1,-1==e.indexOf("#")?e.length:e.indexOf("#")).split("&"))},e.prototype.registerPost=function(e,t,r){var n,i,o,a,s=this,u="http://i."+p.CONFIG.DOMAIN+"/login/2.5/post_callback.html",c="_submitIframe_"+Math.round(1e3*Math.random());p.CONFIG.SET_ROOT_DOMAIN&&p.CONFIG.DOMAIN;!0!==p.CONFIG.ALL_HTTPS&&"https:"!==location.protocol||(u=u.replace("http","https")),r||(r=function(){});var l="_"+Math.round(1e16*Math.random());window[l]="string"==typeof r?window[r]:r,n=document.createElement("form"),n.id="_postFrom_"+Math.round(1e3*Math.random()),n.style.display="none",n.style.position="absolute",n.method="post",n.action=e+"?domain="+p.CONFIG.DOMAIN+"&iframeUrl="+encodeURIComponent(u)+"&callback="+l+"&csrf_token="+s.getCsrfToken(),n.target=c,n.enctype="application/x-www-form-urlencoded",n.acceptCharset="UTF-8",t.domain=p.CONFIG.DOMAIN,t.response="iframe";for(o in t)i=document.createElement("input"),i.name=o,i.value=t[o],i.type="hidden",n.appendChild(i);document.body.appendChild(n);try{a=document.createElement("<iframe name='' + iframeId + ''>")}catch(d){a=document.createElement("iframe"),a.name=c}a.id=c,a.style.display="none",n.appendChild(a),document.body.appendChild(a);var f="_remove"+l;window[f]=function(){a.parentNode.removeChild(a),n.parentNode.removeChild(n),a=null,n=null,window[l]=null,window[f]=null},n.submit()},e.prototype.getCsrfToken=function(){return(0,m["default"])(this.getCookie("deviceid").slice(0,32))},e.prototype.appendUri=function(e,t){var r,n=[];for(r in t)n.push(r+"="+t[r]);return n=n.join("&"),e=-1===e.indexOf("?")?e+"?"+n:e+"&"+n},e.prototype.inArray=function(e,t){if("object"!=(void 0===t?"undefined":(0,d["default"])(t)))return!1;var r;for(r in t)if(t[r]===e)return!0;return!1},e.prototype.delVerifies=function(){var e=this;e.delCookie("VERIFY_KEY",p.CONFIG.DOMAIN),e.delCookie("verify_type",p.CONFIG.DOMAIN),e.delCookie("check_n",p.CONFIG.DOMAIN),e.delCookie("check_e",p.CONFIG.DOMAIN),e.delCookie("logindetail",p.CONFIG.DOMAIN),e.delCookie("result",p.CONFIG.DOMAIN)},e.prototype.genGetJson=function(e,t){return function(r,n,i,o){var a=t.createElement("script"),s=t.getElementsByTagName("head")[0],u="jsonp"+(new Date).getTime(),c=[];o=o||"callback";for(var l in n)c.push(l+"="+n[l]);c.push(o+"="+u),r+=(r.indexOf("?")>=0?"&":"?")+c.join("&"),e[u]=function(t){"string"==typeof i?e[i].call(e,t):i.call(e,t);try{delete e[u]}catch(r){}s.removeChild(a)},a.src=r,s.insertBefore(a,s.firstChild)}},e.prototype.isMainlandPhone=function(e){return!e||"0086"===e},e}()),w=new b;t["default"]=w},function(e,t){e.exports=function(e){if(e==undefined)throw TypeError("Can't call method on  "+e);return e}},function(e,t){var r=Math.ceil,n=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?n:r)(e)}},function(e,t,r){var n=r(34)("keys"),i=r(22);e.exports=function(e){return n[e]||(n[e]=i(e))}},function(e,t,r){var n=r(29);e.exports=function(e){return Object(n(e))}},function(e,t,r){var n=r(6),i=r(1).document,o=n(i)&&n(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},function(e,t,r){var n=r(2),i=r(1),o=i["__core-js_shared__"]||(i["__core-js_shared__"]={});(e.exports=function(e,t){return o[e]||(o[e]=t!==undefined?t:{})})("versions",[]).push({version:n.version,mode:r(16)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,r){var n=r(6);e.exports=function(e,t){if(!n(e))return e;var r,i;if(t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;if("function"==typeof(r=e.valueOf)&&!n(i=r.call(e)))return i;if(!t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,r){"use strict";t.__esModule=!0,t.GBHelper=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=r(23),a=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t["default"]=e,t}(o);t.GBHelper=function(){function e(t){(0,i["default"])(this,e),this._attrName=t,this._target=undefined}return e.prototype.getTarget=function(){return this._target===undefined&&(a.hasAttr(this._attrName)?this._target=a.getAttr(this._attrName):this.setTarget({})),this._target},e.prototype.setTarget=function(e){a.setAttr(this._attrName,e),this._target=a.getAttr(this._attrName)},e.prototype.hasAttr=function(e){return this.getTarget()[e]!==undefined},e.prototype.setAttr=function(e,t){this.getTarget()[e]=t},e.prototype.getAttr=function(e){return this.getTarget()[e]},e}()},function(e,t,r){var n=r(5),i=r(87),o=r(35),a=r(31)("IE_PROTO"),s=function(){},u=function(){var e,t=r(33)("iframe"),n=o.length;for(t.style.display="none",r(59).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;n--;)delete u.prototype[o[n]];return u()};e.exports=Object.create||function(e,t){var r;return null!==e?(s.prototype=n(e),r=new s,s.prototype=null,r[a]=e):r=u(),t===undefined?r:i(r,t)}},function(e,t,r){e.exports={"default":r(105),__esModule:!0}},function(e,t,r){var n=r(17);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},function(e,t,r){"use strict";function n(e){var t,r;this.promise=new e(function(e,n){if(t!==undefined||r!==undefined)throw TypeError("Bad Promise constructor");t=e,r=n}),this.resolve=i(t),this.reject=i(r)}var i=r(21);e.exports.f=function(e){return new n(e)}},function(e,t,r){t.f=r(3)},function(e,t,r){var n=r(1),i=r(2),o=r(16),a=r(43),s=r(8).f;e.exports=function(e){var t=i.Symbol||(i.Symbol=o?{}:n.Symbol||{});"_"==e.charAt(0)||e in t||s(t,e,{value:a.f(e)})}},function(e,t,r){var n=r(30),i=Math.min;e.exports=function(e){return e>0?i(n(e),9007199254740991):0}},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"",r=t;do{if(null===e||e===undefined)break;switch(void 0===e?"undefined":(0,_["default"])(e)){case"string":r=e;break;case"number":case"boolean":r=e.toString()}}while(!1);return r}function o(e){var t={};do{if(null===e||e===undefined){t={};break}switch(void 0===e?"undefined":(0,_["default"])(e)){case"object":for(var r in e)t[r]=o(e[r]);break;case"string":case"number":case"boolean":t=i(e,"")}}while(!1);return t}function a(e,t){var r=e;for(var n in t){var i=t[n];if("object"===(void 0===i?"undefined":(0,_["default"])(i))){var o=r[n];o===undefined||null===o?r[n]=i:r[n]=s(r[n],i)}else i!==undefined&&(r[n]=i)}return r}function s(e,t){return a(JSON.parse((0,d["default"])(e)),JSON.parse((0,d["default"])(t)))}function u(e){return e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()}function c(e){var t=null;try{t=JSON.parse(e)}catch(r){t=null}return t}function l(e){var t=undefined;try{t=(0,d["default"])(e)}catch(r){t=undefined}return t}t.__esModule=!0;var f=r(12),d=n(f),p=r(15),_=n(p);t.forceToString=i,t.forceJsonSimpleValueToString=o,t.combineJsonObject=s,t.dateToTimeString=u,t.parseJson=c,t.stringifyJson=l},function(e,t,r){e.exports=!r(4)&&!r(14)(function(){return 7!=Object.defineProperty(r(33)("div"),"a",{get:function(){return 7}}).a})},function(e,t,r){var n=r(10),i=r(13),o=r(75)(!1),a=r(31)("IE_PROTO");e.exports=function(e,t){var r,s=i(e),u=0,c=[];for(r in s)r!=a&&n(s,r)&&c.push(r);for(;t.length>u;)n(s,r=t[u++])&&(~o(c,r)||c.push(r));return c}},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){return{0:"PC",1:"WEB",3:"WAP",4:"MAC",10:"ANDROID",12:"LINUX"}[e]||"PC"}function o(e){var t=window.APPID!=undefined?window.APPID:"";if(m["default"].getCookie("appinfo")){t=JSON.parse(m["default"].getCookie("appinfo")).appid}return(0,p["default"])({v:"202",appid:t,appname:l(),devicemodel:v.BrowserVersion,devicename:v.BrowserType,hl:E.gbConfig.getHL(),osversion:navigator.platform,providername:L,networktype:L,sdkversion:E.gbConfig.getSDKVersion(),clientversion:E.gbConfig.getAppVersion(),devicesign:s(),platform:f(),entrance:k.getAttr(S.gbOtherInfoAttrNames.showLoginWndSource),format:T?"json":"cookie"},e)}function a(e){var t=window.APPID!=undefined?window.APPID:"";if(m["default"].getCookie("appinfo")){t=JSON.parse(m["default"].getCookie("appinfo")).appid}return""!=t&&t!=undefined||(t=m["default"].getCookie("appidstack").split(",").pop()),(0,p["default"])({appid:t,appName:l(),deviceModel:v.BrowserVersion,deviceName:v.BrowserType,hl:E.gbConfig.getHL(),OSVersion:navigator.platform,provideName:L,netWorkType:L,providerName:L,sdkVersion:E.gbConfig.getSDKVersion(),clientVersion:E.gbConfig.getAppVersion(),protocolVersion:"300",devicesign:s(),entrance:k.getAttr(S.gbOtherInfoAttrNames.showLoginWndSource),platformVersion:C.PlatformMap(),fromPlatformVersion:C.PlatformMap(),format:T?"json":"cookie",timestamp:(new Date).getTime()},e)}function s(){var e=m["default"].getCookie("deviceid");return e&&e!=undefined&&e.length>20?e:w["default"].enabled&&w["default"].has("deviceid")&&w["default"].get("deviceid").length>20?w["default"].get("deviceid").replace(/'/g,""):""}function u(e){var t=window.APPID!=undefined?window.APPID:"";if(m["default"].getCookie("appinfo")){t=JSON.parse(m["default"].getCookie("appinfo")).appid}return(0,p["default"])({appid:t,appName:l(),deviceModel:v.BrowserVersion,deviceName:v.BrowserType,hl:E.gbConfig.getHL(),OSVersion:navigator.platform,providerName:L,netWorkType:L,sdkVersion:E.gbConfig.getSDKVersion(),protocolVersion:"300",clientVersion:E.gbConfig.getAppVersion(),devicesign:s(),platformVersion:C.PlatformMap(),format:T?"json":"cookie"},e)}function c(e){var t=window.APPID!=undefined?window.APPID:"";if(m["default"].getCookie("appinfo")){t=JSON.parse(m["default"].getCookie("appinfo")).appid}return(0,p["default"])({appid:t,appName:l(),deviceModel:v.BrowserVersion,deviceName:v.BrowserType,hl:E.gbConfig.getHL(),OSVersion:navigator.platform,providerName:L,netWorkType:L,sdkVersion:E.gbConfig.getSDKVersion(),protocolVersion:"3.1",clientVersion:E.gbConfig.getAppVersion(),devicesign:s(),platformVersion:f(),format:T?"json":"cookie"},e)}function l(){var e=void 0,t=null;m["default"].getCookie("appinfo")&&(t=JSON.parse(m["default"].getCookie("appinfo")));var r=window.APPNAME||!1;try{if(e=window.parent.location.host,r&&(e=r),t&&(e=t.appname||t["package"]),!/^(IOS|ANDROID|MiniProgram|PC|LINUX)-/.test(e)){var n="";n=v.isMobile?"WAP":(0,y.checkAsPCFlow)()?"PC":f(),e=n+"-"+e}}catch(i){e="unknown"}return e}function f(){return v.isMobile?"WAP":(0,y.checkAsPCFlow)()?"PC":i(new URL(window.location.href).searchParams.get("platformVersion")||new URL(window.parent.location.href).searchParams.get("platformVersion"))}t.__esModule=!0;var d=r(52),p=n(d),_=r(0),h=n(_);t.baseParams=o,t.baseParams2=a,t.deviceid=s,t.baseParamsBeta=u,t.baseParamsRegister=c,t.getHostCategoryAppName=l,t.Platform=f;var g=r(28),m=n(g),v=r(24),y=(n(v),r(62)),b=r(77),w=n(b),E=r(60),S=r(123),I=r(38),T=(v.isXlMac,"file:"===location.protocol),k=new I.GBHelper(S.gbAttrNames.otherInfo),x=function(){function e(){(0,h["default"])(this,e),this.version=v.BrowserVersion,this.type=v.BrowserType,this.platform=f(),this.appname=l()}return e.prototype.PlatformMap=function(){var e={PC:"0",WEB:"1",WAP:"3",MAC:"4",ANDROID:"10",LINUX:"12"};return e[this.platform]?e[this.platform]:1},e}(),C=new x;t["default"]=C;var L="NONE"},function(e,t,r){"use strict";var n=r(85)(!0);r(53)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,r=this._i;return r>=t.length?{value:undefined,done:!0}:(e=n(t,r),this._i+=e.length,{value:e,done:!1})})},function(e,t,r){"use strict";function n(){var e=location.host.split("."),t=e.length;return e[t-2]+"."+e[t-1]}function i(){var e=n();return"file:"===location.protocol?[!0,e]:(document.domain!=e&&(document.domain=e),[!0,e])}t.__esModule=!0,t.getDomain=n,t.checkDomainAllowed=i},function(e,t,r){"use strict";t.__esModule=!0;var n=r(56),i=function(e){return e&&e.__esModule?e:{"default":e}}(n);t["default"]=i["default"]||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}},function(e,t,r){"use strict";var n=r(16),i=r(7),o=r(58),a=r(9),s=r(18),u=r(86),c=r(27),l=r(88),f=r(3)("iterator"),d=!([].keys&&"next"in[].keys()),p=function(){return this};e.exports=function(e,t,r,_,h,g,m){u(r,t,_);var v,y,b,w=function(e){if(!d&&e in T)return T[e];switch(e){case"keys":case"values":return function(){return new r(this,e)}}return function(){return new r(this,e)}},E=t+" Iterator",S="values"==h,I=!1,T=e.prototype,k=T[f]||T["@@iterator"]||h&&T[h],x=k||w(h),C=h?S?w("entries"):x:undefined,L="Array"==t?T.entries||k:k;if(L&&(b=l(L.call(new e)))!==Object.prototype&&b.next&&(c(b,E,!0),n||"function"==typeof b[f]||a(b,f,p)),S&&k&&"values"!==k.name&&(I=!0,x=function(){return k.call(this)}),n&&!m||!d&&!I&&T[f]||a(T,f,x),s[t]=x,s[E]=p,h)if(v={values:S?x:w("values"),keys:g?x:w("keys"),entries:C},m)for(y in v)y in T||o(T,y,v[y]);else i(i.P+i.F*(d||I),t,v);return v}},function(e,t,r){r(89);for(var n=r(1),i=r(9),o=r(18),a=r(3)("toStringTag"),s="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),u=0;u<s.length;u++){var c=s[u],l=n[c],f=l&&l.prototype;f&&!f[a]&&i(f,a,c),o[c]=o.Array}},function(e,t){var r;r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(n){"object"==typeof window&&(r=window)}e.exports=r},function(e,t,r){e.exports={"default":r(78),__esModule:!0}},function(e,t){},function(e,t,r){e.exports=r(9)},function(e,t,r){var n=r(1).document;e.exports=n&&n.documentElement},function(e,t,r){"use strict";t.__esModule=!0,t.gbConfig=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=r(23),a=r(38),s={sdkVersion:"sdkVersion",appId:"appId",appName:"appName",hostCategoryAppName:"hostCategoryAppName",appVersion:"appVersion",hL:"hL",analysisServer:"analysisServer",syncPC:"syncPC",clientFeature:"clientFeature"},u=function(){function e(){(0,i["default"])(this,e),this._gbHelper=new a.GBHelper(o.gbAttrNames.config)}return e.prototype.setSDKVersion=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.sdkVersion,t)},e.prototype.getSDKVersion=function(){return this._gbHelper.getAttr(s.sdkVersion)},e.prototype.setAppId=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.appId,t)},e.prototype.getAppId=function(){return this._gbHelper.getAttr(s.appId)},e.prototype.setAppName=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.appName,t)},e.prototype.getAppName=function(){return this._gbHelper.getAttr(s.appName)},e.prototype.setHostCategoryAppName=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.hostCategoryAppName,t)},e.prototype.getHostCategoryAppName=function(){return this._gbHelper.getAttr(s.hostCategoryAppName)},e.prototype.setAppVersion=function(e){var t="string"==typeof e&&e.length>0?e:"NONE";this._gbHelper.setAttr(s.appVersion,t)},e.prototype.getAppVersion=function(){return this._gbHelper.getAttr(s.appVersion)},e.prototype.setHL=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.hL,t)},e.prototype.getHL=function(){return this._gbHelper.getAttr(s.hL)},e.prototype.setAnalysisServer=function(e){var t=e;this._gbHelper.setAttr(s.analysisServer,t)},e.prototype.getAnalysisServer=function(){return this._gbHelper.getAttr(s.analysisServer)},e.prototype.setSyncPC=function(e){var t=!0===e;this._gbHelper.setAttr(s.syncPC,t)},e.prototype.getSyncPC=function(){return this._gbHelper.getAttr(s.syncPC)},e.prototype.setClientFeature=function(e){var t=!0===e;this._gbHelper.setAttr(s.clientFeature,t)},e.prototype.getClientFeature=function(){return this._gbHelper.getAttr(s.clientFeature)},e}();t.gbConfig=new u},function(e,t,r){var n=r(17),i=r(3)("toStringTag"),o="Arguments"==n(function(){return arguments}()),a=function(e,t){try{return e[t]}catch(r){}};e.exports=function(e){var t,r,s;return e===undefined?"Undefined":null===e?"Null":"string"==typeof(r=a(t=Object(e),i))?r:o?n(t):"Object"==(s=n(t))&&"function"==typeof t.callee?"Arguments":s}},function(e,t,r){"use strict";function n(){return f===undefined&&(f=/\bedge\b/.test(l)),f}function i(){return d===undefined&&(d=/[ ]thunder\/10.0([\d.]*)/.test(l)||/[ ]thunder( )?\/( )?9.([\d.]*)/.test(l)),d}function o(){return p===undefined&&(p=/\bthunder\/10.[1-9][\d.]*/.test(l)),p}function a(){return _===undefined&&(_=/\belectron\/\d+(\.\d+){2}\b/.test(l)),_}function s(){return h===undefined&&(h=/\btbc\/\d+(\.\d+){3}\b/.test(l)&&!!window["native"]),h}function u(){return g===undefined&&(g=o()||!i()&&s()),g}function c(){return m===undefined&&(m="file:"===location.protocol),m}t.__esModule=!0,t.checkIsEdge=n,t.checkIsXl9=i,t.checkIsXlx=o,t.checkIsXdas=a,t.checkIsTbc=s,t.checkAsPCFlow=u,t.checkIsLocal=c;var l=navigator.userAgent.toLocaleLowerCase(),f=undefined,d=undefined,p=undefined,_=undefined,h=undefined,g=undefined,m=undefined},function(e,t,r){var n=r(5),i=r(21),o=r(3)("species");e.exports=function(e,t){var r,a=n(e).constructor;return a===undefined||(r=n(a)[o])==undefined?t:i(r)}},function(e,t,r){var n,i,o,a=r(19),s=r(107),u=r(59),c=r(33),l=r(1),f=l.process,d=l.setImmediate,p=l.clearImmediate,_=l.MessageChannel,h=l.Dispatch,g=0,m={},v=function(){var e=+this;if(m.hasOwnProperty(e)){var t=m[e];delete m[e],t()}},y=function(e){v.call(e.data)};d&&p||(d=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return m[++g]=function(){s("function"==typeof e?e:Function(e),t)},n(g),g},p=function(e){delete m[e]},"process"==r(17)(f)?n=function(e){f.nextTick(a(v,e,1))}:h&&h.now?n=function(e){h.now(a(v,e,1))}:_?(i=new _,o=i.port2,i.port1.onmessage=y,n=a(o.postMessage,o,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(n=function(e){l.postMessage(e+"","*")},l.addEventListener("message",y,!1)):n="onreadystatechange"in c("script")?function(e){u.appendChild(c("script")).onreadystatechange=function(){u.removeChild(this),v.call(e)}}:function(e){setTimeout(a(v,e,1),0)}),e.exports={set:d,clear:p}},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(t){return{e:!0,v:t}}}},function(e,t,r){var n=r(5),i=r(6),o=r(42);e.exports=function(e,t){if(n(e),i(t)&&t.constructor===e)return t;var r=o.f(e);return(0,r.resolve)(t),r.promise}},function(e,t,r){var n=r(48),i=r(35).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,i)}},function(e,t,r){"use strict";function n(){function e(e){var t={};return this.each(function(e,r){t[r]=e}),t}return{dump:e}}e.exports=n},function(e,t,r){"use strict";function n(){function e(e,t,r){return c.on(t,a(this,r))}function t(e,t){c.off(t)}function r(e,t,r){c.once(t,a(this,r))}function n(e,t,r){var n=this.get(t);e(),c.fire(t,r,n)}function o(e,t){var r=this.get(t);e(),c.fire(t,undefined,r)}function u(e){var t={};this.each(function(e,r){t[r]=e}),e(),s(t,function(e,t){c.fire(t,undefined,e)})}var c=i();return{watch:e,unwatch:t,once:r,set:n,remove:o,clearAll:u}}function i(){return u(l,{_id:0,_subSignals:{},_subCallbacks:{}})}var o=r(11),a=o.bind,s=o.each,u=o.create,c=o.slice;e.exports=n;var l={_id:null,_subCallbacks:null,_subSignals:null,on:function(e,t){return this._subCallbacks[e]||(this._subCallbacks[e]={}),this._id+=1,this._subCallbacks[e][this._id]=t,this._subSignals[this._id]=e,this._id},off:function(e){var t=this._subSignals[e];delete this._subCallbacks[t][e],delete this._subSignals[e]},once:function(e,t){var r=this.on(e,a(this,function(){t.apply(this,arguments),this.off(r)}))},fire:function(e){var t=c(arguments,1);s(this._subCallbacks[e],function(e){e.apply(this,t)})}}},function(e,t,r){"use strict";function n(){return r(136),{}}e.exports=n},function(e,t,r){"use strict";function n(){function e(e,t,r,n){3==arguments.length&&(n=r,r=undefined);var i=this.get(t,r),o=n(i);this.set(t,o!=undefined?o:i)}return{update:e}}e.exports=n},function(e,t,r){var n=r(2),i=n.JSON||(n.JSON={stringify:JSON.stringify});e.exports=function(e){return i.stringify.apply(i,arguments)}},function(e,t,r){"use strict";t.__esModule=!0;var n=r(51),i=(0,n.getDomain)();t["default"]={LOGIN_ID:"",APP_NAME:"",LOGIN_TYPE_COOKIE_NAME:"_x_t_",LOGIN_KEY_NAME:"loginkey",ALL_HTTPS:!1,SET_ROOT_DOMAIN:!0,AUTO_LOGIN_EXPIRE_TIME:2592e3,LOGIN_TYPES:"12",REGISTER_TYPES:"2",RETRY_LOGIN_ON_SERVER_ERROR:!0,SERVER_TIMEOUT:7e3,LOGIN_SUCCESS_URL:"",REGISTER_SUCCESS_URL:"",UI_THEME:"embed",UI_TYPE:"embed",UI_TEXT:"",UI_STYLE:!0,THIRD_LOGIN_DISPLAY:!0,DEFUALT_BACKGROUND:"",DEFUALT_UI:"login",LOGIN_BUTTON_TEXT:["登录","登录中..."],REGISTER_BUTTON_TEXT:["注册","注册..."],PROXY_URL:"http://test.kankan.com/proxy.html",DOMAIN:i,DEBUG:!0,DEFAULT_ACCOUNT:"",THIRD_LOGIN_TARGET_PARENT:!1,ALERT_ERROR:!1,USE_CDN:!1,SERVER_REGISTER:"https://zhuce."+i+"/regapi/",CDN_PATH:"",THIRD_LOGIN_GROUP:["qq","weixin","sina","alipay","xiaomi","aq360","renren","tianyi"],THIRD_LOGIN_DEFAULT:["qq","weixin","sina","alipay"],DEFAULT_AVATAR:"",REGISTER_WITH_LOGIN:!1}},function(e,t,r){"use strict";t.__esModule=!0,t.UserBehaviorsStatServer=t.userBehaviorsStatActions=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=r(142),a=r(46),s=t.userBehaviorsStatActions={loginPanelLoginClick:"loginPanelLoginClick",loginPanelPhoneClick:"loginPanelPhoneClick",loginPanelPhoneCode:"loginPanelPhoneCode",loginPanelHistorySelect:"loginPanelHistorySelect",loginPanelEmailShow:"loginPanelEmailShow",loginPanelEyeClick:"loginPanelEyeClick",loginPanelAccountClick:"loginPanelAccountClick",loginSuccess:"loginSuccess",loginFailure:"loginFailure",loginPanelForgetPasswordClick:"loginPanelForgetPasswordClick",loginPanelRegisterClick:"loginPanelRegisterClick",loginPanelQrClick:"loginPanelQrClick",loginPanelQrCodeShow:"loginPanelQrCodeShow",loginPanelQrCodeRefreshClick:"loginPanelQrCodeRefreshClick",loginPanelQrCodeRefresh:"loginPanelQrCodeRefresh",loginPanelQrCodeHover:"loginPanelQrCodeHover",loginPanelQrCodeAccoutClick:"loginPanelQrCodeAccoutClick",thrLoginClick:"thrLoginClick",websdkShow:"websdkShow",websdkClose:"websdkClose",registPanelShow:"registPanelShow",registPanelPhoneCode:"registPanelPhoneCode",registPanelRegistClick:"registPanelRegistClick",registPanelRegistSuccess:"registPanelRegistSuccess",registPanelRegistFail:"registPanelRegistFail",registPanelRegistAgree:"registPanelRegistAgree",registPanelLoginClick:"registPanelLoginClick",lastLoginType:"lastLoginType"};t.UserBehaviorsStatServer=function(){function e(t){(0,i["default"])(this,e),this._statServer=new o.StatServer(t,"websdk-user-behaviors2")}return e.prototype.setPublicData=function(e){return this._statServer.setPublicData(e)},e.prototype.stat=function(e){return this._statServer.stat(e)},e.prototype.statLoginResult=function(e,t){if(t){var r={extData:{}},n=r.extData,i=(new Date).getTime();switch("number"==typeof t.beginTime&&(i-=t.beginTime),n.costTime=i,t.type){case 0:r.action=e?s.loginSuccess:s.loginFailure,n.isAuto="number"==typeof t.isAuto?t.isAuto:0;break;case 1:r.action=e?s.registPanelRegistSuccess:s.registPanelRegistFail;break;default:r=null}null!==r&&(n.mode=(0,a.forceToString)(t.mode,""),e||"undefined"==typeof t.errorCode||(n.errorCode=(0,a.forceToString)(t.errorCode,"")),this.stat(r))}},e}()},function(e,t,r){var n=r(13),i=r(45),o=r(76);e.exports=function(e){return function(t,r,a){var s,u=n(t),c=i(u.length),l=o(a,c);if(e&&r!=r){for(;c>l;)if((s=u[l++])!=s)return!0}else for(;c>l;l++)if((e||l in u)&&u[l]===r)return e||l||0;return!e&&-1}}},function(e,t,r){var n=r(30),i=Math.max,o=Math.min;e.exports=function(e,t){return e=n(e),e<0?i(e+t,0):o(e,t)}},function(e,t,r){"use strict";var n=r(124),i=r(125),o=r(132);e.exports=n.createStore(i,o)},function(e,t,r){r(79),e.exports=r(2).Object.assign},function(e,t,r){var n=r(7);n(n.S+n.F,"Object",{assign:r(80)})},function(e,t,r){"use strict";var n=r(4),i=r(25),o=r(37),a=r(26),s=r(32),u=r(41),c=Object.assign;e.exports=!c||r(14)(function(){var e={},t={},r=Symbol(),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach(function(e){t[e]=e}),7!=c({},e)[r]||Object.keys(c({},t)).join("")!=n})?function(e,t){for(var r=s(e),c=arguments.length,l=1,f=o.f,d=a.f;c>l;)for(var p,_=u(arguments[l++]),h=f?i(_).concat(f(_)):i(_),g=h.length,m=0;g>m;)p=h[m++],n&&!d.call(_,p)||(r[p]=_[p]);return r}:c},function(e,t,r){var n=r(19),i=r(99),o=r(100),a=r(5),s=r(45),u=r(83),c={},l={},t=e.exports=function(e,t,r,f,d){var p,_,h,g,m=d?function(){return e}:u(e),v=n(r,f,t?2:1),y=0;if("function"!=typeof m)throw TypeError(e+" is not iterable!");if(o(m)){for(p=s(e.length);p>y;y++)if((g=t?v(a(_=e[y])[0],_[1]):v(e[y]))===c||g===l)return g}else for(h=m.call(e);!(_=h.next()).done;)if((g=i(h,v,_.value,t))===c||g===l)return g};t.BREAK=c,t.RETURN=l},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,r){var n=r(61),i=r(3)("iterator"),o=r(18);e.exports=r(2).getIteratorMethod=function(e){if(e!=undefined)return e[i]||e["@@iterator"]||o[n(e)]}},function(e,t,r){"use strict";function n(e,t){undefined;return t>e.length?new Array(t-e.length+1).join("0")+e:e}function i(e){return e.getFullYear().toString()+n((e.getMonth()+1).toString(),2)+n(e.getDate().toString(),2)}function o(e){var t=null;try{t=JSON.parse(e)}catch(r){t=null}return t}function a(e){var t=undefined;try{t=JSON.stringify(e)}catch(r){t=undefined}return t}function s(e,t,r){return typeof t===e?t:r}Object.defineProperty(t,"__esModule",{value:!0}),t.dateToDateString=i,t.parseJson=o,t.stringifyJson=a,t.forceGetTypeValue=s},function(e,t,r){var n=r(30),i=r(29);e.exports=function(e){return function(t,r){var o,a,s=String(i(t)),u=n(r),c=s.length;return u<0||u>=c?e?"":undefined:(o=s.charCodeAt(u),o<55296||o>56319||u+1===c||(a=s.charCodeAt(u+1))<56320||a>57343?e?s.charAt(u):o:e?s.slice(u,u+2):a-56320+(o-55296<<10)+65536)}}},function(e,t,r){"use strict";var n=r(39),i=r(20),o=r(27),a={};r(9)(a,r(3)("iterator"),function(){return this}),e.exports=function(e,t,r){e.prototype=n(a,{next:i(1,r)}),o(e,t+" Iterator")}},function(e,t,r){var n=r(8),i=r(5),o=r(25);e.exports=r(4)?Object.defineProperties:function(e,t){i(e);for(var r,a=o(t),s=a.length,u=0;s>u;)n.f(e,r=a[u++],t[r]);return e}},function(e,t,r){var n=r(10),i=r(32),o=r(31)("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=i(e),n(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?a:null}},function(e,t,r){"use strict";var n=r(90),i=r(82),o=r(18),a=r(13);e.exports=r(53)(Array,"Array",function(e,t){this._t=a(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,r=this._i++;return!e||r>=e.length?(this._t=undefined,i(1)):"keys"==t?i(0,r):"values"==t?i(0,e[r]):i(0,[r,e[r]])},"values"),o.Arguments=o.Array,n("keys"),n("values"),n("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t,r,n){if(!(e instanceof t)||n!==undefined&&n in e)throw TypeError(r+": incorrect invocation!");return e}},function(e,t,r){var n=r(9);e.exports=function(e,t,r){for(var i in t)r&&e[i]?e[i]=t[i]:n(e,i,t[i]);return e}},function(e,t,r){var n=r(22)("meta"),i=r(6),o=r(10),a=r(8).f,s=0,u=Object.isExtensible||function(){return!0},c=!r(14)(function(){return u(Object.preventExtensions({}))}),l=function(e){a(e,n,{value:{i:"O"+ ++s,w:{}}})},f=function(e,t){if(!i(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!o(e,n)){if(!u(e))return"F";if(!t)return"E";l(e)}return e[n].i},d=function(e,t){if(!o(e,n)){if(!u(e))return!0;if(!t)return!1;l(e)}return e[n].w},p=function(e){return c&&_.NEED&&u(e)&&!o(e,n)&&l(e),e},_=e.exports={KEY:n,NEED:!1,fastKey:f,getWeak:d,onFreeze:p}},function(e,t,r){var n=r(26),i=r(20),o=r(13),a=r(36),s=r(10),u=r(47),c=Object.getOwnPropertyDescriptor;t.f=r(4)?c:function(e,t){if(e=o(e),t=a(t,!0),u)try{return c(e,t)}catch(r){}if(s(e,t))return i(!n.f.call(e,t),e[t])}},function(e,t,r){e.exports={"default":r(121),__esModule:!0}},function(e,t,r){"use strict";t.__esModule=!0;var n=function(){function e(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var s=1732584193,u=-271733879,c=-1732584194,l=271733878,f=0;e.length>f;f+=16){var d=s,p=u,_=c,h=l;s=r(s,u,c,l,e[f+0],7,-680876936),l=r(l,s,u,c,e[f+1],12,-389564586),c=r(c,l,s,u,e[f+2],17,606105819),u=r(u,c,l,s,e[f+3],22,-1044525330),s=r(s,u,c,l,e[f+4],7,-176418897),l=r(l,s,u,c,e[f+5],12,1200080426),c=r(c,l,s,u,e[f+6],17,-1473231341),u=r(u,c,l,s,e[f+7],22,-45705983),s=r(s,u,c,l,e[f+8],7,1770035416),l=r(l,s,u,c,e[f+9],12,-1958414417),c=r(c,l,s,u,e[f+10],17,-42063),u=r(u,c,l,s,e[f+11],22,-1990404162),s=r(s,u,c,l,e[f+12],7,1804603682),l=r(l,s,u,c,e[f+13],12,-40341101),c=r(c,l,s,u,e[f+14],17,-1502002290),u=r(u,c,l,s,e[f+15],22,1236535329),s=n(s,u,c,l,e[f+1],5,-165796510),l=n(l,s,u,c,e[f+6],9,-1069501632),c=n(c,l,s,u,e[f+11],14,643717713),u=n(u,c,l,s,e[f+0],20,-373897302),s=n(s,u,c,l,e[f+5],5,-701558691),l=n(l,s,u,c,e[f+10],9,38016083),c=n(c,l,s,u,e[f+15],14,-660478335),u=n(u,c,l,s,e[f+4],20,-405537848),s=n(s,u,c,l,e[f+9],5,568446438),l=n(l,s,u,c,e[f+14],9,-1019803690),c=n(c,l,s,u,e[f+3],14,-187363961),u=n(u,c,l,s,e[f+8],20,1163531501),s=n(s,u,c,l,e[f+13],5,-1444681467),l=n(l,s,u,c,e[f+2],9,-51403784),c=n(c,l,s,u,e[f+7],14,1735328473),u=n(u,c,l,s,e[f+12],20,-1926607734),s=i(s,u,c,l,e[f+5],4,-378558),l=i(l,s,u,c,e[f+8],11,-2022574463),c=i(c,l,s,u,e[f+11],16,1839030562),u=i(u,c,l,s,e[f+14],23,-35309556),s=i(s,u,c,l,e[f+1],4,-1530992060),l=i(l,s,u,c,e[f+4],11,1272893353),c=i(c,l,s,u,e[f+7],16,-155497632),u=i(u,c,l,s,e[f+10],23,-1094730640),s=i(s,u,c,l,e[f+13],4,681279174),l=i(l,s,u,c,e[f+0],11,-358537222),c=i(c,l,s,u,e[f+3],16,-722521979),u=i(u,c,l,s,e[f+6],23,76029189),s=i(s,u,c,l,e[f+9],4,-640364487),l=i(l,s,u,c,e[f+12],11,-421815835),c=i(c,l,s,u,e[f+15],16,530742520),u=i(u,c,l,s,e[f+2],23,-995338651),s=o(s,u,c,l,e[f+0],6,-198630844),l=o(l,s,u,c,e[f+7],10,1126891415),c=o(c,l,s,u,e[f+14],15,-1416354905),u=o(u,c,l,s,e[f+5],21,-57434055),s=o(s,u,c,l,e[f+12],6,1700485571),l=o(l,s,u,c,e[f+3],10,-1894986606),c=o(c,l,s,u,e[f+10],15,-1051523),u=o(u,c,l,s,e[f+1],21,-2054922799),s=o(s,u,c,l,e[f+8],6,1873313359),l=o(l,s,u,c,e[f+15],10,-30611744),c=o(c,l,s,u,e[f+6],15,-1560198380),u=o(u,c,l,s,e[f+13],21,1309151649),s=o(s,u,c,l,e[f+4],6,-145523070),l=o(l,s,u,c,e[f+11],10,-1120210379),c=o(c,l,s,u,e[f+2],15,718787259),u=o(u,c,l,s,e[f+9],21,-343485551),s=a(s,d),u=a(u,p),c=a(c,_),l=a(l,h)}return[s,u,c,l]}function t(e,t,r,n,i,o){return a(s(a(a(t,e),a(n,o)),i),r)}function r(e,r,n,i,o,a,s){return t(r&n|~r&i,e,r,o,a,s)}function n(e,r,n,i,o,a,s){return t(r&i|n&~i,e,r,o,a,s)}function i(e,r,n,i,o,a,s){return t(r^n^i,e,r,o,a,s)}function o(e,r,n,i,o,a,s){return t(n^(r|~i),e,r,o,a,s)}function a(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function s(e,t){return e<<t|e>>>32-t}function u(e){for(var t=[],r=(1<<f)-1,n=0;e.length*f>n;n+=f)t[n>>5]|=(e.charCodeAt(n/f)&r)<<n%32;return t}function c(e){for(var t=l?"0123456789ABCDEF":"0123456789abcdef",r="",n=0;4*e.length>n;n++)r+=t.charAt(e[n>>2]>>n%4*8+4&15)+t.charAt(e[n>>2]>>n%4*8&15);return r}var l=0,f=8;return function(t){return c(e(u(t),t.length*f))}}();window.md5=n,t["default"]=n},function(e,t,r){"use strict";t.__esModule=!0,t.CONFIG=undefined;var n=r(51),i=(0,n.getDomain)();t.CONFIG={LOGIN_ID:"",APP_NAME:"",APP_VERSION:"NONE",SET_ROOT_DOMAIN:!0,LOGIN_TYPE_COOKIE_NAME:"_x_t_",AUTO_LOGIN_COOKIE_NAME:"_x_a_",AUTO_LOGIN_EXPIRE_TIME:2592e3,LOGIN_TYPES:["account","mobile"],REGISTER_TYPES:["mobile"],UI_THEME:"embed",UI_TYPE:"embed",UI_TEXT:"",UI_STYLE:"",THIRD_LOGIN_DISPLAY:!0,RETRY_LOGIN_ON_SERVER_ERROR:!0,LOGIN_SUCCESS_FUNC:function(){location.reload()},REGISTER_SUCCESS_FUNC:function(){location.reload()},LOGIN_SUCCESS_URL:location.href,REGISTER_SUCCESS_URL:location.href,ON_UI_CHANGE:function(e){},POPUP_MASK:!0,POPUP_ALLOW_CLOSE:!0,POPUP_CLOSE_FUNC:function(){},POPUP_PRELOAD:!0,DEFUALT_BACKGROUND:"//i."+i+"/login/theme/popup/images/layer_bg.png",DEFUALT_UI:"login",IFRAME_ALLOW_TRANSPARENCY:!1,IFRAME_STYLE:"",IFRAME_ID:"loginIframe",LOGOUT_FUNC:function(){location.reload()},BIND_SUCCESS_FUNC:function(){location.reload()},LOGIN_BUTTON_TEXT:"",REGISTER_BUTTON_TEXT:"",REGISTER_STAT_DATA:"",DOMAIN:i,ALLOW_ACCOUNT_REGISTER_IDS:["vip_niu","niux_web","game"],VERSION:"2.5",DEBUG:!1,DEFAULT_ACCOUNT:"",THIRD_LOGIN_TARGET_PARENT:!1,ALERT_ERROR:!1,CHANGE_SIZE_FUNC:function(e){},LOGIN_EXT_FUNS:[],LOGOUT_EXT_FUNS:[],CLIENT_LOGIN_FUNS:[],CLIENTLOGIN:!1,INITED_FUNS:[],LOGIN_STATE_INITED_FUNS:[],USE_CDN:!1,SERVER_REGISTER:"https://zhuce."+i+"/regapi/",UI_COMPLETED_EXT_FUNS:[],THIRD_LOGIN_GROUP:["qq","weixin","sina"],THIRD_LOGIN_DEFAULT:["qq","weixin","sina"],DEFAULT_AVATAR:"",SERVER_LOGIN:["xluser-ssl."+i,"xluser2-ssl."+i,"xluser3-ssl."+i],SERVER_XLUSER:[],MESSAGE_CHANNEL_GSLB_QURERY_HOST_KEY:"agw-acc-ssl."+i,MESSAGE_CHANNEL_SERVER:"agw-acc-web-ssl."+i,ANALYSIS_SERVER:"",IS_HIT_BLOCK:!0,LOGIN_FAIL_FUNS:[],CAN_GSLB:!0,STATIC_DOMAIN:"i."+i,ISFRAUD:!0,CAN_QR_LOGIN:!1,ONLOAD:function(){},REGISTER_CHECKED:!1,TOAST:function(e){},isDOMAIN:!0,IS_SYNC_APP:!0,IS_SYNC_PC:!0,IS_SYNC_MAC:!1,CLIENT_FEATURE:!1,HL:"",SHOW_GSM:!1,REGISTER_WITH_LOGIN:!1}},function(e,t,r){"use strict";t.__esModule=!0,t.userBehaviorsStatServerLoader=t.UserBehaviorsStatServerLoader=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=r(46),a=r(140),s=r(141),u=r(74),c=t.UserBehaviorsStatServerLoader=function(){function e(){(0,i["default"])(this,e),this._userBehaviorsStatServer=null}return e.prototype.setPublicData=function(e){var t=e||{},r=(0,o.combineJsonObject)((0,s.getPublicData)(),t);this.get().setPublicData(r)},e.prototype.get=function(){if(null===this._userBehaviorsStatServer){var e=a.gbStatAttrNames.userBehaviors;if(!a.gbStat.hasAttr(e)){var t=new u.UserBehaviorsStatServer((0,s.getStatServerUrl)());a.gbStat.setAttr(e,t)}this._userBehaviorsStatServer=a.gbStat.getAttr(e)}return this._userBehaviorsStatServer},e}();t.userBehaviorsStatServerLoader=new c},function(e,t,r){var n=r(5);e.exports=function(e,t,r,i){try{return i?t(n(r)[0],r[1]):t(r)}catch(a){var o=e["return"];throw o!==undefined&&n(o.call(e)),a}}},function(e,t,r){var n=r(18),i=r(3)("iterator"),o=Array.prototype;e.exports=function(e){return e!==undefined&&(n.Array===e||o[i]===e)}},function(e,t,r){"use strict";var n=r(1),i=r(2),o=r(8),a=r(4),s=r(3)("species");e.exports=function(e){var t="function"==typeof i[e]?i[e]:n[e];a&&t&&!t[s]&&o.f(t,s,{configurable:!0,get:function(){return this}})}},function(e,t,r){var n=r(3)("iterator"),i=!1;try{var o=[7][n]();o["return"]=function(){i=!0},Array.from(o,function(){throw 2})}catch(a){}e.exports=function(e,t){if(!t&&!i)return!1;var r=!1;try{var o=[7],a=o[n]();a.next=function(){return{done:r=!0}},o[n]=function(){return a},e(o)}catch(a){}return r}},function(e,t,r){var n=r(17);e.exports=Array.isArray||function(e){return"Array"==n(e)}},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.HttpRequest=undefined;var i=r(40),o=n(i),a=r(0),s=n(a),u=t.HttpRequest=function(){function e(){(0,s["default"])(this,e)}return e.prototype.get=function(e,t){arguments.length>2&&arguments[2]!==undefined&&arguments[2];return this._request("GET",e,t,null)},e.prototype.post=function(e,t,r){return this._request("POST",e,t,r)},e.prototype._request=function(e,t,r){var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:null;return new o["default"](function(i,o){var a=void 0;if(a=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),a.onreadystatechange=function(){if(4==a.readyState){var e={data:a.responseText,status:a.status};i(e)}},"GET"===e)a.open(e,t,!0),a.send(null);else{if(a.open(e,t,!0),r)for(var s in r)a.setRequestHeader(s,r[s]);a.send(n)}})},e}(),c=new u;t["default"]=c},function(e,t,r){r(57),r(50),r(54),r(106),r(110),r(111),e.exports=r(2).Promise},function(e,t,r){"use strict";var n,i,o,a,s=r(16),u=r(1),c=r(19),l=r(61),f=r(7),d=r(6),p=r(21),_=r(91),h=r(81),g=r(63),m=r(64).set,v=r(108)(),y=r(42),b=r(65),w=r(109),E=r(66),S=u.TypeError,I=u.process,T=I&&I.versions,k=T&&T.v8||"",x=u.Promise,C="process"==l(I),L=function(){},O=i=y.f,A=!!function(){try{var e=x.resolve(1),t=(e.constructor={})[r(3)("species")]=function(e){e(L,L)};return(C||"function"==typeof PromiseRejectionEvent)&&e.then(L)instanceof t&&0!==k.indexOf("6.6")&&-1===w.indexOf("Chrome/66")}catch(n){}}(),P=function(e){var t;return!(!d(e)||"function"!=typeof(t=e.then))&&t},N=function(e,t){if(!e._n){e._n=!0;var r=e._c;v(function(){for(var n=e._v,i=1==e._s,o=0;r.length>o;)!function(t){var r,o,a,s=i?t.ok:t.fail,u=t.resolve,c=t.reject,l=t.domain;try{s?(i||(2==e._h&&M(e),e._h=1),!0===s?r=n:(l&&l.enter(),r=s(n),l&&(l.exit(),a=!0)),r===t.promise?c(S("Promise-chain cycle")):(o=P(r))?o.call(r,u,c):u(r)):c(n)}catch(f){l&&!a&&l.exit(),c(f)}}(r[o++]);e._c=[],e._n=!1,t&&!e._h&&D(e)})}},D=function(e){m.call(u,function(){var t,r,n,i=e._v,o=R(e);if(o&&(t=b(function(){C?I.emit("unhandledRejection",i,e):(r=u.onunhandledrejection)?r({promise:e,reason:i}):(n=u.console)&&n.error&&n.error("Unhandled promise rejection",i)}),e._h=C||R(e)?2:1),e._a=undefined,o&&t.e)throw t.v})},R=function(e){return 1!==e._h&&0===(e._a||e._c).length},M=function(e){m.call(u,function(){var t;C?I.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})})},U=function(e){var t=this;t._d||(t._d=!0,t=t._w||t,t._v=e,t._s=2,t._a||(t._a=t._c.slice()),N(t,!0))},G=function(e){var t,r=this;if(!r._d){r._d=!0,r=r._w||r;try{if(r===e)throw S("Promise can't be resolved itself");(t=P(e))?v(function(){var n={_w:r,_d:!1};try{t.call(e,c(G,n,1),c(U,n,1))}catch(i){U.call(n,i)}}):(r._v=e,r._s=1,N(r,!1))}catch(n){U.call({_w:r,_d:!1},n)}}};A||(x=function(e){_(this,x,"Promise","_h"),p(e),n.call(this);try{e(c(G,this,1),c(U,this,1))}catch(t){U.call(this,t)}},n=function(e){this._c=[],this._a=undefined,this._s=0,this._d=!1,this._v=undefined,this._h=0,this._n=!1},n.prototype=r(92)(x.prototype,{then:function(e,t){var r=O(g(this,x));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=C?I.domain:undefined,this._c.push(r),this._a&&this._a.push(r),this._s&&N(this,!1),r.promise},"catch":function(e){return this.then(undefined,e)}}),o=function(){var e=new n;this.promise=e,this.resolve=c(G,e,1),this.reject=c(U,e,1)},y.f=O=function(e){return e===x||e===a?new o(e):i(e)}),f(f.G+f.W+f.F*!A,{Promise:x}),r(27)(x,"Promise"),r(101)("Promise"),a=r(2).Promise,f(f.S+f.F*!A,"Promise",{reject:function(e){var t=O(this);return(0,t.reject)(e),t.promise}}),f(f.S+f.F*(s||!A),"Promise",{resolve:function(e){return E(s&&this===a?x:this,e)}}),f(f.S+f.F*!(A&&r(102)(function(e){x.all(e)["catch"](L)})),"Promise",{all:function(e){var t=this,r=O(t),n=r.resolve,i=r.reject,o=b(function(){var r=[],o=0,a=1;h(e,!1,function(e){var s=o++,u=!1;r.push(undefined),a++,t.resolve(e).then(function(e){u||(u=!0,r[s]=e,--a||n(r))},i)}),--a||n(r)});return o.e&&i(o.v),r.promise},race:function(e){var t=this,r=O(t),n=r.reject,i=b(function(){h(e,!1,function(e){t.resolve(e).then(r.resolve,n)})});return i.e&&n(i.v),r.promise}})},function(e,t){e.exports=function(e,t,r){var n=r===undefined;switch(t.length){case 0:return n?e():e.call(r);case 1:return n?e(t[0]):e.call(r,t[0]);case 2:return n?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},function(e,t,r){var n=r(1),i=r(64).set,o=n.MutationObserver||n.WebKitMutationObserver,a=n.process,s=n.Promise,u="process"==r(17)(a);e.exports=function(){var e,t,r,c=function(){var n,i;for(u&&(n=a.domain)&&n.exit();e;){i=e.fn,e=e.next;try{i()}catch(o){throw e?r():t=undefined,o}}t=undefined,n&&n.enter()};if(u)r=function(){a.nextTick(c)};else if(!o||n.navigator&&n.navigator.standalone)if(s&&s.resolve){var l=s.resolve(undefined);r=function(){l.then(c)}}else r=function(){i.call(n,c)};else{var f=!0,d=document.createTextNode("");new o(c).observe(d,{characterData:!0}),r=function(){d.data=f=!f}}return function(n){var i={fn:n,next:undefined};t&&(t.next=i),e||(e=i,r()),t=i}}},function(e,t,r){var n=r(1),i=n.navigator;e.exports=i&&i.userAgent||""},function(e,t,r){"use strict";var n=r(7),i=r(2),o=r(1),a=r(63),s=r(66);n(n.P+n.R,"Promise",{"finally":function(e){var t=a(this,i.Promise||o.Promise),r="function"==typeof e;return this.then(r?function(r){return s(t,e()).then(function(){return r})}:e,r?function(r){return s(t,e()).then(function(){throw r})}:e)}})},function(e,t,r){"use strict";var n=r(7),i=r(42),o=r(65);n(n.S,"Promise",{"try":function(e){var t=i.f(this),r=o(e);return(r.e?t.reject:t.resolve)(r.v),t.promise}})},function(e,t,r){e.exports={"default":r(113),__esModule:!0}},function(e,t,r){r(50),r(54),e.exports=r(43).f("iterator")},function(e,t,r){e.exports={"default":r(115),__esModule:!0}},function(e,t,r){r(116),r(57),r(119),r(120),e.exports=r(2).Symbol},function(e,t,r){"use strict";var n=r(1),i=r(10),o=r(4),a=r(7),s=r(58),u=r(93).KEY,c=r(14),l=r(34),f=r(27),d=r(22),p=r(3),_=r(43),h=r(44),g=r(117),m=r(103),v=r(5),y=r(6),b=r(32),w=r(13),E=r(36),S=r(20),I=r(39),T=r(118),k=r(94),x=r(37),C=r(8),L=r(25),O=k.f,A=C.f,P=T.f,N=n.Symbol,D=n.JSON,R=D&&D.stringify,M=p("_hidden"),U=p("toPrimitive"),G={}.propertyIsEnumerable,F=l("symbol-registry"),j=l("symbols"),B=l("op-symbols"),V=Object.prototype,q="function"==typeof N&&!!x.f,H=n.QObject,Y=!H||!H.prototype||!H.prototype.findChild,J=o&&c(function(){return 7!=I(A({},"a",{get:function(){return A(this,"a",{value:7}).a}})).a})?function(e,t,r){var n=O(V,t);n&&delete V[t],A(e,t,r),n&&e!==V&&A(V,t,n)}:A,W=function(e){var t=j[e]=I(N.prototype);return t._k=e,t},X=q&&"symbol"==typeof N.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof N},Q=function(e,t,r){return e===V&&Q(B,t,r),v(e),t=E(t,!0),v(r),i(j,t)?(r.enumerable?(i(e,M)&&e[M][t]&&(e[M][t]=!1),r=I(r,{enumerable:S(0,!1)})):(i(e,M)||A(e,M,S(1,{})),e[M][t]=!0),J(e,t,r)):A(e,t,r)},K=function(e,t){v(e);for(var r,n=g(t=w(t)),i=0,o=n.length;o>i;)Q(e,r=n[i++],t[r]);return e},z=function(e,t){return t===undefined?I(e):K(I(e),t)},$=function(e){var t=G.call(this,e=E(e,!0));return!(this===V&&i(j,e)&&!i(B,e))&&(!(t||!i(this,e)||!i(j,e)||i(this,M)&&this[M][e])||t)},Z=function(e,t){if(e=w(e),t=E(t,!0),e!==V||!i(j,t)||i(B,t)){var r=O(e,t);return!r||!i(j,t)||i(e,M)&&e[M][t]||(r.enumerable=!0),r}},ee=function(e){for(var t,r=P(w(e)),n=[],o=0;r.length>o;)i(j,t=r[o++])||t==M||t==u||n.push(t);return n},te=function(e){for(var t,r=e===V,n=P(r?B:w(e)),o=[],a=0;n.length>a;)!i(j,t=n[a++])||r&&!i(V,t)||o.push(j[t]);return o};q||(N=function(){if(this instanceof N)throw TypeError("Symbol is not a constructor!");var e=d(arguments.length>0?arguments[0]:undefined),t=function(r){this===V&&t.call(B,r),i(this,M)&&i(this[M],e)&&(this[M][e]=!1),J(this,e,S(1,r))};return o&&Y&&J(V,e,{configurable:!0,set:t}),W(e)},s(N.prototype,"toString",function(){return this._k}),k.f=Z,C.f=Q,r(67).f=T.f=ee,r(26).f=$,x.f=te,o&&!r(16)&&s(V,"propertyIsEnumerable",$,!0),_.f=function(e){return W(p(e))}),a(a.G+a.W+a.F*!q,{Symbol:N});for(var re="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ne=0;re.length>ne;)p(re[ne++]);for(var ie=L(p.store),oe=0;ie.length>oe;)h(ie[oe++]);a(a.S+a.F*!q,"Symbol",{"for":function(e){return i(F,e+="")?F[e]:F[e]=N(e)},keyFor:function(e){if(!X(e))throw TypeError(e+" is not a symbol!");for(var t in F)if(F[t]===e)return t},useSetter:function(){Y=!0},useSimple:function(){Y=!1}}),a(a.S+a.F*!q,"Object",{create:z,defineProperty:Q,defineProperties:K,getOwnPropertyDescriptor:Z,getOwnPropertyNames:ee,getOwnPropertySymbols:te});var ae=c(function(){x.f(1)});a(a.S+a.F*ae,"Object",{getOwnPropertySymbols:function(e){return x.f(b(e))}}),D&&a(a.S+a.F*(!q||c(function(){var e=N();return"[null]"!=R([e])||"{}"!=R({a:e})||"{}"!=R(Object(e))})),"JSON",{stringify:function(e){for(var t,r,n=[e],i=1;arguments.length>i;)n.push(arguments[i++]);if(r=t=n[1],(y(t)||e!==undefined)&&!X(e))return m(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!X(t))return t}),n[1]=t,R.apply(D,n)}}),N.prototype[U]||r(9)(N.prototype,U,N.prototype.valueOf),f(N,"Symbol"),f(Math,"Math",!0),f(n.JSON,"JSON",!0)},function(e,t,r){var n=r(25),i=r(37),o=r(26);e.exports=function(e){var t=n(e),r=i.f;if(r)for(var a,s=r(e),u=o.f,c=0;s.length>c;)u.call(e,a=s[c++])&&t.push(a);return t}},function(e,t,r){var n=r(13),i=r(67).f,o={}.toString,a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],s=function(e){try{return i(e)}catch(t){return a.slice()}};e.exports.f=function(e){return a&&"[object Window]"==o.call(e)?s(e):i(n(e))}},function(e,t,r){r(44)("asyncIterator")},function(e,t,r){r(44)("observable")},function(e,t,r){r(122);var n=r(2).Object;e.exports=function(e,t){return n.create(e,t)}},function(e,t,r){var n=r(7);n(n.S,"Object",{create:r(39)})},function(e,t,r){"use strict";t.__esModule=!0;t.gbAttrNames={otherInfo:"otherInfo",platformInfo:"platformInfo"},t.gbOtherInfoAttrNames={showLoginWndSource:"showLoginWndSource"},t.gbPlatformInfoAttrNames={deviceSign:"deviceSign"}},function(e,t,r){"use strict";function n(e,t){var r={_seenPlugins:[],_namespacePrefix:"",_namespaceRegexp:null,_legalNamespace:/^[a-zA-Z0-9_\-]+$/,_storage:function(){if(!this.enabled)throw new Error("store.js: No supported storage has been added! Add one (e.g store.addStorage(require('store/storages/cookieStorage')) or use a build with more built-in storages (e.g https://github.com/marcuswestin/store.js/tree/master/dist/store.legacy.min.js)");return this._storage.resolved},_testStorage:function(e){try{var t="__storejs__test__";e.write(t,t);var r=e.read(t)===t;return e.remove(t),r}catch(n){return!1}},_assignPluginFnProp:function(e,t){var r=this[t];this[t]=function(){function t(){if(r)return c(arguments,function(e,t){n[t]=e}),r.apply(i,n)}var n=s(arguments,0),i=this,o=[t].concat(n);return e.apply(i,o)}},_serialize:function(e){return(0,o["default"])(e)},_deserialize:function(e,t){if(!e)return t;var r="";try{r=JSON.parse(e)}catch(n){r=e}return r!==undefined?r:t}},n=l(r,_);return c(e,function(e){n.addStorage(e)}),c(t,function(e){n.addPlugin(e)}),n}var i=r(12),o=function(e){return e&&e.__esModule?e:{"default":e}}(i),a=r(11),s=a.slice,u=a.pluck,c=a.each,l=a.create,f=a.isList,d=a.isFunction,p=a.isObject;e.exports={createStore:n};var _={version:"2.0.3",enabled:!1,addStorage:function(e){this.enabled||this._testStorage(e)&&(this._storage.resolved=e,this.enabled=!0)},addPlugin:function(e){var t=this;if(f(e))return void c(e,function(e){t.addPlugin(e)});if(!u(this._seenPlugins,function(t){return e===t})){if(this._seenPlugins.push(e),!d(e))throw new Error("Plugins must be function values that return objects");var r=e.call(this);if(!p(r))throw new Error("Plugins must return an object of function properties");c(r,function(r,n){if(!d(r))throw new Error("Bad plugin property: "+n+" from plugin "+e.name+". Plugins should only return functions.");t._assignPluginFnProp(r,n)})}},get:function(e,t){var r=this._storage().read(this._namespacePrefix+e);return this._deserialize(r,t)},set:function(e,t){return t===undefined?this.remove(e):(this._storage().write(this._namespacePrefix+e,this._serialize(t)),t)},remove:function(e){this._storage().remove(this._namespacePrefix+e)},each:function(e){var t=this;this._storage().each(function(r,n){e(t._deserialize(r),n.replace(t._namespaceRegexp,""))})},clearAll:function(){this._storage().clearAll()},hasNamespace:function(e){return this._namespacePrefix=="__storejs_"+e+"_"},namespace:function(e){if(!this._legalNamespace.test(e))throw new Error("store.js namespaces can only have alhpanumerics + underscores and dashes");var t="__storejs_"+e+"_";return l(this,{_namespacePrefix:t,_namespaceRegexp:t?new RegExp("^"+t):null})},createStore:function(e,t){return n(e,t)}}},function(e,t,r){"use strict";e.exports={localStorage:r(126),"oldFF-globalStorage":r(127),"oldIE-userDataStorage":r(128),cookieStorage:r(129),sessionStorage:r(130),memoryStorage:r(131)}},function(e,t,r){"use strict";function n(){return l.localStorage}function i(e){return n().getItem(e)}function o(e,t){return n().setItem(e,t)}function a(e){for(var t=n().length-1;t>=0;t--){var r=n().key(t);e(i(r),r)}}function s(e){return n().removeItem(e)}function u(){return n().clear()}var c=r(11),l=c.Global;e.exports={name:"localStorage",read:i,write:o,each:a,remove:s,clearAll:u}},function(e,t,r){"use strict";function n(e){return l[e]}function i(e,t){l[e]=t}function o(e){for(var t=l.length-1;t>=0;t--){var r=l.key(t);e(l[r],r)}}function a(e){return l.removeItem(e)}function s(){o(function(e,t){delete l[e]})}var u=r(11),c=u.Global;e.exports={name:"oldFF-globalStorage",read:n,write:i,each:o,remove:a,clearAll:s};var l=c.globalStorage},function(e,t,r){"use strict";function n(e,t){if(!_){var r=u(e);p(function(e){e.setAttribute(r,t),e.save(f)})}}function i(e){if(!_){var t=u(e),r=null;return p(function(e){r=e.getAttribute(t)}),r}}function o(e){p(function(t){for(var r=t.XMLDocument.documentElement.attributes,n=r.length-1;n>=0;n--){var i=r[n];e(t.getAttribute(i.name),i.name)}})}function a(e){var t=u(e);p(function(e){e.removeAttribute(t),e.save(f)})}function s(){p(function(e){var t=e.XMLDocument.documentElement.attributes;e.load(f);for(var r=t.length-1;r>=0;r--)e.removeAttribute(t[r].name);e.save(f)})}function u(e){return e.replace(/^\d/,"___$&").replace(h,"___")}var c=r(11),l=c.Global;e.exports={name:"oldIE-userDataStorage",write:n,read:i,each:o,remove:a,clearAll:s};var f="storejs",d=l.document,p=function(){if(!d||!d.documentElement||!d.documentElement.addBehavior)return null;var e,t,r;try{t=new ActiveXObject("htmlfile"),t.open(),t.write('<script>document.w=window<\/script><iframe src="/favicon.ico"></iframe>'),t.close(),e=t.w.frames[0].document,r=e.createElement("div")}catch(n){r=d.createElement("div"),e=d.body}return function(t){var n=[].slice.call(arguments,0);n.unshift(r),e.appendChild(r),r.addBehavior("#default#userData"),r.load(f),t.apply(this,n),e.removeChild(r)}}(),_=(l.navigator?l.navigator.userAgent:"").match(/ (MSIE 8|MSIE 9|MSIE 10)\./),h=new RegExp("[!\"#$%&'()*+,/\\\\:;<=>?@[\\]^`{|}~]","g")},function(e,t,r){"use strict";function n(e){if(!e||!u(e))return null;var t="(?:^|.*;\\s*)"+escape(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*((?:[^;](?!;))*[^;]?).*";return unescape(d.cookie.replace(new RegExp(t),"$1"))}function i(e){for(var t=d.cookie.split(/; ?/g),r=t.length-1;r>=0;r--)if(f(t[r])){var n=t[r].split("="),i=unescape(n[0]),o=unescape(n[1]);e(o,i)}}function o(e,t){e&&(d.cookie=escape(e)+"="+escape(t)+"; expires=Tue, 19 Jan 2038 03:14:07 GMT; path=/")}function a(e){e&&u(e)&&(d.cookie=escape(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/")}function s(){i(function(e,t){a(t)})}function u(e){return new RegExp("(?:^|;\\s*)"+escape(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(d.cookie)}var c=r(11),l=c.Global,f=c.trim;e.exports={name:"cookieStorage",read:n,write:o,each:i,remove:a,clearAll:s};var d=l.document},function(e,t,r){"use strict";function n(){return l.sessionStorage}function i(e){return n().getItem(e)}function o(e,t){return n().setItem(e,t)}function a(e){for(var t=n().length-1;t>=0;t--){var r=n().key(t);e(i(r),r)}}function s(e){return n().removeItem(e)}function u(){return n().clear()}var c=r(11),l=c.Global;e.exports={name:"sessionStorage",read:i,write:o,each:a,remove:s,clearAll:u}},function(e,t,r){"use strict";function n(e){return u[e]}function i(e,t){u[e]=t}function o(e){for(var t in u)u.hasOwnProperty(t)&&e(u[t],t)}function a(e){delete u[e]}function s(e){u={}}e.exports={name:"memoryStorage",read:n,write:i,each:o,remove:a,clearAll:s};var u={}},function(e,t,r){"use strict";e.exports={defaults:r(133),dump:r(68),events:r(69),observe:r(134),expire:r(135),json2:r(70),operations:r(137),update:r(71),"v1-backcompat":r(138)}},function(e,t,r){"use strict";function n(){function e(e,t){r=t}function t(e,t){var n=e();return n!==undefined?n:r[t]}var r={};return{defaults:e,get:t}}e.exports=n},function(e,t,r){"use strict";function n(){function e(e,t,r){var n=this.watch(t,r);return r(this.get(t)),n}function t(e,t){this.unwatch(t)}return{observe:e,unobserve:t}}var i=r(69);e.exports=[i,n]},function(e,t,r){"use strict";function n(){function e(e,t,r,o){return this.hasNamespace(i)||n.set(t,o),e()}function t(e,t){if(!this.hasNamespace(i)){n.get(t,Number.MAX_VALUE)<=(new Date).getTime()&&this.remove(t)}return e()}function r(e,t){return this.hasNamespace(i)||n.remove(t),e()}var n=this.namespace(i);return{set:e,get:t,remove:r}}var i="expire_mixin";e.exports=n},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{"default":e}}var _stringify=__webpack_require__(12),_stringify2=_interopRequireDefault(_stringify),_typeof2=__webpack_require__(15),_typeof3=_interopRequireDefault(_typeof2);"object"!==("undefined"==typeof JSON?"undefined":(0,_typeof3["default"])(JSON))&&(JSON={}),function(){function f(e){return e<10?"0"+e:e}function this_value(){return this.valueOf()}function quote(e){return rx_escapable.lastIndex=0,rx_escapable.test(e)?'"'+e.replace(rx_escapable,function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var r,n,i,o,a,s=gap,u=t[e];switch(u&&"object"===(void 0===u?"undefined":(0,_typeof3["default"])(u))&&"function"==typeof u.toJSON&&(u=u.toJSON(e)),"function"==typeof rep&&(u=rep.call(t,e,u)),void 0===u?"undefined":(0,_typeof3["default"])(u)){case"string":return quote(u);case"number":return isFinite(u)?String(u):"null";case"boolean":case"null":return String(u);case"object":if(!u)return"null";if(gap+=indent,a=[],"[object Array]"===Object.prototype.toString.apply(u)){for(o=u.length,r=0;r<o;r+=1)a[r]=str(r,u)||"null";return i=0===a.length?"[]":gap?"[\n"+gap+a.join(",\n"+gap)+"\n"+s+"]":"["+a.join(",")+"]",gap=s,i}if(rep&&"object"===(void 0===rep?"undefined":(0,_typeof3["default"])(rep)))for(o=rep.length,r=0;r<o;r+=1)"string"==typeof rep[r]&&(n=rep[r],(i=str(n,u))&&a.push(quote(n)+(gap?": ":":")+i));else for(n in u)Object.prototype.hasOwnProperty.call(u,n)&&(i=str(n,u))&&a.push(quote(n)+(gap?": ":":")+i);return i=0===a.length?"{}":gap?"{\n"+gap+a.join(",\n"+gap)+"\n"+s+"}":"{"+a.join(",")+"}",gap=s,i}}var rx_one=/^[\],:{}\s]*$/,rx_two=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rx_three=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rx_four=/(?:^|:|,)(?:\s*\[)+/g,rx_escapable=/[\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},Boolean.prototype.toJSON=this_value,Number.prototype.toJSON=this_value,String.prototype.toJSON=this_value);var gap,indent,meta,rep;"function"!=typeof _stringify2["default"]&&(meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(e,t,r){var n;if(gap="",indent="","number"==typeof r)for(n=0;n<r;n+=1)indent+=" ";else"string"==typeof r&&(indent=r);if(rep=t,t&&"function"!=typeof t&&("object"!==(void 0===t?"undefined":(0,_typeof3["default"])(t))||"number"!=typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){function walk(e,t){var r,n,i=e[t];if(i&&"object"===(void 0===i?"undefined":(0,_typeof3["default"])(i)))for(r in i)Object.prototype.hasOwnProperty.call(i,r)&&(n=walk(i,r),n!==undefined?i[r]=n:delete i[r]);return reviver.call(e,t,i)}var j;if(text=String(text),rx_dangerous.lastIndex=0,rx_dangerous.test(text)&&(text=text.replace(rx_dangerous,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),rx_one.test(text.replace(rx_two,"@").replace(rx_three,"]").replace(rx_four,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}()},function(e,t,r){"use strict";function n(){function e(e,t,r,n,i,o){return a.call(this,"push",arguments)}function t(e,t){return a.call(this,"pop",arguments)}function r(e,t){return a.call(this,"shift",arguments)}function n(e,t,r,n,i,o){return a.call(this,"unshift",arguments)}function i(e,t,r,n,i,a){var c=s(arguments,2);return this.update(t,{},function(e){if("object"!=(void 0===e?"undefined":(0,o["default"])(e)))throw new Error('store.assign called for non-object value with key "'+t+'"');return c.unshift(e),u.apply(Object,c)})}function a(e,t){var r,n=t[1],i=s(t,2);return this.update(n,[],function(t){r=Array.prototype[e].apply(t,i)}),r}return{push:e,pop:t,shift:r,unshift:n,assign:i}}var i=r(15),o=function(e){return e&&e.__esModule?e:{"default":e}}(i),a=r(11),s=a.slice,u=a.assign,c=r(71);e.exports=[c,n]},function(e,t,r){"use strict";function n(){return this.disabled=!this.enabled,{has:i,transact:o,clear:a,forEach:s,getAll:u,serialize:c,deserialize:l}}function i(e,t){return this.get(t)!==undefined}function o(e,t,r,n){null==n&&(n=r,r=null),null==r&&(r={});var i=this.get(t,r),o=n(i);this.set(t,o===undefined?i:o)}function a(e){return this.clearAll.call(this)}function s(e,t){return this.each.call(this,function(e,r){t(r,e)})}function u(e){return this.dump.call(this)}function c(e,t){return(0,d["default"])(t)}function l(e,t){if("string"!=typeof t)return undefined;try{return JSON.parse(t)}catch(r){return t||undefined}}var f=r(12),d=function(e){return e&&e.__esModule?e:{"default":e}}(f),p=r(68),_=r(70);e.exports=[p,_,n]},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.RequestServer=undefined;var i=r(12),o=n(i),a=r(40),s=n(a),u=r(0),c=n(u);t.RequestServer=function(){function e(t){(0,c["default"])(this,e),this._requester=t}return e.prototype.get=function(e,t,r,n){return this._request("GET",e,t,r,n)},e.prototype.post=function(e,t,r,n){return this._request("POST",e,t,r,n)},e.prototype._request=function(e,t,r,n,i){var a=this;return new s["default"](function(s,u){var c=t,l=null,f=i||{};"GET"===e?n&&(c+=a._toUrlParams(n)):l=f.stringifyJsonDatas?(0,o["default"])(n):n,a._requester[e.toLocaleLowerCase()](c,r,l).then(function(e){var t=e;if(f.parseJsonResult&&"string"==typeof e.data)try{t.data=JSON.parse(e.data)}catch(r){u(r)}s(t)})["catch"](function(e){u(e)})})},e.prototype._toUrlParams=function(e){var t="";if(e){var r=[];for(var n in e)r.push(n+"="+e[n]);t=r.join("&")}return t},e}()},function(e,t,r){"use strict";t.__esModule=!0,t.gbStat=t.gbStatAttrNames=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=r(23),a=r(38),s=(t.gbStatAttrNames={monitor:"monitor",userBehaviors:"userBehaviors"},function(){function e(){(0,i["default"])(this,e),this._gbHelper=new a.GBHelper(o.gbAttrNames.stat)}return e.prototype.hasAttr=function(e){return this._gbHelper.hasAttr(e)},e.prototype.setAttr=function(e,t){this._gbHelper.setAttr(e,t)},e.prototype.getAttr=function(e){return this._gbHelper.getAttr(e)},e}());t.gbStat=new s},function(e,t,r){"use strict";function n(){return{appId:(0,o.forceGetTypeValue)("string",a.gbConfig.getAppId(),""),appName:(0,o.forceGetTypeValue)("string",a.gbConfig.getAppName(),""),appVersion:(0,o.forceGetTypeValue)("string",a.gbConfig.getAppVersion(),""),deviceSign:(0,o.forceGetTypeValue)("string",(0,s.deviceid)(),""),sdkVersion:(0,o.forceGetTypeValue)("string",a.gbConfig.getSDKVersion(),""),platform:(0,o.forceGetTypeValue)("string",(0,s.Platform)(),"")}}function i(){return"https://"+a.gbConfig.getAnalysisServer()}t.__esModule=!0,t.getPublicData=n,t.getStatServerUrl=i;var o=r(84),a=r(60),s=r(49)},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.StatServer=undefined;var i=r(12),o=n(i),a=r(0),s=n(a),u=r(46),c=r(143),l=r(144),f=r(23),d=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t["default"]=e,t}(f);t.StatServer=function(){function e(t,r){var n=arguments.length>2&&arguments[2]!==undefined&&arguments[2];(0,s["default"])(this,e),this._topic=r,this._preUrl=(t||"https://xluser-test-ssl.n0808.com")+"/analysis-report/v1/"+r+"?msg=",this._needEncrypt=!!n,this._requestServer=c.requestServerLoader.get(),this._publicData={}}return e.prototype._report=function(e){var t="",r=(0,o["default"])(e),n=l.base64ServerLoader.get().encode(r);n=n.replace(/\+/g,"-"),n=n.replace(/\//g,"_");var i=n.length,a=0;i>=2&&"="===n.charAt(i-1)&&(a="="===n.charAt(i-2)?2:1),t=n.substr(0,i-a);var s=this._preUrl+t,u=!0;if(d.getEnvType()!==d.gbEnvTypes.pluginIndex){var c=c||parent.xdas;c&&"function"==typeof c.fireStatEvent&&(u=!1,c.fireStatEvent(s))}u&&this._requestServer.get(s,null,null,null)["catch"](function(e){})},e.prototype.setPublicData=function(e){this._publicData=e},e.prototype.stat=function(e){var t=e||{},r=(0,u.combineJsonObject)({reportTime:(0,u.dateToTimeString)(new Date)},this._publicData);r=(0,u.combineJsonObject)(r,t),this._report(r)},e}()},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.requestServerLoader=t.RequestServerLoader=undefined;var i=r(0),o=n(i),a=r(104),s=n(a),u=r(139),c=t.RequestServerLoader=function(){function e(){(0,o["default"])(this,e),this._requestServer=new u.RequestServer(s["default"])}return e.prototype.get=function(){return this._requestServer},e}();t.requestServerLoader=new c},function(e,t,r){"use strict";t.__esModule=!0,t.base64ServerLoader=t.Base64ServerLoader=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=function(){function e(){(0,i["default"])(this,e)}return e.prototype.encode=function(e){return Base64.encode(e)},e}(),a=t.Base64ServerLoader=function(){function e(){(0,i["default"])(this,e),this._base64Server=new o}return e.prototype.get=function(){return this._base64Server},e}();t.base64ServerLoader=new a},function(e,t,r){"use strict";t.__esModule=!0;var n=r(15),i=function(e){return e&&e.__esModule?e:{"default":e}}(n);t["default"]=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":(0,i["default"])(t))&&"function"!=typeof t?e:t}},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0;var i=r(179),o=n(i),a=r(95),s=n(a),u=r(15),c=n(u);t["default"]=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":(0,c["default"])(t)));e.prototype=(0,s["default"])(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(o["default"]?(0,o["default"])(e,t):e.__proto__=t)}},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){for(var r in e)if(e.hasOwnProperty(r)){var n=r+"="+encodeURIComponent(e[r]);t.push(n)}return t}t.__esModule=!0,t.report_helper=undefined;var o=r(52),a=n(o),s=r(0),u=n(s),c=r(28),l=n(c),f=r(97),d=r(51),p=r(49),_=(0,d.getDomain)(location.href),h=function(){function e(){(0,u["default"])(this,e),this.data=[],this.xrInt=!1}return e.prototype.add=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:3,r={url:"",errorcode:"",responsetime:"",retrynum:"0",serverip:"",cmdid:"",domain:_,b_type:f.CONFIG.LOGIN_ID,platform:"1",clientversion:""};for(var n in r)void 0!==e[n]&&(r[n]=e[n]);this.data.push(r),this.data.length>=t&&this.exec()},e.prototype.push=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=window.Xreport?window.Xreport:window.parent.Xreport;t&&(this.xrInt||(this.xrInt=!0,t.push({type:"conf",global:(0,a["default"])({},(0,p.baseParams2)(),{category:"i_login",server:"xluser-web-login-fail"})})),t.push({type:"now",data:e}))},e.prototype.exec=function(e){var t=this.data.length,r=[];if(0===t)return!0;r.push("cnt="+t);for(var n=0;n<t;++n){r=i(this.data[n],r)}var o="http://stat.login."+f.CONFIG.DOMAIN+":1800/report";return o=o+"?"+r.join("&"),"function"==typeof e?l["default"].getJson(o,{},e):this.image(o),this.data=[],!0},e.prototype.reportRegister=function(e){var t=[],r={regtype:"",errorcode:"",responsetime:"",domain:_,bustype:f.CONFIG.LOGIN_ID,platform:"1",clientversion:""};t.push("op=regStat"),t.push("response=json");for(var n in r)void 0!==e[n]&&(r[n]=e[n]);t=i(r,t);var o="https://zhuce."+f.CONFIG.DOMAIN+"/regapi/?"+t.join("&");this.image(o)},e.prototype.image=function(e){(new Image).src=e},e}(),g=new h;t.report_helper=function(e){g.push((0,a["default"])({},e,{action:"login"}))};t["default"]=g},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(){var e=b["default"].AUTO_LOGIN_EXPIRE_TIME;e&&"number"==typeof e&&e>0&&(S["default"].enabled?S["default"].set("xl_autologin",1):v["default"].setCookie("xl_autologin",1,1e3*e,b["default"].DOMAIN))}function o(){S["default"].enabled?(S["default"].remove("xl_autologin"),S["default"].remove("loginkey")):v["default"].delCookie("xl_autologin"),v["default"].delCookie("loginkey")}function a(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},r=arguments[2];J=e,v["default"].delCookie("_s.login_"),v["default"].delCookie("_s.captcha_"),v["default"].setCookie(b["default"].LOGIN_TYPE_COOKIE_NAME,e+"_1",0,b["default"].DOMAIN),"file:"===location.protocol&&(t.LOGIN_TYPE=e);var n=b["default"].LOGIN_KEY_NAME;switch(S["default"].enabled&&v["default"].getCookie(n)&&S["default"].set(n,v["default"].getCookie(n)),e){case W.ACCOUNT:case W.MOBILE:j.statLoginResult(!0,r)}xll.loginExtFun(t)}function s(){b["default"].SET_ROOT_DOMAIN?xll.registerFunc():window.parent.location.href=b["default"].REGISTER_SUCCESS_URL}function u(e){xll.loginFetch(e)}function c(e,t,r){switch(t){case 3:case 4:j.statLoginResult(!1,r)}xll.loginFail(e,t)}function l(e){return function(){function t(){var e,t,r,n,i,o=!1,s=!1,u="http://dynamic.aq."+b["default"].DOMAIN+"/";e||(t=a.getDoms(),B=a.getErrorMsg(),e=!0,n=function(){return t.verifyCode.value="",t.verifyCode.parentNode.className="",(0,w.register)("captcha",{img:t.getVerifyCode,t:"MEA"},function(e,t){}),!1},n(),v["default"].bind(t.getVerifyCode,"click.x",n),function(){if(""===t.verifyCode.value)return s=!1,a.showError("请输入正确的图形验证码",t.verifyCode.nextSibling.nextSibling),t.verifyCode.parentNode.className="",!1;(0,w.register)("checkRegisterCaptcha",{code:t.verifyCode.value},function(e){return 200==e.result&&200==e.data?(s=!0,a.hideError(t.verifyCode.nextSibling.nextSibling),t.verifyCode.parentNode.className="correct",!0):(s=!1,a.showError("请输入正确的图形验证码",t.verifyCode.nextSibling.nextSibling),n(),t.verifyCode.parentNode.className="",!1)})},r=function(){if(a.checkMobile()){if(!s)return void a.showError("请输入正确的图形验证码",t.verifyCode.nextSibling.nextSibling);var e={m:"set_iframe_send",mobile:t.mobile.value,verifycode:t.verifyCode.value};o||(o=!0,v["default"].getJson(u+"interface/sms",e,function(e){return o=!1,0!=e.result?(n(),void a.showError(B[e.result],t.mobileCode.nextSibling.nextSibling)):void a.setSmsButtonGrey(t.getMobileCode)},"jsoncallback"))}},v["default"].bind(t.getMobileCode,"click.x",r),i=function(){if(a.checkMobile()){if(!s)return void a.showError("请输入正确的图形验证码",t.verifyCode.nextSibling.nextSibling,t.verifyCode);if(a.checkMobileCode()){var e={m:"set_iframe_check",mobile:t.mobile.value,code:t.mobileCode.value};o||(o=!0,v["default"].getJson(u+"interface/sms",e,function(e){return o=!1,0!=e.result?(t.mobileCode.value="",s=!1,n(),void a.showError(B[e.result],t.mobileCode.nextSibling.nextSibling)):(t.bindForm.style.display="none",void(t.succForm.style.display=""))},"jsoncallback"))}}},v["default"].bind(t.submit,"click.x",i),v["default"].bind(t.closeBtn,"click.x",function(){xll.closeBindFunc()}))}function r(e){if(!i){var t=window.checkDomainAllowed(),r=(t[0],t[1]);b["default"].DOMAIN=r,(0,G.initConfig)(b["default"],e,{not_allowd:{XL_CLIENT_PATH:1,DEBUG:1},except:",AUTO_LOGIN_EXPIRE_TIME,UI_STYLE,LOGIN_ID,"});var n=v["default"].getCookie(),o=b["default"].LOGIN_TYPE_COOKIE_NAME;if(J=n[o],"string"==typeof J&&(J=J.split("_"),"1"===J[1]?"1":"0"),J=J===V?-1:parseInt(J),(J===V||J!==J||J<-1||J>5)&&(J=-1),b["default"].SET_ROOT_DOMAIN&&xll.getInitedFuns().length>0)return void xll.initedFuns();f(J)}}function n(e){xll.PARAMS.CAN_GSLB&&parent.gslb&&(parent.gslb.uname(e),parent.gslb.fetch({host:[xll.PARAMS.SERVER_LOGIN[0]],username:e}))}var i,o,a=void 0;return e.TYPE=W,{getLoginBox:function(e){T["default"].load(function(){T["default"].container&&(T["default"].container.style.display="");var t=v["default"]["class"]("text_cite");U["default"].forEach(t,function(e){if(""==e.id){var t=e.href;e.href=t+"?purl="+parent.location.href}}),a=T["default"].UI;var r="";if(h.gbConfig.getClientFeature()&&Y)r="clientLoginGuide",k.clientLoginGuide.init();else if(""!==b["default"].LOGIN_TYPES&&"login"===b["default"].DEFUALT_UI)0==b["default"].LOGIN_TYPES.indexOf("1")?(r=(0,g.getUITypeNameById)(b["default"].DEFUALT_UI,"1"),x.accountLogin.init()):0==b["default"].LOGIN_TYPES.indexOf("2")?(r=(0,g.getUITypeNameById)(b["default"].DEFUALT_UI,"2"),D.mobileLogin.init()):0==b["default"].LOGIN_TYPES.indexOf("4")&&(r=(0,g.getUITypeNameById)(b["default"].DEFUALT_UI,"4"),L.qrLogin.init()),b["default"].THIRD_LOGIN_DISPLAY&&R.thirdLogin.init();else{if(""===b["default"].REGISTER_TYPES||"register"!==b["default"].DEFUALT_UI)throw new Error("请配置登录和注册方式");0==b["default"].REGISTER_TYPES.indexOf("2")?(r=(0,g.getUITypeNameById)(b["default"].DEFUALT_UI,"2"),A.mobileRegister.init()):0==b["default"].REGISTER_TYPES.indexOf("1")?(r=(0,g.getUITypeNameById)(b["default"].DEFUALT_UI,"1"),O.mailRegister.init()):0==b["default"].REGISTER_TYPES.indexOf("3")?(r=(0,g.getUITypeNameById)(b["default"].DEFUALT_UI,"3"),C.accountRegister.init()):0==b["default"].REGISTER_TYPES.indexOf("5")&&(r=(0,g.getUITypeNameById)(b["default"].DEFUALT_UI,"5"),P.mobileRegisterWithPassword.init())}b["default"].SET_ROOT_DOMAIN&&xll.uiChangeFunc(r),a.onShowUI=function(e){switch(r=e,e){case"clientLoginGuide":k.clientLoginGuide.init();break;case"accountLogin":x.accountLogin.init();var t=a.accountLoginDoms();t&&t.input_username&&n(t.input_username.value);break;case"mobileLogin":D.mobileLogin.init();var i=a.mobileLoginDoms();i&&i.input_mobile&&n(i.input_mobile.value);break;case"qrLogin":L.qrLogin.init();break;case"mailRegister":O.mailRegister.init();break;case"mobileRegister":a.onestep?N.mobileRegisterOneStep.init():A.mobileRegister.init();break;case"mobileRegisterWithPassword":P.mobileRegisterWithPassword.init();break;case"accountRegister":C.accountRegister.init()}b["default"].SET_ROOT_DOMAIN&&xll.uiChangeFunc(r)},v["default"].setCookie(b["default"].LOGIN_TYPE_COOKIE_NAME,W.INITED+"",0,b["default"].DOMAIN),v["default"].bind(document,"keypress.xl",function(e){if("13"==e.keyCode){var t={submitType:0};switch(r){case"clientLoginGuide":k.clientLoginGuide.submit();break;case"accountLogin":x.accountLogin.submit(t);break;case"mobileLogin":D.mobileLogin.submit(t);break;case"mailRegister":O.mailRegister.submit();break;case"mobileRegister":A.mobileRegister.submit(t);break;case"mobileRegisterWithPassword":P.mobileRegisterWithPassword.submit();break;case"accountRegister":C.accountRegister.submit()}}else"27"==e.keyCode&&"popup"===b["default"].UI_TYPE&&!0===b["default"].SET_ROOT_DOMAIN&&xll.closeFunc()}),v["default"].bind(v["default"].id("al_u"),"change.x",function(e){n(e.target.value)}),"popup"===b["default"].UI_TYPE&&b["default"].DEFUALT_BACKGROUND&&a.setBackground(b["default"].DEFUALT_BACKGROUND),"popup"!==b["default"].UI_THEME&&"embed"!==b["default"].UI_THEME||a.setAlertError(b["default"].ALERT_ERROR),""!=b["default"].DEFAULT_AVATAR&&a.setAvatar&&a.setAvatar(b["default"].DEFAULT_AVATAR),e&&e()})},getBindBox:function(){T["default"].load(function(){T["default"].container&&(T["default"].container.style.display=""),t()})},setBackgroud:function(e){if("embed"===b["default"].UI_THEME)throw new Error("内嵌主题暂时不支持设置背景图片");e||(e=(b["default"].ALL_HTTPS?"https://":"http://")+"i."+b["default"].DOMAIN+"/login/theme/popup/images/layer_bg.jpg"),a.setBackground(e)},showUI:function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null,r="";"login"===e?t?r=t:0==b["default"].LOGIN_TYPES.indexOf("1")?r="accountLogin":0==b["default"].LOGIN_TYPES.indexOf("2")&&(r="mobileLogin"):"register"===e&&(t?r=t:0==b["default"].REGISTER_TYPES.indexOf("2")?r="mobileRegister":0==b["default"].REGISTER_TYPES.indexOf("1")?r="mailRegister":0==b["default"].REGISTER_TYPES.indexOf("3")?r="accountRegister":0==b["default"].REGISTER_TYPES.indexOf("5")&&(r="mobileRegisterWithPassword")),r&&(a.showUI(r),xll.uiChangeFunc(r))},showError:function(e){o=a.accountLoginDoms(),a.showError(e,o.warn)},init:r}}()}function f(e){if(!Y){var t=v["default"].getCookie();if(!v["default"].isSessionid(t.sessionid)){var r=b["default"].LOGIN_KEY_NAME,n=b["default"].LOGIN_TYPE_COOKIE_NAME,i=t[r]||S["default"].enabled&&S["default"].get(r);if(b["default"].AUTO_LOGIN_EXPIRE_TIME>0&&t.userid&&t.userid>0&&i&&(S["default"].enabled&&S["default"].get("xl_autologin")||t.xl_autologin)||xll.xdasClient()){if(""==t.deviceid||t.deviceid==undefined)return;q=!0;var o={loginkey:i,userid:t.userid};u(1),xll.req.loginkey(o,function(e){if(q=!1,"0"!=e.blogresult)return c(e.blogresult,1),S["default"].enabled&&S["default"].remove(r),void v["default"].delCookie(r);S["default"].enabled&&S["default"].set("xl_autologin_info",(new Date).getTime()+"|0"),a(W.AUTOWEB,e)})}-1!==e&&v["default"].setCookie(n,"0",0,b["default"].DOMAIN)}}}t.__esModule=!0,t.LOGIN_TYPE=undefined,t.setAutoLogin=i,t.delAutoLogin=o,t.loginSuccess=a,t.registerSuccess=s,t.loginFetch=u,t.loginFail=c,t.GetUIManager=l;var d=r(23),p=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t["default"]=e,t}(d),_=r(38),h=r(60),g=r(165),m=r(28),v=n(m),y=r(73),b=n(y),w=r(152),E=r(77),S=n(E),I=r(159),T=n(I),k=r(216),x=r(217),C=r(219),L=r(220),O=r(225),A=r(195),P=r(226),N=r(227),D=r(228),R=r(229),M=r(24),U=n(M),G=r(190),F=r(98),j=F.userBehaviorsStatServerLoader.get(),B=void 0,V=void 0,q=!1,H=new _.GBHelper(p.gbAttrNames.clientFeatureApi),Y=H.getTarget(Y),J=void 0,W=t.LOGIN_TYPE={ORIGIN:-1,INITED:0,ACCOUNT:1,CLIENT:2,AUTOWEB:3,AUTOCLIENT:4,THIRD:5,MOBILE:6,QRAPP:7}},,function(e,t,r){e.exports=r(160)},function(e,t,r){"use strict";t.__esModule=!0;var n=r(40),i=function(e){return e&&e.__esModule?e:{"default":e}}(n);t["default"]=function(e){return function(){var t=e.apply(this,arguments);return new i["default"](function(e,r){function n(o,a){try{var s=t[o](a),u=s.value}catch(c){return void r(c)}if(!s.done)return i["default"].resolve(u).then(function(e){n("next",e)},function(e){n("throw",e)});e(u)}return n("next")})}}},function(e,t,r){"use strict";function n(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function o(e,t,r){var n,i=(0,w.baseParamsRegister)(t);switch("function"==typeof t&&(r=t),e){case"register":i.op="usernameReg",i.from=p["default"].LOGIN_ID,t.code||delete i.code,s(p["default"].SERVER_REGISTER,i,r);break;case"captcha":if(!t.img)throw new Error("post argument error");var o,u=t.img,c=t.t?t.t:h["default"].getCookie("verify_type");c=c||"";var l=a("captcha","/image?t="+c+"&cachetime="+(new Date).getTime(),!1),f=!1;u.onerror=function(){u.onerror=u.onload=u.onreadystatechange=null,clearTimeout(o),r&&r(1,"获取验证码失败，请手动刷新")},u.onload=u.onreadystatechange=function(){f||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(f=!0,clearTimeout(o),u.onerror=u.onload=u.onreadystatechange=null,r&&r(0,"刷新成功"))},o=setTimeout(function(){u.onerror=u.onload=u.onreadystatechange=null,r&&r(1,"获取验证码失败，请手动刷新")},L),u.src=l;break;case"mobilelogin":if(!t.mobile||!t.code)throw new Error("post argument error");i.op="mobileReg",i.from=p["default"].LOGIN_ID,i.mobile=t.mobile,i.code=t.code,i.regtype="mobileLogin",n=p["default"].SERVER_REGISTER,s(n,i,function(e){r&&r(e)});break;case"getsmscode":if(!t.mobile||!t.type)throw new Error("post argument error");i.op="sendSms",i.from=p["default"].LOGIN_ID,i.mobile=t.mobile,i.verifyCode=t.verifyCode,i.verifyKey=h["default"].getCookie("VERIFY_KEY"),i.verifyType="MEA",i.v=2,i.type="register"==t.type?1:2,n=p["default"].SERVER_REGISTER,h["default"].registerPost(n,i,function(e){r&&r(e)});break;case"checkbind":if(!t.account||!t.type)throw new Error("post argument error");i.op="checkBind",i.from=p["default"].LOGIN_ID,i.response="jsonp",i.account=t.account,i.type="mail"===t.type?4:1,n=p["default"].SERVER_REGISTER,D.jsonp(n,i,function(e){r&&r(e)});break;case"mobileregister":if(!t.mobile||!t.code)throw new Error("post argument error");i.op="mobileReg",i.from=p["default"].LOGIN_ID,i.mobile=t.mobile,i.code=t.code,n=p["default"].SERVER_REGISTER,s(n,i,function(e){r&&r(e)});break;case"mobileregisterpwd":if(!(t.mobile&&t.code&&t.password))throw new Error("post argument error");i.op="mobileRegPwd",i.from=p["default"].LOGIN_ID,i.mobile=t.mobile,i.code=t.code,i.pwd=t.password,n=p["default"].SERVER_REGISTER,s(n,i,function(e){r&&r(e)});break;case"setpassword":if(!t.password)throw new Error("post argument error");i.op="changePassword",i.from=p["default"].LOGIN_ID,i.pwd=t.password,n=p["default"].SERVER_REGISTER,h["default"].registerPost(n,i,function(e){r&&r(e)});break;case"mailregister":if(!t.mail||!t.password)throw new Error("post argument error");i.op="emailReg",i.from=p["default"].LOGIN_ID,i.email=t.mail,i.pwd=t.password,t.code&&(i.code=t.code),n=p["default"].SERVER_REGISTER,s(n,i,function(e){r&&r(e)});break;case"isNeedValidate":i.op="needValidate",i.from=p["default"].LOGIN_ID,i.response="jsonp",n=p["default"].SERVER_REGISTER,D.jsonp(n,i,function(e){r&&r(e)});break;case"registerCaptcha":if(!t.img)throw new Error("post argument error");var u=t.img,l=p["default"].SERVER_REGISTER+"?op=validateImg&from="+p["default"].LOGIN_ID+"&size=M&chachtime="+(new Date).getTime(),f=!1;u.onerror=function(){u.onerror=u.onload=u.onreadystatechange=null,clearTimeout(o),r&&r(1,"获取验证码失败，请手动刷新")},u.onload=u.onreadystatechange=function(){f||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(f=!0,u.onerror=u.onload=u.onreadystatechange=null,r&&r(0,"刷新成功"))},u.src=l;break;case"checkRegisterCaptcha":var d=t.code,n=p["default"].SERVER_REGISTER+"?op=CheckVerifyCode",i=[];i.code=d,i.key=h["default"].getCookie("VERIFY_KEY"),i.type="MEA",i.response="jsonp",D.jsonp(n,i,function(e){r&&r(e)});break;default:throw new Error("not support action: "+e)}}function a(e,t){var r=!(arguments.length>2&&arguments[2]!==undefined)||arguments[2],n=!1;switch(x.getEnvType()){case x.gbEnvTypes.outsideIframe:n=window.gslb;break;case x.gbEnvTypes.insideIframe:n=parent.gslb}var i=xlQuickLogin.PARAMS||xll.PARAMS||{};O=i.SERVER_LOGIN||P,n&&i.CAN_GSLB&&(O=n.gslbUse("login",P),A=n.gslbUse("captcha",A));var o="captcha"===e?A:O,a=y["default"].map(o,function(e){return"https://"+e+t});return r?a:a[0]}function s(e,t,r){var n=(new Date).getTime();h["default"].registerPost(e,t,function(e){m["default"].reportRegister({regtype:t.regtype?t.regtype:t.op,errorcode:e.result,responsetime:((new Date).getTime()-n)/1e3}),r(e)})}t.__esModule=!0,t.register=t.req=undefined;var u=r(0),c=i(u),l=r(164),f=n(l),d=r(73),p=i(d),_=r(28),h=i(_),g=r(147),m=i(g),v=r(24),y=i(v),b=r(62),w=r(49),E=r(153),S=i(E),I=r(147),T=r(51),k=r(23),x=n(k);p["default"].DOMAIN=(0,T.getDomain)();var C="."+p["default"].DOMAIN,L=(p["default"].RETRY_LOGIN_ON_SERVER_ERROR,700),O=["https://xluser-ssl","https://xluser2-ssl","https://xluser3-ssl"],A=["captcha-ssl."+p["default"].DOMAIN,"captcha2-ssl."+p["default"].DOMAIN,"https://captcha3-ssl."+p["default"].DOMAIN],P=["xluser-ssl."+p["default"].DOMAIN,"xluser2-ssl."+p["default"].DOMAIN,"xluser3-ssl."+p["default"].DOMAIN],N=function(){function e(){(0,c["default"])(this,e),this.baseUrl="/xluser.core.login/v3/"}return e.prototype.loginRequest=function(e,t,r,n,i){S["default"].post(t,r,{enctype:"application/x-www-form-urlencoded"}).then(function(e){return n(e)})},e.prototype.post=function(e,t,r){S["default"].post(e,t,I.report_helper).then(function(e){return r(e)})["catch"](function(e){return r(e)})},e.prototype.jsonp=function(e,t,r){var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:5e3,i=arguments.length>4&&arguments[4]!==undefined?arguments[4]:"callback";S["default"].jsonp(e,t,i,n,I.report_helper).then(function(e){return r(e)})["catch"](function(e){return r(e)})},e.prototype.loginkey=function(e,t){var r=this.baseUrl+"loginkey",n={},i=(0,w.baseParams2)();for(var o in i)n[o]=i[o];n.loginKey=e.loginkey,n.userName=e.userid,n.format="cookie";var s=a("login",r);this.post(s,n,function(e){!!t&&t(e)})},e.prototype.getuserinfo=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},r=this.baseUrl+"getuserinfo",n={},i=(0,w.baseParams2)(),o=h["default"].getUrlParams(window.location.href);for(var s in i)n[s]=o[s]?o[s]:i[s];n.userID=t.userID||h["default"].getCookie("userid"),n.sessionID=t.sessionID||h["default"].getCookie("sessionid"),n.vasid=t.vasid||"2,14,33,34,35",t.appid&&(n.appid=t.appid),t.appName&&(n.appName=t.appName),t.devicesign&&(n.devicesign=t.devicesign),n.format="jsonp";var u=a("login",r);this.jsonp(u,n,function(t){e(t)},L)},e.prototype.ping=function(e,t){var r,n=this.baseUrl+"ping",i=(0,w.baseParams2)(e);h["default"].delCookie("blogresult",C),r=a("login",n),this.post(r,i,function(e){t&&t(e.blogresult)})},e.prototype.logout=function(e){var t=this.baseUrl+"logout",r={userID:h["default"].getCookie("userid")||h["default"].getCookie("userID"),sessionID:h["default"].getCookie("sessionid")},n=(0,w.baseParams2)(r),i=a("login",t);this.post(i,n,function(t){f.deleteCookieUserInfo(),e&&e()},L)},e.prototype.sessionlogin=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=arguments[1],r=this.baseUrl+"sessionlogin",n=(0,w.baseParams2)(e);n.sessionType=(0,b.checkAsPCFlow)()?1:0,n.sessionFromAppid=window.APPID||"";var i=a("login",r);this.post(i,n,function(e){t&&t(e)})},e.prototype.jumplogin=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=arguments[1],r=this.baseUrl+"jumplogin",n=(0,w.baseParams2)(e),i=a("login",r);this.post(i,n,function(e){t&&t(e)})},e}(),D=new N;t.req=D,t.register=o},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:b,r=document.createElement("form");return r.style.display="none",r.method=e,r.enctype=t,r.encoding=t,r.acceptCharset="UTF-8",document.body.appendChild(r),r}function o(e){var t=e,r={errorCode:"blogresult",errorDesc:"errdesc",userID:"userid",loginKey:"loginkey",nickName:"usernick",sessionID:"sessionid",userName:"usrname",userNewNo:"usernewno",account:"score",verifyType:"VERIFY_KEY",errorIsRetry:"error_retry"};for(var n in r)if(e[n]!=undefined){var i=r[n];t[i]=e[n]}for(var o in t)store.set(o,t[o]);return t}t.__esModule=!0,t.Request=undefined;var a=r(150),s=n(a),u=r(151),c=n(u),l=r(40),f=n(l),d=r(0),p=n(d),_=r(73),h=n(_),g=r(28),m=n(g),v=r(24),y=n(v),b="multipart/form-data",w=t.Request=function(){function e(){(0,p["default"])(this,e)}return e.prototype.post=function(e,t){var r=this,n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null,i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:{enctype:b};return new f["default"](function(){var o=(0,c["default"])(s["default"].mark(function a(o,u){var c,l,f,d;return s["default"].wrap(function(a){for(;;)switch(a.prev=a.next){case 0:c=y["default"].isArray(e)?e:[e],l=c.length,f=0;case 3:if(!(f<l)){a.next=24;break}return a.prev=4,a.next=7,r._post(c[f],t,n,i);case 7:if((d=a.sent)&&"undefined"!=typeof d.error_retry&&0!=d.error_retry&&f!==l-1){a.next=11;break}return o(d),a.abrupt("break",24);case 11:a.next=19;break;case 13:if(a.prev=13,a.t0=a["catch"](4),f!==l-1){a.next=19;break}return u(a.t0),a.abrupt("break",24);case 19:return a.next=21,r._sleep(1e3);case 21:f++,a.next=3;break;case 24:case"end":return a.stop()}},a,r,[[4,13]])}));return function(e,t){return o.apply(this,arguments)}}())},e.prototype._post=function(e,t){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null,n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:{enctype:b};n=n||{enctype:b};var i=n.enctype!==undefined?n.enctype:b,o=null,a=n.requestMonitorStatHelper?n.requestMonitorStatHelper:null;return a&&(a.newStat(),o=a.getStat()),this.request("POST",e,t,r,5e3,o,i)},e.prototype.get=function(e,t){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;return this.request("GET",e,t,r)},e.prototype.request_helper=function(e,t,r,n,i){var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0,a=arguments.length>6&&arguments[6]!==undefined?arguments[6]:null,s=t.length,u=t[o],c=o,l=this;if(c>=s)return n&&n("TIMEOUT");var f=(new Date).getTime();l[e](u,r).then(function(e){return n&&n(e)})["catch"](function(o){return a&&a({url:u,errorcode:o,responsetime:((new Date).getTime()-f)/1e3,retrynum:c}),c+=1,l.request_helper(e,t,r,n,i,c,a)})},e.prototype.jsonp=function(e,t){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"callback",n=this,i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:5e3,o=arguments.length>4&&arguments[4]!==undefined?arguments[4]:null;return y["default"].isArray(e)?new f["default"](function(o,a){return 0==e.length?a("TIMEOUT"):n.jsonp(e.shift(),t,r,i).then(o)["catch"](function(s){n.jsonp(e,t,r,i).then(o)["catch"](function(e){return a(e)})})}):new f["default"](function(a,s){var u=document.createElement("script"),c=document.getElementsByTagName("head")[0],l="jsonp"+(new Date).getTime()+parseInt(1e3*Math.random()),f=n.url_param(t),d=null;e+=(e.indexOf("?")>=0?"&":"?")+f+"&"+r+"="+l,window[l]=function(e){d&&clearTimeout(d),a(e);try{delete window[l]}catch(t){}c.removeChild(u)},i>0&&(d=setTimeout(function(){o&&o({url:e,method:"jsonp",errorcode:"timeout"}),s("TIMEOUT")},i)),u.onerror=function(){d&&clearTimeout(d),o&&o({url:e,method:"jsonp",errorcode:"error"}),s("TIMEOUT")},u.async="async",u.src=e,c.insertBefore(u,c.firstChild)})},e.prototype.request=function(e,t,r){var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:null,a=arguments.length>4&&arguments[4]!==undefined?arguments[4]:5e3,s=this,u=arguments.length>5&&arguments[5]!==undefined?arguments[5]:null,c=arguments.length>6&&arguments[6]!==undefined?arguments[6]:b;return new f["default"](function(l,f){function d(){if(u&&u.recordCostTime(),!b.onerror)return u&&u.recordTimeout(),f("TIMEOUT");w&&clearTimeout(w),b.onreadystatechange=b.onerror=b.onload=null;var r=m["default"].getCookie();if(g){var i=b.contentDocument.body;if(1!=i.childNodes.length)return n&&n({url:t,method:e,errorcode:"error"}),u&&u.recordBusinessStatusText("get json error"),void f("ERROR");var a=i.childNodes[0].innerHTML;try{r=o(JSON.parse(a))}catch(s){r={}}}if(u&&r&&u.recordBusinessStatus(r.blogresult),!r.blogresult&&"0"!=r.blogresult)return n&&n({url:t,method:e,errorcode:"error"}),f("ERROR");setTimeout(function(){b=null,p=null},500),m["default"].delCookie("blogresult",h["default"].DOMAIN),m["default"].delCookie("error_retry",h["default"].DOMAIN),l(r)}var p=i(e,c),_="f"+s.randomstring();p.target=_;var g="json"==r.format;if("GET"==e)t+=s.url_param(r);else for(var v in r){var y=document.createElement("textarea");y.name=v,y.value=r[v],p.appendChild(y)}document.body.appendChild(p);var b=s._create_iframe(_);p.appendChild(b);var w=void 0;if(a>0&&(w=setTimeout(function(){return n&&n({url:t,method:e,errorcode:"timeout"}),u&&(u.recordCostTime(),u.recordTimeout()),f("TIMEOUT")},a)),b.onerror=b.onload=d,b.onreadystatechange=function(e){"complete"==b.readyState&&d()},u&&(u.recordUrl(t),u.recordRequestTime()),"GET"===e)b.src=t;else{try{p.action=t}catch(E){p.setAttribute("action",t)}p.submit()}})},e.prototype.url_param=function(e){var t=[];for(var r in e)t.push(r+"="+e[r]);return t.join("&")},e.prototype._create_iframe=function(e){var t=document.createElement("iframe");return t.name=e,t.id=e,t.style.display="none",t},e.prototype.randomstring=function(){return Math.random().toString(36).replace(/[^a-z0-9]+/g,"")},e.prototype._sleep=function(){function e(e){return t.apply(this,arguments)}var t=(0,c["default"])(s["default"].mark(function r(e){return s["default"].wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new f["default"](function(t){window.setTimeout(function(){t()},e)}));case 1:case"end":return t.stop()}},r,this)}));return e}(),e}(),E=new w;window.request=E,t["default"]=E,e.exports=E},,,function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=v.getErrorDescription("network_timeout_retry"),r=v.getErrorDescription("password_wrong"),n=v.getErrorDescription("verifycode_wrong"),i=v.getErrorDescription("internal_server_errpr_retry"),o=v.getErrorDescription("risk_account_block_may")+"<a href='https://aq."+y+"/unblock_index.html' target='_blank'>"+v.getErrorDescription("self_service_unblock")+"</a>",a=v.getErrorDescription("account_block_contact_service")+"<a href='http://ss.aq."+y+"/' target='_blank'>"+v.getErrorDescription("appeal")+"</a>",s=v.getErrorDescription("login_page_invalid"),u=v.getErrorDescription("security_need_verification_code"),c=v.getErrorDescription("login_env_error_retry"),l=v.getErrorDescription("app_n_id_not_match"),f={"-1":t,0:"登录成功",2:r,3:r,4:r,1004:r,9:n,11:l,8:i,16:i,7:o,38:a,12:s,13:s,14:s,15:s,6:u,10:u,22:c,27:n,33:n},d=f[e];return d=d||t,d=d+"["+e+"]"}function o(e,t){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"login",n=xll.PARAMS.CAN_GSLB&&parent.gslb?parent.gslb.gslbUse(r,b,t):b;return n=g["default"].map(n,function(t){return"https://"+t+"/xluser.core.login/v3/"+e})}t.__esModule=!0,t["default"]=undefined;var a=r(0),s=n(a);t.getMessage=i,t.getGslb=o;var u=r(159),c=n(u),l=r(28),f=n(l),d=r(73),p=n(d),_=(r(191),r(152)),h=r(24),g=n(h),m=r(51),v=(r(148),window.parent.xlQuickLogin.uiText),y=(0,m.getDomain)(),b=xll.PARAMS.SERVER_LOGIN,w=(window.parent.xlQuickLogin.uiErrorCodeTextConfig,function(){function e(){var t=this;(0,s["default"])(this,e),this.inited=!1,this.is_requesting=!1,this.isNeedVerifyCode=!1,this._fresh_captch=function(){var e=t.doms;e.input_captcha.value="";var r={img:e.captcha_img};t.verify_type!=undefined&&""!=t.verify_type&&(r.t=t.verify_type);var n=t.captchaUrl?t.captchaUrl:"captcha";return(0,_.register)(n,r,function(e,r){0!==e&&t.showError(r)}),!1},this.switchLoadStatus=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0,r="register"==t.form_type?p["default"].REGISTER_WITH_LOGIN?"other_config_register_with_login_button_text_status_":"other_config_register_button_text_status_":"other_config_login_button_text_status_",n=v.getText(r+e);n?f["default"].text(t.doms.button_submit,n):(r="register"==t.form_type?"REGISTER_BUTTON_TEXT":"LOGIN_BUTTON_TEXT",f["default"].text(t.doms.button_submit,p["default"][r][e])),t.is_requesting=!!e}}return e.prototype.init=function(){this.inited||(this.inited=!0,this.UI=c["default"].getUI(),this.dom&&this.dom(),this.thirdLoginLocationBackError(),this.main())},e.prototype.thirdLoginLocationBackError=function(){if("thirdLogin"!=this.form_type){var e=parseInt(f["default"].getCookie("blogresult"));if(e){var t="";switch(e){case 7:t=v.getErrorDescription("account_frozen_need_unlock");break;case 8:t=v.getErrorDescription("system_maintenance_retry");break;case 28:t=v.getErrorDescription("login_too_frequent_retry");break;case 38:t=v.getErrorDescription("account_block_contact_service");break;default:t=v.getErrorDescription("security_risk_retry")}7!=e&&8!=e&&28!=e&&38!=e||("function"==typeof xll.ispopup&&xll.ispopup()&&"function"==typeof xll.popup&&xll.popup(),this.showError(t+"["+e+"]"),f["default"].delCookie("blogresult",y))}}},e.prototype.shield=function(e,t){var r=t.verify_type,n=t.blogresult;return!g["default"].isEmpty(r)&&6==n&&(this.verify_type=r,this.shield_type=e,this.show_captch(),this.showError(i(n)),!0)},e.prototype.showError=function(e,t){var r=this.doms;this.UI.showError(e,r.warn,t)},e.prototype.hideError=function(){var e=this.doms;this.UI.hideError(e.warn)},e.prototype.validate=function(e){var t=this;return g["default"].every(e,function(e){var r=e[0],n=e[1],i=e[2],o=e[3],a=t.doms[i];return!r||(t.UI.showError(n,t.doms.warn,a),"function"==typeof o&&o.call(t),!1)})},e}());t["default"]=w},,,function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0;var i=r(0),o=n(i),a=r(73),s=n(a),u=r(28),c=n(u),l=window.parent.xlQuickLogin.uiText,f=!1,d=function(){function e(){(0,o["default"])(this,e),this.UIS={}}return e.prototype.getUI=function(){return this.UI},e.prototype.registerUI=function(e,t){this.UIS[e]=t},e.prototype.getPath=function(){var e=s["default"].UI_THEME,t=1==s["default"].ALL_HTTPS?"https":"http",r=xll.PARAMS.STATIC_DOMAIN,n=t+"://"+r+"/login/",i=n+"theme/"+e+"/css/style.css?v=202009171406",o="static/"+xll.version+"/theme/"+e+".js?v=202009171406",a=n+o;return i="./theme/"+e+"/css/style.css",a="./static/"+xll.version+"/theme/xlx.js",{cssPath:i,jsPath:a}},e.prototype.load=function(e){var t=this;if(!f){var r=this.UIS;this.container;f=!0;var n=s["default"].UI_THEME;this.UI=r[n];var i=function(){var i=t.getPath(),o=i.jsPath,a="string"==typeof t.UI?t.UI:o;t.UI&&"string"!=typeof t.UI||window.loadScript(a,function(){if(f=!1,t.UI=r[n],!t.UI)throw new Error("load style error");var i=t.UI;t.container=i.getContainer&&i.getContainer(),!1===i.supportRegister&&(s["default"].REGISTER_TYPES=""),i.init&&t.UI.init(s["default"].LOGIN_TYPES,s["default"].REGISTER_TYPES,s["default"].DEFUALT_UI);var o=l.getText("other_config_login_button_text_status_0"),a=l.getText(s["default"].REGISTER_WITH_LOGIN?"other_config_register_with_login_button_text_status_0":"other_config_register_button_text_status_0"),u={login_button_text:o,register_button_text:a};i.setText&&i.setText(u),s["default"].AUTO_LOGIN_EXPIRE_TIME||i.hideRememberCheckbox(),e()})};if(s["default"].UI_STYLE)if("string"==typeof s["default"].UI_STYLE){if(!c["default"].checkCss(s["default"].UI_STYLE))throw new Error("传入的css有误");c["default"].loadStyle(s["default"].UI_STYLE,i)}else{var o=this.getPath(),a=o.cssPath;c["default"].loadStyle(a,i)}else i()}},e}(),p=new d;t["default"]=p},function(e,t,r){var n=function(){return this}()||Function("return this")(),i=n.regeneratorRuntime&&Object.getOwnPropertyNames(n).indexOf("regeneratorRuntime")>=0,o=i&&n.regeneratorRuntime;if(n.regeneratorRuntime=undefined,e.exports=r(161),i)n.regeneratorRuntime=o;else try{delete n.regeneratorRuntime}catch(a){n.regeneratorRuntime=undefined}},function(e,t){!function(t){"use strict";function r(e,t,r,n){var o=t&&t.prototype instanceof i?t:i,a=Object.create(o.prototype),s=new p(n||[]);return a._invoke=c(e,r,s),a}function n(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}function i(){}function o(){}function a(){}function s(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function u(e){function t(r,i,o,a){var s=n(e[r],e,i);if("throw"!==s.type){var u=s.arg,c=u.value;return c&&"object"==typeof c&&v.call(c,"__await")?Promise.resolve(c.__await).then(function(e){t("next",e,o,a)},function(e){t("throw",e,o,a)}):Promise.resolve(c).then(function(e){u.value=e,o(u)},a)}a(s.arg)}function r(e,r){function n(){return new Promise(function(n,i){t(e,r,n,i)})}return i=i?i.then(n,n):n()}var i;this._invoke=r}function c(e,t,r){var i=T;return function(o,a){if(i===x)throw new Error("Generator is already running");if(i===C){if("throw"===o)throw a;return h()}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var u=l(s,r);if(u){if(u===L)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===T)throw i=C,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=x;var c=n(e,t,r);if("normal"===c.type){if(i=r.done?C:k,c.arg===L)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=C,r.method="throw",r.arg=c.arg)}}}function l(e,t){var r=e.iterator[t.method];if(r===g){if(t.delegate=null,"throw"===t.method){if(e.iterator["return"]&&(t.method="return",t.arg=g,l(e,t),"throw"===t.method))return L;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return L}var i=n(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,L;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=g),t.delegate=null,L):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,L)}function f(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function d(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function p(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(f,this),this.reset(!0)}function _(e){if(e){var t=e[b];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function i(){for(;++r<e.length;)if(v.call(e,r))return i.value=e[r],i.done=!1,i;return i.value=g,i.done=!0,i};return n.next=n}}return{next:h}}function h(){return{value:g,done:!0}}var g,m=Object.prototype,v=m.hasOwnProperty,y="function"==typeof Symbol?Symbol:{},b=y.iterator||"@@iterator",w=y.asyncIterator||"@@asyncIterator",E=y.toStringTag||"@@toStringTag",S="object"==typeof e,I=t.regeneratorRuntime;if(I)return void(S&&(e.exports=I));I=t.regeneratorRuntime=S?e.exports:{},I.wrap=r;var T="suspendedStart",k="suspendedYield",x="executing",C="completed",L={},O={};O[b]=function(){return this};var A=Object.getPrototypeOf,P=A&&A(A(_([])));P&&P!==m&&v.call(P,b)&&(O=P);var N=a.prototype=i.prototype=Object.create(O);o.prototype=N.constructor=a,a.constructor=o,a[E]=o.displayName="GeneratorFunction",I.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},I.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,a):(e.__proto__=a,E in e||(e[E]="GeneratorFunction")),e.prototype=Object.create(N),e},I.awrap=function(e){return{__await:e}},s(u.prototype),u.prototype[w]=function(){return this},I.AsyncIterator=u,I.async=function(e,t,n,i){var o=new u(r(e,t,n,i));return I.isGeneratorFunction(t)?o:o.next().then(function(e){return e.done?e.value:o.next()})},s(N),N[E]="Generator",N[b]=function(){return this},N.toString=function(){return"[object Generator]"},I.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},I.values=_,p.prototype={constructor:p,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=g,this.done=!1,this.delegate=null,this.method="next",this.arg=g,this.tryEntries.forEach(d),!e)for(var t in this)"t"===t.charAt(0)&&v.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=g)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){function t(t,n){return o.type="throw",o.arg=e,r.next=t,n&&(r.method="next",r.arg=g),!!n}if(this.done)throw e;for(var r=this,n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],o=i.completion;if("root"===i.tryLoc)return t("end");if(i.tryLoc<=this.prev){var a=v.call(i,"catchLoc"),s=v.call(i,"finallyLoc");if(a&&s){if(this.prev<i.catchLoc)return t(i.catchLoc,!0);if(this.prev<i.finallyLoc)return t(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return t(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return t(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&v.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,L):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),L},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),d(r),L}},"catch":function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;d(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:_(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=g),L}}}(function(){return this}()||Function("return this")())},,function(e,t,r){"use strict";function n(){return g===undefined&&(g=/\bedge\b/.test(_)),g}function i(){return m===undefined&&(m=/[ ]thunder\/10.0([\d.]*)/.test(_)||/[ ]thunder( )?\/( )?9.([\d.]*)/.test(_)),m}function o(){return v===undefined&&(v=/\bthunder\/10.[1-9][\d.]*/.test(_)),v}function a(){return y===undefined&&(y=/\bmac\b/.test(_)&&!!h.WebViewJavascriptBridge),y}function s(){return b===undefined&&(b=/\belectron\/\d+(\.\d+){2}\b/.test(_)),b}function u(){return w===undefined&&(w=/\btbc\/\d+(\.\d+){3}\b/.test(_)&&!!h["native"]),w}function c(){return E===undefined&&(E=!l()&&("undefined"!=typeof h.XLJSWebViewBridgeExport||"undefined"!=typeof h.XLJSWebViewBridge)),E}function l(){return S===undefined&&(S="undefined"!=typeof h.webkit&&(h.webkit.messageHandlers,!0)&&"undefined"!=typeof h.webkit.messageHandlers.XLJSWebViewBridgeExport||"undefined"!=typeof h.webkit&&(h.webkit.messageHandlers,!0)&&"undefined"!=typeof h.webkit.messageHandlers.XLJSWebViewBridge),S}function f(){return I===undefined&&(I="undefined"!=typeof h.XLAccountJsBridge),I}function d(){return o()||!i()&&u()}function p(){return T===undefined&&(T="file:"===location.protocol),T}Object.defineProperty(t,"__esModule",{value:!0});var _=navigator.userAgent.toLocaleLowerCase(),h=window,g=undefined,m=undefined,v=undefined,y=undefined,b=undefined,w=undefined,E=undefined,S=undefined,I=undefined,T=undefined;t.checkIsEdge=n,t.checkIsXl9=i,t.checkIsXlx=o,t.checkIsXlMac=a,t.checkIsXdas=s,t.checkIsTBC=u,t.checkIsOldAndroidMobileClient=c,t.checkIsOldIOSMobileClient=l,t.checkSupportAccountMobileSDK=f,t.checkIsNewTBC=d,t.checkIsLocal=p},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(){l["default"].forEach("VERIFY_KEY,blogresult,active,isspwd,score,downbyte,isvip,jumpkey,logintype,nickname,onlinetime,order,safe,downfile,sessionid,sex,upgrade,userid,usernewno,usernick,usertype,usrname,loginkey,xl_autologin".split(","),function(e){u["default"].delCookie(e,a["default"].DOMAIN)}),u["default"].delCookie(a["default"].LOGIN_TYPE_COOKIE_NAME,a["default"].DOMAIN)}t.__esModule=!0,t.deleteCookieUserInfo=i;var o=r(73),a=n(o),s=r(28),u=n(s),c=r(24),l=n(c)},function(e,t,r){"use strict";function n(e){var t={};for(var r in e){t[e[r]]=r}return t}function i(e,t){var r="",n=c[e];return n&&(r=n[t]||""),r}t.__esModule=!0,t.getUITypeNameById=i;var o={accountLogin:"1",mobileLogin:"2",qrLogin:"4"},a={mailRegister:"1",mobileRegister:"2",accountRegister:"3",appRegister:"4",mobileRegisterWithPassword:"5"},s=n(o),u=n(a),c={login:s,register:u}},,,,,,,,function(e,t,r){"use strict";t.__esModule=!0,t.monitorStatServerLoader=t.MonitorStatServerLoader=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=r(140),a=r(141),s=r(174),u=t.MonitorStatServerLoader=function(){function e(){(0,i["default"])(this,e),this._monitorStatServer=null}return e.prototype.setPublicData=function(){this.get().setPublicData((0,a.getPublicData)())},e.prototype.get=function(){if(null===this._monitorStatServer){var e=o.gbStatAttrNames.monitor;if(!o.gbStat.hasAttr(e)){var t=new s.MonitorStatServer((0,a.getStatServerUrl)());o.gbStat.setAttr(e,t)}this._monitorStatServer=o.gbStat.getAttr(e)}return this._monitorStatServer},e}();t.monitorStatServerLoader=new u},function(e,t,r){"use strict";t.__esModule=!0,t.MonitorStatServer=t.businessFlowKeys=t.monitorStatActions=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=r(142),a=t.monitorStatActions={request:"request",pagePerformance:"pagePerformance",businessFlow:"businessFlow",initEnv:"initEnv"};t.businessFlowKeys={loadLoginPluginFile:"loadLoginPluginFile",loadLoginPlugin:"loadLoginPlugin",openLoginWnd:"openLoginWnd",initLoginWnd:"initLoginWnd",closeLoginWnd:"closeLoginWnd"},t.MonitorStatServer=function(){function e(t){(0,i["default"])(this,e),this._statServer=new o.StatServer(t,"websdk-monitor2")}return e.prototype.setPublicData=function(e){return this._statServer.setPublicData(e)},e.prototype.stat=function(e){return this._statServer.stat(e)},e.prototype.statRequest=function(e){var t={type:a.request,extData1:e};return this.stat(t)},e.prototype.statPagePerformance=function(e){var t={type:a.pagePerformance,extData2:e};return this.stat(t)},e.prototype.statBusinessFlow=function(e){var t={type:a.businessFlow,extData3:e};return this.stat(t)},e.prototype.statInitEnv=function(e){var t={type:a.initEnv,extData4:e};return this.stat(t)},e}()},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=e.code,r=e.hideError,n=e.mobile,i=e.showError,s=e.register,u=e.captcha,l=e.gsm;if(!a(t)){r();var f=v["default"].trim(n.value);if(!f)return void i(M.getErrorDescription("phone_cannot_be_empty"));if(!(l?v["default"].isMainlandPhone(l)?v["default"].checkMobile(f):v["default"].checkOverseasMobile(l+f):v["default"].checkMobile(f)))return i(M.getErrorDescription("phone_format_wrong")),!1;var d=f;l&&(d=v["default"].isMainlandPhone(l)?f:l+f);var p=(0,c["default"])({mobile:d},u);return 1==s&&(p.register=1),p.creditkey=store.get("creditkey"),A["default"].vertify_version()&&(p=A["default"].forceBaseParams2(p)),o(p)}}function o(e){return E["default"].post((0,x.getGslb)("sendsms",{username:e.mobile}),(0,S.baseParams2)(e),P.report_helper)}function a(e){return!e||"verify_btn verify_grey_btn"===e.className}function s(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:59;if(e){var r=v["default"].text(e);v["default"].text(e,M.getText("sended")+" "+t+"s"),e.className="verify_btn verify_grey_btn";var n=setInterval(function(){if(0===--t)return v["default"].text(e,r),e.className="verify_btn",void clearInterval(n);v["default"].text(e,M.getText("sended")+" "+t+"s")},1e3)}}t.__esModule=!0,t.MobileForm=undefined;var u=r(52),c=n(u),l=r(0),f=n(l),d=r(145),p=n(d),_=r(146),h=n(_);t.getCode=i,t.sendsms=o,t.isSmsButtonGrey=a,t.setSmsButtonGrey=s;var g=r(159),m=(n(g),r(28)),v=n(m),y=r(73),b=n(y),w=r(153),E=n(w),S=r(49),I=r(24),T=n(I),k=r(46),x=r(156),C=n(x),L=r(148),O=r(176),A=n(O),P=r(147),N=r(74),D=r(98),R=D.userBehaviorsStatServerLoader.get(),M=window.parent.xlQuickLogin.uiText,U=!1;t.MobileForm=function(e){function t(){var r,n,o;(0,f["default"])(this,t);for(var a=arguments.length,u=Array(a),l=0;l<a;l++)u[l]=arguments[l];return r=n=(0,p["default"])(this,e.call.apply(e,[this].concat(u))),n.token="",n.notSendsms=!1,n.captcha_submit=function(){"smslogin"==n.shield_type?n.submit():"sendsms"==n.shield_type&&n.smscode()},n.setNotSendsms=function(){var e=n;e.notSendsms=!0,setTimeout(function(){e.notSendsms=!1},59e3)},n._submit=function(e){var t=e.register,r=e.registerType,i=e.beginTime,o=e.submitType,a=n,s=a.doms;if(!U&&(n._isSubmit(),!n.is_requesting)){n.hideError();var u=n._gsm(),l=n._mobile(),f=l;u&&(f=n._isMainlandPhone()?l:u+l);var d="",p=n._smsCode(),_=!(!s.check_remember||!s.check_remember.checked);if(n._checkMobile()){if(""==n.token)return void n.showError(M.getErrorDescription("need_get_smscode"));var h=n.validateCaptcha(),g=h[0];if(h[1]){var m=(0,c["default"])({smsCode:p,token:n.token,mobile:f},g);if(1==t&&(!1===b["default"].REGISTER_WITH_LOGIN&&(m.register=1),!s.agree.checked))return void n.showError(M.getErrorDescription("read_n_tick_user_agreement"));if(R.stat({action:N.userBehaviorsStatActions.registPanelRegistClick,extData:{clickType:"number"==typeof o?o:undefined}}),!p)return void n.showError(M.getErrorDescription("smscode_cannot_be_empty"));if(!/^\d{6}$/.exec(p))return s.input_code.value="",void n.showError(M.getErrorDescription("smscode_wrong"));if(n._isRegisterWithPassword({registerValue:t,registerTypeValue:r})){if(d=n._password(),!n._checkPassword())return;m.password=d}m.creditkey=store.get("creditkey"),A["default"].vertify_version()&&(m=A["default"].forceBaseParams2(m));var v={type:0,mode:"login"==n.form_type?"phone":"register",beginTime:i};(0,L.loginFetch)(4),E["default"].post((0,x.getGslb)("smslogin",{username:f}),(0,S.baseParams2)(m),P.report_helper).then(function(e){if(!n.shield("smslogin",e)){n.switchLoadStatus();var t=e.blogresult;if(0!=t&&(v.errorCode=(0,k.forceToString)(t,"")),0==t)_?(0,L.setAutoLogin)():(0,L.delAutoLogin)(),(0,L.loginSuccess)(L.LOGIN_TYPE.MOBILE,e,v);else{if(1007==t)return(0,L.loginFail)(t,4,v),void n._reviewPanel();(0,L.loginFail)(t,4,v),n.handlerErrorCode(parseInt(t))}}})["catch"](function(e){(0,L.loginFail)(-1,4,v)})}}}},n._get_code=function(e){var t=e.register,r=t===undefined?0:t,o=e.submitType,a=n,u=(a.UI,a.doms),c=a.notSendsms;if(!U){n._isSubmit();var l=n.validateCaptcha(),f=l[0];if(l[1]&&!c){var d="",p=undefined;if("login"===n.form_type?(d=N.userBehaviorsStatActions.loginPanelPhoneCode,p={clickType:"number"==typeof o?o:undefined}):d=N.userBehaviorsStatActions.registPanelPhoneCode,R.stat({action:d,extData:p}),n._checkMobile()){var _=n._gsm(),h={code:u.button_getcode,mobile:u.input_mobile,hideError:function(){return n.hideError()},showError:function(e){return n.showError(e)},register:r,captcha:f,gsm:_};1==h.register&&!0===b["default"].REGISTER_WITH_LOGIN&&(h.register=0),i(h).then(function(e){if(!n.shield("sendsms",e)){var t=e.blogresult;if(0==t)return n.controlDomDisplay(1),n.token=e.token,s(u.button_getcode),void n.setNotSendsms();if(27==t)n.doms.input_captcha.value="",n._fresh_captch();else if(1007==t)return void n._reviewPanel();var r=M.getServerErrorDescption();n.showError(r)}})["catch"](function(e){n.showError((0,x.getMessage)(-1))})}}}},n.show_captch=function(){var e=n;e.doms,e.UI;if(n.hideError(),n._checkMobile())return n.controlDomDisplay(),n._fresh_captch(),!1},o=r,(0,p["default"])(n,o)}return(0,h["default"])(t,e),t.prototype.bind=function(){var e=this,t=this.doms;v["default"].bind(t.button_getcode,"click.x",function(){return e.smscode({submitType:1})}),v["default"].bind(t.captcha_confirm,"click.x",this.captcha_submit),v["default"].bind(t.button_submit,"click.x",function(){return e.submit({submitType:1})}),T["default"].forEach(t.captcha_fresh,function(t){return v["default"].bind(t,"click.x",e._fresh_captch)}),v["default"].bind(t.input_mobile,"change.x",function(e){var t=e.target.value;xll.PARAMS.CAN_GSLB&&parent.gslb&&parent.gslb.uname(t),xll.PARAMS.CAN_GSLB&&parent.gslb&&parent.gslb.fetch({host:[xll.PARAMS.SERVER_LOGIN[0]],username:t})})},t.prototype._isSubmit=function(){U=!0,setTimeout(function(){return U=!1},2e3)},t.prototype.handlerErrorCode=function(e){var t=M.getServerErrorDescption();this.showError(""+t)},t.prototype._mobileInit=function(){this._initGsm()},t.prototype._initGsm=function(){window.parent.xlQuickLogin.PARAMS.SHOW_GSM?this._loadGSMConfig():this._hideGsm()},t.prototype._hideGsm=function(){var e=this.doms,t=e.gsm;if(t){t.style.display="none";var r=e.input_mobile;r.style.marginLeft="0",r.style.width="100%"}},t.prototype.validateCaptcha=function(){var e=this.doms,t={};if((0,m._visible)(e.captcha_container[0])){var r=v["default"].trim(e.input_captcha.value);if(!r)return this.showError(M.getErrorDescription("smscode_cannot_be_empty")),[t,!1];t.verifyCode=r,t.verifyKey=v["default"].getCookie("VERIFY_KEY")}return[t,!0]},t.prototype.controlDomDisplay=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0,t="login"==this.form_type?"pl_hide_for_sms_captcha":"pr_hide_for_sms_captcha",r=0==e?"":"none",n=1==e?"":"none";(0,m._display)(t,n),(0,m._display)(this.doms.captcha_container,r)},t.prototype.captcha_hook_enter_submit=function(e){var t=this;v["default"].bind(e,"keydown.x",function(e){if("none"!=t.doms.captcha_container[0].style.display){var r;return window.event?r=e.keyCode:e.which&&(r=e.which),13==r?(t.smscode(),!1):void 0}})},t.prototype._checkMobile=function(){var e=this._mobile();if(!e)return this.showError(M.getErrorDescription("phone_cannot_be_empty")),!1;var t=this._gsm();return!!(this._isMainlandPhone()?v["default"].checkMobile(e):v["default"].checkOverseasMobile(t+e))||(this.showError(M.getErrorDescription("phone_format_wrong")),!1)},t.prototype._checkPassword=function(){var e=!1;do{var t=this._password();if(!t){this.showError(M.getErrorDescription("password_cannot_be_empty"));break}if(t.length<6){this.showError(M.getErrorDescription("password_lessthan_6_digits"));break}if(t.length>16){this.showError(M.getErrorDescription("password_largethan_16_digits"));break}if(!v["default"].checkPassword(t)){this.showError(M.getErrorDescription("standard_phone_format"));break}e=!0}while(!1);return e},t.prototype._mobile=function(){var e=this.doms;return v["default"].trim(e.input_mobile.value)},t.prototype._password=function(){var e=this.doms;return v["default"].trim(e.input_enter_password.value)},t.prototype._smsCode=function(){var e=this.doms;return v["default"].trim(e.input_code.value)},t.prototype._gsm=function(){var e=void 0,t=this.doms,r=t.gsm;if(r&&"none"!==r.style.display){var n=t.gsm.value?t.gsm.value:t.gsm.placeholder,i=n.indexOf("+");-1!==i&&(n=n.substring(i+1)),n="00"+n,e=v["default"].trim(n)}return e},t.prototype._isMainlandPhone=function(){var e=this._gsm();return v["default"].isMainlandPhone(e)},t.prototype._isRegisterWithPassword=function(e){var t=e.registerValue,r=e.registerTypeValue;return 1===t&&1===r},t.prototype._reviewPanel=function(){var e=v["default"].getCookie("reviewurl")||store.get("reviewurl");if(e&&(A["default"].show_captch_panel(e),"103"==b["default"].LOGIN_ID))try{parent.niuCaptchaNotify(1)}catch(t){}},t.prototype._loadGSMConfig=function(){do{var e=M.getGsmText();if(!e)break;var t=this.doms,r=t.gsm;if(!r)break;for(var n in e){for(var i=""+n,o=""+e[n],a=7-i.length-o.length,s="  "+i,u=0;u<a;++u)s+=" ";s+=o;var c=new Option(s,e[n]);"+86"===c.value&&(c.selected=!0),r.add(c)}}while(!1)},t}(C["default"])},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0;var i=r(52),o=n(i),a=r(0),s=n(a),u=r(28),c=n(u),l=r(73),f=n(l),d=r(49),p=r(62),_=window.parent.xlQuickLogin.uiText,h=function(){function e(){var t=this;(0,s["default"])(this,e),this.need_review_panel=!1,this.fromLoginTryID=!1,this.vertify_panel_cb=function(e){var r=t;200==e.code&&(setTimeout(function(){(0,p.checkIsEdge)()&&c["default"].setCookie("creditkey",e.creditkey,864e5,document.domain),(0,u._display)(c["default"].id("show_panel_wrap"),"none"),!!r.fromLoginTryID&&(0,u._click)(c["default"].id(r.fromLoginTryID))},1e3),c["default"].setCookie("creditkey",e.creditkey,864e5,document.domain),store.set("creditkey",e.creditkey))},this.vertify_version=function(){var e=!1,t=f["default"].UI_THEME,r=(0,d.baseParams2)();return 1!=r.platformVersion||"embed"!=t&&"popup"!=t&&"default"!=t||(e=!0),0!=r.platformVersion&&12!=r.platformVersion||(e=!0),e}}return e.prototype.show_captch_panel=function(e){function t(){window.XlCaptcha.init(a)}var r=arguments.length>1&&arguments[1]!==undefined&&arguments[1];this.need_review_panel=!0,this.fromLoginTryID=r;var n=e;n=n+"&deviceid="+store.get("deviceid"),n=n+"&panelType=xlx&creditkey="+store.get("creditkey");var i=_.hl;i&&""!=i&&(n=n+"&hl="+i),c["default"].id("iframe_panel").setAttribute("src",n),(0,u._display)(c["default"].id("show_panel_wrap"),"block");var o=f["default"].UI_THEME,a={VERTIFY_SUCC_FUNC:this.vertify_panel_cb,IFRAME_BOX_ID:"show_panel_wrap",PANEL_THEME:o};c["default"].id("iframe_panel").onload=t,c["default"].id("iframe_panel").onreadystatechange=function(){"complete"==c["default"].id("iframe_panel").readyState&&t()}},e.prototype.forceBaseParams2=function(e){return(0,o["default"])({},e,{protocolVersion:"301"})},e}();t["default"]=new h},,function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(){return I}function o(e){var t={},r=JSON.parse((0,c["default"])(e)),n=i();for(var o in r){var a=n[o];if(E["default"].isArray(a))for(var s=a,u=Array.isArray(s),l=0,s=u?s:(0,m["default"])(s);;){var f;if(u){if(l>=s.length)break;f=s[l++]}else{if(l=s.next(),l.done)break;f=l.value}var d=f;t[d]=r[o]}else t[a]=r[o]}return t}function a(e){}function s(e,t){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"login",n=xll.PARAMS.SERVER_LOGIN,i=xll.PARAMS.CAN_GSLB&&parent.gslb?parent.gslb.gslbUse(r,n,t):n;return i=E["default"].map(i,function(t){return"https://"+t+"/xluser.core.login/v3/"+e})}t.__esModule=!0,t.BusinessRequest=undefined;var u=r(12),c=n(u),l=r(40),f=n(l),d=r(52),p=n(d),_=r(0),h=n(_),g=r(187),m=n(g);t.json2CookieLoginData=o,t.cookie2JsonLoginData=a,t.getGslb=s;var v=r(104),y=n(v),b=r(139),w=r(24),E=n(w),S=r(49),I={account:"account",error:"error",errorCode:["errcode","blogresult"],error_description:"error_description",isCompressed:"iscompressed",isSetPassWord:"issetpassword",keepAliveMinPeriod:"keepaliveminperiod",keepAlivePeriod:"keepaliveperiod",loginKey:"loginkey",nickName:"usernick",platformVersion:"platformversion",protocolVersion:"protocolversion",secureKey:"securekey",sequenceNo:"sequenceno",sessionID:"sessionid",timestamp:"timestamp",userID:"userid",userName:"usrname",userNewNo:"usernewno",version:"version",vipList:"vipinfo",birthday:"birthday",city:"city",country:"country",imgURL:"imgurl",isSpecialNum:"isspecialnum",isSubAccount:"issubaccount",mobile:"mobile",order:"order",personalSign:"personalsign",province:"province",rank:"rank",registerDate:"registerdate",role:"role",sex:"sex",todayScore:"todayscore"};t.BusinessRequest=function(){function e(){(0,h["default"])(this,e),this._requestServer=new b.RequestServer(y["default"])}return e.prototype._formatRequestParams=function(e,t,r,n,i){var o={};return"POST"===e&&r&&(o.headers=(0,p["default"])({"Content-Type":"application/json"},r)),o.options=(0,p["default"])({stringifyJsonDatas:!0,parseJsonResult:!0},i),o},e.prototype.post=function(e,t,r,n){var i=this;if(E["default"].isArray(e))return new f["default"](function(o,a){return 0==e.length?void a("TIMEOUT"):i.post(e.shift(),t,r,n).then(o)["catch"](function(s){i.post(e,t,r,n).then(o)["catch"](function(e){return a(e)})})});var o=this._formatRequestParams("POST",e,t,r,n);return this._requestServer.post(e,o.headers,r,o.options)},e.prototype.getQrCode=function(e){var t=(0,p["default"])({},(0,S.baseParams2)({format:"json"}),e);return this.post(s("qrcode"),{},t)},e.prototype.getUserInfo=function(e){var t=(0,p["default"])({vasid:"2,14,33,34,35"},(0,S.baseParams2)({format:"json"}),e);return this.post(s("getuserinfo"),{},t)},e.prototype.sessionId2Token=function(e,t){var r={"Content-Type":"application/json; charset=UTF-8"},n={signin_token:t,provider:"access_endpoint"};return this.post(e+"/v1/auth/signin/token",r,n)},e}()},function(e,t,r){e.exports={"default":r(180),__esModule:!0}},function(e,t,r){r(181),e.exports=r(2).Object.setPrototypeOf},function(e,t,r){var n=r(7);n(n.S,"Object",{setPrototypeOf:r(182).set})},function(e,t,r){var n=r(6),i=r(5),o=function(e,t){if(i(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{n=r(19)(Function.call,r(94).f(Object.prototype,"__proto__").set,2),n(e,[]),t=!(e instanceof Array)}catch(i){t=!0}return function(e,r){return o(e,r),t?e.__proto__=r:n(e,r),e}}({},!1):undefined),check:o}},,,,,function(e,t,r){e.exports={"default":r(188),__esModule:!0}},function(e,t,r){r(54),r(50),e.exports=r(189)},function(e,t,r){var n=r(5),i=r(83);e.exports=r(2).getIterator=function(e){var t=i(e);if("function"!=typeof t)throw TypeError(e+" is not iterable!");return n(t.call(e))}},function(e,t,r){"use strict";function n(e){return e.toLowerCase().replace(/[_\-]/g,"")}function i(e,t){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{not_allowd:{DEBUG:1,VERSION:1,ALLOW_ACCOUNT_REGISTER_IDS:1,IFRAME_ID:1},except:",UI_STYLE,LOGIN_ID,IFRAME_STYLE"};if("object"!==(void 0===t?"undefined":(0,a["default"])(t)))throw new Error("参数类型错误");var i=r.not_allowd,o=r.except,u=s(e),c=s(t),l={};for(var f in e)if(u(f)&&!(f in i)){var d=n(f),p=e[f];l[d]={origin_key:f,value:p,type:void 0===p?"undefined":(0,a["default"])(p)}}for(var _ in t)if(c(_)){var h=n(_);if(h in l){var g=l[h].type,m=t[_],v=l[h].origin_key;if("boolean"===g&&-1===o.indexOf(v)&&(m=!!m),g!==(void 0===m?"undefined":(0,a["default"])(m))&&-1==o.indexOf(v))throw new Error("config key("+_+") error, type not match");"LOGIN_ID"===v&&(e.SET_LOGIN_ID=!0),"CHANGE_SIZE_FUNC"===v&&(e.SET_CHANGE_SIZE_FUNC=!0),e[v]=m}}if(!e.SET_LOGIN_ID)throw new Error("没有设置LOGIN_ID");return e}t.__esModule=!0;var o=r(15),a=function(e){return e&&e.__esModule?e:{"default":e}}(o);t.translateLower=n,t.initConfig=i;var s=function(e){return function(t){return e.hasOwnProperty(t)}}},function(e,t,r){"use strict";t.__esModule=!0;t.defaultErrorCodeText={success:"操作成功",canceled:"用户已取消操作",unknown:"未知错误",invalid_argument:"参数不正确",deadline_exceeded:"请求超时",not_found:"资源未找到",already_exists:"操作冲突，名称已存在",permission_denied:"您没有操作权限",resource_exhausted:"操作过于频繁，请稍后再试",failed_precondition:"先决条件失败",aborted:"操作冲突",outof_range:"超出选择范围",unimplemented:"接口未实现",internal:"系统内部错误",unavailable:"服务不可用",data_loss:"资源可能已被删除",unauthenticated:"登录超时，请重新登录",invalid_sessionid:"登录信息已过期，请重新登录",password_wrong:"帐号或密码错误，请重新输入",check_captcha:"为了您的帐号安全，请输入图形验证码",userinfo_expired:"登录信息已过期，请重新登录",account_locked:"帐号被临时冻结，请进行自助解封",systemupgrade:"服务升级中，请稍后再试",appid_notmatch:"应用程序名和业务跟踪码不匹配",need_checksms:"为了您的帐号安全，请输入手机验证码",need_bind:"为了您的帐号安全，请绑定手机号码",notsupport:"不支持",verifycode_wrong:"验证码错误，请重新输入",shield_reject:"操作过于频繁，请稍后再试",sendsms:"短信发送失败，请重新获取",smscode_wrong:"验证码错误，请重新输入",user_exist:"该帐号已注册，请更换帐号注册或直接登录",password_weak:"密码过于简单，请重新输入",verifycode_expired:"验证码已失效，请重新获取",cannot_unlock:"帐号处于违规封停期，请改天再试",need_changepwd:"请重新设置您的密码",acclogin_nomobile:"登录不成功，请先到个人中心绑定手机",wxmini_noregister:"用户尚未注册",user_not_exist:"帐号不存在，请先注册",password_invalid:"6-16位，含字母/数字/符号至少2种(除空格)",password_same:"新密码与原密码相同",uid_notboundmobile:"短信验证码校验失败",need_relogin:"请重新登录",review_panel:"拉取验证页面",redirect_url:"跳转指定url",mobile_bound:"该手机已绑定到其他帐号",gateway_uidbound:"该uid已绑定帐号",gateway_accountbound:"帐号已经被绑定",gateway_uidnotbound:"该uid未绑定帐号",get_thirdinfo:"获取三方信息失败",query_failed:"查询失败",verify_code:"验证码错误，请重新输入",user_locked:"帐号被锁定",invalid_token:"token非法",invalid_appname:"程序名非法",invalid_deviceid:"设备无效",invalid_parameter:"参数无效",wrong_captcha:"验证码错误，请重新输入",words_is_sensitive:"敏感词",third_id_was_bound:"三方ID已绑定其它帐号",uid_was_bound:"帐号已绑定其它三方ID",no_bind_info:"未查询到绑定信息",refuse_to_unbind:"自动生成帐号拒绝解绑",invalid_third_type:"不可用的三方类型",invalid_third_app_id:"不可用的三方APPID",mobile_regist_incomplete_unbind:"该手机号处于解绑不彻底状态，不允许注册",mobile_not_registered:"该手机号未注册",mobile_bind_phone_already_bind:"该手机号码已绑定其他帐号",mobile_bind_uid_already_bind:"该uid已经绑定手机",mobile_bind_uid_not_found:"绑定的uid没有找到",mobile_bind_incomplete_unbind:"手机号解绑不彻底",name_not_exist:"name不存在",send_sms_fail:"短信发送失败，请重新获取",update_avatar_fail:"更新头像失败",get_thirdinfo_fail:"获取三方用户信息失败",set_certify_fail:"实名认证失败",user_not_certified:"帐号没有进行实名认证",uid_already_certified:"帐号已实名认证过",characters_verify:"请输入图中字符进行验证",slide_vertify:"请进行滑块验证",sms_vertify:"请进行短信验证",intelligent_testing_vertify:"智能检测中，请稍后…",input_verifycode:"请输入验证码",input_correct_image_verifycode:"请输入正确的图形验证码",change_another_one:"看不清，换一个",confirm:"确定",loading:"加载中",sliding_right_tofille_puzzle:"向右滑动滑块填充拼图",sliding_block_to_correct_position:"校验失败，请拉动滑块到正确位置",system_error_try_again:"系统出错，请重试",get_verifycode:"获取验证码",empty_phone:"请传入手机号",empty_userid:"请传入userID",please_get_verifycode:"请获取验证码",please_input_correct_verifycode:"请输入正确的验证码",api_error:"接口错误",sendsms_vertify:"短信验证",sended:"已发送",workload_vertify:"工作量验证",report_failed:"上报失败",invalid_request:"无效请求",parameters_not_enough:"验证参数不足",need_check_mail:"需要验证邮箱",mail_bind_uid_already_bind:"已经绑定了邮箱",need_check_phone:"需要校验手机",uid_not_bind_mobile:"帐号未绑定手机",uid_not_bind_mail:"帐号未绑定邮箱",check_ukey_error:"ukey 验证不通过,操作流程需要重新开始",ukey_expired:"ukey-过期-操作流程需要重新开始",passwd_error:"密码不正确",bind_phone_failed:"绑定手机失败",bind_phone_cannot_same:"新输入的手机号不能和原绑定的手机号相同",config_error:"配置错误",check_id_failed:"身份证号验证失败",check_name_failed:"真实姓名验证失败",bind_mail_cannot_same:"新输入的手机号不能和原绑定的手机号相同",send_mail_verifycode_failed:"邮箱验证码发送失败",check_mail_verifycode_failed:"邮箱验证码校验失败",bind_mail_failed:"绑定邮箱失败",not_accept_verifycode:"未接受过验证码",bind_id_failed:"绑定身份证失败",id_had_bind_uid:"身份证已经绑定其它帐号",mail_had_bind_uid:"邮箱已经绑定其它帐号",uid_had_bind_mobile:"帐号已经绑定手机",uid_had_bind_mail:"帐号已经绑定邮箱",not_bind_mobile_mail:"未绑定手机也未绑定邮箱",set_pwd_failed:"设置密码失败",pay_pwd_same_login_pwd:"支付密码与登录密码相同",check_pay_pwd_failed:"校验支付密码失败",set_pay_pwd_failed:"设置支付密码失败",modify_pay_pwd_failed:"修改支付密码失败",check_smstoken_failed:"smsToken校验失败",get_userinfo_failed:"获取用户信息失败",get_auto_renewal_failed:"获取用户自动续费信息失败",nickname_illegal:"昵称不合法",appname_invalid:"无效的APPNAME",deviceid_invalid:"无效的DEVICEID",user_had_certified:"身份证已认证",mobile_not_match_id:"您当前绑定的手机号与身份信息不匹配",id_certified_failed:"身份认证失败，请稍后重试",need_get_smscode:"请先获取短信验证码",smscode_cannot_be_empty:"短信验证码不能为空",read_n_tick_user_agreement:"请阅读并勾选用户协议",verification_code_must_fill:"验证码不能为空",phone_cannot_be_empty:"手机号不能为空",phone_format_wrong:"手机格式不正确",standard_phone_format:"6-16位,包含字母、数字、符号中至少2种（空格除外）",account_cannot_be_empty:"帐号不能为空",account_lessthan_6_chars:"帐号不能小于6个字符",account_largethan_16_chars:"帐号不能大于16个字符",account_format_error_n_tips:"帐号格式错误，仅支持字母、数字和下划线的组合",account_format_error_n_need_chars:"帐号格式错误，帐号必须包含字母",account_registered:"该帐号已被注册",password_lessthan_6_digits:"密码长度不能少于6位",password_largethan_16_digits:"密码长度不能大于16位",password_inconsistent_need_reenter:"两次输入密码不一致，请重新输入",image_verification_code_error:"图片验证码错误",internal_server_errpr_retry:"服务器内部错误，请重试",username_cannot_be_empty:"用户名不能为空",password_cannot_be_empty:"密码不能为空",incorrect_verification_code:"验证码错误，请重新输入",account_frozen_need_unlock:"您的帐号被临时冻结，请前往安全中心解封",system_maintenance_retry:"系统维护中，请稍后再试",login_too_frequent_retry:"登录操作过于频繁，请2小时后再试",security_risk_retry:"登录环境存在安全风险，请稍后再试",network_timeout_retry:"网络超时，请重试",login_page_invalid:"登录页面失效",security_need_verification_code:"为了您的帐号安全，请输入验证码",login_env_error_retry:"登录环境异常，请于2小时后重试",app_n_id_not_match:"应用名和appid不匹配",password_not_match_rule:"密码不符合规则",enter_correct_sms_code:"请输入正确的短信验证码",enter_correct_image_code:"请输入正确的图形验证码",phone_registered:"手机号已注册，请更换手机号注册或直接登录",risk_account_block_may:"帐号因存在风险被暂时封停,可",self_service_unblock:"自助解封",account_block_contact_service:"帐号处于违规封停期，请改天再试",appeal:"申诉",email_cannot_be_empty:"邮箱不能为空",email_format_error:"邮箱格式不正确",email_registered:"该邮箱已注册，请更换邮箱注册或直接登录"}},,,function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.hitBlock=undefined;var i=r(52),o=n(i),a=r(0),s=n(a),u=r(28),c=n(u),l=r(159),f=n(l),d=r(153),p=n(d),_=r(49),h=r(24),g=(n(h),r(148)),m=r(156),v=(n(m),r(175)),y=r(176),b=n(y),w=r(147),E=window.parent.xlQuickLogin.uiText,S=function(){function e(){var t=this;(0,s["default"])(this,e),this.is_submit=!1,this.hit_db_params={token:"",userID:"",code:"",tokentype:"0",smsEvent:"33"},this.hit_db_result={},this.sendSMSHandler=function(e){var r=c["default"].id("validate_mobile_error"),n=c["default"].id("validate_mobile_captcha_box"),i=c["default"].id("validate_mobile_captcha_img"),a=c["default"].id("validate_mobile_captcha_value"),s=(0,o["default"])({userID:t.hit_db_result.userID,smsEvent:33},t.hit_db_result);if("need"==t.captcha_status){var l=/^([A-Z]|[a-z]|\d){4}$/;if(!a.value||!l.exec(a.value))return void t.UI.showError(E.getErrorDescription("enter_correct_image_code"),r);s.verifyCode=a.value,s.verifyKey=c["default"].getCookie("VERIFY_KEY")}(0,v.isSmsButtonGrey)(c["default"].id("validate_mobile_get_code"))||(s.creditkey=store.get("creditkey"),b["default"].vertify_version()&&(s=b["default"].forceBaseParams2(s)),t.isFromApp(e.appName)&&(s.protocolVersion="300"),t.is_submit||(t._isSubmit(),s=(0,_.baseParams2)(s),p["default"].post((0,m.getGslb)("sendsms"),s,w.report_helper).then(function(e){var o=e.blogresult,a=E.getServerErrorDescption();if(0==o){t.captcha_status="",t.hit_db_params.token=e.token,t.hit_db_params.userID=s.userID;var l=c["default"].id("validate_mobile_get_code");(0,v.setSmsButtonGrey)(l)}else if(6==o)t.captcha_status="need",t.verify_type=e.verifyType?e.verifyType:e.verify_type,t.freshCaptchaUrl(),(0,u._display)(n,"block"),c["default"].bind(i,"click.x",function(){t.freshCaptchaUrl()}),t.UI.showError(""+a,r);else if("27"==o)t.freshCaptchaUrl(),c["default"].id("validate_mobile_captcha_value").value="",t.UI.showError(""+a,r);else{if("1007"==o)return t._reviewPanel();t.captcha_status="",t.UI.showError(""+a,r)}})["catch"](function(e){})))}}return e.prototype._isSubmit=function(){var e=this;this.is_submit=!0,setTimeout(function(){return e.is_submit=!1},2e3)},e.prototype.getUI=function(){this.UI=f["default"].getUI()},e.prototype.validateAccount=function(e){if(e.mobile&&""!=e.mobile&&this.validateMobile({userID:e.userid||e.userID,mobile:e.mobile}),e.msgurl&&""!=e.msgurl){var t=c["default"].getUrlParams(e.msgurl);t.mobile&&""!=t.mobile&&this.validateMobile({userID:t.userid||t.userID,mobile:t.mobile}),t.token&&""!=t.token&&(this.hit_db_params.tokentype="0",this.updatePassword(!0,t))}},e.prototype.updatePassword=function(e){var t=this,r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};this.UI.showUI("passwordChange");var n=c["default"].id("password_change_input"),i=c["default"].id("password_change_submit"),a=c["default"].id("password_change_error");c["default"].bind(i,"click.x",function(){var i=c["default"].trim(n.value);if(!i)return void t.UI.showError(E.getErrorDescription("password_cannot_be_empty"),a);if(!/^(?![\d]+$)(?![a-zA-Z]+$)(?![\`\-\=\[\]\\\;\'\,\.\/\~!@#$%^&*()_+{}|:"<>?]+$)[\da-zA-Z\`\-\=\[\]\\\;\'\,\.\/\~!@#$%^&*()_+{}|:"<>?]{6,16}$/.exec(i))return void t.UI.showError(E.getErrorDescription("password_not_match_rule"),a);t.hit_db_params.passWordNew=i;var s=(0,o["default"])({},t.hit_db_result,t.hit_db_params);e?(t.hit_db_result=r,s=(0,o["default"])({},t.hit_db_params,t.hit_db_result)):s=(0,o["default"])({},t.hit_db_result,t.hit_db_params),s.creditkey=c["default"].getCookie("creditkey"),b["default"].vertify_version()&&(s=b["default"].forceBaseParams2(s)),t.isFromApp(r.appName)&&(s.protocolVersion="300"),t.is_submit||(t._isSubmit(),p["default"].post((0,m.getGslb)("changepwdlogin"),(0,_.baseParams2)(s),w.report_helper).then(function(e){var r=e.blogresult,n=E.getServerErrorDescption();if(0==r)(0,g.loginSuccess)(g.LOGIN_TYPE.ACCOUNT,e);else if("1006"==r)t.UI.showError(""+n,a),setTimeout(function(){window.parent.location.reload()},1e3);else{if("1007"==r)return t._reviewPanel();t.UI.showError(""+n,a)}})["catch"](function(e){(0,g.loginFail)(-1,g.LOGIN_TYPE.ACCOUNT)}))})},e.prototype.validateMobile=function(){var e=this,t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;this.UI.showUI("validateMobile"),this.hit_db_params.tokentype="1",null!=r&&(this.postData=r),this.hit_db_result=t;var n=t.mobile;this.UI.showUI("validateMobile");var i=c["default"].id("validate_mobile_value"),a=c["default"].id("validate_mobile_value_l"),s=c["default"].id("validate_mobile_get_code"),u=c["default"].id("validate_mobile_code"),l=c["default"].id("validate_mobile_submit");i.value=n,i.disabled=!0,a.style.display="none";var f=c["default"].id("validate_mobile_error");c["default"].bind(l,"click.x",function(){var r=c["default"].trim(u.value),n=/^\d{6}$/;if(!r||!n.exec(r))return void e.UI.showError(E.getErrorDescription("enter_correct_sms_code"),f);if(""==e.hit_db_params.token)return void e.UI.showError(E.getErrorDescription("need_get_smscode"),f);e.hit_db_params.smsCode=r;var i=(0,o["default"])({},t,{userID:e.hit_db_result.userID,smsEvent:"33",smsCode:e.hit_db_params.smsCode,token:e.hit_db_params.token}),a=(0,_.baseParams2)(i);a.creditkey=store.get("creditkey"),b["default"].vertify_version()&&(a=b["default"].forceBaseParams2(a)),e.isFromApp(t.appName)&&(a.protocolVersion="300"),e.is_submit||(e._isSubmit(),p["default"].post((0,m.getGslb)("checksms"),a,w.report_helper).then(function(t){var r=E.getServerErrorDescption();if("0"==t.blogresult)e.hit_db_params.token=t.securitytoken,e.updatePassword();else{if("1007"==t.blogresult)return e._reviewPanel();e.UI.showError(""+r,f)}}))}),c["default"].bind(s,"click.x",function(r){e.sendSMSHandler(t)})},e.prototype.isFromApp=function(e){return/ios/i.test(e)||/android/i.test(e)},e.prototype._reviewPanel=function(){var e=c["default"].getCookie("reviewurl")||store.get("reviewurl");e&&b["default"].show_captch_panel(e)},e.prototype.freshCaptchaUrl=function(){c["default"].id("validate_mobile_captcha_img").src="https://"+xll.PARAMS.SERVER_LOGIN[0]+"/image?t="+this.verify_type+"&cachetime="+(new Date).getTime()},e}(),I=new S;t.hitBlock=I},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.mobileRegister=t["default"]=undefined;var i=r(0),o=n(i),a=r(145),s=n(a),u=r(146),c=n(u),l=r(28),f=(n(l),r(24)),d=(n(f),r(175)),p=(r(148),r(74)),_=r(98),h=_.userBehaviorsStatServerLoader.get(),g=void 0,m=function(e){function t(){var r,n,i;(0,o["default"])(this,t);for(var a=arguments.length,u=Array(a),c=0;c<a;c++)u[c]=arguments[c];return r=n=(0,s["default"])(this,e.call.apply(e,[this].concat(u))),n.form_type="register",n.smscode=function(e){var t=e.submitType;n._get_code({register:1,submitType:t})},n.submit=function(e){var t=e.submitType,r=(new Date).getTime();n._submit({register:1,beginTime:r,submitType:t})},i=r,(0,s["default"])(n,i)}return(0,c["default"])(t,e),t.prototype.dom=function(){h.stat({action:p.userBehaviorsStatActions.registPanelShow}),g=this.UI,this.pr_doms=g.mobileRegisterDoms(),this.doms=this.pr_doms},t.prototype.main=function(){var e=this.pr_doms;this.bind(),this.captcha_hook_enter_submit(e.register_form),this._mobileInit()},t}(d.MobileForm);t["default"]=m;t.mobileRegister=new m},,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}var i=r(52),o=n(i),a=r(28),s=n(a),u=r(148),c=r(159),l=n(c),f=r(230),d=r(231),p=r(194),_=r(24),h=n(_),g=!1,m={},v=(0,u.GetUIManager)(m);m=(0,o["default"])({},m,{Util:s["default"],registerUI:function(){return l["default"].registerUI.apply(l["default"],arguments)},init:function(e){!0!==g&&(g=!0,v.init(e))},config:v.config,getLoginBox:v.getLoginBox,getBindBox:v.getBindBox,setBackgroud:v.setBackgroud,showUI:v.showUI,showError:v.showError,clientLoginFail:f.clientLoginFail,setSafePhoneLogin:d.setSafePhoneLogin,hitLibraryAndUpdatePassword:function(e,t){p.hitBlock.getUI(),t?p.hitBlock.updatePassword(!0,e):p.hitBlock.validateMobile(e)}}),window.xlQuickLogin=m,window._=h["default"]},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.clientLoginGuide=t["default"]=undefined;var i=r(150),o=n(i),a=r(151),s=n(a),u=r(0),c=n(u),l=r(145),f=n(l),d=r(146),p=n(d),_=r(23),h=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t["default"]=e,t}(_),g=r(38),m=r(165),v=r(73),y=n(v),b=r(156),w=n(b),E=r(28),S=n(E),I=void 0,T=new g.GBHelper(h.gbAttrNames.clientFeatureApi),k=T.getTarget(k),x=function(e){function t(){var r,n,i,a=this;(0,c["default"])(this,t);for(var u=arguments.length,l=Array(u),d=0;d<u;d++)l[d]=arguments[d];return r=n=(0,f["default"])(this,e.call.apply(e,[this].concat(l))),n.submit=(0,s["default"])(o["default"].mark(function p(){var e,t,r,i,s;return o["default"].wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.prev=0,o.next=3,k.showLoginWindow();case 3:o.next=12;break;case 5:o.prev=5,o.t0=o["catch"](0),e=y["default"].DEFUALT_UI,t=y["default"].LOGIN_TYPES,r=y["default"].REGISTER_TYPES,""!==t&&"login"===e?(i=(0,m.getUITypeNameById)(e,t[0]))&&n.UI.showUI(i):""!==r&&"register"===e&&(s=(0,m.getUITypeNameById)(e,r[0]))&&n.UI.showUI(s);case 12:case"end":return o.stop()}},p,a,[[0,5]])})),i=r,(0,f["default"])(n,i)}return(0,p["default"])(t,e),t.prototype.dom=function(){I=this.UI,this.doms=this.clg_doms=I.clientLoginGuideDoms()},t.prototype.main=function(){var e=this,t=this.clg_doms;S["default"].bind(t.client_login_guide_btn,"click.x",function(){e.submit()})},t}(w["default"]);t["default"]=x;t.clientLoginGuide=new x},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.accountLogin=t["default"]=undefined;var i=r(0),o=n(i),a=r(145),s=n(a),u=r(146),c=n(u),l=r(28),f=n(l),d=r(73),p=n(d),_=r(153),h=n(_),g=r(49),m=r(24),v=n(m),y=r(148),b=r(156),w=n(b),E=r(147),S=r(46),I=r(218),T=r(194),k=r(176),x=n(k),C=r(74),L=r(98),O=L.userBehaviorsStatServerLoader.get(),A=void 0,P=!1,N=!1,D=void 0,R=window.parent.xlQuickLogin.uiText,M=function(e){function t(){var r,n,i;(0,o["default"])(this,t);for(var a=arguments.length,u=Array(a),c=0;c<a;c++)u[c]=arguments[c];return r=n=(0,s["default"])(this,e.call.apply(e,[this].concat(u))),n.form_type="login",n.submit=function(e){var t=e.submitType,r=(new Date).getTime(),i=n,o=i.al_doms;if(!N&&(n._isSubmit(),!P)){var a=0;o&&o.check_remember&&(a=!0===o.check_remember.checked?1:0),O.stat({action:C.userBehaviorsStatActions.loginPanelLoginClick,extData:{isAuto:a,clickType:"number"==typeof t?t:undefined}}),D.hideError(o.warn);var s=f["default"].trim(o.input_username.value),u=o.input_password.value,c=o.input_captcha.value,d=(0,l._visible)(o.input_captcha),p=[[!s,R.getErrorDescription("username_cannot_be_empty"),"input_username"],[!u,R.getErrorDescription("password_cannot_be_empty"),"input_password"],[d&&(!c||0==c.length),R.getErrorDescription("verification_code_must_fill"),"input_captcha"],[d&&c.length<4,R.getErrorDescription("incorrect_verification_code"),"input_captcha"]];if(n.validate(p)){var _={type:0,mode:"account",beginTime:r};if(n.switchLoadStatus(1),f["default"].isClientLogin()){var m={username:s,password:u,captcha:c};xll.getClientLoginFun(m),n.switchLoadStatus()}else{if(n.postData={userName:s,passWord:u,isMd5Pwd:"0"},d&&(n.postData.verifyCode=c,n.postData.verifyKey=f["default"].getCookie("VERIFY_KEY")),n.postData.creditkey=store.get("creditkey"),x["default"].vertify_version()&&(n.postData=x["default"].forceBaseParams2(n.postData)),xll.isLocal&&""!=store.get("loginkey")&&"1"==store.get("xl_loginkey"))return n.postLoginKey(_),!1;var v=new I.RequestMonitorStatHelper;(0,y.loginFetch)(3),h["default"].post((0,b.getGslb)("login",{username:s}),(0,g.baseParams2)(n.postData),E.report_helper,{requestMonitorStatHelper:v}).then(function(e){v.statAll();var t=e.blogresult;if(0!=t&&(_.errorCode=(0,S.forceToString)(t,"")),0!=t&&(0,y.loginFail)(t,3,_),(17==t||39==t)&&xll.PARAMS.IS_HIT_BLOCK)return T.hitBlock.validateAccount(e);n.logincallback(t,e,_)})["catch"](function(e){v.statAll(),(0,y.loginFail)(-1,3,_),n.logincallback(-1,{},_)})}return!1}}},n.logincallback=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},r=arguments[2],i=n,o=i.al_doms;if(n.switchLoadStatus(),0==e)xll.Util.delVerifies(),n.checkAutoLogin(),(0,y.loginSuccess)(y.LOGIN_TYPE.ACCOUNT,t,r),o.input_captcha.value="";else{if(3==e&&(o.input_password.value=""),o.input_captcha.value="","1007"==e){var a=f["default"].getCookie("reviewurl")||store.get("reviewurl");if(a&&(x["default"].show_captch_panel(a,"al_submit"),"103"==p["default"].LOGIN_ID))try{parent.niuCaptchaNotify(1)}catch(c){}return}var s=f["default"].getCookie("verify_type");if(s){if(n._fresh_captch(),n._show_captcha(),"103"==p["default"].LOGIN_ID)try{parent.niuCaptchaNotify(1)}catch(c){}}else if((0,l._display)(o.captcha_container,"none"),"103"==p["default"].LOGIN_ID)try{parent.niuCaptchaNotify(0)}catch(c){}try{xll.changeSizeFunc(s)}catch(c){}var u=R.getServerErrorDescption();D.showError(""+u,o.warn)}},i=r,(0,s["default"])(n,i)}return(0,c["default"])(t,e),t.prototype.dom=function(){D=this.UI,this.doms=this.al_doms=D.accountLoginDoms(),T.hitBlock.UI=D},t.prototype.main=function(){var e=this,t=this.al_doms,r=this.doms;A=!1,(0,l._display)(t.captcha_container,"none"),t.input_password.value="",t.input_captcha.value="",t.input_username.value=p["default"].DEFAULT_ACCOUNT,v["default"].forEach(r.captcha_fresh,function(t){return f["default"].bind(t,"click.x",e._fresh_captch)}),f["default"].bind(r.button_submit,"click.x",function(){return e.submit({submitType:1})})},t.prototype._isSubmit=function(){N=!0,setTimeout(function(){return N=!1},2e3)},t.prototype.postLoginKey=function(e){var t=this,r=this.al_doms,n=f["default"].getCookie();n.userid||(n=store.getAll());var i={loginKey:n.loginkey,userName:n.userid};(0,y.loginFetch)(2),h["default"].post((0,b.getGslb)("loginkey",{userid:n.userid}),(0,g.baseParams2)(i),E.report_helper).then(function(n){var i=n.blogresult;0!=i&&(e.errorCode=(0,S.forceToString)(i,"")),0==i?(store.enabled&&store.set("xl_autologin_info",(new Date).getTime()+"|0"),f["default"].setCookie("xl_loginkey","1",864e5,document.domain)):(r.input_password.value="",(0,y.loginFail)(i,2),store.enabled&&store.remove("loginkey"),f["default"].delCookie("loginkey"),f["default"].setCookie("xl_loginkey","0",864e5,document.domain)),t.logincallback(i,n,e)})["catch"](function(r){(0,y.loginFail)(-1,2,e),t.logincallback(-1)})},t.prototype.checkAutoLogin=function(){!this.doms.check_remember.checked?(0,y.delAutoLogin)():(0,y.setAutoLogin)()},t.prototype._show_captcha=function(){var e=this.al_doms;A=!0,(0,l._display)(e.captcha_container,"")},t}(w["default"]);t["default"]=M;t.accountLogin=new M},function(e,t,r){"use strict";t.__esModule=!0,t.RequestMonitorStatHelper=t.RequestMonitorStat=undefined;var n=r(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(n),o=r(173),a=r(46),s=o.monitorStatServerLoader.get(),u=t.RequestMonitorStat=function(){function e(){(0,i["default"])(this,e),this._url=undefined,this._requestTime=undefined,this._costTime=undefined,this._status=undefined,this._statusText=undefined,this._businessStatus=undefined,this._businessStatusText=undefined}return e.prototype.recordUrl=function(e){this._url===undefined&&(this._url=(0,a.forceToString)(e,""))},e.prototype.recordRequestTime=function(){this._requestTime===undefined&&(this._requestTime=new Date)},e.prototype.recordCostTime=function(){this._costTime===undefined&&(this._costTime=(new Date).getTime(),this._requestTime!==undefined&&(this._costTime=this._costTime-this._requestTime.getTime()))},e.prototype.recordStatus=function(e){this._status===undefined&&"number"==typeof e&&(this._status=e)},e.prototype.recordStatusText=function(e){this._statusText===undefined&&(this._statusText=(0,a.forceToString)(e,""))},e.prototype.recordTimeout=function(){this.recordStatusText("timeout")},e.prototype.recordBusinessStatus=function(e){this._businessStatus===undefined&&(this._businessStatus=(0,a.forceToString)(e,""))},e.prototype.recordBusinessStatusText=function(e){this._businessStatusText===undefined&&(this._businessStatusText=(0,a.forceToString)(e,""))},e.prototype.stat=function(){var e={url:this._url,requestTime:this._requestTime,costTime:this._costTime,status:this._status,statusText:this._statusText,businessStatus:this._businessStatus,businessStatusText:this.businessStatusText};this._formatStatData(e),s.statRequest(e)},e.prototype._formatStatData=function(e){e&&e.requestTime&&(e.requestTime=(0,a.dateToTimeString)(e.requestTime))},e}();t.RequestMonitorStatHelper=function(){function e(){(0,i["default"])(this,e),this._currentMonitorStat=null,this._monitorStatDatas=[]}return e.prototype.newStat=function(){this._currentMonitorStat=new u,this._monitorStatDatas.push(this._currentMonitorStat)},e.prototype.getStat=function(){return this._currentMonitorStat},e.prototype.statAll=function(){for(var e=this._monitorStatDatas.length,t=0;t<e;t++){this._monitorStatDatas[t].stat()}},e}()},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.accountRegister=t["default"]=undefined;var i=r(0),o=n(i),a=r(145),s=n(a),u=r(146),c=n(u),l=r(28),f=n(l),d=r(73),p=n(d),_=r(152),h=r(156),g=n(h),m=r(24),v=(n(m),r(148)),y=window.parent.xlQuickLogin.uiText,b=!1,w=!1,E=void 0,S=void 0,I=function(e){function t(){var r,n,i;(0,o["default"])(this,t);for(var a=arguments.length,u=Array(a),c=0;c<a;c++)u[c]=arguments[c];return r=n=(0,s["default"])(this,e.call.apply(e,[this].concat(u))),n.captchaUrl="registerCaptcha",n.accountCheck={account:"",check:0},n.SIMPLE_TIPS=y.getErrorDescription("password_weak"),n.PASSWORD_TIPS=y.getErrorDescription("standard_phone_format"),i=r,(0,s["default"])(n,i)}return(0,c["default"])(t,e),t.prototype.dom=function(){S=this.UI,this.ar_doms=S.accountRegisterDoms(),this.doms=this.ar_doms},t.prototype.main=function(){var e=this,t=this.ar_doms;(0,_.register)("isNeedValidate",{},function(r){if(200==r.result&&1==r.need){var n;e._fresh_captch();for(n in t.captcha_container)t.captcha_container[n].style.display="";b=!0}if("103"==p["default"].LOGIN_ID)try{parent.niuCaptchaNotify(1)}catch(i){}else if(!b)try{parent.niuCaptchaNotify(0)}catch(i){}E=b?1:0;try{xll.changeSizeFunc(E)}catch(i){}});for(var r=t.captcha_fresh.length-1;r>=0;--r)f["default"].bind(t.captcha_fresh[r],"click.x",function(){return e._fresh_captch()});f["default"].bind(t.button_submit,"click.x",function(){return e.submit()})},t.prototype.clearPassword=function(){var e=this.doms;e.input_password.value="",e.input_password2.value=""},t.prototype.submit=function(){var e=this,t=this.ar_doms;this.doms;if(!w){S.hideError(t.warn);var r=f["default"].trim(t.input_account.value),n=t.input_password.value,i=t.input_password2.value,o=f["default"].trim(t.input_captcha.value),a=/^[a-z0-9A-Z_]{6,16}$/,s=/[a-zA-Z]+/,u=[[""==r,y.getErrorDescription("account_cannot_be_empty"),"input_account"],[r.length<6,y.getErrorDescription("account_lessthan_6_chars"),"input_account"],[r.length>16,y.getErrorDescription("account_largethan_16_chars"),"input_account"],[!a.test(r),y.getErrorDescription("account_format_error_n_tips"),"input_account"],[!s.test(r),y.getErrorDescription("account_format_error_n_need_chars"),"input_account"],[this.accountCheck.account===r&&-1===this.accountCheck.check,y.getErrorDescription("account_registered"),"input_account"],[!n,this.SIMPLE_TIPS,"input_password"],[n.length<6,y.getErrorDescription("password_lessthan_6_digits"),"input_password"],[n.length>16,y.getErrorDescription("password_largethan_16_digits"),"input_password"],[!f["default"].checkPassword(n),this.PASSWORD_TIPS,"input_password",this.clearPassword],[n!==i,y.getErrorDescription("password_inconsistent_need_reenter"),"input_password"],[b&&!o,y.getErrorDescription("verification_code_must_fill"),"input_captcha"],[b&&o.length<4||o.length>6,y.getErrorDescription("image_verification_code_error"),"input_captcha"]];if(this.validate(u)){if(!t.agree.checked)return void S.showError(y.getErrorDescription("read_n_tick_user_agreement"),t.warn);w=!0,f["default"].text(t.button_submit,y.getText(p["default"].REGISTER_WITH_LOGIN?"other_config_register_with_login_button_text_status_1":"other_config_register_button_text_status_1")),(0,_.register)("register",{username:r,pwd:n,code:o},function(r){var n=r.result,i=r.sessionid,o=r.uid;if(200==n&&i)return void _.req.sessionlogin({sessionID:i,userID:o},function(e){if(w=!1,0!=e.blogresult){var r=y.getServerErrorDescption();return void S.showError(r,t.warn)}(0,v.setAutoLogin)(),(0,v.registerSuccess)()});w=!1,f["default"].text(t.button_submit,y.getText(p["default"].REGISTER_WITH_LOGIN?"other_config_register_with_login_button_text_status_0":"other_config_register_button_text_status_0")),e._fresh_captch(),301==n?S.showError(y.getErrorDescription("account_format_error_n_tips"),t.warn,t.input_account):704==n?S.showError(y.getErrorDescription("account_registered"),t.warn,t.input_account):701==n?(e.clearPassword(),S.showError(e.SIMPLE_TIPS,t.warn,t.input_password)):801==n?(e.clearPassword(),S.showError(e.PASSWORD_TIPS,t.warn,t.input_password)):600==n?(t.input_captcha.value="",S.showError(y.getErrorDescription("image_verification_code_error"),t.warn,t.input_captcha)):706==n?S.showError(y.getErrorDescription("shield_reject")+"("+n+")",t.warn,t.input_account):S.showError(y.getErrorDescription("internal_server_errpr_retry")+"("+n+")",t.warn,t.input_account)})}}},t}(g["default"]);t["default"]=I;t.accountRegister=new I},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.qrLogin=t["default"]=undefined;var i=r(52),o=n(i),a=r(15),s=(n(a),r(221)),u=n(s),c=r(0),l=n(c),f=r(145),d=n(f),p=r(146),_=n(p),h=r(28),g=n(h),m=r(153),v=(n(m),r(49),r(24)),y=(n(v),r(148)),b=r(156),w=n(b),E=(r(147),r(178)),S=r(176),I=(n(S),r(46)),T=r(74),k=r(98),x=k.userBehaviorsStatServerLoader.get(),C=window.parent.xlQuickLogin.uiText,L=parent.xdas,O=new E.BusinessRequest,A=void 0,P=function(e){function t(){(0,l["default"])(this,t);var r=(0,d["default"])(this,e.call(this));return r.socket=!1,r.secret=g["default"].getCookie("qrsecret")||store.get("qrsecret")||(0,v.Guid)(32),r.getQrcode=function(){var e=arguments.length>0&&arguments[0]!==undefined&&arguments[0],t=r;if(!r._isGettingQrCode&&r.socket&&r.socket.isCheckIn){r._isGettingQrCode=!0;var n=r.ql_doms;O.getQrCode({secret:r.secret,authdomain:xll.PARAMS.STATIC_DOMAIN}).then(function(i){var o=i.data;if(0==o.errorCode)t._hadSuccessLoadQrCode=!0,n.img.src="data:image/jpeg;base64,"+o.qrcode_image,n.referer.style.display="none",e&&(n.img.onload=function(){var e=0!==r._qrCodeFreshClickTime,t=e?T.userBehaviorsStatActions.loginPanelQrCodeRefresh:T.userBehaviorsStatActions.loginPanelQrCodeShow,n=(new Date).getTime()-(e?r._qrCodeFreshClickTime:r._showQrLoginFormTime);x.stat({action:t,extData:{qrCodeTimes:n}})});else{C.getServerErrorDescption();n.referer.style.display="block"}t._isGettingQrCode=!1})["catch"](function(){t._isGettingQrCode=!1,(0,y.loginFail)(-1,6)})}},r.chunk=function(e,t){var r=e.split("");return(0,u["default"])({length:Math.ceil(r.length/t)},function(e,n){return r.slice(n*t,n*t+t)})},r._qrCodeFreshClickTime=0,r._showQrLoginFormTime=0,r._beginScanQrCodeTime=0,r._hadSuccessLoadQrCode=!1,r._isGettingQrCode=!1,r}return(0,_["default"])(t,e),t.prototype.dom=function(){A=this.UI,this._showQrLoginFormTime=(new Date).getTime(),this.doms=this.ql_doms=A.qrLoginDoms(),this.connectSocket()},t.prototype.display=function(e,t){e.style.display=t},t.prototype.main=function(){var e=this,t=this.ql_doms;g["default"].bind(t.flash,"click.x",function(){return e.showDesc(1,!0,!0)}),g["default"].bind(t.succ,"click.x",function(){return e.showDesc(1,!1,!0)}),g["default"].setCookie("qrsecret",this.secret),xll.isLocal&&store.set("qrsecret",this.secret)},t.prototype.connectSocket=function(){var e=this,t=g["default"].getCookie("deviceid")||store.get("deviceid");this.socket||this.socket.isCheckIn||"undefined"==typeof XlSocket||(this.socket=new XlSocket({appid:window.APPID,deviceid:t,serviceType:"10000",gslbQueryHostKey:xll.PARAMS.MESSAGE_CHANNEL_GSLB_QURERY_HOST_KEY,server:xll.PARAMS.MESSAGE_CHANNEL_SERVER,autoPing:!0,pullTimeout:3e4,enableGSLB:xll.PARAMS.CAN_GSLB,reconnectionDelay:5e3,reconnectionDelayMax:1e4,responseEvent:function(t){t.checkInDevice&&!e._hadSuccessLoadQrCode&&e.getQrcode(!0),t.notifyUnicast2deviceMsg&&e.socketMsg(t.notifyUnicast2deviceMsg)},onConnect:function(){},onDisconnect:function(){}}))},t.prototype.decrypt=function(e,t){t=t.replace(/\_/g,"/"),t=t.replace(/\-/g,"+");var r=t.length%4;r>0&&(t+="===".substr(0,4-r));var n=CryptoJS.enc.Hex.parse(e),i=CryptoJS.AES.decrypt(t,n,{iv:n,mode:CryptoJS.mode.CBC,padding:CryptoJS.pad.Pkcs7}).toString(),o=this.chunk(i,2),a="";return o.map(function(e){var t="0x"+e.join(",").replace(",","");a+=String.fromCharCode(parseInt(t))}),a},t.prototype.socketMsg=function(e){if(e.msgs){var t=e.msgs[e.msgs.length-1],r=!1,n=this.decrypt(this.secret,t.data);/code/gi.test(n)||(r=!0,n=this.decrypt("12345678901234567890123456789012",t.data)),n=JSON.parse(n),r?this.showDesc(3):this.socketRes(n)}},t.prototype.socketRes=function(e){var t=this,r=e.retcode,n=e.data,i="";switch(r){case"1":break;case"2":this._beginScanQrCodeTime=(new Date).getTime(),this.showDesc(2);break;case"3":if(i=n&&"string"==typeof n?JSON.parse(n):n,xll.isLocal){var a=(0,E.json2CookieLoginData)(i);i=(0,o["default"])({},i,a),this.setLogin(i)}else i=(0,E.json2CookieLoginData)(i),O.getUserInfo({userID:i.userid,sessionID:i.sessionid}).then(function(e){var r=e.data;i=(0,o["default"])({},i,(0,E.json2CookieLoginData)(r)),t.setLogin(i)})["catch"](function(){t.setLogin(i)});break;case"4":this.showDesc(1);break;case"5":i=n&&"string"==typeof n?JSON.parse(n):n,this.thirdLogin(i);break;case"6":i=n&&"string"==typeof n?JSON.parse(n):n,this.qrcheck(i)}},t.prototype.setLogin=function(e){var t={type:0,mode:"qr",beginTime:this._beginScanQrCodeTime};if(0==e.errcode){var r=2592e6;g["default"].setCookie("userid",e.userid,r,document.domain),g["default"].setCookie("loginkey",e.loginkey,r,document.domain),g["default"].setCookie("sessionid",e.sessionid,"",document.domain),g["default"].setCookie("usernewno",e.usernewno,"",document.domain),g["default"].setCookie("nickname",e.nickname,"",document.domain),(0,y.loginSuccess)(y.LOGIN_TYPE.QRAPP,e,t)}else t.errorCode=(0,I.forceToString)(e.errcode,""),x.statLoginResult(!0,t),this.showDesc(3)},t.prototype.qrcheck=function(e){0!=e&&this.showDesc(1)},t.prototype.thirdLogin=function(e){var t={type:0,mode:"qr",beginTime:this._beginScanQrCodeTime};if(0==e.errcode){var r=2592e6;g["default"].setCookie("userid",e.userid,r,document.domain),g["default"].setCookie("loginkey",e.loginkey,r,document.domain),g["default"].setCookie("sessionid",e.sessionid,"",document.domain),g["default"].setCookie("usernewno",e.usernewno,"",document.domain),g["default"].setCookie("nickname",e.nickname,"",document.domain),(0,y.loginSuccess)(y.LOGIN_TYPE.QRAPP,e,t)}else if(44==e.retcode){var n=e.retdata.replace(/redirectUrl=([a-z0-9]|%|\.|-)+/gi,"redirectUrl="+encodeURIComponent(parent.location.href));xll.isLocal?(n=n.replace(/platformVersion=3/,"platformVersion=0"),L.openNewTab({url:n,business:"qrLogin"}),L.onLoginWndClose("third")):parent.location.href=n}},t.prototype.showDesc=function(e){var t=!(arguments.length>1&&arguments[1]!==undefined)||arguments[1],r=arguments.length>2&&arguments[2]!==undefined&&arguments[2],n=this.ql_doms;this.hideDesc(),1==e?(this.display(n.qrbox,"block"),this.display(n.desc1,"block"),t&&(r&&(this._qrCodeFreshClickTime=(new Date).getTime(),x.stat({action:T.userBehaviorsStatActions.loginPanelQrCodeRefreshClick})),this.getQrcode(r))):2==e?(this.display(n.desc2,"block"),this.display(n.succ,"block"),this.display(n.qrbox,"none")):(this.display(n.qrbox,"block"),this.display(n.desc3,"block"),this.display(n.referer,"block"))},t.prototype.hideDesc=function(){var e=this.ql_doms;this.display(e.desc1,"none"),this.display(e.desc2,"none"),this.display(e.desc3,"none"),this.display(e.succ,"none"),this.display(e.referer,"none")},t}(w["default"]);t["default"]=P;t.qrLogin=new P},function(e,t,r){e.exports={"default":r(222),__esModule:!0}},function(e,t,r){r(50),r(223),e.exports=r(2).Array.from},function(e,t,r){"use strict";var n=r(19),i=r(7),o=r(32),a=r(99),s=r(100),u=r(45),c=r(224),l=r(83);i(i.S+i.F*!r(102)(function(e){Array.from(e)}),"Array",{from:function(e){var t,r,i,f,d=o(e),p="function"==typeof this?this:Array,_=arguments.length,h=_>1?arguments[1]:undefined,g=h!==undefined,m=0,v=l(d);if(g&&(h=n(h,_>2?arguments[2]:undefined,2)),v==undefined||p==Array&&s(v))for(t=u(d.length),r=new p(t);t>m;m++)c(r,m,g?h(d[m],m):d[m]);else for(f=v.call(d),r=new p;!(i=f.next()).done;m++)c(r,m,g?a(f,h,[i.value,m],!0):i.value);return r.length=m,r}})},function(e,t,r){"use strict";var n=r(8),i=r(20);e.exports=function(e,t,r){t in e?n.f(e,t,i(0,r)):e[t]=r}},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:59;if(e){var r=d["default"].text(e);d["default"].text(e,T.getText("sended")+" "+t+"s"),e.className="verify_btn verify_grey_btn";var n=setInterval(function(){if(0===--t)return d["default"].text(e,r),e.className="verify_btn",void clearInterval(n);d["default"].text(e,T.getText("sended")+" "+t+"s")},1e3)}}t.__esModule=!0,t.mailRegister=t["default"]=undefined;var o=r(0),a=n(o),s=r(145),u=n(s),c=r(146),l=n(c);t.setSmsButtonGrey=i;var f=r(28),d=n(f),p=r(73),_=n(p),h=r(152),g=r(156),m=n(g),v=r(148),y=r(49),b=r(153),w=n(b),E=r(147),S=r(176),I=n(S),T=window.parent.xlQuickLogin.uiText,k=!1,x=void 0,C=!1,L=function(e){function t(){var r,n,i;(0,a["default"])(this,t);for(var o=arguments.length,s=Array(o),c=0;c<o;c++)s[c]=arguments[c];return r=n=(0,u["default"])(this,e.call.apply(e,[this].concat(s))),n.captchaUrl="registerCaptcha",n.mailCheck={mail:"",check:0},n.sendingVerifCode=!1,n._setSendingVerifyCode=function(){var e=n;e.sendingVerifCode=!0,setTimeout(function(){e.sendingVerifCode=!1},59e3)},i=r,(0,u["default"])(n,i)}return(0,l["default"])(t,e),t.prototype.dom=function(){x=this.UI,this.mr_doms=x.mailRegisterDoms(),this.doms=this.mr_doms},t.prototype._isSubmit=function(){C=!0,setTimeout(function(){return C=!1},2e3)},t.prototype.main=function(){var e=this,t=this.mr_doms;d["default"].bind(t.button_getcode,"click.x",function(){return e.getcode()}),d["default"].bind(t.button_submit,"click.x",function(){return e.submit()})},t.prototype.submit=function(){var e=this;if(!C&&(this._isSubmit(),!k)){var t=this.mr_doms;x.hideError(t.warn);var r=d["default"].trim(t.input_mail.value),n=d["default"].trim(t.input_password.value),i=d["default"].trim(t.input_code.value);if(!r)return void x.showError(T.getErrorDescription("email_cannot_be_empty"),t.warn,t.input_mail);if(!d["default"].checkMail(r))return void x.showError(T.getErrorDescription("email_format_error"),t.warn,t.input_mail);if(!this.codeToken||""==this.codeToken)return void x.showError(T.getErrorDescription("please_get_verifycode"),t.warn,t.input_mail);if(!t.agree.checked)return void x.showError(T.getErrorDescription("read_n_tick_user_agreement"),t.warn);if(!i)return void x.showError(T.getErrorDescription("verification_code_must_fill"),t.warn);if(i.length<4||i.length>6)return void x.showError(T.getErrorDescription("verify_code"),t.warn);if(!n)return void x.showError(T.getErrorDescription("password_cannot_be_empty"),t.warn,t.input_password);if(n.length<6)return void x.showError(T.getErrorDescription("password_lessthan_6_digits"),t.warn,t.input_password);if(n.length>16)return void x.showError(T.getErrorDescription("password_largethan_16_digits"),t.warn,t.input_password);if(!d["default"].checkPassword(n))return void x.showError(T.getErrorDescription("standard_phone_format"),t.warn,t.input_password);k=!0,d["default"].text(t.button_submit,T.getText(_["default"].REGISTER_WITH_LOGIN?"other_config_register_with_login_button_text_status_1":"other_config_register_button_text_status_1"));var o={mailID:"mail_register",mail:r,code:i,token:this.codeToken,password:n};o.creditkey=d["default"].getCookie("creditkey"),w["default"].post((0,g.getGslb)("mailreg",{username:r}),(0,y.baseParams2)(o),E.report_helper).then(function(r){if(!e.shield("mailreg",r)){k=!1,d["default"].text(t.button_submit,T.getText(_["default"].REGISTER_WITH_LOGIN?"other_config_register_with_login_button_text_status_0":"other_config_register_button_text_status_0"));var n=r.blogresult;if(0==n)(0,v.setAutoLogin)(),(0,v.registerSuccess)();else{var i=T.getServerErrorDescption();x.showError(i,t.warn),1007==n&&e._reviewPanel()}}})["catch"](function(e){})}},t.prototype.getcode=function(){var e=this;if(!C&&(this._isSubmit(),!this.sendingVerifCode)){var t=this.mr_doms,r=d["default"].trim(t.input_mail.value);if(!r)return void x.showError(T.getErrorDescription("email_cannot_be_empty"),t.warn,t.input_mail);if(!d["default"].checkMail(r))return void x.showError(T.getErrorDescription("email_format_error"),t.warn,t.input_mail);this.hideError(),this.requestCode({mailValue:r,mailIdValue:"mail_register"}).then(function(r){if(!e.shield("sendmail",r)){var n=r.blogresult;if(e.codeToken=r.token,0==n)i(t.button_getcode),e._setSendingVerifyCode();else{var o=T.getServerErrorDescption();e.showError(o),1007==n&&e._reviewPanel()}}})["catch"](function(t){e.showError((0,g.getMessage)(-1))})}},t.prototype.requestCode=function(e){var t=e.mailValue,r=e.mailIdValue,n={mail:t,mailID:r};return n.creditkey=d["default"].getCookie("creditkey"),I["default"].vertify_version()&&(n=I["default"].forceBaseParams2(n)),w["default"].post((0,g.getGslb)("sendmail",{username:n.mail}),(0,y.baseParams2)(n),E.report_helper)},t.prototype._reviewPanel=function(){var e=d["default"].getCookie("reviewurl")||store.get("reviewurl");if(e&&(I["default"].show_captch_panel(e),"103"==_["default"].LOGIN_ID))try{parent.niuCaptchaNotify(1)}catch(t){}},t.prototype._check_mail=function(){var e=this.mailCheck,t=this.mr_doms;x.hideError(t.warn);var r=d["default"].trim(t.input_mail.value);if(r){if(!d["default"].checkMail(r))return void x.showError(T.getErrorDescription("email_format_error"),t.warn,t.input_mail);if(e.mail===r){if(1===e.check)return;if(-1===e.check)return void x.showError(T.getErrorDescription("email_registered"),t.warn)}(0,h.register)("checkbind",{account:r,type:"mail"},function(n){200==n.result&&(e.mail=r,1==n.binded?(e.check=-1,x.showError(T.getErrorDescription("email_registered"),t.warn,t.input_mail)):0==n.binded&&(e.check=1))})}},t}(m["default"]);t["default"]=L;t.mailRegister=new L},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.mobileRegisterWithPassword=t["default"]=undefined;var i=r(0),o=n(i),a=r(145),s=n(a),u=r(146),c=n(u),l=r(28),f=(n(l),r(24)),d=(n(f),r(175)),p=(r(148),void 0),_=function(e){function t(){var r,n,i;(0,o["default"])(this,t);for(var a=arguments.length,u=Array(a),c=0;c<a;c++)u[c]=arguments[c];return r=n=(0,s["default"])(this,e.call.apply(e,[this].concat(u))),n.form_type="register",n.smscode=function(){n._get_code({register:1})},n.submit=function(){n._submit({register:1,registerType:1})},i=r,(0,s["default"])(n,i)}return(0,c["default"])(t,e),t.prototype.dom=function(){p=this.UI,this.pr_doms=p.mobileRegisterWithPasswordDoms(),this.doms=this.pr_doms},t.prototype.main=function(){var e=this.pr_doms;this.bind(),this.captcha_hook_enter_submit(e.register_form),this._mobileInit()},t}(d.MobileForm);t["default"]=_;t.mobileRegisterWithPassword=new _},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.mobileRegisterOneStep=t["default"]=undefined;var i=r(0),o=n(i),a=r(145),s=n(a),u=r(146),c=n(u),l=r(28),f=n(l),d=r(73),p=(n(d),r(152)),_=(r(159),r(195)),h=n(_),g=r(148),m=window.parent.xlQuickLogin.uiText,v=!1,y=function(e){function t(){return(0,o["default"])(this,t),(0,s["default"])(this,e.apply(this,arguments))}return(0,c["default"])(t,e),t.prototype.submit=function(){var e=this.pr_doms,t=this.UI;if(!v){t.hideError(e.warn);var r=f["default"].trim(e.input_mobile.value),n=f["default"].trim(e.input_code.value),i=f["default"].trim(e.input_password.value);if(!r)return void t.showError(m.getErrorDescription("phone_cannot_be_empty"),e.warn,e.input_mobile);if(!f["default"].checkMobile(r))return void t.showError(m.getErrorDescription("phone_format_wrong"),e.warn,e.input_mobile);if(!n)return void t.showError(m.getErrorDescription("smscode_cannot_be_empty"),e.warn,e.input_code);if(6!==n.length)return void t.showError(m.getErrorDescription("smscode_wrong"),e.warn,e.input_code);if(!i)return void t.showError(m.getErrorDescription("password_cannot_be_empty"),e.warn2,e.input_password);if(i.length<6)return void t.showError(m.getErrorDescription("password_lessthan_6_digits"),e.warn2,e.input_password);if(i.length>16)return void t.showError(m.getErrorDescription("password_largethan_16_digits"),e.warn2,e.input_password);if(!e.agree.checked)return void t.showError(m.getErrorDescription("read_n_tick_user_agreement"),e.warn);v=!0,f["default"].text(e.button_submit,m.getText("other_config_register_button_text_status_1")),(0,p.register)("mobileregisterpwd",{mobile:r,code:n,password:i},function(r){200==r.result&&r.sessionid?p.req.sessionlogin({sessionid:r.sessionid,userid:r.uid},function(t){v=!1,f["default"].text(e.button_submit,m.getText("other_config_register_button_text_status_0")),0===t.blogresult&&(0,g.setAutoLogin)(),(0,g.registerSuccess)()}):(v=!1,f["default"].text(e.button_submit,m.getText("other_config_register_button_text_status_0")),702==r.result||700==r.result?t.showError(m.getErrorDescription("phone_registered"),e.warn,e.input_mobile):600==r.result?(e.input_code.value="",t.showError(m.getErrorDescription("smscode_wrong"),e.warn,e.input_code)):t.showError(m.getErrorDescription("internal_server_errpr_retry"),e.warn))})}},t}(h["default"]);t["default"]=y;t.mobileRegisterOneStep=new y},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.mobileLogin=t["default"]=undefined;var i=r(0),o=n(i),a=r(145),s=n(a),u=r(146),c=n(u),l=r(28),f=(n(l),r(24)),d=(n(f),r(148),r(175)),p=void 0,_=function(e){function t(){var r,n,i;(0,o["default"])(this,t);for(var a=arguments.length,u=Array(a),c=0;c<a;c++)u[c]=arguments[c];return r=n=(0,s["default"])(this,e.call.apply(e,[this].concat(u))),n.form_type="login",n.smscode=function(e){var t=e.submitType;n._get_code({submitType:t})},n.submit=function(e){var t=e.submitType,r=(new Date).getTime();n._submit({beginTime:r,submitType:t})},i=r,(0,s["default"])(n,i)}return(0,c["default"])(t,e),t.prototype.dom=function(){p=this.UI,this.ml_doms=p.mobileLoginDoms(),this.doms=this.ml_doms},t.prototype.main=function(){var e=this.ml_doms;this.bind(),this.captcha_hook_enter_submit(e.login_form),this._mobileInit()},t}(d.MobileForm);t["default"]=_;t.mobileLogin=new _},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.thirdLogin=t["default"]=undefined;var i=r(0),o=n(i),a=r(145),s=n(a),u=r(146),c=n(u),l=r(23),f=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t["default"]=e,t}(l),d=r(38),p=r(60),_=r(156),h=n(_),g=r(73),m=n(g),v=r(49),y=r(24),b=(n(y),r(163)),w=new d.GBHelper(f.gbAttrNames.innerQuickLogin),E=w.getTarget(),S=parent.xlQuickLogin,I=function(e){function t(){var r,n,i;(0,o["default"])(this,t);for(var a=arguments.length,u=Array(a),c=0;c<a;c++)u[c]=arguments[c];return r=n=(0,s["default"])(this,e.call.apply(e,[this].concat(u))),n.is_render=!1,n.form_type="thirdLogin",i=r,(0,s["default"])(n,i)}return(0,c["default"])(t,e),t.prototype.main=function(){var e=this;this.renderIcon(),this.intervalId=setInterval(function(){e.renderIcon()},100)},t.prototype.renderIcon=function(){""==(0,v.deviceid)()||(0,v.deviceid)().length<20||(clearInterval(this.intervalId),this.is_render||(this.is_render=!0,this.getParamsAndRender()))},t.prototype.getParamsAndRender=function(){var e=this;if(S.xl9Client()){var t=parent.xl9;t.api.check("GetLoginCommonParamInfo",function(r){if(!r)return void e.initThirdLogin((0,v.baseParams2)({}));t.api.call("GetLoginCommonParamInfo",function(t,r){var n=JSON.parse(r);e.initThirdLogin(n)})})}else(0,b.checkIsNewTBC)()&&p.gbConfig.getSyncPC()?E.isLoginStateInited()?this.initThirdLogin((0,v.baseParams2)({format:"json"})):E.once("after-login-state-inited",function(){e.initThirdLogin((0,v.baseParams2)({format:"json"}))}):this.initThirdLogin((0,v.baseParams2)({}))},t.prototype.initThirdLogin=function(e){var t="";for(var r in e){t+="&"+r+"="+e[r]}this.UI.initThirdLogin(m["default"].LOGIN_SUCCESS_URL,m["default"].ALL_HTTPS,m["default"].USE_CDN,m["default"].CDN_PATH,t,m["default"].THIRD_LOGIN_GROUP,m["default"].THIRD_LOGIN_DEFAULT)},t}(h["default"]);t["default"]=I;t.thirdLogin=new I},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){S.fail(e)}t.__esModule=!0,t.clientLogin=undefined;var o=r(0),a=n(o),s=r(145),u=n(s),c=r(146),l=n(c);t.clientLoginFail=i;var f=r(73),d=n(f),p=r(28),_=n(p),h=r(159),g=n(h),m=r(156),v=n(m),y=window.parent.xlQuickLogin.uiText,b=void 0,w="----",E=function(e){function t(){var r,n,i;(0,a["default"])(this,t);for(var o=arguments.length,s=Array(o),c=0;c<o;c++)s[c]=arguments[c];return r=n=(0,u["default"])(this,e.call.apply(e,[this].concat(s))),n.verify_type="",i=r,(0,u["default"])(n,i)}return(0,l["default"])(t,e),t.prototype.fail=function(e){this.UI=g["default"].UI;var t=this.UI;this.doms=d["default"].LOGIN_TYPES.indexOf("1")>=0?t.accountLoginDoms():t.mobileLoginDoms();var r=this.doms,n=e.errCode,i=e.strErr;i=i+"["+n+"]",_["default"].text(r.button_submit,y.getText("other_config_login_button_text_status_0")),3==n&&(r.input_password.value=""),r.input_captcha.value="",this._check_user(e),this.showError(i)},t.prototype._check_user=function(e){var t=this.doms,r=t.input_username?t.input_username.value:"";if((r=_["default"].trim(r))&&!/^\s*$/.test(r)){var n=e.errCode;w="----",6!=e.errCode&&27!=e.errCode||(w="",_["default"].setCookie("VERIFY_KEY","","",d["default"].DOMAIN),_["default"].setCookie("verify_type",e.verifyType,"",d["default"].DOMAIN)),0!==n&&e.verifyType?(b=!0,(0,p._display)(t.captcha_container,""),this._fresh_captch(),w=!1):(0,p._display)(t.captcha_container,"none");try{var i=0==n?0:1;"103"==d["default"].LOGIN_ID&&parent.niuCaptchaNotify(i),parent.xlQuickLogin.changeSizeFunc(i)}catch(o){}}},t}(v["default"]),S=t.clientLogin=new E},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function i(e){function t(){if(!(0,m.isSmsButtonGrey)(u.button_getcode)&&!p){p=!0;var e={};d["default"].getJson(f+"login_send",e,function(e){p=!1;0==e.result?(0,m.setSmsButtonGrey)(u.button_getcode):(E[e.result]=E[e.result]?E[e.result]:"网络异常,请稍后重试("+e.result+")",r.showError(E[e.result],u.warn))},"jsoncallback")}}if(!s&&y<=3)return void(c=setTimeout(function(){y++;try{w.setSafePhoneLogin(e)}catch(t){}},1e3));c&&clearTimeout(c);var r=g["default"].UI;if(u=r.mobileLoginDoms(),u.input_mobile_label.style.display="none",u.input_mobile.disabled=!0,d["default"].unbind(u.button_getcode,"click.x"),0!=e.result)return E[e.result]=E[e.result]?E[e.result]:"网络异常,请稍后重试，("+e.result+")",void r.showError(E[e.result]+e.result,u.warn);var n=e.uid,i=e.jumpcode,o=e.aqMobile,f="https://dynamic-aq-ssl."+_["default"].DOMAIN+"/interface/account?m=ucthunder9&userid="+n+"&jumpcode="+i+"&jsoncallback=?&func=",p=!1;u.input_mobile.value=o,d["default"].bind(u.button_getcode,"click.x",t),l=function(){if(!b){r.hideError(u.warn);var t=d["default"].trim(u.input_mobile.value),i=d["default"].trim(u.input_code.value),o=1==u.check_remember.checked;if(!t)return void r.showError(v.getErrorDescription("phone_cannot_be_empty"),u.warn,u.input_mobile);if(!i)return void r.showError(v.getErrorDescription("smscode_cannot_be_empty"),u.warn,u.input_code);if(i.length<4||i.length>6)return u.input_code.value="",void r.showError(v.getErrorDescription("smscode_wrong"),u.warn,u.input_code);b=!0,d["default"].text(u.button_submit,v.getText("other_config_login_button_text_status_1"));var s={};s.vcode=i,s.userid=n,s.deviceId=e.deviceId?e.deviceId:d["default"].getCookie("deviceid"),s.businessId=e.businessId,s.packageName=e.packageName,s.sdkVersion=e.sdkVersion,s.signature=e.signature,d["default"].getJson(f+"mobile_login",s,function(e){if(200==e.result){if("undefined"!=typeof XLJSWebViewBridge){var t={roCommand:"login_verify_mobile",roErrorCode:0,roErrorDesc:"",roData:{loginKey:e.login_key}};return void XLJSWebViewBridge.sendMessage("aqRecvOperationResult",(0,a["default"])(t),"")}var i={loginkey:e.login_key,userid:n};w.req.loginkey(i,function(e){if(b=!1,d["default"].text(u.button_submit,v.getText("other_config_login_button_text_status_0")),0!==e){var t=v.getServerErrorDescption();r.showError(t,u.warn)}else o?setAutoLogin():delAutoLogin(),loginSuccess(LOGIN_TYPE.MOBILE)})}else b=!1,d["default"].text(u.button_submit,v.getText("other_config_login_button_text_status_0")),600==e.result?(r.showError(v.getErrorDescription("smscode_wrong"),u.warn,u.input_code),u.input_code.value=""):r.showError(v.getErrorDescription("internal_server_errpr_retry")+e.result,u.warn)},"jsoncallback")}},d["default"].unbind(u.button_submit,"click.x"),d["default"].bind(u.button_submit,"click.x",l)}t.__esModule=!0;var o=r(12),a=n(o);t.setSafePhoneLogin=i;var s,u,c,l,f=r(28),d=n(f),p=r(73),_=n(p),h=r(159),g=n(h),m=r(175),v=window.parent.xlQuickLogin.uiText,y=0,b=!1,w=parent.xlQuickLogin,E={300:"缺少参数",301:"参数格式有误",400:"ip 被访问控制机制屏蔽",401:"同个手机发短信过于频繁",402:"from不在白名单内",403:"注册网关返回506（ip异常）",404:"登录态验证不通过",405:"修改初始密码时查不到对应记录",406:"修改初始密码时超时",407:"命令不存在",408:"签名校验不通过",409:"IP发短信过于频繁，需要提交验证码",500:"接口崩溃",600:"验证码错误",601:"注册网关返回 507（验证码错误）",700:"登录手机不存在，但安全手机存在",701:"密码太过简单",702:"邮箱已被注册",703:"手机已被注册",704:"旧帐号已被注册",3009:"验证码错误，请重新输入",4002:"操作异常，请重新登录"}}]);