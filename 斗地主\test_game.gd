# 简单的测试脚本来验证游戏逻辑
extends Node

func _ready():
	print("开始测试斗地主游戏逻辑...")
	test_card_creation()
	test_player_creation()
	test_card_manager()
	print("测试完成！")

func test_card_creation():
	print("\n=== 测试卡牌创建 ===")
	
	# 创建一张黑桃A
	var card = Card.new()
	card.suit = Card.Suit.SPADES
	card.rank = Card.Rank.ACE
	
	print("创建卡牌: %s" % card.get_card_display_text())
	print("卡牌值: %d" % card.get_card_value())
	
	# 创建大王
	var big_joker = Card.new()
	big_joker.suit = Card.Suit.JOKER
	big_joker.rank = Card.Rank.BIG_JOKER
	
	print("创建大王: %s" % big_joker.get_card_display_text())
	print("大王值: %d" % big_joker.get_card_value())

func test_player_creation():
	print("\n=== 测试玩家创建 ===")
	
	var player = Player.new()
	player.player_name = "测试玩家"
	player.player_type = Player.PlayerType.HUMAN
	
	print("创建玩家: %s" % player.player_name)
	print("玩家类型: %s" % ("人类" if player.player_type == Player.PlayerType.HUMAN else "AI"))

func test_card_manager():
	print("\n=== 测试卡牌管理器 ===")
	
	var card_manager = CardManager.new()
	add_child(card_manager)
	
	print("卡牌总数: %d" % card_manager.all_cards.size())
	
	# 测试洗牌
	card_manager.shuffle_deck()
	print("洗牌完成")
	
	# 测试卡牌组合分析
	var test_cards = []
	for i in range(2):
		if i < card_manager.deck.size():
			test_cards.append(card_manager.deck[i])
	
	var combo = card_manager.get_card_combinations(test_cards)
	print("测试组合类型: %s" % combo["type"])
