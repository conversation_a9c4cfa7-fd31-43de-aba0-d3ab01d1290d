module.exports=function(e){var n={};function t(i){if(n[i])return n[i].exports;var l=n[i]={i,l:!1,exports:{}};return e[i].call(l.exports,l,l.exports,t),l.l=!0,l.exports}return t.m=e,t.c=n,t.d=function(e,n,i){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:i})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(t.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var l in e)t.d(i,l,function(n){return e[n]}.bind(null,l));return i},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=0)}([function(e,n,t){e.exports=t(1)},function(e,n){function t(e,n=2,t=5){let i="0B";if("number"==typeof e&&e>0){let l=["B","KB","MB","GB","TB"],s=0,a=e;for(;a>=1e3&&!(s>=4);)a/=1024,s+=1;if(-1===String(a).indexOf("."))i=a+l[s];else{let e=a.toFixed(n);e.length<=t?i=("KB"===l[s]||"MB"===l[s])&&1!==n?a.toFixed(1)+l[s]:e+l[s]:("."===(e=e.substr(0,t))[t-1]&&(e=e.substr(0,t-1)),i=e+l[s])}}return i}function i(e){let n=null,t=0;do{if(!e)break;let i=localStorage.getItem(e);if(!i)break;let l=null;try{l=JSON.parse(i)}catch(e){l=null}if(l&&l.length>0){let e=l[0];e.count&&!isNaN(Number(e.count))&&(t=Number(e.count)),n=l.slice(1)}}while(0);return{count:t,items:n}}function l(e){let n="";return n=9===e.taskStatus?'\n    <div class="xly-file-info">\n      <p class="xly-file-error">&nbsp;</p>\n    </div>':`\n    <div class="xly-file-info">\n      <p class="xly-file-size">\n        <span class="xly-file-size__finish">${t(e.downloadSize)}</span><span class="xly-file-size__separator">/</span><span class="xly-file-size__total">${t(e.fileSize)}</span>\n      </p>\n    </div>\n    <div class="td-progress td-progress--line">\n      <div class="td-progress-bar">\n        <div class="td-progress-bar__outer">\n          <div class="td-progress-bar__inner" style="width: ${e.fileSize?e.downloadSize/e.fileSize*100:0}%;"></div>\n        </div>\n      </div>\n    </div>`}function s(e,n,t){let i=n?`${n}`:"",s=document.createElement("div");s.className=t?"td-tabs__item":"td-tabs__item is-active",s.innerHTML=`\n    <div>下载中<span class="xly-side__number">${i}</span></div>\n  `;let a=function(e){let n="";if(e&&e.length>0)for(let t of e)n+=`\n      <li data-index="0" data-key="0" class="td-draglist-item is-transition">\n        <div class="xly-side-item">\n          <div class="xly-side-wrapper">\n            <div class="td-media">\n              <div class="td-media__object">\n              </div>\n              <div class="td-media__content">\n                <p class="xly-file-name">\n                  ${t.taskName}\n                </p>\n                <div class="xly-side-operate">\n                  <a href="javascript:;" title="下载" class="xly-side-operate__button">\n                    <i class="xly-icon-download"></i>\n                  </a>\n                </div>\n                ${l(t)}\n              </div>\n            </div>\n          </div>\n        </div>\n      </li>`;return n}(e);var d=document.createElement("div");d.className="td-tabs__pane",d.style=t?"display: none;":"",d.innerHTML=`\n    <ul class="td-draglist" style="outline: none;">\n      ${a}\n    </ul>\n  `,document.querySelector(".td-tabs__nav").appendChild(s),document.querySelector(".td-tabs__content").appendChild(d)}function a(e,n,i){let l=n?`${n}`:"",s=document.createElement("div");s.className=i?"td-tabs__item is-active":"td-tabs__item",s.innerHTML=`\n    <div>已完成<span class="xly-side__number">${l}</span></div>\n  `;let a=function(e){let n="";if(e&&e.length>0)for(let i of e)n+=`\n      <li data-index="0" data-key="0" class="td-draglist-item is-transition">\n        <div class="xly-side-item">\n          <div class="xly-side-wrapper">\n            <div class="td-media">\n              <div class="td-media__object">\n              </div>\n              <div class="td-media__content">\n                <p class="xly-file-name">\n                  ${i.taskName}\n                </p>\n                <div class="xly-file-info">\n                  <p class="xly-file-size">\n                  <span class="xly-file-size__total">${t(i.fileSize)}</span>\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </li>`;return n}(e);var d=document.createElement("div");d.className="td-tabs__pane",d.style=i?"":"display: none;",d.innerHTML=`\n    <ul class="td-draglist" style="outline: none;">\n      ${a}\n    </ul>\n  `,document.querySelector(".td-tabs__nav").appendChild(s),document.querySelector(".td-tabs__content").appendChild(d)}!function(){!function(){let e=localStorage.getItem("skin_body_classes");document.body.classList.add(e)}();let e=i("skeleton_downloading"),n=i("skeleton_completed"),t=!1;do{if(e&&e.count>0)break;n&&n.count>0&&(t=!0)}while(0);window.__xadsSkeletonCompletedActive=t,s(e.items,e.count,t),a(n.items,n.count,t),function(){let e=document.createElement("div");e.className="td-tabs__item",e.innerHTML='\n    <div>回收站<span class="xly-side__number"></span></div>\n  ',document.querySelector(".td-tabs__nav").appendChild(e)}()}()}]);
//# sourceMappingURL=skeleton.js.map