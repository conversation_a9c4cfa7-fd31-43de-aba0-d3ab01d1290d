<!DOCTYPE html>
<html>
<head>
    <meta charset=utf-8>
    <title>迅雷</title>
    <link rel=stylesheet href=../common-style.css>
    <link href=../main-renderer/renderer.css rel=stylesheet>
</head>
<body>
<div id=app class=xly-client>
    <div class="xly-aside dragable">
        <div class="xly-aside-personal none-dragable">
            <div class=td-avatar></div>
        </div>
        <div class="xly-aside-nav none-dragable">
            <ul>
                <li class="xly-aside-nav__item is-active" style="display: flex;
            align-items: center;
            padding-left: 20px;
            height: 42px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 13px;
            transition: color 0.2s;
            cursor: pointer;"><i class=xly-icon-nav-down></i> <span>下载</span></li>
                <li class=xly-aside-nav__item style="display: flex;
            align-items: center;
            padding-left: 20px;
            height: 42px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 13px;
            transition: color 0.2s;
            cursor: pointer;"><i class=xly-icon-nav-cloud></i> <span>云盘</span></li>
            </ul>
        </div>
        <div class="xly-aside-operate none-dragable"><a href=javascript:; title=主菜单><i class=xly-icon-menu></i></a>
        </div>
    </div>
    <div class=xly-client__main>
        <div class="xly-client__header dragable">
            <div class="xly-search none-dragable">
                <button class="td-button td-button--secondary" style="display: flex;
    justify-content: center;
    margin: 0 0 0 12px;
    width: 76px;
    height: 30px;
    font-size: 13px;
    color: var(--color-default);
    background: unset;
    border-radius: 15px;
    border: solid 1px var(--color-border);
    box-sizing: border-box;"><i class=xly-icon-add></i>新建
                </button>
            </div>
        </div>
        <div class=xly-client__content>
            <div class=xly-down>
                <div class=xly-client-side>
                    <div class=xly-side>
                        <div class=td-tabs>
                            <div class=td-tabs__title>
                                <div class=td-tabs__nav></div>
                            </div>
                            <div class=td-tabs__content></div>
                        </div>
                    </div>
                    <div class=xly-status>
                        <div class=xly-status-control>
                            <div class=xly-status-plan><p class=xly-status-plan__text></p><span>下载计划</span></div>
                        </div>
                        <div class=xly-status-secret><i class=xly-img-secret></i>
                            <p class=xly-status-secret__tips>进入私人空间</p></div>
                    </div>
                </div>
                <div class=xly-down__main>
                    <div class=xly-empty><i class=xly-icon-logo></i></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src=../skeleton.js></script>
<script async>let path = require('path');
window.__rootDir = path.resolve(process.execPath, '../resources/app').replace(/\\/g, '/');
window.__profilesDir = path.resolve(__rootDir, '../../../profiles').replace(/\\/g, '/');
window.__thunderStartTime = new Date().getTime();</script>
<script async src=../vendor.js></script>
<script async src=./renderer.js></script>
</body>
</html>