module.exports=function(a){var e={};function n(i){if(e[i])return e[i].exports;var o=e[i]={i,l:!1,exports:{}};return a[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=a,n.c=e,n.d=function(a,e,i){n.o(a,e)||Object.defineProperty(a,e,{enumerable:!0,get:i})},n.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},n.t=function(a,e){if(1&e&&(a=n(a)),8&e)return a;if(4&e&&"object"==typeof a&&a&&a.__esModule)return a;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:a}),2&e&&"string"!=typeof a)for(var o in a)n.d(i,o,function(e){return a[e]}.bind(null,o));return i},n.n=function(a){var e=a&&a.__esModule?function(){return a.default}:function(){return a};return n.d(e,"a",e),e},n.o=function(a,e){return Object.prototype.hasOwnProperty.call(a,e)},n.p="",n(n.s=24)}([function(a,e,n){"use strict";var i=n(8),o=n(28),s=Object.prototype.toString;function t(a){return"[object Array]"===s.call(a)}function c(a){return null!==a&&"object"==typeof a}function r(a){return"[object Function]"===s.call(a)}function p(a,e){if(null!==a&&void 0!==a)if("object"!=typeof a&&(a=[a]),t(a))for(var n=0,i=a.length;n<i;n++)e.call(null,a[n],n,a);else for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&e.call(null,a[o],o,a)}a.exports={isArray:t,isArrayBuffer:function(a){return"[object ArrayBuffer]"===s.call(a)},isBuffer:o,isFormData:function(a){return"undefined"!=typeof FormData&&a instanceof FormData},isArrayBufferView:function(a){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(a):a&&a.buffer&&a.buffer instanceof ArrayBuffer},isString:function(a){return"string"==typeof a},isNumber:function(a){return"number"==typeof a},isObject:c,isUndefined:function(a){return void 0===a},isDate:function(a){return"[object Date]"===s.call(a)},isFile:function(a){return"[object File]"===s.call(a)},isBlob:function(a){return"[object Blob]"===s.call(a)},isFunction:r,isStream:function(a){return c(a)&&r(a.pipe)},isURLSearchParams:function(a){return"undefined"!=typeof URLSearchParams&&a instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:p,merge:function a(){var e={};function n(n,i){"object"==typeof e[i]&&"object"==typeof n?e[i]=a(e[i],n):e[i]=n}for(var i=0,o=arguments.length;i<o;i++)p(arguments[i],n);return e},extend:function(a,e,n){return p(e,function(e,o){a[o]=n&&"function"==typeof e?i(e,n):e}),a},trim:function(a){return a.replace(/^\s*/,"").replace(/\s*$/,"")}}},function(a,e){a.exports=require("util")},function(a,e,n){"use strict";var i=n(0),o=n(30),s={"Content-Type":"application/x-www-form-urlencoded"};function t(a,e){!i.isUndefined(a)&&i.isUndefined(a["Content-Type"])&&(a["Content-Type"]=e)}var c,r={adapter:("undefined"!=typeof XMLHttpRequest?c=n(31):"undefined"!=typeof process&&(c=n(36)),c),transformRequest:[function(a,e){return o(e,"Content-Type"),i.isFormData(a)||i.isArrayBuffer(a)||i.isBuffer(a)||i.isStream(a)||i.isFile(a)||i.isBlob(a)?a:i.isArrayBufferView(a)?a.buffer:i.isURLSearchParams(a)?(t(e,"application/x-www-form-urlencoded;charset=utf-8"),a.toString()):i.isObject(a)?(t(e,"application/json;charset=utf-8"),JSON.stringify(a)):a}],transformResponse:[function(a){if("string"==typeof a)try{a=JSON.parse(a)}catch(a){}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(a){return a>=200&&a<300}};r.headers={common:{Accept:"application/json, text/plain, */*"}},i.forEach(["delete","get","head"],function(a){r.headers[a]={}}),i.forEach(["post","put","patch"],function(a){r.headers[a]=i.merge(s)}),a.exports=r},function(a,e,n){"use strict";var i=n(10);a.exports=function(a,e,n,o,s){var t=new Error(a);return i(t,e,n,o,s)}},function(a,e){a.exports=require("http")},function(a,e){a.exports=require("https")},function(a,e){a.exports=require("url")},function(a,e){a.exports=require("stream")},function(a,e,n){"use strict";a.exports=function(a,e){return function(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];return a.apply(e,n)}}},function(a,e,n){"use strict";var i=n(3);a.exports=function(a,e,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?e(i("Request failed with status code "+n.status,n.config,null,n.request,n)):a(n)}},function(a,e,n){"use strict";a.exports=function(a,e,n,i,o){return a.config=e,n&&(a.code=n),a.request=i,a.response=o,a}},function(a,e,n){"use strict";var i=n(0);function o(a){return encodeURIComponent(a).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}a.exports=function(a,e,n){if(!e)return a;var s;if(n)s=n(e);else if(i.isURLSearchParams(e))s=e.toString();else{var t=[];i.forEach(e,function(a,e){null!==a&&void 0!==a&&(i.isArray(a)?e+="[]":a=[a],i.forEach(a,function(a){i.isDate(a)?a=a.toISOString():i.isObject(a)&&(a=JSON.stringify(a)),t.push(o(e)+"="+o(a))}))}),s=t.join("&")}return s&&(a+=(-1===a.indexOf("?")?"?":"&")+s),a}},function(a,e,n){var i=n(6),o=n(4),s=n(5),t=n(37),c=n(7).Writable,r=n(38)("follow-redirects"),p={GET:!0,HEAD:!0,OPTIONS:!0,TRACE:!0},l=Object.create(null);function u(a,e){c.call(this),a.headers=a.headers||{},this._options=a,this._redirectCount=0,this._requestBodyLength=0,this._requestBodyBuffers=[],e&&this.on("response",e);var n=this;if(this._onNativeResponse=function(a){n._processResponse(a)},!a.pathname&&a.path){var i=a.path.indexOf("?");i<0?a.pathname=a.path:(a.pathname=a.path.substring(0,i),a.search=a.path.substring(i))}this._performRequest()}function d(a){var e={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(a).forEach(function(o){var s=o+":",c=n[s]=a[o],p=e[o]=Object.create(c);p.request=function(a,o){return"string"==typeof a?(a=i.parse(a)).maxRedirects=e.maxRedirects:a=Object.assign({protocol:s,maxRedirects:e.maxRedirects,maxBodyLength:e.maxBodyLength},a),a.nativeProtocols=n,t.equal(a.protocol,s,"protocol mismatch"),r("options",a),new u(a,o)},p.get=function(a,e){var n=p.request(a,e);return n.end(),n}}),e}["abort","aborted","error","socket","timeout"].forEach(function(a){l[a]=function(e){this._redirectable.emit(a,e)}}),u.prototype=Object.create(c.prototype),u.prototype.write=function(a,e,n){this._requestBodyLength+a.length<=this._options.maxBodyLength?(this._requestBodyLength+=a.length,this._requestBodyBuffers.push({data:a,encoding:e}),this._currentRequest.write(a,e,n)):(this.emit("error",new Error("Request body larger than maxBodyLength limit")),this.abort())},u.prototype.end=function(a,e,n){var i=this._currentRequest;a?this.write(a,e,function(){i.end(null,null,n)}):i.end(null,null,n)},u.prototype.setHeader=function(a,e){this._options.headers[a]=e,this._currentRequest.setHeader(a,e)},u.prototype.removeHeader=function(a){delete this._options.headers[a],this._currentRequest.removeHeader(a)},["abort","flushHeaders","getHeader","setNoDelay","setSocketKeepAlive","setTimeout"].forEach(function(a){u.prototype[a]=function(e,n){return this._currentRequest[a](e,n)}}),["aborted","connection","socket"].forEach(function(a){Object.defineProperty(u.prototype,a,{get:function(){return this._currentRequest[a]}})}),u.prototype._performRequest=function(){var a=this._options.protocol,e=this._options.nativeProtocols[a];if(this._options.agents){var n=a.substr(0,a.length-1);this._options.agent=this._options.agents[n]}var o=this._currentRequest=e.request(this._options,this._onNativeResponse);for(var s in this._currentUrl=i.format(this._options),o._redirectable=this,l)s&&o.on(s,l[s]);if(this._isRedirect){var t=this._requestBodyBuffers;!function a(){if(0!==t.length){var e=t.pop();o.write(e.data,e.encoding,a)}else o.end()}()}},u.prototype._processResponse=function(a){var e=a.headers.location;if(e&&!1!==this._options.followRedirects&&a.statusCode>=300&&a.statusCode<400){if(++this._redirectCount>this._options.maxRedirects)return void this.emit("error",new Error("Max redirects exceeded."));var n,o=this._options.headers;if(307!==a.statusCode&&!(this._options.method in p))for(n in this._options.method="GET",this._requestBodyBuffers=[],o)/^content-/i.test(n)&&delete o[n];if(!this._isRedirect)for(n in o)/^host$/i.test(n)&&delete o[n];var s=i.resolve(this._currentUrl,e);r("redirecting to",s),Object.assign(this._options,i.parse(s)),this._isRedirect=!0,this._performRequest()}else a.responseUrl=this._currentUrl,this.emit("response",a),this._requestBodyBuffers=[]},a.exports=d({http:o,https:s}),a.exports.wrap=d},function(a,e,n){function i(a){var n;function i(){if(i.enabled){var a=i,o=+new Date,s=o-(n||o);a.diff=s,a.prev=n,a.curr=o,n=o;for(var t=new Array(arguments.length),c=0;c<t.length;c++)t[c]=arguments[c];t[0]=e.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");var r=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,function(n,i){if("%%"===n)return n;r++;var o=e.formatters[i];if("function"==typeof o){var s=t[r];n=o.call(a,s),t.splice(r,1),r--}return n}),e.formatArgs.call(a,t),(i.log||e.log||console.log.bind(console)).apply(a,t)}}return i.namespace=a,i.enabled=e.enabled(a),i.useColors=e.useColors(),i.color=function(a){var n,i=0;for(n in a)i=(i<<5)-i+a.charCodeAt(n),i|=0;return e.colors[Math.abs(i)%e.colors.length]}(a),i.destroy=o,"function"==typeof e.init&&e.init(i),e.instances.push(i),i}function o(){var a=e.instances.indexOf(this);return-1!==a&&(e.instances.splice(a,1),!0)}(e=a.exports=i.debug=i.default=i).coerce=function(a){return a instanceof Error?a.stack||a.message:a},e.disable=function(){e.enable("")},e.enable=function(a){var n;e.save(a),e.names=[],e.skips=[];var i=("string"==typeof a?a:"").split(/[\s,]+/),o=i.length;for(n=0;n<o;n++)i[n]&&("-"===(a=i[n].replace(/\*/g,".*?"))[0]?e.skips.push(new RegExp("^"+a.substr(1)+"$")):e.names.push(new RegExp("^"+a+"$")));for(n=0;n<e.instances.length;n++){var s=e.instances[n];s.enabled=e.enabled(s.namespace)}},e.enabled=function(a){if("*"===a[a.length-1])return!0;var n,i;for(n=0,i=e.skips.length;n<i;n++)if(e.skips[n].test(a))return!1;for(n=0,i=e.names.length;n<i;n++)if(e.names[n].test(a))return!0;return!1},e.humanize=n(40),e.instances=[],e.names=[],e.skips=[],e.formatters={}},function(a,e,n){"use strict";a.exports=function(a){return!(!a||!a.__CANCEL__)}},function(a,e,n){"use strict";function i(a){this.message=a}i.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},i.prototype.__CANCEL__=!0,a.exports=i},function(a,e){a.exports=require("fs")},function(a,e){a.exports=require("path")},function(a,e,n){var i=n(19),o=n(20);a.exports=function(a,e,n,s){var t=n.keyedList?n.keyedList[n.index]:n.index;n.jobs[t]=function(a,e,n,o){var s;s=2==a.length?a(n,i(o)):a(n,e,i(o));return s}(e,t,a[t],function(a,e){t in n.jobs&&(delete n.jobs[t],a?o(n):n.results[t]=e,s(a,n.results))})}},function(a,e,n){var i=n(63);a.exports=function(a){var e=!1;return i(function(){e=!0}),function(n,o){e?a(n,o):i(function(){a(n,o)})}}},function(a,e){a.exports=function(a){Object.keys(a.jobs).forEach(function(a){"function"==typeof this.jobs[a]&&this.jobs[a]()}.bind(a)),a.jobs={}}},function(a,e){a.exports=function(a,e){var n=!Array.isArray(a),i={index:0,keyedList:n||e?Object.keys(a):null,jobs:{},results:n?{}:[],size:n?Object.keys(a).length:a.length};e&&i.keyedList.sort(n?e:function(n,i){return e(a[n],a[i])});return i}},function(a,e,n){var i=n(20),o=n(19);a.exports=function(a){if(!Object.keys(this.jobs).length)return;this.index=this.size,i(this),o(a)(null,this.results)}},function(a,e,n){var i=n(18),o=n(21),s=n(22);function t(a,e){return a<e?-1:a>e?1:0}a.exports=function(a,e,n,t){var c=o(a,n);return i(a,e,c,function n(o,s){o?t(o,s):(c.index++,c.index<(c.keyedList||a).length?i(a,e,c,n):t(null,c.results))}),s.bind(c,t)},a.exports.ascending=t,a.exports.descending=function(a,e){return-1*t(a,e)}},function(a,e,n){a.exports=n(25)},function(a,e,n){process.on("message",function(a){const e=n(26).default,i=n(16);var o=new(n(55));Object.keys(a.params).forEach(e=>{o.append(e,a.params[e])}),o.append("file",i.createReadStream(a.filePath));let s={"User-Agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Mobile Safari/537.36","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"cross-site","sec-fetch-dest":"empty"};o.getLength(async function(n,i){n||(s["Content-Length"]=i,s["Content-Type"]=`multipart/form-data; boundary=${o.getBoundary()}`,e.post(a.postUrl,o,{headers:s,maxContentLength:1/0}).then(a=>{process.send(!0)}).catch(a=>{process.send(!1)}))})})},function(a,e,n){a.exports=n(27)},function(a,e,n){"use strict";var i=n(0),o=n(8),s=n(29),t=n(2);function c(a){var e=new s(a),n=o(s.prototype.request,e);return i.extend(n,s.prototype,e),i.extend(n,e),n}var r=c(t);r.Axios=s,r.create=function(a){return c(i.merge(t,a))},r.Cancel=n(15),r.CancelToken=n(53),r.isCancel=n(14),r.all=function(a){return Promise.all(a)},r.spread=n(54),a.exports=r,a.exports.default=r},function(a,e){function n(a){return!!a.constructor&&"function"==typeof a.constructor.isBuffer&&a.constructor.isBuffer(a)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
a.exports=function(a){return null!=a&&(n(a)||function(a){return"function"==typeof a.readFloatLE&&"function"==typeof a.slice&&n(a.slice(0,0))}(a)||!!a._isBuffer)}},function(a,e,n){"use strict";var i=n(2),o=n(0),s=n(48),t=n(49);function c(a){this.defaults=a,this.interceptors={request:new s,response:new s}}c.prototype.request=function(a){"string"==typeof a&&(a=o.merge({url:arguments[0]},arguments[1])),(a=o.merge(i,{method:"get"},this.defaults,a)).method=a.method.toLowerCase();var e=[t,void 0],n=Promise.resolve(a);for(this.interceptors.request.forEach(function(a){e.unshift(a.fulfilled,a.rejected)}),this.interceptors.response.forEach(function(a){e.push(a.fulfilled,a.rejected)});e.length;)n=n.then(e.shift(),e.shift());return n},o.forEach(["delete","get","head","options"],function(a){c.prototype[a]=function(e,n){return this.request(o.merge(n||{},{method:a,url:e}))}}),o.forEach(["post","put","patch"],function(a){c.prototype[a]=function(e,n,i){return this.request(o.merge(i||{},{method:a,url:e,data:n}))}}),a.exports=c},function(a,e,n){"use strict";var i=n(0);a.exports=function(a,e){i.forEach(a,function(n,i){i!==e&&i.toUpperCase()===e.toUpperCase()&&(a[e]=n,delete a[i])})}},function(a,e,n){"use strict";var i=n(0),o=n(9),s=n(11),t=n(32),c=n(33),r=n(3),p="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(34);a.exports=function(a){return new Promise(function(e,l){var u=a.data,d=a.headers;i.isFormData(u)&&delete d["Content-Type"];var m=new XMLHttpRequest,x="onreadystatechange",v=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in m||c(a.url)||(m=new window.XDomainRequest,x="onload",v=!0,m.onprogress=function(){},m.ontimeout=function(){}),a.auth){var f=a.auth.username||"",h=a.auth.password||"";d.Authorization="Basic "+p(f+":"+h)}if(m.open(a.method.toUpperCase(),s(a.url,a.params,a.paramsSerializer),!0),m.timeout=a.timeout,m[x]=function(){if(m&&(4===m.readyState||v)&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in m?t(m.getAllResponseHeaders()):null,i={data:a.responseType&&"text"!==a.responseType?m.response:m.responseText,status:1223===m.status?204:m.status,statusText:1223===m.status?"No Content":m.statusText,headers:n,config:a,request:m};o(e,l,i),m=null}},m.onerror=function(){l(r("Network Error",a,null,m)),m=null},m.ontimeout=function(){l(r("timeout of "+a.timeout+"ms exceeded",a,"ECONNABORTED",m)),m=null},i.isStandardBrowserEnv()){var g=n(35),b=(a.withCredentials||c(a.url))&&a.xsrfCookieName?g.read(a.xsrfCookieName):void 0;b&&(d[a.xsrfHeaderName]=b)}if("setRequestHeader"in m&&i.forEach(d,function(a,e){void 0===u&&"content-type"===e.toLowerCase()?delete d[e]:m.setRequestHeader(e,a)}),a.withCredentials&&(m.withCredentials=!0),a.responseType)try{m.responseType=a.responseType}catch(e){if("json"!==a.responseType)throw e}"function"==typeof a.onDownloadProgress&&m.addEventListener("progress",a.onDownloadProgress),"function"==typeof a.onUploadProgress&&m.upload&&m.upload.addEventListener("progress",a.onUploadProgress),a.cancelToken&&a.cancelToken.promise.then(function(a){m&&(m.abort(),l(a),m=null)}),void 0===u&&(u=null),m.send(u)})}},function(a,e,n){"use strict";var i=n(0),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];a.exports=function(a){var e,n,s,t={};return a?(i.forEach(a.split("\n"),function(a){if(s=a.indexOf(":"),e=i.trim(a.substr(0,s)).toLowerCase(),n=i.trim(a.substr(s+1)),e){if(t[e]&&o.indexOf(e)>=0)return;t[e]="set-cookie"===e?(t[e]?t[e]:[]).concat([n]):t[e]?t[e]+", "+n:n}}),t):t}},function(a,e,n){"use strict";var i=n(0);a.exports=i.isStandardBrowserEnv()?function(){var a,e=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function o(a){var i=a;return e&&(n.setAttribute("href",i),i=n.href),n.setAttribute("href",i),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return a=o(window.location.href),function(e){var n=i.isString(e)?o(e):e;return n.protocol===a.protocol&&n.host===a.host}}():function(){return!0}},function(a,e,n){"use strict";var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function o(){this.message="String contains an invalid character"}o.prototype=new Error,o.prototype.code=5,o.prototype.name="InvalidCharacterError",a.exports=function(a){for(var e,n,s=String(a),t="",c=0,r=i;s.charAt(0|c)||(r="=",c%1);t+=r.charAt(63&e>>8-c%1*8)){if((n=s.charCodeAt(c+=.75))>255)throw new o;e=e<<8|n}return t}},function(a,e,n){"use strict";var i=n(0);a.exports=i.isStandardBrowserEnv()?{write:function(a,e,n,o,s,t){var c=[];c.push(a+"="+encodeURIComponent(e)),i.isNumber(n)&&c.push("expires="+new Date(n).toGMTString()),i.isString(o)&&c.push("path="+o),i.isString(s)&&c.push("domain="+s),!0===t&&c.push("secure"),document.cookie=c.join("; ")},read:function(a){var e=document.cookie.match(new RegExp("(^|;\\s*)("+a+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove:function(a){this.write(a,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(a,e,n){"use strict";var i=n(0),o=n(9),s=n(11),t=n(4),c=n(5),r=n(12).http,p=n(12).https,l=n(6),u=n(46),d=n(47),m=n(3),x=n(10);a.exports=function(a){return new Promise(function(e,n){var v,f=a.data,h=a.headers;if(h["User-Agent"]||h["user-agent"]||(h["User-Agent"]="axios/"+d.version),f&&!i.isStream(f)){if(Buffer.isBuffer(f));else if(i.isArrayBuffer(f))f=new Buffer(new Uint8Array(f));else{if(!i.isString(f))return n(m("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",a));f=new Buffer(f,"utf-8")}h["Content-Length"]=f.length}var g=void 0;a.auth&&(g=(a.auth.username||"")+":"+(a.auth.password||""));var b=l.parse(a.url),y=b.protocol||"http:";if(!g&&b.auth){var w=b.auth.split(":");g=(w[0]||"")+":"+(w[1]||"")}g&&delete h.Authorization;var k="https:"===y,j=k?a.httpsAgent:a.httpAgent,_={path:s(b.path,a.params,a.paramsSerializer).replace(/^\?/,""),method:a.method,headers:h,agent:j,auth:g};a.socketPath?_.socketPath=a.socketPath:(_.hostname=b.hostname,_.port=b.port);var C,z=a.proxy;if(!z&&!1!==z){var q=y.slice(0,-1)+"_proxy",E=process.env[q]||process.env[q.toUpperCase()];if(E){var S=l.parse(E);if(z={host:S.hostname,port:S.port},S.auth){var O=S.auth.split(":");z.auth={username:O[0],password:O[1]}}}}if(z&&(_.hostname=z.host,_.host=z.host,_.headers.host=b.hostname+(b.port?":"+b.port:""),_.port=z.port,_.path=y+"//"+b.hostname+(b.port?":"+b.port:"")+_.path,z.auth)){var R=new Buffer(z.auth.username+":"+z.auth.password,"utf8").toString("base64");_.headers["Proxy-Authorization"]="Basic "+R}a.transport?C=a.transport:0===a.maxRedirects?C=k?c:t:(a.maxRedirects&&(_.maxRedirects=a.maxRedirects),C=k?p:r),a.maxContentLength&&a.maxContentLength>-1&&(_.maxBodyLength=a.maxContentLength);var B=C.request(_,function(i){if(!B.aborted){clearTimeout(v),v=null;var s=i;switch(i.headers["content-encoding"]){case"gzip":case"compress":case"deflate":s=s.pipe(u.createUnzip()),delete i.headers["content-encoding"]}var t=i.req||B,c={status:i.statusCode,statusText:i.statusMessage,headers:i.headers,config:a,request:t};if("stream"===a.responseType)c.data=s,o(e,n,c);else{var r=[];s.on("data",function(e){r.push(e),a.maxContentLength>-1&&Buffer.concat(r).length>a.maxContentLength&&n(m("maxContentLength size of "+a.maxContentLength+" exceeded",a,null,t))}),s.on("error",function(e){B.aborted||n(x(e,a,null,t))}),s.on("end",function(){var i=Buffer.concat(r);"arraybuffer"!==a.responseType&&(i=i.toString("utf8")),c.data=i,o(e,n,c)})}}});B.on("error",function(e){B.aborted||n(x(e,a,null,B))}),a.timeout&&!v&&(v=setTimeout(function(){B.abort(),n(m("timeout of "+a.timeout+"ms exceeded",a,"ECONNABORTED",B))},a.timeout)),a.cancelToken&&a.cancelToken.promise.then(function(a){B.aborted||(B.abort(),n(a))}),i.isStream(f)?f.pipe(B):B.end(f)})}},function(a,e){a.exports=require("assert")},function(a,e,n){"undefined"==typeof process||"renderer"===process.type?a.exports=n(39):a.exports=n(41)},function(a,e,n){function i(){var a;try{a=e.storage.debug}catch(a){}return!a&&"undefined"!=typeof process&&"env"in process&&(a=process.env.DEBUG),a}(e=a.exports=n(13)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},e.formatArgs=function(a){var n=this.useColors;if(a[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+a[0]+(n?"%c ":" ")+"+"+e.humanize(this.diff),!n)return;var i="color: "+this.color;a.splice(1,0,i,"color: inherit");var o=0,s=0;a[0].replace(/%[a-zA-Z%]/g,function(a){"%%"!==a&&"%c"===a&&(s=++o)}),a.splice(s,0,i)},e.save=function(a){try{null==a?e.storage.removeItem("debug"):e.storage.debug=a}catch(a){}},e.load=i,e.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},e.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(a){}}(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.formatters.j=function(a){try{return JSON.stringify(a)}catch(a){return"[UnexpectedJSONParseError]: "+a.message}},e.enable(i())},function(a,e){var n=1e3,i=60*n,o=60*i,s=24*o,t=365.25*s;function c(a,e,n){if(!(a<e))return a<1.5*e?Math.floor(a/e)+" "+n:Math.ceil(a/e)+" "+n+"s"}a.exports=function(a,e){e=e||{};var r,p=typeof a;if("string"===p&&a.length>0)return function(a){if((a=String(a)).length>100)return;var e=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(a);if(!e)return;var c=parseFloat(e[1]);switch((e[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*t;case"days":case"day":case"d":return c*s;case"hours":case"hour":case"hrs":case"hr":case"h":return c*o;case"minutes":case"minute":case"mins":case"min":case"m":return c*i;case"seconds":case"second":case"secs":case"sec":case"s":return c*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(a);if("number"===p&&!1===isNaN(a))return e.long?c(r=a,s,"day")||c(r,o,"hour")||c(r,i,"minute")||c(r,n,"second")||r+" ms":function(a){if(a>=s)return Math.round(a/s)+"d";if(a>=o)return Math.round(a/o)+"h";if(a>=i)return Math.round(a/i)+"m";if(a>=n)return Math.round(a/n)+"s";return a+"ms"}(a);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(a))}},function(a,e,n){var i=n(42),o=n(1);(e=a.exports=n(13)).init=function(a){a.inspectOpts={};for(var n=Object.keys(e.inspectOpts),i=0;i<n.length;i++)a.inspectOpts[n[i]]=e.inspectOpts[n[i]]},e.log=function(){return process.stderr.write(o.format.apply(o,arguments)+"\n")},e.formatArgs=function(a){var n=this.namespace;if(this.useColors){var i=this.color,o="[3"+(i<8?i:"8;5;"+i),s="  "+o+";1m"+n+" [0m";a[0]=s+a[0].split("\n").join("\n"+s),a.push(o+"m+"+e.humanize(this.diff)+"[0m")}else a[0]=(e.inspectOpts.hideDate?"":(new Date).toISOString()+" ")+n+" "+a[0]},e.save=function(a){null==a?delete process.env.DEBUG:process.env.DEBUG=a},e.load=t,e.useColors=function(){return"colors"in e.inspectOpts?Boolean(e.inspectOpts.colors):i.isatty(process.stderr.fd)},e.colors=[6,2,3,4,5,1];try{var s=n(43);s&&s.level>=2&&(e.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(a){}function t(){return process.env.DEBUG}e.inspectOpts=Object.keys(process.env).filter(function(a){return/^debug_/i.test(a)}).reduce(function(a,e){var n=e.substring(6).toLowerCase().replace(/_([a-z])/g,function(a,e){return e.toUpperCase()}),i=process.env[e];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),a[n]=i,a},{}),e.formatters.o=function(a){return this.inspectOpts.colors=this.useColors,o.inspect(a,this.inspectOpts).split("\n").map(function(a){return a.trim()}).join(" ")},e.formatters.O=function(a){return this.inspectOpts.colors=this.useColors,o.inspect(a,this.inspectOpts)},e.enable(t())},function(a,e){a.exports=require("tty")},function(a,e,n){"use strict";const i=n(44),o=n(45),s=process.env;let t;function c(a){return function(a){return 0!==a&&{level:a,hasBasic:!0,has256:a>=2,has16m:a>=3}}(function(a){if(!1===t)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(a&&!a.isTTY&&!0!==t)return s.VSCODE_PID?1:0;const e=t?1:0;if("win32"===process.platform){const a=i.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(a[0])>=10&&Number(a[2])>=10586?Number(a[2])>=14931?3:2:1}if("CI"in s)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(a=>a in s)||"codeship"===s.CI_NAME?1:e;if("TEAMCITY_VERSION"in s)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(s.TEAMCITY_VERSION)?1:0;if("truecolor"===s.COLORTERM)return 3;if("TERM_PROGRAM"in s){const a=parseInt((s.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(s.TERM_PROGRAM){case"iTerm.app":return a>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(s.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(s.TERM)?1:"COLORTERM"in s?1:(s.TERM,e)}(a))}o("no-color")||o("no-colors")||o("color=false")?t=!1:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(t=!0),"FORCE_COLOR"in s&&(t=0===s.FORCE_COLOR.length||0!==parseInt(s.FORCE_COLOR,10)),a.exports={supportsColor:c,stdout:c(process.stdout),stderr:c(process.stderr)}},function(a,e){a.exports=require("os")},function(a,e,n){"use strict";a.exports=((a,e)=>{e=e||process.argv;const n=a.startsWith("-")?"":1===a.length?"-":"--",i=e.indexOf(n+a),o=e.indexOf("--");return-1!==i&&(-1===o||i<o)})},function(a,e){a.exports=require("zlib")},function(a){a.exports={_args:[["axios@0.18.0","D:\\jenkins\\workspace\\ThunderPC_AP_Build_Pack\\channel\\trunk\\build\\app"]],_from:"axios@0.18.0",_id:"axios@0.18.0",_inBundle:!1,_integrity:"sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=",_location:"/axios",_phantomChildren:{},_requested:{type:"version",registry:!0,raw:"axios@0.18.0",name:"axios",escapedName:"axios",rawSpec:"0.18.0",saveSpec:null,fetchSpec:"0.18.0"},_requiredBy:["/","/@types/axios","/@xunlei/thunderx-login-main"],_resolved:"http://xnpm.repo.xunlei.cn/axios/-/axios-0.18.0.tgz",_spec:"0.18.0",_where:"D:\\jenkins\\workspace\\ThunderPC_AP_Build_Pack\\channel\\trunk\\build\\app",author:{name:"Matt Zabriskie"},browser:{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},bugs:{url:"https://github.com/axios/axios/issues"},bundlesize:[{path:"./dist/axios.min.js",threshold:"5kB"}],dependencies:{"follow-redirects":"^1.3.0","is-buffer":"^1.1.5"},description:"Promise based HTTP client for the browser and node.js",devDependencies:{bundlesize:"^0.5.7",coveralls:"^2.11.9","es6-promise":"^4.0.5",grunt:"^1.0.1","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.0.0","grunt-contrib-nodeunit":"^1.0.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^19.0.0","grunt-karma":"^2.0.0","grunt-ts":"^6.0.0-beta.3","grunt-webpack":"^1.0.18","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1",karma:"^1.3.0","karma-chrome-launcher":"^2.0.0","karma-coverage":"^1.0.0","karma-firefox-launcher":"^1.0.0","karma-jasmine":"^1.0.2","karma-jasmine-ajax":"^0.1.13","karma-opera-launcher":"^1.0.0","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^1.1.0","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.7","karma-webpack":"^1.7.0","load-grunt-tasks":"^3.5.2",minimist:"^1.2.0",sinon:"^1.17.4",typescript:"^2.0.3","url-search-params":"^0.6.1",webpack:"^1.13.1","webpack-dev-server":"^1.14.1"},homepage:"https://github.com/axios/axios",keywords:["xhr","http","ajax","promise","node"],license:"MIT",main:"index.js",name:"axios",repository:{type:"git",url:"git+https://github.com/axios/axios.git"},scripts:{build:"NODE_ENV=production grunt build",coveralls:"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",examples:"node ./examples/server.js",postversion:"git push && git push --tags",preversion:"npm test",start:"node ./sandbox/server.js",test:"grunt test && bundlesize",version:"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json"},typings:"./index.d.ts",version:"0.18.0"}},function(a,e,n){"use strict";var i=n(0);function o(){this.handlers=[]}o.prototype.use=function(a,e){return this.handlers.push({fulfilled:a,rejected:e}),this.handlers.length-1},o.prototype.eject=function(a){this.handlers[a]&&(this.handlers[a]=null)},o.prototype.forEach=function(a){i.forEach(this.handlers,function(e){null!==e&&a(e)})},a.exports=o},function(a,e,n){"use strict";var i=n(0),o=n(50),s=n(14),t=n(2),c=n(51),r=n(52);function p(a){a.cancelToken&&a.cancelToken.throwIfRequested()}a.exports=function(a){return p(a),a.baseURL&&!c(a.url)&&(a.url=r(a.baseURL,a.url)),a.headers=a.headers||{},a.data=o(a.data,a.headers,a.transformRequest),a.headers=i.merge(a.headers.common||{},a.headers[a.method]||{},a.headers||{}),i.forEach(["delete","get","head","post","put","patch","common"],function(e){delete a.headers[e]}),(a.adapter||t.adapter)(a).then(function(e){return p(a),e.data=o(e.data,e.headers,a.transformResponse),e},function(e){return s(e)||(p(a),e&&e.response&&(e.response.data=o(e.response.data,e.response.headers,a.transformResponse))),Promise.reject(e)})}},function(a,e,n){"use strict";var i=n(0);a.exports=function(a,e,n){return i.forEach(n,function(n){a=n(a,e)}),a}},function(a,e,n){"use strict";a.exports=function(a){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(a)}},function(a,e,n){"use strict";a.exports=function(a,e){return e?a.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):a}},function(a,e,n){"use strict";var i=n(15);function o(a){if("function"!=typeof a)throw new TypeError("executor must be a function.");var e;this.promise=new Promise(function(a){e=a});var n=this;a(function(a){n.reason||(n.reason=new i(a),e(n.reason))})}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var a;return{token:new o(function(e){a=e}),cancel:a}},a.exports=o},function(a,e,n){"use strict";a.exports=function(a){return function(e){return a.apply(null,e)}}},function(a,e,n){var i=n(56),o=n(1),s=n(17),t=n(4),c=n(5),r=n(6).parse,p=n(16),l=n(58),u=n(61),d=n(65);function m(a){if(!(this instanceof m))return new m(a);for(var e in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],i.call(this),a=a||{})this[e]=a[e]}a.exports=m,o.inherits(m,i),m.LINE_BREAK="\r\n",m.DEFAULT_CONTENT_TYPE="application/octet-stream",m.prototype.append=function(a,e,n){"string"==typeof(n=n||{})&&(n={filename:n});var s=i.prototype.append.bind(this);if("number"==typeof e&&(e=""+e),o.isArray(e))this._error(new Error("Arrays are not supported."));else{var t=this._multiPartHeader(a,e,n),c=this._multiPartFooter();s(t),s(e),s(c),this._trackLength(t,e,n)}},m.prototype._trackLength=function(a,e,n){var i=0;null!=n.knownLength?i+=+n.knownLength:Buffer.isBuffer(e)?i=e.length:"string"==typeof e&&(i=Buffer.byteLength(e)),this._valueLength+=i,this._overheadLength+=Buffer.byteLength(a)+m.LINE_BREAK.length,e&&(e.path||e.readable&&e.hasOwnProperty("httpVersion"))&&(n.knownLength||this._valuesToMeasure.push(e))},m.prototype._lengthRetriever=function(a,e){a.hasOwnProperty("fd")?void 0!=a.end&&a.end!=1/0&&void 0!=a.start?e(null,a.end+1-(a.start?a.start:0)):p.stat(a.path,function(n,i){var o;n?e(n):(o=i.size-(a.start?a.start:0),e(null,o))}):a.hasOwnProperty("httpVersion")?e(null,+a.headers["content-length"]):a.hasOwnProperty("httpModule")?(a.on("response",function(n){a.pause(),e(null,+n.headers["content-length"])}),a.resume()):e("Unknown stream")},m.prototype._multiPartHeader=function(a,e,n){if("string"==typeof n.header)return n.header;var i,o=this._getContentDisposition(e,n),s=this._getContentType(e,n),t="",c={"Content-Disposition":["form-data",'name="'+a+'"'].concat(o||[]),"Content-Type":[].concat(s||[])};for(var r in"object"==typeof n.header&&d(c,n.header),c)c.hasOwnProperty(r)&&null!=(i=c[r])&&(Array.isArray(i)||(i=[i]),i.length&&(t+=r+": "+i.join("; ")+m.LINE_BREAK));return"--"+this.getBoundary()+m.LINE_BREAK+t+m.LINE_BREAK},m.prototype._getContentDisposition=function(a,e){var n,i;return"string"==typeof e.filepath?n=s.normalize(e.filepath).replace(/\\/g,"/"):e.filename||a.name||a.path?n=s.basename(e.filename||a.name||a.path):a.readable&&a.hasOwnProperty("httpVersion")&&(n=s.basename(a.client._httpMessage.path||"")),n&&(i='filename="'+n+'"'),i},m.prototype._getContentType=function(a,e){var n=e.contentType;return!n&&a.name&&(n=l.lookup(a.name)),!n&&a.path&&(n=l.lookup(a.path)),!n&&a.readable&&a.hasOwnProperty("httpVersion")&&(n=a.headers["content-type"]),n||!e.filepath&&!e.filename||(n=l.lookup(e.filepath||e.filename)),n||"object"!=typeof a||(n=m.DEFAULT_CONTENT_TYPE),n},m.prototype._multiPartFooter=function(){return function(a){var e=m.LINE_BREAK;0===this._streams.length&&(e+=this._lastBoundary()),a(e)}.bind(this)},m.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+m.LINE_BREAK},m.prototype.getHeaders=function(a){var e,n={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(e in a)a.hasOwnProperty(e)&&(n[e.toLowerCase()]=a[e]);return n},m.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},m.prototype.getBuffer=function(){for(var a=new Buffer.alloc(0),e=this.getBoundary(),n=0,i=this._streams.length;n<i;n++)"function"!=typeof this._streams[n]&&(a=Buffer.isBuffer(this._streams[n])?Buffer.concat([a,this._streams[n]]):Buffer.concat([a,Buffer.from(this._streams[n])]),"string"==typeof this._streams[n]&&this._streams[n].substring(2,e.length+2)===e||(a=Buffer.concat([a,Buffer.from(m.LINE_BREAK)])));return Buffer.concat([a,Buffer.from(this._lastBoundary())])},m.prototype._generateBoundary=function(){for(var a="--------------------------",e=0;e<24;e++)a+=Math.floor(10*Math.random()).toString(16);this._boundary=a},m.prototype.getLengthSync=function(){var a=this._overheadLength+this._valueLength;return this._streams.length&&(a+=this._lastBoundary().length),this.hasKnownLength()||this._error(new Error("Cannot calculate proper length in synchronous way.")),a},m.prototype.hasKnownLength=function(){var a=!0;return this._valuesToMeasure.length&&(a=!1),a},m.prototype.getLength=function(a){var e=this._overheadLength+this._valueLength;this._streams.length&&(e+=this._lastBoundary().length),this._valuesToMeasure.length?u.parallel(this._valuesToMeasure,this._lengthRetriever,function(n,i){n?a(n):(i.forEach(function(a){e+=a}),a(null,e))}):process.nextTick(a.bind(this,null,e))},m.prototype.submit=function(a,e){var n,i,o={method:"post"};return"string"==typeof a?(a=r(a),i=d({port:a.port,path:a.pathname,host:a.hostname,protocol:a.protocol},o)):(i=d(a,o)).port||(i.port="https:"==i.protocol?443:80),i.headers=this.getHeaders(a.headers),n="https:"==i.protocol?c.request(i):t.request(i),this.getLength(function(a,i){if(a)this._error(a);else if(n.setHeader("Content-Length",i),this.pipe(n),e){var o,s=function(a,i){return n.removeListener("error",s),n.removeListener("response",o),e.call(this,a,i)};o=s.bind(this,null),n.on("error",s),n.on("response",o)}}.bind(this)),n},m.prototype._error=function(a){this.error||(this.error=a,this.pause(),this.emit("error",a))},m.prototype.toString=function(){return"[object FormData]"}},function(a,e,n){var i=n(1),o=n(7).Stream,s=n(57);function t(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}a.exports=t,i.inherits(t,o),t.create=function(a){var e=new this;for(var n in a=a||{})e[n]=a[n];return e},t.isStreamLike=function(a){return"function"!=typeof a&&"string"!=typeof a&&"boolean"!=typeof a&&"number"!=typeof a&&!Buffer.isBuffer(a)},t.prototype.append=function(a){if(t.isStreamLike(a)){if(!(a instanceof s)){var e=s.create(a,{maxDataSize:1/0,pauseStream:this.pauseStreams});a.on("data",this._checkDataSize.bind(this)),a=e}this._handleErrors(a),this.pauseStreams&&a.pause()}return this._streams.push(a),this},t.prototype.pipe=function(a,e){return o.prototype.pipe.call(this,a,e),this.resume(),a},t.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop)this._pendingNext=!0;else{this._insideLoop=!0;try{do{this._pendingNext=!1,this._realGetNext()}while(this._pendingNext)}finally{this._insideLoop=!1}}},t.prototype._realGetNext=function(){var a=this._streams.shift();void 0!==a?"function"==typeof a?a(function(a){t.isStreamLike(a)&&(a.on("data",this._checkDataSize.bind(this)),this._handleErrors(a)),this._pipeNext(a)}.bind(this)):this._pipeNext(a):this.end()},t.prototype._pipeNext=function(a){if(this._currentStream=a,t.isStreamLike(a))return a.on("end",this._getNext.bind(this)),void a.pipe(this,{end:!1});var e=a;this.write(e),this._getNext()},t.prototype._handleErrors=function(a){var e=this;a.on("error",function(a){e._emitError(a)})},t.prototype.write=function(a){this.emit("data",a)},t.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},t.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},t.prototype.end=function(){this._reset(),this.emit("end")},t.prototype.destroy=function(){this._reset(),this.emit("close")},t.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},t.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var a="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(new Error(a))}},t.prototype._updateDataSize=function(){this.dataSize=0;var a=this;this._streams.forEach(function(e){e.dataSize&&(a.dataSize+=e.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},t.prototype._emitError=function(a){this._reset(),this.emit("error",a)}},function(a,e,n){var i=n(7).Stream,o=n(1);function s(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}a.exports=s,o.inherits(s,i),s.create=function(a,e){var n=new this;for(var i in e=e||{})n[i]=e[i];n.source=a;var o=a.emit;return a.emit=function(){return n._handleEmit(arguments),o.apply(a,arguments)},a.on("error",function(){}),n.pauseStream&&a.pause(),n},Object.defineProperty(s.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),s.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},s.prototype.resume=function(){this._released||this.release(),this.source.resume()},s.prototype.pause=function(){this.source.pause()},s.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach(function(a){this.emit.apply(this,a)}.bind(this)),this._bufferedEvents=[]},s.prototype.pipe=function(){var a=i.prototype.pipe.apply(this,arguments);return this.resume(),a},s.prototype._handleEmit=function(a){this._released?this.emit.apply(this,a):("data"===a[0]&&(this.dataSize+=a[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(a))},s.prototype._checkIfMaxDataSizeExceeded=function(){if(!(this._maxDataSizeExceeded||this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var a="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",new Error(a))}}},function(a,e,n){"use strict";
/*!
 * mime-types
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var i,o,s,t=n(59),c=n(17).extname,r=/^\s*([^;\s]*)(?:;|\s|$)/,p=/^text\//i;function l(a){if(!a||"string"!=typeof a)return!1;var e=r.exec(a),n=e&&t[e[1].toLowerCase()];return n&&n.charset?n.charset:!(!e||!p.test(e[1]))&&"UTF-8"}e.charset=l,e.charsets={lookup:l},e.contentType=function(a){if(!a||"string"!=typeof a)return!1;var n=-1===a.indexOf("/")?e.lookup(a):a;if(!n)return!1;if(-1===n.indexOf("charset")){var i=e.charset(n);i&&(n+="; charset="+i.toLowerCase())}return n},e.extension=function(a){if(!a||"string"!=typeof a)return!1;var n=r.exec(a),i=n&&e.extensions[n[1].toLowerCase()];if(!i||!i.length)return!1;return i[0]},e.extensions=Object.create(null),e.lookup=function(a){if(!a||"string"!=typeof a)return!1;var n=c("x."+a).toLowerCase().substr(1);if(!n)return!1;return e.types[n]||!1},e.types=Object.create(null),i=e.extensions,o=e.types,s=["nginx","apache",void 0,"iana"],Object.keys(t).forEach(function(a){var e=t[a],n=e.extensions;if(n&&n.length){i[a]=n;for(var c=0;c<n.length;c++){var r=n[c];if(o[r]){var p=s.indexOf(t[o[r]].source),l=s.indexOf(e.source);if("application/octet-stream"!==o[r]&&(p>l||p===l&&"application/"===o[r].substr(0,12)))continue}o[r]=a}}})},function(a,e,n){
/*!
 * mime-db
 * Copyright(c) 2014 Jonathan Ong
 * MIT Licensed
 */
a.exports=n(60)},function(a){a.exports={"application/1d-interleaved-parityfec":{source:"iana"},"application/3gpdash-qoe-report+xml":{source:"iana"},"application/3gpp-ims+xml":{source:"iana"},"application/a2l":{source:"iana"},"application/activemessage":{source:"iana"},"application/alto-costmap+json":{source:"iana",compressible:!0},"application/alto-costmapfilter+json":{source:"iana",compressible:!0},"application/alto-directory+json":{source:"iana",compressible:!0},"application/alto-endpointcost+json":{source:"iana",compressible:!0},"application/alto-endpointcostparams+json":{source:"iana",compressible:!0},"application/alto-endpointprop+json":{source:"iana",compressible:!0},"application/alto-endpointpropparams+json":{source:"iana",compressible:!0},"application/alto-error+json":{source:"iana",compressible:!0},"application/alto-networkmap+json":{source:"iana",compressible:!0},"application/alto-networkmapfilter+json":{source:"iana",compressible:!0},"application/aml":{source:"iana"},"application/andrew-inset":{source:"iana",extensions:["ez"]},"application/applefile":{source:"iana"},"application/applixware":{source:"apache",extensions:["aw"]},"application/atf":{source:"iana"},"application/atfx":{source:"iana"},"application/atom+xml":{source:"iana",compressible:!0,extensions:["atom"]},"application/atomcat+xml":{source:"iana",extensions:["atomcat"]},"application/atomdeleted+xml":{source:"iana"},"application/atomicmail":{source:"iana"},"application/atomsvc+xml":{source:"iana",extensions:["atomsvc"]},"application/atxml":{source:"iana"},"application/auth-policy+xml":{source:"iana"},"application/bacnet-xdd+zip":{source:"iana"},"application/batch-smtp":{source:"iana"},"application/bdoc":{compressible:!1,extensions:["bdoc"]},"application/beep+xml":{source:"iana"},"application/calendar+json":{source:"iana",compressible:!0},"application/calendar+xml":{source:"iana"},"application/call-completion":{source:"iana"},"application/cals-1840":{source:"iana"},"application/cbor":{source:"iana"},"application/cccex":{source:"iana"},"application/ccmp+xml":{source:"iana"},"application/ccxml+xml":{source:"iana",extensions:["ccxml"]},"application/cdfx+xml":{source:"iana"},"application/cdmi-capability":{source:"iana",extensions:["cdmia"]},"application/cdmi-container":{source:"iana",extensions:["cdmic"]},"application/cdmi-domain":{source:"iana",extensions:["cdmid"]},"application/cdmi-object":{source:"iana",extensions:["cdmio"]},"application/cdmi-queue":{source:"iana",extensions:["cdmiq"]},"application/cdni":{source:"iana"},"application/cea":{source:"iana"},"application/cea-2018+xml":{source:"iana"},"application/cellml+xml":{source:"iana"},"application/cfw":{source:"iana"},"application/clue_info+xml":{source:"iana"},"application/cms":{source:"iana"},"application/cnrp+xml":{source:"iana"},"application/coap-group+json":{source:"iana",compressible:!0},"application/coap-payload":{source:"iana"},"application/commonground":{source:"iana"},"application/conference-info+xml":{source:"iana"},"application/cose":{source:"iana"},"application/cose-key":{source:"iana"},"application/cose-key-set":{source:"iana"},"application/cpl+xml":{source:"iana"},"application/csrattrs":{source:"iana"},"application/csta+xml":{source:"iana"},"application/cstadata+xml":{source:"iana"},"application/csvm+json":{source:"iana",compressible:!0},"application/cu-seeme":{source:"apache",extensions:["cu"]},"application/cybercash":{source:"iana"},"application/dart":{compressible:!0},"application/dash+xml":{source:"iana",extensions:["mpd"]},"application/dashdelta":{source:"iana"},"application/davmount+xml":{source:"iana",extensions:["davmount"]},"application/dca-rft":{source:"iana"},"application/dcd":{source:"iana"},"application/dec-dx":{source:"iana"},"application/dialog-info+xml":{source:"iana"},"application/dicom":{source:"iana"},"application/dicom+json":{source:"iana",compressible:!0},"application/dicom+xml":{source:"iana"},"application/dii":{source:"iana"},"application/dit":{source:"iana"},"application/dns":{source:"iana"},"application/docbook+xml":{source:"apache",extensions:["dbk"]},"application/dskpp+xml":{source:"iana"},"application/dssc+der":{source:"iana",extensions:["dssc"]},"application/dssc+xml":{source:"iana",extensions:["xdssc"]},"application/dvcs":{source:"iana"},"application/ecmascript":{source:"iana",compressible:!0,extensions:["ecma"]},"application/edi-consent":{source:"iana"},"application/edi-x12":{source:"iana",compressible:!1},"application/edifact":{source:"iana",compressible:!1},"application/efi":{source:"iana"},"application/emergencycalldata.comment+xml":{source:"iana"},"application/emergencycalldata.control+xml":{source:"iana"},"application/emergencycalldata.deviceinfo+xml":{source:"iana"},"application/emergencycalldata.ecall.msd":{source:"iana"},"application/emergencycalldata.providerinfo+xml":{source:"iana"},"application/emergencycalldata.serviceinfo+xml":{source:"iana"},"application/emergencycalldata.subscriberinfo+xml":{source:"iana"},"application/emergencycalldata.veds+xml":{source:"iana"},"application/emma+xml":{source:"iana",extensions:["emma"]},"application/emotionml+xml":{source:"iana"},"application/encaprtp":{source:"iana"},"application/epp+xml":{source:"iana"},"application/epub+zip":{source:"iana",extensions:["epub"]},"application/eshop":{source:"iana"},"application/exi":{source:"iana",extensions:["exi"]},"application/fastinfoset":{source:"iana"},"application/fastsoap":{source:"iana"},"application/fdt+xml":{source:"iana"},"application/fhir+xml":{source:"iana"},"application/fido.trusted-apps+json":{compressible:!0},"application/fits":{source:"iana"},"application/font-sfnt":{source:"iana"},"application/font-tdpfr":{source:"iana",extensions:["pfr"]},"application/font-woff":{source:"iana",compressible:!1,extensions:["woff"]},"application/framework-attributes+xml":{source:"iana"},"application/geo+json":{source:"iana",compressible:!0,extensions:["geojson"]},"application/geo+json-seq":{source:"iana"},"application/geoxacml+xml":{source:"iana"},"application/gml+xml":{source:"iana",extensions:["gml"]},"application/gpx+xml":{source:"apache",extensions:["gpx"]},"application/gxf":{source:"apache",extensions:["gxf"]},"application/gzip":{source:"iana",compressible:!1,extensions:["gz"]},"application/h224":{source:"iana"},"application/held+xml":{source:"iana"},"application/hjson":{extensions:["hjson"]},"application/http":{source:"iana"},"application/hyperstudio":{source:"iana",extensions:["stk"]},"application/ibe-key-request+xml":{source:"iana"},"application/ibe-pkg-reply+xml":{source:"iana"},"application/ibe-pp-data":{source:"iana"},"application/iges":{source:"iana"},"application/im-iscomposing+xml":{source:"iana"},"application/index":{source:"iana"},"application/index.cmd":{source:"iana"},"application/index.obj":{source:"iana"},"application/index.response":{source:"iana"},"application/index.vnd":{source:"iana"},"application/inkml+xml":{source:"iana",extensions:["ink","inkml"]},"application/iotp":{source:"iana"},"application/ipfix":{source:"iana",extensions:["ipfix"]},"application/ipp":{source:"iana"},"application/isup":{source:"iana"},"application/its+xml":{source:"iana"},"application/java-archive":{source:"apache",compressible:!1,extensions:["jar","war","ear"]},"application/java-serialized-object":{source:"apache",compressible:!1,extensions:["ser"]},"application/java-vm":{source:"apache",compressible:!1,extensions:["class"]},"application/javascript":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["js","mjs"]},"application/jf2feed+json":{source:"iana",compressible:!0},"application/jose":{source:"iana"},"application/jose+json":{source:"iana",compressible:!0},"application/jrd+json":{source:"iana",compressible:!0},"application/json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["json","map"]},"application/json-patch+json":{source:"iana",compressible:!0},"application/json-seq":{source:"iana"},"application/json5":{extensions:["json5"]},"application/jsonml+json":{source:"apache",compressible:!0,extensions:["jsonml"]},"application/jwk+json":{source:"iana",compressible:!0},"application/jwk-set+json":{source:"iana",compressible:!0},"application/jwt":{source:"iana"},"application/kpml-request+xml":{source:"iana"},"application/kpml-response+xml":{source:"iana"},"application/ld+json":{source:"iana",compressible:!0,extensions:["jsonld"]},"application/lgr+xml":{source:"iana"},"application/link-format":{source:"iana"},"application/load-control+xml":{source:"iana"},"application/lost+xml":{source:"iana",extensions:["lostxml"]},"application/lostsync+xml":{source:"iana"},"application/lxf":{source:"iana"},"application/mac-binhex40":{source:"iana",extensions:["hqx"]},"application/mac-compactpro":{source:"apache",extensions:["cpt"]},"application/macwriteii":{source:"iana"},"application/mads+xml":{source:"iana",extensions:["mads"]},"application/manifest+json":{charset:"UTF-8",compressible:!0,extensions:["webmanifest"]},"application/marc":{source:"iana",extensions:["mrc"]},"application/marcxml+xml":{source:"iana",extensions:["mrcx"]},"application/mathematica":{source:"iana",extensions:["ma","nb","mb"]},"application/mathml+xml":{source:"iana",extensions:["mathml"]},"application/mathml-content+xml":{source:"iana"},"application/mathml-presentation+xml":{source:"iana"},"application/mbms-associated-procedure-description+xml":{source:"iana"},"application/mbms-deregister+xml":{source:"iana"},"application/mbms-envelope+xml":{source:"iana"},"application/mbms-msk+xml":{source:"iana"},"application/mbms-msk-response+xml":{source:"iana"},"application/mbms-protection-description+xml":{source:"iana"},"application/mbms-reception-report+xml":{source:"iana"},"application/mbms-register+xml":{source:"iana"},"application/mbms-register-response+xml":{source:"iana"},"application/mbms-schedule+xml":{source:"iana"},"application/mbms-user-service-description+xml":{source:"iana"},"application/mbox":{source:"iana",extensions:["mbox"]},"application/media-policy-dataset+xml":{source:"iana"},"application/media_control+xml":{source:"iana"},"application/mediaservercontrol+xml":{source:"iana",extensions:["mscml"]},"application/merge-patch+json":{source:"iana",compressible:!0},"application/metalink+xml":{source:"apache",extensions:["metalink"]},"application/metalink4+xml":{source:"iana",extensions:["meta4"]},"application/mets+xml":{source:"iana",extensions:["mets"]},"application/mf4":{source:"iana"},"application/mikey":{source:"iana"},"application/mmt-usd+xml":{source:"iana"},"application/mods+xml":{source:"iana",extensions:["mods"]},"application/moss-keys":{source:"iana"},"application/moss-signature":{source:"iana"},"application/mosskey-data":{source:"iana"},"application/mosskey-request":{source:"iana"},"application/mp21":{source:"iana",extensions:["m21","mp21"]},"application/mp4":{source:"iana",extensions:["mp4s","m4p"]},"application/mpeg4-generic":{source:"iana"},"application/mpeg4-iod":{source:"iana"},"application/mpeg4-iod-xmt":{source:"iana"},"application/mrb-consumer+xml":{source:"iana"},"application/mrb-publish+xml":{source:"iana"},"application/msc-ivr+xml":{source:"iana"},"application/msc-mixer+xml":{source:"iana"},"application/msword":{source:"iana",compressible:!1,extensions:["doc","dot"]},"application/mud+json":{source:"iana",compressible:!0},"application/mxf":{source:"iana",extensions:["mxf"]},"application/n-quads":{source:"iana"},"application/n-triples":{source:"iana"},"application/nasdata":{source:"iana"},"application/news-checkgroups":{source:"iana"},"application/news-groupinfo":{source:"iana"},"application/news-transmission":{source:"iana"},"application/nlsml+xml":{source:"iana"},"application/node":{source:"iana"},"application/nss":{source:"iana"},"application/ocsp-request":{source:"iana"},"application/ocsp-response":{source:"iana"},"application/octet-stream":{source:"iana",compressible:!1,extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{source:"iana",extensions:["oda"]},"application/odx":{source:"iana"},"application/oebps-package+xml":{source:"iana",extensions:["opf"]},"application/ogg":{source:"iana",compressible:!1,extensions:["ogx"]},"application/omdoc+xml":{source:"apache",extensions:["omdoc"]},"application/onenote":{source:"apache",extensions:["onetoc","onetoc2","onetmp","onepkg"]},"application/oxps":{source:"iana",extensions:["oxps"]},"application/p2p-overlay+xml":{source:"iana"},"application/parityfec":{source:"iana"},"application/passport":{source:"iana"},"application/patch-ops-error+xml":{source:"iana",extensions:["xer"]},"application/pdf":{source:"iana",compressible:!1,extensions:["pdf"]},"application/pdx":{source:"iana"},"application/pgp-encrypted":{source:"iana",compressible:!1,extensions:["pgp"]},"application/pgp-keys":{source:"iana"},"application/pgp-signature":{source:"iana",extensions:["asc","sig"]},"application/pics-rules":{source:"apache",extensions:["prf"]},"application/pidf+xml":{source:"iana"},"application/pidf-diff+xml":{source:"iana"},"application/pkcs10":{source:"iana",extensions:["p10"]},"application/pkcs12":{source:"iana"},"application/pkcs7-mime":{source:"iana",extensions:["p7m","p7c"]},"application/pkcs7-signature":{source:"iana",extensions:["p7s"]},"application/pkcs8":{source:"iana",extensions:["p8"]},"application/pkcs8-encrypted":{source:"iana"},"application/pkix-attr-cert":{source:"iana",extensions:["ac"]},"application/pkix-cert":{source:"iana",extensions:["cer"]},"application/pkix-crl":{source:"iana",extensions:["crl"]},"application/pkix-pkipath":{source:"iana",extensions:["pkipath"]},"application/pkixcmp":{source:"iana",extensions:["pki"]},"application/pls+xml":{source:"iana",extensions:["pls"]},"application/poc-settings+xml":{source:"iana"},"application/postscript":{source:"iana",compressible:!0,extensions:["ai","eps","ps"]},"application/ppsp-tracker+json":{source:"iana",compressible:!0},"application/problem+json":{source:"iana",compressible:!0},"application/problem+xml":{source:"iana"},"application/provenance+xml":{source:"iana"},"application/prs.alvestrand.titrax-sheet":{source:"iana"},"application/prs.cww":{source:"iana",extensions:["cww"]},"application/prs.hpub+zip":{source:"iana"},"application/prs.nprend":{source:"iana"},"application/prs.plucker":{source:"iana"},"application/prs.rdf-xml-crypt":{source:"iana"},"application/prs.xsf+xml":{source:"iana"},"application/pskc+xml":{source:"iana",extensions:["pskcxml"]},"application/qsig":{source:"iana"},"application/raml+yaml":{compressible:!0,extensions:["raml"]},"application/raptorfec":{source:"iana"},"application/rdap+json":{source:"iana",compressible:!0},"application/rdf+xml":{source:"iana",compressible:!0,extensions:["rdf"]},"application/reginfo+xml":{source:"iana",extensions:["rif"]},"application/relax-ng-compact-syntax":{source:"iana",extensions:["rnc"]},"application/remote-printing":{source:"iana"},"application/reputon+json":{source:"iana",compressible:!0},"application/resource-lists+xml":{source:"iana",extensions:["rl"]},"application/resource-lists-diff+xml":{source:"iana",extensions:["rld"]},"application/rfc+xml":{source:"iana"},"application/riscos":{source:"iana"},"application/rlmi+xml":{source:"iana"},"application/rls-services+xml":{source:"iana",extensions:["rs"]},"application/route-apd+xml":{source:"iana"},"application/route-s-tsid+xml":{source:"iana"},"application/route-usd+xml":{source:"iana"},"application/rpki-ghostbusters":{source:"iana",extensions:["gbr"]},"application/rpki-manifest":{source:"iana",extensions:["mft"]},"application/rpki-publication":{source:"iana"},"application/rpki-roa":{source:"iana",extensions:["roa"]},"application/rpki-updown":{source:"iana"},"application/rsd+xml":{source:"apache",extensions:["rsd"]},"application/rss+xml":{source:"apache",compressible:!0,extensions:["rss"]},"application/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"application/rtploopback":{source:"iana"},"application/rtx":{source:"iana"},"application/samlassertion+xml":{source:"iana"},"application/samlmetadata+xml":{source:"iana"},"application/sbml+xml":{source:"iana",extensions:["sbml"]},"application/scaip+xml":{source:"iana"},"application/scim+json":{source:"iana",compressible:!0},"application/scvp-cv-request":{source:"iana",extensions:["scq"]},"application/scvp-cv-response":{source:"iana",extensions:["scs"]},"application/scvp-vp-request":{source:"iana",extensions:["spq"]},"application/scvp-vp-response":{source:"iana",extensions:["spp"]},"application/sdp":{source:"iana",extensions:["sdp"]},"application/sep+xml":{source:"iana"},"application/sep-exi":{source:"iana"},"application/session-info":{source:"iana"},"application/set-payment":{source:"iana"},"application/set-payment-initiation":{source:"iana",extensions:["setpay"]},"application/set-registration":{source:"iana"},"application/set-registration-initiation":{source:"iana",extensions:["setreg"]},"application/sgml":{source:"iana"},"application/sgml-open-catalog":{source:"iana"},"application/shf+xml":{source:"iana",extensions:["shf"]},"application/sieve":{source:"iana"},"application/simple-filter+xml":{source:"iana"},"application/simple-message-summary":{source:"iana"},"application/simplesymbolcontainer":{source:"iana"},"application/slate":{source:"iana"},"application/smil":{source:"iana"},"application/smil+xml":{source:"iana",extensions:["smi","smil"]},"application/smpte336m":{source:"iana"},"application/soap+fastinfoset":{source:"iana"},"application/soap+xml":{source:"iana",compressible:!0},"application/sparql-query":{source:"iana",extensions:["rq"]},"application/sparql-results+xml":{source:"iana",extensions:["srx"]},"application/spirits-event+xml":{source:"iana"},"application/sql":{source:"iana"},"application/srgs":{source:"iana",extensions:["gram"]},"application/srgs+xml":{source:"iana",extensions:["grxml"]},"application/sru+xml":{source:"iana",extensions:["sru"]},"application/ssdl+xml":{source:"apache",extensions:["ssdl"]},"application/ssml+xml":{source:"iana",extensions:["ssml"]},"application/tamp-apex-update":{source:"iana"},"application/tamp-apex-update-confirm":{source:"iana"},"application/tamp-community-update":{source:"iana"},"application/tamp-community-update-confirm":{source:"iana"},"application/tamp-error":{source:"iana"},"application/tamp-sequence-adjust":{source:"iana"},"application/tamp-sequence-adjust-confirm":{source:"iana"},"application/tamp-status-query":{source:"iana"},"application/tamp-status-response":{source:"iana"},"application/tamp-update":{source:"iana"},"application/tamp-update-confirm":{source:"iana"},"application/tar":{compressible:!0},"application/tei+xml":{source:"iana",extensions:["tei","teicorpus"]},"application/thraud+xml":{source:"iana",extensions:["tfi"]},"application/timestamp-query":{source:"iana"},"application/timestamp-reply":{source:"iana"},"application/timestamped-data":{source:"iana",extensions:["tsd"]},"application/tnauthlist":{source:"iana"},"application/trig":{source:"iana"},"application/ttml+xml":{source:"iana"},"application/tve-trigger":{source:"iana"},"application/ulpfec":{source:"iana"},"application/urc-grpsheet+xml":{source:"iana"},"application/urc-ressheet+xml":{source:"iana"},"application/urc-targetdesc+xml":{source:"iana"},"application/urc-uisocketdesc+xml":{source:"iana"},"application/vcard+json":{source:"iana",compressible:!0},"application/vcard+xml":{source:"iana"},"application/vemmi":{source:"iana"},"application/vividence.scriptfile":{source:"apache"},"application/vnd.1000minds.decision-model+xml":{source:"iana"},"application/vnd.3gpp-prose+xml":{source:"iana"},"application/vnd.3gpp-prose-pc3ch+xml":{source:"iana"},"application/vnd.3gpp-v2x-local-service-information":{source:"iana"},"application/vnd.3gpp.access-transfer-events+xml":{source:"iana"},"application/vnd.3gpp.bsf+xml":{source:"iana"},"application/vnd.3gpp.gmop+xml":{source:"iana"},"application/vnd.3gpp.mcptt-affiliation-command+xml":{source:"iana"},"application/vnd.3gpp.mcptt-floor-request+xml":{source:"iana"},"application/vnd.3gpp.mcptt-info+xml":{source:"iana"},"application/vnd.3gpp.mcptt-location-info+xml":{source:"iana"},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{source:"iana"},"application/vnd.3gpp.mcptt-signed+xml":{source:"iana"},"application/vnd.3gpp.mid-call+xml":{source:"iana"},"application/vnd.3gpp.pic-bw-large":{source:"iana",extensions:["plb"]},"application/vnd.3gpp.pic-bw-small":{source:"iana",extensions:["psb"]},"application/vnd.3gpp.pic-bw-var":{source:"iana",extensions:["pvb"]},"application/vnd.3gpp.sms":{source:"iana"},"application/vnd.3gpp.sms+xml":{source:"iana"},"application/vnd.3gpp.srvcc-ext+xml":{source:"iana"},"application/vnd.3gpp.srvcc-info+xml":{source:"iana"},"application/vnd.3gpp.state-and-event-info+xml":{source:"iana"},"application/vnd.3gpp.ussd+xml":{source:"iana"},"application/vnd.3gpp2.bcmcsinfo+xml":{source:"iana"},"application/vnd.3gpp2.sms":{source:"iana"},"application/vnd.3gpp2.tcap":{source:"iana",extensions:["tcap"]},"application/vnd.3lightssoftware.imagescal":{source:"iana"},"application/vnd.3m.post-it-notes":{source:"iana",extensions:["pwn"]},"application/vnd.accpac.simply.aso":{source:"iana",extensions:["aso"]},"application/vnd.accpac.simply.imp":{source:"iana",extensions:["imp"]},"application/vnd.acucobol":{source:"iana",extensions:["acu"]},"application/vnd.acucorp":{source:"iana",extensions:["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{source:"apache",extensions:["air"]},"application/vnd.adobe.flash.movie":{source:"iana"},"application/vnd.adobe.formscentral.fcdt":{source:"iana",extensions:["fcdt"]},"application/vnd.adobe.fxp":{source:"iana",extensions:["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{source:"iana"},"application/vnd.adobe.xdp+xml":{source:"iana",extensions:["xdp"]},"application/vnd.adobe.xfdf":{source:"iana",extensions:["xfdf"]},"application/vnd.aether.imp":{source:"iana"},"application/vnd.ah-barcode":{source:"iana"},"application/vnd.ahead.space":{source:"iana",extensions:["ahead"]},"application/vnd.airzip.filesecure.azf":{source:"iana",extensions:["azf"]},"application/vnd.airzip.filesecure.azs":{source:"iana",extensions:["azs"]},"application/vnd.amadeus+json":{source:"iana",compressible:!0},"application/vnd.amazon.ebook":{source:"apache",extensions:["azw"]},"application/vnd.amazon.mobi8-ebook":{source:"iana"},"application/vnd.americandynamics.acc":{source:"iana",extensions:["acc"]},"application/vnd.amiga.ami":{source:"iana",extensions:["ami"]},"application/vnd.amundsen.maze+xml":{source:"iana"},"application/vnd.android.package-archive":{source:"apache",compressible:!1,extensions:["apk"]},"application/vnd.anki":{source:"iana"},"application/vnd.anser-web-certificate-issue-initiation":{source:"iana",extensions:["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{source:"apache",extensions:["fti"]},"application/vnd.antix.game-component":{source:"iana",extensions:["atx"]},"application/vnd.apache.thrift.binary":{source:"iana"},"application/vnd.apache.thrift.compact":{source:"iana"},"application/vnd.apache.thrift.json":{source:"iana"},"application/vnd.api+json":{source:"iana",compressible:!0},"application/vnd.apothekende.reservation+json":{source:"iana",compressible:!0},"application/vnd.apple.installer+xml":{source:"iana",extensions:["mpkg"]},"application/vnd.apple.mpegurl":{source:"iana",extensions:["m3u8"]},"application/vnd.apple.pkpass":{compressible:!1,extensions:["pkpass"]},"application/vnd.arastra.swi":{source:"iana"},"application/vnd.aristanetworks.swi":{source:"iana",extensions:["swi"]},"application/vnd.artsquare":{source:"iana"},"application/vnd.astraea-software.iota":{source:"iana",extensions:["iota"]},"application/vnd.audiograph":{source:"iana",extensions:["aep"]},"application/vnd.autopackage":{source:"iana"},"application/vnd.avalon+json":{source:"iana",compressible:!0},"application/vnd.avistar+xml":{source:"iana"},"application/vnd.balsamiq.bmml+xml":{source:"iana"},"application/vnd.balsamiq.bmpr":{source:"iana"},"application/vnd.bbf.usp.msg":{source:"iana"},"application/vnd.bbf.usp.msg+json":{source:"iana",compressible:!0},"application/vnd.bekitzur-stech+json":{source:"iana",compressible:!0},"application/vnd.bint.med-content":{source:"iana"},"application/vnd.biopax.rdf+xml":{source:"iana"},"application/vnd.blink-idb-value-wrapper":{source:"iana"},"application/vnd.blueice.multipass":{source:"iana",extensions:["mpm"]},"application/vnd.bluetooth.ep.oob":{source:"iana"},"application/vnd.bluetooth.le.oob":{source:"iana"},"application/vnd.bmi":{source:"iana",extensions:["bmi"]},"application/vnd.businessobjects":{source:"iana",extensions:["rep"]},"application/vnd.cab-jscript":{source:"iana"},"application/vnd.canon-cpdl":{source:"iana"},"application/vnd.canon-lips":{source:"iana"},"application/vnd.capasystems-pg+json":{source:"iana",compressible:!0},"application/vnd.cendio.thinlinc.clientconf":{source:"iana"},"application/vnd.century-systems.tcp_stream":{source:"iana"},"application/vnd.chemdraw+xml":{source:"iana",extensions:["cdxml"]},"application/vnd.chess-pgn":{source:"iana"},"application/vnd.chipnuts.karaoke-mmd":{source:"iana",extensions:["mmd"]},"application/vnd.cinderella":{source:"iana",extensions:["cdy"]},"application/vnd.cirpack.isdn-ext":{source:"iana"},"application/vnd.citationstyles.style+xml":{source:"iana"},"application/vnd.claymore":{source:"iana",extensions:["cla"]},"application/vnd.cloanto.rp9":{source:"iana",extensions:["rp9"]},"application/vnd.clonk.c4group":{source:"iana",extensions:["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{source:"iana",extensions:["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{source:"iana",extensions:["c11amz"]},"application/vnd.coffeescript":{source:"iana"},"application/vnd.collabio.xodocuments.document":{source:"iana"},"application/vnd.collabio.xodocuments.document-template":{source:"iana"},"application/vnd.collabio.xodocuments.presentation":{source:"iana"},"application/vnd.collabio.xodocuments.presentation-template":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{source:"iana"},"application/vnd.collection+json":{source:"iana",compressible:!0},"application/vnd.collection.doc+json":{source:"iana",compressible:!0},"application/vnd.collection.next+json":{source:"iana",compressible:!0},"application/vnd.comicbook+zip":{source:"iana"},"application/vnd.comicbook-rar":{source:"iana"},"application/vnd.commerce-battelle":{source:"iana"},"application/vnd.commonspace":{source:"iana",extensions:["csp"]},"application/vnd.contact.cmsg":{source:"iana",extensions:["cdbcmsg"]},"application/vnd.coreos.ignition+json":{source:"iana",compressible:!0},"application/vnd.cosmocaller":{source:"iana",extensions:["cmc"]},"application/vnd.crick.clicker":{source:"iana",extensions:["clkx"]},"application/vnd.crick.clicker.keyboard":{source:"iana",extensions:["clkk"]},"application/vnd.crick.clicker.palette":{source:"iana",extensions:["clkp"]},"application/vnd.crick.clicker.template":{source:"iana",extensions:["clkt"]},"application/vnd.crick.clicker.wordbank":{source:"iana",extensions:["clkw"]},"application/vnd.criticaltools.wbs+xml":{source:"iana",extensions:["wbs"]},"application/vnd.ctc-posml":{source:"iana",extensions:["pml"]},"application/vnd.ctct.ws+xml":{source:"iana"},"application/vnd.cups-pdf":{source:"iana"},"application/vnd.cups-postscript":{source:"iana"},"application/vnd.cups-ppd":{source:"iana",extensions:["ppd"]},"application/vnd.cups-raster":{source:"iana"},"application/vnd.cups-raw":{source:"iana"},"application/vnd.curl":{source:"iana"},"application/vnd.curl.car":{source:"apache",extensions:["car"]},"application/vnd.curl.pcurl":{source:"apache",extensions:["pcurl"]},"application/vnd.cyan.dean.root+xml":{source:"iana"},"application/vnd.cybank":{source:"iana"},"application/vnd.d2l.coursepackage1p0+zip":{source:"iana"},"application/vnd.dart":{source:"iana",compressible:!0,extensions:["dart"]},"application/vnd.data-vision.rdz":{source:"iana",extensions:["rdz"]},"application/vnd.datapackage+json":{source:"iana",compressible:!0},"application/vnd.dataresource+json":{source:"iana",compressible:!0},"application/vnd.debian.binary-package":{source:"iana"},"application/vnd.dece.data":{source:"iana",extensions:["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{source:"iana",extensions:["uvt","uvvt"]},"application/vnd.dece.unspecified":{source:"iana",extensions:["uvx","uvvx"]},"application/vnd.dece.zip":{source:"iana",extensions:["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{source:"iana",extensions:["fe_launch"]},"application/vnd.desmume-movie":{source:"iana"},"application/vnd.desmume.movie":{source:"apache"},"application/vnd.dir-bi.plate-dl-nosuffix":{source:"iana"},"application/vnd.dm.delegation+xml":{source:"iana"},"application/vnd.dna":{source:"iana",extensions:["dna"]},"application/vnd.document+json":{source:"iana",compressible:!0},"application/vnd.dolby.mlp":{source:"apache",extensions:["mlp"]},"application/vnd.dolby.mobile.1":{source:"iana"},"application/vnd.dolby.mobile.2":{source:"iana"},"application/vnd.doremir.scorecloud-binary-document":{source:"iana"},"application/vnd.dpgraph":{source:"iana",extensions:["dpg"]},"application/vnd.dreamfactory":{source:"iana",extensions:["dfac"]},"application/vnd.drive+json":{source:"iana",compressible:!0},"application/vnd.ds-keypoint":{source:"apache",extensions:["kpxx"]},"application/vnd.dtg.local":{source:"iana"},"application/vnd.dtg.local.flash":{source:"iana"},"application/vnd.dtg.local.html":{source:"iana"},"application/vnd.dvb.ait":{source:"iana",extensions:["ait"]},"application/vnd.dvb.dvbj":{source:"iana"},"application/vnd.dvb.esgcontainer":{source:"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess2":{source:"iana"},"application/vnd.dvb.ipdcesgpdd":{source:"iana"},"application/vnd.dvb.ipdcroaming":{source:"iana"},"application/vnd.dvb.iptv.alfec-base":{source:"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{source:"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{source:"iana"},"application/vnd.dvb.notif-container+xml":{source:"iana"},"application/vnd.dvb.notif-generic+xml":{source:"iana"},"application/vnd.dvb.notif-ia-msglist+xml":{source:"iana"},"application/vnd.dvb.notif-ia-registration-request+xml":{source:"iana"},"application/vnd.dvb.notif-ia-registration-response+xml":{source:"iana"},"application/vnd.dvb.notif-init+xml":{source:"iana"},"application/vnd.dvb.pfr":{source:"iana"},"application/vnd.dvb.service":{source:"iana",extensions:["svc"]},"application/vnd.dxr":{source:"iana"},"application/vnd.dynageo":{source:"iana",extensions:["geo"]},"application/vnd.dzr":{source:"iana"},"application/vnd.easykaraoke.cdgdownload":{source:"iana"},"application/vnd.ecdis-update":{source:"iana"},"application/vnd.ecip.rlp":{source:"iana"},"application/vnd.ecowin.chart":{source:"iana",extensions:["mag"]},"application/vnd.ecowin.filerequest":{source:"iana"},"application/vnd.ecowin.fileupdate":{source:"iana"},"application/vnd.ecowin.series":{source:"iana"},"application/vnd.ecowin.seriesrequest":{source:"iana"},"application/vnd.ecowin.seriesupdate":{source:"iana"},"application/vnd.efi.img":{source:"iana"},"application/vnd.efi.iso":{source:"iana"},"application/vnd.emclient.accessrequest+xml":{source:"iana"},"application/vnd.enliven":{source:"iana",extensions:["nml"]},"application/vnd.enphase.envoy":{source:"iana"},"application/vnd.eprints.data+xml":{source:"iana"},"application/vnd.epson.esf":{source:"iana",extensions:["esf"]},"application/vnd.epson.msf":{source:"iana",extensions:["msf"]},"application/vnd.epson.quickanime":{source:"iana",extensions:["qam"]},"application/vnd.epson.salt":{source:"iana",extensions:["slt"]},"application/vnd.epson.ssf":{source:"iana",extensions:["ssf"]},"application/vnd.ericsson.quickcall":{source:"iana"},"application/vnd.espass-espass+zip":{source:"iana"},"application/vnd.eszigno3+xml":{source:"iana",extensions:["es3","et3"]},"application/vnd.etsi.aoc+xml":{source:"iana"},"application/vnd.etsi.asic-e+zip":{source:"iana"},"application/vnd.etsi.asic-s+zip":{source:"iana"},"application/vnd.etsi.cug+xml":{source:"iana"},"application/vnd.etsi.iptvcommand+xml":{source:"iana"},"application/vnd.etsi.iptvdiscovery+xml":{source:"iana"},"application/vnd.etsi.iptvprofile+xml":{source:"iana"},"application/vnd.etsi.iptvsad-bc+xml":{source:"iana"},"application/vnd.etsi.iptvsad-cod+xml":{source:"iana"},"application/vnd.etsi.iptvsad-npvr+xml":{source:"iana"},"application/vnd.etsi.iptvservice+xml":{source:"iana"},"application/vnd.etsi.iptvsync+xml":{source:"iana"},"application/vnd.etsi.iptvueprofile+xml":{source:"iana"},"application/vnd.etsi.mcid+xml":{source:"iana"},"application/vnd.etsi.mheg5":{source:"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{source:"iana"},"application/vnd.etsi.pstn+xml":{source:"iana"},"application/vnd.etsi.sci+xml":{source:"iana"},"application/vnd.etsi.simservs+xml":{source:"iana"},"application/vnd.etsi.timestamp-token":{source:"iana"},"application/vnd.etsi.tsl+xml":{source:"iana"},"application/vnd.etsi.tsl.der":{source:"iana"},"application/vnd.eudora.data":{source:"iana"},"application/vnd.evolv.ecig.profile":{source:"iana"},"application/vnd.evolv.ecig.settings":{source:"iana"},"application/vnd.evolv.ecig.theme":{source:"iana"},"application/vnd.ezpix-album":{source:"iana",extensions:["ez2"]},"application/vnd.ezpix-package":{source:"iana",extensions:["ez3"]},"application/vnd.f-secure.mobile":{source:"iana"},"application/vnd.fastcopy-disk-image":{source:"iana"},"application/vnd.fdf":{source:"iana",extensions:["fdf"]},"application/vnd.fdsn.mseed":{source:"iana",extensions:["mseed"]},"application/vnd.fdsn.seed":{source:"iana",extensions:["seed","dataless"]},"application/vnd.ffsns":{source:"iana"},"application/vnd.filmit.zfc":{source:"iana"},"application/vnd.fints":{source:"iana"},"application/vnd.firemonkeys.cloudcell":{source:"iana"},"application/vnd.flographit":{source:"iana",extensions:["gph"]},"application/vnd.fluxtime.clip":{source:"iana",extensions:["ftc"]},"application/vnd.font-fontforge-sfd":{source:"iana"},"application/vnd.framemaker":{source:"iana",extensions:["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{source:"iana",extensions:["fnc"]},"application/vnd.frogans.ltf":{source:"iana",extensions:["ltf"]},"application/vnd.fsc.weblaunch":{source:"iana",extensions:["fsc"]},"application/vnd.fujitsu.oasys":{source:"iana",extensions:["oas"]},"application/vnd.fujitsu.oasys2":{source:"iana",extensions:["oa2"]},"application/vnd.fujitsu.oasys3":{source:"iana",extensions:["oa3"]},"application/vnd.fujitsu.oasysgp":{source:"iana",extensions:["fg5"]},"application/vnd.fujitsu.oasysprs":{source:"iana",extensions:["bh2"]},"application/vnd.fujixerox.art-ex":{source:"iana"},"application/vnd.fujixerox.art4":{source:"iana"},"application/vnd.fujixerox.ddd":{source:"iana",extensions:["ddd"]},"application/vnd.fujixerox.docuworks":{source:"iana",extensions:["xdw"]},"application/vnd.fujixerox.docuworks.binder":{source:"iana",extensions:["xbd"]},"application/vnd.fujixerox.docuworks.container":{source:"iana"},"application/vnd.fujixerox.hbpl":{source:"iana"},"application/vnd.fut-misnet":{source:"iana"},"application/vnd.fuzzysheet":{source:"iana",extensions:["fzs"]},"application/vnd.genomatix.tuxedo":{source:"iana",extensions:["txd"]},"application/vnd.geo+json":{source:"iana",compressible:!0},"application/vnd.geocube+xml":{source:"iana"},"application/vnd.geogebra.file":{source:"iana",extensions:["ggb"]},"application/vnd.geogebra.tool":{source:"iana",extensions:["ggt"]},"application/vnd.geometry-explorer":{source:"iana",extensions:["gex","gre"]},"application/vnd.geonext":{source:"iana",extensions:["gxt"]},"application/vnd.geoplan":{source:"iana",extensions:["g2w"]},"application/vnd.geospace":{source:"iana",extensions:["g3w"]},"application/vnd.gerber":{source:"iana"},"application/vnd.globalplatform.card-content-mgt":{source:"iana"},"application/vnd.globalplatform.card-content-mgt-response":{source:"iana"},"application/vnd.gmx":{source:"iana",extensions:["gmx"]},"application/vnd.google-apps.document":{compressible:!1,extensions:["gdoc"]},"application/vnd.google-apps.presentation":{compressible:!1,extensions:["gslides"]},"application/vnd.google-apps.spreadsheet":{compressible:!1,extensions:["gsheet"]},"application/vnd.google-earth.kml+xml":{source:"iana",compressible:!0,extensions:["kml"]},"application/vnd.google-earth.kmz":{source:"iana",compressible:!1,extensions:["kmz"]},"application/vnd.gov.sk.e-form+xml":{source:"iana"},"application/vnd.gov.sk.e-form+zip":{source:"iana"},"application/vnd.gov.sk.xmldatacontainer+xml":{source:"iana"},"application/vnd.grafeq":{source:"iana",extensions:["gqf","gqs"]},"application/vnd.gridmp":{source:"iana"},"application/vnd.groove-account":{source:"iana",extensions:["gac"]},"application/vnd.groove-help":{source:"iana",extensions:["ghf"]},"application/vnd.groove-identity-message":{source:"iana",extensions:["gim"]},"application/vnd.groove-injector":{source:"iana",extensions:["grv"]},"application/vnd.groove-tool-message":{source:"iana",extensions:["gtm"]},"application/vnd.groove-tool-template":{source:"iana",extensions:["tpl"]},"application/vnd.groove-vcard":{source:"iana",extensions:["vcg"]},"application/vnd.hal+json":{source:"iana",compressible:!0},"application/vnd.hal+xml":{source:"iana",extensions:["hal"]},"application/vnd.handheld-entertainment+xml":{source:"iana",extensions:["zmm"]},"application/vnd.hbci":{source:"iana",extensions:["hbci"]},"application/vnd.hc+json":{source:"iana",compressible:!0},"application/vnd.hcl-bireports":{source:"iana"},"application/vnd.hdt":{source:"iana"},"application/vnd.heroku+json":{source:"iana",compressible:!0},"application/vnd.hhe.lesson-player":{source:"iana",extensions:["les"]},"application/vnd.hp-hpgl":{source:"iana",extensions:["hpgl"]},"application/vnd.hp-hpid":{source:"iana",extensions:["hpid"]},"application/vnd.hp-hps":{source:"iana",extensions:["hps"]},"application/vnd.hp-jlyt":{source:"iana",extensions:["jlt"]},"application/vnd.hp-pcl":{source:"iana",extensions:["pcl"]},"application/vnd.hp-pclxl":{source:"iana",extensions:["pclxl"]},"application/vnd.httphone":{source:"iana"},"application/vnd.hydrostatix.sof-data":{source:"iana",extensions:["sfd-hdstx"]},"application/vnd.hyper-item+json":{source:"iana",compressible:!0},"application/vnd.hyperdrive+json":{source:"iana",compressible:!0},"application/vnd.hzn-3d-crossword":{source:"iana"},"application/vnd.ibm.afplinedata":{source:"iana"},"application/vnd.ibm.electronic-media":{source:"iana"},"application/vnd.ibm.minipay":{source:"iana",extensions:["mpy"]},"application/vnd.ibm.modcap":{source:"iana",extensions:["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{source:"iana",extensions:["irm"]},"application/vnd.ibm.secure-container":{source:"iana",extensions:["sc"]},"application/vnd.iccprofile":{source:"iana",extensions:["icc","icm"]},"application/vnd.ieee.1905":{source:"iana"},"application/vnd.igloader":{source:"iana",extensions:["igl"]},"application/vnd.imagemeter.folder+zip":{source:"iana"},"application/vnd.imagemeter.image+zip":{source:"iana"},"application/vnd.immervision-ivp":{source:"iana",extensions:["ivp"]},"application/vnd.immervision-ivu":{source:"iana",extensions:["ivu"]},"application/vnd.ims.imsccv1p1":{source:"iana"},"application/vnd.ims.imsccv1p2":{source:"iana"},"application/vnd.ims.imsccv1p3":{source:"iana"},"application/vnd.ims.lis.v2.result+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy.id+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings.simple+json":{source:"iana",compressible:!0},"application/vnd.informedcontrol.rms+xml":{source:"iana"},"application/vnd.informix-visionary":{source:"iana"},"application/vnd.infotech.project":{source:"iana"},"application/vnd.infotech.project+xml":{source:"iana"},"application/vnd.innopath.wamp.notification":{source:"iana"},"application/vnd.insors.igm":{source:"iana",extensions:["igm"]},"application/vnd.intercon.formnet":{source:"iana",extensions:["xpw","xpx"]},"application/vnd.intergeo":{source:"iana",extensions:["i2g"]},"application/vnd.intertrust.digibox":{source:"iana"},"application/vnd.intertrust.nncp":{source:"iana"},"application/vnd.intu.qbo":{source:"iana",extensions:["qbo"]},"application/vnd.intu.qfx":{source:"iana",extensions:["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{source:"iana"},"application/vnd.iptc.g2.conceptitem+xml":{source:"iana"},"application/vnd.iptc.g2.knowledgeitem+xml":{source:"iana"},"application/vnd.iptc.g2.newsitem+xml":{source:"iana"},"application/vnd.iptc.g2.newsmessage+xml":{source:"iana"},"application/vnd.iptc.g2.packageitem+xml":{source:"iana"},"application/vnd.iptc.g2.planningitem+xml":{source:"iana"},"application/vnd.ipunplugged.rcprofile":{source:"iana",extensions:["rcprofile"]},"application/vnd.irepository.package+xml":{source:"iana",extensions:["irp"]},"application/vnd.is-xpr":{source:"iana",extensions:["xpr"]},"application/vnd.isac.fcs":{source:"iana",extensions:["fcs"]},"application/vnd.jam":{source:"iana",extensions:["jam"]},"application/vnd.japannet-directory-service":{source:"iana"},"application/vnd.japannet-jpnstore-wakeup":{source:"iana"},"application/vnd.japannet-payment-wakeup":{source:"iana"},"application/vnd.japannet-registration":{source:"iana"},"application/vnd.japannet-registration-wakeup":{source:"iana"},"application/vnd.japannet-setstore-wakeup":{source:"iana"},"application/vnd.japannet-verification":{source:"iana"},"application/vnd.japannet-verification-wakeup":{source:"iana"},"application/vnd.jcp.javame.midlet-rms":{source:"iana",extensions:["rms"]},"application/vnd.jisp":{source:"iana",extensions:["jisp"]},"application/vnd.joost.joda-archive":{source:"iana",extensions:["joda"]},"application/vnd.jsk.isdn-ngn":{source:"iana"},"application/vnd.kahootz":{source:"iana",extensions:["ktz","ktr"]},"application/vnd.kde.karbon":{source:"iana",extensions:["karbon"]},"application/vnd.kde.kchart":{source:"iana",extensions:["chrt"]},"application/vnd.kde.kformula":{source:"iana",extensions:["kfo"]},"application/vnd.kde.kivio":{source:"iana",extensions:["flw"]},"application/vnd.kde.kontour":{source:"iana",extensions:["kon"]},"application/vnd.kde.kpresenter":{source:"iana",extensions:["kpr","kpt"]},"application/vnd.kde.kspread":{source:"iana",extensions:["ksp"]},"application/vnd.kde.kword":{source:"iana",extensions:["kwd","kwt"]},"application/vnd.kenameaapp":{source:"iana",extensions:["htke"]},"application/vnd.kidspiration":{source:"iana",extensions:["kia"]},"application/vnd.kinar":{source:"iana",extensions:["kne","knp"]},"application/vnd.koan":{source:"iana",extensions:["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{source:"iana",extensions:["sse"]},"application/vnd.las.las+json":{source:"iana",compressible:!0},"application/vnd.las.las+xml":{source:"iana",extensions:["lasxml"]},"application/vnd.liberty-request+xml":{source:"iana"},"application/vnd.llamagraphics.life-balance.desktop":{source:"iana",extensions:["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{source:"iana",extensions:["lbe"]},"application/vnd.lotus-1-2-3":{source:"iana",extensions:["123"]},"application/vnd.lotus-approach":{source:"iana",extensions:["apr"]},"application/vnd.lotus-freelance":{source:"iana",extensions:["pre"]},"application/vnd.lotus-notes":{source:"iana",extensions:["nsf"]},"application/vnd.lotus-organizer":{source:"iana",extensions:["org"]},"application/vnd.lotus-screencam":{source:"iana",extensions:["scm"]},"application/vnd.lotus-wordpro":{source:"iana",extensions:["lwp"]},"application/vnd.macports.portpkg":{source:"iana",extensions:["portpkg"]},"application/vnd.mapbox-vector-tile":{source:"iana"},"application/vnd.marlin.drm.actiontoken+xml":{source:"iana"},"application/vnd.marlin.drm.conftoken+xml":{source:"iana"},"application/vnd.marlin.drm.license+xml":{source:"iana"},"application/vnd.marlin.drm.mdcf":{source:"iana"},"application/vnd.mason+json":{source:"iana",compressible:!0},"application/vnd.maxmind.maxmind-db":{source:"iana"},"application/vnd.mcd":{source:"iana",extensions:["mcd"]},"application/vnd.medcalcdata":{source:"iana",extensions:["mc1"]},"application/vnd.mediastation.cdkey":{source:"iana",extensions:["cdkey"]},"application/vnd.meridian-slingshot":{source:"iana"},"application/vnd.mfer":{source:"iana",extensions:["mwf"]},"application/vnd.mfmp":{source:"iana",extensions:["mfm"]},"application/vnd.micro+json":{source:"iana",compressible:!0},"application/vnd.micrografx.flo":{source:"iana",extensions:["flo"]},"application/vnd.micrografx.igx":{source:"iana",extensions:["igx"]},"application/vnd.microsoft.portable-executable":{source:"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{source:"iana"},"application/vnd.miele+json":{source:"iana",compressible:!0},"application/vnd.mif":{source:"iana",extensions:["mif"]},"application/vnd.minisoft-hp3000-save":{source:"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{source:"iana"},"application/vnd.mobius.daf":{source:"iana",extensions:["daf"]},"application/vnd.mobius.dis":{source:"iana",extensions:["dis"]},"application/vnd.mobius.mbk":{source:"iana",extensions:["mbk"]},"application/vnd.mobius.mqy":{source:"iana",extensions:["mqy"]},"application/vnd.mobius.msl":{source:"iana",extensions:["msl"]},"application/vnd.mobius.plc":{source:"iana",extensions:["plc"]},"application/vnd.mobius.txf":{source:"iana",extensions:["txf"]},"application/vnd.mophun.application":{source:"iana",extensions:["mpn"]},"application/vnd.mophun.certificate":{source:"iana",extensions:["mpc"]},"application/vnd.motorola.flexsuite":{source:"iana"},"application/vnd.motorola.flexsuite.adsi":{source:"iana"},"application/vnd.motorola.flexsuite.fis":{source:"iana"},"application/vnd.motorola.flexsuite.gotap":{source:"iana"},"application/vnd.motorola.flexsuite.kmr":{source:"iana"},"application/vnd.motorola.flexsuite.ttc":{source:"iana"},"application/vnd.motorola.flexsuite.wem":{source:"iana"},"application/vnd.motorola.iprm":{source:"iana"},"application/vnd.mozilla.xul+xml":{source:"iana",compressible:!0,extensions:["xul"]},"application/vnd.ms-3mfdocument":{source:"iana"},"application/vnd.ms-artgalry":{source:"iana",extensions:["cil"]},"application/vnd.ms-asf":{source:"iana"},"application/vnd.ms-cab-compressed":{source:"iana",extensions:["cab"]},"application/vnd.ms-color.iccprofile":{source:"apache"},"application/vnd.ms-excel":{source:"iana",compressible:!1,extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{source:"iana",extensions:["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{source:"iana",extensions:["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{source:"iana",extensions:["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{source:"iana",extensions:["xltm"]},"application/vnd.ms-fontobject":{source:"iana",compressible:!0,extensions:["eot"]},"application/vnd.ms-htmlhelp":{source:"iana",extensions:["chm"]},"application/vnd.ms-ims":{source:"iana",extensions:["ims"]},"application/vnd.ms-lrm":{source:"iana",extensions:["lrm"]},"application/vnd.ms-office.activex+xml":{source:"iana"},"application/vnd.ms-officetheme":{source:"iana",extensions:["thmx"]},"application/vnd.ms-opentype":{source:"apache",compressible:!0},"application/vnd.ms-outlook":{compressible:!1,extensions:["msg"]},"application/vnd.ms-package.obfuscated-opentype":{source:"apache"},"application/vnd.ms-pki.seccat":{source:"apache",extensions:["cat"]},"application/vnd.ms-pki.stl":{source:"apache",extensions:["stl"]},"application/vnd.ms-playready.initiator+xml":{source:"iana"},"application/vnd.ms-powerpoint":{source:"iana",compressible:!1,extensions:["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{source:"iana",extensions:["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{source:"iana",extensions:["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{source:"iana",extensions:["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{source:"iana",extensions:["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{source:"iana",extensions:["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{source:"iana"},"application/vnd.ms-printing.printticket+xml":{source:"apache"},"application/vnd.ms-printschematicket+xml":{source:"iana"},"application/vnd.ms-project":{source:"iana",extensions:["mpp","mpt"]},"application/vnd.ms-tnef":{source:"iana"},"application/vnd.ms-windows.devicepairing":{source:"iana"},"application/vnd.ms-windows.nwprinting.oob":{source:"iana"},"application/vnd.ms-windows.printerpairing":{source:"iana"},"application/vnd.ms-windows.wsd.oob":{source:"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.lic-resp":{source:"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.meter-resp":{source:"iana"},"application/vnd.ms-word.document.macroenabled.12":{source:"iana",extensions:["docm"]},"application/vnd.ms-word.template.macroenabled.12":{source:"iana",extensions:["dotm"]},"application/vnd.ms-works":{source:"iana",extensions:["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{source:"iana",extensions:["wpl"]},"application/vnd.ms-xpsdocument":{source:"iana",compressible:!1,extensions:["xps"]},"application/vnd.msa-disk-image":{source:"iana"},"application/vnd.mseq":{source:"iana",extensions:["mseq"]},"application/vnd.msign":{source:"iana"},"application/vnd.multiad.creator":{source:"iana"},"application/vnd.multiad.creator.cif":{source:"iana"},"application/vnd.music-niff":{source:"iana"},"application/vnd.musician":{source:"iana",extensions:["mus"]},"application/vnd.muvee.style":{source:"iana",extensions:["msty"]},"application/vnd.mynfc":{source:"iana",extensions:["taglet"]},"application/vnd.ncd.control":{source:"iana"},"application/vnd.ncd.reference":{source:"iana"},"application/vnd.nearst.inv+json":{source:"iana",compressible:!0},"application/vnd.nervana":{source:"iana"},"application/vnd.netfpx":{source:"iana"},"application/vnd.neurolanguage.nlu":{source:"iana",extensions:["nlu"]},"application/vnd.nintendo.nitro.rom":{source:"iana"},"application/vnd.nintendo.snes.rom":{source:"iana"},"application/vnd.nitf":{source:"iana",extensions:["ntf","nitf"]},"application/vnd.noblenet-directory":{source:"iana",extensions:["nnd"]},"application/vnd.noblenet-sealer":{source:"iana",extensions:["nns"]},"application/vnd.noblenet-web":{source:"iana",extensions:["nnw"]},"application/vnd.nokia.catalogs":{source:"iana"},"application/vnd.nokia.conml+wbxml":{source:"iana"},"application/vnd.nokia.conml+xml":{source:"iana"},"application/vnd.nokia.iptv.config+xml":{source:"iana"},"application/vnd.nokia.isds-radio-presets":{source:"iana"},"application/vnd.nokia.landmark+wbxml":{source:"iana"},"application/vnd.nokia.landmark+xml":{source:"iana"},"application/vnd.nokia.landmarkcollection+xml":{source:"iana"},"application/vnd.nokia.n-gage.ac+xml":{source:"iana"},"application/vnd.nokia.n-gage.data":{source:"iana",extensions:["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{source:"iana",extensions:["n-gage"]},"application/vnd.nokia.ncd":{source:"iana"},"application/vnd.nokia.pcd+wbxml":{source:"iana"},"application/vnd.nokia.pcd+xml":{source:"iana"},"application/vnd.nokia.radio-preset":{source:"iana",extensions:["rpst"]},"application/vnd.nokia.radio-presets":{source:"iana",extensions:["rpss"]},"application/vnd.novadigm.edm":{source:"iana",extensions:["edm"]},"application/vnd.novadigm.edx":{source:"iana",extensions:["edx"]},"application/vnd.novadigm.ext":{source:"iana",extensions:["ext"]},"application/vnd.ntt-local.content-share":{source:"iana"},"application/vnd.ntt-local.file-transfer":{source:"iana"},"application/vnd.ntt-local.ogw_remote-access":{source:"iana"},"application/vnd.ntt-local.sip-ta_remote":{source:"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{source:"iana"},"application/vnd.oasis.opendocument.chart":{source:"iana",extensions:["odc"]},"application/vnd.oasis.opendocument.chart-template":{source:"iana",extensions:["otc"]},"application/vnd.oasis.opendocument.database":{source:"iana",extensions:["odb"]},"application/vnd.oasis.opendocument.formula":{source:"iana",extensions:["odf"]},"application/vnd.oasis.opendocument.formula-template":{source:"iana",extensions:["odft"]},"application/vnd.oasis.opendocument.graphics":{source:"iana",compressible:!1,extensions:["odg"]},"application/vnd.oasis.opendocument.graphics-template":{source:"iana",extensions:["otg"]},"application/vnd.oasis.opendocument.image":{source:"iana",extensions:["odi"]},"application/vnd.oasis.opendocument.image-template":{source:"iana",extensions:["oti"]},"application/vnd.oasis.opendocument.presentation":{source:"iana",compressible:!1,extensions:["odp"]},"application/vnd.oasis.opendocument.presentation-template":{source:"iana",extensions:["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{source:"iana",compressible:!1,extensions:["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{source:"iana",extensions:["ots"]},"application/vnd.oasis.opendocument.text":{source:"iana",compressible:!1,extensions:["odt"]},"application/vnd.oasis.opendocument.text-master":{source:"iana",extensions:["odm"]},"application/vnd.oasis.opendocument.text-template":{source:"iana",extensions:["ott"]},"application/vnd.oasis.opendocument.text-web":{source:"iana",extensions:["oth"]},"application/vnd.obn":{source:"iana"},"application/vnd.ocf+cbor":{source:"iana"},"application/vnd.oftn.l10n+json":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessdownload+xml":{source:"iana"},"application/vnd.oipf.contentaccessstreaming+xml":{source:"iana"},"application/vnd.oipf.cspg-hexbinary":{source:"iana"},"application/vnd.oipf.dae.svg+xml":{source:"iana"},"application/vnd.oipf.dae.xhtml+xml":{source:"iana"},"application/vnd.oipf.mippvcontrolmessage+xml":{source:"iana"},"application/vnd.oipf.pae.gem":{source:"iana"},"application/vnd.oipf.spdiscovery+xml":{source:"iana"},"application/vnd.oipf.spdlist+xml":{source:"iana"},"application/vnd.oipf.ueprofile+xml":{source:"iana"},"application/vnd.oipf.userprofile+xml":{source:"iana"},"application/vnd.olpc-sugar":{source:"iana",extensions:["xo"]},"application/vnd.oma-scws-config":{source:"iana"},"application/vnd.oma-scws-http-request":{source:"iana"},"application/vnd.oma-scws-http-response":{source:"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{source:"iana"},"application/vnd.oma.bcast.drm-trigger+xml":{source:"iana"},"application/vnd.oma.bcast.imd+xml":{source:"iana"},"application/vnd.oma.bcast.ltkm":{source:"iana"},"application/vnd.oma.bcast.notification+xml":{source:"iana"},"application/vnd.oma.bcast.provisioningtrigger":{source:"iana"},"application/vnd.oma.bcast.sgboot":{source:"iana"},"application/vnd.oma.bcast.sgdd+xml":{source:"iana"},"application/vnd.oma.bcast.sgdu":{source:"iana"},"application/vnd.oma.bcast.simple-symbol-container":{source:"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{source:"iana"},"application/vnd.oma.bcast.sprov+xml":{source:"iana"},"application/vnd.oma.bcast.stkm":{source:"iana"},"application/vnd.oma.cab-address-book+xml":{source:"iana"},"application/vnd.oma.cab-feature-handler+xml":{source:"iana"},"application/vnd.oma.cab-pcc+xml":{source:"iana"},"application/vnd.oma.cab-subs-invite+xml":{source:"iana"},"application/vnd.oma.cab-user-prefs+xml":{source:"iana"},"application/vnd.oma.dcd":{source:"iana"},"application/vnd.oma.dcdc":{source:"iana"},"application/vnd.oma.dd2+xml":{source:"iana",extensions:["dd2"]},"application/vnd.oma.drm.risd+xml":{source:"iana"},"application/vnd.oma.group-usage-list+xml":{source:"iana"},"application/vnd.oma.lwm2m+json":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+tlv":{source:"iana"},"application/vnd.oma.pal+xml":{source:"iana"},"application/vnd.oma.poc.detailed-progress-report+xml":{source:"iana"},"application/vnd.oma.poc.final-report+xml":{source:"iana"},"application/vnd.oma.poc.groups+xml":{source:"iana"},"application/vnd.oma.poc.invocation-descriptor+xml":{source:"iana"},"application/vnd.oma.poc.optimized-progress-report+xml":{source:"iana"},"application/vnd.oma.push":{source:"iana"},"application/vnd.oma.scidm.messages+xml":{source:"iana"},"application/vnd.oma.xcap-directory+xml":{source:"iana"},"application/vnd.omads-email+xml":{source:"iana"},"application/vnd.omads-file+xml":{source:"iana"},"application/vnd.omads-folder+xml":{source:"iana"},"application/vnd.omaloc-supl-init":{source:"iana"},"application/vnd.onepager":{source:"iana"},"application/vnd.onepagertamp":{source:"iana"},"application/vnd.onepagertamx":{source:"iana"},"application/vnd.onepagertat":{source:"iana"},"application/vnd.onepagertatp":{source:"iana"},"application/vnd.onepagertatx":{source:"iana"},"application/vnd.openblox.game+xml":{source:"iana"},"application/vnd.openblox.game-binary":{source:"iana"},"application/vnd.openeye.oeb":{source:"iana"},"application/vnd.openofficeorg.extension":{source:"apache",extensions:["oxt"]},"application/vnd.openstreetmap.data+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.drawing+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{source:"iana",compressible:!1,extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.slide":{source:"iana",extensions:["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{source:"iana",extensions:["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.template":{source:"iana",extensions:["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{source:"iana",compressible:!1,extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{source:"iana",extensions:["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.theme+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.vmldrawing":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{source:"iana",compressible:!1,extensions:["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{source:"iana",extensions:["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{source:"iana"},"application/vnd.openxmlformats-package.core-properties+xml":{source:"iana"},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{source:"iana"},"application/vnd.openxmlformats-package.relationships+xml":{source:"iana"},"application/vnd.oracle.resource+json":{source:"iana",compressible:!0},"application/vnd.orange.indata":{source:"iana"},"application/vnd.osa.netdeploy":{source:"iana"},"application/vnd.osgeo.mapguide.package":{source:"iana",extensions:["mgp"]},"application/vnd.osgi.bundle":{source:"iana"},"application/vnd.osgi.dp":{source:"iana",extensions:["dp"]},"application/vnd.osgi.subsystem":{source:"iana",extensions:["esa"]},"application/vnd.otps.ct-kip+xml":{source:"iana"},"application/vnd.oxli.countgraph":{source:"iana"},"application/vnd.pagerduty+json":{source:"iana",compressible:!0},"application/vnd.palm":{source:"iana",extensions:["pdb","pqa","oprc"]},"application/vnd.panoply":{source:"iana"},"application/vnd.paos+xml":{source:"iana"},"application/vnd.paos.xml":{source:"apache"},"application/vnd.patentdive":{source:"iana"},"application/vnd.pawaafile":{source:"iana",extensions:["paw"]},"application/vnd.pcos":{source:"iana"},"application/vnd.pg.format":{source:"iana",extensions:["str"]},"application/vnd.pg.osasli":{source:"iana",extensions:["ei6"]},"application/vnd.piaccess.application-licence":{source:"iana"},"application/vnd.picsel":{source:"iana",extensions:["efif"]},"application/vnd.pmi.widget":{source:"iana",extensions:["wg"]},"application/vnd.poc.group-advertisement+xml":{source:"iana"},"application/vnd.pocketlearn":{source:"iana",extensions:["plf"]},"application/vnd.powerbuilder6":{source:"iana",extensions:["pbd"]},"application/vnd.powerbuilder6-s":{source:"iana"},"application/vnd.powerbuilder7":{source:"iana"},"application/vnd.powerbuilder7-s":{source:"iana"},"application/vnd.powerbuilder75":{source:"iana"},"application/vnd.powerbuilder75-s":{source:"iana"},"application/vnd.preminet":{source:"iana"},"application/vnd.previewsystems.box":{source:"iana",extensions:["box"]},"application/vnd.proteus.magazine":{source:"iana",extensions:["mgz"]},"application/vnd.publishare-delta-tree":{source:"iana",extensions:["qps"]},"application/vnd.pvi.ptid1":{source:"iana",extensions:["ptid"]},"application/vnd.pwg-multiplexed":{source:"iana"},"application/vnd.pwg-xhtml-print+xml":{source:"iana"},"application/vnd.qualcomm.brew-app-res":{source:"iana"},"application/vnd.quarantainenet":{source:"iana"},"application/vnd.quark.quarkxpress":{source:"iana",extensions:["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{source:"iana"},"application/vnd.radisys.moml+xml":{source:"iana"},"application/vnd.radisys.msml+xml":{source:"iana"},"application/vnd.radisys.msml-audit+xml":{source:"iana"},"application/vnd.radisys.msml-audit-conf+xml":{source:"iana"},"application/vnd.radisys.msml-audit-conn+xml":{source:"iana"},"application/vnd.radisys.msml-audit-dialog+xml":{source:"iana"},"application/vnd.radisys.msml-audit-stream+xml":{source:"iana"},"application/vnd.radisys.msml-conf+xml":{source:"iana"},"application/vnd.radisys.msml-dialog+xml":{source:"iana"},"application/vnd.radisys.msml-dialog-base+xml":{source:"iana"},"application/vnd.radisys.msml-dialog-fax-detect+xml":{source:"iana"},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{source:"iana"},"application/vnd.radisys.msml-dialog-group+xml":{source:"iana"},"application/vnd.radisys.msml-dialog-speech+xml":{source:"iana"},"application/vnd.radisys.msml-dialog-transform+xml":{source:"iana"},"application/vnd.rainstor.data":{source:"iana"},"application/vnd.rapid":{source:"iana"},"application/vnd.rar":{source:"iana"},"application/vnd.realvnc.bed":{source:"iana",extensions:["bed"]},"application/vnd.recordare.musicxml":{source:"iana",extensions:["mxl"]},"application/vnd.recordare.musicxml+xml":{source:"iana",extensions:["musicxml"]},"application/vnd.renlearn.rlprint":{source:"iana"},"application/vnd.restful+json":{source:"iana",compressible:!0},"application/vnd.rig.cryptonote":{source:"iana",extensions:["cryptonote"]},"application/vnd.rim.cod":{source:"apache",extensions:["cod"]},"application/vnd.rn-realmedia":{source:"apache",extensions:["rm"]},"application/vnd.rn-realmedia-vbr":{source:"apache",extensions:["rmvb"]},"application/vnd.route66.link66+xml":{source:"iana",extensions:["link66"]},"application/vnd.rs-274x":{source:"iana"},"application/vnd.ruckus.download":{source:"iana"},"application/vnd.s3sms":{source:"iana"},"application/vnd.sailingtracker.track":{source:"iana",extensions:["st"]},"application/vnd.sbm.cid":{source:"iana"},"application/vnd.sbm.mid2":{source:"iana"},"application/vnd.scribus":{source:"iana"},"application/vnd.sealed.3df":{source:"iana"},"application/vnd.sealed.csf":{source:"iana"},"application/vnd.sealed.doc":{source:"iana"},"application/vnd.sealed.eml":{source:"iana"},"application/vnd.sealed.mht":{source:"iana"},"application/vnd.sealed.net":{source:"iana"},"application/vnd.sealed.ppt":{source:"iana"},"application/vnd.sealed.tiff":{source:"iana"},"application/vnd.sealed.xls":{source:"iana"},"application/vnd.sealedmedia.softseal.html":{source:"iana"},"application/vnd.sealedmedia.softseal.pdf":{source:"iana"},"application/vnd.seemail":{source:"iana",extensions:["see"]},"application/vnd.sema":{source:"iana",extensions:["sema"]},"application/vnd.semd":{source:"iana",extensions:["semd"]},"application/vnd.semf":{source:"iana",extensions:["semf"]},"application/vnd.shana.informed.formdata":{source:"iana",extensions:["ifm"]},"application/vnd.shana.informed.formtemplate":{source:"iana",extensions:["itp"]},"application/vnd.shana.informed.interchange":{source:"iana",extensions:["iif"]},"application/vnd.shana.informed.package":{source:"iana",extensions:["ipk"]},"application/vnd.sigrok.session":{source:"iana"},"application/vnd.simtech-mindmapper":{source:"iana",extensions:["twd","twds"]},"application/vnd.siren+json":{source:"iana",compressible:!0},"application/vnd.smaf":{source:"iana",extensions:["mmf"]},"application/vnd.smart.notebook":{source:"iana"},"application/vnd.smart.teacher":{source:"iana",extensions:["teacher"]},"application/vnd.software602.filler.form+xml":{source:"iana"},"application/vnd.software602.filler.form-xml-zip":{source:"iana"},"application/vnd.solent.sdkm+xml":{source:"iana",extensions:["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{source:"iana",extensions:["dxp"]},"application/vnd.spotfire.sfs":{source:"iana",extensions:["sfs"]},"application/vnd.sqlite3":{source:"iana"},"application/vnd.sss-cod":{source:"iana"},"application/vnd.sss-dtf":{source:"iana"},"application/vnd.sss-ntf":{source:"iana"},"application/vnd.stardivision.calc":{source:"apache",extensions:["sdc"]},"application/vnd.stardivision.draw":{source:"apache",extensions:["sda"]},"application/vnd.stardivision.impress":{source:"apache",extensions:["sdd"]},"application/vnd.stardivision.math":{source:"apache",extensions:["smf"]},"application/vnd.stardivision.writer":{source:"apache",extensions:["sdw","vor"]},"application/vnd.stardivision.writer-global":{source:"apache",extensions:["sgl"]},"application/vnd.stepmania.package":{source:"iana",extensions:["smzip"]},"application/vnd.stepmania.stepchart":{source:"iana",extensions:["sm"]},"application/vnd.street-stream":{source:"iana"},"application/vnd.sun.wadl+xml":{source:"iana",compressible:!0,extensions:["wadl"]},"application/vnd.sun.xml.calc":{source:"apache",extensions:["sxc"]},"application/vnd.sun.xml.calc.template":{source:"apache",extensions:["stc"]},"application/vnd.sun.xml.draw":{source:"apache",extensions:["sxd"]},"application/vnd.sun.xml.draw.template":{source:"apache",extensions:["std"]},"application/vnd.sun.xml.impress":{source:"apache",extensions:["sxi"]},"application/vnd.sun.xml.impress.template":{source:"apache",extensions:["sti"]},"application/vnd.sun.xml.math":{source:"apache",extensions:["sxm"]},"application/vnd.sun.xml.writer":{source:"apache",extensions:["sxw"]},"application/vnd.sun.xml.writer.global":{source:"apache",extensions:["sxg"]},"application/vnd.sun.xml.writer.template":{source:"apache",extensions:["stw"]},"application/vnd.sus-calendar":{source:"iana",extensions:["sus","susp"]},"application/vnd.svd":{source:"iana",extensions:["svd"]},"application/vnd.swiftview-ics":{source:"iana"},"application/vnd.symbian.install":{source:"apache",extensions:["sis","sisx"]},"application/vnd.syncml+xml":{source:"iana",extensions:["xsm"]},"application/vnd.syncml.dm+wbxml":{source:"iana",extensions:["bdm"]},"application/vnd.syncml.dm+xml":{source:"iana",extensions:["xdm"]},"application/vnd.syncml.dm.notification":{source:"iana"},"application/vnd.syncml.dmddf+wbxml":{source:"iana"},"application/vnd.syncml.dmddf+xml":{source:"iana"},"application/vnd.syncml.dmtnds+wbxml":{source:"iana"},"application/vnd.syncml.dmtnds+xml":{source:"iana"},"application/vnd.syncml.ds.notification":{source:"iana"},"application/vnd.tableschema+json":{source:"iana",compressible:!0},"application/vnd.tao.intent-module-archive":{source:"iana",extensions:["tao"]},"application/vnd.tcpdump.pcap":{source:"iana",extensions:["pcap","cap","dmp"]},"application/vnd.tmd.mediaflex.api+xml":{source:"iana"},"application/vnd.tml":{source:"iana"},"application/vnd.tmobile-livetv":{source:"iana",extensions:["tmo"]},"application/vnd.tri.onesource":{source:"iana"},"application/vnd.trid.tpt":{source:"iana",extensions:["tpt"]},"application/vnd.triscape.mxs":{source:"iana",extensions:["mxs"]},"application/vnd.trueapp":{source:"iana",extensions:["tra"]},"application/vnd.truedoc":{source:"iana"},"application/vnd.ubisoft.webplayer":{source:"iana"},"application/vnd.ufdl":{source:"iana",extensions:["ufd","ufdl"]},"application/vnd.uiq.theme":{source:"iana",extensions:["utz"]},"application/vnd.umajin":{source:"iana",extensions:["umj"]},"application/vnd.unity":{source:"iana",extensions:["unityweb"]},"application/vnd.uoml+xml":{source:"iana",extensions:["uoml"]},"application/vnd.uplanet.alert":{source:"iana"},"application/vnd.uplanet.alert-wbxml":{source:"iana"},"application/vnd.uplanet.bearer-choice":{source:"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{source:"iana"},"application/vnd.uplanet.cacheop":{source:"iana"},"application/vnd.uplanet.cacheop-wbxml":{source:"iana"},"application/vnd.uplanet.channel":{source:"iana"},"application/vnd.uplanet.channel-wbxml":{source:"iana"},"application/vnd.uplanet.list":{source:"iana"},"application/vnd.uplanet.list-wbxml":{source:"iana"},"application/vnd.uplanet.listcmd":{source:"iana"},"application/vnd.uplanet.listcmd-wbxml":{source:"iana"},"application/vnd.uplanet.signal":{source:"iana"},"application/vnd.uri-map":{source:"iana"},"application/vnd.valve.source.material":{source:"iana"},"application/vnd.vcx":{source:"iana",extensions:["vcx"]},"application/vnd.vd-study":{source:"iana"},"application/vnd.vectorworks":{source:"iana"},"application/vnd.vel+json":{source:"iana",compressible:!0},"application/vnd.verimatrix.vcas":{source:"iana"},"application/vnd.vidsoft.vidconference":{source:"iana"},"application/vnd.visio":{source:"iana",extensions:["vsd","vst","vss","vsw"]},"application/vnd.visionary":{source:"iana",extensions:["vis"]},"application/vnd.vividence.scriptfile":{source:"iana"},"application/vnd.vsf":{source:"iana",extensions:["vsf"]},"application/vnd.wap.sic":{source:"iana"},"application/vnd.wap.slc":{source:"iana"},"application/vnd.wap.wbxml":{source:"iana",extensions:["wbxml"]},"application/vnd.wap.wmlc":{source:"iana",extensions:["wmlc"]},"application/vnd.wap.wmlscriptc":{source:"iana",extensions:["wmlsc"]},"application/vnd.webturbo":{source:"iana",extensions:["wtb"]},"application/vnd.wfa.p2p":{source:"iana"},"application/vnd.wfa.wsc":{source:"iana"},"application/vnd.windows.devicepairing":{source:"iana"},"application/vnd.wmc":{source:"iana"},"application/vnd.wmf.bootstrap":{source:"iana"},"application/vnd.wolfram.mathematica":{source:"iana"},"application/vnd.wolfram.mathematica.package":{source:"iana"},"application/vnd.wolfram.player":{source:"iana",extensions:["nbp"]},"application/vnd.wordperfect":{source:"iana",extensions:["wpd"]},"application/vnd.wqd":{source:"iana",extensions:["wqd"]},"application/vnd.wrq-hp3000-labelled":{source:"iana"},"application/vnd.wt.stf":{source:"iana",extensions:["stf"]},"application/vnd.wv.csp+wbxml":{source:"iana"},"application/vnd.wv.csp+xml":{source:"iana"},"application/vnd.wv.ssp+xml":{source:"iana"},"application/vnd.xacml+json":{source:"iana",compressible:!0},"application/vnd.xara":{source:"iana",extensions:["xar"]},"application/vnd.xfdl":{source:"iana",extensions:["xfdl"]},"application/vnd.xfdl.webform":{source:"iana"},"application/vnd.xmi+xml":{source:"iana"},"application/vnd.xmpie.cpkg":{source:"iana"},"application/vnd.xmpie.dpkg":{source:"iana"},"application/vnd.xmpie.plan":{source:"iana"},"application/vnd.xmpie.ppkg":{source:"iana"},"application/vnd.xmpie.xlim":{source:"iana"},"application/vnd.yamaha.hv-dic":{source:"iana",extensions:["hvd"]},"application/vnd.yamaha.hv-script":{source:"iana",extensions:["hvs"]},"application/vnd.yamaha.hv-voice":{source:"iana",extensions:["hvp"]},"application/vnd.yamaha.openscoreformat":{source:"iana",extensions:["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{source:"iana",extensions:["osfpvg"]},"application/vnd.yamaha.remote-setup":{source:"iana"},"application/vnd.yamaha.smaf-audio":{source:"iana",extensions:["saf"]},"application/vnd.yamaha.smaf-phrase":{source:"iana",extensions:["spf"]},"application/vnd.yamaha.through-ngn":{source:"iana"},"application/vnd.yamaha.tunnel-udpencap":{source:"iana"},"application/vnd.yaoweme":{source:"iana"},"application/vnd.yellowriver-custom-menu":{source:"iana",extensions:["cmp"]},"application/vnd.youtube.yt":{source:"iana"},"application/vnd.zul":{source:"iana",extensions:["zir","zirz"]},"application/vnd.zzazz.deck+xml":{source:"iana",extensions:["zaz"]},"application/voicexml+xml":{source:"iana",extensions:["vxml"]},"application/voucher-cms+json":{source:"iana",compressible:!0},"application/vq-rtcpxr":{source:"iana"},"application/wasm":{compressible:!0,extensions:["wasm"]},"application/watcherinfo+xml":{source:"iana"},"application/webpush-options+json":{source:"iana",compressible:!0},"application/whoispp-query":{source:"iana"},"application/whoispp-response":{source:"iana"},"application/widget":{source:"iana",extensions:["wgt"]},"application/winhlp":{source:"apache",extensions:["hlp"]},"application/wita":{source:"iana"},"application/wordperfect5.1":{source:"iana"},"application/wsdl+xml":{source:"iana",extensions:["wsdl"]},"application/wspolicy+xml":{source:"iana",extensions:["wspolicy"]},"application/x-7z-compressed":{source:"apache",compressible:!1,extensions:["7z"]},"application/x-abiword":{source:"apache",extensions:["abw"]},"application/x-ace-compressed":{source:"apache",extensions:["ace"]},"application/x-amf":{source:"apache"},"application/x-apple-diskimage":{source:"apache",extensions:["dmg"]},"application/x-arj":{compressible:!1,extensions:["arj"]},"application/x-authorware-bin":{source:"apache",extensions:["aab","x32","u32","vox"]},"application/x-authorware-map":{source:"apache",extensions:["aam"]},"application/x-authorware-seg":{source:"apache",extensions:["aas"]},"application/x-bcpio":{source:"apache",extensions:["bcpio"]},"application/x-bdoc":{compressible:!1,extensions:["bdoc"]},"application/x-bittorrent":{source:"apache",extensions:["torrent"]},"application/x-blorb":{source:"apache",extensions:["blb","blorb"]},"application/x-bzip":{source:"apache",compressible:!1,extensions:["bz"]},"application/x-bzip2":{source:"apache",compressible:!1,extensions:["bz2","boz"]},"application/x-cbr":{source:"apache",extensions:["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{source:"apache",extensions:["vcd"]},"application/x-cfs-compressed":{source:"apache",extensions:["cfs"]},"application/x-chat":{source:"apache",extensions:["chat"]},"application/x-chess-pgn":{source:"apache",extensions:["pgn"]},"application/x-chrome-extension":{extensions:["crx"]},"application/x-cocoa":{source:"nginx",extensions:["cco"]},"application/x-compress":{source:"apache"},"application/x-conference":{source:"apache",extensions:["nsc"]},"application/x-cpio":{source:"apache",extensions:["cpio"]},"application/x-csh":{source:"apache",extensions:["csh"]},"application/x-deb":{compressible:!1},"application/x-debian-package":{source:"apache",extensions:["deb","udeb"]},"application/x-dgc-compressed":{source:"apache",extensions:["dgc"]},"application/x-director":{source:"apache",extensions:["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{source:"apache",extensions:["wad"]},"application/x-dtbncx+xml":{source:"apache",extensions:["ncx"]},"application/x-dtbook+xml":{source:"apache",extensions:["dtb"]},"application/x-dtbresource+xml":{source:"apache",extensions:["res"]},"application/x-dvi":{source:"apache",compressible:!1,extensions:["dvi"]},"application/x-envoy":{source:"apache",extensions:["evy"]},"application/x-eva":{source:"apache",extensions:["eva"]},"application/x-font-bdf":{source:"apache",extensions:["bdf"]},"application/x-font-dos":{source:"apache"},"application/x-font-framemaker":{source:"apache"},"application/x-font-ghostscript":{source:"apache",extensions:["gsf"]},"application/x-font-libgrx":{source:"apache"},"application/x-font-linux-psf":{source:"apache",extensions:["psf"]},"application/x-font-pcf":{source:"apache",extensions:["pcf"]},"application/x-font-snf":{source:"apache",extensions:["snf"]},"application/x-font-speedo":{source:"apache"},"application/x-font-sunos-news":{source:"apache"},"application/x-font-type1":{source:"apache",extensions:["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{source:"apache"},"application/x-freearc":{source:"apache",extensions:["arc"]},"application/x-futuresplash":{source:"apache",extensions:["spl"]},"application/x-gca-compressed":{source:"apache",extensions:["gca"]},"application/x-glulx":{source:"apache",extensions:["ulx"]},"application/x-gnumeric":{source:"apache",extensions:["gnumeric"]},"application/x-gramps-xml":{source:"apache",extensions:["gramps"]},"application/x-gtar":{source:"apache",extensions:["gtar"]},"application/x-gzip":{source:"apache"},"application/x-hdf":{source:"apache",extensions:["hdf"]},"application/x-httpd-php":{compressible:!0,extensions:["php"]},"application/x-install-instructions":{source:"apache",extensions:["install"]},"application/x-iso9660-image":{source:"apache",extensions:["iso"]},"application/x-java-archive-diff":{source:"nginx",extensions:["jardiff"]},"application/x-java-jnlp-file":{source:"apache",compressible:!1,extensions:["jnlp"]},"application/x-javascript":{compressible:!0},"application/x-latex":{source:"apache",compressible:!1,extensions:["latex"]},"application/x-lua-bytecode":{extensions:["luac"]},"application/x-lzh-compressed":{source:"apache",extensions:["lzh","lha"]},"application/x-makeself":{source:"nginx",extensions:["run"]},"application/x-mie":{source:"apache",extensions:["mie"]},"application/x-mobipocket-ebook":{source:"apache",extensions:["prc","mobi"]},"application/x-mpegurl":{compressible:!1},"application/x-ms-application":{source:"apache",extensions:["application"]},"application/x-ms-shortcut":{source:"apache",extensions:["lnk"]},"application/x-ms-wmd":{source:"apache",extensions:["wmd"]},"application/x-ms-wmz":{source:"apache",extensions:["wmz"]},"application/x-ms-xbap":{source:"apache",extensions:["xbap"]},"application/x-msaccess":{source:"apache",extensions:["mdb"]},"application/x-msbinder":{source:"apache",extensions:["obd"]},"application/x-mscardfile":{source:"apache",extensions:["crd"]},"application/x-msclip":{source:"apache",extensions:["clp"]},"application/x-msdos-program":{extensions:["exe"]},"application/x-msdownload":{source:"apache",extensions:["exe","dll","com","bat","msi"]},"application/x-msmediaview":{source:"apache",extensions:["mvb","m13","m14"]},"application/x-msmetafile":{source:"apache",extensions:["wmf","wmz","emf","emz"]},"application/x-msmoney":{source:"apache",extensions:["mny"]},"application/x-mspublisher":{source:"apache",extensions:["pub"]},"application/x-msschedule":{source:"apache",extensions:["scd"]},"application/x-msterminal":{source:"apache",extensions:["trm"]},"application/x-mswrite":{source:"apache",extensions:["wri"]},"application/x-netcdf":{source:"apache",extensions:["nc","cdf"]},"application/x-ns-proxy-autoconfig":{compressible:!0,extensions:["pac"]},"application/x-nzb":{source:"apache",extensions:["nzb"]},"application/x-perl":{source:"nginx",extensions:["pl","pm"]},"application/x-pilot":{source:"nginx",extensions:["prc","pdb"]},"application/x-pkcs12":{source:"apache",compressible:!1,extensions:["p12","pfx"]},"application/x-pkcs7-certificates":{source:"apache",extensions:["p7b","spc"]},"application/x-pkcs7-certreqresp":{source:"apache",extensions:["p7r"]},"application/x-rar-compressed":{source:"apache",compressible:!1,extensions:["rar"]},"application/x-redhat-package-manager":{source:"nginx",extensions:["rpm"]},"application/x-research-info-systems":{source:"apache",extensions:["ris"]},"application/x-sea":{source:"nginx",extensions:["sea"]},"application/x-sh":{source:"apache",compressible:!0,extensions:["sh"]},"application/x-shar":{source:"apache",extensions:["shar"]},"application/x-shockwave-flash":{source:"apache",compressible:!1,extensions:["swf"]},"application/x-silverlight-app":{source:"apache",extensions:["xap"]},"application/x-sql":{source:"apache",extensions:["sql"]},"application/x-stuffit":{source:"apache",compressible:!1,extensions:["sit"]},"application/x-stuffitx":{source:"apache",extensions:["sitx"]},"application/x-subrip":{source:"apache",extensions:["srt"]},"application/x-sv4cpio":{source:"apache",extensions:["sv4cpio"]},"application/x-sv4crc":{source:"apache",extensions:["sv4crc"]},"application/x-t3vm-image":{source:"apache",extensions:["t3"]},"application/x-tads":{source:"apache",extensions:["gam"]},"application/x-tar":{source:"apache",compressible:!0,extensions:["tar"]},"application/x-tcl":{source:"apache",extensions:["tcl","tk"]},"application/x-tex":{source:"apache",extensions:["tex"]},"application/x-tex-tfm":{source:"apache",extensions:["tfm"]},"application/x-texinfo":{source:"apache",extensions:["texinfo","texi"]},"application/x-tgif":{source:"apache",extensions:["obj"]},"application/x-ustar":{source:"apache",extensions:["ustar"]},"application/x-virtualbox-hdd":{compressible:!0,extensions:["hdd"]},"application/x-virtualbox-ova":{compressible:!0,extensions:["ova"]},"application/x-virtualbox-ovf":{compressible:!0,extensions:["ovf"]},"application/x-virtualbox-vbox":{compressible:!0,extensions:["vbox"]},"application/x-virtualbox-vbox-extpack":{compressible:!1,extensions:["vbox-extpack"]},"application/x-virtualbox-vdi":{compressible:!0,extensions:["vdi"]},"application/x-virtualbox-vhd":{compressible:!0,extensions:["vhd"]},"application/x-virtualbox-vmdk":{compressible:!0,extensions:["vmdk"]},"application/x-wais-source":{source:"apache",extensions:["src"]},"application/x-web-app-manifest+json":{compressible:!0,extensions:["webapp"]},"application/x-www-form-urlencoded":{source:"iana",compressible:!0},"application/x-x509-ca-cert":{source:"apache",extensions:["der","crt","pem"]},"application/x-xfig":{source:"apache",extensions:["fig"]},"application/x-xliff+xml":{source:"apache",extensions:["xlf"]},"application/x-xpinstall":{source:"apache",compressible:!1,extensions:["xpi"]},"application/x-xz":{source:"apache",extensions:["xz"]},"application/x-zmachine":{source:"apache",extensions:["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{source:"iana"},"application/xacml+xml":{source:"iana"},"application/xaml+xml":{source:"apache",extensions:["xaml"]},"application/xcap-att+xml":{source:"iana"},"application/xcap-caps+xml":{source:"iana"},"application/xcap-diff+xml":{source:"iana",extensions:["xdf"]},"application/xcap-el+xml":{source:"iana"},"application/xcap-error+xml":{source:"iana"},"application/xcap-ns+xml":{source:"iana"},"application/xcon-conference-info+xml":{source:"iana"},"application/xcon-conference-info-diff+xml":{source:"iana"},"application/xenc+xml":{source:"iana",extensions:["xenc"]},"application/xhtml+xml":{source:"iana",compressible:!0,extensions:["xhtml","xht"]},"application/xhtml-voice+xml":{source:"apache"},"application/xml":{source:"iana",compressible:!0,extensions:["xml","xsl","xsd","rng"]},"application/xml-dtd":{source:"iana",compressible:!0,extensions:["dtd"]},"application/xml-external-parsed-entity":{source:"iana"},"application/xml-patch+xml":{source:"iana"},"application/xmpp+xml":{source:"iana"},"application/xop+xml":{source:"iana",compressible:!0,extensions:["xop"]},"application/xproc+xml":{source:"apache",extensions:["xpl"]},"application/xslt+xml":{source:"iana",extensions:["xslt"]},"application/xspf+xml":{source:"apache",extensions:["xspf"]},"application/xv+xml":{source:"iana",extensions:["mxml","xhvml","xvml","xvm"]},"application/yang":{source:"iana",extensions:["yang"]},"application/yang-data+json":{source:"iana",compressible:!0},"application/yang-data+xml":{source:"iana"},"application/yang-patch+json":{source:"iana",compressible:!0},"application/yang-patch+xml":{source:"iana"},"application/yin+xml":{source:"iana",extensions:["yin"]},"application/zip":{source:"iana",compressible:!1,extensions:["zip"]},"application/zlib":{source:"iana"},"audio/1d-interleaved-parityfec":{source:"iana"},"audio/32kadpcm":{source:"iana"},"audio/3gpp":{source:"iana",compressible:!1,extensions:["3gpp"]},"audio/3gpp2":{source:"iana"},"audio/ac3":{source:"iana"},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana"},"audio/amr-wb":{source:"iana"},"audio/amr-wb+":{source:"iana"},"audio/aptx":{source:"iana"},"audio/asc":{source:"iana"},"audio/atrac-advanced-lossless":{source:"iana"},"audio/atrac-x":{source:"iana"},"audio/atrac3":{source:"iana"},"audio/basic":{source:"iana",compressible:!1,extensions:["au","snd"]},"audio/bv16":{source:"iana"},"audio/bv32":{source:"iana"},"audio/clearmode":{source:"iana"},"audio/cn":{source:"iana"},"audio/dat12":{source:"iana"},"audio/dls":{source:"iana"},"audio/dsr-es201108":{source:"iana"},"audio/dsr-es202050":{source:"iana"},"audio/dsr-es202211":{source:"iana"},"audio/dsr-es202212":{source:"iana"},"audio/dv":{source:"iana"},"audio/dvi4":{source:"iana"},"audio/eac3":{source:"iana"},"audio/encaprtp":{source:"iana"},"audio/evrc":{source:"iana"},"audio/evrc-qcp":{source:"iana"},"audio/evrc0":{source:"iana"},"audio/evrc1":{source:"iana"},"audio/evrcb":{source:"iana"},"audio/evrcb0":{source:"iana"},"audio/evrcb1":{source:"iana"},"audio/evrcnw":{source:"iana"},"audio/evrcnw0":{source:"iana"},"audio/evrcnw1":{source:"iana"},"audio/evrcwb":{source:"iana"},"audio/evrcwb0":{source:"iana"},"audio/evrcwb1":{source:"iana"},"audio/evs":{source:"iana"},"audio/fwdred":{source:"iana"},"audio/g711-0":{source:"iana"},"audio/g719":{source:"iana"},"audio/g722":{source:"iana"},"audio/g7221":{source:"iana"},"audio/g723":{source:"iana"},"audio/g726-16":{source:"iana"},"audio/g726-24":{source:"iana"},"audio/g726-32":{source:"iana"},"audio/g726-40":{source:"iana"},"audio/g728":{source:"iana"},"audio/g729":{source:"iana"},"audio/g7291":{source:"iana"},"audio/g729d":{source:"iana"},"audio/g729e":{source:"iana"},"audio/gsm":{source:"iana"},"audio/gsm-efr":{source:"iana"},"audio/gsm-hr-08":{source:"iana"},"audio/ilbc":{source:"iana"},"audio/ip-mr_v2.5":{source:"iana"},"audio/isac":{source:"apache"},"audio/l16":{source:"iana"},"audio/l20":{source:"iana"},"audio/l24":{source:"iana",compressible:!1},"audio/l8":{source:"iana"},"audio/lpc":{source:"iana"},"audio/melp":{source:"iana"},"audio/melp1200":{source:"iana"},"audio/melp2400":{source:"iana"},"audio/melp600":{source:"iana"},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana"},"audio/mp3":{compressible:!1,extensions:["mp3"]},"audio/mp4":{source:"iana",compressible:!1,extensions:["m4a","mp4a"]},"audio/mp4a-latm":{source:"iana"},"audio/mpa":{source:"iana"},"audio/mpa-robust":{source:"iana"},"audio/mpeg":{source:"iana",compressible:!1,extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{source:"iana"},"audio/musepack":{source:"apache"},"audio/ogg":{source:"iana",compressible:!1,extensions:["oga","ogg","spx"]},"audio/opus":{source:"iana"},"audio/parityfec":{source:"iana"},"audio/pcma":{source:"iana"},"audio/pcma-wb":{source:"iana"},"audio/pcmu":{source:"iana"},"audio/pcmu-wb":{source:"iana"},"audio/prs.sid":{source:"iana"},"audio/qcelp":{source:"iana"},"audio/raptorfec":{source:"iana"},"audio/red":{source:"iana"},"audio/rtp-enc-aescm128":{source:"iana"},"audio/rtp-midi":{source:"iana"},"audio/rtploopback":{source:"iana"},"audio/rtx":{source:"iana"},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/silk":{source:"apache",extensions:["sil"]},"audio/smv":{source:"iana"},"audio/smv-qcp":{source:"iana"},"audio/smv0":{source:"iana"},"audio/sp-midi":{source:"iana"},"audio/speex":{source:"iana"},"audio/t140c":{source:"iana"},"audio/t38":{source:"iana"},"audio/telephone-event":{source:"iana"},"audio/tone":{source:"iana"},"audio/uemclip":{source:"iana"},"audio/ulpfec":{source:"iana"},"audio/vdvi":{source:"iana"},"audio/vmr-wb":{source:"iana"},"audio/vnd.3gpp.iufp":{source:"iana"},"audio/vnd.4sb":{source:"iana"},"audio/vnd.audiokoz":{source:"iana"},"audio/vnd.celp":{source:"iana"},"audio/vnd.cisco.nse":{source:"iana"},"audio/vnd.cmles.radio-events":{source:"iana"},"audio/vnd.cns.anp1":{source:"iana"},"audio/vnd.cns.inf1":{source:"iana"},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dlna.adts":{source:"iana"},"audio/vnd.dolby.heaac.1":{source:"iana"},"audio/vnd.dolby.heaac.2":{source:"iana"},"audio/vnd.dolby.mlp":{source:"iana"},"audio/vnd.dolby.mps":{source:"iana"},"audio/vnd.dolby.pl2":{source:"iana"},"audio/vnd.dolby.pl2x":{source:"iana"},"audio/vnd.dolby.pl2z":{source:"iana"},"audio/vnd.dolby.pulse.1":{source:"iana"},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.dvb.file":{source:"iana"},"audio/vnd.everad.plj":{source:"iana"},"audio/vnd.hns.audio":{source:"iana"},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nokia.mobile-xmf":{source:"iana"},"audio/vnd.nortel.vbk":{source:"iana"},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.octel.sbc":{source:"iana"},"audio/vnd.presonus.multitrack":{source:"iana"},"audio/vnd.qcelp":{source:"iana"},"audio/vnd.rhetorex.32kadpcm":{source:"iana"},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/vnd.rn-realaudio":{compressible:!1},"audio/vnd.sealedmedia.softseal.mpeg":{source:"iana"},"audio/vnd.vmx.cvsd":{source:"iana"},"audio/vnd.wave":{compressible:!1},"audio/vorbis":{source:"iana",compressible:!1},"audio/vorbis-config":{source:"iana"},"audio/wav":{compressible:!1,extensions:["wav"]},"audio/wave":{compressible:!1,extensions:["wav"]},"audio/webm":{source:"apache",compressible:!1,extensions:["weba"]},"audio/x-aac":{source:"apache",compressible:!1,extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",compressible:!1,extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-tta":{source:"apache"},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/xm":{source:"apache",extensions:["xm"]},"chemical/x-cdx":{source:"apache",extensions:["cdx"]},"chemical/x-cif":{source:"apache",extensions:["cif"]},"chemical/x-cmdf":{source:"apache",extensions:["cmdf"]},"chemical/x-cml":{source:"apache",extensions:["cml"]},"chemical/x-csml":{source:"apache",extensions:["csml"]},"chemical/x-pdb":{source:"apache"},"chemical/x-xyz":{source:"apache",extensions:["xyz"]},"font/collection":{source:"iana",extensions:["ttc"]},"font/otf":{source:"iana",compressible:!0,extensions:["otf"]},"font/sfnt":{source:"iana"},"font/ttf":{source:"iana",extensions:["ttf"]},"font/woff":{source:"iana",extensions:["woff"]},"font/woff2":{source:"iana",extensions:["woff2"]},"image/aces":{source:"iana"},"image/apng":{compressible:!1,extensions:["apng"]},"image/bmp":{source:"iana",compressible:!0,extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana"},"image/emf":{source:"iana"},"image/fits":{source:"iana"},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",compressible:!1,extensions:["gif"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana"},"image/jp2":{source:"iana",compressible:!1,extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",compressible:!1,extensions:["jpeg","jpg","jpe"]},"image/jpm":{source:"iana",compressible:!1,extensions:["jpm"]},"image/jpx":{source:"iana",compressible:!1,extensions:["jpx","jpf"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/naplps":{source:"iana"},"image/pjpeg":{compressible:!1},"image/png":{source:"iana",compressible:!1,extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana"},"image/pwg-raster":{source:"iana"},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",compressible:!0,extensions:["svg","svgz"]},"image/t38":{source:"iana"},"image/tiff":{source:"iana",compressible:!1,extensions:["tiff","tif"]},"image/tiff-fx":{source:"iana"},"image/vnd.adobe.photoshop":{source:"iana",compressible:!0,extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana"},"image/vnd.cns.inf2":{source:"iana"},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.globalgraphics.pgb":{source:"iana"},"image/vnd.microsoft.icon":{source:"iana"},"image/vnd.mix":{source:"iana"},"image/vnd.mozilla.apng":{source:"iana"},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.radiance":{source:"iana"},"image/vnd.sealed.png":{source:"iana"},"image/vnd.sealedmedia.softseal.gif":{source:"iana"},"image/vnd.sealedmedia.softseal.jpg":{source:"iana"},"image/vnd.svf":{source:"iana"},"image/vnd.tencent.tap":{source:"iana"},"image/vnd.valve.source.texture":{source:"iana"},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana"},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana"},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",compressible:!0,extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",compressible:!0,extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xcf":{compressible:!1},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]},"message/cpim":{source:"iana"},"message/delivery-status":{source:"iana"},"message/disposition-notification":{source:"iana",extensions:["disposition-notification"]},"message/external-body":{source:"iana"},"message/feedback-report":{source:"iana"},"message/global":{source:"iana",extensions:["u8msg"]},"message/global-delivery-status":{source:"iana",extensions:["u8dsn"]},"message/global-disposition-notification":{source:"iana",extensions:["u8mdn"]},"message/global-headers":{source:"iana",extensions:["u8hdr"]},"message/http":{source:"iana",compressible:!1},"message/imdn+xml":{source:"iana",compressible:!0},"message/news":{source:"iana"},"message/partial":{source:"iana",compressible:!1},"message/rfc822":{source:"iana",compressible:!0,extensions:["eml","mime"]},"message/s-http":{source:"iana"},"message/sip":{source:"iana"},"message/sipfrag":{source:"iana"},"message/tracking-status":{source:"iana"},"message/vnd.si.simp":{source:"iana"},"message/vnd.wfa.wsc":{source:"iana",extensions:["wsc"]},"model/3mf":{source:"iana"},"model/gltf+json":{source:"iana",compressible:!0,extensions:["gltf"]},"model/gltf-binary":{source:"iana",compressible:!0,extensions:["glb"]},"model/iges":{source:"iana",compressible:!1,extensions:["igs","iges"]},"model/mesh":{source:"iana",compressible:!1,extensions:["msh","mesh","silo"]},"model/vnd.collada+xml":{source:"iana",extensions:["dae"]},"model/vnd.dwf":{source:"iana",extensions:["dwf"]},"model/vnd.flatland.3dml":{source:"iana"},"model/vnd.gdl":{source:"iana",extensions:["gdl"]},"model/vnd.gs-gdl":{source:"apache"},"model/vnd.gs.gdl":{source:"iana"},"model/vnd.gtw":{source:"iana",extensions:["gtw"]},"model/vnd.moml+xml":{source:"iana"},"model/vnd.mts":{source:"iana",extensions:["mts"]},"model/vnd.opengex":{source:"iana"},"model/vnd.parasolid.transmit.binary":{source:"iana"},"model/vnd.parasolid.transmit.text":{source:"iana"},"model/vnd.rosette.annotated-data-model":{source:"iana"},"model/vnd.valve.source.compiled-map":{source:"iana"},"model/vnd.vtu":{source:"iana",extensions:["vtu"]},"model/vrml":{source:"iana",compressible:!1,extensions:["wrl","vrml"]},"model/x3d+binary":{source:"apache",compressible:!1,extensions:["x3db","x3dbz"]},"model/x3d+fastinfoset":{source:"iana"},"model/x3d+vrml":{source:"apache",compressible:!1,extensions:["x3dv","x3dvz"]},"model/x3d+xml":{source:"iana",compressible:!0,extensions:["x3d","x3dz"]},"model/x3d-vrml":{source:"iana"},"multipart/alternative":{source:"iana",compressible:!1},"multipart/appledouble":{source:"iana"},"multipart/byteranges":{source:"iana"},"multipart/digest":{source:"iana"},"multipart/encrypted":{source:"iana",compressible:!1},"multipart/form-data":{source:"iana",compressible:!1},"multipart/header-set":{source:"iana"},"multipart/mixed":{source:"iana",compressible:!1},"multipart/multilingual":{source:"iana"},"multipart/parallel":{source:"iana"},"multipart/related":{source:"iana",compressible:!1},"multipart/report":{source:"iana"},"multipart/signed":{source:"iana",compressible:!1},"multipart/vnd.bint.med-plus":{source:"iana"},"multipart/voice-message":{source:"iana"},"multipart/x-mixed-replace":{source:"iana"},"text/1d-interleaved-parityfec":{source:"iana"},"text/cache-manifest":{source:"iana",compressible:!0,extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/calender":{compressible:!0},"text/cmd":{compressible:!0},"text/coffeescript":{extensions:["coffee","litcoffee"]},"text/css":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["css"]},"text/csv":{source:"iana",compressible:!0,extensions:["csv"]},"text/csv-schema":{source:"iana"},"text/directory":{source:"iana"},"text/dns":{source:"iana"},"text/ecmascript":{source:"iana"},"text/encaprtp":{source:"iana"},"text/enriched":{source:"iana"},"text/fwdred":{source:"iana"},"text/grammar-ref-list":{source:"iana"},"text/html":{source:"iana",compressible:!0,extensions:["html","htm","shtml"]},"text/jade":{extensions:["jade"]},"text/javascript":{source:"iana",compressible:!0},"text/jcr-cnd":{source:"iana"},"text/jsx":{compressible:!0,extensions:["jsx"]},"text/less":{extensions:["less"]},"text/markdown":{source:"iana",compressible:!0,extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/mizar":{source:"iana"},"text/n3":{source:"iana",compressible:!0,extensions:["n3"]},"text/parameters":{source:"iana"},"text/parityfec":{source:"iana"},"text/plain":{source:"iana",compressible:!0,extensions:["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{source:"iana"},"text/prs.fallenstein.rst":{source:"iana"},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/prs.prop.logic":{source:"iana"},"text/raptorfec":{source:"iana"},"text/red":{source:"iana"},"text/rfc822-headers":{source:"iana"},"text/richtext":{source:"iana",compressible:!0,extensions:["rtx"]},"text/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"text/rtp-enc-aescm128":{source:"iana"},"text/rtploopback":{source:"iana"},"text/rtx":{source:"iana"},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shex":{extensions:["shex"]},"text/slim":{extensions:["slim","slm"]},"text/strings":{source:"iana"},"text/stylus":{extensions:["stylus","styl"]},"text/t140":{source:"iana"},"text/tab-separated-values":{source:"iana",compressible:!0,extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",extensions:["ttl"]},"text/ulpfec":{source:"iana"},"text/uri-list":{source:"iana",compressible:!0,extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",compressible:!0,extensions:["vcard"]},"text/vnd.a":{source:"iana"},"text/vnd.abc":{source:"iana"},"text/vnd.ascii-art":{source:"iana"},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.debian.copyright":{source:"iana"},"text/vnd.dmclientscript":{source:"iana"},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.esmertec.theme-descriptor":{source:"iana"},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.iptc.newsml":{source:"iana"},"text/vnd.iptc.nitf":{source:"iana"},"text/vnd.latex-z":{source:"iana"},"text/vnd.motorola.reflex":{source:"iana"},"text/vnd.ms-mediapackage":{source:"iana"},"text/vnd.net2phone.commcenter.command":{source:"iana"},"text/vnd.radisys.msml-basic-layout":{source:"iana"},"text/vnd.si.uricatalogue":{source:"iana"},"text/vnd.sun.j2me.app-descriptor":{source:"iana",extensions:["jad"]},"text/vnd.trolltech.linguist":{source:"iana"},"text/vnd.wap.si":{source:"iana"},"text/vnd.wap.sl":{source:"iana"},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{charset:"UTF-8",compressible:!0,extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-gwt-rpc":{compressible:!0},"text/x-handlebars-template":{extensions:["hbs"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-jquery-tmpl":{compressible:!0},"text/x-lua":{extensions:["lua"]},"text/x-markdown":{compressible:!0,extensions:["mkd"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-org":{compressible:!0,extensions:["org"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-processing":{compressible:!0,extensions:["pde"]},"text/x-sass":{extensions:["sass"]},"text/x-scss":{extensions:["scss"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-suse-ymp":{compressible:!0,extensions:["ymp"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",compressible:!0,extensions:["xml"]},"text/xml-external-parsed-entity":{source:"iana"},"text/yaml":{extensions:["yaml","yml"]},"video/1d-interleaved-parityfec":{source:"iana"},"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp-tt":{source:"iana"},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/bmpeg":{source:"iana"},"video/bt656":{source:"iana"},"video/celb":{source:"iana"},"video/dv":{source:"iana"},"video/encaprtp":{source:"iana"},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h263-1998":{source:"iana"},"video/h263-2000":{source:"iana"},"video/h264":{source:"iana",extensions:["h264"]},"video/h264-rcdo":{source:"iana"},"video/h264-svc":{source:"iana"},"video/h265":{source:"iana"},"video/iso.segment":{source:"iana"},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpeg2000":{source:"iana"},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp1s":{source:"iana"},"video/mp2p":{source:"iana"},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",compressible:!1,extensions:["mp4","mp4v","mpg4"]},"video/mp4v-es":{source:"iana"},"video/mpeg":{source:"iana",compressible:!1,extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{source:"iana"},"video/mpv":{source:"iana"},"video/nv":{source:"iana"},"video/ogg":{source:"iana",compressible:!1,extensions:["ogv"]},"video/parityfec":{source:"iana"},"video/pointer":{source:"iana"},"video/quicktime":{source:"iana",compressible:!1,extensions:["qt","mov"]},"video/raptorfec":{source:"iana"},"video/raw":{source:"iana"},"video/rtp-enc-aescm128":{source:"iana"},"video/rtploopback":{source:"iana"},"video/rtx":{source:"iana"},"video/smpte291":{source:"iana"},"video/smpte292m":{source:"iana"},"video/ulpfec":{source:"iana"},"video/vc1":{source:"iana"},"video/vnd.cctv":{source:"iana"},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.mp4":{source:"iana"},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.directv.mpeg":{source:"iana"},"video/vnd.directv.mpeg-tts":{source:"iana"},"video/vnd.dlna.mpeg-tts":{source:"iana"},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.hns.video":{source:"iana"},"video/vnd.iptvforum.1dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.1dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.2dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.2dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.ttsavc":{source:"iana"},"video/vnd.iptvforum.ttsmpeg2":{source:"iana"},"video/vnd.motorola.video":{source:"iana"},"video/vnd.motorola.videop":{source:"iana"},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.nokia.interleaved-multimedia":{source:"iana"},"video/vnd.nokia.mp4vr":{source:"iana"},"video/vnd.nokia.videovoip":{source:"iana"},"video/vnd.objectvideo":{source:"iana"},"video/vnd.radgamettools.bink":{source:"iana"},"video/vnd.radgamettools.smacker":{source:"iana"},"video/vnd.sealed.mpeg1":{source:"iana"},"video/vnd.sealed.mpeg4":{source:"iana"},"video/vnd.sealed.swf":{source:"iana"},"video/vnd.sealedmedia.softseal.mov":{source:"iana"},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/vp8":{source:"iana"},"video/webm":{source:"apache",compressible:!1,extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",compressible:!1,extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",compressible:!1,extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",compressible:!1,extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]},"x-conference/x-cooltalk":{source:"apache",extensions:["ice"]},"x-shader/x-fragment":{compressible:!0},"x-shader/x-vertex":{compressible:!0}}},function(a,e,n){a.exports={parallel:n(62),serial:n(64),serialOrdered:n(23)}},function(a,e,n){var i=n(18),o=n(21),s=n(22);a.exports=function(a,e,n){var t=o(a);for(;t.index<(t.keyedList||a).length;)i(a,e,t,function(a,e){a?n(a,e):0!==Object.keys(t.jobs).length||n(null,t.results)}),t.index++;return s.bind(t,n)}},function(a,e){a.exports=function(a){var e="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;e?e(a):setTimeout(a,0)}},function(a,e,n){var i=n(23);a.exports=function(a,e,n){return i(a,e,null,n)}},function(a,e){a.exports=function(a,e){return Object.keys(e).forEach(function(n){a[n]=a[n]||e[n]}),a}}]);
//# sourceMappingURL=post-upload-oss.js.map