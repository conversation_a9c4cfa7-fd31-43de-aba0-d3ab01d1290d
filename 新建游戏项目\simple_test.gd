extends Node

# 简单的功能测试
func _ready():
	print("=== 简单功能测试 ===")

	# 测试Card创建
	test_card_creation()

	# 测试Deck创建
	test_deck_creation()

	# 测试Player创建
	test_player_creation()

	# 测试GameManager创建
	test_game_manager_creation()

	print("=== 测试完成 ===")

func test_card_creation():
	print("\n--- 测试Card创建 ---")
	
	try:
		var CardScene = load("res://scenes/Card.tscn")
		var card = CardScene.instantiate()
		card.suit = 0  # SPADES
		card.rank = 3  # THREE
		print("✓ Card创建成功: ", card.get_card_name())
		card.queue_free()
	except:
		print("✗ Card创建失败")

func test_deck_creation():
	print("\n--- 测试Deck创建 ---")
	
	try:
		var DeckScript = load("res://scripts/Deck.gd")
		var deck = DeckScript.new()
		add_child(deck)
		print("✓ Deck创建成功，共 ", deck.get_card_count(), " 张牌")
		deck.queue_free()
	except:
		print("✗ Deck创建失败")

func test_player_creation():
	print("\n--- 测试Player创建 ---")

	try:
		var PlayerScript = load("res://scripts/Player.gd")
		var player = PlayerScript.new()
		player.player_name = "测试玩家"
		player.player_type = PlayerScript.PlayerType.HUMAN
		print("✓ Player创建成功: ", player.player_name)
		player.queue_free()
	except:
		print("✗ Player创建失败")

func test_game_manager_creation():
	print("\n--- 测试GameManager创建 ---")

	try:
		var GameManagerScript = load("res://scripts/GameManager.gd")
		var game_manager = GameManagerScript.new()
		add_child(game_manager)
		print("✓ GameManager创建成功")
		print("✓ 玩家数量: ", game_manager.get_players().size())

		# 等待一帧让初始化完成
		await get_tree().process_frame

		# 检查玩家是否正确创建
		var players = game_manager.get_players()
		for i in range(players.size()):
			print("  玩家", i, ": ", players[i].player_name, " (", players[i].player_type, ")")

		game_manager.queue_free()
	except:
		print("✗ GameManager创建失败")
