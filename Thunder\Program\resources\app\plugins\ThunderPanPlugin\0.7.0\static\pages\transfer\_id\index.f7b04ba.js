(window.webpackJsonp=window.webpackJsonp||[]).push([[22],{1511:function(t,e,n){"use strict";n.r(e);n(10),n(11),n(32),n(41),n(44),n(7);var s=n(33),r=n(40),a=n(70),i=n(71),o=n(37),c=(n(4),n(29)),u=(n(1342),n(1343),n(1344),n(1345),n(1401),n(1402),n(18)),l=n(82),d=n.n(l),f=n(1408),p=n(174),h=n(686),k=n(58);function v(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,s=Object(o.a)(t);if(e){var r=Object(o.a)(this).constructor;n=Reflect.construct(s,arguments,r)}else n=s.apply(this,arguments);return Object(i.a)(this,n)}}var b=function(t,e,n,s){var r,a=arguments.length,i=a<3?e:null===s?s=Object.getOwnPropertyDescriptor(e,n):s;if("object"===("undefined"==typeof Reflect?"undefined":Object(c.a)(Reflect))&&"function"==typeof Reflect.decorate)i=Reflect.decorate(t,e,n,s);else for(var o=t.length-1;o>=0;o--)(r=t[o])&&(i=(a<3?r(i):a>3?r(e,n,i):r(e,n))||i);return a>3&&i&&Object.defineProperty(e,n,i),i},y=function(t,e,n,s){return new(n||(n=Promise))((function(r,a){function i(t){try{c(s.next(t))}catch(t){a(t)}}function o(t){try{c(s.throw(t))}catch(t){a(t)}}function c(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,o)}c((s=s.apply(t,e||[])).next())}))},_=function(t){Object(a.a)(n,t);var e=v(n);function n(){var t;return Object(s.a)(this,n),(t=e.apply(this,arguments)).visible=!0,t.isLoadMoreTaskStatusShow=!1,t}return Object(r.a)(n,[{key:"mounted",value:function(){var t=this;this.task.statuses[0]||this.$store.dispatch("url-task-list/ipcGetSingleTaskStatus",{taskId:this.$route.params.id}).then((function(){t.isLoadMoreTaskStatusShow=!0}))}},{key:"back",value:function(){this.$router.back()}},{key:"close",value:function(){return y(this,void 0,void 0,regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,d.a.getCurrentWindow().__resolve();case 2:t.sent.hide();case 4:case"end":return t.stop()}}),t)})))}},{key:"dblclickCloudAdd",value:function(t){return y(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:["PHASE_TYPE_RUNNING","PHASE_TYPE_PENDING"].includes(t.phase)&&this.topSpeedCloudConfig?Object(k.createTransferVip)():Object(h.consumeDriveTask)(t,"transmission_yun_add_list_double_click");case 1:case"end":return e.stop()}}),e,this)})))}},{key:"loadMoreTaskStatus",value:function(){var t=this;this.$store.dispatch("url-task-list/ipcGetSingleTaskStatus",{taskId:this.$route.params.id,refresh:!1}).then((function(){t.isLoadMoreTaskStatusShow=!0})).catch((function(e){t.$message({message:"接口出现异常",type:"error",position:"middle",duration:1e3}),console.error(e)}))}},{key:"isDisable",value:function(t){return!![p.AuditStatus.SENSITIVE_RESOURCE,p.AuditStatus.SENSITIVE_WORD].includes(this.$sget(t,"reference_resource","audit","status"))}},{key:"task",get:function(){return this.$store.getters["url-task-list/getUrlTask"](this.$route.params.id)}},{key:"topSpeedCloudConfig",get:function(){return this.$store.state["url-task-list"].cloudAddGlobalConfig.topSpeedCloudConfig}}]),n}(u.Vue),m=_=b([Object(u.Component)({name:"TaskId",components:{TransferItem:f.a}})],_),S=n(72),g=Object(S.a)(m,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("td-dialog",{attrs:{"custom-class":"xly-transport",visible:t.visible,"footer-enabled":!1},on:{"update:visible":function(e){t.visible=e},ok:function(e){t.visible=!1},click:function(e){return e.stopPropagation(),t.close(e)}}},[n("div",{staticClass:"xly-transport__back",on:{click:function(e){return e.stopPropagation(),t.back(e)}}},[n("i",{staticClass:"xly-icon-arrow-left"}),t._v(" "),n("span",{staticClass:"title"},[t._v(t._s(this.task.file_name))])]),t._v(" "),n("div",{staticClass:"xly-transport__content xly-transport__content--add"},[n("ul",{staticClass:"xly-transport__items"},[t._l(t.task.statuses,(function(e){return n("li",{key:e.id,staticClass:"xly-transport__item",class:{"is-disabled":t.isDisable(e)},on:{dblclick:function(n){return t.dblclickCloudAdd(e)}}},[n("TransferItem",{attrs:{task:e,type:"urlTask"}})],1)})),t._v(" "),t.isLoadMoreTaskStatusShow?n("div",{directives:[{name:"load",rawName:"v-load",value:{func:t.loadMoreTaskStatus},expression:"{ func: loadMoreTaskStatus }"}],staticStyle:{height:"20px"}}):t._e()],2)])])}),[],!1,null,null,null);e.default=g.exports}}]);