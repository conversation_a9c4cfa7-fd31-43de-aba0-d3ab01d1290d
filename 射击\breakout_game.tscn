[gd_scene load_steps=10 format=3 uid="uid://c4f77laem850m"]

[ext_resource type="Script" path="res://paddle.gd" id="1_2yjnp"]
[ext_resource type="Script" path="res://ball.gd" id="2_ixnwl"]
[ext_resource type="Script" path="res://brick.gd" id="3_lfcxs"]
[ext_resource type="Script" path="res://game_manager.gd" id="4_ixnwl"]
[ext_resource type="Script" path="res://sound_manager.gd" id="5_ixnwl"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
extents = Vector2(50, 10)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2"]
extents = Vector2(10, 10)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_3"]
extents = Vector2(40, 15)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_4"]
extents = Vector2(500, 10)

[node name="BreakoutGame" type="Node2D"]
script = ExtResource("4_ixnwl")

[node name="Paddle" type="CharacterBody2D" parent="."]
position = Vector2(400, 550)
script = ExtResource("1_2yjnp")

[node name="ColorRect" type="ColorRect" parent="Paddle"]
offset_left = -50.0
offset_top = -10.0
offset_right = 50.0
offset_bottom = 10.0
color = Color(0.2, 0.6, 1.0, 1.0)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Paddle"]
shape = SubResource("RectangleShape2D_1")

[node name="Ball" type="CharacterBody2D" parent="."]
position = Vector2(400, 500)
script = ExtResource("2_ixnwl")

[node name="ColorRect" type="ColorRect" parent="Ball"]
offset_left = -10.0
offset_top = -10.0
offset_right = 10.0
offset_bottom = 10.0
color = Color(1.0, 0.5, 0.0, 1.0)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Ball"]
shape = SubResource("RectangleShape2D_2")

[node name="Walls" type="Node2D" parent="."]

[node name="TopWall" type="StaticBody2D" parent="Walls"]
position = Vector2(400, -10)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Walls/TopWall"]
shape = SubResource("RectangleShape2D_4")

[node name="LeftWall" type="StaticBody2D" parent="Walls"]
position = Vector2(-10, 300)
rotation = 1.5708

[node name="CollisionShape2D" type="CollisionShape2D" parent="Walls/LeftWall"]
shape = SubResource("RectangleShape2D_4")

[node name="RightWall" type="StaticBody2D" parent="Walls"]
position = Vector2(810, 300)
rotation = 1.5708

[node name="CollisionShape2D" type="CollisionShape2D" parent="Walls/RightWall"]
shape = SubResource("RectangleShape2D_4")

[node name="Bricks" type="Node2D" parent="."]
position = Vector2(120, 100)

[node name="Label" type="Label" parent="."]
offset_left = 20.0
offset_top = 20.0
offset_right = 200.0
offset_bottom = 60.0
text = "得分: 0"
font_size = 24

[node name="SoundManager" type="Node" parent="."]
script = ExtResource("5_ixnwl")