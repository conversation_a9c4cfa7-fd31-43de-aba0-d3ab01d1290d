class_name Player
extends Node

# 玩家类 - 表示一个斗地主玩家
enum PlayerType { HUMAN, AI }
enum Role { FARMER, LANDLORD }

@export var player_type: PlayerType = PlayerType.HUMAN
@export var player_name: String = "玩家"
@export var role: Role = Role.FARMER
@export var is_current_player: bool = false

var hand_cards: Array[Card] = []
var selected_cards: Array[Card] = []
var score: int = 0
var position_index: int = 0  # 0=下方(人类), 1=左侧, 2=上方

signal cards_played(cards: Array[Card])
signal turn_passed()
signal role_selected(role: Role)

func _ready():
	pass

func add_card(card: Card):
	"""添加卡牌到手牌"""
	hand_cards.append(card)
	card.card_clicked.connect(_on_card_clicked)
	sort_hand_cards()

func remove_cards(cards: Array[Card]):
	"""从手牌中移除指定卡牌"""
	for card in cards:
		hand_cards.erase(card)
		if card.card_clicked.is_connected(_on_card_clicked):
			card.card_clicked.disconnect(_on_card_clicked)

func _on_card_clicked(card: Card):
	"""处理卡牌点击事件"""
	if not is_current_player or player_type != PlayerType.HUMAN:
		return
	
	if card.is_selected:
		selected_cards.erase(card)
	else:
		selected_cards.append(card)

func sort_hand_cards():
	"""按照卡牌大小排序手牌"""
	hand_cards.sort_custom(func(a: Card, b: Card): return a.get_card_value() < b.get_card_value())

func get_hand_count() -> int:
	"""获取手牌数量"""
	return hand_cards.size()

func play_selected_cards():
	"""出选中的牌"""
	if selected_cards.is_empty():
		return false
	
	var cards_to_play = selected_cards.duplicate()
	
	# 验证出牌是否合法
	if not is_valid_play(cards_to_play):
		return false
	
	# 移除已出的牌
	remove_cards(cards_to_play)
	selected_cards.clear()
	
	# 发出信号
	cards_played.emit(cards_to_play)
	return true

func pass_turn():
	"""过牌"""
	clear_selection()
	turn_passed.emit()

func clear_selection():
	"""清除所有选中的卡牌"""
	for card in selected_cards:
		card.set_selected(false)
	selected_cards.clear()

func is_valid_play(cards: Array[Card]) -> bool:
	"""检查出牌是否合法（简化版本）"""
	if cards.is_empty():
		return false
	
	# 这里可以添加更复杂的出牌规则验证
	# 目前只检查基本情况
	return true

func set_current_player(current: bool):
	"""设置是否为当前玩家"""
	is_current_player = current
	
	# 更新手牌的可交互状态
	for card in hand_cards:
		card.set_playable(current and player_type == PlayerType.HUMAN)

func ai_make_decision() -> Array[Card]:
	"""AI决策出牌（简化版本）"""
	if player_type != PlayerType.AI or not is_current_player:
		return []
	
	# 简单AI：随机出一张牌
	if not hand_cards.is_empty():
		var random_card = hand_cards[randi() % hand_cards.size()]
		return [random_card]
	
	return []

func ai_should_pass() -> bool:
	"""AI决定是否过牌"""
	# 简单逻辑：30%概率过牌
	return randf() < 0.3

func get_role_name() -> String:
	"""获取角色名称"""
	return "地主" if role == Role.LANDLORD else "农民"

func calculate_score(is_winner: bool, multiplier: int = 1):
	"""计算得分"""
	if is_winner:
		if role == Role.LANDLORD:
			score += 2 * multiplier  # 地主获胜得2分
		else:
			score += 1 * multiplier  # 农民获胜得1分
	else:
		if role == Role.LANDLORD:
			score -= 2 * multiplier  # 地主失败扣2分
		else:
			score -= 1 * multiplier  # 农民失败扣1分

func has_won() -> bool:
	"""检查是否获胜"""
	return hand_cards.is_empty()

func get_display_position() -> Vector2:
	"""根据位置索引获取显示位置"""
	match position_index:
		0:  # 下方（人类玩家）
			return Vector2(640, 600)
		1:  # 左侧
			return Vector2(100, 360)
		2:  # 上方
			return Vector2(640, 120)
		_:
			return Vector2(640, 360)

func get_card_layout_direction() -> Vector2:
	"""获取卡牌排列方向"""
	match position_index:
		0:  # 下方：水平排列
			return Vector2(1, 0)
		1:  # 左侧：垂直排列
			return Vector2(0, 1)
		2:  # 上方：水平排列
			return Vector2(1, 0)
		_:
			return Vector2(1, 0)
