# 音效管理器脚本
extends Node

# 预加载音效资源
var break_sound_generator: AudioStreamGenerator

# 初始化
func _ready():
	# 创建音效生成器
	break_sound_generator = AudioStreamGenerator.new()
	break_sound_generator.mix_rate = 44100
	break_sound_generator.buffer_length = 0.1  # 100ms

# 生成砖块破碎音效
func generate_break_sound() -> AudioStreamGenerator:
	# 创建一个新的音效生成器实例
	var sound = AudioStreamGenerator.new()
	sound.mix_rate = 44100
	sound.buffer_length = 0.1
	return sound

# 播放砖块破碎音效
func play_break_sound(player: AudioStreamPlayer):
	# 设置音效
	player.stream = generate_break_sound()
	
	# 先播放音效，这样AudioStreamPlayer才会处于活动状态
	player.play()
	
	# 获取播放缓冲区
	var playback = player.get_stream_playback()
	
	# 填充缓冲区（简单的和弦音效）
	# 使用正确的API - push_frame而不是get_buffer
	var buffer_size = playback.get_frames_available()
	for i in range(buffer_size):
		# 和弦音效，音量随时间衰减
		var decay = 1.0 - (float(i) / buffer_size)
		var sample = sin(2.0 * PI * 440.0 * i / 44100.0) * decay  # 440Hz A音
		playback.push_frame(Vector2(sample, sample))  # 立体声，左右声道相同
