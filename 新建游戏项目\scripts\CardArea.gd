extends Control

# 卡牌区域管理类
@export var card_spacing: float = 60.0
@export var max_cards_per_row: int = 13
@export var row_spacing: float = 80.0
@export var is_player_hand: bool = false  # 是否是玩家手牌区域
@export var selected_offset: float = -30.0  # 选中卡牌的偏移量

var cards = []
var card_scene: PackedScene

signal card_selected(card)

func _ready():
	# 预加载卡牌场景
	card_scene = preload("res://scenes/Card.tscn")

func add_card(card):
	cards.append(card)
	add_child(card)
	card.card_clicked.connect(_on_card_clicked)
	arrange_cards()

func add_cards(new_cards):
	for card in new_cards:
		cards.append(card)
		add_child(card)
		card.card_clicked.connect(_on_card_clicked)
	arrange_cards()

func remove_card(card):
	if card in cards:
		cards.erase(card)
		card.queue_free()
		arrange_cards()

func remove_cards(cards_to_remove):
	for card in cards_to_remove:
		if card in cards:
			cards.erase(card)
			card.queue_free()
	arrange_cards()

func clear_cards():
	for card in cards:
		card.queue_free()
	cards.clear()

func arrange_cards():
	if cards.is_empty():
		return

	if is_player_hand:
		arrange_cards_fan_style()
	else:
		arrange_cards_grid_style()

func arrange_cards_fan_style():
	# 扇形排列（用于玩家手牌）
	var area_size = size
	var total_cards = cards.size()
	var card_width = 60
	var card_height = 90

	# 扇形参数
	var center_x = area_size.x / 2
	var center_y = area_size.y + 100  # 扇形中心在区域下方
	var radius = 280
	var max_angle = 50  # 最大扇形角度

	# 计算角度间距
	var total_angle = min(max_angle, total_cards * 2.5)
	var angle_step = total_angle / max(1, total_cards - 1) if total_cards > 1 else 0
	var start_angle = -total_angle / 2

	for i in range(total_cards):
		var card = cards[i]

		# 计算扇形位置
		var angle = start_angle + i * angle_step
		var angle_rad = deg_to_rad(angle)

		var base_x = center_x + sin(angle_rad) * radius - card_width / 2
		var base_y = center_y - cos(angle_rad) * radius - card_height

		# 如果卡牌被选中，向上偏移
		var offset_y = selected_offset if card.is_selected else 0

		var final_pos = Vector2(base_x, base_y + offset_y)
		card.set_position_smoothly(final_pos)
		card.rotation = angle_rad * 0.3  # 轻微旋转

		# 设置层级
		card.z_index = i

func arrange_cards_grid_style():
	# 网格排列（用于其他区域）
	var area_size = size
	var total_cards = cards.size()

	# 计算每行的卡牌数量
	var cards_per_row = min(max_cards_per_row, total_cards)
	var rows = ceil(float(total_cards) / float(cards_per_row))

	# 计算起始位置
	var total_width = (cards_per_row - 1) * card_spacing
	var start_x = (area_size.x - total_width) / 2
	var start_y = (area_size.y - (rows - 1) * row_spacing) / 2

	# 排列卡牌
	for i in range(total_cards):
		var card = cards[i]
		var row = int(i / cards_per_row)
		var col = i % cards_per_row

		# 如果是最后一行且卡牌数量不足，居中显示
		var current_row_cards = min(cards_per_row, total_cards - row * cards_per_row)
		var row_start_x = start_x
		if current_row_cards < cards_per_row:
			var row_width = (current_row_cards - 1) * card_spacing
			row_start_x = (area_size.x - row_width) / 2

		var pos_x = row_start_x + col * card_spacing
		var pos_y = start_y + row * row_spacing

		card.set_position_smoothly(Vector2(pos_x, pos_y))
		card.rotation = 0
		card.z_index = i

func _on_card_clicked(card):
	# 切换卡牌选择状态
	card.set_selected(!card.is_selected)

	# 重新排列卡牌以显示选择效果
	if is_player_hand:
		arrange_cards()

	card_selected.emit(card)

func get_cards():
	return cards

func get_card_count() -> int:
	return cards.size()

func create_card_from_data(suit, rank):
	var card_instance = card_scene.instantiate()
	card_instance.suit = suit
	card_instance.rank = rank
	return card_instance

func highlight_cards(cards_to_highlight, highlight: bool = true):
	for card in cards_to_highlight:
		if card in cards:
			card.set_selected(highlight)

func get_selected_cards():
	var selected = []
	for card in cards:
		if card.is_selected:
			selected.append(card)
	return selected

func clear_selection():
	for card in cards:
		card.set_selected(false)

func sort_cards():
	cards.sort_custom(func(a, b): return a.get_card_value() < b.get_card_value())
	arrange_cards()

func set_cards_face_up(face_up: bool):
	for card in cards:
		card.is_face_up = face_up
		card.update_card_display()

func animate_cards_to_center():
	var center = size / 2
	for card in cards:
		var tween = create_tween()
		tween.tween_property(card, "position", center, 0.5)
		tween.tween_property(card, "scale", Vector2.ZERO, 0.3)
		tween.tween_callback(card.queue_free)
	cards.clear()
