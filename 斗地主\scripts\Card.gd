class_name Card
extends Area2D

# 卡牌类 - 表示一张扑克牌
enum Suit { SPADES, HEARTS, CLUBS, DIAMONDS, JOKER }
enum Rank { THREE = 3, FOUR, FIVE, SIX, SEVEN, EIGHT, NINE, TEN, JACK, QUEEN, KING, ACE, TWO, SMALL_JOKER, BIG_JOKER }

@export var suit: Suit
@export var rank: Rank
@export var is_selected: bool = false
@export var is_playable: bool = true

var original_position: Vector2
var original_scale: Vector2 = Vector2(1.0, 1.0)
var hover_scale: Vector2 = Vector2(1.1, 1.1)
var selected_offset: Vector2 = Vector2(0, -20)

@onready var sprite: Sprite2D = $Sprite2D
@onready var collision_shape: CollisionShape2D = $CollisionShape2D

signal card_clicked(card: Card)
signal card_hovered(card: Card)
signal card_unhovered(card: Card)

func _ready():
	# 设置卡牌的基本属性
	original_position = position
	original_scale = scale
	
	# 连接信号
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	input_event.connect(_on_input_event)
	
	# 设置卡牌图片
	update_card_texture()

func _on_mouse_entered():
	if is_playable:
		scale = hover_scale
		card_hovered.emit(self)

func _on_mouse_exited():
	if is_playable and not is_selected:
		scale = original_scale
		card_unhovered.emit(self)

func _on_input_event(viewport: Node, event: InputEvent, shape_idx: int):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		if is_playable:
			toggle_selection()
			card_clicked.emit(self)

func toggle_selection():
	is_selected = !is_selected
	update_visual_state()

func set_selected(selected: bool):
	is_selected = selected
	update_visual_state()

func update_visual_state():
	if is_selected:
		position = original_position + selected_offset
		scale = hover_scale
	else:
		position = original_position
		scale = original_scale

func set_playable(playable: bool):
	is_playable = playable
	modulate = Color.WHITE if playable else Color.GRAY

func update_card_texture():
	# 根据花色和点数设置卡牌纹理
	var texture_path = get_card_texture_path()
	if ResourceLoader.exists(texture_path):
		sprite.texture = load(texture_path)
	else:
		# 如果没有找到纹理，使用默认纹理或创建简单的文本显示
		create_simple_card_display()

func get_card_texture_path() -> String:
	# 构建卡牌纹理路径
	if suit == Suit.JOKER:
		if rank == Rank.SMALL_JOKER:
			return "res://assets/cards/joker_small.png"
		else:
			return "res://assets/cards/joker_big.png"
	else:
		var suit_name = ["spades", "hearts", "clubs", "diamonds"][suit]
		return "res://assets/cards/%s_%d.png" % [suit_name, rank]

func create_simple_card_display():
	# 创建简单的卡牌显示（当没有图片资源时）
	var texture = ImageTexture.new()
	var image = Image.create(80, 120, false, Image.FORMAT_RGB8)
	image.fill(Color.WHITE)
	texture.set_image(image)
	sprite.texture = texture
	
	# 添加文本标签显示卡牌信息
	var label = Label.new()
	label.text = get_card_display_text()
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.position = Vector2(-40, -60)
	label.size = Vector2(80, 120)
	add_child(label)

func get_card_display_text() -> String:
	if suit == Suit.JOKER:
		return "小王" if rank == Rank.SMALL_JOKER else "大王"
	
	var suit_symbols = ["♠", "♥", "♣", "♦"]
	var rank_names = {
		3: "3", 4: "4", 5: "5", 6: "6", 7: "7", 8: "8", 9: "9", 10: "10",
		11: "J", 12: "Q", 13: "K", 14: "A", 15: "2"
	}
	
	return suit_symbols[suit] + rank_names.get(rank, str(rank))

func get_card_value() -> int:
	# 返回卡牌的大小值（用于比较）
	if rank == Rank.BIG_JOKER:
		return 17
	elif rank == Rank.SMALL_JOKER:
		return 16
	elif rank == Rank.TWO:
		return 15
	elif rank == Rank.ACE:
		return 14
	else:
		return rank

func is_same_rank(other_card: Card) -> bool:
	return rank == other_card.rank

func is_greater_than(other_card: Card) -> bool:
	return get_card_value() > other_card.get_card_value()
