"""
植物大战僵尸内存读取引擎
支持进程查找、内存读写、数据类型转换等核心功能
"""

import ctypes
from ctypes import wintypes
import struct
import psutil
import time
from typing import Optional, List, Dict, Any, Union

# Windows API 常量
PROCESS_ALL_ACCESS = 0x1F0FFF
PROCESS_VM_READ = 0x0010
PROCESS_VM_WRITE = 0x0020
PROCESS_VM_OPERATION = 0x0008
PROCESS_QUERY_INFORMATION = 0x0400

# 内存保护常量
PAGE_EXECUTE_READWRITE = 0x40
PAGE_READWRITE = 0x04
PAGE_READONLY = 0x02

class MemoryEngine:
    """内存操作引擎类"""
    
    def __init__(self):
        self.process_handle = None
        self.process_id = None
        self.process_name = None
        self.base_address = None
        
        # 加载Windows API
        self.kernel32 = ctypes.windll.kernel32
        self.user32 = ctypes.windll.user32
        
    def find_process(self, process_name: str) -> bool:
        """查找指定名称的进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() == process_name.lower():
                    self.process_id = proc.info['pid']
                    self.process_name = process_name
                    return self.attach_process()
            return False
        except Exception as e:
            print(f"查找进程失败: {e}")
            return False
    
    def attach_process(self) -> bool:
        """附加到进程"""
        if not self.process_id:
            return False
            
        try:
            # 打开进程句柄
            self.process_handle = self.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, self.process_id
            )
            
            if not self.process_handle:
                print(f"无法打开进程 {self.process_id}")
                return False
                
            # 获取模块基址
            self.base_address = self.get_module_base_address()
            return True
            
        except Exception as e:
            print(f"附加进程失败: {e}")
            return False
    
    def get_module_base_address(self) -> Optional[int]:
        """获取主模块基址"""
        try:
            process = psutil.Process(self.process_id)
            for module in process.memory_maps():
                if self.process_name.lower() in module.path.lower():
                    return int(module.addr.split('-')[0], 16)
            return None
        except Exception as e:
            print(f"获取模块基址失败: {e}")
            return None
    
    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """读取内存数据"""
        if not self.process_handle:
            return None
            
        try:
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.c_size_t()
            
            success = self.kernel32.ReadProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            )
            
            if success and bytes_read.value == size:
                return buffer.raw
            return None
            
        except Exception as e:
            print(f"读取内存失败: {e}")
            return None
    
    def write_memory(self, address: int, data: bytes) -> bool:
        """写入内存数据"""
        if not self.process_handle:
            return False
            
        try:
            bytes_written = ctypes.c_size_t()
            
            success = self.kernel32.WriteProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                data,
                len(data),
                ctypes.byref(bytes_written)
            )
            
            return success and bytes_written.value == len(data)
            
        except Exception as e:
            print(f"写入内存失败: {e}")
            return False
    
    def read_int32(self, address: int) -> Optional[int]:
        """读取32位整数"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<I', data)[0]
        return None
    
    def read_float(self, address: int) -> Optional[float]:
        """读取浮点数"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<f', data)[0]
        return None
    
    def read_string(self, address: int, length: int = 256) -> Optional[str]:
        """读取字符串"""
        data = self.read_memory(address, length)
        if data:
            try:
                # 查找字符串结束符
                null_pos = data.find(b'\x00')
                if null_pos != -1:
                    data = data[:null_pos]
                return data.decode('utf-8', errors='ignore')
            except:
                return data.decode('ascii', errors='ignore')
        return None
    
    def write_int32(self, address: int, value: int) -> bool:
        """写入32位整数"""
        data = struct.pack('<I', value)
        return self.write_memory(address, data)
    
    def write_float(self, address: int, value: float) -> bool:
        """写入浮点数"""
        data = struct.pack('<f', value)
        return self.write_memory(address, data)
    
    def scan_memory_pattern(self, pattern: bytes, mask: str = None) -> List[int]:
        """扫描内存模式"""
        results = []
        if not self.process_handle:
            return results
            
        try:
            # 获取进程内存信息
            process = psutil.Process(self.process_id)
            
            for region in process.memory_maps():
                start_addr = int(region.addr.split('-')[0], 16)
                end_addr = int(region.addr.split('-')[1], 16)
                size = end_addr - start_addr
                
                # 读取内存区域
                data = self.read_memory(start_addr, size)
                if not data:
                    continue
                    
                # 搜索模式
                for i in range(len(data) - len(pattern) + 1):
                    if self._match_pattern(data[i:i+len(pattern)], pattern, mask):
                        results.append(start_addr + i)
                        
        except Exception as e:
            print(f"内存扫描失败: {e}")
            
        return results
    
    def _match_pattern(self, data: bytes, pattern: bytes, mask: str = None) -> bool:
        """匹配内存模式"""
        if mask is None:
            return data == pattern
            
        if len(data) != len(pattern) or len(data) != len(mask):
            return False
            
        for i in range(len(data)):
            if mask[i] == 'x' and data[i] != pattern[i]:
                return False
                
        return True
    
    def get_pointer_chain(self, base_addr: int, offsets: List[int]) -> Optional[int]:
        """获取指针链地址"""
        current_addr = base_addr
        
        for i, offset in enumerate(offsets):
            if i == len(offsets) - 1:
                # 最后一个偏移，直接加上
                return current_addr + offset
            else:
                # 读取指针值
                pointer_value = self.read_int32(current_addr + offset)
                if pointer_value is None:
                    return None
                current_addr = pointer_value
                
        return current_addr
    
    def is_process_running(self) -> bool:
        """检查进程是否仍在运行"""
        if not self.process_id:
            return False
            
        try:
            process = psutil.Process(self.process_id)
            return process.is_running()
        except:
            return False
    
    def close(self):
        """关闭进程句柄"""
        if self.process_handle:
            self.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
            self.process_id = None
            self.process_name = None
            self.base_address = None
    
    def __del__(self):
        """析构函数"""
        self.close()
