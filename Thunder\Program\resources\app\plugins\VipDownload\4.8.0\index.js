module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=26)}([function(e,t,n){"use strict";var r=n(18),i=n(45),o=Object.prototype.toString;function s(e){return"[object Array]"===o.call(e)}function a(e){return null!==e&&"object"==typeof e}function c(e){return"[object Function]"===o.call(e)}function l(e,t){if(null!==e&&void 0!==e)if("object"!=typeof e&&(e=[e]),s(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:s,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:i,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:c,isStream:function(e){return a(e)&&c(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:l,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,i=arguments.length;r<i;r++)l(arguments[r],n);return t},extend:function(e,t,n){return l(t,function(t,i){e[i]=n&&"function"==typeof t?r(t,n):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},function(e,t,n){e.exports=n(30)(65)},function(e,t){e.exports=require("path")},function(e,t){e.exports=require("events")},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(32),s=n(16),a=n(15);function c(e){a.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let l=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends i.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,r){do{if(!n&&this.client)break;if(t||(t=s.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase()))break;if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===s.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");let i=new o.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=i,n||(this.client=i),i.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(i,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action){a.information("client on object freer",e);do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0)}else if("call_client_by_id"===e.action)this.callFunctionById(i,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(i,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(i,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===i&&this.emit("stat_call_function_back",i.getContext(),e);const n=this.retCallbackMap[t.rid].callback;n&&(t.success?n(null,t.data):n(t.error,t.data)),delete this.retCallbackMap[t.rid]}}),i.on("error",e=>{r&&r("error",e),this.emit("socket-error",e,i.getContext(),n,i.isInprocess()),delete this.singletonMap[t.toLowerCase()]}),i.isInprocess()?(r&&r("connect"),this.emit("connect",i.getContext(),n,!0)):i.on("connect",()=>{r&&r("connect"),this.emit("connect",i.getContext(),n,!1)}),i.on("end",()=>{let e=i.isInprocess();a.information("server is ended, and this client emit end",t,n,e),r&&r("end",i.getContext(),n,e),this.emit("end",i.getContext(),n,e),delete this.singletonMap[t.toLowerCase()]}),this.registry(i)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return r(this,void 0,void 0,function*(){let r=null,i=yield this.crossCallServerFunctionEx(e,t,...n);return i&&(r=i[0]),r})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(r,t,...n)}}crossIsRemoteClientExist(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(r,t,n)}})}crossCallRemoteClientFunction(e,t,n,...r){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(i,t,n,...r)}}crossAttachServerEvent(e,t,n){let r=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");r=this.internalAttachServerEvent(i,t,n)}return r}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(r,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(r,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.generateId();const i={alias:t,action:"register",rid:r};let o=(e,r)=>{e?(a.error("register error",e.message),n(r)):n(t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(a.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===s.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,r)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let r=this.generateId();const i={action:"check_server_function_exist",method:t,rid:r};let o=(e,t)=>{n(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((r,i)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let i=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const o={rid:i,method:t,args:n};let s=(t,n)=>{t?(a.error("callServerFunction error",t,e.getContext()),r([null,t])):r([n,void 0])};this.retCallbackMap[i]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,r)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let r=this.generateId();const i={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:r};let o=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((r,i)=>{do{if(!e){r(!1);break}if(!t){r(!1);break}if(!n){r(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){r(!(!this.apis||!this.apis[n]));break}let i=this.generateId();const o={action:"check_client_function_exist",method:n,rid:i,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let s=(e,t)=>{r(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalCallRemoteClientFunction(e,t,n,...r){return new Promise((i,o)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"remote client alias is not specifed"]);break}if(!n){i([null,"funcName is not specifed"]);break}let o=(e,t)=>{e?(a.information("callRemoteClientFunction",e.message),i([null,e])):i([t,void 0])};if(r)for(let e=0;e<r.length;e++)r[e]=this.convertFunction2IdEx(r[e]);let s=this.generateId();const c={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:r,rid:s};this.retCallbackMap[s]=Object.assign({callback:o},c),this.sendAdapter(e,c)}while(0)})}internalAttachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(r)||(this.eventCallbackMaps[r]={}),this.eventCallbackMaps[r].hasOwnProperty(t)||(this.eventCallbackMaps[r][t]={}),s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let i=this.generateId();return this.eventCallbackMaps[r][t][i]=n,i}internalDetachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;delete this.eventCallbackMaps[r][t][n],s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;let e=this.eventCallbackMaps[r][t];for(let t in e){let r=e[t];r&&r.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...r){let i=void 0,o=!1;do{const s=this.contextCallbackMap[t];if(!s)break;let a=void 0,c=void 0;try{a=s.apply(null,...r)}catch(e){c=e.message;break}if(void 0===n||null===n)break;if(i={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==c){i.error=c;break}if(a&&a.then){a.then(t=>{i.data=this.convertFunction2Id(t),i.success=!0,this.sendAdapter(e,i)}).catch(t=>{i.error=t instanceof Error?t.message:t,this.sendAdapter(e,i)}),o=!0;break}i.success=!0,i.data=this.convertFunction2Id(a)}while(0);!o&&i&&this.sendAdapter(e,i)}convertFunction2Id(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t=n}else if(e&&"object"==typeof e)for(let t in e){let n=e[t];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,e[t]=r}else n&&"object"==typeof n&&(e[t]=this.convertFunction2Id(n))}return t}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e)for(let t in e){let n=e[t];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,e[t]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":r}}else n&&"object"==typeof n&&(e[t]=this.convertFunction2IdEx(n))}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if("object"!=typeof e)break;let r=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(r){n=((...e)=>{this.callRemoteContextById(t,r,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:r},c);break}for(let n in e){let r=e[n];e[n]=this.decodeParameter(r,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,r=!1;do{if(!t)break;let i=t.method;if(!i)break;let o=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(o):[].concat(o)},!this.apis||!this.apis[i]){n.error=`callRegisterFunction ${i} is undefined`;break}let s=void 0,a=this.decodeParameter(t.args,t.src);try{s=this.apis[i].apply(null,[t.src].concat(a))}catch(e){n.error=e.message;break}if(s&&s.then){s.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),r=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(s)}while(0);a.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(e,n)}}),l=global.__xdasIPCClienInstance,t.client=l},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(12),o=n(29),s=n(2),a=n(1),c=n(31);class l{constructor(){this.mThunderVersionStr=void 0,this.mThunderVersionNumber=void 0,this.mWebPluginVersion=void 0}init(){this.loadThunderVersion().catch(),this.loadWebPluginVersion().catch()}get pluginName(){return c.name}get pluginWebviewName(){return`${c.name}-webview`}get pluginDialogRendererName(){return`${c.name}-dialog-renderer`}get binName(){return"VipDownload"}get thunderVersionNumber(){return this.mThunderVersionNumber}get thunderVersionString(){return this.mThunderVersionStr}get clientName(){return"xl_xdas"}get pluginVersion(){return c.version}get webPluginVersion(){return this.mWebPluginVersion}get currentTime(){return Math.floor((new Date).getTime()/1e3)}get currentMillisecond(){return(new Date).getTime()}combineUrlReferfromAidfrom(e,t,n){return""===n&&(n="0"),""===t&&(t="0"),-1!==e.indexOf("?")?e+=`&referfrom=${t}&aidfrom=${n}`:e+=`?referfrom=${t}&aidfrom=${n}`,e}isUrlInDomains(e,t){let n=!1;for(let r=0;r<t.length;++r)if(e.includes(t[r])){n=!0;break}return n}isPeeridMatchSha256Region(e,t,n){let r=!1,o=i.createHash("sha256");o.update(e);let s=o.digest("hex"),c=s[n=n?s.length-n:s.length-1];return c&&(c=c.toUpperCase(),t.includes(c)&&(r=!0)),a.default.getLogger("peer").info(s),r}loadThunderVersion(){return r(this,void 0,void 0,function*(){const{client:e}=yield Promise.resolve().then(()=>n(4)),t=yield e.callServerFunction("GetThunderVersion");this.mThunderVersionStr=t,this.mThunderVersionNumber=0;let r=this.mThunderVersionStr.split(".");if(r&&4===r.length){let e=Number(r[0]).valueOf(),t=Number(r[1]).valueOf(),n=Number(r[2]).valueOf(),i=128;this.mThunderVersionNumber=i*Math.pow(2,24)+e*Math.pow(2,16)+t*Math.pow(2,8)+n}})}loadWebPluginVersion(){return r(this,void 0,void 0,function*(){this.mWebPluginVersion="";let e=s.join(__rootDir,"../../ThunderXWebXDAS/VERION.txt"),t=yield o.FileSystemAWNS.readFileAW(e);t&&(this.mWebPluginVersion=t.toString())})}}t.PluginHelper=l,t.default=new l},function(e,t){e.exports=require("os")},function(e,t){e.exports=require("url")},function(e,t){e.exports=require("http")},function(e,t){e.exports=require("https")},function(e,t,n){"use strict";var r=n(0),i=n(47),o={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a,c={adapter:("undefined"!=typeof XMLHttpRequest?a=n(48):"undefined"!=typeof process&&(a=n(53)),a),transformRequest:[function(e,t){return i(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(s(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};c.headers={common:{Accept:"application/json, text/plain, */*"}},r.forEach(["delete","get","head"],function(e){c.headers[e]={}}),r.forEach(["post","put","patch"],function(e){c.headers[e]=r.merge(o)}),e.exports=c},function(e,t,n){"use strict";var r=n(20);e.exports=function(e,t,n,i,o){var s=new Error(e);return r(s,t,n,i,o)}},function(e,t){e.exports=require("crypto")},function(e,t){e.exports=require("fs")},function(e,t){e.exports=require("util")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(6),i=n(2);t.getDefaultPrex=function(){return i.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=i.basename(process.execPath,".exe"));let o=i.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(o="\\\\.\\pipe\\"+(o=(o=o.replace(/^\//,"")).replace(/\//g,"-"))),o},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(12),o=n(1).default.getLogger("Thunder.Util"),s=n(2);function a(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(e){let t=null;do{if(void 0===e||null===e)break;t=e.match(/[\/]?([^?]*)\?([^\s]*)/)?RegExp.$2:""}while(0);return t}function n(e){let t={};do{if(void 0===e||null===e)break;let n=/([^&=?]+)=([^&]*)/g;for(;n.exec(e);)t[RegExp.$1]=RegExp.$2}while(0);return t}function c(e){return n(t(e))}function l(e){let t=null;do{if(void 0===e||null===e)break;t=e.match(/[\/]?([^?]*)\?([^\s]*)/)?RegExp.$1:e}while(0);return t}e.formatSize=function(e,t){t=t||2;let n="0B";if("number"==typeof e&&e>0){let r=["B","KB","MB","GB","TB"],i=0,o=e;for(;o>=1e3&&!(i>=4);)o/=1024,i+=1;n=-1===String(o).indexOf(".")?o+r[i]:o.toFixed(t)+r[i]}return n},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){o.warn(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",r=!1,i=[],o=e.length;for(let s=0;s<o;s++){let c=e[s];if('"'!==c&&"'"!==c||(""===n?(r=!0,n=c):n===c&&(r=!1,n=""))," "!==c||r||s===o-1){if(s===o-1){let n=e.substring(t);""!==n.trim()&&i.push(a(n))}}else{let n=e.substring(t,s);""!==n.trim()&&i.push(a(n)),t=s+1}}return i},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,r)=>{e+=0===r?"?":"&",e+=`${n}=${t[n]}`}),e},e.getQueryString=function(e,t){return e&&t&&e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:""},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),r=t.split("."),i=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(r[e])>0){i=1;break}if(Number(n[e])-Number(r[e])<0){i=-1;break}}return i},e.parseDynamicUrlPath=t,e.parseDynamicUrlArgs=n,e.getUrlArgs=c,e.sleep=function(e){return r(this,void 0,void 0,function*(){return new Promise(t=>{setTimeout(()=>{t()},e)})})},e.getStaticPath=function(){let e=s.join(__rootDir,"static").replace("\\","/");for(;-1!==e.indexOf("\\");)e=e.replace("\\","/");return e},e.genarateMd5=function(e){let t=void 0,n=i.createHash("md5");return null!==n&&(t=n.update(e).digest("hex")),t},e.GetUrlHost=l,e.RepleaseUrlArgs=function(e,t){let n=Object.getOwnPropertyNames(e),r=c(t);n.forEach(t=>{e[t]&&(r[t]=e[t])}),n=Object.getOwnPropertyNames(r);let i=l(t);return n.forEach(e=>{i.indexOf("?")>0?i+=`&${e}=${r[e]}`:i+=`?${e}=${r[e]}`}),i}}(t.ThunderUtil||(t.ThunderUtil={}))},function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},function(e,t,n){"use strict";var r=n(11);e.exports=function(e,t,n){var i=n.config.validateStatus;n.status&&i&&!i(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},function(e,t,n){"use strict";e.exports=function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e}},function(e,t,n){"use strict";var r=n(0);function i(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var o;if(n)o=n(t);else if(r.isURLSearchParams(t))o=t.toString();else{var s=[];r.forEach(t,function(e,t){null!==e&&void 0!==e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),s.push(i(t)+"="+i(e))}))}),o=s.join("&")}return o&&(e+=(-1===e.indexOf("?")?"?":"&")+o),e}},function(e,t,n){var r=n(7),i=r.URL,o=n(8),s=n(9),a=n(54),c=n(55).Writable,l=n(56)("follow-redirects"),u={GET:!0,HEAD:!0,OPTIONS:!0,TRACE:!0},d=Object.create(null);function h(e,t){c.call(this),e.headers=e.headers||{},this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],e.host&&(e.hostname||(e.hostname=e.host),delete e.host),t&&this.on("response",t);var n=this;if(this._onNativeResponse=function(e){n._processResponse(e)},!e.pathname&&e.path){var r=e.path.indexOf("?");r<0?e.pathname=e.path:(e.pathname=e.path.substring(0,r),e.search=e.path.substring(r))}this._performRequest()}function f(e,t){clearTimeout(e._timeout),e._timeout=setTimeout(function(){e.emit("timeout")},t)}function p(){clearTimeout(this._timeout)}function m(e){var t={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(e).forEach(function(o){var s=o+":",c=n[s]=e[o],u=t[o]=Object.create(c);u.request=function(e,o,c){if("string"==typeof e){var u=e;try{e=v(new i(u))}catch(t){e=r.parse(u)}}else i&&e instanceof i?e=v(e):(c=o,o=e,e={protocol:s});return"function"==typeof o&&(c=o,o=null),(o=Object.assign({maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},e,o)).nativeProtocols=n,a.equal(o.protocol,s,"protocol mismatch"),l("options",o),new h(o,c)},u.get=function(e,t,n){var r=u.request(e,t,n);return r.end(),r}}),t}function g(){}function v(e){var t={protocol:e.protocol,hostname:e.hostname.startsWith("[")?e.hostname.slice(1,-1):e.hostname,hash:e.hash,search:e.search,pathname:e.pathname,path:e.pathname+e.search,href:e.href};return""!==e.port&&(t.port=Number(e.port)),t}["abort","aborted","error","socket","timeout"].forEach(function(e){d[e]=function(t){this._redirectable.emit(e,t)}}),h.prototype=Object.create(c.prototype),h.prototype.write=function(e,t,n){if(this._ending)throw new Error("write after end");if(!("string"==typeof e||"object"==typeof e&&"length"in e))throw new Error("data should be a string, Buffer or Uint8Array");"function"==typeof t&&(n=t,t=null),0!==e.length?this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,n)):(this.emit("error",new Error("Request body larger than maxBodyLength limit")),this.abort()):n&&n()},h.prototype.end=function(e,t,n){if("function"==typeof e?(n=e,e=t=null):"function"==typeof t&&(n=t,t=null),e){var r=this,i=this._currentRequest;this.write(e,t,function(){r._ended=!0,i.end(null,null,n)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,n)},h.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)},h.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},h.prototype.setTimeout=function(e,t){if(t&&this.once("timeout",t),this.socket)f(this,e);else{var n=this;this._currentRequest.once("socket",function(){f(n,e)})}return this.once("response",p),this.once("error",p),this},["abort","flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){h.prototype[e]=function(t,n){return this._currentRequest[e](t,n)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(h.prototype,e,{get:function(){return this._currentRequest[e]}})}),h.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(t){if(this._options.agents){var n=e.substr(0,e.length-1);this._options.agent=this._options.agents[n]}var i=this._currentRequest=t.request(this._options,this._onNativeResponse);for(var o in this._currentUrl=r.format(this._options),i._redirectable=this,d)o&&i.on(o,d[o]);if(this._isRedirect){var s=0,a=this,c=this._requestBodyBuffers;!function e(t){if(i===a._currentRequest)if(t)a.emit("error",t);else if(s<c.length){var n=c[s++];i.finished||i.write(n.data,n.encoding,e)}else a._ended&&i.end()}()}}else this.emit("error",new Error("Unsupported protocol "+e))},h.prototype._processResponse=function(e){this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:e.statusCode});var t=e.headers.location;if(t&&!1!==this._options.followRedirects&&e.statusCode>=300&&e.statusCode<400){if(this._currentRequest.removeAllListeners(),this._currentRequest.on("error",g),this._currentRequest.abort(),++this._redirectCount>this._options.maxRedirects)return void this.emit("error",new Error("Max redirects exceeded."));var n,i=this._options.headers;if(307!==e.statusCode&&!(this._options.method in u))for(n in this._options.method="GET",this._requestBodyBuffers=[],i)/^content-/i.test(n)&&delete i[n];if(!this._isRedirect)for(n in i)/^host$/i.test(n)&&delete i[n];var o=r.resolve(this._currentUrl,t);l("redirecting to",o),Object.assign(this._options,r.parse(o)),this._isRedirect=!0,this._performRequest(),e.destroy()}else e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[]},e.exports=m({http:o,https:s}),e.exports.wrap=m},function(e,t,n){"use strict";e.exports=function(e){function t(e){for(var t=0,n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return r.colors[Math.abs(t)%r.colors.length]}function r(e){var n;function s(){if(s.enabled){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var o=s,a=Number(new Date),c=a-(n||a);o.diff=c,o.prev=n,o.curr=a,n=a,t[0]=r.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");var l=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,function(e,n){if("%%"===e)return e;l++;var i=r.formatters[n];if("function"==typeof i){var s=t[l];e=i.call(o,s),t.splice(l,1),l--}return e}),r.formatArgs.call(o,t),(o.log||r.log).apply(o,t)}}return s.namespace=e,s.enabled=r.enabled(e),s.useColors=r.useColors(),s.color=t(e),s.destroy=i,s.extend=o,"function"==typeof r.init&&r.init(s),r.instances.push(s),s}function i(){var e=r.instances.indexOf(this);return-1!==e&&(r.instances.splice(e,1),!0)}function o(e,t){return r(this.namespace+(void 0===t?":":t)+e)}return r.debug=r,r.default=r,r.coerce=function(e){return e instanceof Error?e.stack||e.message:e},r.disable=function(){r.enable("")},r.enable=function(e){var t;r.save(e),r.names=[],r.skips=[];var n=("string"==typeof e?e:"").split(/[\s,]+/),i=n.length;for(t=0;t<i;t++)n[t]&&("-"===(e=n[t].replace(/\*/g,".*?"))[0]?r.skips.push(new RegExp("^"+e.substr(1)+"$")):r.names.push(new RegExp("^"+e+"$")));for(t=0;t<r.instances.length;t++){var o=r.instances[t];o.enabled=r.enabled(o.namespace)}},r.enabled=function(e){if("*"===e[e.length-1])return!0;var t,n;for(t=0,n=r.skips.length;t<n;t++)if(r.skips[t].test(e))return!1;for(t=0,n=r.names.length;t<n;t++)if(r.names[t].test(e))return!0;return!1},r.humanize=n(58),Object.keys(e).forEach(function(t){r[t]=e[t]}),r.instances=[],r.names=[],r.skips=[],r.formatters={},r.selectColor=t,r.enable(r.load()),r}},function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},function(e,t,n){n(27),e.exports=n(28)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2);global.__rootDir=r.join(__dirname,""),global.__processName="main"},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(5),o=i.default.pluginName,s=n(4);s.client.start({name:o,version:""},"thunder"),n(36);const a=n(1).default.getLogger("VipDownload:main");a.info("init"),function(){return r(this,void 0,void 0,function*(){yield s.client.callServerFunction("CreateWebview",o,{src:`file:///${__dirname}/item-vip-renderer/index.html`,nodeintegration:"nodeintegration"}),yield function(){return r(this,void 0,void 0,function*(){a.info("trackInit");let e=new Map,t=yield s.client.callServerFunction("IsLogined"),n=!1,r=0;if(t)try{let e=JSON.parse(yield s.client.callServerFunction("GetUserInfo",2));u(e)&&(r=Number(e.vasType).valueOf(),n=Boolean("1"===e.isVip))}catch(e){a.info("trackInit err",e)}e.set("is_login",t?1:0),e.set("is_vip",n?1:0),e.set("vip_type",r),e.set("plugin_name",o),e.set("plugin_version",i.default.pluginVersion);let l={attribute1:"plugin_init",extData:e};yield c.StatUtilitiesNS.trackEvent("xlx_vip_event",l)})}(),process.exit()})}().catch();const c=n(37),l=n(17),{isDef:u}=l.ThunderUtil},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(13),o=n(2),s=n(14).promisify,a=n(1).default.getLogger("fs-utilities");!function(e){function t(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.access);try{yield n(e),t=!0}catch(e){a.info(e)}}return t})}function c(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.mkdir);try{yield n(e),t=!0}catch(e){a.warn(e)}}return t})}function l(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.rmdir);try{yield n(e),t=!0}catch(e){a.warn(e)}}return t})}function u(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.unlink);try{yield n(e),t=!0}catch(e){a.warn(e)}}return t})}function d(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.readdir);try{t=yield n(e)}catch(e){a.warn(e)}}return t})}function h(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.lstat);try{t=yield n(e)}catch(e){a.warn(e)}}return t})}function f(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let r=o.join(e,t),i=yield h(r);n=null!==i&&i.isDirectory()?yield p(r):yield u(r)}return n})}function p(e){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e){if(yield t(e)){n=!0;let t=yield d(e);for(let r=0;r<t.length;r++)n=(yield f(e,t[r]))&&n;(n||0===t.length)&&(n=(yield l(e))&&n)}}return n})}function m(e){return r(this,void 0,void 0,function*(){let n=!1;return a.info("mkdirsAW",e),void 0!==e&&((yield t(e))?n=!0:o.dirname(e)===e?n=!1:(yield m(o.dirname(e)))&&(n=yield c(e))),n})}function g(e,n){return r(this,void 0,void 0,function*(){let r;if(e.toLowerCase()!==n.toLowerCase()&&(yield t(e))){let t=i.createReadStream(e),o=i.createWriteStream(n);r=new Promise(e=>{t.pipe(o).on("finish",()=>{e(!0)})})}else r=new Promise(e=>{e(!1)});return r})}e.readFileAW=function(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.readFile);try{t=yield n(e)}catch(e){a.warn(e)}}return t})},e.writeFileAW=function(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const r=s(i.writeFile);try{yield r(e,t),n=!0}catch(e){a.warn(e)}}return n})},e.existsAW=t,e.mkdirAW=c,e.rmdirAW=l,e.unlinkAW=u,e.readdirAW=d,e.lstatAW=h,e.rmdirsAW=p,e.mkdirsAW=m,e.renameAW=function(e,t){return r(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=s(i.rename);try{yield n(e,t)}catch(e){a.warn(e)}}})},e.copyFileAW=g,e.copyDirsAW=function e(n,i){return r(this,void 0,void 0,function*(){let r=!1,s=yield h(n);if(s.isDirectory()){r=yield m(i);let a=yield d(n);for(let c=0;c<a.length;c++){let l=o.join(n,a[c]),u=o.join(i,a[c]);(r=yield t(l))&&(r=(s=yield h(l)).isDirectory()?yield e(l,u):yield g(l,u))}}return r})},e.mkdtempAW=function(){return r(this,void 0,void 0,function*(){let e=!1;const t=s(i.mkdtemp),r=(yield Promise.resolve().then(()=>n(6))).tmpdir();try{e=yield t(`${r}${o.sep}`)}catch(e){a.warn(e)}return e})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},function(e,t){e.exports=require("./vendor.js")},function(e){e.exports={name:"vip-download",version:"4.8.0",author:"Xunlei",license:"",description:"",main:"4.8.0/index.js",clear:!0}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(33),i=n(3),o=n(15),s=n(34),a=n(16);t.Client=class extends i.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=a.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new s.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{o.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){o.error(e.message)}else o.information("This socket has been ended by the other party")}}},function(e,t){e.exports=require("net")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(35),i=n(3);t.Parser=class extends i.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),i=JSON.parse(e);this.emit("message",i),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},function(e,t){e.exports=require("string_decoder")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13),i=n(2),o=n(1);let s=i.join(__rootDir,"log-options.json");if(r.existsSync(s)){const e={label:__processName,options:s};o.default.start(e)}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(5),o=n(38),s=n(4),a=n(1).default.getLogger("stat-utilities"),c=n(43);!function(e){let t="0",n=0,l=0,u=0,d="",h=0;function f(){return r(this,void 0,void 0,function*(){t="0",n=0,l=0,h=(yield s.client.callServerFunction("IsLogined"))?1:0;let e=yield s.client.callServerFunction("GetAllUserInfo");if(e)if(t=e.userID||"0",e.vipList&&e.vipList[0]){if(e.vipList[0].isVip){let t=Number(e.vipList[0].isVip).valueOf();l=t>0?1:0}else l=0;e.vipList[0].vasType&&(n=Number(e.vipList[0].vasType).valueOf())}else l=0;yield function(){return r(this,void 0,void 0,function*(){if(u=0,"0"==t)return;let e=`https://soa-vip-ssl.xunlei.com/xlvip.common.mooseapi/querytags?sessionid=${yield function(){return r(this,void 0,void 0,function*(){if(d)return d;{let e=yield s.client.callServerFunction("GetSessionID");return e&&""!==e&&(d=e),d||"0"}})}()}&userid=${t}&tags=usedToBeDLVip&platform=xlx`;c.default.get(e,{timeout:1e4}).then(e=>{let t=null;a.info("queryUserTag response:",e),null!==e&&200===e.status&&null!==e.data&&1===e.data.code&&e.data.result&&(t=e.data.result,a.info("userTag.usedToBeDLVip",t.usedToBeDLVip),u=1==t.usedToBeDLVip?0:1)}).catch(e=>{a.error("err:",e)})})}()})}s.client.attachServerEvent("onUserInfoChange",(e,t,n)=>r(this,void 0,void 0,function*(){yield f()})),s.client.attachServerEvent("onLoginStatusChange",(e,t,n)=>r(this,void 0,void 0,function*(){yield f()})),f(),e.trackEvent=function(e,t){return r(this,void 0,void 0,function*(){let r=(t=t||{}).attribute1||"",c=t.attribute2||"",d=t.extData||new Map;if(d.set("plugin_version",i.default.pluginVersion),!d.has("cpeerid")){let e=yield o.default.getTpPeerId();d.set("cpeerid",e)}d.set("is_new_user",u),d.has("is_login")||d.set("is_login",h),d.has("is_vip")||d.has("isvip")||d.set("is_vip",l),d.has("vip_type")||d.has("vas_type")||(l&&5===n?d.set("vip_type",5):l&&n>2?d.set("vip_type",3):l?d.set("vip_type",2):d.set("vip_type",0));let f=function(e){let t="";return e.forEach((e,n)=>{""!==t&&(t+=","),t=t+n+"="+e}),t}(d);a.info("key",e),a.info("attribute1",r),a.info("attribute2",c),a.info("extData",f),f=encodeURIComponent(f),a.info("encode extData",f),yield s.client.callServerFunction("TrackEvent",e,r,c,0,0,0,0,f)})}}(t.StatUtilitiesNS||(t.StatUtilitiesNS={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(39),o=n(3),s=n(4),a=n(5),c=n(40),l=n(1).default.getLogger("VipDownload:download-kernel-helper");var u;!function(e){e.taskInserted="OnTaskInserted",e.taskCompleted="OnTaskCompleted",e.taskRemoved="OnTaskRemoved",e.taskStatusChanged="OnTaskStatusChanged",e.taskDetailChanged="OnTaskDetailChanged",e.taskDcdnStatusChanged="OnTaskDcdnStatusChanged",e.btSubFileDcdnStatusChanged="OnBtSubFileDcdnStatusChanged",e.btSubFileDetailChanged="OnBtSubFileDetailChanged",e.btSubFileForbidden="OnBtSubFileForbidden",e.downloadItemActive="OnDownloadItemActive",e.downloadItemChosen="OnDownloadItemChosen"}(u=t.DkEventNS||(t.DkEventNS={}));class d extends o.EventEmitter{constructor(){super(),this.mPeerId=void 0,this.init()}init(){this.setMaxListeners(0),this.attachDkEvents()}getTpPeerId(){return r(this,void 0,void 0,function*(){if(this.mPeerId)return this.mPeerId;{let e=yield s.client.callServerFunction("GetTpPeerId");return e&&""!==e&&(this.mPeerId=e),this.mPeerId||""}})}getCurrentCategoryId(){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("GetCurrentCategoryId")})}getTaskBaseInfo(e){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("GetTaskBaseInfo2",e)})}getTaskStatus(e){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("GetTaskStatus",e)})}IsTaskExist(e){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("IsTaskExist",e)})}getTaskDetail(e){return r(this,void 0,void 0,function*(){let t=null,n=yield s.client.callServerFunction("GetTaskInfo",e,void 0,"taskDetail");l.info("getTaskDetail",n);do{if(!n)break;let e=null;try{e=JSON.parse(n)}catch(e){l.warn(e)}if(!e||!e.fileList)break;let r=e.infoId,o=e.type;t={infoId:r,files:new Map};for(let n of e.fileList){let e=-1,s=n.url;o===i.DownloadKernel.TaskType.Bt?(e=n.index,s=this.createBtFileUrl(n.index,r)):o===i.DownloadKernel.TaskType.Group&&(e=n.taskId);let a={subId:e,taskStatus:n.status,filePath:n.filePath,fileName:n.fileName,gcid:n.gcid,cid:n.cid,url:s,refUrl:n.refUrl||"",fileSize:n.fileSize,downloadSize:n.downloadSize,errCode:n.errCode,isNeedDownload:n.isNeedDownload,dcdnStatus:n.dcdnStatus};t.files.set(e,a)}}while(0);return t})}createBtTaskUrl(e){return"bt://"+e}createBtFileUrl(e,t){let n=this.createBtTaskUrl(t);return n=n+"/"+e}getIsHDVideo(e){return r(this,void 0,void 0,function*(){let t=!1,n=yield s.client.callServerFunction("GetConfigModules","HDVideo","domains");return l.silly("HDVideo domains",n),n&&0!==n.length||(n=["hd.xunlei.com"]),e&&(t=a.default.isUrlInDomains(e,n)),t})}getIsWeiDuanYouXi(e){return r(this,void 0,void 0,function*(){let t=!1,n=c.default.getValue("VipDownload","WDYXDomains");return l.info("WDYXDomains domains",n),n&&0!==n.length||(n=["lx.patch1.9you.com"]),e&&(t=a.default.isUrlInDomains(e,n)),t})}startTask(e){this.operateTask(e,"continue")}stopTask(e){this.operateTask(e,"pause")}enableDcdnWithVipCert(e,t,n){return r(this,void 0,void 0,function*(){l.info("enableDcdnWithVipCert","taskId",e,"index",t,"vipCert",n),yield s.client.callServerFunction("EnableDcdnWithVipCert",e,n,t)})}updateDcdnWithVipCert(e,t,n){return r(this,void 0,void 0,function*(){l.info("updateDcdnWithVipCert","taskId",e,"index",t,"vipCert",n),yield s.client.callServerFunction("UpdateDcdnWithVipCert",e,n,t)})}disableDcdnWithVipCert(e,t){return r(this,void 0,void 0,function*(){l.info("disableDcdnWithVipCert","taskId",e,"index",t),yield s.client.callServerFunction("DisableDcdnWithVipCert",e,t)})}selectCategoryView(e,t,n,r){s.client.callServerFunction("SelectCategoryView",e,t,n,r).catch()}getDownloadingActiveTaskId(){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("GetDownloadingActiveTaskId")})}attachDkEvents(){s.client.attachServerEvent("OnTaskInserted",this.onTaskInserted.bind(this)),s.client.attachServerEvent("OnTaskRemoved",this.onTaskRemoved.bind(this)),s.client.attachServerEvent("OnTaskStatusChanged",(e,t)=>{{let e=null;try{e=JSON.parse(t)}catch(e){l.warn(e)}if(null!==e)for(let t in e){let n=e[t];this.onTaskStatusChanged(Number(t),n)}}}),s.client.attachServerEvent("OnTaskDetailChanged",(e,t)=>{{let e=null;try{e=JSON.parse(t)}catch(e){l.warn(e)}if(null!==e)for(let t in e){let n=e[t];this.onTaskDetailChanged(Number(t),n)}}}),s.client.attachServerEvent("OnTaskDcdnStatusChanged",(e,t)=>{{let e=null;try{e=JSON.parse(t)}catch(e){l.warn(e)}if(null!==e)for(let t in e){let n=e[t];this.onTaskDcdnStatusChanged(Number(t),n)}}}),s.client.attachServerEvent("OnBtSubFileDcdnStatusChanged",this.onBtSubFileDcdnStatusChanged.bind(this)),s.client.attachServerEvent("OnBtSubFileDetailChanged",(e,t,n)=>{{let e=null;try{e=JSON.parse(n)}catch(e){l.warn(e)}null!==e&&this.onBtSubFileDetailChanged(t,e)}}),s.client.attachServerEvent("OnBtSubFileForbidden",this.onBtSubFileForbidden.bind(this)),s.client.attachServerEvent("OnDownloadItemActive",(e,t,n,r,i,o)=>{this.onDownloadItemActive(t,n,r)}),this.getDownloadingActiveTaskId().then(e=>{e&&this.onDownloadItemActive(i.DownloadKernel.CategroyViewID.Downloading,!0,e)}).catch()}operateTask(e,t,n){s.client.callServerFunction("OperateTask",e,t,n).catch()}onTaskInserted(e,t,n,r){l.info("onTaskInserted categoryId:",t,", categoryViewId:",n);let o=null;if(r){try{o=JSON.parse(r)}catch(e){l.warn(e)}if(null!==o)switch(n){case i.DownloadKernel.CategroyViewID.Downloading:for(let e of o)this.emit(u.taskInserted,e);break;case i.DownloadKernel.CategroyViewID.Completed:for(let e of o)this.emit(u.taskCompleted,e)}}}onTaskRemoved(e,t,n,r){l.info("onTaskRemoved categoryId:",t,", categoryViewId:",n);let o=[];if(r)switch(o=JSON.parse(r),n){case i.DownloadKernel.CategroyViewID.Downloading:case i.DownloadKernel.CategroyViewID.Completed:for(let e of o)this.emit(u.taskRemoved,e)}}onTaskStatusChanged(e,t){this.emit(u.taskStatusChanged,e,t)}onTaskDetailChanged(e,t){this.emit(u.taskDetailChanged,e,t)}onTaskDcdnStatusChanged(e,t){this.emit(u.taskDcdnStatusChanged,e,t)}onBtSubFileDcdnStatusChanged(e,t,n,r){this.emit(u.btSubFileDcdnStatusChanged,t,n,r)}onBtSubFileDetailChanged(e,t){this.emit(u.btSubFileDetailChanged,e,t)}onBtSubFileForbidden(e,t,n){this.emit(u.btSubFileForbidden,t,n)}onDownloadItemActive(e,t,n){this.emit(u.downloadItemActive,e,t,n)}}t.DkHelper=d,t.default=new d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n,r,i,o,s,a,c,l,u,d,h,f,p,m,g,v,y,C;!function(e){e[e.Unkown=0]="Unkown",e[e.Create=1]="Create",e[e.InvaldParam=2]="InvaldParam",e[e.InvaldLink=3]="InvaldLink",e[e.InvaldConfig=4]="InvaldConfig",e[e.Timeout=5]="Timeout",e[e.VerifyData=6]="VerifyData",e[e.Forbidden=7]="Forbidden",e[e.RangeDispatch=8]="RangeDispatch",e[e.FilePathOverRanging=9]="FilePathOverRanging",e[e.FileCreate=201]="FileCreate",e[e.FileWrite=202]="FileWrite",e[e.FileRead=203]="FileRead",e[e.FileRename=204]="FileRename",e[e.FileFull=205]="FileFull",e[e.FileOccupied=211]="FileOccupied",e[e.FileAccessDenied=212]="FileAccessDenied",e[e.BtUploadExist=601]="BtUploadExist",e[e.ForbinddenResource=701]="ForbinddenResource",e[e.ForbinddenAccount=702]="ForbinddenAccount",e[e.ForbinddenArea=703]="ForbinddenArea",e[e.ForbinddenCopyright=704]="ForbinddenCopyright",e[e.ForbinddenReaction=705]="ForbinddenReaction",e[e.ForbinddenPorn=706]="ForbinddenPorn",e[e.DownloadSDKCrash=10001]="DownloadSDKCrash",e[e.torrentFileNotExist=10002]="torrentFileNotExist"}(t=e.TaskError||(e.TaskError={})),function(e){e[e.Unkown=-1]="Unkown",e[e.Success=0]="Success",e[e.QueryFailed=1]="QueryFailed",e[e.ServerError=2]="ServerError",e[e.ResourceNotFound=3]="ResourceNotFound",e[e.AuthorizingFailed=4]="AuthorizingFailed",e[e.ForbidByCopyright=5]="ForbidByCopyright",e[e.ForbidByPorNoGraphy=6]="ForbidByPorNoGraphy",e[e.ForbidByReactionary=7]="ForbidByReactionary",e[e.ForbidByOtherFilter=8]="ForbidByOtherFilter"}(n=e.DcdnStatusCode||(e.DcdnStatusCode={})),function(e){e[e.Begin=-1]="Begin",e[e.Unkown=0]="Unkown",e[e.StandBy=1]="StandBy",e[e.PreDownloading=2]="PreDownloading",e[e.StartWaiting=3]="StartWaiting",e[e.StartPending=4]="StartPending",e[e.Started=5]="Started",e[e.StopPending=6]="StopPending",e[e.Stopped=7]="Stopped",e[e.Succeeded=8]="Succeeded",e[e.Failed=9]="Failed",e[e.Seeding=10]="Seeding",e[e.DestroyPending=11]="DestroyPending",e[e.End=12]="End"}(r=e.TaskStatus||(e.TaskStatus={})),function(e){e[e.Begin=-1]="Begin",e[e.StandBy=0]="StandBy",e[e.Stopped=1]="Stopped",e[e.Started=2]="Started",e[e.Complete=3]="Complete",e[e.Forbidden=4]="Forbidden",e[e.Error=5]="Error",e[e.End=6]="End"}(i=e.BtFileStatus||(e.BtFileStatus={})),function(e){e[e.DispatchStrategyNone=0]="DispatchStrategyNone",e[e.DispatchStrategyOrigin=1]="DispatchStrategyOrigin",e[e.DispatchStrategyP2s=2]="DispatchStrategyP2s",e[e.DispatchStrategyP2p=4]="DispatchStrategyP2p",e[e.DispatchStrategyAll=-1]="DispatchStrategyAll"}(o=e.DispatchStrategy||(e.DispatchStrategy={})),function(e){e[e.Unkown=0]="Unkown",e[e.P2sp=1]="P2sp",e[e.Bt=2]="Bt",e[e.Emule=3]="Emule",e[e.Group=4]="Group",e[e.Magnet=5]="Magnet"}(s=e.TaskType||(e.TaskType={})),function(e){e.Unkown="Unkown",e.Downloading="Downloading",e.Completed="Completed",e.Recycle="Recycle"}(a=e.CategroyViewID||(e.CategroyViewID={})),function(e){e[e.Unknow=0]="Unknow",e[e.TaskCreated=1]="TaskCreated",e[e.InsertToCategoryView=2]="InsertToCategoryView",e[e.RemoveFromCategoryView=3]="RemoveFromCategoryView",e[e.StatusChanged=4]="StatusChanged",e[e.DetailChanged=5]="DetailChanged",e[e.ChannelInfoChanged=6]="ChannelInfoChanged",e[e.DcdnStatusChanged=7]="DcdnStatusChanged",e[e.TaskDescriptionChanged=8]="TaskDescriptionChanged",e[e.TaskUserRead=9]="TaskUserRead",e[e.TaskRenamed=10]="TaskRenamed",e[e.TaskMovedEnd=11]="TaskMovedEnd",e[e.TaskMovingStateChange=12]="TaskMovingStateChange",e[e.BtSubFileDetailChanged=13]="BtSubFileDetailChanged",e[e.BtSubFileLoaded=14]="BtSubFileLoaded",e[e.BtSubFileForbidden=15]="BtSubFileForbidden",e[e.BtSubFileDcdnStatusChanged=16]="BtSubFileDcdnStatusChanged",e[e.TaskCategoryMovedEnd=17]="TaskCategoryMovedEnd",e[e.GroupTaskSubFileDetailChanged=18]="GroupTaskSubFileDetailChanged",e[e.TaskDestroying=19]="TaskDestroying",e[e.TaskDestroyed=20]="TaskDestroyed"}(c=e.TaskEventType||(e.TaskEventType={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.VirtualId=2]="VirtualId",e[e.SrcTotal=3]="SrcTotal",e[e.SrcUsing=4]="SrcUsing",e[e.FileSize=5]="FileSize",e[e.ReceivedSize=6]="ReceivedSize",e[e.DownloadSize=7]="DownloadSize",e[e.TotalDownloadSize=8]="TotalDownloadSize",e[e.CreateTime=9]="CreateTime",e[e.CompletionTime=10]="CompletionTime",e[e.DownloadingPeriod=11]="DownloadingPeriod",e[e.Progress=12]="Progress",e[e.RecycleTime=13]="RecycleTime",e[e.FileCreated=14]="FileCreated",e[e.Forbidden=15]="Forbidden",e[e.CategoryId=16]="CategoryId",e[e.UserRead=17]="UserRead",e[e.OpenOnComplete=18]="OpenOnComplete",e[e.GroupTaskId=19]="GroupTaskId",e[e.DownloadSubTask=20]="DownloadSubTask",e[e.NameType=21]="NameType",e[e.OwnerProduct=22]="OwnerProduct",e[e.FileIndex=23]="FileIndex",e[e.NameFixed=24]="NameFixed",e[e.ValidDownloadSize=25]="ValidDownloadSize",e[e.RealDownloadSize=26]="RealDownloadSize",e[e.ResourceLegal=27]="ResourceLegal",e[e.TaskType=28]="TaskType",e[e.ErrorCode=29]="ErrorCode",e[e.NumberEnd=30]="NumberEnd",e[e.BooleanStart=4096]="BooleanStart",e[e.Destroyed=4097]="Destroyed",e[e.Background=4098]="Background",e[e.Moving=4099]="Moving",e[e.BooleanEnd=4100]="BooleanEnd",e[e.StringStart=8192]="StringStart",e[e.TaskName=8193]="TaskName",e[e.SavePath=8194]="SavePath",e[e.RelativePath=8195]="RelativePath",e[e.TaskUrl=8196]="TaskUrl",e[e.RefUrl=8197]="RefUrl",e[e.Cid=8198]="Cid",e[e.Gcid=8199]="Gcid",e[e.Cookie=8200]="Cookie",e[e.ProductInfo=8201]="ProductInfo",e[e.Origin=8202]="Origin",e[e.Description=8203]="Description",e[e.UserData=8204]="UserData",e[e.OriginName=8205]="OriginName",e[e.HTTPContentType=8206]="HTTPContentType",e[e.CategoryViewId=8207]="CategoryViewId",e[e.StringEnd=8208]="StringEnd",e[e.ObjectStart=12288]="ObjectStart",e[e.ObjectEnd=12289]="ObjectEnd"}(l=e.TaskAttribute||(e.TaskAttribute={})),function(e){e[e.UnKnown=0]="UnKnown",e[e.SrcTotal=1]="SrcTotal",e[e.SrcUsing=2]="SrcUsing",e[e.FileSize=4]="FileSize",e[e.ReceivedSize=8]="ReceivedSize",e[e.DownloadSize=16]="DownloadSize",e[e.CompletionTime=32]="CompletionTime",e[e.DownloadingPeriod=64]="DownloadingPeriod",e[e.Progress=128]="Progress",e[e.RecycleTime=256]="RecycleTime",e[e.FileCreated=512]="FileCreated",e[e.Forbidden=1024]="Forbidden",e[e.UserRead=2048]="UserRead",e[e.OpenOnComplete=4096]="OpenOnComplete",e[e.DownloadSubTask=8192]="DownloadSubTask",e[e.TaskName=16384]="TaskName",e[e.SavePath=32768]="SavePath",e[e.Cid=65536]="Cid",e[e.Gcid=131072]="Gcid",e[e.UserData=262144]="UserData",e[e.CategoryViewId=524288]="CategoryViewId",e[e.ErrorCode=1048576]="ErrorCode",e[e.TaskSpeed=2097152]="TaskSpeed",e[e.ChannelInfo=4194304]="ChannelInfo",e[e.ValidDownloadSize=8388608]="ValidDownloadSize",e[e.OriginName=16777216]="OriginName",e[e.HTTPContentType=33554432]="HTTPContentType"}(u=e.TaskDetailChangedFlags||(e.TaskDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(d=e.BtSubFileDetailChangedFlags||(e.BtSubFileDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(h=e.GroupTaskSubFileDetailChangedFlags||(e.GroupTaskSubFileDetailChangedFlags={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.FileStatus=2]="FileStatus",e[e.DownloadSize=3]="DownloadSize",e[e.FileSize=4]="FileSize",e[e.EnableDcdn=5]="EnableDcdn",e[e.ErrorCode=6]="ErrorCode",e[e.DcdnStatus=7]="DcdnStatus",e[e.RealIndex=8]="RealIndex",e[e.FileOffset=9]="FileOffset",e[e.Visible=10]="Visible",e[e.Download=11]="Download",e[e.NumberEnd=12]="NumberEnd",e[e.StringStart=13]="StringStart",e[e.FinalName=14]="FinalName",e[e.RelativePath=15]="RelativePath",e[e.FileName=16]="FileName",e[e.FilePath=17]="FilePath",e[e.Cid=18]="Cid",e[e.Gcid=19]="Gcid",e[e.UserRead=20]="UserRead",e[e.StringEnd=21]="StringEnd"}(f=e.BtFileAttribute||(e.BtFileAttribute={})),function(e){e[e.P2spUrl=0]="P2spUrl",e[e.BtInfoId=1]="BtInfoId",e[e.EmuleFileHash=2]="EmuleFileHash",e[e.MagnetUrl=3]="MagnetUrl",e[e.GroupTaskName=4]="GroupTaskName"}(p=e.KeyType||(e.KeyType={})),function(e){e[e.NameInclude=1]="NameInclude",e[e.BtDisplayNameInclude=2]="BtDisplayNameInclude"}(m=e.SearchKeyType||(e.SearchKeyType={})),function(e){e[e.ExtEqual=1]="ExtEqual",e[e.NameLikeAndExtEqual=2]="NameLikeAndExtEqual",e[e.TaskTypeEqual=4]="TaskTypeEqual"}(g=e.FilterKeyType||(e.FilterKeyType={})),function(e){e[e.All=0]="All",e[e.CommonForeground=1]="CommonForeground",e[e.CommonBackground=2]="CommonBackground",e[e.Temporary=3]="Temporary",e[e.PreDownload=4]="PreDownload",e[e.PrivateForeground=5]="PrivateForeground"}(v=e.KeyFilter||(e.KeyFilter={})),function(e){e[e.Unknown=-1]="Unknown",e[e.LoadTaskBasic=0]="LoadTaskBasic",e[e.Create=1]="Create",e[e.Recycle=2]="Recycle",e[e.Recover=3]="Recover",e[e.ReDownload=4]="ReDownload",e[e.MoveThoughCategory=5]="MoveThoughCategory"}(y=e.TaskInsertReason||(e.TaskInsertReason={})),function(e){e[e.Unknown=-1]="Unknown",e[e.ContextMenu=0]="ContextMenu",e[e.Button=1]="Button",e[e.TaskDetail=2]="TaskDetail",e[e.DownloadMagnet=3]="DownloadMagnet",e[e.ToolbarButton=4]="ToolbarButton",e[e.SelectDownloadLists=5]="SelectDownloadLists",e[e.DeleteTask=6]="DeleteTask"}(C=e.TaskStopReason||(e.TaskStopReason={}))}(t.DownloadKernel||(t.DownloadKernel={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(41),s=n(42),a=n(1).default.getLogger("VipDownload:config");var c;!function(e){e.configGet="OnConfigGet"}(c=t.ConfigEventNS||(t.ConfigEventNS={}));class l extends i.EventEmitter{constructor(){super(),this.mConfig=new s.Config,this.mConfigInitFinish=!1,this.init().catch()}init(){return r(this,void 0,void 0,function*(){this.setMaxListeners(0);let e=!1,t=yield this.downloadConfig();t&&(e=yield this.config.loadConfigData(t)),a.info("config init","result",e),this.config.printConfigData(),this.mConfigInitFinish=!0,this.emit(c.configGet)})}isConfigInitFinish(){return this.mConfigInitFinish}getValue(e,t){return this.config.getValue(e,t)}downloadConfig(){return r(this,void 0,void 0,function*(){return new Promise(e=>{let t=new o.HttpSession;t.url="http://media.info.client.xunlei.com/VipDownloadConfig.json",t.get(t=>{t&&200===t.statusCode&&t.body?e(t.body):e(null)},t=>{a.info("error",t),e(null)})})})}get config(){return this.mConfig}}t.ConfigHelper=l,t.default=new l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(7),i=n(8),o=n(9),s=n(17),a=n(1).default.getLogger("http-session"),{isDef:c}=s.ThunderUtil;var l;!function(e){e.HTTP="HTTP",e.HTTPS="HTTPS"}(l=t.Protocol||(t.Protocol={}));t.HttpSession=class{constructor(){this.mRetries=0,this.mHost=void 0,this.mPort=void 0,this.mPath=void 0,this.mAuth=void 0,this.mAccept=void 0,this.mBody=null,this.mUrl=void 0,this.mProtocol=l.HTTP,this.mTimeout=void 0,this.mCurRetries=0}set host(e){this.mHost=e}get host(){return this.mHost}set port(e){this.mPort=e}get port(){let e=void 0;return e=c(this.mPort)?this.mPort:this.protocol===l.HTTPS?443:80}set path(e){this.mPath=e}get path(){return this.mPath}set url(e){this.mUrl=e}get protocol(){return this.mProtocol}set protocol(e){this.mProtocol=e}get url(){return this.mUrl}set auth(e){this.mAuth=e}get auth(){return this.mAuth}set accept(e){this.mAccept=e}get accept(){return this.mAccept}set body(e){this.mBody=e}get body(){return this.mBody}set retries(e){this.mRetries=e}get retries(){return this.mRetries}set timeout(e){this.mTimeout=e}get timeout(){return this.mTimeout}post(e,t){do{let n=this.body;if(!n){a.info("body is empty"),t(null);break}let r=this.auth,i=this.accept,o={hostname:this.host,port:this.port,path:this.path||"/",method:"POST",auth:r||void 0,headers:{"Content-Length":n?n.length:0,Accept:i||"*/*"}};a.verbose("option",o);try{this.postImpl(n,o,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,a.info("mCurRetries",this.mCurRetries),this.post(e,t)):t(n)})}catch(e){a.warn("error ",e),t(null)}}while(0)}get(e,t){let n=null;if(this.url){let e=r.parse(this.url,!0);e&&"https:"===e.protocol?this.protocol=l.HTTPS:this.protocol=l.HTTP,n=this.url}else{let e=this.auth,t=this.accept;n={hostname:this.host,port:this.port,path:this.path||"/",method:"GET",auth:e||void 0,headers:{Accept:t||"*/*"}}}a.verbose("option",n);try{this.getImpl(n,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,a.info("mCurRetries",this.mCurRetries),this.get(e,t)):t(n)})}catch(e){a.warn("error ",e),t(null)}}postImpl(e,t,n,r){let s=(this.protocol===l.HTTPS?o:i).request(t,e=>{let t=null;e.on("data",e=>{t=t?Buffer.concat([t,e]):e}),e.on("end",()=>{a.info("statusCode",e.statusCode),a.info("headers",e.headers),n({statusCode:e.statusCode,headers:e.headers,body:t})})});s.on("error",e=>{r(e)}),s.on("timeout",()=>{s.abort()}),this.timeout&&s.setTimeout(this.timeout),s.write(e),s.end()}getImpl(e,t,n){(this.protocol===l.HTTPS?o:i).get(e,e=>{let n=null;e.on("data",e=>{n=n?Buffer.concat([n,e]):e}),e.on("end",()=>{a.info("statusCode",e.statusCode),a.info("headers",e.headers),t({statusCode:e.statusCode,headers:e.headers,body:n})})}).on("error",e=>{n(e)})}}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(1).default.getLogger("VipDownload:config");t.Config=class{constructor(){this.configData={},this.configData={VipDownload:{TokenExpireAdvanceSecond:300,TokenExpireMinSecond:20,TokenDefaultQueryInterval:300,EnableTryMinSize:209715200,EnableTryMaxProgress:40,FileEnableTryMinSize:52428800,TryInterval:1800,TryMaxProgress:20,TryMaxSize:1073741824,TryFailDispearDelay:5,TryFinishDispearDelay:1800,TryFinishClickDispearDelay:180,NewSkinPeerid:[],WarnStylePeerid:[],BeforeBaotuanXgtStylePeerid:["A"],EnableSuperTryMinSize:524288e3,EnableSuperTryMaxProgress:50,SuperTryFinishDispearDelay:180,SuperTryFinishClickDispearDelay:10,MixTryFinishMiniWeb:2,MixTryFinishMiniWebMinPromotePercent:5,AdFinishDispearDelay:180,SceneChangeInterval:10,WDYXDomains:["lx.patch1.9you.com"],PlayGameXgtCount:6,PlayGameHash:!1,SpeedZeroLimitSpeed:1,AuotShowBaotuanNoviceDelay:60,NoVipStatusQueryPeerid:["0","1"],ReportGlobalSpeed:!0,ReportGlobalSpeedTime:60}}}getValue(e,t){let n=void 0;do{if(!e||""===e)break;if(!t||""===t)break;let r=this.configData[e];if(!r)break;n=r[t]}while(0);return n}loadConfigData(e){return r(this,void 0,void 0,function*(){let t=!1,n=null;try{(n=JSON.parse(e.toString()))&&(t=!0)}catch(e){i.warn(e)}return this.mergeConfigData(n),t})}printConfigData(){i.info("configData",this.configData)}mergeConfigData(e){if(e)if(null===this.configData)this.configData=e;else for(let t in e){let n=e[t];if(!n)break;for(let e in n){let r=n[e],i=this.configData[t];if(i)i[e]=r;else{let n={};n[e]=r,this.configData[t]=n}}}}}},function(e,t,n){e.exports=n(44)},function(e,t,n){"use strict";var r=n(0),i=n(18),o=n(46),s=n(10);function a(e){var t=new o(e),n=i(o.prototype.request,t);return r.extend(n,o.prototype,t),r.extend(n,t),n}var c=a(s);c.Axios=o,c.create=function(e){return a(r.merge(s,e))},c.Cancel=n(25),c.CancelToken=n(70),c.isCancel=n(24),c.all=function(e){return Promise.all(e)},c.spread=n(71),e.exports=c,e.exports.default=c},function(e,t){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},function(e,t,n){"use strict";var r=n(10),i=n(0),o=n(65),s=n(66);function a(e){this.defaults=e,this.interceptors={request:new o,response:new o}}a.prototype.request=function(e){"string"==typeof e&&(e=i.merge({url:arguments[0]},arguments[1])),(e=i.merge(r,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var t=[s,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},i.forEach(["delete","get","head","options"],function(e){a.prototype[e]=function(t,n){return this.request(i.merge(n||{},{method:e,url:t}))}}),i.forEach(["post","put","patch"],function(e){a.prototype[e]=function(t,n,r){return this.request(i.merge(r||{},{method:e,url:t,data:n}))}}),e.exports=a},function(e,t,n){"use strict";var r=n(0);e.exports=function(e,t){r.forEach(e,function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])})}},function(e,t,n){"use strict";var r=n(0),i=n(19),o=n(21),s=n(49),a=n(50),c=n(11),l="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(51);e.exports=function(e){return new Promise(function(t,u){var d=e.data,h=e.headers;r.isFormData(d)&&delete h["Content-Type"];var f=new XMLHttpRequest,p="onreadystatechange",m=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in f||a(e.url)||(f=new window.XDomainRequest,p="onload",m=!0,f.onprogress=function(){},f.ontimeout=function(){}),e.auth){var g=e.auth.username||"",v=e.auth.password||"";h.Authorization="Basic "+l(g+":"+v)}if(f.open(e.method.toUpperCase(),o(e.url,e.params,e.paramsSerializer),!0),f.timeout=e.timeout,f[p]=function(){if(f&&(4===f.readyState||m)&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in f?s(f.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?f.response:f.responseText,status:1223===f.status?204:f.status,statusText:1223===f.status?"No Content":f.statusText,headers:n,config:e,request:f};i(t,u,r),f=null}},f.onerror=function(){u(c("Network Error",e,null,f)),f=null},f.ontimeout=function(){u(c("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",f)),f=null},r.isStandardBrowserEnv()){var y=n(52),C=(e.withCredentials||a(e.url))&&e.xsrfCookieName?y.read(e.xsrfCookieName):void 0;C&&(h[e.xsrfHeaderName]=C)}if("setRequestHeader"in f&&r.forEach(h,function(e,t){void 0===d&&"content-type"===t.toLowerCase()?delete h[t]:f.setRequestHeader(t,e)}),e.withCredentials&&(f.withCredentials=!0),e.responseType)try{f.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&f.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&f.upload&&f.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){f&&(f.abort(),u(e),f=null)}),void 0===d&&(d=null),f.send(d)})}},function(e,t,n){"use strict";var r=n(0),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,o,s={};return e?(r.forEach(e.split("\n"),function(e){if(o=e.indexOf(":"),t=r.trim(e.substr(0,o)).toLowerCase(),n=r.trim(e.substr(o+1)),t){if(s[t]&&i.indexOf(t)>=0)return;s[t]="set-cookie"===t?(s[t]?s[t]:[]).concat([n]):s[t]?s[t]+", "+n:n}}),s):s}},function(e,t,n){"use strict";var r=n(0);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function i(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=i(window.location.href),function(t){var n=r.isString(t)?i(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},function(e,t,n){"use strict";var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function i(){this.message="String contains an invalid character"}i.prototype=new Error,i.prototype.code=5,i.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,n,o=String(e),s="",a=0,c=r;o.charAt(0|a)||(c="=",a%1);s+=c.charAt(63&t>>8-a%1*8)){if((n=o.charCodeAt(a+=.75))>255)throw new i;t=t<<8|n}return s}},function(e,t,n){"use strict";var r=n(0);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,i,o,s){var a=[];a.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),r.isString(i)&&a.push("path="+i),r.isString(o)&&a.push("domain="+o),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t,n){"use strict";var r=n(0),i=n(19),o=n(21),s=n(8),a=n(9),c=n(22).http,l=n(22).https,u=n(7),d=n(63),h=n(64),f=n(11),p=n(20);e.exports=function(e){return new Promise(function(t,n){var m,g=e.data,v=e.headers;if(v["User-Agent"]||v["user-agent"]||(v["User-Agent"]="axios/"+h.version),g&&!r.isStream(g)){if(Buffer.isBuffer(g));else if(r.isArrayBuffer(g))g=new Buffer(new Uint8Array(g));else{if(!r.isString(g))return n(f("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",e));g=new Buffer(g,"utf-8")}v["Content-Length"]=g.length}var y=void 0;e.auth&&(y=(e.auth.username||"")+":"+(e.auth.password||""));var C=u.parse(e.url),w=C.protocol||"http:";if(!y&&C.auth){var b=C.auth.split(":");y=(b[0]||"")+":"+(b[1]||"")}y&&delete v.Authorization;var S="https:"===w,k=S?e.httpsAgent:e.httpAgent,x={path:o(C.path,e.params,e.paramsSerializer).replace(/^\?/,""),method:e.method,headers:v,agent:k,auth:y};e.socketPath?x.socketPath=e.socketPath:(x.hostname=C.hostname,x.port=C.port);var F,_=e.proxy;if(!_&&!1!==_){var T=w.slice(0,-1)+"_proxy",D=process.env[T]||process.env[T.toUpperCase()];if(D){var E=u.parse(D);if(_={host:E.hostname,port:E.port},E.auth){var P=E.auth.split(":");_.auth={username:P[0],password:P[1]}}}}if(_&&(x.hostname=_.host,x.host=_.host,x.headers.host=C.hostname+(C.port?":"+C.port:""),x.port=_.port,x.path=w+"//"+C.hostname+(C.port?":"+C.port:"")+x.path,_.auth)){var I=new Buffer(_.auth.username+":"+_.auth.password,"utf8").toString("base64");x.headers["Proxy-Authorization"]="Basic "+I}e.transport?F=e.transport:0===e.maxRedirects?F=S?a:s:(e.maxRedirects&&(x.maxRedirects=e.maxRedirects),F=S?l:c),e.maxContentLength&&e.maxContentLength>-1&&(x.maxBodyLength=e.maxContentLength);var R=F.request(x,function(r){if(!R.aborted){clearTimeout(m),m=null;var o=r;switch(r.headers["content-encoding"]){case"gzip":case"compress":case"deflate":o=o.pipe(d.createUnzip()),delete r.headers["content-encoding"]}var s=r.req||R,a={status:r.statusCode,statusText:r.statusMessage,headers:r.headers,config:e,request:s};if("stream"===e.responseType)a.data=o,i(t,n,a);else{var c=[];o.on("data",function(t){c.push(t),e.maxContentLength>-1&&Buffer.concat(c).length>e.maxContentLength&&n(f("maxContentLength size of "+e.maxContentLength+" exceeded",e,null,s))}),o.on("error",function(t){R.aborted||n(p(t,e,null,s))}),o.on("end",function(){var r=Buffer.concat(c);"arraybuffer"!==e.responseType&&(r=r.toString("utf8")),a.data=r,i(t,n,a)})}}});R.on("error",function(t){R.aborted||n(p(t,e,null,R))}),e.timeout&&!m&&(m=setTimeout(function(){R.abort(),n(f("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",R))},e.timeout)),e.cancelToken&&e.cancelToken.promise.then(function(e){R.aborted||(R.abort(),n(e))}),r.isStream(g)?g.pipe(R):R.end(g)})}},function(e,t){e.exports=require("assert")},function(e,t){e.exports=require("stream")},function(e,t,n){"use strict";"undefined"==typeof process||"renderer"===process.type||!0===process.browser||process.__nwjs?e.exports=n(57):e.exports=n(59)},function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.log=function(){var e;return"object"===("undefined"==typeof console?"undefined":r(console))&&console.log&&(e=console).log.apply(e,arguments)},t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;var n="color: "+this.color;t.splice(1,0,n,"color: inherit");var r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&"%c"===e&&(i=++r)}),t.splice(i,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){var e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.exports=n(23)(t),e.exports.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},function(e,t){var n=1e3,r=60*n,i=60*r,o=24*i,s=7*o,a=365.25*o;function c(e,t,n,r){var i=t>=1.5*n;return Math.round(e/n)+" "+r+(i?"s":"")}e.exports=function(e,t){t=t||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\-?\d?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!t)return;var c=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*a;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*o;case"hours":case"hour":case"hrs":case"hr":case"h":return c*i;case"minutes":case"minute":case"mins":case"min":case"m":return c*r;case"seconds":case"second":case"secs":case"sec":case"s":return c*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===l&&!1===isNaN(e))return t.long?function(e){var t=Math.abs(e);if(t>=o)return c(e,t,o,"day");if(t>=i)return c(e,t,i,"hour");if(t>=r)return c(e,t,r,"minute");if(t>=n)return c(e,t,n,"second");return e+" ms"}(e):function(e){var t=Math.abs(e);if(t>=o)return Math.round(e/o)+"d";if(t>=i)return Math.round(e/i)+"h";if(t>=r)return Math.round(e/r)+"m";if(t>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},function(e,t,n){"use strict";var r=n(60),i=n(14);t.init=function(e){e.inspectOpts={};for(var n=Object.keys(t.inspectOpts),r=0;r<n.length;r++)e.inspectOpts[n[r]]=t.inspectOpts[n[r]]},t.log=function(){return process.stderr.write(i.format.apply(i,arguments)+"\n")},t.formatArgs=function(n){var r=this.namespace;if(this.useColors){var i=this.color,o="[3"+(i<8?i:"8;5;"+i),s="  ".concat(o,";1m").concat(r," [0m");n[0]=s+n[0].split("\n").join("\n"+s),n.push(o+"m+"+e.exports.humanize(this.diff)+"[0m")}else n[0]=function(){if(t.inspectOpts.hideDate)return"";return(new Date).toISOString()+" "}()+r+" "+n[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?Boolean(t.inspectOpts.colors):r.isatty(process.stderr.fd)},t.colors=[6,2,3,4,5,1];try{var o=n(61);o&&(o.stderr||o).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(function(e){return/^debug_/i.test(e)}).reduce(function(e,t){var n=t.substring(6).toLowerCase().replace(/_([a-z])/g,function(e,t){return t.toUpperCase()}),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[n]=r,e},{}),e.exports=n(23)(t);var s=e.exports.formatters;s.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).replace(/\s*\n\s*/g," ")},s.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},function(e,t){e.exports=require("tty")},function(e,t,n){"use strict";const r=n(6),i=n(62),o=process.env;let s;function a(e){return function(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}(function(e){if(!1===s)return 0;if(i("color=16m")||i("color=full")||i("color=truecolor"))return 3;if(i("color=256"))return 2;if(e&&!e.isTTY&&!0!==s)return 0;const t=s?1:0;if("win32"===process.platform){const e=r.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in o)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(e=>e in o)||"codeship"===o.CI_NAME?1:t;if("TEAMCITY_VERSION"in o)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(o.TEAMCITY_VERSION)?1:0;if("truecolor"===o.COLORTERM)return 3;if("TERM_PROGRAM"in o){const e=parseInt((o.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(o.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(o.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(o.TERM)?1:"COLORTERM"in o?1:(o.TERM,t)}(e))}i("no-color")||i("no-colors")||i("color=false")?s=!1:(i("color")||i("colors")||i("color=true")||i("color=always"))&&(s=!0),"FORCE_COLOR"in o&&(s=0===o.FORCE_COLOR.length||0!==parseInt(o.FORCE_COLOR,10)),e.exports={supportsColor:a,stdout:a(process.stdout),stderr:a(process.stderr)}},function(e,t,n){"use strict";e.exports=((e,t)=>{t=t||process.argv;const n=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(n+e),i=t.indexOf("--");return-1!==r&&(-1===i||r<i)})},function(e,t){e.exports=require("zlib")},function(e){e.exports={_args:[["axios@0.18.0","D:\\code\\xl_vip_client_develop\\vip-download-develop\\thunderx_plugin_vip_download-develop-9.2.1"]],_from:"axios@0.18.0",_id:"axios@0.18.0",_inBundle:!1,_integrity:"sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=",_location:"/axios",_phantomChildren:{},_requested:{type:"version",registry:!0,raw:"axios@0.18.0",name:"axios",escapedName:"axios",rawSpec:"0.18.0",saveSpec:null,fetchSpec:"0.18.0"},_requiredBy:["/"],_resolved:"http://xnpm.repo.xunlei.cn/axios/-/axios-0.18.0.tgz",_spec:"0.18.0",_where:"D:\\code\\xl_vip_client_develop\\vip-download-develop\\thunderx_plugin_vip_download-develop-9.2.1",author:{name:"Matt Zabriskie"},browser:{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},bugs:{url:"https://github.com/axios/axios/issues"},bundlesize:[{path:"./dist/axios.min.js",threshold:"5kB"}],dependencies:{"follow-redirects":"^1.3.0","is-buffer":"^1.1.5"},description:"Promise based HTTP client for the browser and node.js",devDependencies:{bundlesize:"^0.5.7",coveralls:"^2.11.9","es6-promise":"^4.0.5",grunt:"^1.0.1","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.0.0","grunt-contrib-nodeunit":"^1.0.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^19.0.0","grunt-karma":"^2.0.0","grunt-ts":"^6.0.0-beta.3","grunt-webpack":"^1.0.18","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1",karma:"^1.3.0","karma-chrome-launcher":"^2.0.0","karma-coverage":"^1.0.0","karma-firefox-launcher":"^1.0.0","karma-jasmine":"^1.0.2","karma-jasmine-ajax":"^0.1.13","karma-opera-launcher":"^1.0.0","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^1.1.0","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.7","karma-webpack":"^1.7.0","load-grunt-tasks":"^3.5.2",minimist:"^1.2.0",sinon:"^1.17.4",typescript:"^2.0.3","url-search-params":"^0.6.1",webpack:"^1.13.1","webpack-dev-server":"^1.14.1"},homepage:"https://github.com/axios/axios",keywords:["xhr","http","ajax","promise","node"],license:"MIT",main:"index.js",name:"axios",repository:{type:"git",url:"git+https://github.com/axios/axios.git"},scripts:{build:"NODE_ENV=production grunt build",coveralls:"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",examples:"node ./examples/server.js",postversion:"git push && git push --tags",preversion:"npm test",start:"node ./sandbox/server.js",test:"grunt test && bundlesize",version:"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json"},typings:"./index.d.ts",version:"0.18.0"}},function(e,t,n){"use strict";var r=n(0);function i(){this.handlers=[]}i.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},i.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},i.prototype.forEach=function(e){r.forEach(this.handlers,function(t){null!==t&&e(t)})},e.exports=i},function(e,t,n){"use strict";var r=n(0),i=n(67),o=n(24),s=n(10),a=n(68),c=n(69);function l(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return l(e),e.baseURL&&!a(e.url)&&(e.url=c(e.baseURL,e.url)),e.headers=e.headers||{},e.data=i(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||s.adapter)(e).then(function(t){return l(e),t.data=i(t.data,t.headers,e.transformResponse),t},function(t){return o(t)||(l(e),t&&t.response&&(t.response.data=i(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},function(e,t,n){"use strict";var r=n(0);e.exports=function(e,t,n){return r.forEach(n,function(n){e=n(e,t)}),e}},function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,n){"use strict";var r=n(25);function i(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new r(e),t(n.reason))})}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var e;return{token:new i(function(t){e=t}),cancel:e}},e.exports=i},function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}}]);
//# sourceMappingURL=index.js.map