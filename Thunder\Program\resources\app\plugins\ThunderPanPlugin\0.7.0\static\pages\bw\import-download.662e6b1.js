/*! For license information please see ../../LICENSES */
(window.webpackJsonp=window.webpackJsonp||[]).push([[12],{1329:function(e,o,t){"use strict";t.d(o,"a",(function(){return n}));var r=t(1332);function n(e){return Object(r.createDecorator)((function(o,t){if(!o.methods)throw new Error("This decorator must be used on a vue component method.");var r="number"==typeof e?e:e.time,n=o.methods[t],i=0,a=function(){i&&(clearTimeout(i),i=0)};o.methods[t]=function(){for(var e=this,o=[],t=0;t<arguments.length;t++)o[t]=arguments[t];a(),i=setTimeout((function(){i=0,n.apply(e,o)}),r)}}))}},1332:function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0});var r,n=(r=t(5))&&"object"==typeof r&&"default"in r?r.default:r,i="undefined"!=typeof Reflect&&Reflect.defineMetadata;function a(e,o,t){(t?Reflect.getOwnMetadataKeys(o,t):Reflect.getOwnMetadataKeys(o)).forEach((function(r){var n=t?Reflect.getOwnMetadata(r,o,t):Reflect.getOwnMetadata(r,o);t?Reflect.defineMetadata(r,n,e,t):Reflect.defineMetadata(r,n,e)}))}var c={__proto__:[]}instanceof Array;var d=["data","beforeCreate","created","beforeMount","mounted","beforeDestroy","destroyed","beforeUpdate","updated","activated","deactivated","render","errorCaptured"];function l(e,o){void 0===o&&(o={}),o.name=o.name||e._componentTag||e.name;var t=e.prototype;Object.getOwnPropertyNames(t).forEach((function(e){if("constructor"!==e)if(d.indexOf(e)>-1)o[e]=t[e];else{var r=Object.getOwnPropertyDescriptor(t,e);void 0!==r.value?"function"==typeof r.value?(o.methods||(o.methods={}))[e]=r.value:(o.mixins||(o.mixins=[])).push({data:function(){var o;return(o={})[e]=r.value,o}}):(r.get||r.set)&&((o.computed||(o.computed={}))[e]={get:r.get,set:r.set})}})),(o.mixins||(o.mixins=[])).push({data:function(){return function(e,o){var t=o.prototype._init;o.prototype._init=function(){var o=this,t=Object.getOwnPropertyNames(e);if(e.$options.props)for(var r in e.$options.props)e.hasOwnProperty(r)||t.push(r);t.forEach((function(t){"_"!==t.charAt(0)&&Object.defineProperty(o,t,{get:function(){return e[t]},set:function(o){e[t]=o},configurable:!0})}))};var r=new o;o.prototype._init=t;var n={};return Object.keys(r).forEach((function(e){void 0!==r[e]&&(n[e]=r[e])})),n}(this,e)}});var r=e.__decorators__;r&&(r.forEach((function(e){return e(o)})),delete e.__decorators__);var l,s,b=Object.getPrototypeOf(e.prototype),u=b instanceof n?b.constructor:n,p=u.extend(o);return function(e,o,t){Object.getOwnPropertyNames(o).forEach((function(r){if("prototype"!==r){var n=Object.getOwnPropertyDescriptor(e,r);if(!n||n.configurable){var i,a,d=Object.getOwnPropertyDescriptor(o,r);if(!c){if("cid"===r)return;var l=Object.getOwnPropertyDescriptor(t,r);if(i=d.value,a=typeof i,null!=i&&("object"===a||"function"===a)&&l&&l.value===d.value)return}0,Object.defineProperty(e,r,d)}}}))}(p,e,u),i&&(a(l=p,s=e),Object.getOwnPropertyNames(s.prototype).forEach((function(e){a(l.prototype,s.prototype,e)})),Object.getOwnPropertyNames(s).forEach((function(e){a(l,s,e)}))),p}function s(e){return"function"==typeof e?l(e):function(o){return l(o,e)}}s.registerHooks=function(e){d.push.apply(d,e)},o.default=s,o.createDecorator=function(e){return function(o,t,r){var n="function"==typeof o?o:o.constructor;n.__decorators__||(n.__decorators__=[]),"number"!=typeof r&&(r=void 0),n.__decorators__.push((function(o){return e(o,t,r)}))}},o.mixins=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];return n.extend({mixins:e})}},1333:function(e,o,t){var r=t(1359);"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,t(90).default)("6972a4fa",r,!0,{sourceMap:!1})},1357:function(e,o,t){var r=t(1425);"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,t(90).default)("596b2acb",r,!0,{sourceMap:!1})},1358:function(e,o,t){"use strict";var r=t(1333);t.n(r).a},1359:function(e,o,t){var r=t(89),n=t(126),i=t(675),a=t(676),c=t(677),d=t(678),l=t(679),s=t(680),b=t(681),u=t(682);o=r(!1);var p=n(i),y=n(a),f=n(c),h=n(d),x=n(l),g=n(s),m=n(b),v=n(u);o.push([e.i,'pre{font-family:"microsoft yahei",-apple-system,"PingFang SC","simsun",Arial,sans-serif}@font-face{font-family:"dincond";src:url('+p+') format("woff")}body::-webkit-scrollbar{width:8px;background:transparent}body::-webkit-scrollbar:horizontal{height:8px}body::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}body::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body::-webkit-scrollbar-corner{background:transparent}body .td-progress-bar__outer,body .td-slider__bar{background:var(--background-progress)}body .td-input__inner,body .td-textarea__inner{background:var(--background-module);border-color:var(--color-border-form)}body .td-input__inner::-moz-placeholder,body .td-textarea__inner::-moz-placeholder{color:var(--color-secondary)}body .td-input__inner:-ms-input-placeholder,body .td-textarea__inner:-ms-input-placeholder{color:var(--color-secondary)}body .td-input__inner::-ms-input-placeholder,body .td-textarea__inner::-ms-input-placeholder{color:var(--color-secondary)}body .td-input__inner::placeholder,body .td-textarea__inner::placeholder{color:var(--color-secondary)}body .td-textarea__inner::-webkit-scrollbar-thumb{border-right:unset}body .is-disabled .td-input__inner{background:var(--color-border)}body .td-textarea.is-disabled .td-textarea__inner{background:var(--background-main)}body .td-checkbox{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}body .td-checkbox .td-checkbox__inner{width:14px;height:14px;background:var(--background-area);border-color:var(--color-border-form)}body .td-checkbox .td-checkbox__inner:hover{border-color:var(--color-primary)}body .td-checkbox .td-checkbox__inner:checked:before{left:2px;width:7px;border-width:2px;border-color:var(--color-primary)}body .td-checkbox.is-indeterminate .td-checkbox__inner{background:unset;border-color:var(--color-border-form)}body .td-checkbox.is-indeterminate .td-checkbox__inner:before{top:50%;left:50%;width:6px;height:6px;background:var(--color-primary);border:unset;transform:translate3d(-50%,-50%,0)}body .td-checkbox.is-indeterminate .td-checkbox__inner:hover{border-color:var(--color-primary)}body .td-checkbox.is-disabled{cursor:default}body .td-checkbox.is-disabled .td-checkbox__inner,body .td-checkbox.is-disabled .td-checkbox__inner:checked{background:var(--color-border);border:1px solid var(--color-border-form)}body .td-checkbox.is-disabled .td-checkbox__inner:checked:before{border-color:var(--color-border-form)}body .td-checkbox.is-disabled.is-indeterminate .td-checkbox__inner{background:var(--color-border);border:1px solid var(--color-border-form)}body .td-checkbox.is-disabled.is-indeterminate .td-checkbox__inner:before{background:var(--color-border-form)}body .td-icon-success:before{font-family:"xly-icon"!important;content:"\\e7b6"}body .td-dialog{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}body .td-dialog__close{-webkit-app-region:no-drag}body .td-select,body .td-select.is-disabled{background:transparent}body .td-select.is-disabled .td-select-group__label{color:var(--color-disabled)}body .td-select.is-disabled .td-select-group{background:var(--background-main);border-color:var(--color-border-form)}body .td-select.is-disabled .td-select__drop{opacity:.2}body .td-select.is-disabled .td-input__inner{background:transparent;color:var(--color-disabled)}body .td-select-group,body .td-select-group:hover{border-color:var(--color-border-form)}body .td-select__drop{color:var(--color-secondary)}body .td-tooltip{box-shadow:0 0 10px var(--color-shadow)}body .td-poper__arrow,body .td-tooltip{background:var(--background-module)}body .td-radio__inner{width:14px;height:14px}body .td-radio__inner,body .td-radio__inner:checked{border-color:var(--color-border-form)}body .td-radio__inner:checked:after{width:8px;height:8px}body .td-table{background:unset;border-color:var(--color-border)}body .td-table th{height:23px;border-bottom:unset}body .td-table td{height:28px;border-bottom:unset}body .td-table__panel::-webkit-scrollbar-thumb{border-right:var(--background-module)}body .td-button{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}body .td-button.is-disabled{color:var(--color-disabled);background:var(--background-main);cursor:default}body .td-button.td-button--large{font-size:14px}body .td-button.td-button--secondary{color:var(--color-auxiliary);background:var(--background-main)}body .td-button.td-button--secondary:hover{color:var(--color-primary)}body .td-button.td-button--secondary.is-disabled,body .td-button.td-button--secondary.is-disabled:hover{color:var(--color-disabled)}body .td-button.td-button--other{font-size:12px;color:var(--color-default);border-color:var(--color-border-form)}body .td-button.td-button--other:hover{color:var(--color-primary)}body .td-button.td-button--other:active{background:var(--color-item-active);border-color:var(--color-primary)}body .td-button.td-button--other.is-disabled{color:var(--color-disabled);background:var(--background-main);border-color:var(--color-border)}body .td-button.td-button--text.is-disabled{color:var(--color-secondary);background:transparent}body .td-button.is-status{background:rgba(63,133,255,.5)}body .td-icon-arrow-drop:before{font-family:"xly-icon"!important;content:"\\e7b1"}body .td-dropdown-menu{background:var(--background-module);box-shadow:0 0 20px var(--color-shadow);border:unset}body .td-dropdown-menu::-webkit-scrollbar{width:8px;background:transparent}body .td-dropdown-menu::-webkit-scrollbar:horizontal{height:8px}body .td-dropdown-menu::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}body .td-dropdown-menu::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .td-dropdown-menu::-webkit-scrollbar-corner{background:transparent}body .td-dropdown-menu::-webkit-scrollbar{width:10px}body .td-dropdown-menu::-webkit-scrollbar-thumb{border-left:2px solid var(--background-module)}body .td-dropdown-menu__item:hover{background:var(--color-list-hover)}body .xly-pop-loading{position:fixed;top:50%;left:50%;display:flex;flex-direction:column;justify-content:center;align-items:center;padding:0;width:120px;height:120px;background:var(--background-module);box-shadow:0 0 10px var(--color-shadow);border-radius:6px;transform:translate3d(-50%,-50%,0);z-index:99;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}body .xly-pop-loading .td-dialog__header{min-height:unset}body .xly-pop-loading .td-dialog__body{display:flex;flex-direction:column;justify-content:center;align-items:center}body .xly-pop-loading .td-dialog__close:hover{background:unset}body .xly-pop-loading .td-dialog__close:hover .td-icon-close{color:var(--color-primary)}body .xly-pop-loading .td-icon-close{position:absolute;top:6px;right:6px;color:var(--color-secondary);cursor:pointer}body .xly-pop-loading p{margin-top:8px;white-space:nowrap}.xly-line{width:1px;height:10px;margin:0 4px;background:var(--color-border)}.xly-select{position:relative;-webkit-appearance:unset;padding:0 24px 0 8px;height:30px;color:var(--color-default);font-size:12px;border:1px solid var(--color-border-form);border-radius:4px;outline:none;background:url('+y+") 99% no-repeat;background-size:16px auto}.xly-select option{height:30px;background:var(--background-module)}.xly-img-empty-netdisk{width:160px;height:140px;background:url("+f+") no-repeat;background-size:100% auto}.xly-img-network{width:160px;height:140px;background:url("+h+") no-repeat;background-size:100% auto}.xly-img-404{width:160px;height:140px;background:url("+x+") no-repeat;background-size:100% auto}.xly-img-message{width:160px;height:140px;background:url("+g+') no-repeat;background-size:100% auto}.xly-empty{display:flex;justify-content:center;align-items:center;width:100%;height:100%;background:var(--background-main)}.xly-empty i.xly-icon-logo{font-size:120px;color:var(--color-border)}.xly-404,.xly-error{display:flex;justify-content:center;align-items:center;width:100%;height:100%}.xly-error{flex-direction:column;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.xly-error p{margin:20px 0 22px;color:var(--color-secondary)}.xly-error .td-button{margin:0 6px;width:72px}.xly-error__button{display:flex}.xly-error__button .td-button.td-button--other{line-height:23px}body .td-cover{line-height:1.5}body .td-cover .td-icon-error,body .td-cover .td-icon-warning{font-family:"xly-icon"!important}body .td-cover .td-icon-error:before,body .td-cover .td-icon-warning:before{content:"\\e7a5"}body .td-cover.td-cover--message{justify-content:flex-end}body .td-cover .td-message{margin-bottom:66px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}body .td-cover .td-message__text{margin-left:12px}body .td-cover .td-message--error{color:#fff;background:#e66056}body .td-cover .td-message--warning{color:#fff;background:#ff9700}body .td-cover .td-message--success{color:#fff;background:#38b878}body .td-icon-close:before{font-family:"xly-icon";content:"\\e768"}.xly-tips{display:none;position:absolute;left:50%;bottom:28px;padding:0 12px;color:#fff;white-space:nowrap;text-align:center;line-height:30px;background:#46423c;box-shadow:0 0 10px 0 rgba(26,26,26,.2);border-radius:4px;transform:translateX(-50%);z-index:2}i.xly-img-icon-rocket{margin-right:5px;width:27px;height:15px;background:url('+m+');background-size:100% auto}.xly-more-drop{display:none;position:absolute;top:24px;left:0;min-width:100px;padding:6px 0;width:76px;background:var(--background-module);box-shadow:0 0 10px 0 var(--color-shadow);border-radius:4px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.xly-more-drop li{padding-left:20px;height:30px;line-height:30px;cursor:default}.xly-more-drop li:hover{background:var(--color-list-hover)}.xly-point{display:inline-block;width:12px;vertical-align:top}.xly-point:before{content:"";animation:content 1.5s infinite both}@keyframes content{0%{content:""}25%{content:"."}50%{content:".."}75%{content:"..."}}.xly-drop-file{top:0;padding:6px 0 0;background:var(--background-module)}.xly-drop-file .td-dropdown-menu__item{position:relative;padding:0 0 0 8px;line-height:30px}.xly-drop-file .td-dropdown-menu__item:hover .xly-drop-file__close{display:block}.xly-drop-file ul{max-height:unset;overflow:visible}.xly-drop-file__close{display:none;position:absolute;top:0;right:12px;color:var(--color-secondary)}.xly-drop-file__close:hover{color:var(--color-primary)}.xly-drop-file__link{margin-top:6px;display:block;line-height:30px;text-align:center;color:var(--color-default);border-top:1px solid var(--color-border)}.xly-drop-file__link:hover{color:var(--color-primary)}i.img-icon-loading{display:block;width:48px;height:48px;background:url('+v+") no-repeat;background-size:100%;animation:rotate 2s linear infinite both}@keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(1turn)}}",""]),e.exports=o},1421:function(e,o,t){e.exports=t.p+"img/2T.9289471.png"},1422:function(e,o,t){e.exports=t.p+"img/3T.7fcd0d2.png"},1423:function(e,o,t){e.exports=t.p+"img/6T.8630a22.png"},1424:function(e,o,t){"use strict";var r=t(1357);t.n(r).a},1425:function(e,o,t){(o=t(89)(!1)).push([e.i,'body .xly-dialog-success{width:420px}body .xly-dialog-success .td-dialog__close{color:#fff}.xly-dialog-success__banner{width:calc(100% + 60px);height:158px;margin:-30px 0 0 -30px;background:#ccc;border-radius:4px 4px 0 0;overflow:hidden}.xly-dialog-success__banner img{width:100%;height:100%;-o-object-fit:cover;object-fit:cover}.xly-dialog-success__content{margin:30px 0}.xly-dialog-success__content h2{font-weight:700;font-size:16px;color:var(--color-title);text-align:center}.xly-dialog-success__content h2 .xly-dialog-success__content-text.is-6t{color:#c27a13}.xly-dialog-success__content h2 .xly-dialog-success__content-text.is-2t{color:#3f85ff}.xly-dialog-success__content h2 .xly-dialog-success__content-text.is-3t{color:#e9b23e}.xly-dialog-success__content p{margin-top:8px;text-align:center;line-height:18px;color:var(--color-secondary)}.xly-dialog-success__content .td-button{margin-top:16px;width:100%}.xly-dialog-success__code{margin-top:40px}.xly-dialog-success__code--large{margin-top:-15px;margin-bottom:30px}.xly-dialog-success__code--large .xly-dialog-success__code-image{width:145px;height:145px}.xly-dialog-success__tag{margin-right:4px;width:58px;height:16px;line-height:16px;font-size:12px;text-align:center;color:#fff;background:#ff6668;border-radius:8px 8px 0 8px}.xly-dialog-success__code-text{display:flex;justify-content:center;align-items:center;font-size:14px;color:var(--color-title)}.xly-dialog-success__code-image{padding:3px;margin:14px auto;width:90px;height:90px;border:1px solid var(--color-border)}.xly-dialog-success__code-image img{width:100%;height:100%}.xly-dialog-success__code-tips{text-align:center;color:var(--color-secondary)}body .xly-file-import .td-table__body-wrapper{height:120px;overflow-y:auto}body .xly-file-import .td-table__body-wrapper::-webkit-scrollbar{width:8px;background:transparent}body .xly-file-import .td-table__body-wrapper::-webkit-scrollbar:horizontal{height:8px}body .xly-file-import .td-table__body-wrapper::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}body .xly-file-import .td-table__body-wrapper::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .xly-file-import .td-table__body-wrapper::-webkit-scrollbar-corner{background:transparent}body .xly-file-import .td-table__body-wrapper::-webkit-scrollbar-thumb{border-right:0}body .xly-file-import tr th{height:23px;color:var(--color-secondary)}body .xly-file-import tr th:first-child .td-table__text{padding-left:10px}body .xly-file-import tr th:nth-child(2) .td-table__text{position:relative}body .xly-file-import tr th:nth-child(2) .td-table__text:before{position:absolute;top:7px;left:0;width:1px;height:10px;background:var(--color-border);content:""}body .xly-file-import tr td:nth-child(2){color:var(--color-secondary)}.xly-file-import__label{margin-left:10px;width:210px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}',""]),e.exports=o},1513:function(e,o,t){"use strict";t.r(o);t(32);var r=t(9),n=(t(10),t(11),t(7),t(83)),i=(t(471),t(33)),a=t(40),c=t(70),d=t(71),l=t(37),s=(t(4),t(29)),b=t(18),u=t(1421),p=t.n(u),y=t(1422),f=t.n(y),h=t(1423),x=t.n(h),g=t(8),m=t(1),v=t(13),_=t(1329),k=t(82),w=t.n(k),O=t(139);function j(e){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var t,r=Object(l.a)(e);if(o){var n=Object(l.a)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return Object(d.a)(this,t)}}var T,z=function(e,o,t,r){var n,i=arguments.length,a=i<3?o:null===r?r=Object.getOwnPropertyDescriptor(o,t):r;if("object"===("undefined"==typeof Reflect?"undefined":Object(s.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,o,t,r);else for(var c=e.length-1;c>=0;c--)(n=e[c])&&(a=(i<3?n(a):i>3?n(o,t,a):n(o,t))||a);return i>3&&a&&Object.defineProperty(o,t,a),a},R=function(e,o,t,r){return new(t||(t=Promise))((function(n,i){function a(e){try{d(r.next(e))}catch(e){i(e)}}function c(e){try{d(r.throw(e))}catch(e){i(e)}}function d(e){var o;e.done?n(e.value):(o=e.value,o instanceof t?o:new t((function(e){e(o)}))).then(a,c)}d((r=r.apply(e,o||[])).next())}))};!function(e){e[e.unknow=1]="unknow",e[e.text=2]="text",e[e.image=3]="image",e[e.video=4]="video",e[e.audio=5]="audio",e[e.archive=6]="archive",e[e.bt=7]="bt"}(T||(T={}));var P=function(e){Object(c.a)(t,e);var o=j(t);function t(){var e,r,a;return Object(i.a)(this,t),(a=o.apply(this,arguments)).visible=!0,a.hasRecordToImport=!1,a.bwInstance=null,a.templates=Object.freeze((e={},Object(n.a)(e,O.VipType.normal,{volume:"2T",class:"is-2t",img:p.a}),Object(n.a)(e,O.VipType.normalVip,{volume:"3T",class:"is-3t",img:f.a}),Object(n.a)(e,O.VipType.platinum,{volume:"3T",class:"is-3t",img:f.a}),Object(n.a)(e,O.VipType.superv,{volume:"6T",class:"is-6t",img:x.a}),e)),a.columns=Object.freeze([{label:"文件名称",prop:"filename"},{label:"大小",prop:"filesize",width:100}]),a.fileTypeMapping=(r={},Object(n.a)(r,T.unknow,"xly-type-unknow"),Object(n.a)(r,T.text,"xly-type-txt"),Object(n.a)(r,T.image,"xly-type-pic"),Object(n.a)(r,T.video,"xly-type-video"),Object(n.a)(r,T.audio,"xly-type-music"),Object(n.a)(r,T.archive,"xly-type-rar"),Object(n.a)(r,T.bt,"xly-type-bt-link"),r),a.history=[],a}return Object(a.a)(t,[{key:"mounted",value:function(){return R(this,void 0,void 0,regeneratorRuntime.mark((function e(){var o=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.$eventTrack("firstlogin_onebutton_import_pop_show"),e.next=3,this.getDownloadHistory(0,50);case 3:this.hasRecordToImport=this.history.length>0,this.$nextTick((function(){return R(o,void 0,void 0,regeneratorRuntime.mark((function e(){var o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.hasRecordToImport){e.next=7;break}return e.next=3,w.a.getCurrentWindow().__resolve();case 3:o=e.sent,this.bwInstance=o,o.setSize(420,520),o.center();case 7:case"end":return e.stop()}}),e,this)})))})),this.hasRecordToImport&&this.history.length>=50&&this.getDownloadHistory(50,100);case 6:case"end":return e.stop()}}),e,this)})))}},{key:"getDownloadHistory",value:function(e){function o(o,t){return e.apply(this,arguments)}return o.toString=function(){return e.toString()},o}((function(e,o){var t=this;return getDownloadHistory(e,o,this.$store.state.users.curUser.userId,this.$store.state.clientInfo.peerId).then((function(e){0===e.result&&(t.history=[].concat(Object(r.a)(t.history),Object(r.a)(e.task_info.map((function(e){return e.icon=t.fileTypeMapping[e.filetype],e.filesize=Object(m.formatSize)(e.filesize),e})))))})).catch((function(e){return console.error(e)}))}))},{key:"onCloseClick",value:function(){this.$eventTrack("firstlogin_onebutton_import_pop_click",{clickid:"close"}),this.closeWindow()}},{key:"closeWindow",value:function(){return R(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:window.close();case 1:case"end":return e.stop()}}),e)})))}},{key:"importDownloadRecord",value:function(){var e=this;this.$eventTrack("firstlogin_onebutton_import_pop_click",{clickid:"import"}),Object(g.u)().then((function(){return R(e,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v.client.callRemoteClientFunction("ThunderPanPluginWebview","showImportHint");case 2:this.$eventTrack("transmission_yun_add_create",{from:"import"}),this.closeWindow();case 4:case"end":return e.stop()}}),e,this)})))})).catch((function(e){console.error(e)}))}},{key:"vipType",get:function(){return this.$store.getters["users/curVipType"]}},{key:"currentTemplate",get:function(){return this.templates[this.vipType]||this.templates[O.VipType.normal]}}]),t}(b.Vue);z([Object(_.a)(500)],P.prototype,"importDownloadRecord",null);var D=P=z([Object(b.Component)({head:{title:"导入"}})],P),C=(t(1424),t(1358),t(72)),I=Object(C.a)(D,(function(){var e=this,o=e.$createElement,t=e._self._c||o;return t("td-dialog",{attrs:{visible:e.visible,"custom-class":"xly-dialog-success"},on:{"update:visible":function(o){e.visible=o},ok:function(o){e.visible=!1},close:e.onCloseClick}},[t("div",{staticClass:"xly-dialog-success__banner"},[t("img",{attrs:{src:e.currentTemplate.img,alt:e.currentTemplate.volume+"空间",draggable:"false"}})]),e._v(" "),t("div",{staticClass:"xly-dialog-success__content"},[t("h2",[e._v("登录成功，恭喜你获得 "),t("span",{staticClass:"xly-dialog-success__content-text",class:e.currentTemplate.class},[e._v(e._s(e.currentTemplate.volume))]),e._v(" 网盘空间！")]),e._v(" "),e.hasRecordToImport?t("p",[e._v("\n      你可以将最近100条迅雷下载内容一键导入到"),t("br"),e._v("云盘，即刻体验迅雷云盘服务\n    ")]):e._e()]),e._v(" "),e.hasRecordToImport?t("td-table",{staticClass:"xly-file-import",attrs:{columns:e.columns,data:e.history},scopedSlots:e._u([{key:"default",fn:function(o){var r=o.prop,n=(o.value,o.row);return["filename"===r?[t("i",{staticClass:"xly-icon-type is-small",class:n.icon}),e._v(" "),t("span",{staticClass:"xly-file-import__label"},[e._v(e._s(n.filename))])]:e._e()]}}],null,!1,1760350566)}):e._e(),e._v(" "),e.hasRecordToImport?t("td-button",{attrs:{slot:"footer",size:"large"},on:{click:e.importDownloadRecord},slot:"footer"},[e._v("\n    一键导入\n  ")]):t("td-button",{attrs:{slot:"footer",size:"large"},on:{click:e.closeWindow},slot:"footer"},[e._v("\n    知道了\n  ")])],1)}),[],!1,null,null,null);o.default=I.exports}}]);
//# sourceMappingURL=import-download.662e6b1.js.map