class_name UIManager
extends Control

# UI管理器 - 负责游戏界面的显示和交互

@onready var play_button: But<PERSON>
@onready var pass_button: But<PERSON>
@onready var restart_button: But<PERSON>
@onready var game_info_label: Label
@onready var player_info_labels: Array[Label] = []

var game_manager: GameManager
var card_displays: Array[Node] = []

signal play_cards_requested()
signal pass_turn_requested()
signal restart_game_requested()

func _ready():
	setup_ui_elements()
	connect_ui_signals()

func setup_ui_elements():
	"""设置UI元素"""
	# 创建按钮
	play_button = Button.new()
	play_button.text = "出牌"
	play_button.size = Vector2(100, 40)
	play_button.position = Vector2(500, 650)
	add_child(play_button)
	
	pass_button = Button.new()
	pass_button.text = "过牌"
	pass_button.size = Vector2(100, 40)
	pass_button.position = Vector2(620, 650)
	add_child(pass_button)
	
	restart_button = Button.new()
	restart_button.text = "重新开始"
	restart_button.size = Vector2(100, 40)
	restart_button.position = Vector2(740, 650)
	add_child(restart_button)
	
	# 创建信息标签
	game_info_label = Label.new()
	game_info_label.text = "欢迎来到斗地主游戏"
	game_info_label.size = Vector2(400, 30)
	game_info_label.position = Vector2(440, 20)
	game_info_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	add_child(game_info_label)
	
	# 创建玩家信息标签
	for i in range(3):
		var label = Label.new()
		label.text = "玩家%d" % (i + 1)
		label.size = Vector2(200, 30)
		
		match i:
			0:  # 下方玩家
				label.position = Vector2(540, 550)
			1:  # 左侧玩家
				label.position = Vector2(20, 300)
			2:  # 上方玩家
				label.position = Vector2(540, 80)
		
		add_child(label)
		player_info_labels.append(label)

func connect_ui_signals():
	"""连接UI信号"""
	play_button.pressed.connect(_on_play_button_pressed)
	pass_button.pressed.connect(_on_pass_button_pressed)
	restart_button.pressed.connect(_on_restart_button_pressed)

func set_game_manager(manager: GameManager):
	"""设置游戏管理器引用"""
	game_manager = manager
	
	# 连接游戏管理器信号
	game_manager.game_started.connect(_on_game_started)
	game_manager.game_ended.connect(_on_game_ended)
	game_manager.turn_changed.connect(_on_turn_changed)
	game_manager.landlord_selected.connect(_on_landlord_selected)

func _on_play_button_pressed():
	"""出牌按钮点击"""
	play_cards_requested.emit()

func _on_pass_button_pressed():
	"""过牌按钮点击"""
	pass_turn_requested.emit()

func _on_restart_button_pressed():
	"""重新开始按钮点击"""
	restart_game_requested.emit()

func _on_game_started():
	"""游戏开始"""
	game_info_label.text = "游戏开始！"
	update_button_states(true)

func _on_game_ended(winner: Player):
	"""游戏结束"""
	game_info_label.text = "%s 获胜！" % winner.player_name
	update_button_states(false)

func _on_turn_changed(player: Player):
	"""回合改变"""
	game_info_label.text = "轮到 %s 出牌" % player.player_name
	
	# 更新按钮状态
	var is_human_turn = player.player_type == Player.PlayerType.HUMAN
	play_button.disabled = not is_human_turn
	pass_button.disabled = not is_human_turn

func _on_landlord_selected(landlord: Player):
	"""地主选定"""
	game_info_label.text = "%s 成为地主" % landlord.player_name

func update_button_states(game_active: bool):
	"""更新按钮状态"""
	play_button.disabled = not game_active
	pass_button.disabled = not game_active

func update_all_players_display(players: Array[Player]):
	"""更新所有玩家显示"""
	for i in range(min(players.size(), player_info_labels.size())):
		update_player_display(players[i], i)

func update_player_display(player: Player, index: int):
	"""更新单个玩家显示"""
	if index >= player_info_labels.size():
		return
	
	var label = player_info_labels[index]
	var role_text = player.get_role_name() if player.role != Player.Role.FARMER else ""
	var card_count = player.get_hand_count()
	
	label.text = "%s %s\n手牌: %d张\n得分: %d" % [
		player.player_name,
		role_text,
		card_count,
		player.score
	]

func display_cards_on_table(cards: Array[Card], player_index: int):
	"""在桌面显示出的牌"""
	# 清除之前的卡牌显示
	clear_table_cards()
	
	if cards.is_empty():
		return
	
	# 计算卡牌显示位置
	var center_pos = Vector2(640, 360)
	var card_spacing = 60
	var start_x = center_pos.x - (cards.size() - 1) * card_spacing / 2
	
	for i in range(cards.size()):
		var card = cards[i]
		card.position = Vector2(start_x + i * card_spacing, center_pos.y)
		card.scale = Vector2(0.8, 0.8)  # 稍微缩小显示
		add_child(card)
		card_displays.append(card)

func clear_table_cards():
	"""清除桌面卡牌"""
	for card_display in card_displays:
		if is_instance_valid(card_display):
			card_display.queue_free()
	card_displays.clear()

func arrange_player_cards(player: Player):
	"""排列玩家手牌"""
	var cards = player.hand_cards
	if cards.is_empty():
		return
	
	var base_pos = player.get_display_position()
	var direction = player.get_card_layout_direction()
	var spacing = 50
	
	# 计算起始位置
	var total_width = (cards.size() - 1) * spacing
	var start_pos = base_pos - direction * total_width / 2
	
	for i in range(cards.size()):
		var card = cards[i]
		card.position = start_pos + direction * i * spacing
		card.original_position = card.position
		
		# 根据玩家位置调整卡牌显示
		match player.position_index:
			0:  # 下方玩家：正常显示
				card.scale = Vector2(1.0, 1.0)
				card.rotation = 0
			1:  # 左侧玩家：只显示背面
				card.scale = Vector2(0.8, 0.8)
				card.rotation = PI / 2
			2:  # 上方玩家：只显示背面
				card.scale = Vector2(0.8, 0.8)
				card.rotation = PI
		
		# 添加到场景
		if not card.get_parent():
			add_child(card)

func show_message(message: String, duration: float = 2.0):
	"""显示临时消息"""
	var message_label = Label.new()
	message_label.text = message
	message_label.size = Vector2(400, 50)
	message_label.position = Vector2(440, 300)
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	message_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	
	# 设置样式
	message_label.modulate = Color.YELLOW
	
	add_child(message_label)
	
	# 自动移除
	var tween = create_tween()
	tween.tween_property(message_label, "modulate:a", 0.0, duration)
	tween.tween_callback(message_label.queue_free)

func update_game_info(info: Dictionary):
	"""更新游戏信息显示"""
	var text = "当前玩家: %s" % info.get("current_player", "")
	if info.get("landlord", "") != "":
		text += " | 地主: %s" % info["landlord"]
	if info.get("last_played", "") != "":
		text += " | 上一手: %s" % info["last_played"]
	
	game_info_label.text = text

func highlight_current_player(player_index: int):
	"""高亮当前玩家"""
	for i in range(player_info_labels.size()):
		var label = player_info_labels[i]
		if i == player_index:
			label.modulate = Color.YELLOW
		else:
			label.modulate = Color.WHITE

func show_card_selection_hint(can_play: bool):
	"""显示选牌提示"""
	if can_play:
		show_message("请选择要出的牌，然后点击出牌按钮", 1.0)
	else:
		show_message("当前选择的牌无法出牌", 1.0)
