!function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=105)}([,,function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=n(15),o=n(33),s=Object.prototype.toString;function a(e){return"[object Array]"===s.call(e)}function u(e){return null!==e&&"object"===(void 0===e?"undefined":r(e))}function c(e){return"[object Function]"===s.call(e)}function l(e,t){if(null!=e)if("object"!==(void 0===e?"undefined":r(e))&&(e=[e]),a(e))for(var n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:a,isArrayBuffer:function(e){return"[object ArrayBuffer]"===s.call(e)},isBuffer:o,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:u,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===s.call(e)},isFile:function(e){return"[object File]"===s.call(e)},isBlob:function(e){return"[object Blob]"===s.call(e)},isFunction:c,isStream:function(e){return u(e)&&c(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:l,merge:function e(){var t={};function n(n,i){"object"===r(t[i])&&"object"===(void 0===n?"undefined":r(n))?t[i]=e(t[i],n):t[i]=n}for(var i=0,o=arguments.length;i<o;i++)l(arguments[i],n);return t},extend:function(e,t,n){return l(t,(function(t,r){e[r]=n&&"function"==typeof t?i(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},,,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getGBObjName=a,t.init=function(e){if(null===i){var t=a(o=e);i={},s()[t]=i}},t.hasAttr=function(e){return void 0!==u()[e]},t.setAttr=function(e,t){u()[e]=t},t.getAttr=function(e){return u()[e]},t.getEnvType=function(){return o},t.gbAttrNames={config:"config",stat:"stat",platformInfo:"platformInfo",innerQuickLogin:"innerQuickLogin",clientFeatureApi:"clientFeatureApi"};var r=t.gbEnvTypes={insideIframe:"insideIframe",outsideIframe:"outsideIframe",pluginIndex:"pluginIndex"},i=null,o=void 0;function s(){return window||global}function a(e){var t="";switch(e){case r.insideIframe:case r.outsideIframe:case r.node:t=e;break;default:t="default"}return t+"WebSdkGlobalObject_CA7FFF8A_0F5B_4654_822B_98B9E74F23DD"}function u(){if(null===i){var e=s();i=e[a(r.insideIframe)]||e[a(r.outsideIframe)]||e[a(r.pluginIndex)]}return i}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GBHelper=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(6));function o(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),this._attrName=e,this._target=void 0}t.GBHelper=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(o,[{key:"getTarget",value:function(){return void 0===this._target&&(i.hasAttr(this._attrName)?this._target=i.getAttr(this._attrName):this.setTarget({})),this._target}},{key:"setTarget",value:function(e){i.setAttr(this._attrName,e),this._target=i.getAttr(this._attrName)}},{key:"hasAttr",value:function(e){return void 0!==this.getTarget()[e]}},{key:"setAttr",value:function(e,t){this.getTarget()[e]=t}},{key:"getAttr",value:function(e){return this.getTarget()[e]}}]),o)},function(e,t,n){"use strict";var r=n(2),i=n(36),o=n(38),s=n(39),a=n(40),u=n(16);e.exports=function(e){return new Promise((function(t,c){var l=e.data,f=e.headers;r.isFormData(l)&&delete f["Content-Type"];var d=new XMLHttpRequest;if(e.auth){var h=e.auth.username||"",p=e.auth.password||"";f.Authorization="Basic "+btoa(h+":"+p)}if(d.open(e.method.toUpperCase(),o(e.url,e.params,e.paramsSerializer),!0),d.timeout=e.timeout,d.onreadystatechange=function(){if(d&&4===d.readyState&&(0!==d.status||d.responseURL&&0===d.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in d?s(d.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?d.response:d.responseText,status:d.status,statusText:d.statusText,headers:n,config:e,request:d};i(t,c,r),d=null}},d.onerror=function(){c(u("Network Error",e,null,d)),d=null},d.ontimeout=function(){c(u("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",d)),d=null},r.isStandardBrowserEnv()){var _=n(41),v=(e.withCredentials||a(e.url))&&e.xsrfCookieName?_.read(e.xsrfCookieName):void 0;v&&(f[e.xsrfHeaderName]=v)}if("setRequestHeader"in d&&r.forEach(f,(function(e,t){void 0===l&&"content-type"===t.toLowerCase()?delete f[t]:d.setRequestHeader(t,e)})),e.withCredentials&&(d.withCredentials=!0),e.responseType)try{d.responseType=e.responseType}catch(h){if("json"!==e.responseType)throw h}"function"==typeof e.onDownloadProgress&&d.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&d.upload&&d.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){d&&(d.abort(),c(e),d=null)})),void 0===l&&(l=null),d.send(l)}))}},,function(e,t,n){"use strict";var r=n(2),i=n(35),o={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a,u={adapter:("undefined"==typeof XMLHttpRequest&&"undefined"==typeof process||(a=n(8)),a),transformRequest:[function(e,t){return i(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(s(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return 200<=e&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(e){u.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){u.headers[e]=r.merge(o)})),e.exports=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function i(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";do{if(null==e)break;switch(void 0===e?"undefined":r(e)){case"string":t=e;break;case"number":case"boolean":t=e.toString()}}while(0);return t}t.forceToString=i,t.forceJsonSimpleValueToString=function e(t){var n={};do{if(null==t){n={};break}switch(void 0===t?"undefined":r(t)){case"object":for(var o in t)n[o]=e(t[o]);break;case"string":case"number":case"boolean":n=i(t,"")}}while(0);return n},t.combineJsonObject=function e(t,n){return function(t,n){var i=t;for(var o in n){var s=n[o];if("object"===(void 0===s?"undefined":r(s))){var a=i[o];i[o]=null==a?s:e(i[o],s)}else void 0!==s&&(i[o]=s)}return i}(JSON.parse(JSON.stringify(t)),JSON.parse(JSON.stringify(n)))},t.dateToTimeString=function(e){return e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()},t.parseJson=function(e){var t=null;try{t=JSON.parse(e)}catch(e){t=null}return t},t.stringifyJson=function(e){var t=void 0;try{t=JSON.stringify(e)}catch(e){t=void 0}return t}},,,,function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},function(e,t,n){"use strict";var r=n(37);e.exports=function(e,t,n,i,o){var s=new Error(e);return r(s,t,n,i,o)}},function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.gbAttrNames={otherInfo:"otherInfo",platformInfo:"platformInfo"},t.gbOtherInfoAttrNames={showLoginWndSource:"showLoginWndSource"},t.gbPlatformInfoAttrNames={deviceSign:"deviceSign"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MonitorStatServer=t.businessFlowKeys=t.monitorStatActions=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=n(29),o=t.monitorStatActions={request:"request",pagePerformance:"pagePerformance",businessFlow:"businessFlow",initEnv:"initEnv"};function s(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),this._statServer=new i.StatServer(e,"websdk-monitor2")}t.businessFlowKeys={loadLoginPluginFile:"loadLoginPluginFile",loadLoginPlugin:"loadLoginPlugin",openLoginWnd:"openLoginWnd",initLoginWnd:"initLoginWnd",closeLoginWnd:"closeLoginWnd"},t.MonitorStatServer=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(s,[{key:"setPublicData",value:function(e){return this._statServer.setPublicData(e)}},{key:"stat",value:function(e){return this._statServer.stat(e)}},{key:"statRequest",value:function(e){var t={type:o.request,extData1:e};return this.stat(t)}},{key:"statPagePerformance",value:function(e){var t={type:o.pagePerformance,extData2:e};return this.stat(t)}},{key:"statBusinessFlow",value:function(e){var t={type:o.businessFlow,extData3:e};return this.stat(t)}},{key:"statInitEnv",value:function(e){var t={type:o.initEnv,extData4:e};return this.stat(t)}}]),s)},function(e,t){e.exports=require("electron")},function(e,t,n){"use strict";e.exports=n(32)},,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(50),i=n(51),o=(s.prototype.get=function(){return this.requestServer},s);function s(){this.requestServer=null,this.requestServer=new r.RequestServer(new i.HttpRequest)}t.RequestServerLoader=o,t.requestServerLoader=new o},,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.gbConfig=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=n(6),o=n(7),s="sdkVersion",a="appId",u="appName",c="hostCategoryAppName",l="appVersion",f="analysisServer",d="syncPC",h="clientFeature",p=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(_,[{key:"setSDKVersion",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s,t)}},{key:"getSDKVersion",value:function(){return this._gbHelper.getAttr(s)}},{key:"setAppId",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(a,t)}},{key:"getAppId",value:function(){return this._gbHelper.getAttr(a)}},{key:"setAppName",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(u,t)}},{key:"getAppName",value:function(){return this._gbHelper.getAttr(u)}},{key:"setHostCategoryAppName",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(c,t)}},{key:"getHostCategoryAppName",value:function(){return this._gbHelper.getAttr(c)}},{key:"setAppVersion",value:function(e){var t="string"==typeof e&&0<e.length?e:"NONE";this._gbHelper.setAttr(l,t)}},{key:"getAppVersion",value:function(){return this._gbHelper.getAttr(l)}},{key:"setHL",value:function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr("hL",t)}},{key:"getHL",value:function(){return this._gbHelper.getAttr("hL")}},{key:"setAnalysisServer",value:function(e){var t=e;this._gbHelper.setAttr(f,t)}},{key:"getAnalysisServer",value:function(){return this._gbHelper.getAttr(f)}},{key:"setSyncPC",value:function(e){var t=!0===e;this._gbHelper.setAttr(d,t)}},{key:"getSyncPC",value:function(){return this._gbHelper.getAttr(d)}},{key:"setClientFeature",value:function(e){var t=!0===e;this._gbHelper.setAttr(h,t)}},{key:"getClientFeature",value:function(){return this._gbHelper.getAttr(h)}}]),_);function _(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,_),this._gbHelper=new o.GBHelper(i.gbAttrNames.config)}t.gbConfig=new p},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StatServer=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=n(11),o=n(26),s=n(53),a=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(6));function u(e,t){var n=2<arguments.length&&void 0!==arguments[2]&&arguments[2];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),this._topic=t,this._preUrl=(e||"https://xluser-test-ssl.n0808.com")+"/analysis-report/v1/"+t+"?msg=",this._needEncrypt=!!n,this._requestServer=o.requestServerLoader.get(),this._publicData={}}t.StatServer=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(u,[{key:"_report",value:function(e){var t,n=JSON.stringify(e),r=s.base64ServerLoader.get().encode(n),i=(r=(r=r.replace(/\+/g,"-")).replace(/\//g,"_")).length,o=0;2<=i&&"="===r.charAt(i-1)&&(o="="===r.charAt(i-2)?2:1),t=r.substr(0,i-o);var u=this._preUrl+t,c=!0;if(a.getEnvType()!==a.gbEnvTypes.pluginIndex){var l=l||parent.xdas;l&&"function"==typeof l.fireStatEvent&&(c=!1,l.fireStatEvent(u))}c&&this._requestServer.get(u,null,null,null).catch((function(e){}))}},{key:"setPublicData",value:function(e){this._publicData=e}},{key:"stat",value:function(e){var t=e||{},n=(0,i.combineJsonObject)({reportTime:(0,i.dateToTimeString)(new Date)},this._publicData);n=(0,i.combineJsonObject)(n,t),this._report(n)}}]),u)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.gbStat=t.gbStatAttrNames=void 0;function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=n(6),o=n(7);t.gbStatAttrNames={monitor:"monitor",userBehaviors:"userBehaviors"};var s=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(a,[{key:"hasAttr",value:function(e){return this._gbHelper.hasAttr(e)}},{key:"setAttr",value:function(e,t){this._gbHelper.setAttr(e,t)}},{key:"getAttr",value:function(e){return this._gbHelper.getAttr(e)}}]),a);function a(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),this._gbHelper=new o.GBHelper(i.gbAttrNames.stat)}t.gbStat=new s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(20),i=n(7),o=n(28),s=new i.GBHelper(r.gbAttrNames.platformInfo);t.getPublicData=function(){return{appId:o.gbConfig.getAppId(),appName:o.gbConfig.getAppName(),appVersion:o.gbConfig.getAppVersion(),deviceSign:s.getAttr(r.gbPlatformInfoAttrNames.deviceSign),sdkVersion:o.gbConfig.getSDKVersion(),platform:function(e){var t="";if("string"==typeof e&&0<e.length){var n=e.split("-");n&&0<n.length&&(t=n[0])}return t}(o.gbConfig.getHostCategoryAppName())}},t.getStatServerUrl=function(){return"https://analysis-acc-ssl.xunlei.com"}},function(e,t,n){"use strict";var r=n(2),i=n(15),o=n(34),s=n(10);function a(e){var t=new o(e),n=i(o.prototype.request,t);return r.extend(n,o.prototype,t),r.extend(n,t),n}var u=a(s);u.Axios=o,u.create=function(e){return a(r.merge(s,e))},u.Cancel=n(18),u.CancelToken=n(47),u.isCancel=n(17),u.all=function(e){return Promise.all(e)},u.spread=n(48),e.exports=u,e.exports.default=u},function(e,t,n){"use strict";e.exports=function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}},function(e,t,n){"use strict";var r=n(10),i=n(2),o=n(42),s=n(43);function a(e){this.defaults=e,this.interceptors={request:new o,response:new o}}a.prototype.request=function(e,t){"string"==typeof e&&(e=i.merge({url:arguments[0]},t)),(e=i.merge(r,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var n=[s,void 0],o=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){n.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){n.push(e.fulfilled,e.rejected)}));n.length;)o=o.then(n.shift(),n.shift());return o},i.forEach(["delete","get","head","options"],(function(e){a.prototype[e]=function(t,n){return this.request(i.merge(n||{},{method:e,url:t}))}})),i.forEach(["post","put","patch"],(function(e){a.prototype[e]=function(t,n,r){return this.request(i.merge(r||{},{method:e,url:t,data:n}))}})),e.exports=a},function(e,t,n){"use strict";var r=n(2);e.exports=function(e,t){r.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))}},function(e,t,n){"use strict";var r=n(16);e.exports=function(e,t,n){var i=n.config.validateStatus;n.status&&i&&!i(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},function(e,t,n){"use strict";e.exports=function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e}},function(e,t,n){"use strict";var r=n(2);function i(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var o;if(n)o=n(t);else if(r.isURLSearchParams(t))o=t.toString();else{var s=[];r.forEach(t,(function(e,t){null!=e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),s.push(i(t)+"="+i(e))})))})),o=s.join("&")}return o&&(e+=(-1===e.indexOf("?")?"?":"&")+o),e}},function(e,t,n){"use strict";var r=n(2),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,o,s={};return e&&r.forEach(e.split("\n"),(function(e){if(o=e.indexOf(":"),t=r.trim(e.substr(0,o)).toLowerCase(),n=r.trim(e.substr(o+1)),t){if(s[t]&&0<=i.indexOf(t))return;s[t]="set-cookie"===t?(s[t]?s[t]:[]).concat([n]):s[t]?s[t]+", "+n:n}})),s}},function(e,t,n){"use strict";var r,i,o,s=n(2);function a(e){var t=e;return i&&(o.setAttribute("href",t),t=o.href),o.setAttribute("href",t),{href:o.href,protocol:o.protocol?o.protocol.replace(/:$/,""):"",host:o.host,search:o.search?o.search.replace(/^\?/,""):"",hash:o.hash?o.hash.replace(/^#/,""):"",hostname:o.hostname,port:o.port,pathname:"/"===o.pathname.charAt(0)?o.pathname:"/"+o.pathname}}e.exports=s.isStandardBrowserEnv()?(i=/(msie|trident)/i.test(navigator.userAgent),o=document.createElement("a"),r=a(window.location.href),function(e){var t=s.isString(e)?a(e):e;return t.protocol===r.protocol&&t.host===r.host}):function(){return!0}},function(e,t,n){"use strict";var r=n(2);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,i,o,s){var a=[];a.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),r.isString(i)&&a.push("path="+i),r.isString(o)&&a.push("domain="+o),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t,n){"use strict";var r=n(2);function i(){this.handlers=[]}i.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},i.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},i.prototype.forEach=function(e){r.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=i},function(e,t,n){"use strict";var r=n(2),i=n(44),o=n(17),s=n(10),a=n(45),u=n(46);function c(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return c(e),e.baseURL&&!a(e.url)&&(e.url=u(e.baseURL,e.url)),e.headers=e.headers||{},e.data=i(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||s.adapter)(e).then((function(t){return c(e),t.data=i(t.data,t.headers,e.transformResponse),t}),(function(t){return o(t)||(c(e),t&&t.response&&(t.response.data=i(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},function(e,t,n){"use strict";var r=n(2);e.exports=function(e,t,n){return r.forEach(n,(function(n){e=n(e,t)})),e}},function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,n){"use strict";var r=n(18);function i(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new r(e),t(n.reason))}))}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var e;return{token:new i((function(t){e=t})),cancel:e}},e.exports=i},function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function i(e,t){return t>e.length?new Array(t-e.length+1).join("0")+e:e}Object.defineProperty(t,"__esModule",{value:!0}),t.dateToDateString=function(e){return e.getFullYear().toString()+i((e.getMonth()+1).toString(),2)+i(e.getDate().toString(),2)},t.parseJson=function(e){var t=null;try{t=JSON.parse(e)}catch(e){t=null}return t},t.stringifyJson=function(e){var t=void 0;try{t=JSON.stringify(e)}catch(e){t=void 0}return t},t.forceGetTypeValue=function(e,t,n){return(void 0===t?"undefined":r(t))===e?t:n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this._requester=e}t.RequestServer=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(i,[{key:"get",value:function(e,t,n,r){return this._request("GET",e,t,n,r)}},{key:"post",value:function(e,t,n,r){return this._request("POST",e,t,n,r)}},{key:"_request",value:function(e,t,n,r,i){var o=this;return new Promise((function(s,a){var u=t,c=null,l=i||{};"GET"===e?r&&(u+=o._toUrlParams(r)):c=l.stringifyJsonDatas?JSON.stringify(r):r,o._requester[e.toLocaleLowerCase()](u,n,c).then((function(e){var t=e;if(l.parseJsonResult&&"string"==typeof e.data)try{t.data=JSON.parse(e.data)}catch(e){a(e)}s(t)})).catch((function(e){a(e)}))}))}},{key:"_toUrlParams",value:function(e){var t="";if(e){var n=[];for(var r in e)n.push(r+"="+e[r]);t=n.join("&")}return t}}]),i)},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var i=r(n(23));i.default.defaults.adapter=n(8);var o=(s.prototype.get=function(e,t,n){return void 0===n&&(n=null),this._request("GET",e,t,null)},s.prototype.post=function(e,t,n){return this._request("POST",e,t,n)},s.prototype._request=function(e,t,n,r){return void 0===r&&(r=null),new Promise((function(o,s){var a={method:e,url:t,headers:n,data:r};i.default(a).then((function(e){var t={data:e.data,status:e.status};o(t)})).catch((function(e){s(e)}))}))},s);function s(){}t.HttpRequest=o},,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=(i.prototype.encode=function(e){return new Buffer(e).toString("base64")},i);function i(){}var o=(s.prototype.get=function(){return this.base64Server},s);function s(){this.base64Server=null,this.base64Server=new r}t.Base64ServerLoader=o,t.base64ServerLoader=new o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(30),i=n(31),o=n(21),s=(a.prototype.setPublicData=function(){this.get().setPublicData(i.getPublicData())},a.prototype.get=function(){if(null===this.monitorStatServer){var e=r.gbStatAttrNames.monitor;if(!r.gbStat.hasAttr(e)){var t=new o.MonitorStatServer(i.getStatServerUrl());r.gbStat.setAttr(e,t)}this.monitorStatServer=r.gbStat.getAttr(e)}return this.monitorStatServer},a);function a(){this.monitorStatServer=null,this.monitorStatServer=null}t.MonitorStatServerLoader=s,t.monitorStatServerLoader=new s},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Object.defineProperty(t,"__esModule",{value:!0});var i=n(22),o=n(56),s=n(57),a=n(58);!function(e){e.mainProcessContext="main-process",e.mainRendererContext="main-renderer",e.mainPageWebviewRendererContext="main-page-webview-renderer",e.newTaskRendererContext="new-task-renderer",e.preNewTaskRendererContext="pre-new-task-renderer",e.loginRendererContext="login-renderer";var t=(function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(n,[{key:"onMessage",value:function(e,t){do{if(!o.isString(e)||0===e.length){s.error("msgName is null");break}if(o.isNullOrUndefined(t)){s.error("listener is null");break}this.listeners.has(e)?this.listeners.get(e).push(t):this.listeners.set(e,[t])}while(0)}},{key:"getCommunicatorInfo",value:function(){return this.currInfo}},{key:"getAllRenderer",value:function(){return this.rendererInfos}},{key:"getCommunicatorInfoById",value:function(e){var t=!0,n=!1,r=void 0;try{for(var i,o=this.rendererInfos[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){var s=i.value;if(s.id===e)return s}}catch(e){n=!0,r=e}finally{try{!t&&o.return&&o.return()}finally{if(n)throw r}}return null}},{key:"getCommunicatorInfoByContext",value:function(e){var t=!0,n=!1,r=void 0;try{for(var i,o=this.rendererInfos[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){var s=i.value;if(s.context===e)return s}}catch(e){n=!0,r=e}finally{try{!t&&o.return&&o.return()}finally{if(n)throw r}}return null}},{key:"startListenIPCMessage",value:function(e){this.isOnIPCEvent||(this.isOnIPCEvent=!0,e&&this.ListenSendToMainMsg(),this.ListenSendToRendererMsg(e))}},{key:"ListenSendToMainMsg",value:function(){var e=this;i.ipcMain.on(a.CommonIPCMessage.msgIPCSendToMain,(function(t,n){var r=void 0;do{if(o.isNullOrUndefined(n)){s.error("msgInfo is empty");break}if(!e.isConnected){s.warning("hasnot been connected yet");break}var i=n.msg.name;if(e.isIPCModuleIntervalMsg(i)){s.information("IPC module interval msg : "+i);var a=e.handleIPCModuleIntervalMsg(t.sender,n);if(r=a[1],!a[0])break;s.information("need to dispatch msg:"+i)}o.isNullOrUndefined(r)?r=e.NotifyListener(n):e.NotifyListener(n)}while(0);o.isNullOrUndefined(r)||(t.returnValue=r),n=null}))}},{key:"ListenSendToRendererMsg",value:function(e){var t=this;(e?i.ipcMain:i.ipcRenderer).on(a.CommonIPCMessage.msgIPCSendToRenderer,(function(n,r){var i=void 0;do{if(o.isNullOrUndefined(r)){s.error("msgInfo is empty");break}if(!t.isConnected){s.warning("hasnot been connected yet");break}var a=r.msg.name;if(t.isIPCModuleIntervalMsg(a)){s.information("IPC module interval msg : "+a);var u=t.handleIPCModuleIntervalMsg(n.sender,r);if(i=u[1],!u[0])break;s.information("need to dispatch msg:"+a)}e?(s.information("is main, handle forward msg"),t.handleForwardRendererToRendererMsg(r)):(s.information("is renderer, handle business msg"),o.isNullOrUndefined(i)?i=t.NotifyListener(r):t.NotifyListener(r))}while(0);o.isNullOrUndefined(i)||(n.returnValue=i),r=null}))}},{key:"isIPCModuleIntervalMsg",value:function(e){var t=!0,r=!1,i=void 0;try{for(var o,s=n.intervalIPCModuleMsgs[Symbol.iterator]();!(t=(o=s.next()).done);t=!0)if(e===o.value)return!0}catch(e){r=!0,i=e}finally{try{!t&&s.return&&s.return()}finally{if(r)throw i}}return!1}},{key:"handleIPCModuleIntervalMsg",value:function(e,t){var n=[!1,void 0];do{var r=t.msg.name;if(r===a.CommonIPCMessage.msgIPCRendererConnect){n=[!0,this.handleRendererConnectMsg(e,t)];break}if(r===a.CommonIPCMessage.msgIPCRendererDisconnect){n=[!0,this.handleRendererDisconnectMsg(e,t)];break}}while(0);return n}},{key:"handleRendererConnectMsg",value:function(e,t){s.verbose(e),s.verbose(t)}},{key:"handleRendererDisconnectMsg",value:function(e,t){s.verbose(e),s.verbose(t)}},{key:"handleForwardRendererToRendererMsg",value:function(e){this.sendForwardRendererToRendererMsg(e)}},{key:"sendForwardRendererToRendererMsg",value:function(e){s.verbose(e)}},{key:"NotifyListener",value:function(e){var t=void 0,n=e.msg.name;if(this.listeners.has(n)){var r=this.listeners.get(n),i=!0,o=!0,s=!1,a=void 0;try{for(var u,c=r[Symbol.iterator]();!(o=(u=c.next()).done);o=!0){var l=u.value;i?(i=!1,t=l(e)):l(e)}}catch(e){s=!0,a=e}finally{try{!o&&c.return&&c.return()}finally{if(s)throw a}}}return t}}]),n);function n(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),this.isConnected=!1,this.isOnIPCEvent=!1,this.rendererInfos=[],this.listeners=new Map,n.intervalIPCModuleMsgs=[a.CommonIPCMessage.msgIPCRendererConnect,a.CommonIPCMessage.msgIPCRendererDisconnect]}e.Communicator=t}(t.CommonIPCBase||(t.CommonIPCBase={}))},function(e,t){e.exports=require("util")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=function(){},t.error=function(){},t.warning=function(){},t.critical=function(){},t.verbose=function(){},"development"===process.env.LOGGER_ENV&&(t.information=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]},t.error=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]},t.warning=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]},t.critical=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]},t.verbose=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]})},function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),(r=t.CommonIPCMessage||(t.CommonIPCMessage={})).msgIPCCommunicatorForward="ipc_communicator_forward",r.msgIPCSendToMain="ipc_send_to_main",r.msgIPCSendToRenderer="ipc_send_to_renderer",r.msgIPCRendererConnect="ipc_renderer_connect",r.msgIPCRendererDisconnect="ipc_renderer_disconnect",r.msgNCCallNativeFunction="nc_call_native_function",r.msgNCCheckNativeFunction="nc_check_native_function",r.msgNCCallJsFunctionById="nc_call_js_function_by_id",r.msgNCCallJsFunctionByName="nc_call_js_function_by_name",r.msgNCNativeFireEvent="nc_native_fire_event",r.msgNCNativeCallReady="nc_native_call_ready"},function(e,t,n){"use strict";var r,i;Object.defineProperty(t,"__esModule",{value:!0}),t.CLIENT_VERSION="3.1.3",(r=t.Syntax||(t.Syntax={})).CLIENT_ID="client_id",r.CLIENT_SECRET="client_secret",r.RESPONSE_TYPE="response_type",r.SCOPE="scope",r.STATE="state",r.REDIRECT_URI="redirect_uri",r.ERROR="error",r.ERROR_DESCRIPTION="error_description",r.ERROR_URI="error_uri",r.GRANT_TYPE="grant_type",r.CODE="code",r.ACCESS_TOKEN="access_token",r.TOKEN_TYPE="token_type",r.EXPIRES_IN="expires_in",r.USERNAME="username",r.PASSWORD="password",r.REFRESH_TOKEN="refresh_token",(i=t.ErrorType||(t.ErrorType={})).UNREACHABLE="unreachable",i.LOCAL="local",i.CANCELLED="cancelled",i.UNKNOWN="unknown",i.INVALID_ARGUMENT="invalid_argument",i.DEADLINE_EXCEEDED="deadline_exceeded",i.NOT_FOUND="not_found",i.ALREADY_EXISTS="already_exists",i.PERMISSION_DENIED="permission_denied",i.UNAUTHENTICATED="unauthenticated",i.RESOURCE_EXHAUSTED="resource_exhausted",i.FAILED_PRECONDITION="failed_precondition",i.ABORTED="aborted",i.OUT_OF_RANGE="out_of_range",i.UNIMPLEMENTED="unimplemented",i.INTERNAL="internal",i.UNAVAILABLE="unavailable",i.DATA_LOSS="data_loss",i.CAPTCHA_REQUIRED="captcha_required",i.CAPTCHA_INVALID="captcha_invalid",i.INVALID_PASSWORD="invalid_password",i.INVALID_STATUS="invalid_status",i.USER_PENDING="user_pending",i.USER_BLOCKED="user_blocked",i.INVALID_VERIFICATION_CODE="invalid_verification_code",i.TWO_FACTOR_REQUIRED="two_factor_required",i.INVALID_TWO_FACTOR="invalid_two_factor",i.INVALID_TWO_FACTOR_RECOVERY="invalid_two_factor_recovery",i.UNDER_REVIEW="under_review",i.INVALID_REQUEST="invalid_request",i.UNAUTHORIZED_CLIENT="unauthorized_client",i.ACCESS_DENIED="access_denied",i.UNSUPPORTED_RESPONSE_TYPE="unsupported_response_type",i.INVALID_SCOPE="invalid_scope",i.INVALID_GRANT="invalid_grant",i.SERVER_ERROR="server_error",i.TEMPORARILY_UNAVAILABLE="temporarily_unavailable",i.INTERACTION_REQUIRED="interaction_required",i.LOGIN_REQUIRED="login_required",i.ACCOUNT_SELECTION_REQUIRED="account_selection_required",i.CONSENT_REQUIRED="consent_required",i.INVALID_REQUEST_URI="invalid_request_uri",i.INVALID_REQUEST_OBJECT="invalid_request_object",i.REQUEST_NOT_SUPPORTED="request_not_supported",i.REQUEST_URI_NOT_SUPPORTED="request_uri_not_supported",i.REGISTRATION_NOT_SUPPORTED="registration_not_supported"},,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Object.defineProperty(t,"__esModule",{value:!0});var i,o,s=n(22),a=n(56),u=n(57),c=n(58),l=n(55);function f(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(f.__proto__||Object.getPrototypeOf(f)).call(this))}i=t.CommonIPCRenderer||(t.CommonIPCRenderer={}),function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(f,l.CommonIPCBase.Communicator),function(e,t,n){t&&r(e.prototype,t),n&&r(e,n)}(f,[{key:"initialize",value:function(e){this.currInfo={id:void 0,context:e,isMainCommunicator:!1}}},{key:"connect",value:function(){this.isConnected?u.warning("has been connected"):(this.sendConnectMsgToMain(),this.isConnected=!0,this.startListenIPCMessage(!1))}},{key:"disconnect",value:function(){this.isConnected?(this.isConnected=!1,this.sendDisconnectMsgToMain()):u.warning("hasnot been connected yet")}},{key:"sendMessageToMain",value:function(e){this.sendIPCMsgToMain(e)}},{key:"sendMessageToMainSync",value:function(e){return this.sendIPCMsgToMain(e,!0)}},{key:"sendMessageToRenderer",value:function(e,t){this.sendIPCMsgToRenderer(e,t)}},{key:"handleRendererConnectMsg",value:function(e,t){do{if(a.isNullOrUndefined(t)){u.error("msgInfo is null");break}var n=t.msg.args[0];if(a.isNullOrUndefined(n)){u.error("connectRendererInfo is null");break}u.information("Renderer: new renderer will connect, id = "+n.id+", context = "+n.context),this.rendererInfos.push(n)}while(0)}},{key:"handleRendererDisconnectMsg",value:function(e,t){do{if(a.isNullOrUndefined(t)){u.error("msgInfo is null");break}var n=t.msg.args[0];if(a.isNullOrUndefined(n)){u.error("disconnectRendererInfo is null");break}u.information("renderer will disconnect, id = "+n.id+", context = "+n.context);for(var r=0;r<this.rendererInfos.length;++r)if(this.rendererInfos[r]===n){this.rendererInfos.splice(r,1);break}}while(0)}},{key:"sendConnectMsgToMain",value:function(){var e=this.sendMessageToMainSync({name:c.CommonIPCMessage.msgIPCRendererConnect,args:[]});this.currInfo.id=e[0],this.rendererInfos=e[1]}},{key:"sendDisconnectMsgToMain",value:function(){this.sendMessageToMain({name:c.CommonIPCMessage.msgIPCRendererDisconnect,args:[]})}},{key:"sendIPCMsgToMain",value:function(e,t){var n=1<arguments.length&&void 0!==t&&t,r=void 0;do{if(a.isNullOrUndefined(e)){u.error("msg is null");break}r=(n?s.ipcRenderer.sendSync:s.ipcRenderer.send)(c.CommonIPCMessage.msgIPCSendToMain,{msg:e,senderInfo:this.currInfo})}while(0);return r}},{key:"sendIPCMsgToRenderer",value:function(e,t){do{if(a.isNullOrUndefined(e)){u.error("rendererId is null");break}if(a.isNullOrUndefined(t)){u.error("msg is null");break}var n=[e].concat(t.args);t.args=n,s.ipcRenderer.send(c.CommonIPCMessage.msgIPCSendToRenderer,{msg:t,senderInfo:this.currInfo})}while(0)}}]),o=f,i.RendererCommunicator=o,i.rendererCommunicator=new o},function(e,t,n){"use strict";var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},i=function(e,t,n,r){return new(n=n||Promise)((function(i,o){function s(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},o=function(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=0<(i=s.trys).length&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};Object.defineProperty(t,"__esModule",{value:!0});var s=n(59),a=n(87),u=n(109),c="x-request-id";function l(){return a.uuidv4()}t.defaultRequest=function(e,t){return i(void 0,void 0,void 0,(function(){var n,r,i,a,u,c;return o(this,(function(o){switch(o.label){case 0:r=n=null,o.label=1;case 1:return o.trys.push([1,4,,5]),i=Object.assign({method:"GET",mode:"cors"},t),a=Object.assign({"content-type":"application/json"},i.headers),i.headers=a,i.body&&"string"!=typeof i.body&&(i.body=JSON.stringify(i.body)),[4,fetch(e,i)];case 2:return[4,o.sent().json()];case 3:return(u=o.sent())&&u.error?(r=u).error_uri=new URL(e).pathname:n=u,[3,5];case 4:return c=o.sent(),r={error:s.ErrorType.UNREACHABLE,error_description:c.message,error_uri:new URL(e).pathname},[3,5];case 5:if(r)throw r;return[2,n]}}))}))},t.toResponseError=function(e,t){var n,r=t||{};if(e instanceof Error)n={error:r.error||s.ErrorType.LOCAL,error_description:r.error_description||e.message,error_uri:r.error_uri,details:r.details||e.stack};else{var i=e||{};n={error:r.error||i.error||s.ErrorType.LOCAL,error_description:r.error_description||i.error_description,error_uri:r.error_uri||i.error_uri,details:r.details||i.details}}return n},t.generateRequestId=l;var f=(d.prototype.getItem=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return[2,window.localStorage.getItem(e)]}))}))},d.prototype.removeItem=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return window.localStorage.removeItem(e),[2]}))}))},d.prototype.setItem=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return window.localStorage.setItem(e,t),[2]}))}))},d);function d(){}function h(e){var t=!0;return e&&e.expires_at&&e.access_token&&(t=e.expires_at<new Date),t}t.defaultStorage=new f;var p=(_.prototype.setCredentials=function(e){return i(this,void 0,void 0,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return e&&e.expires_in?(e.expires_at instanceof Date||("string"==typeof e.expires_at?e.expires_at=new Date(e.expires_at):e.expires_at=new Date(Date.now()+1e3*(e.expires_in-30))),this._storage?(t=JSON.stringify(e),[4,this._storage.setItem(this._tokenSectionName,t)]):[3,2]):[3,3];case 1:n.sent(),n.label=2;case 2:return this._credentials=e,[3,6];case 3:return this._storage?[4,this._storage.removeItem(this._tokenSectionName)]:[3,5];case 4:n.sent(),n.label=5;case 5:this._credentials=null,n.label=6;case 6:return[2]}}))}))},_.prototype.getCredentials=function(){return i(this,void 0,void 0,(function(){var e=this;return o(this,(function(t){return[2,this._singlePromise.run("getCredentials",(function(){return i(e,void 0,void 0,(function(){var e;return o(this,(function(t){switch(t.label){case 0:return h(this._credentials)?[4,(e=this)._getStorageCredentials()]:[3,2];case 1:e._credentials=t.sent(),t.label=2;case 2:return[2,this._credentials]}}))}))}))]}))}))},_.prototype._getStorageCredentials=function(){return i(this,void 0,void 0,(function(){var e=this;return o(this,(function(t){return[2,this._singlePromise.run("_getStorageCredentials",(function(){return i(e,void 0,void 0,(function(){var e,t;return o(this,(function(n){switch(n.label){case 0:return e=null,[4,this._storage.getItem(this._tokenSectionName)];case 1:if(null==(t=n.sent()))return[3,5];n.label=2;case 2:return n.trys.push([2,3,,5]),(e=JSON.parse(t))&&e.expires_at&&(e.expires_at=new Date(e.expires_at)),[3,5];case 3:return n.sent(),[4,this._storage.removeItem(this._tokenSectionName)];case 4:return n.sent(),e=null,[3,5];case 5:return[2,e]}}))}))}))]}))}))},_);function _(e){this._credentials=null,this._singlePromise=new u.SinglePromise,this._tokenSectionName=e.tokenSectionName,this._storage=e.storage}t.LocalCredentials=p;var v=(g.prototype.setCredentials=function(e){return this._localCredentials.setCredentials(e)},g.prototype.getAccessToken=function(){return i(this,void 0,void 0,(function(){var e;return o(this,(function(t){switch(t.label){case 0:return[4,this._getCredentials()];case 1:return(e=t.sent())&&e.access_token?[2,Promise.resolve(e.access_token)]:[2,Promise.reject({error:s.ErrorType.UNAUTHENTICATED})]}}))}))},g.prototype.request=function(e,t){return i(this,void 0,void 0,(function(){var n,i,a,u,f,d,h,p,_,v;return o(this,(function(o){switch(o.label){case 0:return t=t||{},n=this._formatRetry(t.retry,this._retry),t.headers=t.headers||{},this._headers&&(t.headers=r(r({},this._headers),t.headers)),this._devMode&&(t.headers[c]||(t.headers[c]=l())),i=e.startsWith("/"),a=e,i&&(a=this._apiOrigin+a),u=new URL(a),f=u.searchParams,t&&t.withCredentials?[4,this._getCredentials()]:[3,2];case 1:return(d=o.sent())&&(this._tokenInURL?f.set("access_token",d.access_token):t.headers.Authorization=d.token_type+" "+d.access_token),[3,3];case 2:this._clientId&&i&&!f.has("client_id")&&f.set("client_id",this._clientId),o.label=3;case 3:i&&e.startsWith("/v1/auth")&&f.set("client_version",s.CLIENT_VERSION),a=u.href,h=null,p=n+1,_=0,o.label=4;case 4:if(!(_<p))return[3,11];o.label=5;case 5:return o.trys.push([5,7,,8]),[4,this._baseRequest(a,t)];case 6:return h=o.sent(),[3,11];case 7:return v=o.sent(),_!==n&&v&&"unreachable"===v.error?[3,8]:[2,Promise.reject(v)];case 8:return[4,this._sleep(g._retryInterval)];case 9:o.sent(),o.label=10;case 10:return _++,[3,4];case 11:return[2,h]}}))}))},g.prototype._checkRetry=function(e){var t=null;if(("number"!=typeof e||e<g._minRetry||g._maxRetry<e)&&(t={error:s.ErrorType.UNREACHABLE,error_description:"wrong options param: retry"}),t)throw t;return e},g.prototype._formatRetry=function(e,t){return void 0===e?t:this._checkRetry(e)},g.prototype._sleep=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return[2,new Promise((function(t){setTimeout((function(){t()}),e)}))]}))}))},g.prototype._refreshToken=function(e){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,this._singlePromise.run("_refreshToken",(function(){return i(t,void 0,void 0,(function(){var t,n;return o(this,(function(r){switch(r.label){case 0:return r.trys.push([0,3,,6]),[4,this._refreshTokenFunc(e)];case 1:return t=r.sent(),[4,this._localCredentials.setCredentials(t)];case 2:return r.sent(),[3,6];case 3:return(n=r.sent()).error!==s.ErrorType.INVALID_GRANT?[3,5]:[4,this._localCredentials.setCredentials(null)];case 4:return r.sent(),[2,this._unAuthenticatedError(n.error_description)];case 5:return[2,Promise.reject(n)];case 6:return[2,this._localCredentials.getCredentials()]}}))}))}))]}))}))},g.prototype._defaultRefreshTokenFunc=function(e){return e&&e.refresh_token?this.request("/v1/auth/token",{method:"POST",body:{client_id:this._clientId,client_secret:this._clientSecret,grant_type:"refresh_token",refresh_token:e.refresh_token}}):this._unAuthenticatedError("refresh token not found")},g.prototype._getCredentials=function(){return i(this,void 0,void 0,(function(){var e;return o(this,(function(t){switch(t.label){case 0:return[4,this._localCredentials.getCredentials()];case 1:return h(e=t.sent())?[4,this._refreshToken(e)]:[3,3];case 2:e=t.sent(),t.label=3;case 3:return[2,e]}}))}))},g.prototype._unAuthenticatedError=function(e){return Promise.reject({error:s.ErrorType.UNAUTHENTICATED,error_description:e})},g._defaultRetry=1,g._minRetry=0,g._maxRetry=5,g._retryInterval=1e3,g);function g(e){this._singlePromise=new u.SinglePromise,this._devMode=e.devMode,this._apiOrigin=e.apiOrigin,this._clientId=e.clientId,this._retry=this._formatRetry(e.retry,g._defaultRetry),null!=e.baseRequest?this._baseRequest=e.baseRequest:this._baseRequest=t.defaultRequest,this._tokenInURL=e.tokenInURL,this._headers=e.headers,this._storage=e.storage||t.defaultStorage,this._localCredentials=new p({tokenSectionName:"credentials_"+e.clientId,storage:this._storage}),this._clientSecret=e.clientSecret,this._refreshTokenFunc=e.refreshTokenFunc||this._defaultRefreshTokenFunc}t.OAuth2Client=v},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.uuidv4=function(){return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))}},,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=this&&this.__assign||Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i=this&&this.__awaiter||function(e,t,n,r){return new(n=n||Promise)((function(i,o){function s(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){e.done?i(e.value):new n((function(t){t(e.value)})).then(s,a)}u((r=r.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=0<(i=s.trys).length&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var a=n(55),u=n(85);u.CommonIPCRenderer.rendererCommunicator.initialize(a.CommonIPCBase.loginRendererContext),u.CommonIPCRenderer.rendererCommunicator.connect();var c=n(106),l=n(22),f=n(49),d=n(20),h=n(7),p=n(21),_=n(54),v=s(n(107)),g=n(108),m=_.monitorStatServerLoader.get();_.monitorStatServerLoader.setPublicData(),m.statBusinessFlow({key:p.businessFlowKeys.openLoginWnd}),window.addEventListener("keyup",(function(e){var t;"F12"===e.key&&e.ctrlKey&&null!==(t=l.remote.getCurrentWindow())&&(t.webContents.isDevToolsOpened()?t.webContents.closeDevTools():t.webContents.openDevTools({mode:"undocked"}))}),!0);var y=window.xlQuickLogin||{},b=window.store||{},C=!1,I=!1,k=(S.prototype.getItem=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return this._map.has(e)?[2,this._map.get(e)||""]:[2,null]}))}))},S.prototype.removeItem=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return this._map.delete(e),[2]}))}))},S.prototype.setItem=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return this._map.set(e,t),[2]}))}))},S);function S(){this._map=new Map}var w=(E.prototype.start=function(){return i(this,void 0,void 0,(function(){var e,t,n,r,i,s,a,u,c,l;return o(this,(function(o){switch(o.label){case 0:return this._isStarting=!0,this._elements.qrCodeBoxMaskIcon.style.display="none",this._elements.qrCodeBoxMaskText.innerHTML="二维码加载中...",this._elements.qrCodeBoxMaskText.style.display="",this._elements.qrCodeBoxMask.style.display="",this._elements.qrCodeStatusText.style.display="none",this._qrious=null,this._elements.qrCodeImg.src="",this._oAuth2Client.setCredentials(),[4,this._auth.genDeviceCode({scope:"user"})];case 1:return e=o.sent(),(t=new URL(e.verification_uri_complete)).host="misc-xl9-ssl.xunlei.com",t.pathname="auth-device/",(n=new URL("https://xluser-ssl.xunlei.com/xluser.core.login/v3/qrlogin")).searchParams.set("redirect_uri",t.href),[4,this._showQrcode(n.href)];case 2:o.sent(),this._elements.qrCodeBoxMask.style.display="none",o.label=3;case 3:return[4,this._tryLogin(e.device_code,e.interval,e.expires_in)];case 4:return o.sent()?[3,3]:[3,5];case 5:this._expireAt=null,o.label=6;case 6:return o.trys.push([6,14,,17]),this._isLogin?[4,this._oAuth2Client.getAccessToken()]:[3,13];case 7:return"string"==typeof(r=o.sent())&&0<r.length?[4,this._synchronizeSessionInfoByCredentials(r)]:[3,13];case 8:i=o.sent(),s="",o.label=9;case 9:return o.trys.push([9,11,,12]),[4,this._auth.getUserProfile()];case 10:return a=o.sent(),s=a.name||"",[3,12];case 11:return o.sent(),[3,12];case 12:u={userid:""+i.user_id,sessionid:i.sessionid,usernick:s,loginkey:i.loginkey||"",uName:"",xl_autologin:!0,show:!1,loginType:"7"},C=!0,this._config.xdas.saveToXdasEx("memory","userinfo",JSON.stringify(u)),this._config.xdas.cloLogin(),o.label=13;case 13:return[3,17];case 14:return c=o.sent(),l='<span class="is-error">登录失败</span>请刷新后重新扫码',c&&"/session/v1/register"===c.error_uri?[4,this._oAuth2Client.setCredentials()]:[3,16];case 15:switch(o.sent(),c.error){case g.ErrorType.UNREACHABLE:l='<span class="is-error">网络异常</span>请稍后重新扫码';break;case g.ErrorType.INTERNAL:case g.ErrorType.INVALID_ARGUMENT:l='<span class="is-error">系统错误</span>请稍后重新扫码';break;case g.ErrorType.PERMISSION_DENIED:l='<span class="is-error">账号异常</span>请稍后重新扫码'}o.label=16;case 16:return this._elements.qrCodeBoxMask.className="xlx-code-box__mask xlx-code-box__mask--refresh",this._elements.qrCodeBoxMaskIcon.className="login-icon-refresh",this._elements.qrCodeBoxMaskText.innerText="点击刷新",this._elements.qrCodeStatusText.innerHTML=l,this._elements.qrCodeBoxMaskIcon.style.display="",this._elements.qrCodeBoxMaskText.style.display="",this._elements.qrCodeBoxMask.style.display="",this._elements.qrCodeStatusText.style.display="",[3,17];case 17:return this._isStarting=!1,[2]}}))}))},E.prototype._getElements=function(){return{qrCodeImg:document.getElementById("xlx-code-img"),qrCodeBoxMask:document.getElementById("xlx-code-img-mask"),qrCodeBoxMaskIcon:document.getElementById("xlx-code-img-mask-icon"),qrCodeBoxMaskText:document.getElementById("xlx-code-img-mask-text"),qrCodeStatusText:document.getElementById("xlx-code-status-text")}},E.prototype._showQrcode=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return this._qrious=new v.default({element:this._elements.qrCodeImg,value:e,size:182}),[2]}))}))},E.prototype._tryLogin=function(e,t,n){return i(this,void 0,void 0,(function(){var r,i,s;return o(this,(function(o){switch(o.label){case 0:return r=!1,this._expireAt||(this._expireAt=new Date,this._expireAt.setSeconds(this._expireAt.getSeconds()+n)),new Date>this._expireAt?(this._elements.qrCodeBoxMask.className="xlx-code-box__mask xlx-code-box__mask--refresh",this._elements.qrCodeBoxMaskIcon.className="login-icon-refresh",this._elements.qrCodeBoxMaskText.innerText="点击刷新",this._elements.qrCodeStatusText.innerHTML="二维码已失效",this._elements.qrCodeBoxMaskIcon.style.display="",this._elements.qrCodeBoxMaskText.style.display="",this._elements.qrCodeBoxMask.style.display="",this._elements.qrCodeStatusText.style.display="",[2,r]):[4,this._sleep(1e3*t)];case 1:o.sent(),o.label=2;case 2:return o.trys.push([2,4,,5]),[4,this._auth.grantToken({client_secret:this._config.clientSecret,grant_type:"urn:ietf:params:oauth:grant-type:device_code",device_code:e})];case 3:return o.sent(),this._isLogin=!0,[3,5];case 4:if((i=o.sent())&&"string"==typeof i.error)switch(i.error){case"expired_token":this._elements.qrCodeBoxMask.className="xlx-code-box__mask xlx-code-box__mask--refresh",this._elements.qrCodeBoxMaskIcon.className="login-icon-refresh",this._elements.qrCodeBoxMaskText.innerText="点击刷新",this._elements.qrCodeStatusText.innerHTML="二维码已失效",this._elements.qrCodeBoxMaskIcon.style.display="",this._elements.qrCodeBoxMaskText.style.display="",this._elements.qrCodeBoxMask.style.display="",this._elements.qrCodeStatusText.style.display="";break;case"access_denied":this._elements.qrCodeBoxMask.className="xlx-code-box__mask xlx-code-box__mask--refresh",this._elements.qrCodeBoxMaskIcon.className="login-icon-refresh",this._elements.qrCodeBoxMaskText.innerText="点击刷新",this._elements.qrCodeStatusText.innerHTML='<span class="is-error">登录失败</span>请刷新后重新扫码',this._elements.qrCodeBoxMaskIcon.style.display="",this._elements.qrCodeBoxMaskText.style.display="",this._elements.qrCodeBoxMask.style.display="",this._elements.qrCodeStatusText.style.display="";break;case"authorization_pending":s=void 0,i.details&&0<i.details.length&&i.details[0].state&&(s=i.details[0].state),"WAITING_CONSENT"===s&&(this._elements.qrCodeBoxMask.className="xlx-code-box__mask",this._elements.qrCodeBoxMaskIcon.className="login-icon-success",this._elements.qrCodeBoxMaskText.innerHTML="扫码成功<br>请在手机端确认登录",this._elements.qrCodeBoxMaskIcon.style.display="",this._elements.qrCodeBoxMaskText.style.display="",this._elements.qrCodeStatusText.innerHTML="",this._elements.qrCodeBoxMask.style.display="",this._elements.qrCodeStatusText.style.display="none"),r=!0;break;default:this._elements.qrCodeBoxMask.className="xlx-code-box__mask xlx-code-box__mask--refresh",this._elements.qrCodeBoxMaskIcon.className="login-icon-refresh",this._elements.qrCodeBoxMaskText.innerText="点击刷新",this._elements.qrCodeStatusText.innerHTML="网络错误，请刷新重试",this._elements.qrCodeBoxMaskIcon.style.display="",this._elements.qrCodeBoxMaskText.style.display="",this._elements.qrCodeBoxMask.style.display="",this._elements.qrCodeStatusText.style.display=""}else this._elements.qrCodeBoxMask.className="xlx-code-box__mask xlx-code-box__mask--refresh",this._elements.qrCodeBoxMaskIcon.className="login-icon-refresh",this._elements.qrCodeBoxMaskText.innerText="点击刷新",this._elements.qrCodeStatusText.innerHTML="网络错误，请刷新重试",this._elements.qrCodeBoxMaskIcon.style.display="",this._elements.qrCodeBoxMaskText.style.display="",this._elements.qrCodeBoxMask.style.display="",this._elements.qrCodeStatusText.style.display="";return[3,5];case 5:return[2,r]}}))}))},E.prototype._sleep=function(e){return new Promise((function(t){setTimeout((function(){t()}),e)}))},E.prototype._synchronizeSessionInfoByCredentials=function(e){return i(this,void 0,void 0,(function(){var t;return o(this,(function(n){try{return t=this._config.apiOrigin+"/session/v1/register?appid="+this._config.appId+"&token="+e+"&appname="+this._config.appName+"&devicesign="+this._config.deviceSign,[2,g.defaultRequest(t,{method:"GET"})]}catch(n){return[2,Promise.reject(g.toResponseError(n))]}return[2]}))}))},E);function E(e){var t=this;this._qrious=null,this._expireAt=null,this._isStarting=!1,this._tempStorage=new k,this._isLogin=!1,this._config={apiOrigin:e.apiOrigin,appId:e.appId,appName:e.appName,clientId:e.clientId,clientSecret:e.clientSecret,deviceSign:e.deviceSign,tokenSectionName:"credentials_"+e.clientId,xdas:e.xdas};var n={apiOrigin:this._config.apiOrigin,clientId:this._config.clientId,clientSecret:e.clientSecret,storage:this._tempStorage};this._oAuth2Client=new g.OAuth2Client(n);var r={clientId:this._config.clientId,credentialsClient:this._oAuth2Client,request:this._oAuth2Client.request.bind(this._oAuth2Client),storage:this._tempStorage};this._auth=new g.Auth(r),this._elements=this._getElements(),this._elements.qrCodeBoxMask.onclick=function(){t._isStarting||t.start()}}function x(){this.loginTYPE=0,this.userinfo={},this.allUserInfo={},this.isGetFail=!1,this.isRuning=!1,this.reportPerformanceIntervalId=void 0,this.getInitConf(),this.bindXdas()}var T=new(x.prototype.getInitConf=function(){var e=this;e.getXdasDatas("memory","initconfig",(function(t){e.getXdasDatas("memory","showLoginWndAction",(function(n){var r=this,s="";n&&(s="string"==typeof n.from?n.from:""),b.set("showLoginWndSource",s),null!==t&&e.getXdasDatas("memory","deviceid",(function(n){return i(r,void 0,void 0,(function(){var r;return o(this,(function(i){return n&&(b.set("deviceid",n.id),new h.GBHelper(d.gbAttrNames.platformInfo).setAttr(d.gbPlatformInfoAttrNames.deviceSign,f.forceGetTypeValue("string",n.id,""))),window.Xreport.push({type:"conf",global:{category:"xdas_bugs",clientVersion:t.clientVersion||"unknown"}}),"./theme/xlx/css/style.css",r=t.activityEnable&&t.activityCssPath||"./theme/xlx/css/style.css",y.init({loginID:t.appid+"",appName:t.package,registerID:t.appid+"",APP_VERSION:t.clientVersion,UI_THEME:"xlx",UI_STYLE:r,LOGIN_TYPES:t.loginTypes,REGISTER_TYPES:t.registerTypes,THIRD_LOGIN_DEFAULT:t.thirdLoginDefault,THIRD_LOGIN_GROUP:t.thirdLoginGroup,LOGIN_SUCCESS_URL:"",REGISTER_SUCCESS_URL:"",UI_TEXT:t.uiText,static_domain:"i.xunlei.com",server_login:["xluser-ssl.xunlei.com","xluser2-ssl.xunlei.com","xluser3-ssl.xunlei.com"],MESSAGE_CHANNEL_GSLB_QURERY_HOST_KEY:"agw-acc-ssl.xunlei.com",MESSAGE_CHANNEL_SERVER:"agw-acc-web-ssl.xunlei.com",ANALYSIS_SERVER:"analysis-acc-ssl.xunlei.com",defualt_background:"",IFRAME_ALLOW_TRANSPARENCY:!0}),e._qrLogin=new w({appId:t.appid,appName:t.package,deviceSign:n.id,apiOrigin:"https://xluser-ssl.xunlei.com",clientId:t.clientId,clientSecret:t.clientSecret,xdas:e}),_.monitorStatServerLoader.setPublicData(),m.statBusinessFlow({key:p.businessFlowKeys.initLoginWnd}),e.setLoginBox(),e.reportPerformance(),b.set("clientVersion",t.clientVersion),[2]}))}))}))}))}))},x.prototype.setLoginBox=function(){this.loginIframe(),this.checkIsOut()},x.prototype.reportPerformance=function(){var e=this;this.reportPerformanceIntervalId=setInterval((function(){if(window&&window.performance&&window.performance.timing){var t=window.performance.timing,n=t.loadEventEnd-t.navigationStart;if(0<=n){clearInterval(e.reportPerformanceIntervalId),e.reportPerformanceIntervalId=void 0;var r=t.fetchStart-t.navigationStart,i=t.domainLookupEnd-t.domainLookupStart,o=t.connectEnd-t.connectStart,s=t.responseStart-t.requestStart,a=t.responseEnd-t.requestStart,u=t.domComplete-t.domLoading;window.Xreport.push({type:"now",data:{action:"performance",category:"xdas_bugs",bussiness:"0",loadTime:n,beforeFetchTime:r,dominLookupTime:i,connectTime:o,firstResponseTime:s,responseTime:a,domTime:u}}),m.statPagePerformance({from:"outsideIframe",loadTime:n,beforeFetchTime:r,dominLookupTime:i,connectTime:o,firstResponseTime:s,responseTime:a,domTime:u})}}}),100)},x.prototype.checkIsOut=function(){var e=this;e.getXdasDatas("memory","userinfo",(function(t){if(t&&"sessionOut"==t.type){var n=t;n.type="",e.saveToXdas("memory","userinfo",JSON.stringify(n)),setTimeout((function(){e.showError("您的帐号已在其他设备登录，请注意帐号安全")}),1e3)}!t||"thirdFail"!=t.loginType&&"autoLoginFail"!=t.loginType||(e.saveToXdas("memory","userinfo",JSON.stringify({})),setTimeout((function(){e.showError(t.errmsg)}),1e3))}))},x.prototype.loginFetch=function(e){this.loginTYPE=e;var t=b.get("usernick")||"",n=b.get("userid")||"";c.NativeCallModule.nativeCall.CallNativeFunction("NativeFireEvent","onLoginBefore",t,n,(function(e,t){}))},x.prototype.loginFail=function(e,t){c.NativeCallModule.nativeCall.CallNativeFunction("NativeFireEvent","onLoginFailed",t+"",e+"",(function(e,t){}))},x.prototype.getUserIMg=function(e,t){c.NativeCallModule.nativeCall.CallNativeFunction("GetUserHeaderByUserID",e,(function(e,n){t&&t(n)}))},x.prototype.bindXdas=function(){var e=this;window.addEventListener("unload",(function(t){C||e.loginFail("close","close")})),y.setLoginExt(this.loginExtFun.bind(this)),y.getXdasDatas=this.getXdasDatas.bind(this),y.saveToXdas=this.saveToXdas.bind(this),y.cloLogin=this.cloLogin.bind(this),y.openNewTab=this.openNewTab.bind(this),y.setLoginFailExt(this.loginFail.bind(this)),y.loginFetch=this.loginFetch.bind(this),y.onLoginWndClose=this.onLoginWndClose.bind(this),y.getUserImg=this.getUserIMg.bind(this),y.updateLoginWnd=this.updateLoginWnd.bind(this),y.getSkinInfo=this.getSkinInfo.bind(this),y.fireStatEvent=this.fireStatEvent.bind(this),c.NativeCallModule.nativeCall.AttachNativeEvent("OnChangeSkin",(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=e.getSkinTheme(t[0]);e.changeTheme(r)}))},x.prototype.updateLoginWnd=function(e){var t=this;I||(I=!0,this.getSkinInfo((function(n){var r=document.body;r.style=n,r.classList=""==n?"":"is-theme",t._qrLogin&&t._qrLogin.start(),c.NativeCallModule.nativeCall.CallNativeFunction("UpdateLoginWnd",e)})))},x.prototype.showError=function(e){void 0===e&&(e=""),y.showError(e)},x.prototype.sendLoginSuc=function(){this.isGetFail=!1,this.onLoginWndClose("suc")},x.prototype.onLoginWndClose=function(e,t){c.NativeCallModule.nativeCall.CallNativeFunction("NativeFireEvent","onLoginWndClose",e,t),m.statBusinessFlow({key:p.businessFlowKeys.closeLoginWnd}),window.close()},x.prototype.loginIframe=function(){document.getElementById("xl_login")&&y.getLoginIframe("xl_login")},x.prototype.loginExtFun=function(e){var t=e;C=!0,t.loginType="0",7==t.LOGIN_TYPE?t.xl_autologin=!0:(t.uName=b.get("uName")||"",t.xl_autologin="1"==b.get("xl_autologin")),this.saveToXdas("memory","userinfo",JSON.stringify(t)),this.sendLoginSuc()},x.prototype.openNewTab=function(e){var t=e||{},n=r({url:t.url,name:"string"==typeof t.name&&0<t.name.length?e.name:"",from:"user-plugin"},e);c.NativeCallModule.nativeCall.CallNativeFunction("openNewTab",n.url,n?JSON.stringify(n):void 0)},x.prototype.cloLogin=function(){this.onLoginWndClose("close")},x.prototype.saveToXdas=function(e,t,n){c.NativeCallModule.nativeCall.CallNativeFunction("SaveLoginData",e,t,n,(function(e,t){}))},x.prototype.saveToXdasEx=function(e,t,n){return new Promise((function(r){c.NativeCallModule.nativeCall.CallNativeFunction("SaveLoginData",e,t,n,(function(e,t){r({errorCode:e,value:t})}))}))},x.prototype.getXdasDatas=function(e,t,n){c.NativeCallModule.nativeCall.CallNativeFunction("LoadLoginData",e,t,(function(e,t){var r=t?JSON.parse(t):null;n&&n(r)}))},x.prototype.getSkinInfo=function(e){var t=this;c.NativeCallModule.nativeCall.CallNativeFunction("GetSkinInfo",(function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=t.getSkinTheme(n[1]);e(i)}))},x.prototype.getSkinTheme=function(e){var t="";if(e&&0!=e.colorID){for(var n=e.colors,r=function(e,n){var r="--"+e.replace(/([A-Z0-9])/g,"-$1").toLowerCase();Array.isArray(n)?n.forEach((function(e,n){t+=r+"-"+(n+1)+": "+e+";"})):t+=r+": "+n+";"},i=0,o=Object.entries(n);i<o.length;i++){var s=o[i];r(s[0],s[1])}e.opacity&&(t+="--default-opacity: "+e.opacity+";"),e.image&&(t+="--background-main: url("+e.image+");")}return t},x.prototype.changeTheme=function(e){var t=window.frames.loginIframe.document.body;t.style=e,t.classList=""==e?"":"is-theme";var n=document.body;n.style=e,n.classList=""==e?"":"is-theme"},x.prototype.fireStatEvent=function(e){c.NativeCallModule.nativeCall.CallNativeFunction("FireLoginEvent","remoteStat",e,(function(e,t){}))},x);window.xdas=T},function(e,t,n){"use strict";var r=function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e};function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=n(56),a=n(57),u=n(58),c=n(55),l=n(85);!function(e){var t,n;(n=t=e.NativeCallErrorCode||(e.NativeCallErrorCode={}))[n.Unknown=-1]="Unknown",n[n.Success=0]="Success",n[n.FunctionNotExist=1]="FunctionNotExist",n[n.ParamaterError=2]="ParamaterError",n[n.CallFailed=3]="CallFailed";var i=(r(f,[{key:"generateId",value:function(){return this.minId===this.maxId?this.invalidId:this.minId++}},{key:"isInvalidId",value:function(e){return e===this.invalidId}}]),f);function f(e,t,n){o(this,f),this.maxId=e,this.minId=t,this.invalidId=n}e.IdGenerator=i,e.idGenerator=new i(1e7,1,0);var d=(r(h,[{key:"CallNativeFunction",value:function(t){for(var n=arguments.length,r=Array(1<n?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];do{if(s.isNullOrUndefined(t)||0===t.length){a.error("funcName is empty");break}a.information("funcName = ",t),this.printArgs(r);for(var o=0,c=0;c<r.length;++c)if(s.isFunction(r[c])){var f=e.idGenerator.generateId(),d=r[c];c===r.length-1?(this.jsReturnCallbacks.set(f,d),o=f,r.pop()):(this.jsCallbacks.set(f,d),r[c]=f)}l.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:u.CommonIPCMessage.msgNCCallNativeFunction,args:[t,o].concat(r)})}while(0)}},{key:"AttachNativeEvent",value:function(t,n){var r=void 0;do{if(s.isNullOrUndefined(t)||0===t.length){a.error("eventName is empty");break}if(s.isNullOrUndefined(n)){a.error("callback is empty");break}var i=e.idGenerator.generateId();if(e.idGenerator.isInvalidId(i)){a.error("id error");break}if(this.eventJsCallbakcs.has(t))this.eventJsCallbakcs.get(t).set(i,n);else{var o=new Map;o.set(i,n),this.eventJsCallbakcs.set(t,o)}r=i}while(0);return r}},{key:"DetachNativeEvent",value:function(e,t){do{if(s.isNullOrUndefined(e)||0===e.length){a.error("eventName is empty");break}if(s.isNullOrUndefined(t)){a.error("callback is empty");break}if(!this.eventJsCallbakcs.has(e)){a.error("event: "+e+" doesnot have listener");break}if(!this.eventJsCallbakcs.get(e).has(t)){a.error("event: "+e+" doesnot have the listener of id="+t);break}this.eventJsCallbakcs.get(e).delete(t)}while(0)}},{key:"CheckNativeFunction",value:function(t,n){do{if(s.isNullOrUndefined(t)||0===t.length){a.error("funcName is empty");break}if(s.isNullOrUndefined(n)){a.error("callback is empty");break}a.information("funcName = ",t);var r=e.idGenerator.generateId();this.jsReturnCallbacks.set(r,n),l.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:u.CommonIPCMessage.msgNCCheckNativeFunction,args:[t,r]})}while(0)}},{key:"RegisterJSFunction",value:function(e,n){var r=t.ParamaterError;do{if(s.isNullOrUndefined(e)||0===e.length){a.error("funcName is empty");break}if(s.isNullOrUndefined(n)){a.error("jsFunc is empty");break}this.jsRegisterFunctions.set(e,n),r=t.Success}while(0);return r}},{key:"bindMsgListeners",value:function(){var e=this;l.CommonIPCRenderer.rendererCommunicator.onMessage(u.CommonIPCMessage.msgNCCallJsFunctionById,(function(t){e.handleCallJsFunctionById(t.msg.args)})),l.CommonIPCRenderer.rendererCommunicator.onMessage(u.CommonIPCMessage.msgNCCallJsFunctionByName,(function(t){e.handleCallJsFunctionByName(t.msg.args)})),l.CommonIPCRenderer.rendererCommunicator.onMessage(u.CommonIPCMessage.msgNCNativeFireEvent,(function(t){e.handleNativeFireEvent(t.msg.args)}))}},{key:"handleCallJsFunctionById",value:function(t){do{var n=t[0];if(!s.isNumber(n)){a.error("id error id = "+n);break}if(e.idGenerator.isInvalidId(n)){a.error("id = "+n+" invalid");break}var r=null,i=0;if(this.jsCallbacks.has(n)&&(i=1,r=this.jsCallbacks.get(n)),this.jsReturnCallbacks.has(n)&&(i=2,r=this.jsReturnCallbacks.get(n)),0===i){a.error("callbacks["+n+"] is null");break}t.splice(0,1),r.apply(null,t),2===i&&this.jsReturnCallbacks.delete(n)}while(0)}},{key:"handleCallJsFunctionByName",value:function(e){do{var t=e[0];if(!s.isString(t)){a.error("funcName error funcName = "+t);break}if(!this.jsRegisterFunctions.has(t)){a.error("jsRegisterFunctions["+t+"] is null");break}e.splice(0,1),this.jsRegisterFunctions.get(t).apply(null,e)}while(0)}},{key:"handleNativeFireEvent",value:function(e){do{var t=e[0];if(!s.isString(t)){a.warning("eventName error eventName = "+t);break}if(!this.eventJsCallbakcs.has(t)){a.warning("eventJsCallbakcs["+t+"] is null");break}e.shift(),this.eventJsCallbakcs.get(t).forEach((function(t,n,r){a.information("value = "+t+", key = "+n+", map = "+r),s.isNullOrUndefined(t)||t.apply(null,e)}))}while(0)}},{key:"notifyNativeCallReady",value:function(){l.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:u.CommonIPCMessage.msgNCNativeCallReady,args:[l.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfo()]})}},{key:"printArgs",value:function(e){for(var t in e)a.information("index "+t+" = ",e[t])}}]),h);function h(){o(this,h),this.jsCallbacks=new Map,this.jsReturnCallbacks=new Map,this.eventJsCallbakcs=new Map,this.jsRegisterFunctions=new Map,this.targetCommunitorInfo=l.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfoByContext(c.CommonIPCBase.mainRendererContext),this.bindMsgListeners(),this.notifyNativeCallReady()}e.NativeCallImpl=d,e.nativeCall=new d}(t.NativeCallModule||(t.NativeCallModule={}))},function(e,t,n){"use strict";var r,i,o;o=function(){var e=function(){},t=Object.prototype.hasOwnProperty,n=Array.prototype.slice;function r(e,r,i){for(var o,s,a=0,u=(i=n.call(arguments,2)).length;a<u;a++)for(o in s=i[a])e&&!t.call(s,o)||(r[o]=s[o])}function i(){}i.class_="Nevis",i.super_=Object,i.extend=function(t,n,i,o){var s,a,u,c=this;return"string"!=typeof t&&(o=i,i=n,n=t,t=null),"function"!=typeof n&&(o=i,i=n,n=function(){return c.apply(this,arguments)}),r(!1,n,c,o),n.prototype=(s=c.prototype,a=i,"function"==typeof Object.create?u=Object.create(s):(e.prototype=s,u=new e,e.prototype=null),a&&r(!0,u,a),u),(n.prototype.constructor=n).class_=t||c.class_,n.super_=c,n};var o=i,s=o.extend((function(e,t,n){this.qrious=e,this.element=t,this.element.qrious=e,this.enabled=Boolean(n)}),{draw:function(){},getElement:function(){return this.enabled||(this.enabled=!0,this.render()),this.element},getModuleSize:function(e){var t=this.qrious,n=t.padding||0,r=Math.floor((t.size-2*n)/e.width);return Math.max(1,r)},getOffset:function(e){var t=this.qrious,n=t.padding;if(null!=n)return n;var r=this.getModuleSize(e),i=Math.floor((t.size-r*e.width)/2);return Math.max(0,i)},render:function(e){this.enabled&&(this.resize(),this.reset(),this.draw(e))},reset:function(){},resize:function(){}}),a=s.extend({draw:function(e){var t,n,r=this.qrious,i=this.getModuleSize(e),o=this.getOffset(e),s=this.element.getContext("2d");for(s.fillStyle=r.foreground,s.globalAlpha=r.foregroundAlpha,t=0;t<e.width;t++)for(n=0;n<e.width;n++)e.buffer[n*e.width+t]&&s.fillRect(i*t+o,i*n+o,i,i)},reset:function(){var e=this.qrious,t=this.element.getContext("2d"),n=e.size;t.lineWidth=1,t.clearRect(0,0,n,n),t.fillStyle=e.background,t.globalAlpha=e.backgroundAlpha,t.fillRect(0,0,n,n)},resize:function(){var e=this.element;e.width=e.height=this.qrious.size}}),u=o.extend(null,{BLOCK:[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28]}),c=o.extend(null,{BLOCKS:[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],FINAL_FORMAT:[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],LEVELS:{L:1,M:2,Q:3,H:4}}),l=o.extend(null,{EXPONENT:[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],LOG:[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175]}),f=o.extend(null,{BLOCK:[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177]}),d=o.extend((function(e){var t,n,r,i,o,s=e.value.length;for(this._badness=[],this._level=c.LEVELS[e.level],this._polynomial=[],this._value=e.value,this._version=0,this._stringBuffer=[];this._version<40&&(this._version++,r=4*(this._level-1)+16*(this._version-1),i=c.BLOCKS[r++],o=c.BLOCKS[r++],t=c.BLOCKS[r++],n=c.BLOCKS[r],!(s<=(r=t*(i+o)+o-3+(this._version<=9)))););this._dataBlock=t,this._eccBlock=n,this._neccBlock1=i,this._neccBlock2=o;var a=this.width=17+4*this._version;this.buffer=d._createArray(a*a),this._ecc=d._createArray(t+(t+n)*(i+o)+o),this._mask=d._createArray((a*(1+a)+1)/2),this._insertFinders(),this._insertAlignments(),this.buffer[8+a*(a-8)]=1,this._insertTimingGap(),this._reverseMask(),this._insertTimingRowAndColumn(),this._insertVersion(),this._syncMask(),this._convertBitStream(s),this._calculatePolynomial(),this._appendEccToData(),this._interleaveBlocks(),this._pack(),this._finish()}),{_addAlignment:function(e,t){var n,r=this.buffer,i=this.width;for(r[e+i*t]=1,n=-2;n<2;n++)r[e+n+i*(t-2)]=1,r[e-2+i*(t+n+1)]=1,r[e+2+i*(t+n)]=1,r[e+n+1+i*(t+2)]=1;for(n=0;n<2;n++)this._setMask(e-1,t+n),this._setMask(e+1,t-n),this._setMask(e-n,t-1),this._setMask(e+n,t+1)},_appendData:function(e,t,n,r){var i,o,s,a=this._polynomial,u=this._stringBuffer;for(o=0;o<r;o++)u[n+o]=0;for(o=0;o<t;o++){if(255!==(i=l.LOG[u[e+o]^u[n]]))for(s=1;s<r;s++)u[n+s-1]=u[n+s]^l.EXPONENT[d._modN(i+a[r-s])];else for(s=n;s<n+r;s++)u[s]=u[s+1];u[n+r-1]=255===i?0:l.EXPONENT[d._modN(i+a[0])]}},_appendEccToData:function(){var e,t=0,n=this._dataBlock,r=this._calculateMaxLength(),i=this._eccBlock;for(e=0;e<this._neccBlock1;e++)this._appendData(t,n,r,i),t+=n,r+=i;for(e=0;e<this._neccBlock2;e++)this._appendData(t,n+1,r,i),t+=n+1,r+=i},_applyMask:function(e){var t,n,r,i,o=this.buffer,s=this.width;switch(e){case 0:for(i=0;i<s;i++)for(r=0;r<s;r++)r+i&1||this._isMasked(r,i)||(o[r+i*s]^=1);break;case 1:for(i=0;i<s;i++)for(r=0;r<s;r++)1&i||this._isMasked(r,i)||(o[r+i*s]^=1);break;case 2:for(i=0;i<s;i++)for(r=t=0;r<s;r++,t++)3===t&&(t=0),t||this._isMasked(r,i)||(o[r+i*s]^=1);break;case 3:for(i=n=0;i<s;i++,n++)for(3===n&&(n=0),t=n,r=0;r<s;r++,t++)3===t&&(t=0),t||this._isMasked(r,i)||(o[r+i*s]^=1);break;case 4:for(i=0;i<s;i++)for(n=i>>1&1,r=t=0;r<s;r++,t++)3===t&&(t=0,n=!n),n||this._isMasked(r,i)||(o[r+i*s]^=1);break;case 5:for(i=n=0;i<s;i++,n++)for(3===n&&(n=0),r=t=0;r<s;r++,t++)3===t&&(t=0),(r&i&1)+!(!t|!n)||this._isMasked(r,i)||(o[r+i*s]^=1);break;case 6:for(i=n=0;i<s;i++,n++)for(3===n&&(n=0),r=t=0;r<s;r++,t++)3===t&&(t=0),(r&i&1)+(t&&t===n)&1||this._isMasked(r,i)||(o[r+i*s]^=1);break;case 7:for(i=n=0;i<s;i++,n++)for(3===n&&(n=0),r=t=0;r<s;r++,t++)3===t&&(t=0),(t&&t===n)+(r+i&1)&1||this._isMasked(r,i)||(o[r+i*s]^=1)}},_calculateMaxLength:function(){return this._dataBlock*(this._neccBlock1+this._neccBlock2)+this._neccBlock2},_calculatePolynomial:function(){var e,t,n=this._eccBlock,r=this._polynomial;for(r[0]=1,e=0;e<n;e++){for(r[e+1]=1,t=e;0<t;t--)r[t]=r[t]?r[t-1]^l.EXPONENT[d._modN(l.LOG[r[t]]+e)]:r[t-1];r[0]=l.EXPONENT[d._modN(l.LOG[r[0]]+e)]}for(e=0;e<=n;e++)r[e]=l.LOG[r[e]]},_checkBadness:function(){var e,t,n,r,i,o=0,s=this._badness,a=this.buffer,u=this.width;for(i=0;i<u-1;i++)for(r=0;r<u-1;r++)(a[r+u*i]&&a[r+1+u*i]&&a[r+u*(i+1)]&&a[r+1+u*(i+1)]||!(a[r+u*i]||a[r+1+u*i]||a[r+u*(i+1)]||a[r+1+u*(i+1)]))&&(o+=d.N2);var c=0;for(i=0;i<u;i++){for(r=e=s[n=0]=0;r<u;r++)e===(t=a[r+u*i])?s[n]++:s[++n]=1,c+=(e=t)?1:-1;o+=this._getBadness(n)}c<0&&(c=-c);var l=0,f=c;for(f+=f<<2,f<<=1;u*u<f;)f-=u*u,l++;for(o+=l*d.N4,r=0;r<u;r++){for(i=e=s[n=0]=0;i<u;i++)e===(t=a[r+u*i])?s[n]++:s[++n]=1,e=t;o+=this._getBadness(n)}return o},_convertBitStream:function(e){var t,n,r=this._ecc,i=this._version;for(n=0;n<e;n++)r[n]=this._value.charCodeAt(n);var o=this._stringBuffer=r.slice(),s=this._calculateMaxLength();s-2<=e&&(e=s-2,9<i&&e--);var a=e;if(9<i){for(o[a+2]=0,o[a+3]=0;a--;)t=o[a],o[a+3]|=255&t<<4,o[a+2]=t>>4;o[2]|=255&e<<4,o[1]=e>>4,o[0]=64|e>>12}else{for(o[a+1]=0,o[a+2]=0;a--;)t=o[a],o[a+2]|=255&t<<4,o[a+1]=t>>4;o[1]|=255&e<<4,o[0]=64|e>>4}for(a=e+3-(i<10);a<s;)o[a++]=236,o[a++]=17},_getBadness:function(e){var t,n=0,r=this._badness;for(t=0;t<=e;t++)5<=r[t]&&(n+=d.N1+r[t]-5);for(t=3;t<e-1;t+=2)r[t-2]===r[t+2]&&r[t+2]===r[t-1]&&r[t-1]===r[t+1]&&3*r[t-1]===r[t]&&(0===r[t-3]||e<t+3||3*r[t-3]>=4*r[t]||3*r[t+3]>=4*r[t])&&(n+=d.N3);return n},_finish:function(){var e,t;this._stringBuffer=this.buffer.slice();var n=0,r=3e4;for(t=0;t<8&&(this._applyMask(t),(e=this._checkBadness())<r&&(r=e,n=t),7!==n);t++)this.buffer=this._stringBuffer.slice();n!==t&&this._applyMask(n),r=c.FINAL_FORMAT[n+(this._level-1<<3)];var i=this.buffer,o=this.width;for(t=0;t<8;t++,r>>=1)1&r&&(i[o-1-t+8*o]=1,t<6?i[8+o*t]=1:i[8+o*(t+1)]=1);for(t=0;t<7;t++,r>>=1)1&r&&(i[8+o*(o-7+t)]=1,t?i[6-t+8*o]=1:i[7+8*o]=1)},_interleaveBlocks:function(){var e,t,n=this._dataBlock,r=this._ecc,i=this._eccBlock,o=0,s=this._calculateMaxLength(),a=this._neccBlock1,u=this._neccBlock2,c=this._stringBuffer;for(e=0;e<n;e++){for(t=0;t<a;t++)r[o++]=c[e+t*n];for(t=0;t<u;t++)r[o++]=c[a*n+e+t*(n+1)]}for(t=0;t<u;t++)r[o++]=c[a*n+e+t*(n+1)];for(e=0;e<i;e++)for(t=0;t<a+u;t++)r[o++]=c[s+e+t*i];this._stringBuffer=r},_insertAlignments:function(){var e,t,n,r=this._version,i=this.width;if(1<r)for(e=u.BLOCK[r],n=i-7;;){for(t=i-7;e-3<t&&(this._addAlignment(t,n),!(t<e));)t-=e;if(n<=e+9)break;n-=e,this._addAlignment(6,n),this._addAlignment(n,6)}},_insertFinders:function(){var e,t,n,r,i=this.buffer,o=this.width;for(e=0;e<3;e++){for(r=t=0,1===e&&(t=o-7),2===e&&(r=o-7),i[r+3+o*(t+3)]=1,n=0;n<6;n++)i[r+n+o*t]=1,i[r+o*(t+n+1)]=1,i[r+6+o*(t+n)]=1,i[r+n+1+o*(t+6)]=1;for(n=1;n<5;n++)this._setMask(r+n,t+1),this._setMask(r+1,t+n+1),this._setMask(r+5,t+n),this._setMask(r+n+1,t+5);for(n=2;n<4;n++)i[r+n+o*(t+2)]=1,i[r+2+o*(t+n+1)]=1,i[r+4+o*(t+n)]=1,i[r+n+1+o*(t+4)]=1}},_insertTimingGap:function(){var e,t,n=this.width;for(t=0;t<7;t++)this._setMask(7,t),this._setMask(n-8,t),this._setMask(7,t+n-7);for(e=0;e<8;e++)this._setMask(e,7),this._setMask(e+n-8,7),this._setMask(e,n-8)},_insertTimingRowAndColumn:function(){var e,t=this.buffer,n=this.width;for(e=0;e<n-14;e++)1&e?(this._setMask(8+e,6),this._setMask(6,8+e)):(t[8+e+6*n]=1,t[6+n*(8+e)]=1)},_insertVersion:function(){var e,t,n,r,i=this.buffer,o=this._version,s=this.width;if(6<o)for(e=f.BLOCK[o-7],t=17,n=0;n<6;n++)for(r=0;r<3;r++,t--)1&(11<t?o>>t-12:e>>t)?(i[5-n+s*(2-r+s-11)]=1,i[2-r+s-11+s*(5-n)]=1):(this._setMask(5-n,2-r+s-11),this._setMask(2-r+s-11,5-n))},_isMasked:function(e,t){var n=d._getMaskBit(e,t);return 1===this._mask[n]},_pack:function(){var e,t,n,r=1,i=1,o=this.width,s=o-1,a=o-1,u=(this._dataBlock+this._eccBlock)*(this._neccBlock1+this._neccBlock2)+this._neccBlock2;for(t=0;t<u;t++)for(e=this._stringBuffer[t],n=0;n<8;n++,e<<=1)for(128&e&&(this.buffer[s+o*a]=1);i?s--:(s++,r?0!==a?a--:(r=!r,6==(s-=2)&&(s--,a=9)):a!==o-1?a++:(r=!r,6==(s-=2)&&(s--,a-=8))),i=!i,this._isMasked(s,a););},_reverseMask:function(){var e,t,n=this.width;for(e=0;e<9;e++)this._setMask(e,8);for(e=0;e<8;e++)this._setMask(e+n-8,8),this._setMask(8,e);for(t=0;t<7;t++)this._setMask(8,t+n-7)},_setMask:function(e,t){var n=d._getMaskBit(e,t);this._mask[n]=1},_syncMask:function(){var e,t,n=this.width;for(t=0;t<n;t++)for(e=0;e<=t;e++)this.buffer[e+n*t]&&this._setMask(e,t)}},{_createArray:function(e){var t,n=[];for(t=0;t<e;t++)n[t]=0;return n},_getMaskBit:function(e,t){var n;return t<e&&(n=e,e=t,t=n),n=t,n+=t*t,(n>>=1)+e},_modN:function(e){for(;255<=e;)e=((e-=255)>>8)+(255&e);return e},N1:3,N2:3,N3:40,N4:10}),h=d,p=s.extend({draw:function(){this.element.src=this.qrious.toDataURL()},reset:function(){this.element.src=""},resize:function(){var e=this.element;e.width=e.height=this.qrious.size}}),_=o.extend((function(e,t,n,r){this.name=e,this.modifiable=Boolean(t),this.defaultValue=n,this._valueTransformer=r}),{transform:function(e){var t=this._valueTransformer;return"function"==typeof t?t(e,this):e}}),v=o.extend(null,{abs:function(e){return null!=e?Math.abs(e):null},hasOwn:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},noop:function(){},toUpperCase:function(e){return null!=e?e.toUpperCase():null}}),g=o.extend((function(e){this.options={},e.forEach((function(e){this.options[e.name]=e}),this)}),{exists:function(e){return null!=this.options[e]},get:function(e,t){return g._get(this.options[e],t)},getAll:function(e){var t,n=this.options,r={};for(t in n)v.hasOwn(n,t)&&(r[t]=g._get(n[t],e));return r},init:function(e,t,n){var r,i;for(r in"function"!=typeof n&&(n=v.noop),this.options)v.hasOwn(this.options,r)&&(i=this.options[r],g._set(i,i.defaultValue,t),g._createAccessor(i,t,n));this._setAll(e,t,!0)},set:function(e,t,n){return this._set(e,t,n)},setAll:function(e,t){return this._setAll(e,t)},_set:function(e,t,n,r){var i=this.options[e];if(!i)throw new Error("Invalid option: "+e);if(!i.modifiable&&!r)throw new Error("Option cannot be modified: "+e);return g._set(i,t,n)},_setAll:function(e,t,n){if(!e)return!1;var r,i=!1;for(r in e)v.hasOwn(e,r)&&this._set(r,e[r],t,n)&&(i=!0);return i}},{_createAccessor:function(e,t,n){var r={get:function(){return g._get(e,t)}};e.modifiable&&(r.set=function(r){g._set(e,r,t)&&n(r,e)}),Object.defineProperty(t,e.name,r)},_get:function(e,t){return t["_"+e.name]},_set:function(e,t,n){var r="_"+e.name,i=n[r],o=e.transform(null!=t?t:e.defaultValue);return(n[r]=o)!==i}}),m=g,y=o.extend((function(){this._services={}}),{getService:function(e){var t=this._services[e];if(!t)throw new Error("Service is not being managed with name: "+e);return t},setService:function(e,t){if(this._services[e])throw new Error("Service is already managed with name: "+e);t&&(this._services[e]=t)}}),b=new m([new _("background",!0,"white"),new _("backgroundAlpha",!0,1,v.abs),new _("element"),new _("foreground",!0,"black"),new _("foregroundAlpha",!0,1,v.abs),new _("level",!0,"L",v.toUpperCase),new _("mime",!0,"image/png"),new _("padding",!0,null,v.abs),new _("size",!0,100,v.abs),new _("value",!0,"")]),C=new y,I=o.extend((function(e){b.init(e,this,this.update.bind(this));var t=b.get("element",this),n=C.getService("element"),r=t&&n.isCanvas(t)?t:n.createCanvas(),i=t&&n.isImage(t)?t:n.createImage();this._canvasRenderer=new a(this,r,!0),this._imageRenderer=new p(this,i,i===t),this.update()}),{get:function(){return b.getAll(this)},set:function(e){b.setAll(e,this)&&this.update()},toDataURL:function(e){return this.canvas.toDataURL(e||this.mime)},update:function(){var e=new h({level:this.level,value:this.value});this._canvasRenderer.render(e),this._imageRenderer.render(e)}},{use:function(e){C.setService(e.getName(),e)}});Object.defineProperties(I.prototype,{canvas:{get:function(){return this._canvasRenderer.getElement()}},image:{get:function(){return this._imageRenderer.getElement()}}});var k=I,S=o.extend({getName:function(){}}).extend({createCanvas:function(){},createImage:function(){},getName:function(){return"element"},isCanvas:function(){},isImage:function(){}}).extend({createCanvas:function(){return document.createElement("canvas")},createImage:function(){return document.createElement("img")},isCanvas:function(e){return e instanceof HTMLCanvasElement},isImage:function(e){return e instanceof HTMLImageElement}});return k.use(new S),k},"object"===("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)&&void 0!==e?e.exports=o():void 0===(i="function"==typeof(r=o)?r.call(t,n,t,e):r)||(e.exports=i)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(59);t.Syntax=r.Syntax,t.ErrorType=r.ErrorType;var i=n(86);t.defaultStorage=i.defaultStorage,t.defaultRequest=i.defaultRequest,t.toResponseError=i.toResponseError,t.generateRequestId=i.generateRequestId,t.OAuth2Client=i.OAuth2Client;var o=n(110);t.AuthClient=o.AuthClient;var s=n(111);t.Auth=s.Auth;var a=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(113));t.authModels=a},function(e,t,n){"use strict";var r=function(e,t,n,r){return new(n=n||Promise)((function(i,o){function s(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},i=function(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=0<(i=s.trys).length&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};Object.defineProperty(t,"__esModule",{value:!0});var o=(s.prototype.run=function(e,t){return r(this,void 0,void 0,(function(){var n,o=this;return i(this,(function(s){return(n=this._fnPromiseMap.get(e))||(n=new Promise((function(n,s){return r(o,void 0,void 0,(function(){var r,o,a;return i(this,(function(i){switch(i.label){case 0:return i.trys.push([0,3,4,5]),[4,this._runIdlePromise()];case 1:return i.sent(),r=t(),o=n,[4,r];case 2:return o.apply(void 0,[i.sent()]),[3,5];case 3:return a=i.sent(),s(a),[3,5];case 4:return this._fnPromiseMap.delete(e),[7];case 5:return[2]}}))}))})),this._fnPromiseMap.set(e,n)),[2,n]}))}))},s.prototype._runIdlePromise=function(){return Promise.resolve()},s);function s(){this._fnPromiseMap=new Map}t.SinglePromise=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AuthClient=function(){}},function(e,t,n){"use strict";var r=function(e,t,n,r){return new(n=n||Promise)((function(i,o){function s(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},i=function(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=0<(i=s.trys).length&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};Object.defineProperty(t,"__esModule",{value:!0});var o=n(59),s=n(112),a=n(86),u=n(87),c="device_id",l=(f.prototype.setDeviceId=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return this._isDeviceIdValid(e)?[4,this._config.storage.setItem(c,e)]:[3,2];case 1:return t.sent(),[3,3];case 2:throw{error:o.ErrorType.INVALID_ARGUMENT,error_description:"deviceId is invalid, deviceId must be a string of 16 to 40 characters."};case 3:return[2]}}))}))},f.prototype.getDeviceId=function(){return r(this,void 0,void 0,(function(){var e;return i(this,(function(t){switch(t.label){case 0:return[4,this._config.storage.getItem(c)];case 1:return e=t.sent(),this._isDeviceIdValid(e)?[3,3]:(e=u.uuidv4(),[4,this._config.storage.setItem(c,e)]);case 2:t.sent(),t.label=3;case 3:return[2,e]}}))}))},f.prototype.signIn=function(e){return r(this,void 0,void 0,(function(){var t,n;return i(this,(function(r){switch(r.label){case 0:return t={},e.username.startsWith("+")?t.phone_number=e.username:e.username.includes("@")?t.email=e.username:t.username=e.username,e.client_id=this._config.clientId,[4,this._config.request(s.ApiUrls.AUTH_SIGN_IN_URL,{method:"POST",body:e,withCaptchaMeta:t})];case 1:return n=r.sent(),[4,this._config.credentialsClient.setCredentials(n)];case 2:return r.sent(),[2,Promise.resolve(n)]}}))}))},f.prototype.signUp=function(e){return r(this,void 0,void 0,(function(){var t;return i(this,(function(n){switch(n.label){case 0:return e.client_id=this._config.clientId,[4,this._config.request(s.ApiUrls.AUTH_SIGN_UP_URL,{method:"POST",body:e})];case 1:return t=n.sent(),[4,this._config.credentialsClient.setCredentials(t)];case 2:return n.sent(),[2,Promise.resolve(t)]}}))}))},f.prototype.signOut=function(){return r(this,void 0,void 0,(function(){var e,t;return i(this,(function(n){switch(n.label){case 0:return[4,this._config.credentialsClient.getAccessToken()];case 1:return e=n.sent(),[4,this._config.request(s.ApiUrls.AUTH_REVOKE_URL,{method:"POST",body:{client_id:this._config.clientId,token:e}})];case 2:return t=n.sent(),[4,this._config.credentialsClient.setCredentials()];case 3:return n.sent(),[2,Promise.resolve(t)]}}))}))},f.prototype.getVerification=function(e){return r(this,void 0,void 0,(function(){var t;return i(this,(function(n){return t={},e.phone_number?t.phone_number=e.phone_number:t.email=e.email,e.client_id=this._config.clientId,[2,this._config.request(s.ApiUrls.VERIFICATION_URL,{method:"POST",body:e,withCaptcha:!0,withCaptchaMeta:t})]}))}))},f.prototype.verify=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return e.client_id=this._config.clientId,[2,this._config.request(s.ApiUrls.VERIFY_URL,{method:"POST",body:e})]}))}))},f.prototype.genProviderRedirectUri=function(e){return r(this,void 0,void 0,(function(){var t,n;return i(this,(function(r){return t=s.ApiUrls.PROVIDER_URI_URL+"?client_id="+this._config.clientId+"&provider_id="+e.provider_id+"&redirect_uri="+encodeURIComponent(e.provider_redirect_uri)+"&state="+e.state,(n=e.other_params)&&"string"==typeof n.sign_out_uri&&0<n.sign_out_uri.length&&(t+="&other_params[sign_out_uri]="+n.sign_out_uri),[2,this._config.request(t,{method:"GET"})]}))}))},f.prototype.genDeviceCode=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return e.client_id=this._config.clientId,[2,this._config.request(s.ApiUrls.AUTH_DEVICE_CODE,{method:"POST",body:e})]}))}))},f.prototype.grantProviderToken=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return e.client_id=this._config.clientId,[2,this._config.request(s.ApiUrls.PROVIDER_TOKEN_URL,{method:"POST",body:e})]}))}))},f.prototype.patchProviderToken=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return e.client_id=this._config.clientId,[2,this._config.request(s.ApiUrls.PROVIDER_TOKEN_URL,{method:"PATCH",body:e})]}))}))},f.prototype.signInWithProvider=function(e){return r(this,void 0,void 0,(function(){var t;return i(this,(function(n){switch(n.label){case 0:return e.client_id=this._config.clientId,[4,this._config.request(s.ApiUrls.AUTH_SIGN_IN_WITH_PROVIDER_URL,{method:"POST",body:e})];case 1:return t=n.sent(),[4,this._config.credentialsClient.setCredentials(t)];case 2:return n.sent(),[2,t]}}))}))},f.prototype.bindWithProvider=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return e.client_id=this._config.clientId,[2,this._config.request(s.ApiUrls.PROVIDER_BIND_URL,{method:"POST",body:e,withCredentials:!0})]}))}))},f.prototype.getUserProfile=function(){return r(this,void 0,void 0,(function(){return i(this,(function(e){return[2,this._config.request(s.ApiUrls.USER_ME_URL,{method:"GET",withCredentials:!0})]}))}))},f.prototype.transByProvider=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return[2,this._config.request(s.ApiUrls.USER_TRANS_BY_PROVIDER_URL,{method:"PATCH",body:e,withCredentials:!0})]}))}))},f.prototype.grantToken=function(e){return r(this,void 0,void 0,(function(){var t;return i(this,(function(n){switch(n.label){case 0:return e.client_id=this._config.clientId,[4,this._config.request(s.ApiUrls.AUTH_TOKEN_URL,{method:"POST",body:e})];case 1:return t=n.sent(),[4,this._config.credentialsClient.setCredentials(t)];case 2:return n.sent(),[2,t]}}))}))},f.prototype.getProviders=function(){return r(this,void 0,void 0,(function(){return i(this,(function(e){return[2,this._config.request(s.ApiUrls.PROVIDER_LIST,{method:"GET",withCredentials:!0})]}))}))},f.prototype.unbindProvider=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return e.client_id=this._config.clientId,[2,this._config.request(s.ApiUrls.PROVIDER_UNBIND_URL+"/"+e.provider_id,{method:"DELETE",withCredentials:!0})]}))}))},f.prototype.checkPassword=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return[2,this._config.request(""+s.ApiUrls.CHECK_PWD_URL,{method:"POST",withCredentials:!0,body:e})]}))}))},f.prototype.bindPhone=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return[2,this._config.request(""+s.ApiUrls.BIND_PHONE_URL,{method:"PATCH",withCredentials:!0,body:e})]}))}))},f.prototype.setPassword=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return[2,this._config.request(""+s.ApiUrls.AUTH_SET_PASSWORD,{method:"PATCH",withCredentials:!0,body:e})]}))}))},f.prototype.getCurUserVerification=function(e){return r(this,void 0,void 0,(function(){var t;return i(this,(function(n){return t={},e.client_id=this._config.clientId,e.target="CUR_USER",[2,this._config.request(s.ApiUrls.VERIFICATION_URL,{method:"POST",body:e,withCredentials:!0,withCaptcha:!0,withCaptchaMeta:t})]}))}))},f.prototype.changeBindedProvider=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return e.client_id=this._config.clientId,[2,this._config.request(s.ApiUrls.PROVIDER_LIST+"/"+e.provider_id+"/trans",{method:"POST",body:{provider_trans_token:e.trans_token},withCredentials:!0})]}))}))},f.prototype.setUserProfile=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){return[2,this._config.request(s.ApiUrls.USER_PRIFILE_URL,{method:"PATCH",body:e,withCredentials:!0})]}))}))},f.prototype.queryUserProfile=function(e){return r(this,void 0,void 0,(function(){var t;return i(this,(function(n){return t=""+s.ApiUrls.USER_QUERY_URL+e,[2,this._config.request(t,{method:"GET",withCredentials:!0})]}))}))},f.prototype._isDeviceIdValid=function(e){return"string"==typeof e&&16<=e.length&&e.length<=48},f);function f(e){this._config={clientId:e.clientId,request:e.request,credentialsClient:e.credentialsClient,storage:e.storage||a.defaultStorage}}t.Auth=l},function(e,t,n){"use strict";var r,i;Object.defineProperty(t,"__esModule",{value:!0}),(r=t.ApiUrls||(t.ApiUrls={})).AUTH_SIGN_IN_URL="/v1/auth/signin",r.AUTH_SIGN_IN_WITH_PROVIDER_URL="/v1/auth/signin/with/provider",r.AUTH_SIGN_UP_URL="/v1/auth/signup",r.AUTH_TOKEN_URL="/v1/auth/token",r.AUTH_DEVICE_CODE="/v1/auth/device/code",r.AUTH_REVOKE_URL="/v1/auth/revoke",r.PROVIDER_BIND_URL="/v1/user/provider/bind",r.PROVIDER_TOKEN_URL="/v1/auth/provider/token",r.PROVIDER_URI_URL="/v1/auth/provider/uri",r.USER_ME_URL="/v1/user/me",r.USER_QUERY_URL="/v1/user/query",r.USER_PRIFILE_URL="/v1/user/profile",r.USER_TRANS_BY_PROVIDER_URL="/v1/user/trans/by/provider",r.VERIFICATION_URL="/v1/auth/verification",r.VERIFY_URL="/v1/auth/verification/verify",r.PROVIDER_LIST="/v1/user/provider",r.PROVIDER_UNBIND_URL="/v1/user/provider",r.CHECK_PWD_URL="/v1/user/sudo",r.BIND_PHONE_URL="/v1/user/contact",r.AUTH_SET_PASSWORD="/v1/user/password",(i=t.VerificationUsages||(t.VerificationUsages={})).REGISTER="REGISTER",i.SIGN_IN="SIGN_IN",i.PASSWORD_RESET="PASSWORD_RESET",i.EMAIL_ADDRESS_CHANGE="EMAIL_ADDRESS_CHANGE",i.PHONE_NUMBER_CHANGE="PHONE_NUMBER_CHANGE"},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0})}]);