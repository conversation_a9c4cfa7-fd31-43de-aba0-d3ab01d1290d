[gd_scene load_steps=4 format=3 uid="uid://vkoll4a5ijb7"]

[ext_resource type="Script" uid="uid://c8hsk5axcjhse" path="res://brick.gd" id="1_lfcxs"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_3"]
size = Vector2(80, 30)

[sub_resource type="AudioStreamGenerator" id="AudioStreamGenerator_1"]

[node name="Brick" type="StaticBody2D"]
script = ExtResource("1_lfcxs")

[node name="ColorRect" type="ColorRect" parent="."]
offset_left = -40.0
offset_top = -15.0
offset_right = 40.0
offset_bottom = 15.0
color = Color(1, 0.5, 0.5, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_3")

[node name="BreakSound" type="AudioStreamPlayer" parent="."]
stream = SubResource("AudioStreamGenerator_1")
