module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=291)}({0:function(e,t,n){e.exports=n(21)(65)},1:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(17),o=n(26),s=n(11),a=n(0),l=n(57);class c{constructor(){this.mThunderVersionStr=void 0,this.mThunderVersionNumber=void 0,this.mWebPluginVersion=void 0}init(){this.loadThunderVersion().catch(),this.loadWebPluginVersion().catch()}get pluginName(){return l.name}get pluginWebviewName(){return`${l.name}-webview`}get pluginDialogRendererName(){return`${l.name}-dialog-renderer`}get binName(){return"VipDownload"}get thunderVersionNumber(){return this.mThunderVersionNumber}get thunderVersionString(){return this.mThunderVersionStr}get clientName(){return"xl_xdas"}get pluginVersion(){return l.version}get webPluginVersion(){return this.mWebPluginVersion}get currentTime(){return Math.floor((new Date).getTime()/1e3)}get currentMillisecond(){return(new Date).getTime()}combineUrlReferfromAidfrom(e,t,n){return""===n&&(n="0"),""===t&&(t="0"),-1!==e.indexOf("?")?e+=`&referfrom=${t}&aidfrom=${n}`:e+=`?referfrom=${t}&aidfrom=${n}`,e}isUrlInDomains(e,t){let n=!1;for(let r=0;r<t.length;++r)if(e.includes(t[r])){n=!0;break}return n}isPeeridMatchSha256Region(e,t,n){let r=!1,o=i.createHash("sha256");o.update(e);let s=o.digest("hex"),l=s[n=n?s.length-n:s.length-1];return l&&(l=l.toUpperCase(),t.includes(l)&&(r=!0)),a.default.getLogger("peer").info(s),r}loadThunderVersion(){return r(this,void 0,void 0,function*(){const{client:e}=yield Promise.resolve().then(()=>n(2)),t=yield e.callServerFunction("GetThunderVersion");this.mThunderVersionStr=t,this.mThunderVersionNumber=0;let r=this.mThunderVersionStr.split(".");if(r&&4===r.length){let e=Number(r[0]).valueOf(),t=Number(r[1]).valueOf(),n=Number(r[2]).valueOf(),i=128;this.mThunderVersionNumber=i*Math.pow(2,24)+e*Math.pow(2,16)+t*Math.pow(2,8)+n}})}loadWebPluginVersion(){return r(this,void 0,void 0,function*(){this.mWebPluginVersion="";let e=s.join(__rootDir,"../../ThunderXWebXDAS/VERION.txt"),t=yield o.FileSystemAWNS.readFileAW(e);t&&(this.mWebPluginVersion=t.toString())})}}t.PluginHelper=c,t.default=new c},10:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(7),o=n(8),s=n(2),a=n(1),l=n(13),c=n(0).default.getLogger("VipDownload:download-kernel-helper");var u;!function(e){e.taskInserted="OnTaskInserted",e.taskCompleted="OnTaskCompleted",e.taskRemoved="OnTaskRemoved",e.taskStatusChanged="OnTaskStatusChanged",e.taskDetailChanged="OnTaskDetailChanged",e.taskDcdnStatusChanged="OnTaskDcdnStatusChanged",e.btSubFileDcdnStatusChanged="OnBtSubFileDcdnStatusChanged",e.btSubFileDetailChanged="OnBtSubFileDetailChanged",e.btSubFileForbidden="OnBtSubFileForbidden",e.downloadItemActive="OnDownloadItemActive",e.downloadItemChosen="OnDownloadItemChosen"}(u=t.DkEventNS||(t.DkEventNS={}));class d extends o.EventEmitter{constructor(){super(),this.mPeerId=void 0,this.init()}init(){this.setMaxListeners(0),this.attachDkEvents()}getTpPeerId(){return r(this,void 0,void 0,function*(){if(this.mPeerId)return this.mPeerId;{let e=yield s.client.callServerFunction("GetTpPeerId");return e&&""!==e&&(this.mPeerId=e),this.mPeerId||""}})}getCurrentCategoryId(){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("GetCurrentCategoryId")})}getTaskBaseInfo(e){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("GetTaskBaseInfo2",e)})}getTaskStatus(e){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("GetTaskStatus",e)})}IsTaskExist(e){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("IsTaskExist",e)})}getTaskDetail(e){return r(this,void 0,void 0,function*(){let t=null,n=yield s.client.callServerFunction("GetTaskInfo",e,void 0,"taskDetail");c.info("getTaskDetail",n);do{if(!n)break;let e=null;try{e=JSON.parse(n)}catch(e){c.warn(e)}if(!e||!e.fileList)break;let r=e.infoId,o=e.type;t={infoId:r,files:new Map};for(let n of e.fileList){let e=-1,s=n.url;o===i.DownloadKernel.TaskType.Bt?(e=n.index,s=this.createBtFileUrl(n.index,r)):o===i.DownloadKernel.TaskType.Group&&(e=n.taskId);let a={subId:e,taskStatus:n.status,filePath:n.filePath,fileName:n.fileName,gcid:n.gcid,cid:n.cid,url:s,refUrl:n.refUrl||"",fileSize:n.fileSize,downloadSize:n.downloadSize,errCode:n.errCode,isNeedDownload:n.isNeedDownload,dcdnStatus:n.dcdnStatus};t.files.set(e,a)}}while(0);return t})}createBtTaskUrl(e){return"bt://"+e}createBtFileUrl(e,t){let n=this.createBtTaskUrl(t);return n=n+"/"+e}getIsHDVideo(e){return r(this,void 0,void 0,function*(){let t=!1,n=yield s.client.callServerFunction("GetConfigModules","HDVideo","domains");return c.silly("HDVideo domains",n),n&&0!==n.length||(n=["hd.xunlei.com"]),e&&(t=a.default.isUrlInDomains(e,n)),t})}getIsWeiDuanYouXi(e){return r(this,void 0,void 0,function*(){let t=!1,n=l.default.getValue("VipDownload","WDYXDomains");return c.info("WDYXDomains domains",n),n&&0!==n.length||(n=["lx.patch1.9you.com"]),e&&(t=a.default.isUrlInDomains(e,n)),t})}startTask(e){this.operateTask(e,"continue")}stopTask(e){this.operateTask(e,"pause")}enableDcdnWithVipCert(e,t,n){return r(this,void 0,void 0,function*(){c.info("enableDcdnWithVipCert","taskId",e,"index",t,"vipCert",n),yield s.client.callServerFunction("EnableDcdnWithVipCert",e,n,t)})}updateDcdnWithVipCert(e,t,n){return r(this,void 0,void 0,function*(){c.info("updateDcdnWithVipCert","taskId",e,"index",t,"vipCert",n),yield s.client.callServerFunction("UpdateDcdnWithVipCert",e,n,t)})}disableDcdnWithVipCert(e,t){return r(this,void 0,void 0,function*(){c.info("disableDcdnWithVipCert","taskId",e,"index",t),yield s.client.callServerFunction("DisableDcdnWithVipCert",e,t)})}selectCategoryView(e,t,n,r){s.client.callServerFunction("SelectCategoryView",e,t,n,r).catch()}getDownloadingActiveTaskId(){return r(this,void 0,void 0,function*(){return yield s.client.callServerFunction("GetDownloadingActiveTaskId")})}attachDkEvents(){s.client.attachServerEvent("OnTaskInserted",this.onTaskInserted.bind(this)),s.client.attachServerEvent("OnTaskRemoved",this.onTaskRemoved.bind(this)),s.client.attachServerEvent("OnTaskStatusChanged",(e,t)=>{{let e=null;try{e=JSON.parse(t)}catch(e){c.warn(e)}if(null!==e)for(let t in e){let n=e[t];this.onTaskStatusChanged(Number(t),n)}}}),s.client.attachServerEvent("OnTaskDetailChanged",(e,t)=>{{let e=null;try{e=JSON.parse(t)}catch(e){c.warn(e)}if(null!==e)for(let t in e){let n=e[t];this.onTaskDetailChanged(Number(t),n)}}}),s.client.attachServerEvent("OnTaskDcdnStatusChanged",(e,t)=>{{let e=null;try{e=JSON.parse(t)}catch(e){c.warn(e)}if(null!==e)for(let t in e){let n=e[t];this.onTaskDcdnStatusChanged(Number(t),n)}}}),s.client.attachServerEvent("OnBtSubFileDcdnStatusChanged",this.onBtSubFileDcdnStatusChanged.bind(this)),s.client.attachServerEvent("OnBtSubFileDetailChanged",(e,t,n)=>{{let e=null;try{e=JSON.parse(n)}catch(e){c.warn(e)}null!==e&&this.onBtSubFileDetailChanged(t,e)}}),s.client.attachServerEvent("OnBtSubFileForbidden",this.onBtSubFileForbidden.bind(this)),s.client.attachServerEvent("OnDownloadItemActive",(e,t,n,r,i,o)=>{this.onDownloadItemActive(t,n,r)}),this.getDownloadingActiveTaskId().then(e=>{e&&this.onDownloadItemActive(i.DownloadKernel.CategroyViewID.Downloading,!0,e)}).catch()}operateTask(e,t,n){s.client.callServerFunction("OperateTask",e,t,n).catch()}onTaskInserted(e,t,n,r){c.info("onTaskInserted categoryId:",t,", categoryViewId:",n);let o=null;if(r){try{o=JSON.parse(r)}catch(e){c.warn(e)}if(null!==o)switch(n){case i.DownloadKernel.CategroyViewID.Downloading:for(let e of o)this.emit(u.taskInserted,e);break;case i.DownloadKernel.CategroyViewID.Completed:for(let e of o)this.emit(u.taskCompleted,e)}}}onTaskRemoved(e,t,n,r){c.info("onTaskRemoved categoryId:",t,", categoryViewId:",n);let o=[];if(r)switch(o=JSON.parse(r),n){case i.DownloadKernel.CategroyViewID.Downloading:case i.DownloadKernel.CategroyViewID.Completed:for(let e of o)this.emit(u.taskRemoved,e)}}onTaskStatusChanged(e,t){this.emit(u.taskStatusChanged,e,t)}onTaskDetailChanged(e,t){this.emit(u.taskDetailChanged,e,t)}onTaskDcdnStatusChanged(e,t){this.emit(u.taskDcdnStatusChanged,e,t)}onBtSubFileDcdnStatusChanged(e,t,n,r){this.emit(u.btSubFileDcdnStatusChanged,t,n,r)}onBtSubFileDetailChanged(e,t){this.emit(u.btSubFileDetailChanged,e,t)}onBtSubFileForbidden(e,t,n){this.emit(u.btSubFileForbidden,t,n)}onDownloadItemActive(e,t,n){this.emit(u.downloadItemActive,e,t,n)}}t.DkHelper=d,t.default=new d},11:function(e,t){e.exports=require("path")},12:function(e,t,n){e.exports=n(21)(155)},126:function(e,t,n){"use strict";n.r(t);var r=n(127),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},127:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,s=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(o<3?i(s):o>3?i(t,n,s):i(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(12),s=n(28),a=n(2),l=n(16),c=n(1);n(53);const u=n(0),d=n(94),f=u.default.getLogger("VipDownload:try-tips-renderer"),p=n(295);let h=class extends o.Vue{constructor(){super(...arguments),this.tipText="",this.labelText="",this.buttonText="",this.bkgImagePath="",this.url="",this.aidfrom="",this.config="",this.showPercent=!1}get showBkgImage(){return Boolean(this.bkgImagePath)}get bkgImage(){return"default"===this.bkgImagePath?p:this.bkgImagePath}parseDynamicUrlPath(e){let t=null;do{if(void 0===e||null===e)break;e.match(/[\/]?([^?]*)\?([^\s]*)/)&&(t=RegExp.$2)}while(0);return t}parseDynamicUrlArgs(e){let t={};do{if(void 0===e||null===e)break;let n=/([^&=?]+)=([^&]*)/g;for(;n.exec(e);)t[RegExp.$1]=RegExp.$2}while(0);return t}created(){return i(this,void 0,void 0,function*(){let e=window.location.href,t=this.parseDynamicUrlPath(e),n=this.parseDynamicUrlArgs(t),r=decodeURIComponent(n.params);this.urlParams=JSON.parse(r),this.normalSpeed=this.formatSpeed(this.urlParams.normalSpeed),this.vipSpeed=this.formatSpeed(this.urlParams.vipSpeed);let i=void 0;(i=0===Number(this.urlParams.normalSpeed)?200:Math.ceil(100*(this.urlParams.vipSpeed/this.urlParams.normalSpeed-1)))>99999?(this.multiple="速度爆表",this.showPercent=!1):(this.multiple=i+"%",this.showPercent=!0);let o=i/100;o=o>=5?Math.floor(o):Number(o.toFixed(2)),this.multipleText="本次加速提升了"+o+"倍下载速度",this.tipText=this.urlParams.tipText,this.labelText=this.urlParams.labelText,this.bkgImagePath=this.urlParams.bkgImage,f.info("tipText",this.tipText),f.info("labelText",this.labelText),f.info("bkgImagePath",this.bkgImagePath);let s=this.urlParams.buttonData,a=s.url,l=s.referfrom;this.aidfrom=s.aidfrom||"",this.config=s.config,this.url=c.default.combineUrlReferfromAidfrom(a,l,this.aidfrom),this.buttonText=s.text,f.info("buttonText",this.buttonText),f.info("url",this.url),this.trackShow()})}onClose(){return i(this,void 0,void 0,function*(){yield this.trackClick("x"),(yield d.asyncRemoteCall.getCurrentWindow()).close()})}onJoinVip(){return i(this,void 0,void 0,function*(){yield this.trackClick("pay"),a.client.callRemoteClientFunction(c.default.pluginWebviewName,"OpenPayVipUrl",this.url),yield a.client.callServerFunction("BringMainWndToTop"),(yield d.asyncRemoteCall.getCurrentWindow()).close()})}trackShow(){f.info("trackShow");let e=new Map;e.set("vip_type",this.urlParams.vip_type),e.set("taskid",this.urlParams.taskid),e.set("gcid",this.urlParams.gcid),e.set("url",this.urlParams.url),e.set("filesize",this.urlParams.filesize),e.set("is_login",this.urlParams.is_login),e.set("is_vip",this.urlParams.is_vip),e.set("config",this.config),e.set("aidfrom",this.aidfrom);let t={attribute1:"sy_complete_pop_show",extData:e};l.StatUtilitiesNS.trackEvent("xlx_vip_event",t)}trackClick(e){return i(this,void 0,void 0,function*(){f.info("trackClick");let t=new Map;t.set("clickid",e),t.set("vip_type",this.urlParams.vip_type),t.set("taskid",this.urlParams.taskid),t.set("gcid",this.urlParams.gcid),t.set("url",this.urlParams.url),t.set("filesize",this.urlParams.filesize),t.set("is_login",this.urlParams.is_login),t.set("is_vip",this.urlParams.is_vip),t.set("config",this.config),t.set("aidfrom",this.aidfrom);let n={attribute1:"sy_complete_pop_click",extData:t};yield l.StatUtilitiesNS.trackEvent("xlx_vip_event",n)})}formatSpeed(e){let t="0B";if("number"==typeof e&&e>0){let n=["B","K","M","G","T"],r=0,i=e;for(;i>=1024&&!(r>=4);)i/=1024,r+=1;t=i.toFixed(2)+n[r]}return t+"/s"}mounted(){document.addEventListener("mouseenter",this.handleMouseEnter),document.addEventListener("mouseleave",this.handleMouseLeave)}destroyed(){document.removeEventListener("mouseenter",this.handleMouseEnter),document.removeEventListener("mouseleave",this.handleMouseLeave)}handleMouseEnter(){s.ipcRenderer.send("MR_NOTIFICATION_MSG","hover",!0)}handleMouseLeave(){s.ipcRenderer.send("MR_NOTIFICATION_MSG","hover",!1)}};h=r([o.Component({components:{}})],h),t.default=h},13:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(8),o=n(15),s=n(64),a=n(0).default.getLogger("VipDownload:config");var l;!function(e){e.configGet="OnConfigGet"}(l=t.ConfigEventNS||(t.ConfigEventNS={}));class c extends i.EventEmitter{constructor(){super(),this.mConfig=new s.Config,this.mConfigInitFinish=!1,this.init().catch()}init(){return r(this,void 0,void 0,function*(){this.setMaxListeners(0);let e=!1,t=yield this.downloadConfig();t&&(e=yield this.config.loadConfigData(t)),a.info("config init","result",e),this.config.printConfigData(),this.mConfigInitFinish=!0,this.emit(l.configGet)})}isConfigInitFinish(){return this.mConfigInitFinish}getValue(e,t){return this.config.getValue(e,t)}downloadConfig(){return r(this,void 0,void 0,function*(){return new Promise(e=>{let t=new o.HttpSession;t.url="http://media.info.client.xunlei.com/VipDownloadConfig.json",t.get(t=>{t&&200===t.statusCode&&t.body?e(t.body):e(null)},t=>{a.info("error",t),e(null)})})})}get config(){return this.mConfig}}t.ConfigHelper=c,t.default=new c},15:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(61),i=n(62),o=n(63),s=n(5),a=n(0).default.getLogger("http-session"),{isDef:l}=s.ThunderUtil;var c;!function(e){e.HTTP="HTTP",e.HTTPS="HTTPS"}(c=t.Protocol||(t.Protocol={}));t.HttpSession=class{constructor(){this.mRetries=0,this.mHost=void 0,this.mPort=void 0,this.mPath=void 0,this.mAuth=void 0,this.mAccept=void 0,this.mBody=null,this.mUrl=void 0,this.mProtocol=c.HTTP,this.mTimeout=void 0,this.mCurRetries=0}set host(e){this.mHost=e}get host(){return this.mHost}set port(e){this.mPort=e}get port(){let e=void 0;return e=l(this.mPort)?this.mPort:this.protocol===c.HTTPS?443:80}set path(e){this.mPath=e}get path(){return this.mPath}set url(e){this.mUrl=e}get protocol(){return this.mProtocol}set protocol(e){this.mProtocol=e}get url(){return this.mUrl}set auth(e){this.mAuth=e}get auth(){return this.mAuth}set accept(e){this.mAccept=e}get accept(){return this.mAccept}set body(e){this.mBody=e}get body(){return this.mBody}set retries(e){this.mRetries=e}get retries(){return this.mRetries}set timeout(e){this.mTimeout=e}get timeout(){return this.mTimeout}post(e,t){do{let n=this.body;if(!n){a.info("body is empty"),t(null);break}let r=this.auth,i=this.accept,o={hostname:this.host,port:this.port,path:this.path||"/",method:"POST",auth:r||void 0,headers:{"Content-Length":n?n.length:0,Accept:i||"*/*"}};a.verbose("option",o);try{this.postImpl(n,o,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,a.info("mCurRetries",this.mCurRetries),this.post(e,t)):t(n)})}catch(e){a.warn("error ",e),t(null)}}while(0)}get(e,t){let n=null;if(this.url){let e=r.parse(this.url,!0);e&&"https:"===e.protocol?this.protocol=c.HTTPS:this.protocol=c.HTTP,n=this.url}else{let e=this.auth,t=this.accept;n={hostname:this.host,port:this.port,path:this.path||"/",method:"GET",auth:e||void 0,headers:{Accept:t||"*/*"}}}a.verbose("option",n);try{this.getImpl(n,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,a.info("mCurRetries",this.mCurRetries),this.get(e,t)):t(n)})}catch(e){a.warn("error ",e),t(null)}}postImpl(e,t,n,r){let s=(this.protocol===c.HTTPS?o:i).request(t,e=>{let t=null;e.on("data",e=>{t=t?Buffer.concat([t,e]):e}),e.on("end",()=>{a.info("statusCode",e.statusCode),a.info("headers",e.headers),n({statusCode:e.statusCode,headers:e.headers,body:t})})});s.on("error",e=>{r(e)}),s.on("timeout",()=>{s.abort()}),this.timeout&&s.setTimeout(this.timeout),s.write(e),s.end()}getImpl(e,t,n){(this.protocol===c.HTTPS?o:i).get(e,e=>{let n=null;e.on("data",e=>{n=n?Buffer.concat([n,e]):e}),e.on("end",()=>{a.info("statusCode",e.statusCode),a.info("headers",e.headers),t({statusCode:e.statusCode,headers:e.headers,body:n})})}).on("error",e=>{n(e)})}}},157:function(e,t,n){"use strict";const r=n(32);if("renderer"===process.type){if(r.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(158);e.require=t.require,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,r)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,r)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(r.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(162);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},158:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const r=n(28),i=n(48),o=n(159),s=n(160),a=n(85),l=n(161),c=n(32),u=n(86),d=r.ipcRenderer,f=process.electronBinding("v8_util"),p=new o.default,h=f.createIDWeakMap(),y=f.getHiddenValue(global,"contextId");class m{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function v(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let r=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:v(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(r))return{type:"buffer",value:i.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(u.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(f.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:f.getHiddenValue(e,"__remote_id__")};let r={type:e instanceof m?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)r.members.push({name:t,value:n(e[t])});return t.delete(e),r}if("function"==typeof e){return{type:"function",id:p.add(e),location:f.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function g(e,t,n){u.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function b(e,t,n,r=!1){const i=t=>{if(e.hasOwnProperty(t.name)&&!r)return;let n,i={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(h.has(t.value.refId)&&(n=h.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=C(t.value,e,t.name);i.get=(()=>n),i.set=(e=>n=e)}else"get"===t.type&&(i.get=(()=>n),t.writable&&(i.set=(e=>{n=e})),n=C(t.value));Object.defineProperty(e,t.name,i)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)i(n[t])}}function w(e,t,n){if(n){let t=C(n);Object.setPrototypeOf(e,t)}}function V(e,t){f.setHiddenValue(e,"__remote_id__",t)}function A(e){return f.getHiddenValue(e,"__remote_id__")}function C(e,t,n){const r={value:()=>e.value,array:()=>e.members.map(e=>C(e)),buffer:()=>i.Buffer.from(e.value),promise:()=>Promise.resolve({then:C(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:r,value:i}=e.members[n];t[r]=i}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let o;return e.type in r?o=r[e.type]():e.refId?h.has(e.refId)?(f.addRemoteObjectRef(y,e.refId),o=h.get(e.refId)):(c.warn("[metaToValue] refId point to null"+e.refId),o="function"===e.type?()=>{}:{}):e.id?h.has(e.id)?(f.addRemoteObjectRef(y,e.id),b(o=h.get(e.id),e.id,e.members,!0),w(o,e.id,e.proto)):(o="function"===e.type?t?function(e,t,n){if(h.has(n.id))return h.get(n.id);let r=A(e),i=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(i,"name",{value:t,writable:!1,enumerable:!0});let o=new Proxy(i,{apply:(e,n,i)=>new Promise((e,o)=>{let c=A(n);if(c||(c=A(n.__remoteObj_)),!c)throw Error("is this function was a bound function?");let u=a.default.browser.memberCall,f=l.default(u),p=v(i);d.send(u,y,f,c,r,t,p),s.default.add(f,t=>{try{g(C(t),e,o)}catch(e){o(e)}})}),construct:(e,n,i)=>new Promise((e,i)=>{let o=a.default.browser.memberConstruct,c=l.default(o);d.send(o,y,c,r,t,v(n)),s.default.add(c,t=>{try{let n=C(t);e(n)}catch(e){i(e)}})})});return f.setHiddenValue(i,"__remote_id__",n.id),o}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return V(n,t),new Proxy(n,{apply:(e,n,r)=>new Promise((e,i)=>{let o=a.default.browser.functionCall,c=l.default(o),u=A(n);d.send(o,y,c,u,t,v(r)),s.default.add(c,t=>{try{g(C(t),e,i)}catch(e){i(e)}})}),construct:(e,n,r)=>new Promise((e,r)=>{let i=a.default.browser.construct,o=l.default(i);d.send(i,y,o,t,v(n)),s.default.add(o,t=>{try{let n=C(t);e(n)}catch(e){r(e)}})})})}(e):{},f.setRemoteObjectFreer(o,y,e.id),h.set(e.id,o),f.setHiddenValue(o,"__remote_id__",e.id),f.addRemoteObjectRef(y,e.id),function(e){let t=A(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,r){if("function"==typeof r)throw new Error("set a function to a remote member is dangerous");return new Promise((i,o)=>{let c=a.default.browser.memberSet,u=l.default(c),f=v([r]);d.send(c,y,u,t,n,f),s.default.add(u,t=>{try{let s=C(t);e[n]=r,i(s)}catch(e){o(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((r,i)=>{let o=a.default.browser.memberGet,c=l.default(o);d.send(o,y,c,t,n),s.default.add(c,t=>{try{const o=C(t);e[n]=o,r(o)}catch(e){i(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let r=a.default.browser.sync,i=l.default(r);d.send(r,y,i,t),s.default.add(i,r=>{try{if(r.id!==t)throw Error("SYNC_RETURN: remote id not match");let i=C(r);e(i)}catch(e){n(e)}})})}}})}(o),b(o,e.id,e.members),w(o,e.id,e.proto),Object.defineProperty(o.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=o[e],r=o.__proto__;for(;r;){if(Object.prototype.hasOwnProperty.call(r,e)){delete r[e];break}r=r.__proto__}Object.defineProperty(o,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):c.error("no id of meta:",e),o}t.Interest=m;class R{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,r)=>{let i=this.getChannel(e),o=l.default(i);d.send(i,y,o,t),s.default.add(o,e=>{try{let t=C(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){r(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=a.default.browser.require;break;case"builtin":t=a.default.browser.builtIn;break;case"global":t=a.default.browser.global;break;case"current_window":t=a.default.browser.currentWindow;break;case"current_web_contents":t=a.default.browser.currentWebContents;break;case"client_web_contents":t=a.default.browser.clientWebContents;break;case"web_contents":t=a.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function S(e,t,n){try{s.default.invoke(t,n).remove(t)}finally{s.default.remove(t)}}d.on(a.default.renderer.requireReturn,S),d.on(a.default.renderer.getBuiltInReturn,S),d.on(a.default.renderer.getGlobalReturn,S),d.on(a.default.renderer.currentWindowReturn,S),d.on(a.default.renderer.currentWebContentsReturn,S),d.on(a.default.renderer.functionCallReturn,S),d.on(a.default.renderer.constructReturn,S),d.on(a.default.renderer.memberCallReturn,S),d.on(a.default.renderer.memberSetReturn,S),d.on(a.default.renderer.memberGetReturn,S),d.on(a.default.renderer.memberConstructReturn,S),d.on(a.default.renderer.callback,(e,t,n)=>{p.apply(t,C(n))}),d.on(a.default.renderer.syncReturn,S),d.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{c.info("[RELEASE_CALLBACK]: callbackId:",t),p.remove(t)}),process.on("exit",()=>{d.send(a.default.browser.contextRelease)});const x=["__resolve","__isResolved"],O=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],k=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function P(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(k(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(k(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(k(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(k(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(k(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(k(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,r)=>(k(e),Reflect.defineProperty(e.__remoteObj_,n,r)),get:(t,n,r)=>{if("string"==typeof n){if(String.prototype.includes.call(O,n)){return e[n]}if(String.prototype.includes.call(x,n)){return e[n]}}return k(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,r,i)=>(k(e),Reflect.set(e.__remoteObj_,n,r,i)),ownKeys:t=>(k(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,r)=>{k(e),Reflect.apply(e.__remoteObj_,n,r)},construct:(t,n,r)=>{if(k(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,r)=>{let i=a.default.browser.construct,o=l.default(i),c=f.getHiddenValue(e.__remoteObj_,"__remote_id__");d.send(i,y,o,c,v(n)),s.default.add(o,e=>{try{t(C(e))}catch(e){r(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=A(e);t&&V(n,t)}}),n}t.remoteRequire=function(e){return P(new R("module",e))},t.getBuiltin=function(e){return P(new R("builtin",e))},t.getGlobal=function(e){return P(new R("global",e))},t.getCurrentWindow=function(){return P(new R("current_window"))},t.getCurrentWebContents=function(){return P(new R("current_web_contents"))},t.getWebContents=function(){return P(new R("web_contents"))}},159:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=r.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,i=(new Error).stack;let o,s=n.exec(i);for(;null!==s;){const e=s[1];if(!e.includes("native")&&!e.includes("electron.asar")){o=/([^/^)]*)\)?$/gi.exec(e)[1];break}s=n.exec(i)}return this.callbacks[t]=e,r.setHiddenValue(e,"__remote_callback_id__",t),r.setHiddenValue(e,"__remote_call_location__",o),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(r.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}},16:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(1),o=n(10),s=n(2),a=n(0).default.getLogger("stat-utilities"),l=n(38);!function(e){let t="0",n=0,c=0,u=0,d="",f=0;function p(){return r(this,void 0,void 0,function*(){t="0",n=0,c=0,f=(yield s.client.callServerFunction("IsLogined"))?1:0;let e=yield s.client.callServerFunction("GetAllUserInfo");if(e)if(t=e.userID||"0",e.vipList&&e.vipList[0]){if(e.vipList[0].isVip){let t=Number(e.vipList[0].isVip).valueOf();c=t>0?1:0}else c=0;e.vipList[0].vasType&&(n=Number(e.vipList[0].vasType).valueOf())}else c=0;yield function(){return r(this,void 0,void 0,function*(){if(u=0,"0"==t)return;let e=`https://soa-vip-ssl.xunlei.com/xlvip.common.mooseapi/querytags?sessionid=${yield function(){return r(this,void 0,void 0,function*(){if(d)return d;{let e=yield s.client.callServerFunction("GetSessionID");return e&&""!==e&&(d=e),d||"0"}})}()}&userid=${t}&tags=usedToBeDLVip&platform=xlx`;l.default.get(e,{timeout:1e4}).then(e=>{let t=null;a.info("queryUserTag response:",e),null!==e&&200===e.status&&null!==e.data&&1===e.data.code&&e.data.result&&(t=e.data.result,a.info("userTag.usedToBeDLVip",t.usedToBeDLVip),u=1==t.usedToBeDLVip?0:1)}).catch(e=>{a.error("err:",e)})})}()})}s.client.attachServerEvent("onUserInfoChange",(e,t,n)=>r(this,void 0,void 0,function*(){yield p()})),s.client.attachServerEvent("onLoginStatusChange",(e,t,n)=>r(this,void 0,void 0,function*(){yield p()})),p(),e.trackEvent=function(e,t){return r(this,void 0,void 0,function*(){let r=(t=t||{}).attribute1||"",l=t.attribute2||"",d=t.extData||new Map;if(d.set("plugin_version",i.default.pluginVersion),!d.has("cpeerid")){let e=yield o.default.getTpPeerId();d.set("cpeerid",e)}d.set("is_new_user",u),d.has("is_login")||d.set("is_login",f),d.has("is_vip")||d.has("isvip")||d.set("is_vip",c),d.has("vip_type")||d.has("vas_type")||(c&&5===n?d.set("vip_type",5):c&&n>2?d.set("vip_type",3):c?d.set("vip_type",2):d.set("vip_type",0));let p=function(e){let t="";return e.forEach((e,n)=>{""!==t&&(t+=","),t=t+n+"="+e}),t}(d);a.info("key",e),a.info("attribute1",r),a.info("attribute2",l),a.info("extData",p),p=encodeURIComponent(p),a.info("encode extData",p),yield s.client.callServerFunction("TrackEvent",e,r,l,0,0,0,0,p)})}}(t.StatUtilitiesNS||(t.StatUtilitiesNS={}))},160:function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t={};e.add=function(e,n,r){t[e]={func:n,thisArg:r}},e.invoke=function(n,...r){let i=t[n];return i.thisArg?i.func.apply(i.thisArg,...r):i.func(...r),e},e.remove=function(e){delete t[e]}}(r||(r={})),t.default=r},161:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=0;t.default=function(e){return e?e.concat(".").concat(String(++r)):String(++r)}},162:function(e,t,n){"use strict";const r=n(28),i=n(163),o=n(85),s=n(164),a=n(32),l=n(86),c=r.ipcMain,u=process.electronBinding("v8_util");let d=u.createDoubleIDWeakMap();const f=new i.default;function p(e,t,n,r,i,o){let s,l=!1,c=null,u=!1;do{try{s=t[r]}catch(e){l=!0}if(l)try{s=n.value[r],l=!1,n.meta.extendedMemberNames.push(r),u=!0}catch(e){a.error(`property ${r} untouchable, even try root[name]`)}if(l)break;let i=Object.getOwnPropertyDescriptor(t,r);if(void 0===i){a.warn(`descriptor of property ${r} is undefined`);break}c={name:r,enumerable:i.enumerable,writable:!1,type:"get"},void 0===i.get&&"function"==typeof s?c.type="method":((i.set||i.writable)&&(c.writable=!0),c.type="get"),u?(c.configurable=!0,c.value=m(e,s,o,!1,null)):c.value=m(e,s,o,!1,n)}while(0);return c}function h(e,t,n,r=null){let i=Object.getOwnPropertyNames(t);"function"==typeof t&&(i=i.filter(function(e){return!String.prototype.includes.call(s.propertiesOfFunction,e)}));let o=[];do{if(0===i.length)break;let s=i.length;for(let a=0;a<s;a++){let s=p(e,t,n,i[a],0,r);s&&o.push(s)}}while(0);return o}function y(e,t,n,r=null){let i=null,o=Object.getPrototypeOf(t);return i=null===o||o===Object.prototype||o===Function.prototype?null:m(e,o,r,!1,n)}function m(e,t,n=null,r=!1,i=null){n=null===n?{}:n;const o={type:typeof t};"object"===o.type&&(o.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":l.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&u.getHiddenValue(e,"simple")&&(n="value"),n}(t,r));do{if("object"===o.type||"function"===o.type){let r=f.getIdOfObject(t);if(r&&n[r]){o.refId=r,f.add(e,t);break}}"array"===o.type?o.members=t.map(t=>m(e,t,n)):"object"===o.type||"function"===o.type?(null==i&&(o.extendedMemberNames=[],i={value:t,meta:o}),o.name=t.constructor?t.constructor.name:"",o.id=f.add(e,t),n[o.id]=!0,o.members=h(e,t,i,n),o.proto=y(e,t,i,n)):"buffer"===o.type?o.value=Buffer.from(t):"promise"===o.type?(t.then(function(){},function(){}),o.then=m(e,function(e,n){t.then(e,n)})):"error"===o.type?(o.members=v(t),o.members.push({name:"name",value:t.name})):"date"===o.type?o.value=t.getTime():(o.type="value",o.value=t)}while(0);return o}function v(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function g(e,t,n,i){const s=function(i){let l,c,p=0,h=0;switch(i.type){case"value":return i.value;case"remote-object":return f.get(i.id);case"array":return g(e,t,n,i.value);case"buffer":return Buffer.from(i.value);case"date":return new Date(i.value);case"promise":return Promise.resolve({then:s(i.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:i.name}),p=0,h=(c=i.members).length;p<h;p++)e[(l=c[p]).name]=s(l.value);return e}case"function":{const s=e.id,l=[n,i.id];if(a.info("renderer function id:"+l),d.has(l))return d.get(l);let c=function(...t){a.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||s!==e.id?function(e,t,n){let i="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof r.webContents.constructor){const{sender:e}=t[0],r=e.eventNames().filter(t=>{let r=e.listeners(t),i=!1;return r.forEach(e=>{e===n&&(i=!0)}),i});r.length>0&&(i+=`\nRemote event names: ${r.join(", ")}`,r.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}a.warn(i)}(i,n,c):e.send(o.default.renderer.callback,i.id,m(e,n))};return Object.defineProperty(c,"length",{value:i.length}),u.setRemoteCallbackFreer(c,t,n,i.id,e),d.set(l,c),c}default:throw new TypeError(`Unknown type: ${i.type}`)}};return i.map(s)}function b(e,t,n,r){let i,o;try{return t.apply(n,r)}catch(e){return o=t.name,new Error(`Could not call remote function '${i=null!=o?o:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function w(e){return{type:"exception",message:e.message,stack:e.stack||e}}function V(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var A;!function(e){const t=(e,t,...n)=>{const r=e.sender;r.isDestroyed()?a.warn("webcontext is destroyed, id=",r.id):r.send(t,...n)};e.startServer=function(){c.on(o.default.browser.require,(e,n,r)=>{a.info(`[REQUIRE] module=${r} `);let i=process.mainModule.require(r),s=m(e.sender,i);t(e,o.default.renderer.requireReturn,n,s)}),c.on(o.default.browser.builtIn,(e,n,i,s)=>{a.info(`[BUILTIN]: property=${s} contextId=${n}`);let l=r[s],c=m(e.sender,l);a.info(`[BUILTIN]: returns remoteId:${c.id}, type: ${typeof l}`),t(e,o.default.renderer.getBuiltInReturn,i,c)}),c.on(o.default.browser.global,(e,n,r,i)=>{a.info(`[GLOBAL]: proerty:${i} contextId=${n}`);let s,l=global[i];s=m(e.sender,l),a.info(`[GLOBAL]: returns remoteid=${s.id}, obj=`+typeof l),t(e,o.default.renderer.getGlobalReturn,r,s)}),c.on(o.default.browser.currentWindow,(e,n,r,i)=>{a.info(`[CURRENT_WINDOW]: property=${i} contextId=${n}`);let s=e.sender.getOwnerBrowserWindow.call(e.sender),l=m(e.sender,s);a.info(`[CURRENT_WINDOW]: returns remoteid=${l.id}, obj=`+s),t(e,o.default.renderer.currentWindowReturn,r,l)}),c.on(o.default.browser.currentWebContents,(e,n,r,i)=>{t(e,o.default.renderer.currentWebContentsReturn,r,m(e.sender,e.sender))}),c.on(o.default.browser.webContents,(e,n,i,s)=>{a.info(`[WebContents]: proerty:${s} contextId=${n}`);let l,c=r.webContents;l=m(e.sender,c),a.info(`[WebContents]: returns remoteid=${l.id}, obj=`+typeof c),t(e,o.default.renderer.webContentsReturn,i,l)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(r=>{n?e.once(r,t[r]):e.on(r,t[r])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};c.on(o.default.browser.construct,(n,r,i,s,l)=>{let c,u=null;try{a.info(`[CONSTRUCTOR]: remoteId=${s} `);let d=l.length>0?l[l.length-1]:null;l=g(n.sender,n.frameId,r,l);let p=f.get(s);null==p&&V(`Cannot call constructor on missing remote object ${s}`),d&&"interest"===d.type&&(u=l.pop());let h=new(Function.prototype.bind.apply(p,[null,...l]));h&&u&&e(h,u),c=m(n.sender,h,null,!1),a.info(`[CONSTRUCTOR]: returns remoteId =${c.id} name=${p.name} `)}catch(e){c=w(e)}finally{t(n,o.default.renderer.constructReturn,i,c)}}),c.on(o.default.browser.functionCall,function(e,n,r,i,s,l){let c;try{a.info(`[FUNCTION_CALL]: remoteId=${s}`),l=g(e.sender,e.frameId,n,l);let u=f.get(s);if(null==u)a.error(`Cannot call function on missing remote object ${s}`),c=m(e.sender,void 0);else{let t=i?f.get(i):global;if(t){let n=b(0,u,t,l);c=m(e.sender,n)}else a.error(`Cannot call function(${s}) on missing context(${i})`),c=m(e.sender,void 0)}a.info(`[FUNCTION_CALL]: name=${u.name}`)}catch(e){c=w(e)}finally{t(e,o.default.renderer.functionCallReturn,r,c)}}),c.on(o.default.browser.memberCall,function(e,n,r,i,s,l,c){let u;a.info(`[MEMBER_CALL]: thisArg=${i}, remoteId=${s}, method=${l}, args count=${c.length}`);try{c=g(e.sender,e.frameId,n,c);let d=f.get(s);null==d&&V(`Cannot call function '${l}' on missing remote object ${s}`);let p=i?f.get(i):d;if(p){let t=b(0,d[l],p,c);u=m(e.sender,t),a.info("[MEMBER_CALL]: return="+t)}else u=m(e.sender,void 0)}catch(e){u=w(e)}finally{t(e,o.default.renderer.memberCallReturn,r,u)}}),c.on(o.default.browser.memberGet,function(e,n,r,i,s){let l;try{a.info(`[MEMBER_GET]: remoteId=${i}, property=`,s);let n=f.get(i);null==n&&V(`Cannot get property '${Object.toString.call(s)}' on missing remote object ${i}`);let c=n[s];l=m(e.sender,c)}catch(e){l=w(e)}finally{t(e,o.default.renderer.memberGetReturn,r,l)}}),c.on(o.default.browser.memberSet,function(e,n,r,i,s,l){try{a.info(`[MEMBER_SET]: remoteId=${i}, property=`+s),l=g(e.sender,e.frameId,n,l);let c=f.get(i);null==c&&V(`Cannot set property '${Object.toString.call(s)}' on missing remote object ${i}`),c[s]=l[0],t(e,o.default.renderer.memberSetReturn,r,{type:"value",value:!0})}catch(n){t(e,o.default.renderer.memberSetReturn,r,w(n))}}),c.on(o.default.browser.memberConstruct,function(n,r,i,s,l,c){let u,d=null;try{a.info(`[MEMBER_CONSTRUCTOR]: regId=${s}, method=${l}`);let p=c.length>0?c[c.length-1]:null;c=g(n.sender,n.frameId,r,c);let h=f.get(s);null==h&&V(`Cannot call constructor '${l}' on missing remote object ${s}`),p&&"interest"===p.type&&(d=c.pop());let y=h[l],v=new(Function.prototype.bind.apply(y,[null,...c]));v&&d&&e(v,d),u=m(n.sender,v)}catch(e){u=w(e)}finally{t(n,o.default.renderer.memberConstructReturn,i,u)}}),c.on(o.default.browser.sync,function(e,n,r,i){let s=f.get(i);t(e,o.default.renderer.syncReturn,r,m(e.sender,s))}),c.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=f.get(t);if(r.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let r=n.name;r||(r=n.constructor?n.constructor.name:""),f.remove(e.sender.id,t)}else t<0&&a.warn("remote id reference to nothing:",t)}),c.on(o.default.browser.contextRelease,e=>{f.clear(e.sender.id)})},e.getObjectRegistry=function(){return f}}(A||(A={})),e.exports=A},163:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(32),i=-1*Math.pow(2,31),o=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),r=e.id;let i=this.owners.get(r);return i||(i=new Set,this.owners.set(r,i),this.registerDeleteListener(e,r)),i.has(n)||(i.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return o.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=o.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=i)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),o.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(o.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),i=(o,s)=>{s===n&&(r.info("render-view-deleted: processid="+n),(()=>{r.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",i),this.clear(t))};e.on("render-view-deleted",i)}}},164:function(e,t,n){"use strict";var r;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(r||(r={})),e.exports=r},17:function(e,t){e.exports=require("crypto")},170:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"xlx-dialog-tryvip"},[r("div",{staticClass:"xlx-dialog-tryvip__header"},[r("h4",[e._v("迅雷-加速试用结束")]),e._v(" "),r("a",{staticClass:"xlx-dialog-tryvip__close",attrs:{href:"javascript:;"},on:{click:function(t){return e.onClose()}}},[r("i")])]),e._v(" "),r("div",{staticClass:"xlx-dialog-tryvip__picture"},[r("a",{attrs:{href:"#"}},[e.showBkgImage?r("img",{staticStyle:{cursor:"default"},attrs:{src:e.bkgImage}}):r("img",{attrs:{src:n(294)}})]),e._v(" "),e.showBkgImage?e._e():r("div",{staticClass:"xlx-dialog-tryvip__data"},[r("p",[r("em",[e.showPercent?r("i"):e._e(),e._v("\n            "+e._s(this.multiple)+"\n          ")]),e._v("\n          "+e._s(this.multipleText)+"\n        ")]),e._v(" "),r("div",{staticClass:"xlx-dialog-tryvip__curve"},[r("i"),e._v(" "),r("span",{staticClass:"xlx-dialog-tryvip__speed xlx-dialog-tryvip__speed--novip"},[e._v(e._s(this.normalSpeed))]),e._v(" "),r("span",{staticClass:"xlx-dialog-tryvip__speed xlx-dialog-tryvip__speed--vip"},[e._v(e._s(this.vipSpeed))])])])]),e._v(" "),r("div",{staticClass:"xlx-dialog-tryvip__content"},[r("p",[e._v(e._s(this.tipText))]),e._v(" "),r("a",{staticClass:"xlx-dialog-tryvip__button",attrs:{href:"javascript:;"},on:{click:function(t){return e.onJoinVip()}}},[e._v("\n        "+e._s(this.buttonText)+"\n        "),r("span",{directives:[{name:"show",rawName:"v-show",value:e.labelText,expression:"labelText"}]},[e._v(e._s(e.labelText))])])])])])},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},2:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(8),o=n(58),s=n(30),a=n(37);function l(e){a.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let c=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends i.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,r){do{if(!n&&this.client)break;if(t||(t=s.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase()))break;if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===s.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");let i=new o.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=i,n||(this.client=i),i.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(i,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action){a.information("client on object freer",e);do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0)}else if("call_client_by_id"===e.action)this.callFunctionById(i,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(i,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(i,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===i&&this.emit("stat_call_function_back",i.getContext(),e);const n=this.retCallbackMap[t.rid].callback;n&&(t.success?n(null,t.data):n(t.error,t.data)),delete this.retCallbackMap[t.rid]}}),i.on("error",e=>{r&&r("error",e),this.emit("socket-error",e,i.getContext(),n,i.isInprocess()),delete this.singletonMap[t.toLowerCase()]}),i.isInprocess()?(r&&r("connect"),this.emit("connect",i.getContext(),n,!0)):i.on("connect",()=>{r&&r("connect"),this.emit("connect",i.getContext(),n,!1)}),i.on("end",()=>{let e=i.isInprocess();a.information("server is ended, and this client emit end",t,n,e),r&&r("end",i.getContext(),n,e),this.emit("end",i.getContext(),n,e),delete this.singletonMap[t.toLowerCase()]}),this.registry(i)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return r(this,void 0,void 0,function*(){let r=null,i=yield this.crossCallServerFunctionEx(e,t,...n);return i&&(r=i[0]),r})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(r,t,...n)}}crossIsRemoteClientExist(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(r,t,n)}})}crossCallRemoteClientFunction(e,t,n,...r){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(i,t,n,...r)}}crossAttachServerEvent(e,t,n){let r=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");r=this.internalAttachServerEvent(i,t,n)}return r}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(r,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(r,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.generateId();const i={alias:t,action:"register",rid:r};let o=(e,r)=>{e?(a.error("register error",e.message),n(r)):n(t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(a.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===s.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,r)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let r=this.generateId();const i={action:"check_server_function_exist",method:t,rid:r};let o=(e,t)=>{n(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((r,i)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let i=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const o={rid:i,method:t,args:n};let s=(t,n)=>{t?(a.error("callServerFunction error",t,e.getContext()),r([null,t])):r([n,void 0])};this.retCallbackMap[i]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,r)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let r=this.generateId();const i={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:r};let o=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((r,i)=>{do{if(!e){r(!1);break}if(!t){r(!1);break}if(!n){r(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){r(!(!this.apis||!this.apis[n]));break}let i=this.generateId();const o={action:"check_client_function_exist",method:n,rid:i,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let s=(e,t)=>{r(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalCallRemoteClientFunction(e,t,n,...r){return new Promise((i,o)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"remote client alias is not specifed"]);break}if(!n){i([null,"funcName is not specifed"]);break}let o=(e,t)=>{e?(a.information("callRemoteClientFunction",e.message),i([null,e])):i([t,void 0])};if(r)for(let e=0;e<r.length;e++)r[e]=this.convertFunction2IdEx(r[e]);let s=this.generateId();const l={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:r,rid:s};this.retCallbackMap[s]=Object.assign({callback:o},l),this.sendAdapter(e,l)}while(0)})}internalAttachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(r)||(this.eventCallbackMaps[r]={}),this.eventCallbackMaps[r].hasOwnProperty(t)||(this.eventCallbackMaps[r][t]={}),s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let i=this.generateId();return this.eventCallbackMaps[r][t][i]=n,i}internalDetachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;delete this.eventCallbackMaps[r][t][n],s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;let e=this.eventCallbackMaps[r][t];for(let t in e){let r=e[t];r&&r.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...r){let i=void 0,o=!1;do{const s=this.contextCallbackMap[t];if(!s)break;let a=void 0,l=void 0;try{a=s.apply(null,...r)}catch(e){l=e.message;break}if(void 0===n||null===n)break;if(i={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==l){i.error=l;break}if(a&&a.then){a.then(t=>{i.data=this.convertFunction2Id(t),i.success=!0,this.sendAdapter(e,i)}).catch(t=>{i.error=t instanceof Error?t.message:t,this.sendAdapter(e,i)}),o=!0;break}i.success=!0,i.data=this.convertFunction2Id(a)}while(0);!o&&i&&this.sendAdapter(e,i)}convertFunction2Id(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t=n}else if(e&&"object"==typeof e)for(let t in e){let n=e[t];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,e[t]=r}else n&&"object"==typeof n&&(e[t]=this.convertFunction2Id(n))}return t}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e)for(let t in e){let n=e[t];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,e[t]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":r}}else n&&"object"==typeof n&&(e[t]=this.convertFunction2IdEx(n))}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if("object"!=typeof e)break;let r=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(r){n=((...e)=>{this.callRemoteContextById(t,r,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:r},l);break}for(let n in e){let r=e[n];e[n]=this.decodeParameter(r,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,r=!1;do{if(!t)break;let i=t.method;if(!i)break;let o=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(o):[].concat(o)},!this.apis||!this.apis[i]){n.error=`callRegisterFunction ${i} is undefined`;break}let s=void 0,a=this.decodeParameter(t.args,t.src);try{s=this.apis[i].apply(null,[t.src].concat(a))}catch(e){n.error=e.message;break}if(s&&s.then){s.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),r=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(s)}while(0);a.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(e,n)}}),c=global.__xdasIPCClienInstance,t.client=c},21:function(e,t){e.exports=require("../vendor.js")},22:function(e,t,n){"use strict";var r=n(6),i=n(68),o={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a,l={adapter:("undefined"!=typeof XMLHttpRequest?a=n(23):"undefined"!=typeof process&&(a=n(23)),a),transformRequest:[function(e,t){return i(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(s(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};l.headers={common:{Accept:"application/json, text/plain, */*"}},r.forEach(["delete","get","head"],function(e){l.headers[e]={}}),r.forEach(["post","put","patch"],function(e){l.headers[e]=r.merge(o)}),e.exports=l},23:function(e,t,n){"use strict";var r=n(6),i=n(69),o=n(71),s=n(72),a=n(73),l=n(40),c="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(74);e.exports=function(e){return new Promise(function(t,u){var d=e.data,f=e.headers;r.isFormData(d)&&delete f["Content-Type"];var p=new XMLHttpRequest,h="onreadystatechange",y=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in p||a(e.url)||(p=new window.XDomainRequest,h="onload",y=!0,p.onprogress=function(){},p.ontimeout=function(){}),e.auth){var m=e.auth.username||"",v=e.auth.password||"";f.Authorization="Basic "+c(m+":"+v)}if(p.open(e.method.toUpperCase(),o(e.url,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,p[h]=function(){if(p&&(4===p.readyState||y)&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in p?s(p.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?p.response:p.responseText,status:1223===p.status?204:p.status,statusText:1223===p.status?"No Content":p.statusText,headers:n,config:e,request:p};i(t,u,r),p=null}},p.onerror=function(){u(l("Network Error",e,null,p)),p=null},p.ontimeout=function(){u(l("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",p)),p=null},r.isStandardBrowserEnv()){var g=n(75),b=(e.withCredentials||a(e.url))&&e.xsrfCookieName?g.read(e.xsrfCookieName):void 0;b&&(f[e.xsrfHeaderName]=b)}if("setRequestHeader"in p&&r.forEach(f,function(e,t){void 0===d&&"content-type"===t.toLowerCase()?delete f[t]:p.setRequestHeader(t,e)}),e.withCredentials&&(p.withCredentials=!0),e.responseType)try{p.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){p&&(p.abort(),u(e),p=null)}),void 0===d&&(d=null),p.send(d)})}},26:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(27),o=n(11),s=n(35).promisify,a=n(0).default.getLogger("fs-utilities");!function(e){function t(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.access);try{yield n(e),t=!0}catch(e){a.info(e)}}return t})}function l(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.mkdir);try{yield n(e),t=!0}catch(e){a.warn(e)}}return t})}function c(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.rmdir);try{yield n(e),t=!0}catch(e){a.warn(e)}}return t})}function u(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.unlink);try{yield n(e),t=!0}catch(e){a.warn(e)}}return t})}function d(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.readdir);try{t=yield n(e)}catch(e){a.warn(e)}}return t})}function f(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.lstat);try{t=yield n(e)}catch(e){a.warn(e)}}return t})}function p(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let r=o.join(e,t),i=yield f(r);n=null!==i&&i.isDirectory()?yield h(r):yield u(r)}return n})}function h(e){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e){if(yield t(e)){n=!0;let t=yield d(e);for(let r=0;r<t.length;r++)n=(yield p(e,t[r]))&&n;(n||0===t.length)&&(n=(yield c(e))&&n)}}return n})}function y(e){return r(this,void 0,void 0,function*(){let n=!1;return a.info("mkdirsAW",e),void 0!==e&&((yield t(e))?n=!0:o.dirname(e)===e?n=!1:(yield y(o.dirname(e)))&&(n=yield l(e))),n})}function m(e,n){return r(this,void 0,void 0,function*(){let r;if(e.toLowerCase()!==n.toLowerCase()&&(yield t(e))){let t=i.createReadStream(e),o=i.createWriteStream(n);r=new Promise(e=>{t.pipe(o).on("finish",()=>{e(!0)})})}else r=new Promise(e=>{e(!1)});return r})}e.readFileAW=function(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.readFile);try{t=yield n(e)}catch(e){a.warn(e)}}return t})},e.writeFileAW=function(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const r=s(i.writeFile);try{yield r(e,t),n=!0}catch(e){a.warn(e)}}return n})},e.existsAW=t,e.mkdirAW=l,e.rmdirAW=c,e.unlinkAW=u,e.readdirAW=d,e.lstatAW=f,e.rmdirsAW=h,e.mkdirsAW=y,e.renameAW=function(e,t){return r(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=s(i.rename);try{yield n(e,t)}catch(e){a.warn(e)}}})},e.copyFileAW=m,e.copyDirsAW=function e(n,i){return r(this,void 0,void 0,function*(){let r=!1,s=yield f(n);if(s.isDirectory()){r=yield y(i);let a=yield d(n);for(let l=0;l<a.length;l++){let c=o.join(n,a[l]),u=o.join(i,a[l]);(r=yield t(c))&&(r=(s=yield f(c)).isDirectory()?yield e(c,u):yield m(c,u))}}return r})},e.mkdtempAW=function(){return r(this,void 0,void 0,function*(){let e=!1;const t=s(i.mkdtemp),r=(yield Promise.resolve().then(()=>n(36))).tmpdir();try{e=yield t(`${r}${o.sep}`)}catch(e){a.warn(e)}return e})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},27:function(e,t){e.exports=require("fs")},28:function(e,t){e.exports=require("electron")},291:function(e,t,n){e.exports=n(292)},292:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(1),i=r.default.pluginName+"-tryTips";n(2).client.start({name:i,version:r.default.pluginVersion},"thunder"),n(53),n(0).default.getLogger("VipDownload:try-tips-renderer").info("init");const o=n(89),s=n(293);new o.default({components:{App:s.default},render:e=>e("app")}).$mount("#app")},293:function(e,t,n){"use strict";n.r(t);var r=n(170),i=n(126);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);n(296);var s=n(4),a=Object(s.a)(i.default,r.a,r.b,!1,null,null,null);a.options.__file="src/try-tips-renderer/app.vue",t.default=a.exports},294:function(e,t){e.exports="data:image/png;base64,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**************************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"},295:function(e,t){e.exports="data:image/png;base64,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*********************************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"},296:function(e,t,n){"use strict";var r=n(363);n.n(r).a},30:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(36),i=n(11);t.getDefaultPrex=function(){return i.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=i.basename(process.execPath,".exe"));let o=i.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(o="\\\\.\\pipe\\"+(o=(o=o.replace(/^\//,"")).replace(/\//g,"-"))),o},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},32:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const r=n(11);let i,o=console;function s(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,i=(new Error).stack.split("\n").slice(e+1);i.shift();let o=[];return i.forEach((e,i)=>{let s=t.exec(e)||n.exec(e),a={};s&&5===s.length&&(a.method=s[1],a.path=s[2],a.line=s[3],a.pos=s[4],a.file=r.basename(a.path),o.push(a))}),o}if(i="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return s(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){o.time(...e)},t.timeEnd=function(...e){o.timeEnd(...e)},t.trace=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.trace(i,...e)},t.info=function(...e){let t=s(),n="anonymous";t[0]&&t[0].method&&(n=n),o.info(i,"["+n+"]",e.join(","))},t.warn=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.warn("<WARN>"+i,"["+n+"]",e.join(","))},t.error=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.error("<ERROR>"+i,"["+n+"]",e.join(","))},t.log=function(...e){o.log(i,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},35:function(e,t){e.exports=require("util")},36:function(e,t){e.exports=require("os")},363:function(e,t){},37:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},38:function(e,t,n){e.exports=n(65)},39:function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},4:function(e,t,n){"use strict";function r(e,t,n,r,i,o,s,a){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),o&&(c._scopeId="data-v-"+o),s?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},c._ssrRegister=l):i&&(l=a?function(){i.call(this,this.$root.$options.shadowRoot)}:i),l)if(c.functional){c._injectStyles=l;var u=c.render;c.render=function(e,t){return l.call(t),u(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}n.d(t,"a",function(){return r})},40:function(e,t,n){"use strict";var r=n(70);e.exports=function(e,t,n,i,o){var s=new Error(e);return r(s,t,n,i,o)}},41:function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},42:function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},48:function(e,t){e.exports=require("buffer")},5:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(17),o=n(0).default.getLogger("Thunder.Util"),s=n(11);function a(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(e){let t=null;do{if(void 0===e||null===e)break;t=e.match(/[\/]?([^?]*)\?([^\s]*)/)?RegExp.$2:""}while(0);return t}function n(e){let t={};do{if(void 0===e||null===e)break;let n=/([^&=?]+)=([^&]*)/g;for(;n.exec(e);)t[RegExp.$1]=RegExp.$2}while(0);return t}function l(e){return n(t(e))}function c(e){let t=null;do{if(void 0===e||null===e)break;t=e.match(/[\/]?([^?]*)\?([^\s]*)/)?RegExp.$1:e}while(0);return t}e.formatSize=function(e,t){t=t||2;let n="0B";if("number"==typeof e&&e>0){let r=["B","KB","MB","GB","TB"],i=0,o=e;for(;o>=1e3&&!(i>=4);)o/=1024,i+=1;n=-1===String(o).indexOf(".")?o+r[i]:o.toFixed(t)+r[i]}return n},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){o.warn(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",r=!1,i=[],o=e.length;for(let s=0;s<o;s++){let l=e[s];if('"'!==l&&"'"!==l||(""===n?(r=!0,n=l):n===l&&(r=!1,n=""))," "!==l||r||s===o-1){if(s===o-1){let n=e.substring(t);""!==n.trim()&&i.push(a(n))}}else{let n=e.substring(t,s);""!==n.trim()&&i.push(a(n)),t=s+1}}return i},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,r)=>{e+=0===r?"?":"&",e+=`${n}=${t[n]}`}),e},e.getQueryString=function(e,t){return e&&t&&e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:""},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),r=t.split("."),i=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(r[e])>0){i=1;break}if(Number(n[e])-Number(r[e])<0){i=-1;break}}return i},e.parseDynamicUrlPath=t,e.parseDynamicUrlArgs=n,e.getUrlArgs=l,e.sleep=function(e){return r(this,void 0,void 0,function*(){return new Promise(t=>{setTimeout(()=>{t()},e)})})},e.getStaticPath=function(){let e=s.join(__rootDir,"static").replace("\\","/");for(;-1!==e.indexOf("\\");)e=e.replace("\\","/");return e},e.genarateMd5=function(e){let t=void 0,n=i.createHash("md5");return null!==n&&(t=n.update(e).digest("hex")),t},e.GetUrlHost=c,e.RepleaseUrlArgs=function(e,t){let n=Object.getOwnPropertyNames(e),r=l(t);n.forEach(t=>{e[t]&&(r[t]=e[t])}),n=Object.getOwnPropertyNames(r);let i=c(t);return n.forEach(e=>{i.indexOf("?")>0?i+=`&${e}=${r[e]}`:i+=`?${e}=${r[e]}`}),i}}(t.ThunderUtil||(t.ThunderUtil={}))},52:function(e,t){e.exports=require("string_decoder")},53:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(27),i=n(11),o=n(0);let s=i.join(__rootDir,"log-options.json");if(r.existsSync(s)){const e={label:__processName,options:s};o.default.start(e)}},57:function(e){e.exports={name:"vip-download",version:"4.8.0",author:"Xunlei",license:"",description:"",main:"4.8.0/index.js",clear:!0}},58:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(59),i=n(8),o=n(37),s=n(60),a=n(30);t.Client=class extends i.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=a.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new s.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{o.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){o.error(e.message)}else o.information("This socket has been ended by the other party")}}},59:function(e,t){e.exports=require("net")},6:function(e,t,n){"use strict";var r=n(39),i=n(66),o=Object.prototype.toString;function s(e){return"[object Array]"===o.call(e)}function a(e){return null!==e&&"object"==typeof e}function l(e){return"[object Function]"===o.call(e)}function c(e,t){if(null!==e&&void 0!==e)if("object"!=typeof e&&(e=[e]),s(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:s,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:i,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:l,isStream:function(e){return a(e)&&l(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:c,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,i=arguments.length;r<i;r++)c(arguments[r],n);return t},extend:function(e,t,n){return c(t,function(t,i){e[i]=n&&"function"==typeof t?r(t,n):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},60:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(52),i=n(8);t.Parser=class extends i.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),i=JSON.parse(e);this.emit("message",i),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},61:function(e,t){e.exports=require("url")},62:function(e,t){e.exports=require("http")},63:function(e,t){e.exports=require("https")},64:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(0).default.getLogger("VipDownload:config");t.Config=class{constructor(){this.configData={},this.configData={VipDownload:{TokenExpireAdvanceSecond:300,TokenExpireMinSecond:20,TokenDefaultQueryInterval:300,EnableTryMinSize:209715200,EnableTryMaxProgress:40,FileEnableTryMinSize:52428800,TryInterval:1800,TryMaxProgress:20,TryMaxSize:1073741824,TryFailDispearDelay:5,TryFinishDispearDelay:1800,TryFinishClickDispearDelay:180,NewSkinPeerid:[],WarnStylePeerid:[],BeforeBaotuanXgtStylePeerid:["A"],EnableSuperTryMinSize:524288e3,EnableSuperTryMaxProgress:50,SuperTryFinishDispearDelay:180,SuperTryFinishClickDispearDelay:10,MixTryFinishMiniWeb:2,MixTryFinishMiniWebMinPromotePercent:5,AdFinishDispearDelay:180,SceneChangeInterval:10,WDYXDomains:["lx.patch1.9you.com"],PlayGameXgtCount:6,PlayGameHash:!1,SpeedZeroLimitSpeed:1,AuotShowBaotuanNoviceDelay:60,NoVipStatusQueryPeerid:["0","1"],ReportGlobalSpeed:!0,ReportGlobalSpeedTime:60}}}getValue(e,t){let n=void 0;do{if(!e||""===e)break;if(!t||""===t)break;let r=this.configData[e];if(!r)break;n=r[t]}while(0);return n}loadConfigData(e){return r(this,void 0,void 0,function*(){let t=!1,n=null;try{(n=JSON.parse(e.toString()))&&(t=!0)}catch(e){i.warn(e)}return this.mergeConfigData(n),t})}printConfigData(){i.info("configData",this.configData)}mergeConfigData(e){if(e)if(null===this.configData)this.configData=e;else for(let t in e){let n=e[t];if(!n)break;for(let e in n){let r=n[e],i=this.configData[t];if(i)i[e]=r;else{let n={};n[e]=r,this.configData[t]=n}}}}}},65:function(e,t,n){"use strict";var r=n(6),i=n(39),o=n(67),s=n(22);function a(e){var t=new o(e),n=i(o.prototype.request,t);return r.extend(n,o.prototype,t),r.extend(n,t),n}var l=a(s);l.Axios=o,l.create=function(e){return a(r.merge(s,e))},l.Cancel=n(42),l.CancelToken=n(81),l.isCancel=n(41),l.all=function(e){return Promise.all(e)},l.spread=n(82),e.exports=l,e.exports.default=l},66:function(e,t){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},67:function(e,t,n){"use strict";var r=n(22),i=n(6),o=n(76),s=n(77);function a(e){this.defaults=e,this.interceptors={request:new o,response:new o}}a.prototype.request=function(e){"string"==typeof e&&(e=i.merge({url:arguments[0]},arguments[1])),(e=i.merge(r,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var t=[s,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},i.forEach(["delete","get","head","options"],function(e){a.prototype[e]=function(t,n){return this.request(i.merge(n||{},{method:e,url:t}))}}),i.forEach(["post","put","patch"],function(e){a.prototype[e]=function(t,n,r){return this.request(i.merge(r||{},{method:e,url:t,data:n}))}}),e.exports=a},68:function(e,t,n){"use strict";var r=n(6);e.exports=function(e,t){r.forEach(e,function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])})}},69:function(e,t,n){"use strict";var r=n(40);e.exports=function(e,t,n){var i=n.config.validateStatus;n.status&&i&&!i(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},7:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n,r,i,o,s,a,l,c,u,d,f,p,h,y,m,v,g,b;!function(e){e[e.Unkown=0]="Unkown",e[e.Create=1]="Create",e[e.InvaldParam=2]="InvaldParam",e[e.InvaldLink=3]="InvaldLink",e[e.InvaldConfig=4]="InvaldConfig",e[e.Timeout=5]="Timeout",e[e.VerifyData=6]="VerifyData",e[e.Forbidden=7]="Forbidden",e[e.RangeDispatch=8]="RangeDispatch",e[e.FilePathOverRanging=9]="FilePathOverRanging",e[e.FileCreate=201]="FileCreate",e[e.FileWrite=202]="FileWrite",e[e.FileRead=203]="FileRead",e[e.FileRename=204]="FileRename",e[e.FileFull=205]="FileFull",e[e.FileOccupied=211]="FileOccupied",e[e.FileAccessDenied=212]="FileAccessDenied",e[e.BtUploadExist=601]="BtUploadExist",e[e.ForbinddenResource=701]="ForbinddenResource",e[e.ForbinddenAccount=702]="ForbinddenAccount",e[e.ForbinddenArea=703]="ForbinddenArea",e[e.ForbinddenCopyright=704]="ForbinddenCopyright",e[e.ForbinddenReaction=705]="ForbinddenReaction",e[e.ForbinddenPorn=706]="ForbinddenPorn",e[e.DownloadSDKCrash=10001]="DownloadSDKCrash",e[e.torrentFileNotExist=10002]="torrentFileNotExist"}(t=e.TaskError||(e.TaskError={})),function(e){e[e.Unkown=-1]="Unkown",e[e.Success=0]="Success",e[e.QueryFailed=1]="QueryFailed",e[e.ServerError=2]="ServerError",e[e.ResourceNotFound=3]="ResourceNotFound",e[e.AuthorizingFailed=4]="AuthorizingFailed",e[e.ForbidByCopyright=5]="ForbidByCopyright",e[e.ForbidByPorNoGraphy=6]="ForbidByPorNoGraphy",e[e.ForbidByReactionary=7]="ForbidByReactionary",e[e.ForbidByOtherFilter=8]="ForbidByOtherFilter"}(n=e.DcdnStatusCode||(e.DcdnStatusCode={})),function(e){e[e.Begin=-1]="Begin",e[e.Unkown=0]="Unkown",e[e.StandBy=1]="StandBy",e[e.PreDownloading=2]="PreDownloading",e[e.StartWaiting=3]="StartWaiting",e[e.StartPending=4]="StartPending",e[e.Started=5]="Started",e[e.StopPending=6]="StopPending",e[e.Stopped=7]="Stopped",e[e.Succeeded=8]="Succeeded",e[e.Failed=9]="Failed",e[e.Seeding=10]="Seeding",e[e.DestroyPending=11]="DestroyPending",e[e.End=12]="End"}(r=e.TaskStatus||(e.TaskStatus={})),function(e){e[e.Begin=-1]="Begin",e[e.StandBy=0]="StandBy",e[e.Stopped=1]="Stopped",e[e.Started=2]="Started",e[e.Complete=3]="Complete",e[e.Forbidden=4]="Forbidden",e[e.Error=5]="Error",e[e.End=6]="End"}(i=e.BtFileStatus||(e.BtFileStatus={})),function(e){e[e.DispatchStrategyNone=0]="DispatchStrategyNone",e[e.DispatchStrategyOrigin=1]="DispatchStrategyOrigin",e[e.DispatchStrategyP2s=2]="DispatchStrategyP2s",e[e.DispatchStrategyP2p=4]="DispatchStrategyP2p",e[e.DispatchStrategyAll=-1]="DispatchStrategyAll"}(o=e.DispatchStrategy||(e.DispatchStrategy={})),function(e){e[e.Unkown=0]="Unkown",e[e.P2sp=1]="P2sp",e[e.Bt=2]="Bt",e[e.Emule=3]="Emule",e[e.Group=4]="Group",e[e.Magnet=5]="Magnet"}(s=e.TaskType||(e.TaskType={})),function(e){e.Unkown="Unkown",e.Downloading="Downloading",e.Completed="Completed",e.Recycle="Recycle"}(a=e.CategroyViewID||(e.CategroyViewID={})),function(e){e[e.Unknow=0]="Unknow",e[e.TaskCreated=1]="TaskCreated",e[e.InsertToCategoryView=2]="InsertToCategoryView",e[e.RemoveFromCategoryView=3]="RemoveFromCategoryView",e[e.StatusChanged=4]="StatusChanged",e[e.DetailChanged=5]="DetailChanged",e[e.ChannelInfoChanged=6]="ChannelInfoChanged",e[e.DcdnStatusChanged=7]="DcdnStatusChanged",e[e.TaskDescriptionChanged=8]="TaskDescriptionChanged",e[e.TaskUserRead=9]="TaskUserRead",e[e.TaskRenamed=10]="TaskRenamed",e[e.TaskMovedEnd=11]="TaskMovedEnd",e[e.TaskMovingStateChange=12]="TaskMovingStateChange",e[e.BtSubFileDetailChanged=13]="BtSubFileDetailChanged",e[e.BtSubFileLoaded=14]="BtSubFileLoaded",e[e.BtSubFileForbidden=15]="BtSubFileForbidden",e[e.BtSubFileDcdnStatusChanged=16]="BtSubFileDcdnStatusChanged",e[e.TaskCategoryMovedEnd=17]="TaskCategoryMovedEnd",e[e.GroupTaskSubFileDetailChanged=18]="GroupTaskSubFileDetailChanged",e[e.TaskDestroying=19]="TaskDestroying",e[e.TaskDestroyed=20]="TaskDestroyed"}(l=e.TaskEventType||(e.TaskEventType={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.VirtualId=2]="VirtualId",e[e.SrcTotal=3]="SrcTotal",e[e.SrcUsing=4]="SrcUsing",e[e.FileSize=5]="FileSize",e[e.ReceivedSize=6]="ReceivedSize",e[e.DownloadSize=7]="DownloadSize",e[e.TotalDownloadSize=8]="TotalDownloadSize",e[e.CreateTime=9]="CreateTime",e[e.CompletionTime=10]="CompletionTime",e[e.DownloadingPeriod=11]="DownloadingPeriod",e[e.Progress=12]="Progress",e[e.RecycleTime=13]="RecycleTime",e[e.FileCreated=14]="FileCreated",e[e.Forbidden=15]="Forbidden",e[e.CategoryId=16]="CategoryId",e[e.UserRead=17]="UserRead",e[e.OpenOnComplete=18]="OpenOnComplete",e[e.GroupTaskId=19]="GroupTaskId",e[e.DownloadSubTask=20]="DownloadSubTask",e[e.NameType=21]="NameType",e[e.OwnerProduct=22]="OwnerProduct",e[e.FileIndex=23]="FileIndex",e[e.NameFixed=24]="NameFixed",e[e.ValidDownloadSize=25]="ValidDownloadSize",e[e.RealDownloadSize=26]="RealDownloadSize",e[e.ResourceLegal=27]="ResourceLegal",e[e.TaskType=28]="TaskType",e[e.ErrorCode=29]="ErrorCode",e[e.NumberEnd=30]="NumberEnd",e[e.BooleanStart=4096]="BooleanStart",e[e.Destroyed=4097]="Destroyed",e[e.Background=4098]="Background",e[e.Moving=4099]="Moving",e[e.BooleanEnd=4100]="BooleanEnd",e[e.StringStart=8192]="StringStart",e[e.TaskName=8193]="TaskName",e[e.SavePath=8194]="SavePath",e[e.RelativePath=8195]="RelativePath",e[e.TaskUrl=8196]="TaskUrl",e[e.RefUrl=8197]="RefUrl",e[e.Cid=8198]="Cid",e[e.Gcid=8199]="Gcid",e[e.Cookie=8200]="Cookie",e[e.ProductInfo=8201]="ProductInfo",e[e.Origin=8202]="Origin",e[e.Description=8203]="Description",e[e.UserData=8204]="UserData",e[e.OriginName=8205]="OriginName",e[e.HTTPContentType=8206]="HTTPContentType",e[e.CategoryViewId=8207]="CategoryViewId",e[e.StringEnd=8208]="StringEnd",e[e.ObjectStart=12288]="ObjectStart",e[e.ObjectEnd=12289]="ObjectEnd"}(c=e.TaskAttribute||(e.TaskAttribute={})),function(e){e[e.UnKnown=0]="UnKnown",e[e.SrcTotal=1]="SrcTotal",e[e.SrcUsing=2]="SrcUsing",e[e.FileSize=4]="FileSize",e[e.ReceivedSize=8]="ReceivedSize",e[e.DownloadSize=16]="DownloadSize",e[e.CompletionTime=32]="CompletionTime",e[e.DownloadingPeriod=64]="DownloadingPeriod",e[e.Progress=128]="Progress",e[e.RecycleTime=256]="RecycleTime",e[e.FileCreated=512]="FileCreated",e[e.Forbidden=1024]="Forbidden",e[e.UserRead=2048]="UserRead",e[e.OpenOnComplete=4096]="OpenOnComplete",e[e.DownloadSubTask=8192]="DownloadSubTask",e[e.TaskName=16384]="TaskName",e[e.SavePath=32768]="SavePath",e[e.Cid=65536]="Cid",e[e.Gcid=131072]="Gcid",e[e.UserData=262144]="UserData",e[e.CategoryViewId=524288]="CategoryViewId",e[e.ErrorCode=1048576]="ErrorCode",e[e.TaskSpeed=2097152]="TaskSpeed",e[e.ChannelInfo=4194304]="ChannelInfo",e[e.ValidDownloadSize=8388608]="ValidDownloadSize",e[e.OriginName=16777216]="OriginName",e[e.HTTPContentType=33554432]="HTTPContentType"}(u=e.TaskDetailChangedFlags||(e.TaskDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(d=e.BtSubFileDetailChangedFlags||(e.BtSubFileDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(f=e.GroupTaskSubFileDetailChangedFlags||(e.GroupTaskSubFileDetailChangedFlags={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.FileStatus=2]="FileStatus",e[e.DownloadSize=3]="DownloadSize",e[e.FileSize=4]="FileSize",e[e.EnableDcdn=5]="EnableDcdn",e[e.ErrorCode=6]="ErrorCode",e[e.DcdnStatus=7]="DcdnStatus",e[e.RealIndex=8]="RealIndex",e[e.FileOffset=9]="FileOffset",e[e.Visible=10]="Visible",e[e.Download=11]="Download",e[e.NumberEnd=12]="NumberEnd",e[e.StringStart=13]="StringStart",e[e.FinalName=14]="FinalName",e[e.RelativePath=15]="RelativePath",e[e.FileName=16]="FileName",e[e.FilePath=17]="FilePath",e[e.Cid=18]="Cid",e[e.Gcid=19]="Gcid",e[e.UserRead=20]="UserRead",e[e.StringEnd=21]="StringEnd"}(p=e.BtFileAttribute||(e.BtFileAttribute={})),function(e){e[e.P2spUrl=0]="P2spUrl",e[e.BtInfoId=1]="BtInfoId",e[e.EmuleFileHash=2]="EmuleFileHash",e[e.MagnetUrl=3]="MagnetUrl",e[e.GroupTaskName=4]="GroupTaskName"}(h=e.KeyType||(e.KeyType={})),function(e){e[e.NameInclude=1]="NameInclude",e[e.BtDisplayNameInclude=2]="BtDisplayNameInclude"}(y=e.SearchKeyType||(e.SearchKeyType={})),function(e){e[e.ExtEqual=1]="ExtEqual",e[e.NameLikeAndExtEqual=2]="NameLikeAndExtEqual",e[e.TaskTypeEqual=4]="TaskTypeEqual"}(m=e.FilterKeyType||(e.FilterKeyType={})),function(e){e[e.All=0]="All",e[e.CommonForeground=1]="CommonForeground",e[e.CommonBackground=2]="CommonBackground",e[e.Temporary=3]="Temporary",e[e.PreDownload=4]="PreDownload",e[e.PrivateForeground=5]="PrivateForeground"}(v=e.KeyFilter||(e.KeyFilter={})),function(e){e[e.Unknown=-1]="Unknown",e[e.LoadTaskBasic=0]="LoadTaskBasic",e[e.Create=1]="Create",e[e.Recycle=2]="Recycle",e[e.Recover=3]="Recover",e[e.ReDownload=4]="ReDownload",e[e.MoveThoughCategory=5]="MoveThoughCategory"}(g=e.TaskInsertReason||(e.TaskInsertReason={})),function(e){e[e.Unknown=-1]="Unknown",e[e.ContextMenu=0]="ContextMenu",e[e.Button=1]="Button",e[e.TaskDetail=2]="TaskDetail",e[e.DownloadMagnet=3]="DownloadMagnet",e[e.ToolbarButton=4]="ToolbarButton",e[e.SelectDownloadLists=5]="SelectDownloadLists",e[e.DeleteTask=6]="DeleteTask"}(b=e.TaskStopReason||(e.TaskStopReason={}))}(t.DownloadKernel||(t.DownloadKernel={}))},70:function(e,t,n){"use strict";e.exports=function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e}},71:function(e,t,n){"use strict";var r=n(6);function i(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var o;if(n)o=n(t);else if(r.isURLSearchParams(t))o=t.toString();else{var s=[];r.forEach(t,function(e,t){null!==e&&void 0!==e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),s.push(i(t)+"="+i(e))}))}),o=s.join("&")}return o&&(e+=(-1===e.indexOf("?")?"?":"&")+o),e}},72:function(e,t,n){"use strict";var r=n(6),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,o,s={};return e?(r.forEach(e.split("\n"),function(e){if(o=e.indexOf(":"),t=r.trim(e.substr(0,o)).toLowerCase(),n=r.trim(e.substr(o+1)),t){if(s[t]&&i.indexOf(t)>=0)return;s[t]="set-cookie"===t?(s[t]?s[t]:[]).concat([n]):s[t]?s[t]+", "+n:n}}),s):s}},73:function(e,t,n){"use strict";var r=n(6);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function i(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=i(window.location.href),function(t){var n=r.isString(t)?i(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},74:function(e,t,n){"use strict";var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function i(){this.message="String contains an invalid character"}i.prototype=new Error,i.prototype.code=5,i.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,n,o=String(e),s="",a=0,l=r;o.charAt(0|a)||(l="=",a%1);s+=l.charAt(63&t>>8-a%1*8)){if((n=o.charCodeAt(a+=.75))>255)throw new i;t=t<<8|n}return s}},75:function(e,t,n){"use strict";var r=n(6);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,i,o,s){var a=[];a.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),r.isString(i)&&a.push("path="+i),r.isString(o)&&a.push("domain="+o),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},76:function(e,t,n){"use strict";var r=n(6);function i(){this.handlers=[]}i.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},i.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},i.prototype.forEach=function(e){r.forEach(this.handlers,function(t){null!==t&&e(t)})},e.exports=i},77:function(e,t,n){"use strict";var r=n(6),i=n(78),o=n(41),s=n(22),a=n(79),l=n(80);function c(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return c(e),e.baseURL&&!a(e.url)&&(e.url=l(e.baseURL,e.url)),e.headers=e.headers||{},e.data=i(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||s.adapter)(e).then(function(t){return c(e),t.data=i(t.data,t.headers,e.transformResponse),t},function(t){return o(t)||(c(e),t&&t.response&&(t.response.data=i(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},78:function(e,t,n){"use strict";var r=n(6);e.exports=function(e,t,n){return r.forEach(n,function(n){e=n(e,t)}),e}},79:function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},8:function(e,t){e.exports=require("events")},80:function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},81:function(e,t,n){"use strict";var r=n(42);function i(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new r(e),t(n.reason))})}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var e;return{token:new i(function(t){e=t}),cancel:e}},e.exports=i},82:function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},85:function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(r||(r={})),t.default=r},86:function(e,t,n){"use strict";var r;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(r||(r={})),e.exports=r},89:function(e,t,n){e.exports=n(21)(19)},94:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(0).default.getLogger("async-remote-call"),o=n(157),s=n(8),a=n(35);t.asyncRemoteCall=new class extends s.EventEmitter{constructor(){super(),this.mapObj=new Map,this.mapObjIniting=new Map,"renderer"!==process.type&&i.error('can not import "renderer-process-call" module in non-renderer process',process.type)}getAppName(){return r(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}getAppVersion(){return r(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return r(this,void 0,void 0,function*(){return o.global.process.__resolve()})}getIpcMain(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getBrowserWindow(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getGlobalShortcut(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield o.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emit("OnInitCurrentWebContents",e),this.listeners("OnInitCurrentWebContents").forEach(e=>{this.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield o.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emit("OnInitCurrentWindow",e),this.listeners("OnInitCurrentWindow").forEach(e=>{this.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return r(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return a.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>r(this,void 0,void 0,function*(){this.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield o.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emit(e,t),this.listeners(e).forEach(t=>{this.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}}});
//# sourceMappingURL=renderer.js.map