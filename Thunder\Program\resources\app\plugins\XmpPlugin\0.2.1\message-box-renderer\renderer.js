module.exports=function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(i,r,function(t){return e[t]}.bind(null,r));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=254)}([function(e,t,n){e.exports=n(29)(21)},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){e.done?r(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const r=n(11),o=n(96),s=n(20),a=n(39);function l(e){a.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let c=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends r.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,i){do{if(t||(t=s.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(i)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))i("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{i("error",e)}),e.on("connect",()=>{i("connect")}),e.on("end",()=>{let t=e.isInprocess();i("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===s.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let r=new o.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=r,n||(this.client=r),r.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(r,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(r,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(r,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(r,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===r&&this.emit("stat_call_function_back",r.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let i=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,i));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),r.on("error",e=>{i&&i("error",e),this.emit("socket-error",e,r.getContext(),n,r.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),r.isInprocess()?(this.connectedMap[t.toLowerCase()]=r,i&&i("connect"),this.emit("connect",r.getContext(),n,!0)):r.on("connect",()=>{this.connectedMap[t.toLowerCase()]=r,i&&i("connect"),this.emit("connect",r.getContext(),n,!1)}),r.on("end",()=>{let e=r.isInprocess();a.information("server is ended, and this client emit end",t,n,e),i&&i("end",r.getContext(),n,e),this.emit("end",r.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(r)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return i(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return i(this,void 0,void 0,function*(){let n=null,i=yield this.callServerFunctionEx(e,...t);return i&&(n=i[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return console.info(this.client.getContext().name,"attachServerEvent",e),this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return i(this,void 0,void 0,function*(){let i=null,r=yield this.crossCallServerFunctionEx(e,t,...n);return r&&(i=r[0]),i})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(i,t,...n)}}crossIsRemoteClientExist(e,t){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(i,t,n)}})}crossCallRemoteClientFunction(e,t,n,...i){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(r,t,n,...i)}}crossAttachServerEvent(e,t,n){let i=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");i=this.internalAttachServerEvent(r,t,n)}return i}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(i,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(i,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,i)=>{do{if(!t){n(!1);break}let i=this.generateId();const r={alias:t,action:"register",rid:i};let o=(e,i)=>{e?(a.error("register error",e.message),n(i)):n(t)};this.retCallbackMap[i]=Object.assign({callback:o},r),this.sendAdapter(e,r)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(a.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===s.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,i)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let i=this.generateId();const r={action:"check_server_function_exist",method:t,rid:i};let o=(e,t)=>{n(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:o},r),this.sendAdapter(e,r)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((i,r)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let r=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const o={rid:r,method:t,args:n};let s=(t,n)=>{t?(a.error("callServerFunction error",t,e.getContext()),i([null,t])):i([n,void 0])};this.retCallbackMap[r]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,i)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let i=this.generateId();const r={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:i};let o=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[i]=Object.assign({callback:o},r),this.sendAdapter(e,r)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((i,r)=>{do{if(!e){i(!1);break}if(!t){i(!1);break}if(!n){i(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){i(!(!this.apis||!this.apis[n]));break}let r=this.generateId();const o={action:"check_client_function_exist",method:n,rid:r,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let s=(e,t)=>{i(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalCallRemoteClientFunction(e,t,n,...i){return new Promise((r,o)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"remote client alias is not specifed"]);break}if(!n){r([null,"funcName is not specifed"]);break}let o=(e,t)=>{e?(a.information("callRemoteClientFunction",e.message),r([null,e])):r([t,void 0])};if(i)for(let e=0;e<i.length;e++)i[e]=this.convertFunction2IdEx(i[e]);let s=this.generateId();const l={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:i,rid:s};this.retCallbackMap[s]=Object.assign({callback:o},l),this.sendAdapter(e,l)}while(0)})}internalAttachServerEvent(e,t,n){let i=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(i)||(this.eventCallbackMaps[i]={}),this.eventCallbackMaps[i].hasOwnProperty(t)||(this.eventCallbackMaps[i][t]={}),s.isObjectEmpty(this.eventCallbackMaps[i][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let r=this.generateId();return this.eventCallbackMaps[i][t][r]=n,r}internalDetachServerEvent(e,t,n){let i=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(i))break;if(!this.eventCallbackMaps[i].hasOwnProperty(t))break;delete this.eventCallbackMaps[i][t][n],s.isObjectEmpty(this.eventCallbackMaps[i][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let i=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(i))break;if(!this.eventCallbackMaps[i].hasOwnProperty(t))break;let e=this.eventCallbackMaps[i][t];for(let t in e){let i=e[t];i&&i.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...i){let r=void 0,o=!1;do{const s=this.contextCallbackMap[t];if(!s){a.error("the context function has been freeer",t),r={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let l=void 0,c=void 0;try{l=s.apply(null,...i)}catch(e){c=e.message;break}if(void 0===n||null===n)break;if(r={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==c){r.error=c;break}if(l&&l.then){l.then(t=>{r.data=this.convertFunction2IdEx(t),r.success=!0,this.sendAdapter(e,r)}).catch(t=>{r.error=t instanceof Error?t.message:t,this.sendAdapter(e,r)}),o=!0;break}r.success=!0,r.data=this.convertFunction2IdEx(l)}while(0);!o&&r&&this.sendAdapter(e,r)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let i=this.generateId();this.contextCallbackMap[i]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":i}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let i=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(i){n=((...e)=>{this.callRemoteContextById(t,i,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:i},l);break}for(let n in e){let i=e[n];e[n]=this.decodeParameter(i,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,i=!1;do{if(!t)break;let r=t.method;if(!r)break;let o=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(o):[].concat(o)},!this.apis||!this.apis[r]){n.error=`callRegisterFunction ${r} is undefined`;break}let s=void 0,a=this.decodeParameter(t.args,t.src);try{s=this.apis[r].apply(null,[t.src].concat(a))}catch(e){n.error=e.message;break}if(s&&s.then){s.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),i=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(s)}while(0);a.information("callRegisterFunction",n),!i&&n&&this.sendAdapter(e,n)}}),c=global.__xdasIPCClienInstance,t.client=c},function(e,t,n){"use strict";function i(e,t,n,i,r,o,s,a){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),i&&(c.functional=!0),o&&(c._scopeId="data-v-"+o),s?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),r&&r.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},c._ssrRegister=l):r&&(l=a?function(){r.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:r),l)if(c.functional){c._injectStyles=l;var u=c.render;c.render=function(e,t){return l.call(t),u(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}n.d(t,"a",function(){return i})},function(e,t){var n=e.exports={version:"2.6.11"};"number"==typeof __e&&(__e=n)},function(e,t,n){"use strict";n.d(t,"d",function(){return d}),n.d(t,"c",function(){return h}),n.d(t,"e",function(){return f}),n.d(t,"f",function(){return p}),n.d(t,"h",function(){return m}),n.d(t,"g",function(){return v}),n.d(t,"b",function(){return g}),n.d(t,"a",function(){return y});var i=n(79),r=n.n(i),o=n(80),s=n.n(o),a=n(27),l=n.n(a),c=n(52),u=n.n(c);function d(){return"undefined"!=typeof process&&u()(process,"versions","electron")}function h(e){return void 0!==e&&null!==e}function f(e){return void 0===e||null===e}function p(e){return new l.a(function(t,n){e?t():n()})}function m(e,t){console.warn("[Warn]["+e+"], "+t)}function v(e,t,n){var i=0;return function(){var r=+new Date;if(r-i>t){for(var o=arguments.length,s=Array(o),a=0;a<o;a++)s[a]=arguments[a];e.apply(n||this,s),i=r}}}function g(e,t,n){var i=null;return function(){clearTimeout(i);for(var r=arguments.length,o=Array(r),s=0;s<r;s++)o[s]=arguments[s];i=setTimeout(e.bind.apply(e,[n||this].concat(o)),t)}}var y=function(){function e(t){r()(this,e),this.unique=t.unique||!1,this.maxLength=t.maxLength||-1,this.queue=[]}return s()(e,[{key:"push",value:function(e){try{if(this.queue=this.queue.filter(function(e){return void 0!==e.timer&&null!==e.timer}),this.unique){var t=this.queue.filter(function(t){return e.type===t.type&&e.message===t.message});this.queue=this.queue.filter(function(t){return!(e.type===t.type&&e.message===t.message)}),t.forEach(function(e){e.$destroy()})}if(this.maxLength<=-1||this.queue.length<this.maxLength)return this.queue.push(e),!0;for(;this.queue.length>=this.maxLength;){this.queue.shift().$destroy()}return this.queue.push(e),!0}catch(e){return console.error(e),!1}}},{key:"clear",value:function(){return this.queue=this.queue.filter(function(e){return void 0!==e.timer&&null!==e.timer}),this.queue.length}}]),e}()},function(e,t){e.exports=require("path")},function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t,n){var i=n(70)("wks"),r=n(48),o=n(6).Symbol,s="function"==typeof o;(e.exports=function(e){return i[e]||(i[e]=s&&o[e]||(s?o:r)("Symbol."+e))}).store=i},function(e,t){e.exports=require("electron")},function(e,t,n){"use strict";t.__esModule=!0;var i,r=n(122),o=(i=r)&&i.__esModule?i:{default:i};t.default=function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return(0,o.default)(e)}},function(e,t,n){"use strict";n.r(t);var i={render:function(){var e=this.$createElement,t=this._self._c||e;return this.svg?t("svg",{staticClass:"td-icon-svg",class:"td-icon-svg-"+this.type,attrs:{"aria-hidden":"true"}},[t("use",{attrs:{"xmlns:xlink":"http://www.w3.org/1999/xlink","xlink:href":"#td-icon-svg-"+this.type}})]):t("i",{class:"td-icon-"+this.type})},staticRenderFns:[],name:"td-icon",props:{type:{type:String,require:!0},svg:{type:Boolean,default:!1}},install:function(e){e.component(i.name,i)}};t.default=i},function(e,t){e.exports=require("events")},function(e,t,n){e.exports=n(29)(50)},function(e,t,n){var i=n(6),r=n(3),o=n(43),s=n(21),a=n(22),l=function(e,t,n){var c,u,d,h=e&l.F,f=e&l.G,p=e&l.S,m=e&l.P,v=e&l.B,g=e&l.W,y=f?r:r[t]||(r[t]={}),_=y.prototype,b=f?i:p?i[t]:(i[t]||{}).prototype;for(c in f&&(n=t),n)(u=!h&&b&&void 0!==b[c])&&a(y,c)||(d=u?b[c]:n[c],y[c]=f&&"function"!=typeof b[c]?n[c]:v&&u?o(d,i):g&&b[c]==d?function(e){var t=function(t,n,i){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,i)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(d):m&&"function"==typeof d?o(Function.call,d):d,m&&((y.virtual||(y.virtual={}))[c]=d,e&l.R&&_&&!_[c]&&s(_,c,d)))};l.F=1,l.G=2,l.S=4,l.P=8,l.B=16,l.W=32,l.U=64,l.R=128,e.exports=l},function(e,t,n){var i=n(15),r=n(107),o=n(65),s=Object.defineProperty;t.f=n(16)?Object.defineProperty:function(e,t,n){if(i(e),t=o(t,!0),i(n),r)try{return s(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){var i=n(19);e.exports=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e}},function(e,t,n){e.exports=!n(35)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const i=n(5);let r,o=console;function s(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,r=(new Error).stack.split("\n").slice(e+1);r.shift();let o=[];return r.forEach((e,r)=>{let s=t.exec(e)||n.exec(e),a={};s&&5===s.length&&(a.method=s[1],a.path=s[2],a.line=s[3],a.pos=s[4],a.file=i.basename(a.path),o.push(a))}),o}if(r="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return s(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){o.time(...e)},t.timeEnd=function(...e){o.timeEnd(...e)},t.trace=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.trace(r,...e)},t.info=function(...e){let t=s(),n="anonymous";t[0]&&t[0].method&&(n=n),o.info(r,"["+n+"]",e.join(","))},t.warn=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.warn("<WARN>"+r,"["+n+"]",e.join(","))},t.error=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.error("<ERROR>"+r,"["+n+"]",e.join(","))},t.log=function(...e){o.log(r,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SubtitleFunction=t.TimeTipFunction=t.VipBufferTipFunction=t.XmpPluginFunction=t.XmpSubtitleFunction=t.PlayListFunction=t.ZipFunction=t.XmpPlayerFunction=t.XmpPlayType=t.VideoTipFunction=t.UIFunction=t.MainMenuFunction=t.SnapshotFunction=t.LoginSDKFunction=t.LoginFunction=t.DKFunction=t.ConfigFunction=void 0,function(e){e.saveConfig="SaveConfig",e.getConfigValue="GetConfigValue",e.setConfigValue="SetConfigValue",e.isConfigInitFinish="IsConfigInitFinish",e.getConfigModules="GetConfigModules",e.setXmpAndThunderConfigValue="SetXmpAndThunderConfigValue"}(t.ConfigFunction||(t.ConfigFunction={})),function(e){e.getTaskChannelInfo="GetTaskChannelInfo",e.getTaskBaseInfo="GetTaskBaseInfo",e.getTaskBaseInfos="GetTaskBaseInfos",e.getTaskAttribute="GetTaskAttribute",e.getTaskStatus="GetTaskStatus",e.findTask="FindTask",e.isTaskExist="IsTaskExist",e.isSupportPlay="IsSupportPlay",e.getTaskDownloadSpeed="GetTaskDownloadSpeed",e.enableDcdnWithVipCert="EnableDcdnWithVipCert",e.updateDcdnWithVipCert="UpdateDcdnWithVipCert",e.disableDcdnWithVipCert="DisableDcdnWithVipCert",e.getTpPeerId="GetTpPeerId",e.execSqlite="ExecSqlite",e.querySqlite="QuerySqlite",e.getCategoryViewTaskList="GetCategoryViewTaskList",e.getCategoryViewTaskListForSuspensionWnd="GetCategoryViewTaskListForSuspensionWnd",e.isLoadStorageTaskFinish="IsLoadStorageTaskFinish",e.isSingleBtTask="IsSingleBtTask",e.setTaskExtStat="SetTaskExtStat"}(t.DKFunction||(t.DKFunction={})),function(e){e.login="Login",e.logout="Logout",e.getUserID="GetUserID",e.getSessionID="GetSessionID",e.getSessionIDAw="GetSessionIDAw",e.getLoginOption="GetLoginOption",e.isLogined="IsLogined",e.getAllUserInfo="GetAllUserInfo",e.getAllUserInfoAw="GetAllUserInfoAw",e.getUserInfoEx="GetUserInfoEx",e.getUserInfo="GetUserInfo",e.getUserInfoAw="GetUserInfoAw",e.getUserHeader="GetUserHeader",e.showLoginDlg="ShowLoginDlg",e.createPersonalInfoWnd="CreatePersonalInfoWnd",e.createRetryLoginWnd="CreateRetryLoginWnd",e.switchAccount="SwitchAccount",e.updateHeader="UpdateHeader",e.pingThroughXLUserServer="PingThroughXLUserServer",e.retryLogIn="RetryLogIn",e.createWebWnd="CreateWebWnd",e.isAutoLoginAw="IsAutoLoginAw",e.channel="Channel",e.getVipInfo="GetVipInfo",e.getUserInfoByKeyAw="GetUserInfoByKeyAw"}(t.LoginFunction||(t.LoginFunction={})),function(e){e.getDeviceID="GetDeviceID",e.getInitData="GetInitData",e.saveData="SaveData"}(t.LoginSDKFunction||(t.LoginSDKFunction={})),function(e){e.subtitleIsSupportJpeg="XmpSubtitleIsSupportJpeg",e.queryMediaInfo="QueryMediaInfo",e.openVideoEdit="OpenVideoEdit",e.openGifEdit="OpenGifEdit",e.createSnapShotEx="CreateSnapShotEx",e.createSnapShot="CreateSnapShot",e.createHDPicture="CreateHDPicture",e.isHDPicCreating="IsHDPicCreating",e.openClearBlack="OpenClearBlack",e.closeClearBlack="CloseClearBlack",e.clearBlackWindow="ClearBlackWindow",e.getBlackCollectCount="GetBlackCollectCount",e.getCollectData="GetCollectData",e.getClearBlackRect="GetClearBlackRect",e.createPreViewSnapshot="CreatePreViewSnapshot",e.getPreViewSnapshot="GetPreViewSnapshot"}(t.SnapshotFunction||(t.SnapshotFunction={})),function(e){e.showSystemMenu="ShowSystemMenu"}(t.MainMenuFunction||(t.MainMenuFunction={})),function(e){e.createButton="CreateButton",e.updateButton="UpdateButton",e.createWebview="CreateWebview",e.setDefaultVipSpeedStatus="SetDefaultVipSpeedStatus",e.updateVipSpeedStatus="UpdateVipSpeedStatus",e.getWebviewInfo="GetWebviewInfo",e.createVipView="CreateVipview",e.showCurveChart="showCurveChart",e.applyBrowserView="ApplyBrowserView",e.getBrowserViewId="GetBrowserViewId",e.initBrowserViewConfig="initBrowserViewConfig",e.createVipMenu="CreateVipMenu",e.getMenuSlot="GetMenuSlot",e.openNewTab="OpenNewTab",e.videoPause="DoVideoPause",e.videoPlay="DoVideoPlay",e.videoPauseEx="DoVideoPauseEx",e.videoPlayEx="DoVideoPlayEx",e.setPlaySpeed="SetPlaySpeed",e.setProgressColor="SetProgressColor",e.getProgressColor="GetProgressColor",e.showBufferingEntry="ShowBufferingEntry",e.isBufferingEntryShow="IsBufferingEntryShow",e.showOpenVipEntry="ShowOpenVipEntry",e.registerFunctionCall="RegisterFunctionCall",e.executeFunctionCall="ExecuteFunctionCall",e.videoGetPlayState="VideoGetPlayState",e.setXmpFocus="SetXmpFocus",e.leaveFullScreen="LeaveFullScreen"}(t.UIFunction||(t.UIFunction={})),function(e){e.addItem="VideoTipLeftTopAddItem",e.addLinkItem="VideoTipLeftTopAddLinkItem",e.updateItemText="VideoTipLeftTopUpdateItemText",e.removeItem="VideoTipLeftTopRemoveItem",e.showTip="VideoTipLeftTopShowTip",e.closeTip="VideoTipLeftTopCloseTip",e.hideTip="HideTip"}(t.VideoTipFunction||(t.VideoTipFunction={})),function(e){e.unKnow="UnKnow",e.localFile="LocalFile",e.downloadAndPlay="DownloadAndPlay",e.yunBo="YunBo"}(t.XmpPlayType||(t.XmpPlayType={})),function(e){e.init="XmpPlayerInit",e.showAPlayerWindow="XmpShowAPlayerWindow",e.createXmpMedia="XmpPlayerCreateXmpMedia",e.open="XmpPlayerOpen",e.openFile="XmpPlayerOpenFile",e.addFile="XmpPlayerAddFile",e.openFolder="XmpPlayerOpenFolder",e.openBt="XmpPlayerOpenBt",e.openUrl="XmpPlayerOpenUrl",e.openMedia="XmpPlayerOpenMedia",e.close="XmpPlayerClose",e.stop="XmpPlayerStop",e.play="XmpPlayerPlay",e.pause="XmpPlayerPause",e.getVersion="XmpPlayerGetVersion",e.getState="XmpPlayerGetState",e.isPlaying="XmpPlayerIsPlaying",e.getDuration="XmpPlayerGetDuration",e.getPosition="XmpPlayerGetPosition",e.setPosition="XmpPlayerSetPosition",e.getVideoWidth="XmpPlayerGetVideoWidth",e.getVideoHeight="XmpPlayerGetVideoHeight",e.getVolume="XmpPlayerGetVolume",e.setVolume="XmpPlayerSetVolume",e.isSeeking="XmpPlayerIsSeeking",e.getBufferProgress="XmpPlayerGetBufferProgress",e.getConfig="XmpPlayerGetConfig",e.setConfig="XmpPlayerSetConfig",e.setCurrentMedia="XmpPlayerSetCurrentMedia",e.getCurrentMedia="XmpPlayerGetCurrentMedia",e.getLastMediaId="XmpPlayerGetLastMediaId",e.getMediaAttribute="XmpPlayerMediaAttribute",e.getCurrentMediaAttribute="XmpPlayerGetCurrentMediaAttribute",e.clearCurrentMedia="XmpPlayerClearCurrentMedia",e.openPreMedia="XmpPlayerOpenPreMedia",e.openNextMedia="XmpPlayerOpenNextMedia",e.isPlayOrderFirstMedia="XmpPlayerIsPlayOrderFirstMedia",e.isPlayOrderLastMedia="XmpPlayerIsPlayOrderLastMedia",e.isFirstMedia="XmpPlayerIsFirstMedia",e.isLastMedia="XmpPlayerIsLastMedia",e.isMediasLoadFinished="XmpPlayerIsMediasLoadFinished",e.getMediasInfo="XmpPlayerGetMediasInfo",e.removeMedia="XmpPlayerRemoveMedia",e.removeAllMedia="XmpPlayerRemoveAllMedia",e.setASpectRatioAtive="XmpPlayerSetASpectRatioAtive",e.setPlayForward="XmpPlayerSetPlayForward",e.postionChange="XmpPlayerPostionChange",e.getBufferRange="XmpPlayerGetBufferRange",e.fireKeyDown="XmpPlayerFireKeyDown",e.fireKeyUp="XmpPlayerFireKeyUp",e.setAudioTime="XmpPlayerSetAudioTime",e.setProportion="XmpPlayerSetProportion",e.setVlomue="XmpPlayerSetVlomue",e.getCurView="XmpPlayerGetCurView",e.recordStat="XmpPlayerRecordStat",e.getHostWnd="XmpPlayerGetHostWnd",e.startWindowZorderChecker="StartWindowZorderChecker",e.stopWindowZorderChecker="StopWindowZorderChecker",e.setZOrderCheckerWindowHandle="SetZOrderCheckerWindowHandle",e.removeZOrderCheckerWindowHandle="RemoveZOrderCheckerWindowHandle",e.getVideoDuration="GetVideoDuration",e.getDownloadState="GetDownloadState",e.getResConfig="GetResConfig",e.queryTaskState="QueryTaskState",e.shareMediaInfo="ShareMediaInfo",e.getThunderDirState="GetThunderDirState",e.setThunderDirState="SetThunderDirState",e.showSyncDig="ShowSyncDig",e.isXmpLite="IsXmpLite",e.handleVipPlaySpeed="handleVipPlaySpeed",e.getMediaInfo="GetMediaInfo",e.getCurrentMediaInfo="GetCurrentMediaInfo",e.setMediaAttribute="XmpPlayerSetMediaAttribute",e.updateDownloadEmbedRect="UpdateDownloadEmbedRect",e.updatePanEmbedRect="UpdatePanEmbedRect",e.createIndependentWindow="CreateIndependentWindow",e.thunderResize="ThunderResize",e.updateScrollShowOrHide="UpdateScrollShowOrHide",e.updateFloatWindow="IpdateFloatWindow",e.updateAplayerHwnd="UpdateAplayerHwnd",e.updatePlaySource="UpdatePlaySource",e.setXmpCurrentMode="SetXmpCurrentMode",e.getXmpCurrentMode="GetXmpCurrentMode",e.isSwitchXmpMode="IsSwitchXmpMode",e.destroy="XmpDestroy",e.maximize="XmpMaximize",e.unmaximize="XmpUnmaximize",e.minmized="XmpMinmized",e.setPlaySpeed="SetPlaySpeed",e.getPlaySpeed="GetPlaySpeed",e.setWindowTopMode="SetWindowTopMode",e.calcelWindowTopMode="CalcelWindowTopMode",e.enterFullScreen="EnterFullScreen",e.leaveFullScreen="LeaveFullScreen",e.setFullcScreen="SetFullcScreen",e.getIsFullScreen="GetIsFullScreen",e.getIsFullScreenEx="GetIsFullScreenEx",e.setPlayingTaskId="SetPlayingTaskId",e.setlayPingPanFileId="SetPlayingPanFileId",e.setSelectTaskId="SetSelectTaskId",e.setSelectPanel="SetSelectPanel",e.setSelectTab="SetSelectTab",e.setSelectPanFileId="SetSelectPanFileId",e.getPlayingTaskId="GetPlayingTaskId",e.getPlayingPanFileId="GetPlayingPanFileId",e.getSelectTaskId="GetSelectTaskId",e.getSelectPanel="GetSelectPanel",e.getSelectTab="GetSelectTab",e.getSelectPanFileId="GetSelectPanFileId",e.getXmpPlayType="GetXmpPlayType",e.getDownloadAndPlayTaskData="GetDownloadAndPlayTaskData",e.openDLNA="OpenDLNA",e.disConnectDLNA="DisConnectDLNA",e.getCurrentDLNADeviceName="GetCurrentDLNADeviceName",e.addXmpVideoTipPos="AddXmpVideoTipPos",e.removeXmpVideoTipPos="RemoveXmpVideoTipPos",e.addXmpVideoTipPosMainRenderer="AddXmpVideoTipPosRenderer",e.removeXmpVideoTipPosMainRenderer="RemoveXmpVideoTipPosRenderer",e.switchSilent="XmpSwitchSilent",e.isSilent="XmpIsSilent",e.cancelDownloadCodecs="OnCancelDownloadCodecs",e.getCachedDownloadCodecInfo="OnGetCachedDownloadCodecInfo",e.onTaskStopped="OnTaskStopped",e.showOrHideXmpWindow="OnShowOrHideXmpWindow",e.getPlayingSoure="OnGetPlayingSoure",e.userLogout="OnUserLogout",e.isCurrentBuffingIsByDrag="IsCurrentBuffingIsByDrag",e.thunderQuit="ThunderQuit",e.getLastPlayError="GetLastPlayError",e.showAplayerAndFloatWindow="ShowAplayerAndFloatWindow",e.hideAplayerAndFloatWindow="HideAplayerAndFloatWindow",e.hideEmbedAplayerAndFloatWindow="HideEmbedAplayerAndFloatWindow",e.isPlayMusic="IsPlayMusic",e.getMediaById="GetMediaById",e.setPlayEndInfo="SetPlayEndInfo",e.getPlayId="GetPlayId",e.getPlayTick="GetPlayTick",e.setAudioTrack="SetAudioTrack",e.vipTellPopupWindowShow="VipTellPopupWindowShow",e.vipTellPopupWindowClose="VipTellPopupWindowClose"}(t.XmpPlayerFunction||(t.XmpPlayerFunction={})),function(e){e.doGetFileList="ZipDoGetFileList",e.doDelFileList="ZipDoDelFileList",e.abortGetFileList="ZipAbortGetFileList",e.openFile="ZipOpenFile",e.openPassWord="ZipOpenPassWord",e.openIndex="ZipOpenIndex",e.makeIndexFile="ZipMakeIndexFile",e.abortMakeIndexFile="ZipAbortMakeIndexFile",e.needWaitIndexFile="ZipNeedWaitIndexFile",e.isIndexError="ZipIsIndexError",e.isPasswordError="ZipIsPasswordError",e.isFilePlaying="ZipIsFilePlaying"}(t.ZipFunction||(t.ZipFunction={})),function(e){e.getMediaPlayed="PlayList.GetMediaPlayed",e.setMediaPlayed="PlayList.SetMediaPlayed"}(t.PlayListFunction||(t.PlayListFunction={})),function(e){e.isSubtitleShow="XmpSubtitleIsSubtitleShow",e.isSubtitle2Show="XmpSubtitleIsSubtitle2Show",e.showSubtitle="XmpSubtitleShowSubtitle",e.showSubtitle2="XmpSubtitleShowSubtitle2",e.getCurrentSubtitle="XmpSubtitleGetCurrentSubtitle",e.getCurrentSubtitle2="XmpSubtitleGetCurrentSubtitle2",e.getSubtitleList="XmpSubtitleGetSubtitleList",e.loadSubtitleFile="XmpSubtitleLoadSubtitleFile",e.setCurrentSubtitle="XmpSubtitleSetCurrentSubtitle",e.setSubtitleLanguageIndex="XmpSubtitleSetSubtitleLanguageIndex",e.setSubtitlePosition="XmpSubtitleSetSubtitlePosition",e.getSubtitlePosition="XmpSubtitleGetSubtitlePosition",e.setSubtitleFontStyle="XmpSubtitleSetSubtitleFontStyle",e.getSubtitleSize="XmpSubtitleGetSubtitleSize",e.getSubtitleColor="XmpSubtitleGetSubtitleColor",e.getSubtitleFont="XmpSubtitleGetSubtitleFont",e.setSubtitleTimming="XmpSubtitleSetSubtitleTimming",e.getSubtitleTimming="XmpSubtitleGetSubtitleTimming",e.setSubtitle3DMode="XmpSubtitleSetSubtitle3DMode",e.setUseHardware="XmpSubtitleSetUseHardware",e.reportSubTitle="ReportSubTitle",e.uploadSubTitle="UploadubTitle",e.onSubtitleInit="XmpSubtitleInit",e.onSubtitleLoadSuc="XmpSubtitleLoadSuc",e.createSuppress="CreateSuppress",e.getSuppressState="GetSuppressState",e.getSuppressPlayUrl="GetSuppressPlayUrl",e.getSuppressProgress="GetSuppressProgress",e.getSuppressNeedTime="GetSuppressNeedTime",e.showSubtitleSuppressDialog="ShowSubtitleSuppressDialog",e.continueSuppress="ContinueSuppress",e.getFullState="GetFullState",e.isSettingDefaultSecondSubtitle="IsSettingDefaultSecondSubtitle",e.isSecondSubtitle="IsSecondSubtitle"}(t.XmpSubtitleFunction||(t.XmpSubtitleFunction={})),function(e){e.getTaskInfo="GetTaskInfo",e.shellOpen="ShellOpen",e.openFolderWithSelectFile="OpenFolderWithSelectFile",e.getFileMd5="GetFileMd5",e.getPeerID="GetPeerID",e.getDeviceID="GetDeviceID",e.getThunderVersion="GetThunderVersion",e.getXmpVersion="GetXmpVersion",e.trackEvent="TrackEvent",e.getCurInternetState="GetCurInternetState",e.getCurPlatform="GetCurPlatform",e.getXmpShowMode="GetXmpShowMode",e.isModuleEnable="IsModuleEnable",e.updateTabSelectState="UpdateTabSelectState",e.getMouseShow="getMouseShow",e.updateFullFill="UpdateFullFill",e.getWindowTopMode="GetWindowTopMode",e.isVideoEditMode="IsVideoEditMode",e.showCtrlView="ShowCtrlView",e.showTopView="ShowTopView",e.showMoreAbout="ShowMoreAbout",e.setMenuPopUp="SetMenuPopUp",e.setWindowPopUp="SetWindowPopUp",e.isListExpand="IsListExpand",e.isMenuPopUp="IsMenuPopUp",e.isWindowPopUp="IsWindowPopUp",e.isMouseInFloat="IsMouseInFloat",e.switchtoNormalMode="SwitchtoNormalMode",e.switchToTransparentMode="SwitchToTransparentMode",e.dropOpenFiles="DropOpenFiles",e.dropOpenUrl="DropOpenUrl",e.getShowCondition="GetShowCondition",e.utf8StringEncodeToBinary="Utf8StringEncodeToBinary",e.utf8StringDecodeFromBinary="Utf8StringDecodeFromBinary",e.getErrorCodeConfigMessage="GetErrorCodeConfigMessage",e.quitProcess="QuitProcess",e.associate="Associate",e.checkXmpUpdate="CheckXmpUpdate",e.changeVideoRatio="ChangeVideoRatio",e.getVideoScaleRate="GetVideoScaleRate",e.getHoverPos="GetHoverPos",e.setHoverPos="SetHoverPos",e.getCurrentDevice="GetCurrentDevice",e.activeDevice="ActiveDevice",e.setCurrentDevice="SetCurrentDevice",e.getDeviceList="GetDeviceList",e.searchDevice="SearchDevice",e.isDeviceSupport="IsDeviceSupport",e.getInitUserLoginParam="GetInitUserLoginParam",e.handleMouseTimeOut="HandleMouseTimeOut",e.getOpenFrom="GetOpenFrom",e.setHideAndPauseWindow="SetHideAndPauseWindow",e.setHideWindowNotPause="SetHideWindowNotPause",e.setFullWindowMouseEnter="SetFullWindowMouseHover",e.setDragMove="SetDragMove",e.isEmbedMode="IsEmbedMode",e.getBackUpValue="getBackUpValue"}(t.XmpPluginFunction||(t.XmpPluginFunction={})),function(e){e.getVideoStuckVipData="GetVideoStuckVipData",e.openPayVipUrl="OpenPayVipUrl",e.actionPreOpen="ActionPreOpen"}(t.VipBufferTipFunction||(t.VipBufferTipFunction={})),function(e){e.showTimeTips="ShowTimeTips",e.destroyTimeTips="DestroyTimeTips"}(t.TimeTipFunction||(t.TimeTipFunction={})),function(e){e.loadSubtitle="LoadSubtitle",e.onlineSubtitleMatch="OnlineSubtitleMatch",e.showOrHideSubtitle="ShowOrHideSubtitle",e.isEnableHideSubtitle="IsEnableHideSubtitle",e.isSubtitleHided="IsSubtitleHided",e.loadEmbedSubtitle="LoadEmbedSubtitle",e.loadOnlineSubtitle="LoadOnlineSubtitle",e.loadLocalSubtitle="LoadLocalSubtitle",e.getOnlineSubtitleList="GetOnlineSubtitleList",e.getEmbedSubtitleList="GetEmbedSubtitleList",e.getLocalSubtitleList="GetLocalSubtitleList",e.getManualSubtitleList="GetManualSubtitleList",e.getCurrentTitle="GetCurrentTitle",e.getCurrentTitle2="GetCurrentTitle2",e.manualLoadSubtitle="ManualLoadSubtitle",e.setSubtitleFontStyle="SetSubtitleFontStyle",e.getSubtitleFontStyle="GetSubtitleFontStyle",e.setSubtitlePosition="SetSubtitlePosition",e.getSubtitlePosition="GetSubtitlePosition",e.setUseHardware="SetUseHardware",e.getSubtitleTimming="GetSubtitleTimming",e.setSubtitleTimming="SetSubtitleTimming"}(t.SubtitleFunction||(t.SubtitleFunction={}))},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(34),r=n(5);t.getDefaultPrex=function(){return r.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=i.tmpdir();let n=e;e||(n=r.basename(process.execPath,".exe"));let o=r.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(o="\\\\.\\pipe\\"+(o=(o=o.replace(/^\//,"")).replace(/\//g,"-"))),o},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},function(e,t,n){var i=n(14),r=n(44);e.exports=n(16)?function(e,t,n){return i.f(e,t,r(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,n){"use strict";n.r(t);var i={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("button",e._g({staticClass:"td-button",class:{"td-button--secondary":e.secondary,"td-button--other":e.other,"is-disabled":e.disabled,"is-status":e.status,"td-button--small":"small"===e.size,"td-button--large":"large"===e.size},attrs:{disabled:e.disabled}},e.$listeners),[e.status&&e.statusIcon?n("td-icon",{attrs:{type:e.statusIcon}}):e._e(),e._v(" "),e._t("default")],2)},staticRenderFns:[],name:"td-button",props:{secondary:Boolean,other:Boolean,disabled:Boolean,size:String,status:Boolean,statusIcon:String},install:function(e){e.component(i.name,i)}};t.default=i},function(e,t){e.exports=require("util")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderHelper=void 0;const r=n(5),o=n(25),s=n(63),a=n(0).default.getLogger("XMP.ThunderHelper"),l=o.default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(e){return i(this,void 0,void 0,function*(){if(!e||(yield e.isDestroyed()))return 0;let t=0,n=yield e.getNativeWindowHandle();return n&&(t=n.readUIntLE(0,4)),t})}function r(e,t,n,i,r,o,a,c){if(!l||!e)return;let u="";c&&c.length>3&&(u=c.substring(0,c.length-3)),a|=s.Uflag.SWP_ASYNCWINDOWPOS,l.setWindowPos(e,t,n,i,r,o,a)}function o(){return l.getDPIAwareSupport()}function c(e){return l.getMonitorDPIFactor(e)}function u(){return l.getSysDPIFactor()}e.getDriveInfo=function(e){return l.getDriveInfo(e)},e.getFreePartitionSpace=function(e){return l.getFreePartitionSpace(e)},e.getLogicalDriveStrings=function(){return l.getLogicalDriveStrings()},e.getPartitionSpace=function(e){return l.getPartitionSpace(e)},e.getSystemTempPath=function(){return l.getSystemTempPath()},e.getTaskTypeFromUrl=function(e){return l.getTaskTypeFromUrl(e)},e.dwmIsCompositionEnabled=function(){return l.dwmIsCompositionEnabled()},e.setWindowLong=function(e,t,n){return l.setWindowLong(e,t,n)},e.showCursor=function(e){l&&l.showCursor(e)},e.isWindowVisible=function(e){return!!l&&l.isWindowVisible(e)},e.isWindowEnable=function(e){return!!l&&l.isWindowEnable(e)},e.enableWindow=function(e,t){return!!l&&l.enableWindow(e,t)},e.shChangeNotify=function(){if(l)return l.refreshIcon()},e.getBrowserWindowHandle=t,e.setTopMostWindow=function(e){return i(this,void 0,void 0,function*(){l&&e&&r(yield t(e),s.OptionOfHWNDInAfter.HWND_TOPMOST,0,0,0,0,s.Uflag.SWP_NOSIZE|s.Uflag.SWP_NOMOVE)})},e.getGraphicsAdapterList=function(){return l?l.getGraphicsAdapterList():null},e.getFileVersion=function(e){return l&&e?l.getFileVersion(e):""},e.getCursorPos=function(){return l?l.getCursorPos():{x:0,y:0}},e.getPowerInfo=function(){return l?l.getPowerInfo():null},e.setWindowRectRgn=function(e,t,n,i,r){l&&l.setWindowRectRgn(e,t,n,i,r)},e.createRectRgn=function(e,t,n,i){return l?l.createRectRgn(e,t,n,i):0},e.combineRgn=function(e,t,n=s.CombineRgnType.RGN_OR){return l?l.combineRgn(e,t,n):-1},e.deleteObject=function(e){return!!l&&l.deleteObject(e)},e.setWindowRgn=function(e,t,n=!0){return l?l.setWindowRgn(e,t,n):-1},e.hitCaption=function(e){return!!l&&l.hitCaption(e)},e.isWindowForeGround=function(e){return i(this,void 0,void 0,function*(){if(!l)return!1;let t=l.getForegroundWindow();return e===t})},e.getForegroundWindow=function(){return l?l.getForegroundWindow():0},e.isWindowForceGroundEx=function(e){return!!l&&e===l.getForegroundWindow()},e.setListExpandMode=function(e){return l?l.setListExpandMode(e):0},e.setTopMostWindowEx=function(e){l&&e&&r(e,s.OptionOfHWNDInAfter.HWND_TOPMOST,0,0,0,0,s.Uflag.SWP_NOSIZE|s.Uflag.SWP_NOMOVE)},e.cancelTopMostWindow=function(e,n){return i(this,void 0,void 0,function*(){if(!l||!e)return;let i=yield t(e),o=s.Uflag.SWP_NOSIZE|s.Uflag.SWP_NOMOVE|s.Uflag.SWP_NOACTIVATE;n&&(o|=n),r(i,s.OptionOfHWNDInAfter.HWND_NOTOPMOST,0,0,0,0,o)})},e.cancelTopMostWindowEx=function(e,t){if(!l||!e)return;let n=s.Uflag.SWP_NOSIZE|s.Uflag.SWP_NOMOVE|s.Uflag.SWP_NOACTIVATE;t&&(n|=t),r(e,s.OptionOfHWNDInAfter.HWND_NOTOPMOST,0,0,0,0,n)},e.getTempPath=function(){return l?l.getSystemTempPath():""},e.switchToThisWindow=function(e,t=!1){l&&l.switchToThisWindow(e,t)},e.setScreenSaveActive=function(e){if(l)return l.setScreenSaveActive(e)},e.getDiskFreeSpace=function(e){return l?l.getFreePartitionSpace(e):0},e.getMemInfoByPointer=function(e,t){return l?l.getMemInfoByPointer(e,t):null},e.setMinTrackSize=function(e,t,n){return l?l.setMinTrackSize(e,t,n):null},e.setWindowPos=function(e,t,n,i,o,s,a,c){if(!l||!e||e.isDestroyed())return;r(e.getNativeWindowHandle().readUIntLE(0,4),t,n,i,o,s,a,c)},e.moveWindowGently=function(e,t,n,i,r,o,s){l&&t&&l.moveWindowGently(e,t,n,i,r,o,s)},e.moveWindow=function(e,t){l&&e&&l.moveWindow(e,t.x,t.y,t.width,t.height,!0)},e.setWindowFocus=function(e,t=!0){l&&e&&l.setWindowFocus(e,t)},e.getWindowRect=function(e){return l&&e?l.getWindowRect(e):{x:0,y:0,width:0,height:0}},e.getClientRect=function(e){return l&&e?l.getClientRect(e):{x:0,y:0,width:0,height:0}},e.getCurrentScreenRect=function(e){return l&&e?l.getCurrentScreenRect(e):{x:0,y:0,width:0,height:0}},e.getCurrentWorkRect=function(e){return l&&e?l.getCurrentScreenRect(e,!0):{x:0,y:0,width:0,height:0}},e.getChildWindow=function(e){return l&&e?l.getChildWindow(e):0},e.getParentWindow=function(e){return l&&e?l.getParentWindow(e):0},e.getKeyState=function(e){return l?l.getKeyState(e):0},e.setWindowPosEx=r,e.beginDeferWindowPos=function(e){return l&&e?l.beginDeferWindowPos(e):0},e.endDeferWindowPos=function(e){return l&&e?l.endDeferWindowPos(e):0},e.deferWindowPos=function(e,t,n,i,r,o,s,a){return l&&e?l.deferWindowPos(e,t,n,i,r,o,s,a):0},e.bindChildWindow=function(e,t,n){if(!l||!t||!e||t.isDestroyed()||e.isDestroyed())return;let i=t.getNativeWindowHandle().readUIntLE(0,4),r=e.getNativeWindowHandle().readUIntLE(0,4);n?l.bindChildWindow(r,i,n.x,n.y,n.width,n.height):l.bindChildWindow(r,i)},e.bindChildWindowEx=function(e,t,n){l&&t&&e&&(n?l.bindChildWindow(e,t,n.x,n.y,n.width,n.height):l.bindChildWindow(e,t))},e.setDllDirectory=function(e){l&&e&&l.setDllDirectory(e)},e.bringWindowToTop=function(e){if(!e||e.isDestroyed())return;let t=e.getNativeWindowHandle().readUIntLE(0,4);l.bringWindowToTop(t)},e.bringWindowToTopEx=function(e){e&&l.bringWindowToTop(e)},e.modifyStyleEx=function(e,t,n){e&&l.modifyStyle(e,t,n)},e.modifyStyle=function(e,t,n){if(!e||e.isDestroyed())return;let i=e.getNativeWindowHandle().readUIntLE(0,4);l.modifyStyle(i,t,n)},e.registerSliderCreate=function(e){l.registerSliderCreate(e)},e.showSlider=function(e){l.showSlider(e)},e.initSideBar=function(e,t,n,i){l.initSideBar(e,t,n,i)},e.uninitSideBar=function(){return i(this,void 0,void 0,function*(){return new Promise(e=>{l.uninitSideBar(()=>{e()})})})},e.createSideBar=function(){l.createSideBar()},e.destroySideBar=function(){l.destroySideBar()},e.readRegValue=function(e,t,n){return l?l.readRegString(e,t,n):""},e.setForcegroundWindow=function(e){if(!e||e.isDestroyed())return;let t=e.getNativeWindowHandle().readUIntLE(0,4);l.setForegroundWindow(t)},e.setActiveWindowEx=function(e){l&&e&&l.setActiveWindow(e)},e.setForcegroundWindowEx=function(e){e&&l.setForegroundWindow(e)},e.is64BitWindows=function(){return!!l&&l.is64bitSystem()},e.setSideBarExpandState=function(e){l.setExpandState(e)},e.unBindChildWindow=function(e,t){if(!l||!t||!e||t.isDestroyed()||e.isDestroyed())return;a.information("unBindChildWindow");let n=t.getNativeWindowHandle().readUIntLE(0,4),i=e.getNativeWindowHandle().readUIntLE(0,4);l.unBindChildWindow(i,n)},e.unBindChildWindowEx=function(e,t){l&&t&&e&&(a.information("unBindChildWindowEx"),l.unBindChildWindow(e,t))},e.addBrowserWindowClipStyle=function(e){if(!l||!e||e.isDestroyed())return!1;let t=e.getNativeWindowHandle().readUIntLE(0,4),n=l.findChildWindow(t,"Intermediate D3D Window");return n&&l.setWindowLong(n,s.SetWindowType.GWL_STYLE,s.WindowStyle.WS_CLIPSIBLINGS),l.setWindowLong(t,s.SetWindowType.GWL_STYLE,s.WindowStyle.WS_CLIPCHILDREN),!!n},e.addBrowserWindowClipStyleEx=function(e){if(!l)return!1;let t=l.findChildWindow(e,"Intermediate D3D Window");return t&&l.setWindowLong(t,s.SetWindowType.GWL_STYLE,s.WindowStyle.WS_CLIPSIBLINGS),l.setWindowLong(e,s.SetWindowType.GWL_STYLE,s.WindowStyle.WS_CLIPCHILDREN),!!t},e.getDoubleClickTime=function(){return l?l.getDoubleClickTime():50},e.invalidateRect=function(e,t,n){l&&l.invalidateRect(e,t,n)},e.isIconic=function(e){return!!l&&l.isIconic(e)},e.isZoomed=function(e){return!!l&&l.isZoomed(e)},e.showWindow=function(e){return!!l&&l.showWindow(e,s.ShowWindowCmd.SW_SHOW)},e.hideWindow=function(e){return!!l&&l.showWindow(e,s.ShowWindowCmd.SW_HIDE)},e.setZorderWindow=function(e,t,n){if(l)return l.setZorderWindow(e,t,n)},e.getWindowLong=function(e,t){return l?l.getWindowLong(e,t):0},e.getHelperObject=function(){return l},e.getPeerID=function(){return l.getPeerID()},e.getDPIAwareSupport=o,e.getMonitorDPIFactor=c,e.getSysDPIFactor=u,e.getDpi=function(e){let t=1;return t=o()?c(e):u()},e.killProcess=function(e){return i(this,void 0,void 0,function*(){let t=null;{let i="tasklist",r=n(106).exec;t=new Promise(t=>{r(i,function(n,i,r){let o=!1;do{if(n){a.warning(n),o=!1;break}i.split("\n").filter(function(t){let n=t.trim().split(/\s+/);if(n.length<2)return;let i=n[0],r=Number(n[1]);i.toLowerCase().indexOf(e.toLowerCase())>=0&&r&&(process.kill(r,"SIGTERM"),o=!0)})}while(0);t(o)})})}return t})},e.sleep=function(e){return i(this,void 0,void 0,function*(){yield new Promise((t,n)=>{setTimeout(t,e)})})},e.calculateFileGCID=function(e){return i(this,void 0,void 0,function*(){return new Promise(t=>{l.calculateFileGCID(e,e=>{a.warning("getVideoMediaInfo calculateFileGCID result",e),t(e)})})})},e.getTickCount=function(){return l.getTickCount()},e.getTextScale=function(){return Number(l.readRegString("HKEY_CURRENT_USER","SOFTWARE\\Microsoft\\Accessibility","TextScaleFactor"))||100}}(t.ThunderHelper||(t.ThunderHelper={}))},function(e,t,n){e.exports={default:n(172),__esModule:!0}},function(e,t,n){var i=n(129),r=n(67);e.exports=function(e){return i(r(e))}},function(e,t){e.exports=vendor_67f12d0c83789636af43},function(e,t){e.exports={}},,,function(e,t,n){"use strict";t.__esModule=!0;var i,r=n(128),o=(i=r)&&i.__esModule?i:{default:i};t.default=function(e,t,n){return t in e?(0,o.default)(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}},function(e,t){e.exports=require("os")},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t){e.exports=!0},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},function(e,t,n){"use strict";const i=n(17);if("renderer"===process.type){if(i.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(98);e.require=t.remoteRequire,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,i)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,i)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(i.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(102);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.FileSystemAWNS=void 0;const r=n(45),o=n(5),s=n(24),a=n(34),l=s.promisify,c=n(0).default.getLogger("Thunder.base.fs-utilities");!function(e){function t(e){return i(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(r.open);try{t=yield n(e,"r")}catch(e){c.warning(e)}}return t})}function n(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(r.access);try{yield n(e),t=!0}catch(e){c.information(e)}}return t})}function s(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(r.mkdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function u(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(r.rmdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function d(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(r.unlink);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function h(e){return i(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(r.readdir);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function f(e){return i(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(r.lstat);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function p(e,t){return i(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let i=o.join(e,t),r=yield f(i);n=null!==r&&r.isDirectory()?yield m(i):yield d(i)}return n})}function m(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){if(yield n(e)){t=!0;let n=yield h(e);for(let i=0;i<n.length;i++)t=(yield p(e,n[i]))&&t;(t||0===n.length)&&(t=(yield u(e))&&t)}}return t})}function v(e){return i(this,void 0,void 0,function*(){let t=!1;return c.information("mkdirsAW",e),void 0!==e&&((yield n(e))?t=!0:o.dirname(e)===e?t=!1:(yield v(o.dirname(e)))&&(t=yield s(e))),t})}function g(e,t){return i(this,void 0,void 0,function*(){let i;if(e.toLowerCase()!==t.toLowerCase()&&(yield n(e))){let n=r.createReadStream(e),o=r.createWriteStream(t);i=new Promise(e=>{n.pipe(o).on("finish",()=>{e(!0)})})}else i=new Promise(e=>{e(!1)});return i})}e.readFileAW=function(e){return i(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(r.readFile);try{t=yield n(e)}catch(e){c.warning(e)}}return t})},e.openFile=t,e.readFilePartAw=function(e,n,o,s=0){return i(this,void 0,void 0,function*(){let i={bytesRead:0,buffer:new Buffer(0)},a=yield t(e);if(!a)return i;const u=l(r.read);try{let e=new Buffer(o);i=yield u(a,e,n,o,s),e=void 0}catch(e){c.warning(e)}return yield l(r.close)(a),i})},e.writeFileAW=function(e,t){return i(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const i=l(r.writeFile);try{yield i(e,t),n=!0}catch(e){c.warning(e)}}return n})},e.existsAW=n,e.mkdirAW=s,e.rmdirAW=u,e.unlinkAW=d,e.readdirAW=h,e.readdirAllFiles=function e(t){return i(this,void 0,void 0,function*(){let i=!1,r=[],s=yield f(t),a=yield h(t);if(!a)return r;for(let l=0;l<a.length;l++){let c=o.join(t,a[l]);(i=yield n(c))&&((s=yield f(c)).isDirectory()?r.push(...yield e(c)):r.push(c))}return r})},e.lstatAW=f,e.rmdirsAW=m,e.mkdirsAW=v,e.renameAW=function(e,t){return i(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=l(r.rename);try{yield n(e,t)}catch(e){c.warning(e)}}})},e.copyFileAW=g,e.copyDirsAW=function e(t,r){return i(this,void 0,void 0,function*(){let i=!1,s=yield f(t);if(s.isDirectory()){i=yield v(r);let a=yield h(t);for(let l=0;l<a.length;l++){let c=o.join(t,a[l]),u=o.join(r,a[l]);(i=yield n(c))&&(i=(s=yield f(c)).isDirectory()?yield e(c,u):yield g(c,u))}}return i})},e.mkdtempAW=function(){return i(this,void 0,void 0,function*(){let e=!1;const t=l(r.mkdtemp),n=a.tmpdir();try{e=yield t(`${n}${o.sep}`)}catch(e){c.warning(e)}return e})},e.getFileSize=function e(t){return i(this,void 0,void 0,function*(){let i=0;do{if(!t)break;if(!(yield n(t)))break;let r=yield f(t);if(r)if(r.isDirectory()){let n=yield h(t);for(let r=0;r<n.length;r++)i+=(yield e(o.join(t,n[r])))}else i=r.size}while(0);return i})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.rpc=void 0;const r=n(40),o=n(8),s=n(11),a=n(24);t.rpc=new class{constructor(){this.mapObj=new Map,this.mapObjIniting=new Map,this.emitter=new s.EventEmitter}isXmpLiteEx(){return"renderer"!==process.type&&"xmplite"===o.app.getName()}getAppName(){return i(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}isXmpLite(){return i(this,void 0,void 0,function*(){return"xmplite"===(yield this.getAppName())})}getAppVersion(){return i(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return i(this,void 0,void 0,function*(){return r.global.process.__resolve()})}getIpcMain(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getWebContents(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("webContents")})}getBrowserWindow(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getGlobalShortcut(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return i(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>i(this,void 0,void 0,function*(){this.emitter.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield r.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emitter.emit("OnInitCurrentWebContents",e),this.emitter.listeners("OnInitCurrentWebContents").forEach(e=>{this.emitter.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return i(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>i(this,void 0,void 0,function*(){this.emitter.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield r.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emitter.emit("OnInitCurrentWindow",e),this.emitter.listeners("OnInitCurrentWindow").forEach(e=>{this.emitter.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return i(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return a.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>i(this,void 0,void 0,function*(){this.emitter.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield r.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emitter.emit(e,t),this.emitter.listeners(e).forEach(t=>{this.emitter.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}},function(e,t,n){var i=n(46);e.exports=function(e,t,n){if(i(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,i){return e.call(t,n,i)};case 3:return function(n,i,r){return e.call(t,n,i,r)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){e.exports=require("fs")},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,n){var i=n(111),r=n(71);e.exports=Object.keys||function(e){return i(e,r)}},function(e,t){var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+i).toString(36))}},function(e,t,n){var i=n(14).f,r=n(22),o=n(7)("toStringTag");e.exports=function(e,t,n){e&&!r(e=n?e:e.prototype,o)&&i(e,o,{configurable:!0,value:t})}},,function(e,t,n){"use strict";n.r(t);var i=n(9),r=n.n(i),o={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("label",{staticClass:"td-checkbox",class:{"is-checked":e.checked,"is-disabled":e.disabled,"is-indeterminate":e.indeterminate}},[n("input",{staticClass:"td-checkbox__inner",attrs:{type:"checkbox",disabled:e.disabled},domProps:{checked:e.checked},on:{change:e.handleChange}}),e._v(" "),n("span",{staticClass:"td-checkbox__label"},[e._t("default")],2)])},staticRenderFns:[],name:"td-checkbox",props:{label:[String,Number],value:[Boolean,Array],disabled:{type:Boolean,default:!1},indeterminate:Boolean},computed:{checked:function(){return"boolean"==typeof this.value?this.value:this.value.includes(this.label)}},methods:{handleChange:function(){var e=this;"boolean"==typeof this.value?this.$emit("input",!this.value):this.checked?this.$emit("input",this.value.filter(function(t){return t!==e.label})):this.$emit("input",[].concat(r()(this.value),[this.label]))}},install:function(e){e.component(o.name,o)}};t.default=o},function(e,t){e.exports=function(){for(var e=[].slice.call(arguments),t=e[0],n=1;n<e.length;n++)try{t=t[e[n]]}catch(e){return}return t}},function(e,t,n){"use strict";t.__esModule=!0;var i=s(n(192)),r=s(n(194)),o="function"==typeof r.default&&"symbol"==typeof i.default?function(e){return typeof e}:function(e){return e&&"function"==typeof r.default&&e.constructor===r.default&&e!==r.default.prototype?"symbol":typeof e};function s(e){return e&&e.__esModule?e:{default:e}}t.default="function"==typeof r.default&&"symbol"===o(i.default)?function(e){return void 0===e?"undefined":o(e)}:function(e){return e&&"function"==typeof r.default&&e.constructor===r.default&&e!==r.default.prototype?"symbol":void 0===e?"undefined":o(e)}},function(e,t,n){"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(i||(i={})),t.default=i},function(e,t,n){"use strict";var i;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(i||(i={})),e.exports=i},function(e,t,n){"use strict";var i=n(173)(!0);n(109)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=i(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){var i=n(67);e.exports=function(e){return Object(i(e))}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,n){"use strict";n.r(t),n.d(t,"Confirm",function(){return a});var i=n(10),r=n(23),o=n(4),s={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"show",rawName:"v-show",value:e.visible,expression:"visible"}],staticClass:"td-cover"},[n("div",{staticClass:"td-dialog",class:[{"td-dialog--fullscreen":e.fullscreen},e.customClass]},[n("div",{staticClass:"td-dialog__header"},[e._t("header"),e._v(" "),n("a",{staticClass:"td-dialog__close",attrs:{href:"javascript:;",title:"关闭"},on:{click:e.handleClose}},[n("td-icon",{attrs:{type:"close"}})],1)],2),e._v(" "),n("div",{staticClass:"td-dialog__body"},[e._t("default")],2),e._v(" "),e.footerEnabled?n("div",{staticClass:"td-dialog__footer"},[n("div",{staticClass:"td-dialog-footer"},[e._t("footer",[n("td-button",{on:{click:e.handleOk}},[e._v("确定")])])],2),e._v(" "),e.$slots.more?n("a",{staticClass:"td-more-arrow",class:{"td-more-arrow--down":e.moreVisible},attrs:{href:"javascript:;"},on:{click:e.handleMoreToggle}},[n("td-icon",{attrs:{type:"arrow-drop"}})],1):e._e()]):e._e(),e._v(" "),e.$slots.more&&e.moreVisible?n("div",{ref:"more",staticClass:"td-dialog__more"},[e._t("more")],2):e._e()])])},staticRenderFns:[],name:"td-dialog",components:{TdIcon:i.default,TdButton:r.default},props:{customClass:[Array,Object,String],visible:{type:Boolean,default:!1},fullscreen:{type:Boolean,default:!1},beforeClose:Function,footerEnabled:{type:Boolean,default:!0}},data:function(){return{moreVisible:!1,moreHeight:0}},mounted:function(){document.body.appendChild(this.$el)},destroyed:function(){Object(o.c)(this.$el.parentNode)&&this.$el.parentNode.removeChild(this.$el)},methods:{handleClose:function(){var e=this;Object(o.f)(!this.beforeClose).catch(function(){return e.beforeClose()}).then(function(){e.$emit("update:visible",!1),e.$emit("close"),e.fullscreen&&Object(o.d)()&&window.close()})},handleOk:function(){this.$emit("ok")},handleMoreToggle:function(){var e=this;this.moreVisible=!this.moreVisible,this.$emit("more-toggle",this.moreVisible),this.fullscreen&&Object(o.d)()&&this.$nextTick(function(){0===e.moreHeight&&(e.moreHeight=e.$refs.more.clientHeight+30),window.resizeBy(0,e.moreVisible?e.moreHeight:-e.moreHeight)})}}},a={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-dialog",e._g(e._b({},"td-dialog",e.$attrs,!1),e.$listeners),[n("div",{staticClass:"td-dialog-comfirm"},[n("span",{staticClass:"td-dialog-comfirm__icon"},[n("td-icon",{attrs:{type:e.icon[e.type]}})],1),e._v(" "),n("div",{staticClass:"td-dialog-comfirm__content"},[n("p",{staticClass:"td-dialog-comfirm__title"},[e._v(e._s(e.title))]),e._v(" "),n("p",{staticClass:"td-dialog-comfirm__text"},[e._t("default")],2)])]),e._v(" "),n("template",{slot:"footer"},[n("td-button",{on:{click:function(t){return e.$emit("ok")}}},[e._v(e._s(e.okText))]),e._v(" "),n("td-button",{attrs:{secondary:""},on:{click:function(t){return e.$emit("cancel")}}},[e._v(e._s(e.cancelText))])],1)],2)},staticRenderFns:[],name:"td-confirm",components:{TdButton:r.default,TdDialog:s},props:{title:String,type:{type:String,default:"info"},okText:{type:String,default:"确定"},cancelText:{type:String,default:"取消"}},data:function(){return{icon:{info:"question",warning:"warning",error:"error"}}}};s.install=function(e){e.component(s.name,s),e.component(a.name,a)},t.default=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderChannelList=void 0,function(e){e.channelBase="ChannelBase",e.channelRMNewTaskSetTaskData=e.channelBase+"1",e.channelRMPreNewTaskSetTaskData=e.channelBase+"2",e.channelRMNewTaskCreateNewTask=e.channelBase+"3",e.channelRMNewTaskSetBTInfo=e.channelBase+"4",e.channelRMNewTaskDownloadTorrent=e.channelBase+"5",e.channelRMNewTaskCreateBtTask=e.channelBase+"6",e.channelRMNewTaskCancleMagnet=e.channelBase+"7",e.channelRMImportTorrent=e.channelBase+"8",e.channelRMGetConfigValueResolve=e.channelBase+"9",e.channelRMGetConfigValueReject=e.channelBase+"10",e.channelMRTrayMenuClick=e.channelBase+"11",e.channelMRNewTaskMagnetTaskCreated=e.channelBase+"12",e.channelMRNewTaskDownloadTorrentResult=e.channelBase+"13",e.channelMRNewTaskCreateNewTaskResult=e.channelBase+"14",e.channelMRNewTaskCreateBtTaskResult=e.channelBase+"15",e.channelMRGetConfigValue=e.channelBase+"16",e.channelMRSetConfigValue=e.channelBase+"17",e.channelRMCommitPlanTask=e.channelBase+"18",e.channelRMPerformePlanTask=e.channelBase+"19",e.channelRMProcessSend=e.channelBase+"20",e.channelRMGetPrivateSpaceInfo=e.channelBase+"21",e.channelMRGetPrivateSpaceInfoResult=e.channelBase+"22",e.channelRMFileCopy=e.channelBase+"23",e.channelRMFileMove=e.channelBase+"24",e.channelMRFileCopyResult=e.channelBase+"25",e.channelMRFileMoveResult=e.channelBase+"26",e.channelRMGetSutitleByCid=e.channelBase+"27",e.channelMRGetSutitleByCidResult=e.channelBase+"28",e.channelRMGetSutitleByName=e.channelBase+"29",e.channelMRGetSutitleByNameResult=e.channelBase+"30",e.channelRMDownloadSutitle=e.channelBase+"31",e.channelMRDownloadSutitleSuc=e.channelBase+"32",e.channelMRDownloadSutitleFail=e.channelBase+"33",e.channelRMGetDisplayName=e.channelBase+"34",e.channelMRGetDisplayNameResult=e.channelBase+"35",e.channelMRBringWindowToTop=e.channelBase+"36",e.channelRMDownloadXmp=e.channelBase+"37",e.channelMRFixXmpSuc=e.channelBase+"38",e.channelMRFixXMPFail=e.channelBase+"39",e.channelMRAPlayerCreated=e.channelBase+"41",e.channelMRMainRendererNativeCallInited=e.channelBase+"42",e.channelMRShowWindow=e.channelBase+"43",e.channelMRCreateTopView=e.channelBase+"44",e.channelMRSendTopView=e.channelBase+"44.1",e.channelMRDestroyTopView=e.channelBase+"44.5",e.channelMRCreateTopViewReadyFinish=e.channelBase+"45",e.channelMRCreateTopViewFinish=e.channelBase+"45.5",e.channelMRCreateCtrlView=e.channelBase+"46",e.channelMRSendCtrlView=e.channelBase+"46.1",e.channelMRDestroyCtrlView=e.channelBase+"46.5",e.channelMRCreateCtrlViewReadyFinish=e.channelBase+"47",e.channelMRCreateCtrlViewFinish=e.channelBase+"47.5",e.channelMRCreateSideBar=e.channelBase+"48",e.channelMRCreateSideBarFinish=e.channelBase+"49",e.channelMRExpandListWindow=e.channelBase+"52",e.channelMRExpandListVue=e.channelBase+"53",e.channelMRSetChangeViewSize=e.channelBase+"54",e.channelMRSyncShowMode=e.channelBase+"55",e.channelMRAPWindowUpdate=e.channelBase+"56",e.channelMRUpdateAPlayerPos=e.channelBase+"57",e.channelMRXmpVideoTipCreated=e.channelBase+"58",e.channelMRXmpVideoTipDestroy=e.channelBase+"59",e.channelMRShowSettingCenterWindow=e.channelBase+"60",e.channelRMSetPosition=e.channelBase+"61",e.channelRMShowPlaySettingWindow=e.channelBase+"62",e.channelRMShowCursor=e.channelBase+"63",e.channelRMAPlayerState=e.channelBase+"64",e.channelRMAPlayerProgress=e.channelBase+"65",e.channelRMPlayAction=e.channelBase+"66",e.channelRMSetFoucs=e.channelBase+"67",e.channelMRSetZorder=e.channelBase+"68",e.channelRMGetBrowserStartType=e.channelBase+"69",e.channelMRGetBrowserStartTypeResult=e.channelBase+"70",e.channelMRWindowPopUp=e.channelBase+"71",e.channelMRUpdateTipWindowZorder=e.channelBase+"72",e.channelMRSetShadowWindowResize=e.channelBase+"73",e.channelMRBrowserWindowChange=e.channelBase+"74",e.channelMRChangeFullScreen=e.channelBase+"75",e.channelMRTabNumberChange=e.channelBase+"76",e.channelMRThumbTaskBarAction=e.channelBase+"77",e.channelMRThumbTaskBarButtonStatus=e.channelBase+"78",e.channelRMOpenFolder=e.channelBase+"79",e.channelRReCreateCtrlWindow=e.channelBase+"80",e.channelRReCreateTopWindow=e.channelBase+"81",e.channelRMSetEnvironmentVariable=e.channelBase+"82",e.channelRMServerStarted=e.channelBase+"83",e.channelRMOpenDevTools=e.channelBase+"84",e.channelEnterEditMode=e.channelBase+"85",e.channelUpdateEditRect=e.channelBase+"86",e.channelMessageBoxClose=e.channelBase+"87",e.channelPreventSleep=e.channelBase+"88",e.channelCancelPreventSleep=e.channelBase+"89",e.channelCloseEffectWindow=e.channelBase+"90",e.channelPopUpMenu=e.channelBase+"91",e.channelHideTray=e.channelBase+"92",e.channelEmbedMoveResize=e.channelBase+"94",e.channelTimeTipPos=e.channelBase+"95",e.channelUpdateVideoTip=e.channelBase+"96",e.channelGetBrowserView=e.channelBase+"97",e.channelGetBrowserViewResult=e.channelBase+"98",e.channelBrowserViewLoad=e.channelBase+"99",e.channelBrowserViewOpenDev=e.channelBase+"100",e.channelApplyBrowserView=e.channelBase+"101",e.channelCreateBrowserView=e.channelBase+"102",e.channelCreateBrowserViewResult=e.channelBase+"103",e.channelBrowserViewCall=e.channelBase+"104",e.channelBrowserViewCallRet=e.channelBase+"105",e.channelEmbedWindowRgn=e.channelBase+"106",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},function(e,t){e.exports=require("net")},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderUtil=void 0;const r=n(8),o=n(5),s=n(0),a=n(41),l=n(26),c=n(1),u=n(18),d=s.default.getLogger("Thunder.Util"),h="Thunder Network\\Xmp6\\";function f(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(){let e=l.ThunderHelper.getSystemTempPath(),t=l.ThunderHelper.getLogicalDriveStrings(),n=0;for(let i=0;i<t.length;i++){let r=l.ThunderHelper.getDriveInfo(t[i]);3===r.type&&n<r.freeBytes&&t[i]!==e&&(n=r.freeBytes,e=t[i])}return e.substring(0,1)+":\\迅雷下载"}function s(e){let t="00";return t=e>=10?e.toString():"0"+e.toString()}function p(e){let t=(e.style.webkitTransform||getComputedStyle(e,"").getPropertyValue("-webkit-transform")||e.style.transform||getComputedStyle(e,"").getPropertyValue("transform")).match(/\-?[0-9]+\.?[0-9]*/g);return{x:parseInt(t&&(t[12]||t[4])||"0",10),y:parseInt(t&&(t[13]||t[5])||"0",10)}}function m(e){let t=null;do{if(void 0===e||null===e)break;t=e.match(/[\/]?([^?]*)\?([^\s]*)/)?RegExp.$2:""}while(0);return t}function v(e){let t={};do{if(void 0===e||null===e)break;let n=/([^&=?]+)=([^&]*)/g;for(;n.exec(e);)t[RegExp.$1]=RegExp.$2}while(0);return t}e.formatSize=function(e,t){t=t||2;let n="0B";if("number"==typeof e&&e>0){let i=["B","KB","MB","GB","TB"],r=0,o=e;for(;o>=1e3&&!(r>=4);)o/=1024,r+=1;n=-1===String(o).indexOf(".")?o+i[r]:o.toFixed(t)+i[r]}return n},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.getConfigValueAW=function(e,t,n){return i(this,void 0,void 0,function*(){return c.client.callServerFunction(u.ConfigFunction.getConfigValue,e,t,n)})},e.getDefaultDownloadDir=t,e.getMaxFreeDriver=function(){return t().substring(0,1)},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){d.warning(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",i=!1,r=[],o=e.length;for(let s=0;s<o;s++){let a=e[s];if('"'!==a&&"'"!==a||(""===n?(i=!0,n=a):n===a&&(i=!1,n=""))," "!==a||i||s===o-1){if(s===o-1){let n=e.substring(t);""!==n.trim()&&r.push(f(n))}}else{let n=e.substring(t,s);""!==n.trim()&&r.push(f(n)),t=s+1}}return r},e.getXmpTempPath=function(e,t){return i(this,void 0,void 0,function*(){const i=yield Promise.resolve().then(()=>n(34));let r=o.join(i.tmpdir(),h);return t&&(r=o.join(r,t)),void 0!==e&&e&&(yield a.FileSystemAWNS.mkdirsAW(r)),r})},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,i)=>{e+=0===i?"?":"&",e+=`${n}=${encodeURIComponent(t[n])}`}),e},e.getQueryString=function(e,t){let n="";if(e&&t){n=e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:"";try{n=decodeURIComponent(n)}catch(e){}}return n},e.isClipboardTextFormatAvailable=function(){let e=!1,t=r.clipboard.availableFormats();for(let n of t)if("text/plain"===n){e=!0;break}return e},e.resizeToFitContent=function(){let e=document.querySelector(".td-dialog");window.resizeTo(e.offsetWidth,e.offsetHeight)},e.keywordsHighLight=function(e,t,n){if(!e)return"";if(!t)return e;if(0===e.length)return e;if(0===t.length)return e;let i=/\\/,r=t.split(" ");if(0===(r=r.filter(e=>e.trim().length>0)).length)return e;for(let t=0;t<r.length;t++)if(r[t].search(i)>0)return e;n=void 0===n||null===n?"#FF0000":n;let o="",s=["\\[","\\^","\\*","\\(","\\)","\\|","\\?","\\$","\\.","\\+"],a="",l="|";for(let e=0;e<r.length;e++){for(let t=0;t<s.length;t++){let n=new RegExp(s[t],"g");r[e]=r[e].replace(n,s[t])}e===r.length-1&&(l=""),a=a.concat(r[e],l)}let c=new RegExp(a,"gi");return o=e.replace(c,e=>'<span style= "color:'+n+'">'+e+"</span>")},e.convertTimeToMinutes=function(e,t,n){return 3600*e+60*t+n},e.convertMinuteToTime=function(e){return[Math.floor(e/3600),Math.floor(e/60%60),Math.floor(e%60)]},e.formatTimeNumber=s,e.createGUID=function(){let e;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t,n)=>(e=16*Math.random()|0,("x"===t?e:3&e|8).toString(16)))},e.formatDate=function(e,t){let n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(let t in n)new RegExp("("+t+")").test(e)&&(e=e.replace(RegExp.$1,1===RegExp.$1.length?n[t]:("00"+n[t]).substr((""+n[t]).length)));return e},e.formatMilliSecondTime2=function(e){let t=((e=Math.floor(e))%1e3).toString();1===t.length?t="00"+t:2===t.length&&(t="0"+t),e=Math.floor(e/1e3);let n=[];for(;e>0;){let t=Math.floor(e/60);if(n.push(e-60*t),e=t,n.length>=3)break}let i="";return i=1===n.length?s(n[0])+"秒":2===n.length?s(n[1])+"分"+s(n[0])+"秒":3===n.length?s(n[2])+"时"+s(n[1])+"分"+s(n[0])+"秒":"0秒"},e.formatMilliSecondTime=function(e,t,n=":"){let i=((e=Math.floor(e))%1e3).toString();1===i.length?i="00"+i:2===i.length&&(i="0"+i),e=Math.floor(e/1e3);let r=[];for(;e>0;){let t=Math.floor(e/60);if(r.push(e-60*t),e=t,r.length>=3)break}let o="";return o=1===r.length?`00${n}00${n}`+s(r[0]):2===r.length?`00${n}`+s(r[1])+`${n}`+s(r[0]):3===r.length?s(r[2])+n+s(r[1])+n+s(r[0]):`00${n}00${n}00`,t&&(o+=n+i),o},e.formatSeconds=function(e){let t="";do{if(e<=0){t="00分00秒";break}let n=Math.floor(e/3600),i=Math.floor(e/60)%60,r=Math.floor(e%60);n>0&&(t=n<10?"0"+n+"时":n+"时"),t=(t+=i<10?"0"+i+"分":i+"分")+(r<10?"0"+r:""+r)+"秒"}while(0);return t},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setStyle=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style[t]=n})},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),i=t.split("."),r=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(i[e])>0){r=1;break}if(Number(n[e])-Number(i[e])<0){r=-1;break}}return r},e.throttle=function(e,t){let n,i=0;return(...r)=>{const o=Date.now();clearTimeout(n),o-i>t?(e(...r),i=o):n=setTimeout(()=>{e(...r),i=o},t)}},e.getElementFixed=function(e){let t=e.offsetLeft,n=e.offsetTop,i=e.offsetParent;for(;null!==i;){let e=p(i);t+=i.offsetLeft+e.x,n+=i.offsetTop+e.y,i=i.offsetParent}return{x:t,y:n}},e.getElementRelative=function(e){let t=e.offsetLeft,n=e.offsetTop,i=e.offsetParent;for(;null!==i;)t+=i.offsetLeft,n+=i.offsetTop,i=i.offsetParent;return{x:t,y:n}},e.getCmdParam=function(e,t,n){if(!e)return{};let i,r={};for(let o=0;o<e.length;o++)if(e[o].includes(t)){r.key=t,r.index=o,n&&(i=e[o].split(n))&&(r.val=i[1]);break}return r},e.parseDynamicUrlPath=m,e.parseDynamicUrlArgs=v,e.getUrlArgs=function(e){return v(m(e))},e.debounce=function(e,t){let n=null;return(...i)=>{n&&clearTimeout(n),n=setTimeout(()=>{e(...i)},t)}},e.implode=function(e,t){let n="";return e.forEach(e=>{""===n?n=e:n+=t+e}),n}}(t.ThunderUtil||(t.ThunderUtil={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DisplayCardInfo=t.DetailIndex=t.PanelID=t.XmpPlaySoure=t.WindowParams=t.CombineRgnType=t.ZOrderChangedType=t.WindowTopMode=t.XmpMode=t.ShowWindowCmd=t.VirtualKeyCode=t.HitTestPositionCode=t.GWCmd=t.WindowMessage=t.SetWindowType=t.WindowStyleEx=t.WindowStyle=t.CmdShow=t.Uflag=t.OptionOfHWNDInAfter=void 0,function(e){e[e.HWND_NOTOPMOST=-2]="HWND_NOTOPMOST",e[e.HWND_TOPMOST=-1]="HWND_TOPMOST",e[e.HWND_TOP=0]="HWND_TOP",e[e.HWND_BOTTOM=1]="HWND_BOTTOM"}(t.OptionOfHWNDInAfter||(t.OptionOfHWNDInAfter={})),function(e){e[e.SWP_ASYNCWINDOWPOS=16384]="SWP_ASYNCWINDOWPOS",e[e.SWP_DEFERERASE=8192]="SWP_DEFERERASE",e[e.SWP_DRAWFRAME=32]="SWP_DRAWFRAME",e[e.SWP_FRAMECHANGED=32]="SWP_FRAMECHANGED",e[e.SWP_HIDEWINDOW=128]="SWP_HIDEWINDOW",e[e.SWP_NOACTIVATE=16]="SWP_NOACTIVATE",e[e.SWP_NOCOPYBITS=256]="SWP_NOCOPYBITS",e[e.SWP_NOMOVE=2]="SWP_NOMOVE",e[e.SWP_NOOWNERZORDER=512]="SWP_NOOWNERZORDER",e[e.SWP_NOREDRAW=8]="SWP_NOREDRAW",e[e.SWP_NOREPOSITION=512]="SWP_NOREPOSITION",e[e.SWP_NOSENDCHANGING=1024]="SWP_NOSENDCHANGING",e[e.SWP_NOSIZE=1]="SWP_NOSIZE",e[e.SWP_NOZORDER=4]="SWP_NOZORDER",e[e.SWP_SHOWWINDOW=64]="SWP_SHOWWINDOW"}(t.Uflag||(t.Uflag={})),function(e){e[e.SW_FORCEMINIMIZE=11]="SW_FORCEMINIMIZE",e[e.SW_HIDE=0]="SW_HIDE",e[e.SW_MAXIMIZE=3]="SW_MAXIMIZE",e[e.SW_MINIMIZE=6]="SW_MINIMIZE",e[e.SW_RESTORE=9]="SW_RESTORE",e[e.SW_SHOW=5]="SW_SHOW",e[e.SW_SHOWDEFAULT=10]="SW_SHOWDEFAULT",e[e.SW_SHOWMAXIMIZED=3]="SW_SHOWMAXIMIZED",e[e.SW_SHOWMINIMIZED=2]="SW_SHOWMINIMIZED",e[e.SW_SHOWMINNOACTIVE=7]="SW_SHOWMINNOACTIVE",e[e.SW_SHOWNA=8]="SW_SHOWNA",e[e.SW_SHOWNOACTIVATE=4]="SW_SHOWNOACTIVATE",e[e.SW_SHOWNORMAL=1]="SW_SHOWNORMAL"}(t.CmdShow||(t.CmdShow={})),function(e){e[e.WS_BORDER=8388608]="WS_BORDER",e[e.WS_CAPTION=12582912]="WS_CAPTION",e[e.WS_CHILD=1073741824]="WS_CHILD",e[e.WS_CHILDWINDOW=1073741824]="WS_CHILDWINDOW",e[e.WS_CLIPCHILDREN=33554432]="WS_CLIPCHILDREN",e[e.WS_CLIPSIBLINGS=67108864]="WS_CLIPSIBLINGS",e[e.WS_POPUP=2147483648]="WS_POPUP",e[e.WS_THICKFRAME=262144]="WS_THICKFRAME"}(t.WindowStyle||(t.WindowStyle={})),function(e){e[e.WS_EX_TOOLWINDOW=128]="WS_EX_TOOLWINDOW",e[e.WS_EX_APPWINDOW=262144]="WS_EX_APPWINDOW",e[e.WS_EX_TOPMOST=8]="WS_EX_TOPMOST",e[e.WS_EX_WINDOWEDGE=256]="WS_EX_WINDOWEDGE",e[e.WS_EX_NOACTIVATE=134217728]="WS_EX_NOACTIVATE"}(t.WindowStyleEx||(t.WindowStyleEx={})),function(e){e[e.GWL_EXSTYLE=-20]="GWL_EXSTYLE",e[e.GWL_HINSTANCE=-6]="GWL_HINSTANCE",e[e.GWL_ID=-12]="GWL_ID",e[e.GWL_STYLE=-16]="GWL_STYLE",e[e.GWL_USERDATA=-21]="GWL_USERDATA",e[e.GWL_WNDPROC=-4]="GWL_WNDPROC"}(t.SetWindowType||(t.SetWindowType={})),function(e){e[e.WM_CREATE=1]="WM_CREATE",e[e.WM_DESTROY=2]="WM_DESTROY",e[e.WM_MOVE=3]="WM_MOVE",e[e.WM_SIZE=5]="WM_SIZE",e[e.WM_ACTIVATE=6]="WM_ACTIVATE",e[e.WM_SETFOCUS=7]="WM_SETFOCUS",e[e.WM_KILLFOCUS=8]="WM_KILLFOCUS",e[e.WM_ENABLE=10]="WM_ENABLE",e[e.WM_KEYDOWN=256]="WM_KEYDOWN",e[e.WM_KEYUP=257]="WM_KEYUP",e[e.WM_SYSKEYDOWN=260]="WM_SYSKEYDOWN",e[e.WM_MOUSEMOVE=512]="WM_MOUSEMOVE",e[e.WM_SETCURSOR=32]="WM_SETCURSOR",e[e.WM_LBUTTONDOWN=513]="WM_LBUTTONDOWN",e[e.WM_LBUTTONUP=514]="WM_LBUTTONUP",e[e.WM_LBUTTONDBLCLK=515]="WM_LBUTTONDBLCLK",e[e.WM_RBUTTONUP=517]="WM_RBUTTONUP",e[e.WM_MOUSEWHEEL=522]="WM_MOUSEWHEEL",e[e.WM_MOUSELEAVE=675]="WM_MOUSELEAVE",e[e.WM_DPICHANGED=736]="WM_DPICHANGED",e[e.WM_GETMINMAXINFO=36]="WM_GETMINMAXINFO",e[e.WM_NCHITTEST=132]="WM_NCHITTEST",e[e.WM_NCMOUSEMOVE=160]="WM_NCMOUSEMOVE",e[e.WM_NCLBUTTONDOWN=161]="WM_NCLBUTTONDOWN",e[e.WM_NCLBUTTONUP=162]="WM_NCLBUTTONUP",e[e.WM_NCLBUTTONDBLCLK=163]="WM_NCLBUTTONDBLCLK",e[e.WM_NCRBUTTONDOWN=164]="WM_NCRBUTTONDOWN",e[e.WM_NCRBUTTONUP=165]="WM_NCRBUTTONUP",e[e.WM_NCRBUTTONDBLCLK=166]="WM_NCRBUTTONDBLCLK",e[e.WM_NCMBUTTONDOWN=167]="WM_NCMBUTTONDOWN",e[e.WM_NCMBUTTONUP=168]="WM_NCMBUTTONUP",e[e.WM_NCMBUTTONDBLCLK=169]="WM_NCMBUTTONDBLCLK",e[e.WM_WINDOWPOSCHANGED=71]="WM_WINDOWPOSCHANGED",e[e.WM_WINDOWPOSCHANGING=70]="WM_WINDOWPOSCHANGING",e[e.WM_ACTIVATEAPP=28]="WM_ACTIVATEAPP",e[e.WM_DWMCOMPOSITIONCHANGED=798]="WM_DWMCOMPOSITIONCHANGED"}(t.WindowMessage||(t.WindowMessage={})),function(e){e[e.GW_HWNDFIRST=0]="GW_HWNDFIRST",e[e.GW_HWNDLAST=1]="GW_HWNDLAST",e[e.GW_HWNDNEXT=2]="GW_HWNDNEXT",e[e.GW_HWNDPREV=3]="GW_HWNDPREV",e[e.GW_OWNER=4]="GW_OWNER",e[e.GW_CHILD=5]="GW_CHILD",e[e.GW_ENABLEDPOPUP=6]="GW_ENABLEDPOPUP"}(t.GWCmd||(t.GWCmd={})),function(e){e[e.HTERROR=-2]="HTERROR",e[e.HTTRANSPARENT=-1]="HTTRANSPARENT",e[e.HTNOWHERE=0]="HTNOWHERE",e[e.HTCLIENT=1]="HTCLIENT",e[e.HTCAPTION=2]="HTCAPTION",e[e.HTSYSMENU=3]="HTSYSMENU",e[e.HTGROWBOX=4]="HTGROWBOX",e[e.HTSIZE=4]="HTSIZE",e[e.HTMENU=5]="HTMENU",e[e.HTHSCROLL=6]="HTHSCROLL",e[e.HTVSCROLL=7]="HTVSCROLL",e[e.HTMINBUTTON=8]="HTMINBUTTON",e[e.HTMAXBUTTON=9]="HTMAXBUTTON",e[e.HTLEFT=10]="HTLEFT",e[e.HTRIGHT=11]="HTRIGHT",e[e.HTTOP=12]="HTTOP",e[e.HTTOPLEFT=13]="HTTOPLEFT",e[e.HTTOPRIGHT=14]="HTTOPRIGHT",e[e.HTBOTTOM=15]="HTBOTTOM",e[e.HTBOTTOMLEFT=16]="HTBOTTOMLEFT",e[e.HTBOTTOMRIGHT=17]="HTBOTTOMRIGHT",e[e.HTBORDER=18]="HTBORDER",e[e.HTREDUCE=8]="HTREDUCE",e[e.HTZOOM=9]="HTZOOM",e[e.HTSIZEFIRST=10]="HTSIZEFIRST",e[e.HTSIZELAST=17]="HTSIZELAST",e[e.HTOBJECT=19]="HTOBJECT",e[e.HTCLOSE=20]="HTCLOSE",e[e.HTHELP=21]="HTHELP"}(t.HitTestPositionCode||(t.HitTestPositionCode={})),function(e){e[e.VK_LBUTTON=1]="VK_LBUTTON",e[e.VK_RBUTTON=2]="VK_RBUTTON",e[e.VK_MBUTTON=4]="VK_MBUTTON",e[e.VK_BACK=8]="VK_BACK",e[e.VK_TAB=9]="VK_TAB",e[e.VK_RETURN=13]="VK_RETURN",e[e.VK_SHIFT=16]="VK_SHIFT",e[e.VK_CONTROL=17]="VK_CONTROL",e[e.VK_MENU=18]="VK_MENU",e[e.VK_ESCAPE=27]="VK_ESCAPE",e[e.VK_SPACE=32]="VK_SPACE",e[e.VK_PRIOR=33]="VK_PRIOR",e[e.VK_NEXT=34]="VK_NEXT",e[e.VK_END=35]="VK_END",e[e.VK_LEFT=37]="VK_LEFT",e[e.VK_UP=38]="VK_UP",e[e.VK_RIGHT=39]="VK_RIGHT",e[e.VK_DOWN=40]="VK_DOWN",e[e.VK_DELETE=46]="VK_DELETE",e[e.VK_F1=112]="VK_F1",e[e.VK_F2=113]="VK_F2",e[e.VK_F3=114]="VK_F3",e[e.VK_F4=115]="VK_F4",e[e.VK_F5=116]="VK_F5",e[e.VK_F6=117]="VK_F6",e[e.VK_F7=118]="VK_F7",e[e.VK_F8=119]="VK_F8",e[e.VK_F9=120]="VK_F9",e[e.VK_F10=121]="VK_F10",e[e.VK_F11=122]="VK_F11",e[e.VK_F12=123]="VK_F12",e[e.VK_OEM_1=186]="VK_OEM_1",e[e.VK_OEM_PLUS=187]="VK_OEM_PLUS",e[e.VK_OEM_COMMA=188]="VK_OEM_COMMA",e[e.VK_OEM_MINUS=189]="VK_OEM_MINUS",e[e.VK_OEM_PERIOD=190]="VK_OEM_PERIOD",e[e.VK_OEM_2=191]="VK_OEM_2",e[e.VK_OEM_3=192]="VK_OEM_3",e[e.VK_OEM_4=219]="VK_OEM_4",e[e.VK_OEM_5=220]="VK_OEM_5",e[e.VK_OEM_6=221]="VK_OEM_6",e[e.VK_OEM_7=222]="VK_OEM_7",e[e.VK_PROCESSKEY=229]="VK_PROCESSKEY"}(t.VirtualKeyCode||(t.VirtualKeyCode={})),function(e){e[e.SW_HIDE=0]="SW_HIDE",e[e.SW_SHOWMAXIMIZED=3]="SW_SHOWMAXIMIZED",e[e.SW_SHOW=5]="SW_SHOW"}(t.ShowWindowCmd||(t.ShowWindowCmd={})),function(e){e[e.UNKNOWN_MODE=-1]="UNKNOWN_MODE",e[e.INDEPENDENT_MODE=0]="INDEPENDENT_MODE",e[e.MAGNETIC_MODE=1]="MAGNETIC_MODE",e[e.EMBED_MODE=2]="EMBED_MODE"}(t.XmpMode||(t.XmpMode={})),function(e){e[e.NO_TOPMOST_MODE=0]="NO_TOPMOST_MODE",e[e.PLAYING_TOPMOST_MODE=1]="PLAYING_TOPMOST_MODE",e[e.ALWAYLS_TOPMOST_MODE=2]="ALWAYLS_TOPMOST_MODE"}(t.WindowTopMode||(t.WindowTopMode={})),function(e){e[e.ZORDER_CHANGED_TYPE_UNKNOW=-1]="ZORDER_CHANGED_TYPE_UNKNOW",e[e.ZORDER_CHANGED_TYPE_BEFORE=0]="ZORDER_CHANGED_TYPE_BEFORE",e[e.ZORDER_CHANGED_TYPE_AFTER=1]="ZORDER_CHANGED_TYPE_AFTER"}(t.ZOrderChangedType||(t.ZOrderChangedType={})),function(e){e[e.RGN_AND=1]="RGN_AND",e[e.RGN_OR=2]="RGN_OR",e[e.RGN_XOR=3]="RGN_XOR",e[e.RGN_DIFF=4]="RGN_DIFF",e[e.RGN_COPY=5]="RGN_COPY"}(t.CombineRgnType||(t.CombineRgnType={})),function(e){e[e.DEFAULT_WIDTH=850]="DEFAULT_WIDTH",e[e.DEFAULT_HEIGHT=550]="DEFAULT_HEIGHT",e[e.MIN_WINDOW_WIDTH=350]="MIN_WINDOW_WIDTH",e[e.MIN_WINDOW_HEIGHT=200]="MIN_WINDOW_HEIGHT",e[e.TOPCTRL_HEIGHT=42]="TOPCTRL_HEIGHT",e[e.PLAYCTRL_HEIGHT=62]="PLAYCTRL_HEIGHT"}(t.WindowParams||(t.WindowParams={})),function(e){e[e.PLAY_UNKNOWN=0]="PLAY_UNKNOWN",e[e.PLAY_BY_DOWNLOAD=1]="PLAY_BY_DOWNLOAD",e[e.PLAY_BY_PAN=2]="PLAY_BY_PAN"}(t.XmpPlaySoure||(t.XmpPlaySoure={})),function(e){e.Download="download-panel",e.Cloud="pan-plugin-view",e.Browser="find-page",e.Message="message-page"}(t.PanelID||(t.PanelID={})),function(e){e.Preview="Preview",e.Speed="TaskChart",e.Attribute="Attribute"}(t.DetailIndex||(t.DetailIndex={}));t.DisplayCardInfo=class{}},function(e,t,n){var i=n(19),r=n(6).document,o=i(r)&&i(r.createElement);e.exports=function(e){return o?r.createElement(e):{}}},function(e,t,n){var i=n(19);e.exports=function(e,t){if(!i(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!i(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")}},function(e,t){var n=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:n)(e)}},function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,n){var i=n(15),r=n(175),o=n(71),s=n(69)("IE_PROTO"),a=function(){},l=function(){var e,t=n(64)("iframe"),i=o.length;for(t.style.display="none",n(112).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),l=e.F;i--;)delete l.prototype[o[i]];return l()};e.exports=Object.create||function(e,t){var n;return null!==e?(a.prototype=i(e),n=new a,a.prototype=null,n[s]=e):n=l(),void 0===t?n:r(n,t)}},function(e,t,n){var i=n(70)("keys"),r=n(48);e.exports=function(e){return i[e]||(i[e]=r(e))}},function(e,t,n){var i=n(3),r=n(6),o=r["__core-js_shared__"]||(r["__core-js_shared__"]={});(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:i.version,mode:n(36)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){n(179);for(var i=n(6),r=n(21),o=n(30),s=n(7)("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),l=0;l<a.length;l++){var c=a[l],u=i[c],d=u&&u.prototype;d&&!d[s]&&r(d,s,c),o[c]=o.Array}},function(e,t,n){"use strict";var i=n(46);e.exports.f=function(e){return new function(e){var t,n;this.promise=new e(function(e,i){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=i}),this.resolve=i(t),this.reject=i(n)}(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ModeChangeEvent=t.ClientProcessEvent=t.ZipEvent=t.XmpEvent=void 0,function(e){e.onWindowMessage="OnWindowMessage",e.onStateChanged="OnStateChanged",e.onCreateMediaError="OnCreateMediaError",e.onOpenSucceeded="OnOpenSucceeded",e.onVideoSizeChanged="OnVideoSizeChanged",e.onAPlayerCreated="OnAPlayerCreated",e.onAllStoped="OnAllStoped",e.onSeekCompleted="OnSeekCompleted",e.onBuffer="OnBuffer",e.onDownloadCodec="OnDownloadCodec",e.onDownloadCodecProgress="OnDownloadCodecProgress",e.onDropFiles="OnDropFiles",e.onEvent="OnEvent",e.onGetPlayUrl="OnGetPlayUrl",e.onOpenFailed="OnOpenFailed",e.onPlayFailed="OnPlayFailed",e.onClearError="OnClearError",e.onError="OnError",e.onPlaying="OnPlaying",e.onPreOpen="OnPreOpen",e.onPaused="OnPaused",e.onPlayCompleted="OnPlayCompleted",e.onPreStop="OnPreStop",e.onStop="OnStop",e.onForceLoadingState="OnForceLoadingState",e.onVipStruckPlayState="OnVipStruckPlayState",e.onShowFloatWindow="OnShowFloatWindow",e.onTimeDesChange="OnTimeDesChange",e.onPositionChanged="OnPositionChanged",e.onSnapShotCreated="OnSnapShotCreated",e.onXmpPlayerEvent="OnXmpPlayerEvent",e.onXmpAplayerCrash="OnXmpAplayerCrash",e.onUpdateMediaCid="OnUpdateMediaCid",e.onUpdateMediaGcid="OnUpdateMediaGcid",e.onSpeedChange="OnSpeedChange",e.onFullScreenStateChange="OnFullScreenStateChange",e.onSetConfig="OnSetConfig",e.onVolumeChange="OnVolumeChange",e.onPlayStateChange="OnPlayStateChange",e.onCurViewChanged="OnCurViewChanged",e.onUserLogin="OnUserLogin",e.onDwmCompositionChanged="OnDwmCompositionChanged",e.onDpiChanged="OnDpiChanged",e.onGPUProcessCrashed="OnGPUProcessCrashed",e.onSetCurrentDevice="OnSetCurrentDevice",e.onXmpPlayerRemoveMedias="OnXmpPlayerRemoveMedias",e.onOpenMediaNotExist="OnOpenMediaNotExist",e.onSwitchModeError="OnSwitchModeError",e.onDlnaEvent="OnDlnaEvent",e.onSubtitleInit="OnSubtitleInit",e.onSubtitleUnInit="onSubtitleUnInit",e.onEmbedSubInitFinish="OnEmbedSubInitFinish",e.onEntertCurrentDevice="OnEntertCurrentDevice",e.onWindowZOrderChanged="OnWindowZOrderChanged",e.onAplayerSeekFrameChanged="OnAplayerSeekFrameChanged",e.onModeBarChange="OnModeBarChange",e.onThunderTaskRemove="OnThunderTaskRemove",e.onXmpClose="OnXmpClose",e.onXmpStartFailed="OnXmpStartFailed",e.onVipViewInitFinish="OnVipViewInitFinish",e.onModeEvent="OnModeEvent",e.onMaxmized="OnMaxmized",e.onUnMaxmized="OnUnMaxmized",e.onSetWindowTopMost="OnWindowTopMost",e.onCancelWindowTopMost="OnCancelWindowTopMost",e.onOpenOrCloseSettingCenter="OnOpenOrCloseSettingCenter",e.onUpdateBufferingUI="OnUpdateBufferingUI",e.onOpenSucceededCreateTask="OnOpenSucceededCreateTask"}(t.XmpEvent||(t.XmpEvent={})),function(e){e.listCompleted="OnZipListCompleted",e.listCompletedError="OnZipListCompletedError",e.indexCompleted="OnZipIndexCompleted",e.passWordError="OnZipPassWordError",e.indexProgress="OnZipIndexProgress",e.indexError="OnZipIndexError",e.indexProgressBegin="OnZipIndexProgressBegin"}(t.ZipEvent||(t.ZipEvent={})),function(e){e.onMainWindowSize="OnMainWindowSize",e.onTabSelectChange="OnTabSelectChange",e.onThunderQuit="OnThunderQuit"}(t.ClientProcessEvent||(t.ClientProcessEvent={})),function(e){e.onXmpModeChanging="OnXmpModeChanging",e.onXmpModeChanged="OnXmpModeChanged",e.onXmpEnterFullScreen="OnXmpEnterFullScreen",e.onXmpLeaveFullScreen="OnXmpLeaveFullScreen"}(t.ModeChangeEvent||(t.ModeChangeEvent={}))},function(e,t,n){t.f=n(7)},function(e,t,n){var i=n(6),r=n(3),o=n(36),s=n(75),a=n(14).f;e.exports=function(e){var t=r.Symbol||(r.Symbol=o?{}:i.Symbol||{});"_"==e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},,,function(e,t,n){"use strict";t.__esModule=!0,t.default=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t,n){"use strict";t.__esModule=!0;var i,r=n(128),o=(i=r)&&i.__esModule?i:{default:i};t.default=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),(0,o.default)(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}()},function(e,t,n){e.exports={default:n(203),__esModule:!0}},function(e,t,n){e.exports=n(283)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(97),r=n(11);t.Parser=class extends r.EventEmitter{constructor(){super(),this.decoder=new i.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,i=(t+=this.decoder.write(e)).indexOf("\n",n);for(;i>=0;){const e=t.slice(n,i),r=JSON.parse(e);this.emit("message",r),n=i+1,i=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},function(e,t){e.exports=require("buffer")},function(e,t,n){var i=n(66),r=Math.min;e.exports=function(e){return e>0?r(i(e),9007199254740991):0}},function(e,t,n){var i=n(37),r=n(7)("toStringTag"),o="Arguments"==i(function(){return arguments}());e.exports=function(e){var t,n,s;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),r))?n:o?i(t):"Object"==(s=i(t))&&"function"==typeof t.callee?"Arguments":s}},function(e,t){t.f=Object.getOwnPropertySymbols},,,function(e,t,n){"use strict";n.r(t);var i=n(53),r=n.n(i),o=n(10),s=n(4),a={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"show",rawName:"v-show",value:e.visible,expression:"visible"}],staticClass:"td-message",class:"td-message--"+e.type},[e._t("icon",[n("td-icon",{attrs:{type:e.type}})]),e._v(" "),n("span",{staticClass:"td-message__text"},[e._t("default",[e._v(e._s(e.message))])],2)],2)},staticRenderFns:[],name:"td-message",components:{TdIcon:o.default},props:{message:String,type:String,duration:{type:Number,default:3e3,validator:function(e){return e>=0}},position:{type:String,default:"top"}},data:function(){return{visible:!1}},mounted:function(){var e=document.querySelector(".td-cover.td-cover--message.is-"+this.position);Object(s.e)(e)&&((e=document.createElement("div")).className="td-cover td-cover--message is-"+this.position,document.body.appendChild(e)),e.appendChild(this.$el)},destroyed:function(){Object(s.c)(this.$el.parentNode)&&(window.clearTimeout(this.timer),this.timer=null,this.$el.parentNode.removeChild(this.$el))},methods:{show:function(){this.visible||(this.visible=!0,this.$emit("show")),this.timer&&clearTimeout(this.timer),this.duration&&(this.timer=setTimeout(this.hide,this.duration))},hide:function(){this.visible&&(this.visible=!1,this.timer&&(clearTimeout(this.timer),this.timer=null),this.$emit("hide"))}},beforeDestroy:function(){this.timer&&(clearTimeout(this.timer),this.timer=null)}},l={};a.install=function(e){e.prototype.$message=function(t){var n=e.extend(a),i={},o=t.slots;if(t&&"object"===(void 0===t?"undefined":r()(t)))for(var c in t)c in a.props&&(i[c]=t[c]);var u=new n({el:document.body.appendChild(document.createElement("div")),propsData:i});if(t.id){var d=t.id;l[d]||(l[d]=new s.a(t)),l[d].push(u)||u.$destroy()}if(o)for(var h in o)u.$slots[h]=o[h];return u.show(),u.$on("hide",function(){if(t.id){var e=t.id;if(l[e])l[e].clear()||delete l[e]}u.$destroy()}),u},["success","warning","error"].forEach(function(t){e.prototype.$message[t]=function(n){e.prototype.$message({type:t,position:"middle",message:n})}}),e.component(a.name,a)},t.default=a},function(e,t,n){"use strict";n.r(t);var i={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-carousel",on:{mouseenter:function(t){e.autoPlay&&e.clearAutoPlayListener()},mouseleave:function(t){e.autoPlay&&e.addAutoPlayListener()}}},[n("div",{staticClass:"td-carousel__container"},[n("ul",[e._t("default")],2)]),e._v(" "),e.showArrowButton?[n("span",{staticClass:"td-carousel__button td-carousel__button--left",on:{click:e.decreaseActiveIndex}},[n("i",{staticClass:"td-icon-arrow-left"})]),e._v(" "),n("span",{staticClass:"td-carousel__button td-carousel__button--right",on:{click:e.increaseActiveIndex}},[n("i",{staticClass:"td-icon-arrow-right"})])]:e._e(),e._v(" "),"preview"===e.indicatorType?n("div",{staticClass:"td-carousel__indicators td-carousel__indicators--image"},[n("ul",e._l(e.previewImageUrlList,function(t,i){return n("li",{key:t+i,staticClass:"td-carousel__indicator",class:{"is-active":i===e.activeIndex},on:{click:function(t){"click"===e.trigger&&(e.activeIndex=i)},mouseenter:function(t){"hover"===e.trigger&&(e.activeIndex=i)}}},[n("a",{attrs:{href:"javascript:;"}},[n("img",{attrs:{src:t}})])])}),0)]):"line"===e.indicatorType?n("div",{staticClass:"td-carousel__indicators"},[n("ul",e._l(e.carouselItemList,function(t,i){return n("li",{key:i,staticClass:"td-carousel__indicator",class:{"is-active":i===e.activeIndex},on:{click:function(t){"click"===e.trigger&&(e.activeIndex=i)},mouseenter:function(t){"hover"===e.trigger&&(e.activeIndex=i)}}})}),0)]):e._e()],2)},staticRenderFns:[],name:"td-carousel",props:{initIndex:{type:Number,default:0},trigger:{type:String,default:"hover",validator:function(e){return["click","hover"].includes(e)}},showArrowButton:{type:Boolean,default:!1},indicatorType:{type:String,default:"line",validator:function(e){return["none","preview","line"].includes(e)}},previewImageUrlList:{type:Array},autoPlay:{type:Boolean,default:!1},timeout:{type:Number,default:1e3}},data:function(){return{activeIndex:0,carouselItemList:[],autoPlayTimer:null}},mounted:function(){this.getCarouselItemList(),this.activeIndex=this.initIndex,this.setCarouselActiveByIndex(this.activeIndex),this.autoPlay&&this.addAutoPlayListener()},beforeDestroy:function(){this.clearAutoPlayListener()},watch:{$children:{immediate:!0,handler:function(){this.getCarouselItemList()}},activeIndex:{immediate:!0,handler:function(e){this.setCarouselActiveByIndex(e)}}},methods:{autoPlayCallback:function(){this.increaseActiveIndex()},addAutoPlayListener:function(){this.autoPlayTimer=window.setInterval(this.autoPlayCallback,this.timeout)},clearAutoPlayListener:function(){window.clearInterval(this.autoPlayTimer)},getCarouselItemList:function(){this.carouselItemList=this.$children.filter(function(e){return"td-carousel-item"===e.$options._componentTag})},setCarouselActiveByIndex:function(e){var t=this;e<0||e>=this.carouselItemList.length||this.carouselItemList.forEach(function(n,i){t.carouselItemList[i].active=!1,i===e&&(t.carouselItemList[i].active=!0)})},setCarouselItemActive:function(e){var t=this.carouselItemList.findIndex(function(t){return t===e});this.setCarouselActiveByIndex(t)},decreaseActiveIndex:function(){var e=this.carouselItemList.length;this.activeIndex=(this.activeIndex-1+e)%e},increaseActiveIndex:function(){var e=this.carouselItemList.length;this.activeIndex=(this.activeIndex+1)%e}},install:function(e){e.component(i.name,i)}};t.default=i},function(e,t,n){"use strict";n.r(t);var i=n(81),r=n.n(i),o={render:function(){var e=this.$createElement,t=this._self._c||e;return t("transition",{attrs:{name:"fade"}},[t("li",{directives:[{name:"show",rawName:"v-show",value:this.active,expression:"active"}],staticClass:"td-carousel__item",class:this.computeClass},[this._t("default")],2)])},staticRenderFns:[],name:"td-carousel-item",props:{customClass:{type:String,default:""}},data:function(){return{active:!1}},mounted:function(){this.$parent},computed:{computeClass:function(){return this.customClass.split(/\s+/).reduce(function(e,t){return"is-active"!==t&&(e[t]=!0),e},r()(null))}},install:function(e){e.component(o.name,o)}};t.default=o},function(e,t,n){e.exports={default:n(275),__esModule:!0}},function(e,t,n){"use strict";t.__esModule=!0;var i,r=n(123),o=(i=r)&&i.__esModule?i:{default:i};t.default=o.default||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(0),r=n(24),o=n(5),s=n(25).default(o.join(__rootDir,"../bin/ThunderHelper.node")),a=n(60),l=n(8);function c(){"console"===process.env.TL_OUTPUT?i.default.outputLogger=i.outputLoggerConsole:i.default.outputLogger=function(){function e(e){return function(...t){s.printEtwLog(e,function(...e){return e.map(e=>r.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[i.LogLevel.Critical]:e(i.LogLevel.Critical),[i.LogLevel.Error]:e(i.LogLevel.Error),[i.LogLevel.Warning]:e(i.LogLevel.Warning),[i.LogLevel.Information]:e(i.LogLevel.Information),[i.LogLevel.Verbose]:e(i.LogLevel.Verbose)}}()}function u(){i.default.enableLogger="1"===process.env.TL_ENABLE,c()}c(),"browser"===process.type?l.ipcMain.on(a.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{u()}):"renderer"===process.type&&l.ipcRenderer.on(a.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{u()})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(61),r=n(11),o=n(39),s=n(83),a=n(20);t.Client=class extends r.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=a.getSockPath(e.socketPrex);this.socket=i.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new s.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{o.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){o.error(e.message)}else o.information("This socket has been ended by the other party",this.context&&this.context.name)}}},function(e,t){e.exports=require("string_decoder")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const i=n(8),r=n(84),o=n(99),s=n(100),a=n(54),l=n(101),c=n(17),u=n(55),d=i.ipcRenderer,h=process.electronBinding("v8_util"),f=new o.default,p=h.createIDWeakMap(),m=h.getHiddenValue(global,"contextId");class v{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function g(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let i=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:g(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(i))return{type:"buffer",value:r.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(u.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(h.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:h.getHiddenValue(e,"__remote_id__")};let i={type:e instanceof v?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)i.members.push({name:t,value:n(e[t])});return t.delete(e),i}if("function"==typeof e){return{type:"function",id:f.add(e),location:h.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function y(e,t,n){u.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function _(e,t,n,i=!1){const r=t=>{if(e.hasOwnProperty(t.name)&&!i)return;let n,r={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(p.has(t.value.refId)&&(n=p.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=C(t.value,e,t.name);r.get=(()=>n),r.set=(e=>n=e)}else"get"===t.type&&(r.get=(()=>n),t.writable&&(r.set=(e=>{n=e})),n=C(t.value));Object.defineProperty(e,t.name,r)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)r(n[t])}}function b(e,t,n){if(n){let t=C(n);Object.setPrototypeOf(e,t)}}function w(e,t){h.setHiddenValue(e,"__remote_id__",t)}function S(e){return h.getHiddenValue(e,"__remote_id__")}function C(e,t,n){const i={value:()=>e.value,array:()=>e.members.map(e=>C(e)),buffer:()=>r.Buffer.from(e.value),promise:()=>Promise.resolve({then:C(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:i,value:r}=e.members[n];t[i]=r}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let o;return e.type in i?o=i[e.type]():e.refId?p.has(e.refId)?(h.addRemoteObjectRef(m,e.refId),o=p.get(e.refId)):(c.warn("[metaToValue] refId point to null"+e.refId),o="function"===e.type?()=>{}:{}):e.id?p.has(e.id)?(h.addRemoteObjectRef(m,e.id),_(o=p.get(e.id),e.id,e.members,!0),b(o,e.id,e.proto)):(o="function"===e.type?t?function(e,t,n){if(p.has(n.id))return p.get(n.id);let i=S(e),r=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(r,"name",{value:t,writable:!1,enumerable:!0});let o=new Proxy(r,{apply:(e,n,r)=>new Promise((e,o)=>{let c=S(n);if(c||(c=S(n.__remoteObj_)),!c)throw Error("is this function was a bound function?");let u=a.default.browser.memberCall,h=l.default(u),f=g(r);d.send(u,m,h,c,i,t,f),s.default.add(h,t=>{try{y(C(t),e,o)}catch(e){o(e)}})}),construct:(e,n,r)=>new Promise((e,r)=>{let o=a.default.browser.memberConstruct,c=l.default(o);d.send(o,m,c,i,t,g(n)),s.default.add(c,t=>{try{let n=C(t);e(n)}catch(e){r(e)}})})});return h.setHiddenValue(r,"__remote_id__",n.id),o}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return w(n,t),new Proxy(n,{apply:(e,n,i)=>new Promise((e,r)=>{let o=a.default.browser.functionCall,c=l.default(o),u=S(n);d.send(o,m,c,u,t,g(i)),s.default.add(c,t=>{try{y(C(t),e,r)}catch(e){r(e)}})}),construct:(e,n,i)=>new Promise((e,i)=>{let r=a.default.browser.construct,o=l.default(r);d.send(r,m,o,t,g(n)),s.default.add(o,t=>{try{let n=C(t);e(n)}catch(e){i(e)}})})})}(e):{},h.setRemoteObjectFreer(o,m,e.id),p.set(e.id,o),h.setHiddenValue(o,"__remote_id__",e.id),h.addRemoteObjectRef(m,e.id),function(e){let t=S(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,i){if("function"==typeof i)throw new Error("set a function to a remote member is dangerous");return new Promise((r,o)=>{let c=a.default.browser.memberSet,u=l.default(c),h=g([i]);d.send(c,m,u,t,n,h),s.default.add(u,t=>{try{let s=C(t);e[n]=i,r(s)}catch(e){o(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((i,r)=>{let o=a.default.browser.memberGet,c=l.default(o);d.send(o,m,c,t,n),s.default.add(c,t=>{try{const o=C(t);e[n]=o,i(o)}catch(e){r(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let i=a.default.browser.sync,r=l.default(i);d.send(i,m,r,t),s.default.add(r,i=>{try{if(i.id!==t)throw Error("SYNC_RETURN: remote id not match");let r=C(i);e(r)}catch(e){n(e)}})})}}})}(o),_(o,e.id,e.members),b(o,e.id,e.proto),Object.defineProperty(o.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=o[e],i=o.__proto__;for(;i;){if(Object.prototype.hasOwnProperty.call(i,e)){delete i[e];break}i=i.__proto__}Object.defineProperty(o,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):c.error("no id of meta:",e),o}t.Interest=v;class x{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,i)=>{let r=this.getChannel(e),o=l.default(r);d.send(r,m,o,t),s.default.add(o,e=>{try{let t=C(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){i(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=a.default.browser.require;break;case"builtin":t=a.default.browser.builtIn;break;case"global":t=a.default.browser.global;break;case"current_window":t=a.default.browser.currentWindow;break;case"current_web_contents":t=a.default.browser.currentWebContents;break;case"client_web_contents":t=a.default.browser.clientWebContents;break;case"web_contents":t=a.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function E(e,t,n){try{s.default.invoke(t,n).remove(t)}finally{s.default.remove(t)}}d.on(a.default.renderer.requireReturn,E),d.on(a.default.renderer.getBuiltInReturn,E),d.on(a.default.renderer.getGlobalReturn,E),d.on(a.default.renderer.currentWindowReturn,E),d.on(a.default.renderer.currentWebContentsReturn,E),d.on(a.default.renderer.functionCallReturn,E),d.on(a.default.renderer.constructReturn,E),d.on(a.default.renderer.memberCallReturn,E),d.on(a.default.renderer.memberSetReturn,E),d.on(a.default.renderer.memberGetReturn,E),d.on(a.default.renderer.memberConstructReturn,E),d.on(a.default.renderer.callback,(e,t,n)=>{f.apply(t,C(n))}),d.on(a.default.renderer.syncReturn,E),d.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{c.info("[RELEASE_CALLBACK]: callbackId:",t),f.remove(t)}),process.on("exit",()=>{d.send(a.default.browser.contextRelease)});const T=["__resolve","__isResolved"],O=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],P=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function k(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(P(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(P(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(P(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(P(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(P(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(P(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,i)=>(P(e),Reflect.defineProperty(e.__remoteObj_,n,i)),get:(t,n,i)=>{if("string"==typeof n){if(String.prototype.includes.call(O,n)){return e[n]}if(String.prototype.includes.call(T,n)){return e[n]}}return P(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,i,r)=>(P(e),Reflect.set(e.__remoteObj_,n,i,r)),ownKeys:t=>(P(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,i)=>{P(e),Reflect.apply(e.__remoteObj_,n,i)},construct:(t,n,i)=>{if(P(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,i)=>{let r=a.default.browser.construct,o=l.default(r),c=h.getHiddenValue(e.__remoteObj_,"__remote_id__");d.send(r,m,o,c,g(n)),s.default.add(o,e=>{try{t(C(e))}catch(e){i(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=S(e);t&&w(n,t)}}),n}t.remoteRequire=function(e){return k(new x("module",e))},t.getBuiltin=function(e){return k(new x("builtin",e))},t.getGlobal=function(e){return k(new x("global",e))},t.getCurrentWindow=function(){return k(new x("current_window"))},t.getCurrentWebContents=function(){return k(new x("current_web_contents"))},t.getWebContents=function(){return k(new x("web_contents"))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=i.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,r=(new Error).stack;let o,s=n.exec(r);for(;null!==s;){const e=s[1];if(!e.includes("native")&&!e.includes("electron.asar")){o=/([^/^)]*)\)?$/gi.exec(e)[1];break}s=n.exec(r)}return this.callbacks[t]=e,i.setHiddenValue(e,"__remote_callback_id__",t),i.setHiddenValue(e,"__remote_call_location__",o),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(i.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(17);var r;!function(e){let t={};e.add=function(e,n,i){t[e]={func:n,thisArg:i}},e.invoke=function(n,...r){let o=t[n];return o?o.thisArg?o.func.apply(o.thisArg,...r):o.func(...r):i.error(`Cannot invoke function by unrecognize id. ${n}`),e},e.remove=function(e){delete t[e]}}(r||(r={})),t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let i=0;t.default=function(e){return e?e.concat(".").concat(String(++i)):String(++i)}},function(e,t,n){"use strict";const i=n(8),r=n(103),o=n(54),s=n(104),a=n(17),l=n(55),c=i.ipcMain,u=process.electronBinding("v8_util");let d=u.createDoubleIDWeakMap();const h=new r.default;function f(e,t,n,i,r,o){let s,l=!1,c=null,u=!1;do{try{s=t[i]}catch(e){l=!0}if(l)try{s=n.value[i],l=!1,n.meta.extendedMemberNames.push(i),u=!0}catch(e){a.error(`property ${i} untouchable, even try root[name]`)}if(l)break;let r=Object.getOwnPropertyDescriptor(t,i);if(void 0===r){a.warn(`descriptor of property ${i} is undefined`);break}c={name:i,enumerable:r.enumerable,writable:!1,type:"get"},void 0===r.get&&"function"==typeof s?c.type="method":((r.set||r.writable)&&(c.writable=!0),c.type="get"),u?(c.configurable=!0,c.value=v(e,s,o,!1,null)):c.value=v(e,s,o,!1,n)}while(0);return c}function p(e,t,n,i=null){let r=Object.getOwnPropertyNames(t);"function"==typeof t&&(r=r.filter(function(e){return!String.prototype.includes.call(s.propertiesOfFunction,e)}));let o=[];do{if(0===r.length)break;let s=r.length;for(let a=0;a<s;a++){let s=f(e,t,n,r[a],0,i);s&&o.push(s)}}while(0);return o}function m(e,t,n,i=null){let r=null,o=Object.getPrototypeOf(t);return r=null===o||o===Object.prototype||o===Function.prototype?null:v(e,o,i,!1,n)}function v(e,t,n=null,i=!1,r=null){n=null===n?{}:n;const o={type:typeof t};"object"===o.type&&(o.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":l.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&u.getHiddenValue(e,"simple")&&(n="value"),n}(t,i));do{if("object"===o.type||"function"===o.type){let i=h.getIdOfObject(t);if(i&&n[i]){o.refId=i,h.add(e,t);break}}"array"===o.type?o.members=t.map(t=>v(e,t,n)):"object"===o.type||"function"===o.type?(null==r&&(o.extendedMemberNames=[],r={value:t,meta:o}),o.name=t.constructor?t.constructor.name:"",o.id=h.add(e,t),n[o.id]=!0,o.members=p(e,t,r,n),o.proto=m(e,t,r,n)):"buffer"===o.type?o.value=Buffer.from(t):"promise"===o.type?(t.then(function(){},function(){}),o.then=v(e,function(e,n){t.then(e,n)})):"error"===o.type?(o.members=g(t),o.members.push({name:"name",value:t.name})):"date"===o.type?o.value=t.getTime():(o.type="value",o.value=t)}while(0);return o}function g(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function y(e,t,n,r){const s=function(r){let l,c,f=0,p=0;switch(r.type){case"value":return r.value;case"remote-object":return h.get(r.id);case"array":return y(e,t,n,r.value);case"buffer":return Buffer.from(r.value);case"date":return new Date(r.value);case"promise":return Promise.resolve({then:s(r.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:r.name}),f=0,p=(c=r.members).length;f<p;f++)e[(l=c[f]).name]=s(l.value);return e}case"function":{const s=e.id,l=[n,r.id];if(a.info("renderer function id:"+l),d.has(l))return d.get(l);let c=function(...t){a.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||s!==e.id?function(e,t,n){let r="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof i.webContents.constructor){const{sender:e}=t[0],i=e.eventNames().filter(t=>{let i=e.listeners(t),r=!1;return i.forEach(e=>{e===n&&(r=!0)}),r});i.length>0&&(r+=`\nRemote event names: ${i.join(", ")}`,i.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}a.warn(r)}(r,n,c):e.send(o.default.renderer.callback,r.id,v(e,n))};return Object.defineProperty(c,"length",{value:r.length}),u.setRemoteCallbackFreer(c,t,n,r.id,e),d.set(l,c),c}default:throw new TypeError(`Unknown type: ${r.type}`)}};return r.map(s)}function _(e,t,n,i){let r,o;try{return t.apply(n,i)}catch(e){return o=t.name,new Error(`Could not call remote function '${r=null!=o?o:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function b(e){return{type:"exception",message:e.message,stack:e.stack||e}}function w(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var S;!function(e){const t=(e,t,...n)=>{const i=e.sender;i.isDestroyed()?a.warn("webcontext is destroyed."):i.send(t,...n)};e.startServer=function(){c.on(o.default.browser.require,(e,n,i,r)=>{a.info(`[REQUIRE] module=${r} `);let s=process.mainModule.require(r),l=v(e.sender,s);t(e,o.default.renderer.requireReturn,i,l)}),c.on(o.default.browser.builtIn,(e,n,r,s)=>{a.info(`[BUILTIN]: property=${s} contextId=${n}`);let l=i[s],c=v(e.sender,l);a.info(`[BUILTIN]: returns remoteId:${c.id}, type: ${typeof l}`),t(e,o.default.renderer.getBuiltInReturn,r,c)}),c.on(o.default.browser.global,(e,n,i,r)=>{a.info(`[GLOBAL]: proerty:${r} contextId=${n}`);let s,l=global[r];s=v(e.sender,l),a.info(`[GLOBAL]: returns remoteid=${s.id}, obj=`+typeof l),t(e,o.default.renderer.getGlobalReturn,i,s)}),c.on(o.default.browser.currentWindow,(e,n,i,r)=>{a.info(`[CURRENT_WINDOW]: property=${r} contextId=${n}`);let s=e.sender.getOwnerBrowserWindow.call(e.sender),l=v(e.sender,s);a.info(`[CURRENT_WINDOW]: returns remoteid=${l.id}, obj=`+s),t(e,o.default.renderer.currentWindowReturn,i,l)}),c.on(o.default.browser.currentWebContents,(e,n,i,r)=>{t(e,o.default.renderer.currentWebContentsReturn,i,v(e.sender,e.sender))}),c.on(o.default.browser.webContents,(e,n,r,s)=>{a.info(`[WebContents]: proerty:${s} contextId=${n}`);let l,c=i.webContents;l=v(e.sender,c),a.info(`[WebContents]: returns remoteid=${l.id}, obj=`+typeof c),t(e,o.default.renderer.webContentsReturn,r,l)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(i=>{n?e.once(i,t[i]):e.on(i,t[i])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};c.on(o.default.browser.construct,(n,i,r,s,l)=>{let c,u=null;try{a.info(`[CONSTRUCTOR]: remoteId=${s} `);let d=l.length>0?l[l.length-1]:null;l=y(n.sender,n.frameId,i,l);let f=h.get(s);null==f&&w(`Cannot call constructor on missing remote object ${s}`),d&&"interest"===d.type&&(u=l.pop());let p=new(Function.prototype.bind.apply(f,[null,...l]));p&&u&&e(p,u),c=v(n.sender,p,null,!1),a.info(`[CONSTRUCTOR]: returns remoteId =${c.id} name=${f.name} `)}catch(e){c=b(e)}finally{t(n,o.default.renderer.constructReturn,r,c)}}),c.on(o.default.browser.functionCall,function(e,n,i,r,s,l){let c;try{a.info(`[FUNCTION_CALL]: remoteId=${s}`),l=y(e.sender,e.frameId,n,l);let u=h.get(s);if(null==u)a.error(`Cannot call function on missing remote object ${s}`),c=v(e.sender,void 0);else{let t=r?h.get(r):global;if(t){let n=_(0,u,t,l);c=v(e.sender,n)}else a.error(`Cannot call function(${s}) on missing context(${r})`),c=v(e.sender,void 0)}a.info(`[FUNCTION_CALL]: name=${u.name}`)}catch(e){c=b(e)}finally{t(e,o.default.renderer.functionCallReturn,i,c)}}),c.on(o.default.browser.memberCall,function(e,n,i,r,s,l,c){let u;a.info(`[MEMBER_CALL]: thisArg=${r}, remoteId=${s}, method=${l}, args count=${c.length}`);try{c=y(e.sender,e.frameId,n,c);let d=h.get(s);null==d&&w(`Cannot call function '${l}' on missing remote object ${s}`);let f=r?h.get(r):d;if(f){let t=_(0,d[l],f,c);u=v(e.sender,t),a.info("[MEMBER_CALL]: return="+t)}else u=v(e.sender,void 0)}catch(e){u=b(e)}finally{t(e,o.default.renderer.memberCallReturn,i,u)}}),c.on(o.default.browser.memberGet,function(e,n,i,r,s){let l;try{a.info(`[MEMBER_GET]: remoteId=${r}, property=`,s);let n=h.get(r);null==n&&w(`Cannot get property '${Object.toString.call(s)}' on missing remote object ${r}`);let c=n[s];l=v(e.sender,c)}catch(e){l=b(e)}finally{t(e,o.default.renderer.memberGetReturn,i,l)}}),c.on(o.default.browser.memberSet,function(e,n,i,r,s,l){try{a.info(`[MEMBER_SET]: remoteId=${r}, property=`+s),l=y(e.sender,e.frameId,n,l);let c=h.get(r);null==c&&w(`Cannot set property '${Object.toString.call(s)}' on missing remote object ${r}`),c[s]=l[0],t(e,o.default.renderer.memberSetReturn,i,{type:"value",value:!0})}catch(n){t(e,o.default.renderer.memberSetReturn,i,b(n))}}),c.on(o.default.browser.memberConstruct,function(n,i,r,s,l,c){let u,d=null;try{a.info(`[MEMBER_CONSTRUCTOR]: regId=${s}, method=${l}`);let f=c.length>0?c[c.length-1]:null;c=y(n.sender,n.frameId,i,c);let p=h.get(s);null==p&&w(`Cannot call constructor '${l}' on missing remote object ${s}`),f&&"interest"===f.type&&(d=c.pop());let m=p[l],g=new(Function.prototype.bind.apply(m,[null,...c]));g&&d&&e(g,d),u=v(n.sender,g)}catch(e){u=b(e)}finally{t(n,o.default.renderer.memberConstructReturn,r,u)}}),c.on(o.default.browser.sync,function(e,n,i,r){let s=h.get(r);t(e,o.default.renderer.syncReturn,i,v(e.sender,s))}),c.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=h.get(t);if(i.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let i=n.name;i||(i=n.constructor?n.constructor.name:""),h.remove(e.sender.id,t)}else t<0&&a.warn("remote id reference to nothing:",t)}),c.on(o.default.browser.contextRelease,e=>{h.clear(e.sender.id)})},e.getObjectRegistry=function(){return h}}(S||(S={})),e.exports=S},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(17),r=-1*Math.pow(2,31),o=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),i=e.id;let r=this.owners.get(i);return r||(r=new Set,this.owners.set(i,r),this.registerDeleteListener(e,i)),r.has(n)||(r.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return o.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=o.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=r)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),o.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(o.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),r=(o,s)=>{s===n&&(i.info("render-view-deleted: processid="+n),(()=>{i.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",r),this.clear(t))};e.on("render-view-deleted",r)}}},function(e,t,n){"use strict";var i;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(i||(i={})),e.exports=i},function(e,t,n){e.exports=n(29)(5)},function(e,t){e.exports=require("child_process")},function(e,t,n){e.exports=!n(16)&&!n(35)(function(){return 7!=Object.defineProperty(n(64)("div"),"a",{get:function(){return 7}}).a})},function(e,t){},function(e,t,n){"use strict";var i=n(36),r=n(13),o=n(110),s=n(21),a=n(30),l=n(174),c=n(49),u=n(178),d=n(7)("iterator"),h=!([].keys&&"next"in[].keys()),f=function(){return this};e.exports=function(e,t,n,p,m,v,g){l(n,t,p);var y,_,b,w=function(e){if(!h&&e in E)return E[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},S=t+" Iterator",C="values"==m,x=!1,E=e.prototype,T=E[d]||E["@@iterator"]||m&&E[m],O=T||w(m),P=m?C?w("entries"):O:void 0,k="Array"==t&&E.entries||T;if(k&&(b=u(k.call(new e)))!==Object.prototype&&b.next&&(c(b,S,!0),i||"function"==typeof b[d]||s(b,d,f)),C&&T&&"values"!==T.name&&(x=!0,O=function(){return T.call(this)}),i&&!g||!h&&!x&&E[d]||s(E,d,O),a[t]=O,a[S]=f,m)if(y={values:C?O:w("values"),keys:v?O:w("keys"),entries:P},g)for(_ in y)_ in E||o(E,_,y[_]);else r(r.P+r.F*(h||x),t,y);return y}},function(e,t,n){e.exports=n(21)},function(e,t,n){var i=n(22),r=n(28),o=n(176)(!1),s=n(69)("IE_PROTO");e.exports=function(e,t){var n,a=r(e),l=0,c=[];for(n in a)n!=s&&i(a,n)&&c.push(n);for(;t.length>l;)i(a,n=t[l++])&&(~o(c,n)||c.push(n));return c}},function(e,t,n){var i=n(6).document;e.exports=i&&i.documentElement},function(e,t,n){var i=n(86),r=n(7)("iterator"),o=n(30);e.exports=n(3).getIteratorMethod=function(e){if(void 0!=e)return e[r]||e["@@iterator"]||o[i(e)]}},function(e,t,n){var i=n(15),r=n(46),o=n(7)("species");e.exports=function(e,t){var n,s=i(e).constructor;return void 0===s||void 0==(n=i(s)[o])?t:r(n)}},function(e,t,n){var i,r,o,s=n(43),a=n(185),l=n(112),c=n(64),u=n(6),d=u.process,h=u.setImmediate,f=u.clearImmediate,p=u.MessageChannel,m=u.Dispatch,v=0,g={},y=function(){var e=+this;if(g.hasOwnProperty(e)){var t=g[e];delete g[e],t()}},_=function(e){y.call(e.data)};h&&f||(h=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return g[++v]=function(){a("function"==typeof e?e:Function(e),t)},i(v),v},f=function(e){delete g[e]},"process"==n(37)(d)?i=function(e){d.nextTick(s(y,e,1))}:m&&m.now?i=function(e){m.now(s(y,e,1))}:p?(o=(r=new p).port2,r.port1.onmessage=_,i=s(o.postMessage,o,1)):u.addEventListener&&"function"==typeof postMessage&&!u.importScripts?(i=function(e){u.postMessage(e+"","*")},u.addEventListener("message",_,!1)):i="onreadystatechange"in c("script")?function(e){l.appendChild(c("script")).onreadystatechange=function(){l.removeChild(this),y.call(e)}}:function(e){setTimeout(s(y,e,1),0)}),e.exports={set:h,clear:f}},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,n){var i=n(15),r=n(19),o=n(73);e.exports=function(e,t){if(i(e),r(t)&&t.constructor===e)return t;var n=o.f(e);return(0,n.resolve)(t),n.promise}},function(e,t,n){var i=n(111),r=n(71).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return i(e,r)}},,,,function(e,t,n){e.exports={default:n(263),__esModule:!0}},function(e,t,n){e.exports={default:n(270),__esModule:!0}},function(e,t){var n=/^(attrs|props|on|nativeOn|class|style|hook)$/;function i(e,t){return function(){e&&e.apply(this,arguments),t&&t.apply(this,arguments)}}e.exports=function(e){return e.reduce(function(e,t){var r,o,s,a,l;for(s in t)if(r=e[s],o=t[s],r&&n.test(s))if("class"===s&&("string"==typeof r&&(l=r,e[s]=r={},r[l]=!0),"string"==typeof o&&(l=o,t[s]=o={},o[l]=!0)),"on"===s||"nativeOn"===s||"hook"===s)for(a in o)r[a]=i(r[a],o[a]);else if(Array.isArray(r))e[s]=r.concat(o);else if(Array.isArray(o))e[s]=[r].concat(o);else for(a in o)r[a]=o[a];else e[s]=t[s];return e},{})}},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.MessageBox=void 0;const r=n(8),o=n(40);!function(e){let t;!function(e){e.Info="info",e.Warning="warning",e.Error="error",e.Success="success"}(t=e.ConfirmType||(e.ConfirmType={})),e.confirm=function(e,t={}){return i(this,void 0,void 0,function*(){return e.boxId=String(Math.random()).replace(/\D/,""),r.ipcRenderer.send("xmp-message-box-confirm-create",{dialogConf:t,options:e}),(yield o.electron.ipcMain.__resolve()).once(`xmp-message-box-init-${e.boxId}`,t=>{t.sender.send("xmp-message-box-init-reply",{popType:"confirm",options:e})}),new Promise(t=>i(this,void 0,void 0,function*(){(yield o.electron.ipcMain.__resolve()).once(`xmp-message-box-resolve-${e.boxId}`,(e,n,i)=>{t({action:n,checkboxChecked:i})})}))})},e.pop=function(e,t={}){return i(this,void 0,void 0,function*(){let n=String(Math.random()).replace(/\D/,"");return r.ipcRenderer.send("xmp-message-box-pop-create",{dialogConf:t,boxId:n}),(yield o.electron.ipcMain.__resolve()).once(`xmp-message-box-init-${n}`,t=>{t.sender.send("xmp-message-box-init-reply",{popType:"message-box",options:e})}),new Promise(e=>i(this,void 0,void 0,function*(){(yield o.electron.ipcMain.__resolve()).once(`xmp-message-box-resolve-${n}`,(t,n,i)=>{e({action:n,formData:i})})}))})},e.custom=function(e,t={},n={}){return i(this,void 0,void 0,function*(){let s=String(Math.random()).replace(/\D/,"");return t.boxId=s,r.ipcRenderer.send("xmp-message-box-custom-create",{popType:e,dialogConf:n,boxId:s,options:t}),(yield o.electron.ipcMain.__resolve()).once(`xmp-message-box-init-${s}`,i=>{i.sender.send("xmp-message-box-init-reply",{popType:e,options:t,dialogConf:n})}),new Promise(e=>i(this,void 0,void 0,function*(){(yield o.electron.ipcMain.__resolve()).once(`xmp-message-box-resolve-${s}`,(t,n,i)=>{e({action:n,args:i})})}))})}}(t.MessageBox||(t.MessageBox={}))},function(e,t,n){e.exports=n(29)(1)},function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},r=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.PositionMixin=t.DraggableMixin=void 0;const o=n(12),s=n(62),a=n(260);let l=class extends o.Vue{constructor(){super(...arguments),this.removeDraggable=null}destroyed(){this.removeDraggable()}};l=i([o.Component],l),t.DraggableMixin=l;let c=class extends o.Vue{constructor(){super(...arguments),this.windowWidth=0,this.windowHeight=0,this.resizeToFitContent=a.ThunderWindowNS.resizeToFitContent,this.fitWindowPosInParent=a.ThunderWindowNS.fitWindowPosInParent}get relativeToParent(){return!s.ThunderUtil.isDef(this.options.relativeToParent)||this.options.relativeToParent}mounted(){return r(this,void 0,void 0,function*(){0===this.windowWidth&&(this.windowWidth=this.options.windowWidth||0),0===this.windowHeight&&(this.windowHeight=this.options.windowHeight||0),this.relativeToParent?this.resizeToFitContent(this.windowWidth,this.windowHeight,this.fitWindowPosInParent).catch():this.resizeToFitContent(this.windowWidth,this.windowHeight).catch()})}};i([o.Prop()],c.prototype,"options",void 0),c=i([o.Component],c),t.PositionMixin=c},function(e,t,n){e.exports={default:n(170),__esModule:!0}},function(e,t,n){var i=n(37);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==i(e)?e.split(""):Object(e)}},function(e,t,n){var i=n(15);e.exports=function(e,t,n,r){try{return r?t(i(n)[0],n[1]):t(n)}catch(t){var o=e.return;throw void 0!==o&&i(o.call(e)),t}}},function(e,t,n){var i=n(30),r=n(7)("iterator"),o=Array.prototype;e.exports=function(e){return void 0!==e&&(i.Array===e||o[r]===e)}},function(e,t,n){var i=n(7)("iterator"),r=!1;try{var o=[7][i]();o.return=function(){r=!0},Array.from(o,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!r)return!1;var n=!1;try{var o=[7],s=o[i]();s.next=function(){return{done:n=!0}},o[i]=function(){return s},e(o)}catch(e){}return n}},function(e,t,n){var i=n(48)("meta"),r=n(19),o=n(22),s=n(14).f,a=0,l=Object.isExtensible||function(){return!0},c=!n(35)(function(){return l(Object.preventExtensions({}))}),u=function(e){s(e,i,{value:{i:"O"+ ++a,w:{}}})},d=e.exports={KEY:i,NEED:!1,fastKey:function(e,t){if(!r(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!o(e,i)){if(!l(e))return"F";if(!t)return"E";u(e)}return e[i].i},getWeak:function(e,t){if(!o(e,i)){if(!l(e))return!0;if(!t)return!1;u(e)}return e[i].w},onFreeze:function(e){return c&&d.NEED&&l(e)&&!o(e,i)&&u(e),e}}},,,function(e,t,n){"use strict";n.r(t);var i=n(137),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return i[e]})}(o);t.default=r.a},function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s};Object.defineProperty(t,"__esModule",{value:!0});const r=n(8),o=n(12),s=n(125),a=n(233);let l=class extends o.Vue{constructor(){super(...arguments),this.popType="",this.options={title:"",type:s.MessageBox.ConfirmType.Info},this.boxId=a.parse(location.href,!0).query.boxId,this.removeDraggable=null}created(){r.ipcRenderer.once("xmp-message-box-init-reply",(e,t)=>{this.popType=t.popType,this.options=t.options}),r.ipcRenderer.send(`xmp-message-box-init-${this.boxId}`)}};l=i([o.Component({components:{Confirm:()=>Promise.resolve().then(()=>n(259)),MessageBox:()=>Promise.resolve().then(()=>n(267)),DLNAView:()=>Promise.resolve().then(()=>n(268)),SubtitleSetting:()=>Promise.resolve().then(()=>n(269))}})],l),t.default=l},function(e,t,n){"use strict";n.r(t);var i=n(139),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return i[e]})}(o);t.default=r.a},function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},r=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(8),s=n(126),a=n(12),l=n(127),c=n(235),u=n(59),d=n(10),h=n(51),f=n(23);a.Vue.use(u.default),a.Vue.use(d.default),a.Vue.use(h.default),a.Vue.use(f.default);let p=class extends(s.mixins(l.DraggableMixin,l.PositionMixin)){constructor(){super(...arguments),this.action=c.MessageBoxNS.Action,this.icon={info:"question",warning:"warning",error:"error",success:"success"}}handleInput(e){this.$emit("update:options",Object.assign({},this.options,{checkboxChecked:e}))}handleOK(){o.ipcRenderer.send(`xmp-message-box-resolve-${this.boxId}`,c.MessageBoxNS.Action.OK,this.options.checkboxChecked),window.close()}handleCancel(e){o.ipcRenderer.send(`xmp-message-box-resolve-${this.boxId}`,e),window.close()}handleKeyDown(e){13===e.keyCode&&this.handleOK()}mounted(){return r(this,void 0,void 0,function*(){document.addEventListener("keydown",this.handleKeyDown)})}destroyed(){document.removeEventListener("keydown",this.handleKeyDown)}};i([a.Prop()],p.prototype,"options",void 0),i([a.Prop()],p.prototype,"boxId",void 0),p=i([a.Component({props:{options:Object}})],p),t.default=p},function(e,t,n){"use strict";n.r(t);var i=n(141),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return i[e]})}(o);t.default=r.a},function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},r=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(8),s=n(126),a=n(12),l=n(62),c=n(127),u=n(59),d=n(23);a.Vue.use(u.default),a.Vue.use(d.default);let h=class extends(s.mixins(c.DraggableMixin,c.PositionMixin)){handleInput(e,t){let n=l.ThunderUtil.deepCopy(this.formData);Array.isArray(e)?n[t]=e:n[t].value=e,this.$emit("update:formData",n)}handleOK(){o.ipcRenderer.send("xmp-message-box-resolve",this.formData),window.close()}handleCancel(){o.ipcRenderer.send("xmp-message-box-reject"),window.close()}get comfirmButtonActive(){if(void 0===this.formData)return!0;if(void 0===this.formData.maxDownloadSpeedEnabled)return!0;let e=this.formData.maxDownloadSpeedEnabled.value,t=this.formData.maxUploadSpeedEnabled.value;return e||t}mounted(){return r(this,void 0,void 0,function*(){this.resizeToFitContent()})}};i([a.Prop()],h.prototype,"options",void 0),i([a.Prop()],h.prototype,"formData",void 0),h=i([a.Component({components:{}})],h),t.default=h},function(e,t,n){"use strict";n.r(t);var i=n(143),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return i[e]})}(o);t.default=r.a},function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},r=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(12),s=n(1),a=n(0),l=n(235),c=n(125),u=n(18),d=n(59),h=n(127),f=n(126),p=n(74);o.Vue.use(d.default);const m=a.default.getLogger("dlna-select");let v=class extends(f.mixins(h.PositionMixin)){constructor(){super(...arguments),this.searchTips="正在搜寻设备...",this.searching=!0,this.deviceMap=[],this.firstFlag=!1,this.searchCount=0,this.timerCookie=null,this.itemSelected=!1,this.itemConnected=!1,this.ipcEventCookie=0,this.firstSelect=!1,this.searchSuccess=!1,this.selectDeviceId=""}mounted(){return r(this,void 0,void 0,function*(){this.searchCount=0,this.ipcEventCookie=s.client.attachServerEvent(p.XmpEvent.onDlnaEvent,this.onDlnaEvent.bind(this)),this.refreshDevice().catch()})}destroyed(){this.clearSearchTimer(),s.client.detachServerEvent(p.XmpEvent.onDlnaEvent,this.ipcEventCookie)}clearSearchTimer(){this.timerCookie&&(clearInterval(this.timerCookie),this.timerCookie=null)}onDlnaEvent(e,t,n){return r(this,void 0,void 0,function*(){m.information("DLNA 事件 hiword:",t),1===t?yield this.loadDeviceList():2===t&&this.disConnectDevice("DLNA").catch()})}onItemSelect(e){return r(this,void 0,void 0,function*(){for(const t in this.deviceMap)this.deviceMap.hasOwnProperty(t)&&this.deviceMap[t].id!==e.id&&(this.deviceMap[t].isActive=!1);e.isActive=!0,this.itemSelected=!0,(yield s.client.callServerFunction(u.XmpPluginFunction.getCurrentDevice,"DLNA"))===e.id?this.itemConnected=!0:this.itemConnected=!1})}isConnected(e){return r(this,void 0,void 0,function*(){let t=yield s.client.callServerFunction(u.XmpPluginFunction.getCurrentDevice,"DLNA");return!(!t||""===t||t!==e)})}disConnectDevice(e){return r(this,void 0,void 0,function*(){return s.client.callServerFunction(u.XmpPluginFunction.setCurrentDevice,e,"")})}connectDevice(e,t,n,i){return r(this,void 0,void 0,function*(){let r=!0;if(!1===(yield s.client.callServerFunction(u.XmpPluginFunction.isDeviceSupport,i,t))){let e;r=!1,(e=yield c.MessageBox.confirm({title:"投屏格式不支持",type:c.MessageBox.ConfirmType.Warning,content:"当前选中的设备可能不支持该视频格式，确定继续投屏操作吗？",okText:"继续投屏",titleClass:"xmp-dialog--title",cancelVisible:!0},{modal:!1})).action===l.MessageBoxNS.Action.OK&&(r=!0)}r&&(yield s.client.callServerFunction(u.XmpPluginFunction.setCurrentDevice,i,e,t,n))})}refreshDevice(){return r(this,void 0,void 0,function*(){yield s.client.callServerFunction(u.XmpPluginFunction.activeDevice,!0),this.clearSearchTimer(),this.searchCount=0,this.deviceMap=[],this.firstSelect=!0,this.searchSuccess=!1,this.timerCookie=setInterval(()=>r(this,void 0,void 0,function*(){this.searchCount++,s.client.callServerFunction(u.XmpPluginFunction.searchDevice).catch(),this.searchCount>=30&&this.resetSearch(),(yield this.loadDeviceList())&&(this.searchSuccess||s.client.callServerFunctionEx(u.XmpPluginFunction.trackEvent,"xmp_player","xmp_player_throwing_screen_search"),this.searchSuccess=!0)}),300)})}loadDeviceList(){return r(this,void 0,void 0,function*(){let e=yield s.client.callServerFunction(u.XmpPluginFunction.getDeviceList);if(m.information("deviceData:",e," firstSelect:",this.firstSelect),e.forEach(e=>{this.pushItem(e,this.firstSelect),this.firstSelect&&(this.selectDeviceId=e.id),this.firstSelect=!1}),this.deviceMap.length>0){this.itemSelected=!0,this.searching=!1;let e=yield s.client.callServerFunction(u.XmpPluginFunction.getCurrentDevice,"DLNA");return m.information("DLNA 当前选择device:",e," 列表项:",this.selectDeviceId),e===this.selectDeviceId?this.itemConnected=!0:this.itemConnected=!1,!0}return this.timerCookie||(this.searchTips="未搜索到相关设备!"),!1})}onClickConnect(){return r(this,void 0,void 0,function*(){for(const e in this.deviceMap)if(this.deviceMap.hasOwnProperty(e)&&this.deviceMap[e].isActive){(yield this.isConnected(this.deviceMap[e].id))?this.disConnectDevice(this.deviceMap[e].deviceType).then(()=>{this.itemConnected=!1}).catch():this.connectDevice(this.deviceMap[e].id,this.deviceMap[e].supportExts,this.deviceMap[e].name,this.deviceMap[e].deviceType).then(()=>{window.close()}).catch(()=>{window.close()});break}})}onClickRefresh(){return r(this,void 0,void 0,function*(){this.searching=!0,this.searchTips="正在搜寻设备...",yield s.client.callServerFunction(u.XmpPluginFunction.activeDevice,!1),yield this.refreshDevice()})}onCloseClick(){window.close()}onClickEmptyBlock(){m.information("onClickEmptyBlock"),this.itemSelected=!1;for(const e in this.deviceMap)this.deviceMap.hasOwnProperty(e)&&(this.deviceMap[e].isActive=!1)}onKeyDown(e){"Escape"===e.key&&window.close()}onItemDbClick(e){this.isConnected(e.id)||this.connectDevice(e.id,e.supportExts,e.name,e.deviceType).then(()=>{window.close()}).catch(()=>{window.close()})}resetSearch(){clearInterval(this.timerCookie),this.timerCookie=null,this.searchCount=0}isItemExist(e){let t=!1;for(let n=0;n<this.deviceMap.length;n++)if(this.deviceMap[n].id.trim()===e.trim()){t=!0;break}return t}pushItem(e,t){this.isItemExist(e.id)||this.deviceMap.push({id:e.id,name:e.name,isActive:t,supportExts:e.supportExts,deviceType:e.deviceType})}};v=i([o.Component],v),t.default=v},function(e,t,n){"use strict";n.r(t);var i=n(145),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return i[e]})}(o);t.default=r.a},function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},r=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(12),s=n(0),a=n(59),l=n(357),c=n(127),u=n(126),d=n(18),h=n(1),f=n(74),p=n(26),m=n(42);o.Vue.use(a.default),o.Vue.use(l.default);const v=s.default.getLogger("SubtitleSetting");let g=class extends(u.mixins(c.PositionMixin)){constructor(){super(...arguments),this.inited=!1,this.showFontNameList=!1,this.fontName="默认",this.showFontSizeList=!1,this.fontSize="默认",this.fontColor="#FFFFFF",this.timeLine=0,this.timeLineEx="0.0",this.subtitlePosition=95,this.enableSubtitleOutsidePicture=!1,this.subtitleOutsidePicture=!1}onFontColorChanged(){this.setFontStyle()}onTimeLineChanged(){h.client.callServerFunction(d.SubtitleFunction.setSubtitleTimming,100*this.timeLine).catch()}onFontSizeChanged(){this.setFontStyle()}onFontNameChanged(){this.setFontStyle()}setFontStyle(){if(!this.inited)return;let e={color:"FF"+this.fontColor.substr(5,2)+this.fontColor.substr(3,2)+this.fontColor.substr(1,2),size:this.fontSize,font:this.fontName};h.client.callServerFunction(d.SubtitleFunction.setSubtitleFontStyle,e).catch()}get fontColorStyle(){return"background: "+this.fontColor}created(){return r(this,void 0,void 0,function*(){let e=yield h.client.callServerFunction(d.SubtitleFunction.getSubtitleFontStyle);this.fontName=e.font||"默认",this.fontSize=e.size?String(e.size):"默认",this.fontColor=e.color?"#"+e.color.substr(6)+e.color.substr(4,2)+e.color.substr(2,2):"#FFFFFF",v.information("init",e,this.fontName,this.fontSize,this.fontColor),this.timeLine=(yield h.client.callServerFunction(d.SubtitleFunction.getSubtitleTimming))/100||0,v.information("getSubtitleTimming",this.timeLine),this.timeLineEx=`${(this.timeLine/10).toFixed(1)}`,this.subtitlePosition=yield h.client.callServerFunction(d.SubtitleFunction.getSubtitlePosition),this.inited=!0,h.client.attachServerEvent(f.XmpEvent.onStop,()=>{window.close()}),window.onblur=(()=>{});let t=yield m.rpc.getCurrentWindow();p.ThunderHelper.setTopMostWindow(t)})}selectFontName(e){this.showFontNameList=!1,this.fontName=e}selectFontSize(e){this.showFontSizeList=!1,this.fontSize=e}onpointerup(e,t){"showFontNameList"!==t&&(this.showFontNameList=!1),"showFontSizeList"!==t&&(this.showFontSizeList=!1)}decreaseTimeLine(){this.timeLine=Math.max(-600,this.timeLine-1),this.timeLineEx=`${(this.timeLine/10).toFixed(1)}`}increaseTimeLine(){this.timeLine=Math.min(600,this.timeLine+1),this.timeLineEx=`${(this.timeLine/10).toFixed(1)}`}onTimeLineInput(e){let t=["-",".","0","1","2","3","4","5","6","7","8","9"];do{if("-"===this.timeLineEx||"--"===this.timeLineEx||"-."===this.timeLineEx||".-"===this.timeLineEx){this.timeLineEx="-",this.timeLine=0;break}let n=!0;switch(this.timeLineEx){case"-":case"--":this.timeLineEx="-",this.timeLine=0;break;case"-.":this.timeLine=0;break;case"..":case".-":case".":this.timeLineEx=".",this.timeLine=0;break;default:n=!1}if(n)break;if(t.includes(e.data)){let e=10*Number(this.timeLineEx);e=Math.min(600,e),e=Math.max(-600,e),this.timeLine=Math.floor(e),this.timeLineEx.includes(".")?"."!==this.timeLineEx[this.timeLineEx.length-1]&&(this.timeLineEx=`${(this.timeLine/10).toFixed(1)}`):this.timeLineEx=`${(this.timeLine/10).toFixed(0)}`;break}if("deleteContentBackward"===e.inputType||"deleteContentForward"===e.inputType){let e=10*Number(this.timeLineEx);e=Math.min(600,e),e=Math.max(-600,e),this.timeLine=Math.floor(e);break}this.timeLineEx.includes(".")?"."!==this.timeLineEx[this.timeLineEx.length-1]&&(this.timeLineEx=`${(this.timeLine/10).toFixed(1)}`):this.timeLineEx=`${(this.timeLine/10).toFixed(0)}`}while(0)}handleSubtitlePositionChange(e){this.inited&&h.client.callServerFunction(d.SubtitleFunction.setSubtitlePosition,e).then(e=>{v.information("XmpSubtitleSetSubtitlePosition ret:",e)}).catch()}handleSubtitleOutsidePicture(e){}reset(){this.fontColor="#FFFFFF",this.fontName="默认",this.fontSize="默认",this.subtitlePosition=95,this.handleSubtitlePositionChange(this.subtitlePosition),this.timeLineDefault()}timeLineDefault(){this.timeLine=0,this.timeLineEx="0.0"}clickClose(){window.close()}};i([o.Watch("fontColor")],g.prototype,"onFontColorChanged",null),i([o.Watch("timeLine")],g.prototype,"onTimeLineChanged",null),i([o.Watch("fontSize")],g.prototype,"onFontSizeChanged",null),i([o.Watch("fontName")],g.prototype,"onFontNameChanged",null),g=i([o.Component],g),t.default=g},,,,,,,,,,,,,,,,,,,,,function(e,t,n){e.exports={default:n(273),__esModule:!0}},function(e,t,n){"use strict";t.__esModule=!0;var i,r=n(27),o=(i=r)&&i.__esModule?i:{default:i};t.default=function(e){return function(){var t=e.apply(this,arguments);return new o.default(function(e,n){return function i(r,s){try{var a=t[r](s),l=a.value}catch(e){return void n(e)}if(!a.done)return o.default.resolve(l).then(function(e){i("next",e)},function(e){i("throw",e)});e(l)}("next")})}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GlobalDataNS=void 0;const i=n(5);let r=i.normalize(i.join(process.execPath,"../resources")),o=i.normalize(i.join(r,"app"));!function(e){e.getResourcesPath=function(){return r},e.getAppPath=function(){return o}}(t.GlobalDataNS||(t.GlobalDataNS={}))},function(e,t){e.exports=require("crypto")},function(e,t,n){n(171);var i=n(3).Object;e.exports=function(e,t,n){return i.defineProperty(e,t,n)}},function(e,t,n){var i=n(13);i(i.S+i.F*!n(16),"Object",{defineProperty:n(14).f})},function(e,t,n){n(108),n(56),n(72),n(182),n(190),n(191),e.exports=n(3).Promise},function(e,t,n){var i=n(66),r=n(67);e.exports=function(e){return function(t,n){var o,s,a=String(r(t)),l=i(n),c=a.length;return l<0||l>=c?e?"":void 0:(o=a.charCodeAt(l))<55296||o>56319||l+1===c||(s=a.charCodeAt(l+1))<56320||s>57343?e?a.charAt(l):o:e?a.slice(l,l+2):s-56320+(o-55296<<10)+65536}}},function(e,t,n){"use strict";var i=n(68),r=n(44),o=n(49),s={};n(21)(s,n(7)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=i(s,{next:r(1,n)}),o(e,t+" Iterator")}},function(e,t,n){var i=n(14),r=n(15),o=n(47);e.exports=n(16)?Object.defineProperties:function(e,t){r(e);for(var n,s=o(t),a=s.length,l=0;a>l;)i.f(e,n=s[l++],t[n]);return e}},function(e,t,n){var i=n(28),r=n(85),o=n(177);e.exports=function(e){return function(t,n,s){var a,l=i(t),c=r(l.length),u=o(s,c);if(e&&n!=n){for(;c>u;)if((a=l[u++])!=a)return!0}else for(;c>u;u++)if((e||u in l)&&l[u]===n)return e||u||0;return!e&&-1}}},function(e,t,n){var i=n(66),r=Math.max,o=Math.min;e.exports=function(e,t){return(e=i(e))<0?r(e+t,0):o(e,t)}},function(e,t,n){var i=n(22),r=n(57),o=n(69)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=r(e),i(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},function(e,t,n){"use strict";var i=n(180),r=n(181),o=n(30),s=n(28);e.exports=n(109)(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,r(1)):r(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])},"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){"use strict";var i,r,o,s,a=n(36),l=n(6),c=n(43),u=n(86),d=n(13),h=n(19),f=n(46),p=n(183),m=n(184),v=n(114),g=n(115).set,y=n(186)(),_=n(73),b=n(116),w=n(187),S=n(117),C=l.TypeError,x=l.process,E=x&&x.versions,T=E&&E.v8||"",O=l.Promise,P="process"==u(x),k=function(){},M=r=_.f,R=!!function(){try{var e=O.resolve(1),t=(e.constructor={})[n(7)("species")]=function(e){e(k,k)};return(P||"function"==typeof PromiseRejectionEvent)&&e.then(k)instanceof t&&0!==T.indexOf("6.6")&&-1===w.indexOf("Chrome/66")}catch(e){}}(),I=function(e){var t;return!(!h(e)||"function"!=typeof(t=e.then))&&t},W=function(e,t){if(!e._n){e._n=!0;var n=e._c;y(function(){for(var i=e._v,r=1==e._s,o=0,s=function(t){var n,o,s,a=r?t.ok:t.fail,l=t.resolve,c=t.reject,u=t.domain;try{a?(r||(2==e._h&&N(e),e._h=1),!0===a?n=i:(u&&u.enter(),n=a(i),u&&(u.exit(),s=!0)),n===t.promise?c(C("Promise-chain cycle")):(o=I(n))?o.call(n,l,c):l(n)):c(i)}catch(e){u&&!s&&u.exit(),c(e)}};n.length>o;)s(n[o++]);e._c=[],e._n=!1,t&&!e._h&&L(e)})}},L=function(e){g.call(l,function(){var t,n,i,r=e._v,o=D(e);if(o&&(t=b(function(){P?x.emit("unhandledRejection",r,e):(n=l.onunhandledrejection)?n({promise:e,reason:r}):(i=l.console)&&i.error&&i.error("Unhandled promise rejection",r)}),e._h=P||D(e)?2:1),e._a=void 0,o&&t.e)throw t.v})},D=function(e){return 1!==e._h&&0===(e._a||e._c).length},N=function(e){g.call(l,function(){var t;P?x.emit("rejectionHandled",e):(t=l.onrejectionhandled)&&t({promise:e,reason:e._v})})},A=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),W(t,!0))},F=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw C("Promise can't be resolved itself");(t=I(e))?y(function(){var i={_w:n,_d:!1};try{t.call(e,c(F,i,1),c(A,i,1))}catch(e){A.call(i,e)}}):(n._v=e,n._s=1,W(n,!1))}catch(e){A.call({_w:n,_d:!1},e)}}};R||(O=function(e){p(this,O,"Promise","_h"),f(e),i.call(this);try{e(c(F,this,1),c(A,this,1))}catch(e){A.call(this,e)}},(i=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=n(188)(O.prototype,{then:function(e,t){var n=M(v(this,O));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=P?x.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&W(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),o=function(){var e=new i;this.promise=e,this.resolve=c(F,e,1),this.reject=c(A,e,1)},_.f=M=function(e){return e===O||e===s?new o(e):r(e)}),d(d.G+d.W+d.F*!R,{Promise:O}),n(49)(O,"Promise"),n(189)("Promise"),s=n(3).Promise,d(d.S+d.F*!R,"Promise",{reject:function(e){var t=M(this);return(0,t.reject)(e),t.promise}}),d(d.S+d.F*(a||!R),"Promise",{resolve:function(e){return S(a&&this===s?O:this,e)}}),d(d.S+d.F*!(R&&n(132)(function(e){O.all(e).catch(k)})),"Promise",{all:function(e){var t=this,n=M(t),i=n.resolve,r=n.reject,o=b(function(){var n=[],o=0,s=1;m(e,!1,function(e){var a=o++,l=!1;n.push(void 0),s++,t.resolve(e).then(function(e){l||(l=!0,n[a]=e,--s||i(n))},r)}),--s||i(n)});return o.e&&r(o.v),n.promise},race:function(e){var t=this,n=M(t),i=n.reject,r=b(function(){m(e,!1,function(e){t.resolve(e).then(n.resolve,i)})});return r.e&&i(r.v),n.promise}})},function(e,t){e.exports=function(e,t,n,i){if(!(e instanceof t)||void 0!==i&&i in e)throw TypeError(n+": incorrect invocation!");return e}},function(e,t,n){var i=n(43),r=n(130),o=n(131),s=n(15),a=n(85),l=n(113),c={},u={};(t=e.exports=function(e,t,n,d,h){var f,p,m,v,g=h?function(){return e}:l(e),y=i(n,d,t?2:1),_=0;if("function"!=typeof g)throw TypeError(e+" is not iterable!");if(o(g)){for(f=a(e.length);f>_;_++)if((v=t?y(s(p=e[_])[0],p[1]):y(e[_]))===c||v===u)return v}else for(m=g.call(e);!(p=m.next()).done;)if((v=r(m,y,p.value,t))===c||v===u)return v}).BREAK=c,t.RETURN=u},function(e,t){e.exports=function(e,t,n){var i=void 0===n;switch(t.length){case 0:return i?e():e.call(n);case 1:return i?e(t[0]):e.call(n,t[0]);case 2:return i?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return i?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return i?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){var i=n(6),r=n(115).set,o=i.MutationObserver||i.WebKitMutationObserver,s=i.process,a=i.Promise,l="process"==n(37)(s);e.exports=function(){var e,t,n,c=function(){var i,r;for(l&&(i=s.domain)&&i.exit();e;){r=e.fn,e=e.next;try{r()}catch(i){throw e?n():t=void 0,i}}t=void 0,i&&i.enter()};if(l)n=function(){s.nextTick(c)};else if(!o||i.navigator&&i.navigator.standalone)if(a&&a.resolve){var u=a.resolve(void 0);n=function(){u.then(c)}}else n=function(){r.call(i,c)};else{var d=!0,h=document.createTextNode("");new o(c).observe(h,{characterData:!0}),n=function(){h.data=d=!d}}return function(i){var r={fn:i,next:void 0};t&&(t.next=r),e||(e=r,n()),t=r}}},function(e,t,n){var i=n(6).navigator;e.exports=i&&i.userAgent||""},function(e,t,n){var i=n(21);e.exports=function(e,t,n){for(var r in t)n&&e[r]?e[r]=t[r]:i(e,r,t[r]);return e}},function(e,t,n){"use strict";var i=n(6),r=n(3),o=n(14),s=n(16),a=n(7)("species");e.exports=function(e){var t="function"==typeof r[e]?r[e]:i[e];s&&t&&!t[a]&&o.f(t,a,{configurable:!0,get:function(){return this}})}},function(e,t,n){"use strict";var i=n(13),r=n(3),o=n(6),s=n(114),a=n(117);i(i.P+i.R,"Promise",{finally:function(e){var t=s(this,r.Promise||o.Promise),n="function"==typeof e;return this.then(n?function(n){return a(t,e()).then(function(){return n})}:e,n?function(n){return a(t,e()).then(function(){throw n})}:e)}})},function(e,t,n){"use strict";var i=n(13),r=n(73),o=n(116);i(i.S,"Promise",{try:function(e){var t=r.f(this),n=o(e);return(n.e?t.reject:t.resolve)(n.v),t.promise}})},function(e,t,n){e.exports={default:n(193),__esModule:!0}},function(e,t,n){n(56),n(72),e.exports=n(75).f("iterator")},function(e,t,n){e.exports={default:n(195),__esModule:!0}},function(e,t,n){n(196),n(108),n(201),n(202),e.exports=n(3).Symbol},function(e,t,n){"use strict";var i=n(6),r=n(22),o=n(16),s=n(13),a=n(110),l=n(133).KEY,c=n(35),u=n(70),d=n(49),h=n(48),f=n(7),p=n(75),m=n(76),v=n(197),g=n(198),y=n(15),_=n(19),b=n(57),w=n(28),S=n(65),C=n(44),x=n(68),E=n(199),T=n(200),O=n(87),P=n(14),k=n(47),M=T.f,R=P.f,I=E.f,W=i.Symbol,L=i.JSON,D=L&&L.stringify,N=f("_hidden"),A=f("toPrimitive"),F={}.propertyIsEnumerable,B=u("symbol-registry"),V=u("symbols"),j=u("op-symbols"),$=Object.prototype,H="function"==typeof W&&!!O.f,U=i.QObject,G=!U||!U.prototype||!U.prototype.findChild,K=o&&c(function(){return 7!=x(R({},"a",{get:function(){return R(this,"a",{value:7}).a}})).a})?function(e,t,n){var i=M($,t);i&&delete $[t],R(e,t,n),i&&e!==$&&R($,t,i)}:R,X=function(e){var t=V[e]=x(W.prototype);return t._k=e,t},z=H&&"symbol"==typeof W.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof W},Y=function(e,t,n){return e===$&&Y(j,t,n),y(e),t=S(t,!0),y(n),r(V,t)?(n.enumerable?(r(e,N)&&e[N][t]&&(e[N][t]=!1),n=x(n,{enumerable:C(0,!1)})):(r(e,N)||R(e,N,C(1,{})),e[N][t]=!0),K(e,t,n)):R(e,t,n)},Z=function(e,t){y(e);for(var n,i=v(t=w(t)),r=0,o=i.length;o>r;)Y(e,n=i[r++],t[n]);return e},q=function(e){var t=F.call(this,e=S(e,!0));return!(this===$&&r(V,e)&&!r(j,e))&&(!(t||!r(this,e)||!r(V,e)||r(this,N)&&this[N][e])||t)},J=function(e,t){if(e=w(e),t=S(t,!0),e!==$||!r(V,t)||r(j,t)){var n=M(e,t);return!n||!r(V,t)||r(e,N)&&e[N][t]||(n.enumerable=!0),n}},Q=function(e){for(var t,n=I(w(e)),i=[],o=0;n.length>o;)r(V,t=n[o++])||t==N||t==l||i.push(t);return i},ee=function(e){for(var t,n=e===$,i=I(n?j:w(e)),o=[],s=0;i.length>s;)!r(V,t=i[s++])||n&&!r($,t)||o.push(V[t]);return o};H||(a((W=function(){if(this instanceof W)throw TypeError("Symbol is not a constructor!");var e=h(arguments.length>0?arguments[0]:void 0),t=function(n){this===$&&t.call(j,n),r(this,N)&&r(this[N],e)&&(this[N][e]=!1),K(this,e,C(1,n))};return o&&G&&K($,e,{configurable:!0,set:t}),X(e)}).prototype,"toString",function(){return this._k}),T.f=J,P.f=Y,n(118).f=E.f=Q,n(58).f=q,O.f=ee,o&&!n(36)&&a($,"propertyIsEnumerable",q,!0),p.f=function(e){return X(f(e))}),s(s.G+s.W+s.F*!H,{Symbol:W});for(var te="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ne=0;te.length>ne;)f(te[ne++]);for(var ie=k(f.store),re=0;ie.length>re;)m(ie[re++]);s(s.S+s.F*!H,"Symbol",{for:function(e){return r(B,e+="")?B[e]:B[e]=W(e)},keyFor:function(e){if(!z(e))throw TypeError(e+" is not a symbol!");for(var t in B)if(B[t]===e)return t},useSetter:function(){G=!0},useSimple:function(){G=!1}}),s(s.S+s.F*!H,"Object",{create:function(e,t){return void 0===t?x(e):Z(x(e),t)},defineProperty:Y,defineProperties:Z,getOwnPropertyDescriptor:J,getOwnPropertyNames:Q,getOwnPropertySymbols:ee});var oe=c(function(){O.f(1)});s(s.S+s.F*oe,"Object",{getOwnPropertySymbols:function(e){return O.f(b(e))}}),L&&s(s.S+s.F*(!H||c(function(){var e=W();return"[null]"!=D([e])||"{}"!=D({a:e})||"{}"!=D(Object(e))})),"JSON",{stringify:function(e){for(var t,n,i=[e],r=1;arguments.length>r;)i.push(arguments[r++]);if(n=t=i[1],(_(t)||void 0!==e)&&!z(e))return g(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!z(t))return t}),i[1]=t,D.apply(L,i)}}),W.prototype[A]||n(21)(W.prototype,A,W.prototype.valueOf),d(W,"Symbol"),d(Math,"Math",!0),d(i.JSON,"JSON",!0)},function(e,t,n){var i=n(47),r=n(87),o=n(58);e.exports=function(e){var t=i(e),n=r.f;if(n)for(var s,a=n(e),l=o.f,c=0;a.length>c;)l.call(e,s=a[c++])&&t.push(s);return t}},function(e,t,n){var i=n(37);e.exports=Array.isArray||function(e){return"Array"==i(e)}},function(e,t,n){var i=n(28),r=n(118).f,o={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return s&&"[object Window]"==o.call(e)?function(e){try{return r(e)}catch(e){return s.slice()}}(e):r(i(e))}},function(e,t,n){var i=n(58),r=n(44),o=n(28),s=n(65),a=n(22),l=n(107),c=Object.getOwnPropertyDescriptor;t.f=n(16)?c:function(e,t){if(e=o(e),t=s(t,!0),l)try{return c(e,t)}catch(e){}if(a(e,t))return r(!i.f.call(e,t),e[t])}},function(e,t,n){n(76)("asyncIterator")},function(e,t,n){n(76)("observable")},function(e,t,n){n(204);var i=n(3).Object;e.exports=function(e,t){return i.create(e,t)}},function(e,t,n){var i=n(13);i(i.S,"Object",{create:n(68)})},function(e,t,n){"use strict";var i=n(367);n.n(i).a},function(e,t,n){"use strict";var i=n(371);n.n(i).a},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderToolsNS=void 0;const r=n(41),o=n(1),s=n(5),a=n(25),l=n(18),c=n(42),u=a.default(s.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(e,t){return i(this,void 0,void 0,function*(){if(null!==e){let n=e.webContents;(yield n.isDevToolsOpened())?yield n.closeDevTools():yield n.openDevTools(t)}})}e.openDevTool=t,e.enableDevTools=function(e){return i(this,void 0,void 0,function*(){(yield r.FileSystemAWNS.existsAW(s.resolve(__rootDir,"../../enableDevTools")))&&window.addEventListener("keyup",n=>i(this,void 0,void 0,function*(){"F12"===n.key&&n.ctrlKey&&t(yield c.rpc.getCurrentWindow(),e).catch()}),!0)})},e.enableDragOpenFile=function(e){void 0===e&&(e=!1),document.addEventListener("dragover",e=>{e.preventDefault()},!1),document.addEventListener("drop",e=>i(this,void 0,void 0,function*(){e.preventDefault();let t=e.dataTransfer,n=t.files,i=t.items;if(void 0!==i&&null!==i&&i.length>0)for(let e=0;e<i.length;e++){let t=i[e];"string"===t.kind&&"text/uri-list"===t.type?t.getAsString(e=>{o.client.callServerFunctionEx(l.XmpPluginFunction.dropOpenUrl,e).catch()}):t.kind}if(void 0!==n&&null!==n&&n.length>0){let e=[];for(let t=0;t<n.length;t++){let i=n[t].path;void 0!==i&&null!==i&&""!==i&&e.push(i)}e.length>0&&o.client.callServerFunctionEx(l.XmpPluginFunction.dropOpenFiles,e).catch()}}),!1)},e.setBugreportUrlInfo=function(){return i(this,void 0,void 0,function*(){let e=yield c.rpc.getCurrentWindow();if(e&&!(yield e.isDestroyed())){let t=yield e.webContents.getURL();u.setBugreportCustomInfo(t)}})},e.setBugreportCustomInfo=function(e){u.setBugreportCustomInfo(e||"xmp-xdas-renderer")},e.setBugreportSilentMode=function(){u.setBugreportShowMode(1)}}(t.ThunderToolsNS||(t.ThunderToolsNS={}))},,,,,,,,function(e,t,n){e.exports={default:n(276),__esModule:!0}},function(e,t){e.exports=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.target,i=t.root,r=void 0===i?document.body:i,o=t.distance,s=void 0===o?"100px":o,a=t.direction,l=void 0===a?"down":a,c="0px 0px "+s+" 0px";"down"!==l&&(c=s+" 0px 0px 0px");try{var u=new window.IntersectionObserver(function(t){(t[0].isIntersecting||t[0].intersectionRatio)&&e()},{threshold:[1e-6],rootMargin:c,root:r});n||(n=document.createElement("div"),"up"===l?r.insertBefore(n):"down"===l&&r.appendChild(n)),u.observe(n)}catch(e){console.info("Your browser does not support IntersectionObserver,\n    please upgrade your browser or use polyfill:\n    https://github.com/WICG/IntersectionObserver/tree/gh-pages/polyfill")}}},function(e,t,n){"use strict";t.__esModule=!0;var i=o(n(278)),r=o(n(215));function o(e){return e&&e.__esModule?e:{default:e}}t.default=function(){return function(e,t){if(Array.isArray(e))return e;if((0,i.default)(Object(e)))return function(e,t){var n=[],i=!0,o=!1,s=void 0;try{for(var a,l=(0,r.default)(e);!(i=(a=l.next()).done)&&(n.push(a.value),!t||n.length!==t);i=!0);}catch(e){o=!0,s=e}finally{try{!i&&l.return&&l.return()}finally{if(o)throw s}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}()},function(e,t,n){"use strict";n.d(t,"a",function(){return i}),n.d(t,"b",function(){return r});var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return"confirm"===e.popType?n("confirm",{attrs:{options:e.options,"box-id":e.boxId},on:{"update:options":function(t){e.options=t}},nativeOn:{dragstart:function(e){e.preventDefault()}}}):"message-box"===e.popType?n("message-box",{attrs:{options:e.options,formData:e.options.formData,"box-id":e.boxId},on:{"update:formData":function(t){return e.$set(e.options,"formData",t)},"update:form-data":function(t){return e.$set(e.options,"formData",t)}},nativeOn:{dragstart:function(e){e.preventDefault()}}}):n(e.popType,{tag:"component",attrs:{options:e.options,"box-id":e.boxId},nativeOn:{dragstart:function(e){e.preventDefault()}}})},r=[];i._withStripped=!0},,,function(e,t,n){"use strict";n.d(t,"a",function(){return i}),n.d(t,"b",function(){return r});var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-dialog",e._g(e._b({class:{"is-logo":e.options.showIcon},attrs:{visible:""},on:{close:function(t){return e.handleCancel(e.action.Close)}}},"td-dialog",e.$attrs,!1),e.$listeners),[n("div",{staticClass:"td-dialog-comfirm"},[e.options.type?n("span",{staticClass:"td-dialog-comfirm__icon"},[n("td-icon",{attrs:{type:e.icon[e.options.type]}})],1):e._e(),e._v(" "),n("div",{staticClass:"td-dialog-comfirm__content"},[n("p",{staticClass:"td-dialog-comfirm__title",class:e.options.titleClass},[e._v(e._s(e.options.title))]),e._v(" "),n("div",{staticClass:"td-dialog-comfirm__text"},[e.options.checkboxEnabled?n("td-checkbox",{attrs:{value:e.options.checkboxChecked},on:{input:e.handleInput}},[e._v(e._s(e.options.checkboxLabel))]):e.options.content?n("span",{domProps:{innerHTML:e._s(e.options.content)}}):e.options.fileName?n("div",{staticClass:"xlx-dialog-comfirm__film-name",attrs:{title:e.options.fileName}},[e._v(e._s(e.options.fileName))]):e._e()],1)])]),e._v(" "),n("template",{slot:"footer"},["okVisible"in e.options&&!e.options.okVisible?e._e():n("td-button",{class:e.options.okClass,on:{click:e.handleOK}},[e._v(e._s(e.options.okText||"确定"))]),e._v(" "),"cancelVisible"in e.options&&!e.options.cancelVisible?e._e():n("td-button",{class:e.options.cancelClass,attrs:{secondary:""},on:{click:function(t){return e.handleCancel(e.action.Cancel)}}},[e._v(e._s(e.options.cancelText||"取消"))])],1)],2)},r=[];i._withStripped=!0},function(e,t,n){"use strict";n.d(t,"a",function(){return i}),n.d(t,"b",function(){return r});var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-dialog",{attrs:{"custom-class":["xlx-dialog--"+e.options.size,"xlx-dialog-setting"],visible:""},on:{close:e.handleCancel}},[n("h2",{attrs:{slot:"header"},slot:"header"},[e._v(e._s(e.options.title))]),e._v(" "),e.options.schemas?n("div",{staticClass:"xlx-dialog-setting__content"},[n("div",{staticClass:"xlx-dialog-setting__speed"})]):e.options.message?[e._v(e._s(e.options.message))]:e._e(),e._v(" "),n("template",{slot:"footer"},["okVisible"in e.options&&!e.options.okVisible?e._e():n("td-button",{attrs:{disabled:!e.comfirmButtonActive},on:{click:e.handleOK}},[e._v(e._s(e.options.okText||"确定"))]),e._v(" "),"cancelVisible"in e.options&&!e.options.cancelVisible?e._e():n("td-button",{attrs:{secondary:""},on:{click:e.handleCancel}},[e._v(e._s(e.options.cancelText||"取消"))])],1)],2)},r=[];i._withStripped=!0},function(e,t,n){"use strict";n.d(t,"a",function(){return i}),n.d(t,"b",function(){return r});var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-dialog",{attrs:{"custom-class":"xmpc-dialog-screen",visible:""},on:{close:e.onCloseClick},nativeOn:{keydown:function(t){return e.onKeyDown(t)}}},[n("h2",{attrs:{slot:"header"},slot:"header"},[e._v("DLNA/WiDi播放设备选择")]),e._v(" "),n("div",{staticClass:"xmpc-dialog-screen__body"},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.searching,expression:"searching"}],staticClass:"screen-loading is-empty"},[e._v(e._s(e.searchTips))]),e._v(" "),n("ul",{directives:[{name:"show",rawName:"v-show",value:!e.searching,expression:"!searching"}],staticClass:"xmpc-dialog-screen__list",on:{click:e.onClickEmptyBlock}},e._l(e.deviceMap,function(t,i){return n("li",{key:i,staticClass:"xmpc-dialog-screen__item",class:{"is-checked":t.isActive},on:{click:function(n){return n.stopPropagation(),e.onItemSelect(t)},dblclick:function(n){return e.onItemDbClick(t)}}},[n("span",{staticClass:"device-name"},[e._v(e._s(t.deviceType+": "+t.name))])])}),0)]),e._v(" "),n("div",{staticClass:"xmpc-dialog__footer",attrs:{slot:"footer"},slot:"footer"},[n("div",{staticClass:"xmpc-dialog__button"},[e.searching?e._e():n("button",{class:"td-button "+(e.itemSelected?"":"td-button--secondary"),on:{click:e.onClickConnect}},[e._v(e._s(e.itemConnected?"断开":"连接"))]),e._v(" "),n("button",{staticClass:"td-button",on:{click:e.onClickRefresh}},[e._v("刷新")])])])])},r=[];i._withStripped=!0},function(e,t,n){"use strict";n.d(t,"a",function(){return i}),n.d(t,"b",function(){return r});var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"xmpc-dialog-subtitle",on:{pointerup:function(t){return e.onpointerup(t)}}},[n("div",{staticClass:"xmpc-dialog-subtitle__header"},[n("div",{staticClass:"xmpc-dialog-subtitle__close",on:{click:e.clickClose}},[n("i",{staticClass:"td-icon-close"})])]),e._v(" "),n("div",{staticClass:"xmpc-dialog-subtitle__form"},[n("ul",[n("li",{staticClass:"xmpc-dialog-subtitle__item"},[n("span",{staticClass:"xmpc-dialog-subtitle__title"},[e._v("字幕同步")]),e._v(" "),n("div",{staticClass:"xmpc-dialog-subtitle__content"},[n("button",{staticClass:"td-button td-button--other td-button--small",on:{click:e.timeLineDefault}},[e._v(" 默认")]),e._v(" "),n("div",{staticClass:"xmpc-input-number"},[n("span",{staticClass:"xmpc-input-number__button",on:{click:e.decreaseTimeLine}},[e._v("-")]),e._v(" "),n("input",{directives:[{name:"model",rawName:"v-model",value:e.timeLineEx,expression:"timeLineEx"}],staticClass:"xmpc-input-number__input",attrs:{type:"text"},domProps:{value:e.timeLineEx},on:{input:[function(t){t.target.composing||(e.timeLineEx=t.target.value)},e.onTimeLineInput]}}),e._v(" "),n("span",{staticClass:"xmpc-input-number__text"},[e._v("秒")]),e._v(" "),n("span",{staticClass:"xmpc-input-number__button",on:{click:e.increaseTimeLine}},[e._v("+")])])])]),e._v(" "),n("li",{staticClass:"xmpc-dialog-subtitle__item"},[n("span",{staticClass:"xmpc-dialog-subtitle__title"},[e._v("字幕位置")]),e._v(" "),n("div",{staticClass:"xmpc-dialog-subtitle__slider"},[n("span",[e._v("顶部")]),e._v(" "),n("td-slider",{attrs:{scales:[-8,100]},on:{change:e.handleSubtitlePositionChange},model:{value:e.subtitlePosition,callback:function(t){e.subtitlePosition=t},expression:"subtitlePosition"}}),e._v(" "),n("span",[e._v("底部")])],1)]),e._v(" "),n("li",{staticClass:"xmpc-dialog-subtitle__item"},[n("span",{staticClass:"xmpc-dialog-subtitle__title"},[e._v("文字设置")]),e._v(" "),n("div",{staticClass:"xmpc-dialog-subtitle__select"},[n("div",{staticClass:"td-select is-top",attrs:{tabindex:"0"}},[n("div",{staticClass:"td-select-group",on:{click:function(t){e.showFontNameList=!0},pointerup:function(t){return t.stopPropagation(),e.onpointerup(t,"showFontNameList")}}},[n("span",{staticClass:"td-select-group__label"},[e._v(" "+e._s(e.fontName)+" ")]),e._v(" "),e._m(0)]),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.showFontNameList,expression:"showFontNameList"}],staticClass:"td-dropdown-menu"},[n("ul",e._l(["默认","宋体","楷体","微软雅黑","华文行楷"],function(t){return n("li",{staticClass:"td-dropdown-menu__item",on:{click:function(n){return n.stopPropagation(),e.selectFontName(t)}}},[e._v("\n            "+e._s(t)+"\n          ")])}),0)])]),e._v(" "),n("div",{staticClass:"td-select is-top",attrs:{tabindex:"0"}},[n("div",{staticClass:"td-select-group",on:{click:function(t){e.showFontSizeList=!0},pointerup:function(t){return t.stopPropagation(),e.onpointerup(t,"showFontSizeList")}}},[n("span",{staticClass:"td-select-group__label"},[e._v(" "+e._s(e.fontSize)+" ")]),e._v(" "),e._m(1)]),e._v(" "),n("ul",{directives:[{name:"show",rawName:"v-show",value:e.showFontSizeList,expression:"showFontSizeList"}],staticClass:"td-dropdown-menu"},e._l(["默认","14","16","18","20","24","26","28","40","52","64","76"],function(t){return n("li",{staticClass:"td-dropdown-menu__item",on:{click:function(n){return n.stopPropagation(),e.selectFontSize(t)}}},[e._v("\n          "+e._s(t)+"\n        ")])}),0)]),e._v(" "),n("div",{staticClass:"xmpc-select-color is-disabled"},[n("div",{staticClass:"xmpc-select-group"},[n("span",{staticClass:"xmpc-select-group__label",style:e.fontColorStyle}),e._v(" "),e._m(2),e._v(" "),n("input",{directives:[{name:"model",rawName:"v-model",value:e.fontColor,expression:"fontColor"}],staticClass:"xmpc-input-color",attrs:{type:"color"},domProps:{value:e.fontColor},on:{input:function(t){t.target.composing||(e.fontColor=t.target.value)}}})])])])])])]),e._v(" "),n("div",{staticClass:"xmpc-dialog-subtitle__footer"},[n("a",{attrs:{href:"javascript:;"},on:{click:e.reset}},[n("i",{staticClass:"xmpc-icon-refresh"}),e._v("重置")])])])},r=[function(){var e=this.$createElement,t=this._self._c||e;return t("a",{staticClass:"td-select__drop",attrs:{href:"javascript:;"}},[t("i",{staticClass:"td-icon-arrow-drop"})])},function(){var e=this.$createElement,t=this._self._c||e;return t("a",{staticClass:"td-select__drop",attrs:{href:"javascript:;"}},[t("i",{staticClass:"td-icon-arrow-drop"})])},function(){var e=this.$createElement,t=this._self._c||e;return t("a",{staticClass:"xmpc-select__drop",attrs:{href:"javascript:;"}},[t("i",{staticClass:"xmpc-icon-arrow-file"})])}];i._withStripped=!0},,,,,,,,,function(e,t){e.exports=require("url")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PerformanceMonitorUtilNS=void 0;const i=n(5),r=n(25),o=n(168);let s=r.default(i.join(o.GlobalDataNS.getResourcesPath(),"bin/ThunderHelper.node")),a=r.default(i.join(o.GlobalDataNS.getResourcesPath(),"bin/XDASEnhancerAddon.node"));const l="xmp_xdas_profile_stat";let c=void 0;!function(e){function t(e){return null===e||void 0===e}function n(e=""){let n;if("string"==typeof e)n=e;else if(!t(e)&&"object"==typeof e){let i=[];for(let n in e)t(e[n])||i.push(n+"="+encodeURIComponent(e[n]));n=i.join(",")}return n}function r(e,t="",i="",r=0,o=0,a=0,l=0,c="",u=0){let d=0;do{if(void 0===e){d=1;break}let h=n(c);global.b={key:e,attr1:t,attr2:t,cost1:r,cost2:o,cost3:a,cost4:l,extDataStr:h,cookie:u},d=s.trackEvent(e,t,i,r,o,a,l,h,u)}while(0);return d}e.formatBussinessName=function(e){let n="";if((t(e)||0===e.length)&&"renderer"===process.type){let e=i.normalize(decodeURIComponent(window.location.href)),t=e.indexOf(o.GlobalDataNS.getResourcesPath());t=t>-1?t+o.GlobalDataNS.getResourcesPath().length+1:t;let r=e.length-1,s=e.indexOf("?"),a=e.indexOf("#");r=s>-1?Math.min(s-1,r):r,r=a>-1?Math.min(a-1,r):r,t>-1&&r>=t&&(n=e.substr(t,r-t+1))}return 0===n.length&&t(n=t(e)||0===e.length?process.type:e)&&(n=""),n=n.replace(/\||,|;/g,"_")},e.isNullOrUndefined=t,e.trackEvent=r,e.trackXdasProfileStatEvent=function(e="",t="",n=0,i=0,o=0,s=0,a="",c=0){return r(l,e,t,n,i,o,s,a,c)},e.getOSVersionString=function(){if(void 0===c){let e=a.os.getOSVersion();e&&e.versionNumber&&(c=`${e.versionNumber.major||"0"}.${e.versionNumber.minor||"0"}.${e.versionNumber.build||"0"}.${e.versionNumber.patch||"0"}`)}return c}}(t.PerformanceMonitorUtilNS||(t.PerformanceMonitorUtilNS={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageBoxNS=void 0,function(e){let t;!function(e){e[e.OK=0]="OK",e[e.Cancel=1]="Cancel",e[e.Close=2]="Close"}(t=e.Action||(e.Action={}))}(t.MessageBoxNS||(t.MessageBoxNS={}))},function(e,t,n){var i=n(13),r=n(3),o=n(35);e.exports=function(e,t){var n=(r.Object||{})[e]||Object[e],s={};s[e]=t(n),i(i.S+i.F*o(function(){n(1)}),"Object",s)}},,,,,,,,,,,,,,,,function(e,t,n){e.exports={default:n(281),__esModule:!0}},function(module,exports){function parseExpression(e){var t=/^(-)?(.*)/.exec(e);return{prop:t[2],desc:"-"===t[1]}}function compare(e,t,n){return e===t?0:e<t?n?1:-1:n?-1:1}function getPropertyValue(obj,property){return eval("obj."+property)}module.exports=function(){var e=Array.prototype.concat.apply([],arguments)||[];return function(t,n){for(var i=0;i<e.length;){if("function"==typeof e[i])var r=e[i](t,n);else{var o=parseExpression(e[i]);r=compare(getPropertyValue(t,o.prop),getPropertyValue(n,o.prop),o.desc)}if(0!==r)return r;if(i===e.length-1)return r;i++}}}},function(e,t,n){n(95),e.exports=n(255)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=`messageBoxRendererContext${n(233).parse(location.href,!0).query.boxId}`;n(1).client.start({name:i});const r=n(256),o=n(105),s=n(258);n(373);const a=n(207),l=n(287);r.BrowserRendererPerformanceMonitorStatLoaderNS.init("message-box-renderer"),a.ThunderToolsNS.setBugreportCustomInfo("message-box-renderer"),new o.default({components:{App:s.default},render:e=>e("app")}).$mount("#app"),a.ThunderToolsNS.enableDevTools().catch(),a.ThunderToolsNS.enableDragOpenFile(),a.ThunderToolsNS.setBugreportSilentMode(),a.ThunderToolsNS.setBugreportUrlInfo().catch(),l.crossProcMenu.init(i)},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.BrowserRendererPerformanceMonitorStatLoaderNS=void 0;n(45);const r=n(5),o=n(8),s=n(0),a=n(25),l=n(60),c=n(168),u=n(257),d=n(234);n(40);let h=new(a.default(r.join(c.GlobalDataNS.getResourcesPath(),"bin/XDASEnhancerAddon.node")).os.ColdStartupHelper)("XMP");class f{constructor(){this.browserStartType=void 0,this.getBrowserStartTypeInRendererFuncPromise=null}init(){return i(this,void 0,void 0,function*(){this.initStartTypeBrowserEnv(),this.initPageLoadTimeStat(),this.initCriticalErrorLogStat()})}initPageLoadTimeStat(){if("renderer"===process.type){let e=1e3,t=window.setInterval(()=>i(this,void 0,void 0,function*(){do{if(--e<0){window.clearInterval(t);break}if(null===window.performance||void 0===window.performance)break;let n=window.performance.timing;n&&n.loadEventEnd>0&&(window.clearInterval(t),yield this.pageLoadTimeStat())}while(0)}),300)}}pageLoadTimeStat(){return i(this,void 0,void 0,function*(){do{if("renderer"!==process.type)break;if(null===window.performance.timing||void 0===window.performance.timing)break;let e=h.isColdStartup()?1:0,t=d.PerformanceMonitorUtilNS.getOSVersionString(),n=window.performance.timing,i=n.loadEventEnd-n.navigationStart,r=n.fetchStart-n.navigationStart,o=n.domainLookupEnd-n.domainLookupStart,s=n.connectEnd-n.connectStart,a=n.responseStart-n.requestStart,l=n.responseEnd-n.responseStart,c=n.domComplete-n.domLoading,f=yield this.getBrowserStartType();d.PerformanceMonitorUtilNS.trackXdasProfileStatEvent("page_load_time","",0,0,0,0,`start_type=${f},cold_start_up=${e},os_version=${t},load_time=${i},before_fetch_time=${r},domin_lookup_time=${o},connect_time=${s},first_response_time=${a},responseTime=${l},domTime=${c},process=${yield u.BasePerformanceMonitorStatNS.getBusinessName()}`)}while(0)})}initStartTypeBrowserEnv(){"browser"===process.type&&o.ipcMain.on(l.ThunderChannelList.channelRMGetBrowserStartType,e=>i(this,void 0,void 0,function*(){let t=yield this.getBrowserStartType();e.sender.send(l.ThunderChannelList.channelMRGetBrowserStartTypeResult,t)}))}initCriticalErrorLogStat(){s.default.hook("beforeLog",(e,t,...n)=>{e===s.LogLevel.Critical&&d.PerformanceMonitorUtilNS.trackXdasProfileStatEvent("critical_error","",0,0,0,0,`module_name=${t},messages=${n}`)})}getBrowserStartTypeInMain(){if(void 0===this.browserStartType){let e=process.argv.length,t=process.argv;for(let n=0;n<e;n++){let e=t[n];if(e.startsWith("-StartType:")){this.browserStartType=e.substring("-StartType:".length);break}}void 0===this.browserStartType&&(this.browserStartType="")}return this.browserStartType}getBrowserStartTypeInRenderer(){return i(this,void 0,void 0,function*(){return null===this.getBrowserStartTypeInRendererFuncPromise&&(this.getBrowserStartTypeInRendererFuncPromise=new Promise(e=>{o.ipcRenderer.send(l.ThunderChannelList.channelRMGetBrowserStartType),o.ipcRenderer.once(l.ThunderChannelList.channelMRGetBrowserStartTypeResult,(t,n)=>{this.browserStartType=n,e(n),this.getBrowserStartTypeInRendererFuncPromise=null})})),this.getBrowserStartTypeInRendererFuncPromise})}getBrowserStartType(){return i(this,void 0,void 0,function*(){return new Promise(e=>i(this,void 0,void 0,function*(){let t="";t=void 0===this.browserStartType?"browser"===process.type?this.getBrowserStartTypeInMain():yield this.getBrowserStartTypeInRenderer():this.browserStartType,e(t)}))})}showExceptionHint(e,t,n,r){return i(this,void 0,void 0,function*(){0})}}!function(e){e.init=function(e){u.BasePerformanceMonitorStatNS.init(e,new f)}}(t.BrowserRendererPerformanceMonitorStatLoaderNS||(t.BrowserRendererPerformanceMonitorStatLoaderNS={}))},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.BasePerformanceMonitorStatNS=void 0;const r=n(5),o=n(24);let s=null;const a=n(25),l=n(168),c=n(234);let u=a.default(r.join(l.GlobalDataNS.getResourcesPath(),"bin/XDASEnhancerAddon.node")),d=new u.os.ColdStartupHelper("XMP"),h=null;var f;!function(e){let t=void 0,r=new Set,a=null;function u(){return i(this,void 0,void 0,function*(){return new Promise(e=>i(this,void 0,void 0,function*(){void 0===t&&(null===a&&(a=new Promise(e=>{e(t=c.PerformanceMonitorUtilNS.formatBussinessName(""))})),t=yield a),e(t)}))})}function d(e){let t="";do{if(null===e||void 0===e)break;switch(typeof e){case"string":t=e;break;case"object":t=o.inspect(e)||"";break;case"number":case"boolean":t=e.toString()||""}}while(0);return t}function f(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function p(e){return i(this,void 0,void 0,function*(){return new Promise(t=>i(this,void 0,void 0,function*(){let i=void 0;null===s&&(s=yield Promise.resolve().then(()=>n(169)));let r=s.createHash("md5");null!==r&&(i=r.update(e).digest("hex")),t(i)}))})}function m(e,t,n,o){return i(this,void 0,void 0,function*(){let i=d(t),s=d(n),a=yield p(s),m=function(e){let t=new RegExp(f("file:///"),"g"),n=new RegExp(f(l.GlobalDataNS.getResourcesPath()+"\\"),"g"),i=new RegExp(f(encodeURI(l.GlobalDataNS.getResourcesPath().replace(/\\/g,"/")+"/")),"g");return e.replace(t,"").replace(n,"").replace(i,"")}(d(o)),v=yield p(m),g=`${e}:${a}:${v}`;r.has(g)||(r.add(g),c.PerformanceMonitorUtilNS.trackXdasProfileStatEvent("uncaught_exception","",0,0,0,0,`type=${e},business_name=${yield u()},code=${i},message_hash=${a},message=${encodeURI(s)},stack_hash=${v},stack=${encodeURI(m)}`)),h&&(yield h.showExceptionHint(e,i,s,m))})}function v(e){return i(this,void 0,void 0,function*(){console.error(e);let t=e||{};yield m("unhandledRejection",t.code,t instanceof Error?t.message:t,t.stack)})}e.init=function(e){t=e,function(){process.on("uncaughtException",e=>i(this,void 0,void 0,function*(){console.error(e);let t=e||{};yield m("uncaughtException",t.code,t.message,t.stack)})),"renderer"!==process.type?process.on("unhandledRejection",(e,t)=>i(this,void 0,void 0,function*(){yield v(e)})):window.addEventListener("unhandledrejection",e=>i(this,void 0,void 0,function*(){yield v(e&&e.reason||{})}))}()},e.getBusinessName=u}(f||(f={})),function(e){e.init=function(e,t){f.init(e),(h=t)&&h.init().catch(e=>{console.log(e)})},e.getBusinessName=function(){return i(this,void 0,void 0,function*(){return f.getBusinessName()})},e.trackColdStartUpEvent=function(e){return i(this,void 0,void 0,function*(){let t=d.isColdStartup()?1:0,n=c.PerformanceMonitorUtilNS.getOSVersionString(),i=u.os.performanceMonitorReporter.getProcessUptime(),r="";h&&(r=yield h.getBrowserStartType());let o=`key=${e},start_type=${r},cold_start_up=${t},os_version=${n},cost_time=${i}`;c.PerformanceMonitorUtilNS.trackXdasProfileStatEvent("cold_start_up","",0,0,0,0,o)})}}(t.BasePerformanceMonitorStatNS||(t.BasePerformanceMonitorStatNS={}))},function(e,t,n){"use strict";n.r(t);var i=n(218),r=n(136);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return r[e]})}(o);n(285),n(286),n(205),n(206);var s=n(2),a=Object(s.a)(r.default,i.a,i.b,!1,null,null,null);a.options.__file="src/message-box-renderer/app.vue",t.default=a.exports},function(e,t,n){"use strict";n.r(t);var i=n(221),r=n(138);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return r[e]})}(o);n(266);var s=n(2),a=Object(s.a)(r.default,i.a,i.b,!1,null,null,null);a.options.__file="src/message-box-renderer/views/confirm.vue",t.default=a.exports},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderWindowNS=void 0;const r=n(8),o=n(60),s=n(261),a=n(262),l=n(42),c=n(26);!function(e){e.resizeToFitContent=function(e=0,t=0,n){return i(this,void 0,void 0,function*(){let i=yield l.rpc.getCurrentWindow();if(e>0&&t>0)yield i.setSize(e,t);else{let e=document.querySelector(".td-dialog");r.dialog&&(yield i.setSize(e.offsetWidth,e.offsetHeight))}n&&"function"==typeof n&&(yield n()),i.show()})},e.autoAdaptScrollIntoView=function(e,t,n){let i=t.scrollTop,r=t.getBoundingClientRect(),o=e.getBoundingClientRect(),s=r.bottom;void 0!==n&&"number"==typeof n&&n>0&&(s=r.top+n),o.top<r.top?t.scrollTop=i-(r.top-o.top):o.bottom>s&&(t.scrollTop=i+(o.bottom-s))},e.fitWindowPosInParent=function(e,t){return i(this,void 0,void 0,function*(){if(e||(e=yield l.rpc.getCurrentWindow()),t||(t=yield e.getParentWindow()),!e||!t||(yield e.isDestroyed())||(yield t.isDestroyed()))return;let n=yield e.getNativeWindowHandle(),i=yield t.getNativeWindowHandle();r.ipcRenderer.send(o.ThunderChannelList.channelRMSetPosition,n.readUIntLE(0,4),i.readUIntLE(0,4))})},e.getWindowsInParentCenterPos=function(e,t,n){return i(this,void 0,void 0,function*(){n||(n=yield l.rpc.getCurrentWindow());let i=yield n.getPosition(),r=yield n.getSize(),o=yield s.default.getCursorScreenPoint(),a=yield s.default.getDisplayNearestPoint(o),c=a.size.width,u=a.size.height,d=r[0],h=r[1];i[0]+d>c&&(d=c-i[0]),i[1]+h>u&&(h=u-i[1]);let f=i[0]+(d-e)/2,p=i[1]+(h-t)/2;return f<0?f=0:f>c-e&&(f=c-e),p<0?p=0:p>u-t&&(p=u-t),[Math.round(f),Math.round(p)]})},e.centerWnd=function(e,t,n){return i(this,void 0,void 0,function*(){do{if(!e||!t||e.isDestroyed()||t.isDestroyed())break;let n=e.getNativeWindowHandle().readUIntLE(0,4);if(!n)break;let i=t.getPosition(),r=t.getSize(),o=e.getSize(),a=yield s.default.getCursorScreenPoint(),l=yield s.default.getDisplayNearestPoint(a),u=l.scaleFactor,d=l.size.width,h=l.size.height,f=r[0],p=r[1];i[0]+f>d&&(f=d-i[0]),i[1]+p>h&&(p=h-i[1]);let m=i[0]+(f-o[0])/2,v=i[1]+(p-o[1])/2;m<0?m=0:m>d-o[0]&&(m=d-o[0]),v<0?v=0:v>h-o[1]&&(v=h-o[1]),c.ThunderHelper.setWindowPosEx(n,0,m*u,v*u,0,0,5)}while(0)})},e.bringWindowToTop=function(e){return i(this,void 0,void 0,function*(){e||(e=yield a.default.getWindowHandle()),r.ipcRenderer.send(o.ThunderChannelList.channelMRBringWindowToTop,e)})}}(t.ThunderWindowNS||(t.ThunderWindowNS={}))},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const r=n(40);const o=new class{constructor(){this.screen=null}init(){return i(this,void 0,void 0,function*(){this.screen=yield r.electron.screen.__resolve()})}getAllDisplays(){return i(this,void 0,void 0,function*(){return this.screen||(yield this.init()),this.screen.getAllDisplays()})}getCursorScreenPoint(){return i(this,void 0,void 0,function*(){return this.screen||(yield this.init()),this.screen.getCursorScreenPoint()})}getDisplayMatching(e){return i(this,void 0,void 0,function*(){return this.screen||(yield this.init()),this.screen.getDisplayMatching(e)})}getDisplayNearestPoint(e){return i(this,void 0,void 0,function*(){return this.screen||(yield this.init()),this.screen.getDisplayNearestPoint(e)})}getMenuBarHeight(){return i(this,void 0,void 0,function*(){return this.screen||(yield this.init()),this.screen.getMenuBarHeight()})}getPrimaryDisplay(){return i(this,void 0,void 0,function*(){return this.screen||(yield this.init()),this.screen.getPrimaryDisplay()})}};t.default=o},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const r=n(40),o=n(11),s=n(0).default.getLogger("mian-window");const a=new class{constructor(){this.mainWindow=null,this.handle=0,this.closed=!1,this.inited=!1,this.isIniting=!1,this.emitter=new o.EventEmitter,this.emitter.setMaxListeners(0)}init(){return i(this,void 0,void 0,function*(){this.mainWindow||this.closed||(this.isIniting?yield new Promise(e=>i(this,void 0,void 0,function*(){this.emitter.on("OnInit",()=>{e()})})):(this.isIniting=!0,this.mainWindow=yield r.global.mainRendererWindow.__resolve(),this.isIniting=!1,this.emitter.emit("OnInit"),this.emitter.removeAllListeners())),this.inited||(this.inited=!0,this.mainWindow.on("closed",()=>{this.closed=!0}))})}getWindow(){return i(this,void 0,void 0,function*(){return(yield this.isMainWindowValid())?this.mainWindow:null})}getId(){return i(this,void 0,void 0,function*(){return(yield this.isMainWindowValid())?this.mainWindow.id:0})}getChildWindows(){return i(this,void 0,void 0,function*(){return(yield this.isMainWindowValid())?this.mainWindow.getChildWindows():[]})}close(){return i(this,void 0,void 0,function*(){(yield this.isMainWindowValid())&&this.mainWindow.close()})}showInactive(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.showInactive()})}getBounds(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.getBounds()})}setBounds(e){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.setBounds(e)})}getWindowHandle(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid()){if(!this.handle&&this.mainWindow){let e=yield this.mainWindow.getNativeWindowHandle();this.handle=e.readUInt32LE(0)}return this.handle}})}getWebContents(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.webContents})}isFullScreen(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.isFullScreen()})}setFullScreen(e){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.setFullScreen(e)})}setResizeable(e){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.setResizable(e)})}getMaximumSize(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.getMaximumSize()})}isResizable(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.isResizable()})}focus(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.focus()})}getMinimumSize(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.getMinimumSize()})}setMinimumSize(e,t){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.setMinimumSize(e,t)})}setMaximumSize(e,t){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.setMaximumSize(e,t)})}isMinimized(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.isMinimized()})}minimize(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.minimize()})}isMaximized(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.isMaximized()})}maximize(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.maximize()})}unmaximize(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.unmaximize()})}hide(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.hide()})}show(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return s.warning("cqm mainwindow show"),this.mainWindow.show()})}restore(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.restore()})}isVisible(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.isVisible()})}isDestroyed(){return i(this,void 0,void 0,function*(){return!(this.mainWindow&&!this.closed)||this.mainWindow.isDestroyed()})}on(e,t){return i(this,void 0,void 0,function*(){(yield this.isMainWindowValid())&&(yield this.mainWindow.on(e,t))})}removeListener(e,t){return i(this,void 0,void 0,function*(){(yield this.isMainWindowValid())&&this.mainWindow.removeListener(e,t)})}removeAllListener(){return i(this,void 0,void 0,function*(){(yield this.isMainWindowValid())&&this.mainWindow.removeAllListeners()})}once(e,t){return i(this,void 0,void 0,function*(){(yield this.isMainWindowValid())&&(yield this.mainWindow.once(e,t))})}send(e,...t){return i(this,void 0,void 0,function*(){(yield this.isMainWindowValid())&&(yield this.mainWindow.send(e,...t))})}hookWindowMessage(e,t){return i(this,void 0,void 0,function*(){(yield this.isMainWindowValid())&&this.mainWindow.hookWindowMessage(e,(e,n)=>{t(e,n)})})}unhookWindowMessage(e){return i(this,void 0,void 0,function*(){(yield this.isMainWindowValid())&&this.mainWindow.unhookWindowMessage(e)})}center(){return i(this,void 0,void 0,function*(){if(yield this.isMainWindowValid())return this.mainWindow.center()})}isMainWindowValid(){return i(this,void 0,void 0,function*(){if(!this.mainWindow){if(this.closed)return!1;yield this.init()}return this.mainWindow&&!this.closed})}};t.default=a},function(e,t,n){n(56),n(264),e.exports=n(3).Array.from},function(e,t,n){"use strict";var i=n(43),r=n(13),o=n(57),s=n(130),a=n(131),l=n(85),c=n(265),u=n(113);r(r.S+r.F*!n(132)(function(e){Array.from(e)}),"Array",{from:function(e){var t,n,r,d,h=o(e),f="function"==typeof this?this:Array,p=arguments.length,m=p>1?arguments[1]:void 0,v=void 0!==m,g=0,y=u(h);if(v&&(m=i(m,p>2?arguments[2]:void 0,2)),void 0==y||f==Array&&a(y))for(n=new f(t=l(h.length));t>g;g++)c(n,g,v?m(h[g],g):h[g]);else for(d=y.call(h),n=new f;!(r=d.next()).done;g++)c(n,g,v?s(d,m,[r.value,g],!0):r.value);return n.length=g,n}})},function(e,t,n){"use strict";var i=n(14),r=n(44);e.exports=function(e,t,n){t in e?i.f(e,t,r(0,n)):e[t]=n}},function(e,t,n){"use strict";var i=n(358);n.n(i).a},function(e,t,n){"use strict";n.r(t);var i=n(222),r=n(140);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return r[e]})}(o);var s=n(2),a=Object(s.a)(r.default,i.a,i.b,!1,null,null,null);a.options.__file="src/message-box-renderer/views/message-box.vue",t.default=a.exports},function(e,t,n){"use strict";n.r(t);var i=n(223),r=n(142);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return r[e]})}(o);var s=n(2),a=Object(s.a)(r.default,i.a,i.b,!1,null,null,null);a.options.__file="src/message-box-renderer/views/dlna-select.vue",t.default=a.exports},function(e,t,n){"use strict";n.r(t);var i=n(224),r=n(144);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return r[e]})}(o);var s=n(2),a=Object(s.a)(r.default,i.a,i.b,!1,null,null,null);a.options.__file="src/message-box-renderer/views/subtitle-setting.vue",t.default=a.exports},function(e,t,n){n(271),e.exports=n(3).Object.assign},function(e,t,n){var i=n(13);i(i.S+i.F,"Object",{assign:n(272)})},function(e,t,n){"use strict";var i=n(16),r=n(47),o=n(87),s=n(58),a=n(57),l=n(129),c=Object.assign;e.exports=!c||n(35)(function(){var e={},t={},n=Symbol(),i="abcdefghijklmnopqrst";return e[n]=7,i.split("").forEach(function(e){t[e]=e}),7!=c({},e)[n]||Object.keys(c({},t)).join("")!=i})?function(e,t){for(var n=a(e),c=arguments.length,u=1,d=o.f,h=s.f;c>u;)for(var f,p=l(arguments[u++]),m=d?r(p).concat(d(p)):r(p),v=m.length,g=0;v>g;)f=m[g++],i&&!h.call(p,f)||(n[f]=p[f]);return n}:c},function(e,t,n){n(274),e.exports=n(3).Object.keys},function(e,t,n){var i=n(57),r=n(47);n(236)("keys",function(){return function(e){return r(i(e))}})},function(e,t,n){var i=n(3),r=i.JSON||(i.JSON={stringify:JSON.stringify});e.exports=function(e){return r.stringify.apply(r,arguments)}},function(e,t,n){n(72),n(56),e.exports=n(277)},function(e,t,n){var i=n(15),r=n(113);e.exports=n(3).getIterator=function(e){var t=r(e);if("function"!=typeof t)throw TypeError(e+" is not iterable!");return i(t.call(e))}},function(e,t,n){e.exports={default:n(279),__esModule:!0}},function(e,t,n){n(72),n(56),e.exports=n(280)},function(e,t,n){var i=n(86),r=n(7)("iterator"),o=n(30);e.exports=n(3).isIterable=function(e){var t=Object(e);return void 0!==t[r]||"@@iterator"in t||o.hasOwnProperty(i(t))}},function(e,t,n){n(282),e.exports=n(3).Object.freeze},function(e,t,n){var i=n(19),r=n(133).onFreeze;n(236)("freeze",function(e){return function(t){return e&&i(t)?e(r(t)):t}})},function(e,t,n){var i=function(){return this}()||Function("return this")(),r=i.regeneratorRuntime&&Object.getOwnPropertyNames(i).indexOf("regeneratorRuntime")>=0,o=r&&i.regeneratorRuntime;if(i.regeneratorRuntime=void 0,e.exports=n(284),r)i.regeneratorRuntime=o;else try{delete i.regeneratorRuntime}catch(e){i.regeneratorRuntime=void 0}},function(e,t){!function(t){"use strict";var n,i=Object.prototype,r=i.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag",c="object"==typeof e,u=t.regeneratorRuntime;if(u)c&&(e.exports=u);else{(u=t.regeneratorRuntime=c?e.exports:{}).wrap=b;var d="suspendedStart",h="suspendedYield",f="executing",p="completed",m={},v={};v[s]=function(){return this};var g=Object.getPrototypeOf,y=g&&g(g(R([])));y&&y!==i&&r.call(y,s)&&(v=y);var _=x.prototype=S.prototype=Object.create(v);C.prototype=_.constructor=x,x.constructor=C,x[l]=C.displayName="GeneratorFunction",u.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===C||"GeneratorFunction"===(t.displayName||t.name))},u.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,l in e||(e[l]="GeneratorFunction")),e.prototype=Object.create(_),e},u.awrap=function(e){return{__await:e}},E(T.prototype),T.prototype[a]=function(){return this},u.AsyncIterator=T,u.async=function(e,t,n,i){var r=new T(b(e,t,n,i));return u.isGeneratorFunction(t)?r:r.next().then(function(e){return e.done?e.value:r.next()})},E(_),_[l]="Generator",_[s]=function(){return this},_.toString=function(){return"[object Generator]"},u.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var i=t.pop();if(i in e)return n.value=i,n.done=!1,n}return n.done=!0,n}},u.values=R,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function i(i,r){return a.type="throw",a.arg=e,t.next=i,r&&(t.method="next",t.arg=n),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return i("end");if(s.tryLoc<=this.prev){var l=r.call(s,"catchLoc"),c=r.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return i(s.catchLoc,!0);if(this.prev<s.finallyLoc)return i(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return i(s.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return i(s.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var r=i.arg;k(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:R(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=n),m}}}function b(e,t,n,i){var r=t&&t.prototype instanceof S?t:S,o=Object.create(r.prototype),s=new M(i||[]);return o._invoke=function(e,t,n){var i=d;return function(r,o){if(i===f)throw new Error("Generator is already running");if(i===p){if("throw"===r)throw o;return I()}for(n.method=r,n.arg=o;;){var s=n.delegate;if(s){var a=O(s,n);if(a){if(a===m)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===d)throw i=p,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=f;var l=w(e,t,n);if("normal"===l.type){if(i=n.done?p:h,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i=p,n.method="throw",n.arg=l.arg)}}}(e,n,s),o}function w(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}function S(){}function C(){}function x(){}function E(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function T(e){var t;this._invoke=function(n,i){function o(){return new Promise(function(t,o){!function t(n,i,o,s){var a=w(e[n],e,i);if("throw"!==a.type){var l=a.arg,c=l.value;return c&&"object"==typeof c&&r.call(c,"__await")?Promise.resolve(c.__await).then(function(e){t("next",e,o,s)},function(e){t("throw",e,o,s)}):Promise.resolve(c).then(function(e){l.value=e,o(l)},s)}s(a.arg)}(n,i,t,o)})}return t=t?t.then(o,o):o()}}function O(e,t){var i=e.iterator[t.method];if(i===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=n,O(e,t),"throw"===t.method))return m;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return m}var r=w(i,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,m;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,m):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function R(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function t(){for(;++i<e.length;)if(r.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=n,t.done=!0,t};return o.next=o}}return{next:I}}function I(){return{value:n,done:!0}}}(function(){return this}()||Function("return this")())},function(e,t,n){"use strict";var i=n(363);n.n(i).a},function(e,t,n){"use strict";var i=n(365);n.n(i).a},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.crossProcMenu=void 0;const r=n(62),o=n(1),s=n(0),a=n(18),l=s.default.getLogger("[crossmenu]-cross-process-menu"),c="main-process";t.crossProcMenu=new class{constructor(){this.menuClickFuncMap=new Map,this.clientFuncId=process.pid+"_handleProcessMenuItemClick",this.clientFuncCloseId=process.pid+"_onMainProcMenuClose",this.remoteId=""}init(e){this.remoteId=e,o.client.registerFunctions({[this.clientFuncId]:this.handleProcessMenuItemClick.bind(this)}),o.client.registerFunctions({[this.clientFuncCloseId]:this.onMainProcMenuClose.bind(this)}),l.information("注册菜单回调函数,remoteId=",this.remoteId,"clientFuncId=",this.clientFuncId," clientFuncCloseId=",this.clientFuncCloseId)}initMenu(e){return i(this,void 0,void 0,function*(){for(let t=0;t<e.length;t++){if(e[t].submenu&&(yield this.initMenu(e[t].submenu)),"function"==typeof e[t].click){let n=r.ThunderUtil.createGUID();this.menuClickFuncMap.set(n,{click:e[t].click,userData:e[t].userData}),e[t].clickGUID=n,e[t].click=void 0}"function"==typeof e[t].getChecked&&(e[t].checked=yield e[t].getChecked(e[t]),e[t].getChecked=void 0),"function"==typeof e[t].getEnabled&&(e[t].enabled=yield e[t].getEnabled(e[t]),e[t].getEnabled=void 0),"function"==typeof e[t].getVisible&&(e[t].visible=yield e[t].getVisible(e[t]),e[t].getVisible=void 0),e[t].userData=void 0}})}popUpMenuInMainProc(e,t){l.information("popUpMenuInMainProc enter"),o.client.callRemoteClientFunction(c,"handleRenderMenuPopup",this.remoteId,this.clientFuncId,this.clientFuncCloseId,e,t).catch(),o.client.callServerFunction(a.XmpPluginFunction.setMenuPopUp,!0).catch()}onMainProcMenuClose(){l.information("菜单关闭了"),this.menuClickFuncMap.clear(),o.client.callServerFunction(a.XmpPluginFunction.setMenuPopUp,!1).catch()}handleProcessMenuItemClick(e,t){if(l.information("handleProcessMenuItemClick enter remoteId=",e," item=",t),!t.clickGUID)return void l.warning("非GUID映射菜单调用");let n=this.menuClickFuncMap.get(t.clickGUID);n?(l.information("回调函数: func=",n," type:",typeof n),t.userData=n.userData,n.click(t),this.menuClickFuncMap.delete(t.clickGUID),l.information("handleProcessMenuItemClick leave")):l.error("回调函数对象不存在, clickGUID=",t.clickGUID)}}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.r(t),n.d(t,"Avatar",function(){return I}),n.d(t,"BrowserTab",function(){return ie}),n.d(t,"Badge",function(){return oe}),n.d(t,"Button",function(){return se.default}),n.d(t,"Checkbox",function(){return ae.default}),n.d(t,"Collapse",function(){return ue}),n.d(t,"ContextMenu",function(){return me}),n.d(t,"Dialog",function(){return ve.default}),n.d(t,"Dropdown",function(){return _e}),n.d(t,"Icon",function(){return ge.default}),n.d(t,"Input",function(){return we}),n.d(t,"InputGroup",function(){return xe}),n.d(t,"List",function(){return Ae}),n.d(t,"Loading",function(){return Be}),n.d(t,"Media",function(){return je}),n.d(t,"Pagination",function(){return He}),n.d(t,"Progress",function(){return Ge}),n.d(t,"Radio",function(){return Xe}),n.d(t,"Rate",function(){return Ye}),n.d(t,"Select",function(){return qe}),n.d(t,"Table",function(){return ut}),n.d(t,"Tabs",function(){return ht}),n.d(t,"Tooltip",function(){return pt}),n.d(t,"Tree",function(){return lt}),n.d(t,"Message",function(){return mt.default}),n.d(t,"Slider",function(){return yt}),n.d(t,"Switch",function(){return bt}),n.d(t,"load",function(){return Le}),n.d(t,"loading",function(){return wt}),n.d(t,"Carousel",function(){return St.default}),n.d(t,"CarouselItem",function(){return Ct.default}),n.d(t,"Breadcrumb",function(){return Et}),n.d(t,"TreePlus",function(){return Lt});var i={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"td-avatar",class:"is-"+this.className},this._l(this.imgs,function(e,n){return t("img",{key:n,attrs:{src:e.src,alt:e.alt}})}),0)},staticRenderFns:[],name:"td-multiple-avatar",props:{imgs:{type:Array,required:!0}},computed:{className:function(){var e=this.imgs.length;return["one","two","three","four"][e>4?3:e-1]}}},r={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-avatar",class:{"td-avatar--icon":e.icon}},[e.src?n("img",{attrs:{src:e.src,alt:e.alt}}):e.icon?n("i",{class:e.icon}):e.text?[e._v(e._s(e.text))]:e._e()],2)},staticRenderFns:[],name:"td-circle-avatar",props:{src:{type:String,default:""},alt:String,icon:{type:String,default:""},text:{type:String,default:""}}},o={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-avatar td-avatar--square",class:{"td-avatar--icon":e.icon}},[e.src?n("img",{attrs:{src:e.src,alt:e.alt}}):e.icon?n("i",{class:e.icon}):e.text?[e._v(e._s(e.text))]:e._e()],2)},staticRenderFns:[],name:"td-square-avatar",props:{src:{type:String,default:""},alt:String,icon:{type:String,default:""},text:{type:String,default:""}}},s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},a=1/0,l=9007199254740991,c="[object Arguments]",u="[object Function]",d="[object GeneratorFunction]",h="[object Symbol]",f="object"==typeof s&&s&&s.Object===Object&&s,p="object"==typeof self&&self&&self.Object===Object&&self,m=f||p||Function("return this")();function v(e,t){for(var n=-1,i=t.length,r=e.length;++n<i;)e[r+n]=t[n];return e}var g=Object.prototype,y=g.hasOwnProperty,_=g.toString,b=m.Symbol,w=g.propertyIsEnumerable,S=b?b.isConcatSpreadable:void 0,C=Math.max;function x(e){return T(e)||function(e){return function(e){return O(e)&&function(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=l}(e.length)&&!function(e){var t=function(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}(e)?_.call(e):"";return t==u||t==d}(e)}(e)}(e)&&y.call(e,"callee")&&(!w.call(e,"callee")||_.call(e)==c)}(e)||!!(S&&e&&e[S])}function E(e){if("string"==typeof e||function(e){return"symbol"==typeof e||O(e)&&_.call(e)==h}(e))return e;var t=e+"";return"0"==t&&1/e==-a?"-0":t}var T=Array.isArray;function O(e){return!!e&&"object"==typeof e}var P,k,M=(P=function(e,t){return null==e?{}:function(e,t){return function(e,t,n){for(var i=-1,r=t.length,o={};++i<r;){var s=t[i],a=e[s];n(a,s)&&(o[s]=a)}return o}(e=Object(e),t,function(t,n){return n in e})}(e,function(e,t){for(var n=-1,i=e?e.length:0,r=Array(i);++n<i;)r[n]=t(e[n],n,e);return r}(function e(t,n,i,r,o){var s=-1,a=t.length;for(i||(i=x),o||(o=[]);++s<a;){var l=t[s];n>0&&i(l)?n>1?e(l,n-1,i,r,o):v(o,l):r||(o[o.length]=l)}return o}(t,1),E))},k=C(void 0===k?P.length-1:k,0),function(){for(var e=arguments,t=-1,n=C(e.length-k,0),i=Array(n);++t<n;)i[t]=e[k+t];t=-1;for(var r=Array(k+1);++t<k;)r[t]=e[t];return r[k]=i,function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}(P,this,r)}),R={render:function(){var e=this.$createElement;return(this._self._c||e)(this.componentName,this._b({tag:"component"},"component",this.pick(this.$props,["src","alt","icon","text","imgs"]),!1))},staticRenderFns:[],name:"td-avatar",props:{shape:{type:String,default:"circle",validator:function(e){return["circle","square","multiple"].includes(e)}},src:{type:String},alt:String,icon:{type:String},text:{type:String},imgs:{type:Array}},components:{multiple:i,circleAvatar:r,square:o},computed:{componentName:function(){return{multiple:i,circle:"circleAvatar",square:o}[this.shape]}},methods:{pick:M},install:function(e){e.component(R.name,R)}},I=R,W=n(124),L=n.n(W),D=n(9),N=n.n(D),A=n(27),F=n.n(A),B=n(53),V=n.n(B),j=n(122),$=n.n(j),H=n(94),U=n.n(H),G=n(123),K=n.n(G),X=n(79),z=n.n(X),Y=n(80),Z=n.n(Y);function q(e,t){return e.children[t]}var J=function(){function e(t){z()(this,e),this.vm=t.vm,this.insertNodes=null,this.vertical=t.vertical,this.options=t}return Z()(e,[{key:"buildRect",value:function(e){return q(this.container,e).getBoundingClientRect()}},{key:"getMargin",value:function(e){var t=window.getComputedStyle(q(this.container,e));return this.vertical?parseInt(t.marginTop)+parseInt(t.marginBottom):parseInt(t.marginLeft)+parseInt(t.marginRight)}},{key:"updateTranslateY",value:function(e){var t=this,n=e.e,i=e.moveRects,r=e.ulRect,o=e.sortStartC,s=e.areaDistance,a=this.insertNodes,l=this.vertical,c=this.container;if(a){var u=l?n.clientY-o+c.scrollTop:n.clientX-o+c.scrollLeft,d=l?"top":"left";a.forEach(function(e,n){var o=i[n][d]-r[d];e.style[d]=Math.min(Math.max(o+u,l?c.scrollTop:c.scrollLeft),l?c.offsetHeight+c.scrollTop-s-t.getMargin(n):c.offsetWidth+c.scrollLeft-s-t.getMargin(n))+"px"})}}},{key:"doCloneNodes",value:function(e,t){var n=this.options.multiDragTextGenerator,i=void 0===n?function(){return""}:n,r=this.insertNodes=[],o=window.getComputedStyle(e),s=e.cloneNode();if(s.innerHTML=e.innerHTML,s.style.position="absolute",s.style.zIndex=100,s.className+=" is-drag",s.style.width=o.width,s.style.height=o.height,t.length>1){s.className+=" is-drag-more";var a=document.createElement("div");a.className+=" td-draglist-item__number",a.innerHTML="<span>"+i(t)+"</span>",s.appendChild(a)}console.log(t),r.push(s)}},{key:"doInsertNodes",value:function(){var e=this.insertNodes,t=this.container;e&&e.forEach(function(e){return t.appendChild(e)})}},{key:"doRemoveNodes",value:function(e){var t=this.insertNodes,n=this.container;t&&n&&(t.forEach(function(e){n.contains(e)&&n.removeChild(e)}),this.insertNodes=null)}},{key:"container",get:function(){return this.options.container.call(this.vm)}}]),e}(),Q=n(4);var ee=function(e){return e=K()({},{supportScroll:!1,vertical:!1,multiple:!1},e),{props:{sortable:Boolean,offset:{type:Number,default:10},scrollSpeed:{type:Number,default:2},distance:{type:Number,default:8},sortByHandler:{type:Boolean,default:!1},handleClassList:{type:Array}},data:function(){return{sorting:!1,moving:!1,moveTargetIndexs:null}},created:function(){this.ui=new J(U()({},e,{vm:this})),this.onSortingThrottled=Object(Q.g)(this.onSorting,16)},mounted:function(){var t=this;this.sortable&&(e.container.call(this).addEventListener("scroll",this.handleScroll),this.$watch(e.getList,function(){t.traceEvent()},{immediate:!0}))},methods:{traceEvent:function(t){var n=this;this.$nextTick(function(){var i=e.container.call(n);$()(i.children).forEach(function(e,i,r){e&&(e.removeEventListener("mousedown",n.onSortStart),t||e.addEventListener("mousedown",n.onSortStart))})})},eventPathElementHasHandleClass:function(e,t){var n=this;if(!t||!t.length)return!1;var i=function(e){return t[e].classList&&n.handleClassList.some(function(n){return t[e].classList.contains(n)})?{v:!0}:"LI"===t[e].tagName&&t[e].classList.contains("td-draglist-item")?"break":void 0};e:for(var r=0;r<t.length;r++){var o=i(r);switch(o){case"break":break e;default:if("object"===(void 0===o?"undefined":V()(o)))return o.v}}return!1},onSortStart:function(t){if((!this.sortByHandler||this.eventPathElementHasHandleClass(t.target,t.path))&&this.sortable&&1===t.which){t.preventDefault(),t.stopPropagation();var n=e,i=n.vertical,r=n.supportScroll;this.sorting=!0,this.setupMoveTarget(t);var o=e.container.call(this);r&&(this.isScrollable=o.scrollHeight>o.offsetHeight,this.scrollHeight=o.scrollHeight),this.sortStartC=this.mouseStartC=this.sortPrevC=i?t.clientY:t.clientX,o.addEventListener("mouseleave",this.onSortEnd),o.addEventListener("mouseup",this.onSortEnd),o.addEventListener("mousemove",this.onSortingThrottled),this.$emit("on-sort-start",this.ui.insertNodes,t)}},setupMoveTarget:function(t){var n=t.currentTarget,i=e.getList.call(this);this.moveTarget=n,this.moveTargetKey=i[n.dataset.index].key,e.multiple?this.moveIndexs=e.getMoveIndexs.call(this):this.moveIndexs=[Number(n.dataset.index)],this.moveKeys=this.moveIndexs.map(function(e){return i.find(function(t,n){return e===n}).key})},onSorting:function(t){var n=this,i=this.sorting,r=this.distance,o=this.mouseStartC,s=this.sortPrevC,a=this.moveRects;if(i){var l=e.vertical?t.clientY:t.clientX;if(!this.moving&&Math.abs(l-o)<r)return void(this.sortStartC=this.sortPrevC=l);if(l-s>0?this.direction="down":l-s<0&&(this.direction="up"),this.sortPrevC=l,this.moveItem=e.getList.call(this)[this.moveTarget.dataset.index],!this.startDragHookCalled&&e.onDragStart&&(e.onDragStart.call(this,t,this.moveItem),this.startDragHookCalled=!0),!this.areaDistance){var c=this.moveTarget.getBoundingClientRect();this.areaDistance=e.vertical?c.height:c.width}this.lastMouseEvent=t,a?(this.ui.updateTranslateY({e:t,moveRects:this.moveRects,ulRect:this.ulRect,sortStartC:this.sortStartC,areaDistance:this.areaDistance}),e.supportScroll&&this.isScrollable&&this.doScroll(t),this.handleSort(t,!1)):this.setupRect().then(function(){if(n.sorting){n.moving=!0;var i=e.getMoveIndexs?e.getMoveIndexs.call(n):n.moveIndexs;n.ui.doCloneNodes(n.moveTarget,i),n.ui.updateTranslateY({e:t,moveRects:n.moveRects,ulRect:n.ulRect,sortStartC:n.sortStartC,areaDistance:n.areaDistance}),e.supportScroll&&n.isScrollable&&n.doScroll(t),n.handleSort(t,!0),n.ui.doInsertNodes()}}),this.$emit("on-sorting",this.ui.insertNodes,t)}},setupRect:function(){var t=this;return this.moveRects=[this.moveTarget.getBoundingClientRect()],new F.a(function(n){t.$nextTick(function(){var i=e.container.call(t);t.ulRect=i.getBoundingClientRect(),t.rects=[],e.getList.call(t).forEach(function(e,n){var r=t.ui.buildRect(n),o={width:r.width,height:r.height,margin:t.ui.getMargin(n)};t.vertical?o.top=r.top+i.scrollTop:o.left=r.left+i.scrollLeft,t.rects.push(o)}),n()})})},updateRect:function(){var t=this;if(this.moving){var n=this.rects,i=[];e.getList.call(this).forEach(function(e,r){var o=n[r]||t.ui.buildRect(r),s={width:o.width,height:o.height,margin:void 0===o.margin?t.ui.getMargin(r):o.margin};t.vertical?s.top=0===r?o.top:i[r-1].top+i[r-1].height+i[r-1].margin:s.left=0===r?o.left:i[r-1].left+i[r-1].width+i[r-1].margin,i.push(s)}),this.rects=i}},handleSort:function(t,n){if(this.moving&&!1!==this.rectUpdated){var i=this.moveRects,r=this.rects,o=this.areaDistance,s=this.sortStartC,a=this.sortPrevC,l=e.getList.call(this),c=t?e.vertical?t.clientY:t.clientX:a,u=e.container.call(this),d=void 0;d=e.getMoveIndexs?e.getMoveIndexs.call(this):this.moveIndexs;for(var h=1;h<d.length;h++)if(d[h]-d[h-1]!=1)return Object(Q.h)("List","non-continuous moveIndexs: "+d),this.onSortEnd();var f=void 0;f=e.vertical?c-s+i[0].top-r[d[0]].top+u.scrollTop:c-s+i[0].left-r[d[0]].left+u.scrollLeft;var p=void 0,m=void 0;if(Math.abs(f)>o/2)if(f>0&&(n||"down"===this.direction)){if((m=Math.max.apply(Math,N()(d))+1)>l.length-1)return;if(this.moved!=="+"+m){this.moved="+"+m;for(var v=l[m],g=U()({},r[m]),y=d.length-1;y>=0;y--)l[d[y]+1]=l[d[y]],r[d[y]+1].width=r[d[y]].width,r[d[y]+1].height=r[d[y]].height;l[d[0]]=v,r[d[0]].width=g.width,r[d[0]].height=g.height,p=!0}}else if(f<0&&(n||"up"===this.direction)){if((m=Math.min.apply(Math,N()(d))-1)<0)return;if(this.moved!=="-"+m){this.moved="-"+m;for(var _=l[m],b=U()({},r[m]),w=0;w<d.length;w++)l[d[w]-1]=l[d[w]],r[d[w]-1].width=r[d[w]].width,r[d[w]-1].height=r[d[w]].height;l[d[d.length-1]]=_,r[d[d.length-1]].width=b.width,r[d[d.length-1]].height=b.height,p=!0}}p&&(e.onUpdateList&&e.onUpdateList.call(this),e.multiple||(this.moveIndexs=[m]),this.updateRect(),this.handleSort(void 0,n))}},doScroll:function(t){var n=this;if(this.moving){var i=e.vertical,r=this.moveRects,o=this.sortStartC,s=this.ulRect,a=this.offset,l=this.scrollSpeed,c=this.areaDistance;if(!r)return this.onSortEnd();var u=e.container.call(this),d=(i?t.clientY:t.clientX)-o+r[0].top-s.top,h=void 0;u.scrollTop+u.offsetHeight<this.scrollHeight&&"down"===this.direction?d+c+a>u.offsetHeight&&(h=l):u.scrollTop>0&&"up"===this.direction&&d<a&&(h=-1*l),h&&(this.frame||(this.frame=!0,window.requestAnimationFrame(function(){n.frame=!1,n.updateScrollTop(h),n.lastMouseEvent&&(n.ui.updateTranslateY({e:n.lastMouseEvent,moveRects:n.moveRects,ulRect:n.ulRect,sortStartC:n.sortStartC,areaDistance:n.areaDistance}),n.doScroll(n.lastMouseEvent))})))}},updateScrollTop:function(t){if(this.moving){var n=e.container.call(this);this.scrollTop=n.scrollTop=Math.min(this.scrollHeight-n.offsetHeight,(this.scrollTop||n.scrollTop)+t),this.handleSort(this.lastMouseEvent)}},onSortEnd:function(){if(this.sorting){this.sorting=this.moving=!1,this.startDragHookCalled=this.moveItem=this.moveTargetIndexs=this.moved=this.lastMouseEvent=this.areaDistance=null,e.vertical&&this.supportScroll&&(this.scrollTop=this.scrollHeight=this.isScrollable=null),this.$emit("on-sort-end",this.ui.insertNodes),this.ui.doRemoveNodes(),this.destoryMoveTarget(),this.destoryRect();var t=e.container.call(this);t.removeEventListener("mouseleave",this.onSortEnd),t.removeEventListener("mouseup",this.onSortEnd),t.removeEventListener("mousemove",this.onSortingThrottled),e.onSortEnd&&e.onSortEnd.call(this)}},destoryMoveTarget:function(){this.moveKeys=this.moveTargetKey=this.moveIndexs=this.moveTarget=null},destoryRect:function(){this.moveRects=this.ulRect=this.rects=null},handleScroll:function(){this.sorting&&this.lastMouseEvent&&this.ui.updateTranslateY({e:this.lastMouseEvent,moveRects:this.moveRects,ulRect:this.ulRect,sortStartC:this.sortStartC,areaDistance:this.areaDistance})}},beforeDestory:function(){this.sortable&&(e.container.call(this).removeEventListener("scroll",this.handleScroll),this.traceEvent(!0))}}},te={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",e._g(e._b({staticClass:"td-browser-tab__item",class:{"td-browser-tab__item--first":e.first,"td-browser-tab__item--normal":"normal"===e.type,"td-browser-tab__item--new":"new"===e.type,"is-current":e.current,"is-pinned":e.pinned,"is-hover":e.isHover},style:{"min-width":e.pinned?e.$parent.pinnedMinWidth+"px":"unset"},attrs:{title:e.title,"data-item-index":e.index,"data-current":e.current},on:{mouseenter:function(t){return e.handleMouseEvent(!0)},mouseleave:function(t){return e.handleMouseEvent(!1)},mousedown:e.handleClick}},"div","normal"===e.type?e.tabAttrs(e.tab,e.index):{},!1),"normal"===e.type?e.tabListeners(e.tab,e.index):{}),[n("custom-title"),e._v(" "),"normal"===e.type&&!e.pinned||e.tab&&e.tab.closable?n("a",{staticClass:"td-browser-tab__close",attrs:{href:"javascript:;",title:"关闭"},on:{click:function(t){return t.stopPropagation(),e.handleRemove(t)}}},[n("i",{staticClass:"td-icon td-icon-close"})]):e._e()],1)},staticRenderFns:[],name:"td-browser-tab-item",components:{customTitle:{functional:!0,render:function(e,t){var n=t.parent;return"normal"===n.type?e("div",{class:"td-browser-tab__text"},["function"==typeof n.renderTitle?n.renderTitle.call(n._renderProxy,e,{title:n.title,pinned:n.pinned},n.index):n.$slots.default||n.title]):""}}},props:{tab:Object,tabAttrs:Function,tabListeners:Function,title:{type:String,default:"新标签页"},type:{type:String,default:"normal"},current:Boolean,first:Boolean,pinned:Boolean,index:Number,renderTitle:Function},data:function(){return{isHover:!1}},methods:{handleRemove:function(){this.$emit("remove")},handleMouseEvent:function(e){this.isHover=e},handleClick:function(e){2===e.which&&this.$parent.$emit("middle-click",{tab:this.tab,index:this.index,e})}}},ne={name:"td-browser-tab",components:{BrowserTabItem:te},mixins:[ee({container:function(){return this.$refs.groupCon},getList:function(){return this.showableTabs},onUpdateList:function(){var e=this,t=this.showableTabs,n=this.unpinnedTabs,i=this.pinnedTabs,r=this.showableStart,o=this.showableEnd,s=[].concat(N()(i),N()(n.slice(0,r)),N()(t),N()(n.slice(o+1,n.length+1)));this.$emit("update:tabs",s),this.$emit("update:current",s.findIndex(function(t){return t.key===e.currentKey}))}})],props:{tabStyle:{type:String,default:"parall"},TabOverlapWidth:{type:Number,default:20},background:{type:String,default:"#bbb"},tabs:Array,tabAttrs:{type:Function,default:function(){return{}}},tabListeners:{type:Function,default:function(){return{}}},current:{type:Number,default:0},renderTitle:Function,sortable:Boolean,minWidth:{type:Number,default:100},pinnedMinWidth:{type:Number,default:220},unpinnedWidth:{type:Number,default:327},showTotalLimit:{type:Number,default:6},triggerChangeTabByMouseDown:{type:Boolean,default:!0}},computed:{pinnedTabs:function(){return this.tabs.filter(function(e){return e.pinned})},unpinnedTabs:function(){return this.tabs.filter(function(e){return!e.pinned})},groupStyle:function(){var e=this.unpinnedTabs.length*this.unpinnedWidth,t=this.pinnedTabs.length,n="simple"===this.tabStyle?"0px":(t-1)*this.TabOverlapWidth+"px",i="simple"===this.tabStyle?"0px":"90px";return 0===t&&(e+=10),{width:e+"px",maxWidth:"calc(100% - "+i+" - "+this.pinnedMinWidth+"px * "+t+" - "+n+")",flexBasis:"auto",position:"relative"}}},data:function(){return{moving:!1,currentKey:null,showableTabs:[]}},watch:{current:{handler:function(e){this.currentKey=this.tabs[e].key}}},created:function(){var e=this;this.$watch(function(){var t=e.unpinnedTabs.length+e.current;return Math.random()+t},this.handleShowableTabs)},methods:{handleShowableTabs:function(){var e=this.unpinnedTabs,t=this.showableTabs,n=this.pinnedTabs;if(this.$refs.groupCon){for(var i=this.$refs.groupCon.offsetWidth,r=Math.min(Math.max(Math.floor(i/this.minWidth),1),e.length),o=t,s=0;s<o.length;)-1===e.indexOf(o[s])?o.splice(s,1):s++;if(o&&o.length&&e.length!==r||(o=this.showableTabs=e.slice(0,r)),o.length>r)this.removeOverflowItems(r);else{if(this.current<=n.length-1)this.completeShowable(r);else{var a=e.findIndex(function(e){return e.key===o[0].key}),l=e.findIndex(function(e){return e.key===o[o.length-1].key}),c=this.current-n.length;if(c<a){var u=Math.max(c-2,0),d=Math.min(u+r-1,u+o.length-1);this.showableTabs=e.slice(u,d+1)}else if(c>l){var h=Math.min(c+2,e.length-1),f=Math.max(0,h-r+1);this.showableTabs=e.slice(f,h+1)}else if(c>=a&&c<=l&&-1===o.findIndex(function(t){return t.key===e[c].key}))if(Math.abs(c-a)>Math.abs(c-l)){var p=Math.min(c+2,e.length-1),m=Math.max(0,p-r+1);this.showableTabs=e.slice(m,p+1)}else{var v=Math.max(c-2,0),g=Math.min(v+r-1,v+o.length-1);this.showableTabs=e.slice(v,g+1)}else this.completeShowable(r)}this.computeStartAndEndIndex()}}},computeStartAndEndIndex:function(){var e=this;this.showableTabs&&this.showableTabs.length?(this.showableStart=this.unpinnedTabs.findIndex(function(t){return t.key===e.showableTabs[0].key}),this.showableEnd=this.unpinnedTabs.findIndex(function(t){return t.key===e.showableTabs[e.showableTabs.length-1].key})):this.showableEnd=this.showableStart=0},removeOverflowItems:function(e){var t=this,n=this.showableTabs,i=n.length-e,r=n.findIndex(function(e){return e.key===t.currentKey});if(i)for(var o=0;o<i&&o<r;o++)n.shift(),e--;if(i=n.length-e)for(var s=0;s<i;s++)n.pop()},completeShowable:function(e){var t=this.unpinnedTabs,n=this.showableTabs,i=t.findIndex(function(e){return e.key===n[0].key}),r=t.findIndex(function(e){return e.key===n[n.length-1].key}),o=e-n.length;if(o)for(var s=i-1,a=0;a<o&&s>-1;a++)n.unshift(t[s]),s--;if(o=e-n.length)for(var l=r+1,c=0;c<o&&l<t.length;c++)n.push(t[l]),l++},handleSelect:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.triggerChangeTabByMouseDown===n&&(this.$emit("update:current",t),this.$emit("change",{tab:e,index:t}))},handleAdd:function(){this.$emit("add")},handleRemove:function(e,t){this.$emit("remove",{tab:e,index:t})},handleTotalClick:function(e){this.$emit("total-click",e)}},mounted:function(){this.handleShowableTabs(),window.addEventListener("resize",this.handleShowableTabs),this.currentKey=this.tabs[this.current].key},render:function(){var e=this,t=arguments[0],n=this.tabStyle,i=this.background,r=this.showTotalLimit,o=this.tabs,s=this.tabAttrs,a=this.tabListeners,l=this.pinnedTabs,c=this.unpinnedTabs,u=this.showableTabs,d=this.renderTitle,h=this.$scopedSlots,f=this.current,p=this.groupStyle,m=this.moveIndexs,v=this.moving;return t("div",{class:{"td-browser-tab":!0,"td-browser-tab--simple":"simple"===n},style:{background:i}},[l.map(function(n){var i=o.indexOf(n);return t(te,L()([{props:n},{attrs:{current:i===f,first:0===i,index:i,"render-title":d,tab:n,tabAttrs:s,tabListeners:a},on:{remove:function(){return e.handleRemove(n,i)}},nativeOn:{click:function(){return e.handleSelect(n,i)},mousedown:function(){return e.handleSelect(n,i,!0)}}}]),[h.default?h.default({tab:n,index:i}):""])}),t("div",{class:"td-browser-tab-group",style:p,ref:"groupCon"},[u.map(function(n,i){var r=o.indexOf(n);return t(te,L()([{props:n},{attrs:{current:r===f,first:0===r,index:r,"data-index":i,"data-key":n.key,"render-title":d,tab:n,tabAttrs:s,tabListeners:a},ref:"item"+i,nativeOn:{click:function(){return e.handleSelect(n,r)},mousedown:function(){return e.handleSelect(n,r,!0)}},on:{remove:function(){return e.handleRemove(n,r)}},class:{"is-transition":!m||m[0]!==i,"is-hidden":v&&m&&m[0]===i},key:n.key}]),[h.default?h.default({tab:n,index:r}):""])})]),t(te,{attrs:{title:"打开新标签页",type:"new"},nativeOn:{click:this.handleAdd},style:{visibility:v?"hidden":"visible"}}),c.length>=r?t("div",{class:"td-browser-tab__item td-browser-tab__item--more",on:{click:this.handleTotalClick}},[t("span",{class:"td-browser-tab__number-more"},[c.length])]):""])},install:function(e){e.component(ne.name,ne)}},ie=ne,re={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"td-badge",class:{"td-badge--dot":this.isDot}},[this._t("default"),this._v(" "),this.hideSup?this._e():t("sup",{staticClass:"td-badge__content",domProps:{textContent:this._s(this.content)}})],2)},staticRenderFns:[],name:"td-badge",props:{value:[String,Number],max:Number,isDot:Boolean,hideSup:{type:Boolean,default:!1}},computed:{content:function(){if(!this.isDot){var e=this.value,t=this.max;return"number"==typeof e&&"number"==typeof t&&t<e?t+"+":e}}},install:function(e){e.component(re.name,re)}},oe=re,se=n(23),ae=n(51),le={render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"td-collapse"},[this._t("default")],2)},staticRenderFns:[],name:"td-collapse",model:{prop:"value",event:"change"},provide:function(){return{collapse:this}},props:{value:[Number,String]},created:function(){var e=this;this.$on("item-change",function(t){e.$emit("change",t)})}},ce={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-collapse-item",class:{"is-active":e.active}},[n("div",{staticClass:"td-collapse-item__title",on:{click:e.handleTitleClick}},[e._t("title",[e._v(e._s(e.title))])],2),e._v(" "),n("div",{staticClass:"td-collapse-item__content"},[e._t("default")],2)])},staticRenderFns:[],name:"td-collapse-item",inject:["collapse"],props:{title:[Number,String],value:[Number,String]},computed:{active:function(){return this.value===this.collapse.value}},methods:{handleTitleClick:function(){this.collapse.$emit("item-change",this.value)}}};le.install=function(e){e.component(le.name,le),e.component(ce.name,ce)};var ue=le,de=n(166),he=n.n(de),fe={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"show",rawName:"v-show",value:e.visible,expression:"visible"}],staticClass:"td-context-menu",class:{"is-left":e.isRightOverflow},staticStyle:{position:"fixed"},style:{left:e.computedPosition.x+"px",top:e.computedPosition.y+"px"},attrs:{tabindex:"0"}},[n("ul",{staticClass:"td-context-menu__main"},e._l(e.menu,function(t,i){return n("td-context-menu-item",{key:i,attrs:{item:t,active:e.activeIndex===i},nativeOn:{mouseenter:function(t){e.activeIndex=i},mouseleave:function(t){e.activeIndex=-1}}})}),1)])},staticRenderFns:[],name:"td-context-menu",props:{menu:Array,visible:Boolean,containerBounding:[Object,DOMRect],position:{type:Object}},provide:function(){return{root:this}},data:function(){return{x:null,y:null,activeIndex:-1,isBottomOverflow:!1,computedPosition:{x:0,y:0},shortcutCollection:[],menuLevel:1,isRightOverflow:!1}},mounted:function(){this.collectShortcut(this.menu,1),document.addEventListener("keydown",this.handleKeyPress)},beforeDestroy:function(){document.removeEventListener("keydown",this.handleKeyPress)},watch:{position:function(){var e=this;this.visible&&(this.isRightOverflow=!1,this.$el.focus(),this.computedPosition={x:this.position.x,y:this.position.y},this.$nextTick(function(){var t=e.$el.getBoundingClientRect();t.bottom>e.containerBounding.bottom&&(e.computedPosition.y=t.top-t.height),t.right>e.containerBounding.right&&(e.computedPosition.x=t.left-t.width)}))}},methods:{collectShortcut:function(e,t){for(var n=0;n<e.length;n++)e[n].__level=t,e[n].shortcut&&this.shortcutCollection.push(e[n]),e[n].children&&this.collectShortcut(e[n].children,t+1)},show:function(e){e.preventDefault(),this.$emit("show")},handleKeyPress:function(e){var t=this;this.visible&&this.shortcutCollection.forEach(function(n){n.tipImage?n.shortcut:he()(n.shortcut).every(function(t){return"key"===t?e[t].toLowerCase()===n.shortcut[t].toLowerCase():e[t]===n.shortcut[t]})&&t.menuLevel>=n.__level&&t.$emit("click",n)})},click:function(e){e.disabled||(this.hide(),this.$emit("click",e))}}},pe={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"td-context-menu__item",class:{"is-separate":e.item.underline}},[n("div",{staticClass:"td-context-menu__content",class:{"is-active":e.active},style:{width:e.item.width||"100px"},on:{click:e.handleClickItem}},[e.item.iconClass||e.item.checked?n("span",{staticClass:"td-icon",class:[e.item.iconClass,e.item.checked&&"td-icon-choose"]}):e._e(),e._v(" "),e.item.text?n("span",{staticClass:"td-context-menu__text"},[e._v(e._s(e.item.text))]):e._e(),e._v(" "),n("span",{staticClass:"td-context-menu__tips"},[e.item.tipImage?n("img",{attrs:{src:e.item.tipImage}}):e.item.tip?[e._v(" "+e._s(e.item.tip)+" ")]:e._e()],2),e._v(" "),e.item.children?n("i",{staticClass:"td-icon-more-right"}):e._e()]),e._v(" "),e.showChildren?n("ul",{ref:"menuChildren",staticClass:"td-context-menu__children",class:{"is-top":e.isBottomOverflow}},e._l(e.item.children,function(t,i){return n("td-context-menu-item",{key:i,attrs:{item:t,active:e.activeIndex===i},nativeOn:{mouseenter:function(t){e.activeIndex=i},mouseleave:function(t){e.activeIndex=-1}}})}),1):e._e()])},staticRenderFns:[],name:"td-context-menu-item",props:{item:{type:Object},active:{type:Boolean}},inject:["root"],data:function(){return{activeIndex:-1,isBottomOverflow:!1}},computed:{showChildren:function(){return this.item.children&&this.item.children.length&&this.active}},methods:{handleClickItem:function(){this.root.$emit("click",this.item)}},watch:{showChildren:function(e){var t=this;this.active?this.root.menuLevel++:this.root.menuLevel--,e?this.$nextTick(function(){var e=t.$refs.menuChildren.getBoundingClientRect();e.bottom>t.root.containerBounding.bottom&&(t.isBottomOverflow=!0),e.right>t.root.containerBounding.right&&(t.root.isRightOverflow=!0)}):this.isBottomOverflow=!1}}};fe.install=function(e){e.component(fe.name,fe),e.component(pe.name,pe)};var me=fe,ve=n(59),ge=n(10),ye={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-dropdown"},[n("div",{staticClass:"td-dropdown-group"},[n("td-button",{attrs:{size:"large",disabled:e.disabled},on:{click:e.handleButtonClick}},[e._t("default")],2),e._v(" "),n("td-button",{attrs:{size:"large",disabled:e.disabled},on:{click:function(t){return t.stopPropagation(),e.handleDropClick(t)}}},[n("td-icon",{attrs:{type:"arrow-drop"}})],1)],1),e._v(" "),e.customMenuEnabled?e._e():n("ul",{directives:[{name:"show",rawName:"v-show",value:e.menuVisible,expression:"menuVisible"}],staticClass:"td-dropdown-menu"},e._l(e.menus,function(t){return n("li",{key:t,staticClass:"td-dropdown-menu__item",on:{click:function(n){return e.handleMenuClick(t)}}},[e._v(" "+e._s(t)+" ")])}),0)])},staticRenderFns:[],name:"td-dropdown",components:{TdButton:se.default,TdIcon:ge.default},props:{menus:Array,customMenuEnabled:Boolean,disabled:Boolean},data:function(){return{menuVisible:!1}},methods:{handleButtonClick:function(){this.$emit("click")},handleDropClick:function(){this.menuVisible=!0,this.$emit("drop-click")},handleMenuClick:function(e){this.$emit("input",e)}},mounted:function(){var e=this;document.addEventListener("click",function(){e.menuVisible=!1})},install:function(e){e.component(ye.name,ye)}},_e=ye,be={render:function(){var e=this,t=this,n=t.$createElement,i=t._self._c||n;return"text"===t.type?i("label",{staticClass:"td-input",class:{"is-warn":t.warn,"is-disabled":t.disabled}},[i("span",{staticClass:"td-input__label"},[t._v(t._s(t.label))]),t._v(" "),i("input",t._g(t._b({ref:"input",staticClass:"td-input__inner",attrs:{disabled:t.disabled},domProps:{value:t.value},on:{blur:function(t){e.$emit("blur",t)},focus:function(t){e.$emit("focus",t)}}},"input",t.$attrs,!1),t.inputListeners))]):"textarea"===t.type?i("label",{staticClass:"td-textarea",class:{"is-warn":t.warn,"is-disabled":t.disabled}},[i("span",{staticClass:"td-textarea__label"},[t._v(t._s(t.label))]),t._v(" "),i("textarea",t._g(t._b({ref:"input",staticClass:"td-textarea__inner",attrs:{disabled:t.disabled},domProps:{value:t.value}},"textarea",t.$attrs,!1),t.inputListeners))]):t._e()},staticRenderFns:[],name:"td-input",inheritAttrs:!1,props:{type:{type:String,default:"text"},value:[Number,String],label:String,disabled:{type:Boolean,default:!1},warn:{type:Boolean,default:!1}},computed:{inputListeners:function(){return K()({},this.$listeners,{input:this.handleInput})}},methods:{select:function(){this.$refs.input.select()},handleInput:function(e){this.$emit("input",e.target.value)}},install:function(e){e.component(be.name,be)}},we=be,Se={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-input-group"},[e.$slots.prepend?n("div",{staticClass:"td-input-group__prepend"},[e._t("prepend")],2):e._e(),e._v(" "),e._t("default"),e._v(" "),e.$slots.append?n("div",{staticClass:"td-input-group__append"},[e._t("append")],2):e._e()],2)},staticRenderFns:[],name:"td-input-group"},Ce={render:function(){var e=this.$createElement;return(this._self._c||e)("a",{staticClass:"td-input__button",class:{"is-disabled":this.disabled},attrs:{href:"javascript:;"}},[this._t("default")],2)},staticRenderFns:[],name:"td-input-group-button",props:{disabled:{type:Boolean,default:!1}}};Se.install=function(e){e.component(Se.name,Se),e.component(Ce.name,Ce)};var xe=Se,Ee=n(93),Te=n.n(Ee),Oe=n(215),Pe=n.n(Oe),ke=n(52),Me=n.n(ke),Re=n(216),Ie=n.n(Re),We={inserted:function(e,t){var n=t.value;"function"==typeof n?Ie()(n,{target:e}):Ie()(n.handler,{target:e,root:e.parentElement,distance:n.distance})},install:function(e){e.directive("load",We)}},Le=We;var De=void 0,Ne={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("transition-group",{staticClass:"td-draglist",staticStyle:{outline:"none"},attrs:{name:e.moving?"drag-list":"",tag:"ul",tabindex:"0"},nativeOn:{click:function(t){return e.handleCleanChosen(t)},keydown:function(t){return t.type.indexOf("key")||65===t.keyCode?(t.preventDefault(),t.ctrlKey?e.handleChooseAll(t):null):null}}},[e._l(e.list,function(t,i){return n("li",{directives:[{name:"show",rawName:"v-show",value:!e.status[t.key].hide||!e.moving,expression:"!status[item.key].hide || !moving"}],key:t.key,ref:"item"+i,refInFor:!0,staticClass:"td-draglist-item",class:[{"is-chosen":e.status[t.key].chosen&&!e.status[t.key].active,"is-active":e.status[t.key].active,"is-transition":!e.status[t.key].moving,"is-hidden":e.status[t.key].moving&&e.moving},e.sget(t.template,"class")],attrs:{"data-index":i,"data-key":t.key},on:{click:function(n){return n.stopPropagation(),e.handleItemClick(n,t)},mousedown:function(n){return!n.type.indexOf("key")&&e._k(n.keyCode,"right",39,n.key,["Right","ArrowRight"])?null:"button"in n&&2!==n.button?null:e.handleItemClickRight(t)}}},[e._t("default",null,{item:t,index:i,chosen:e.status[t.key].chosen,active:e.status[t.key].active})],2)}),e._v(" "),n("li",{directives:[{name:"load",rawName:"v-load",value:{handler:e.handleLoad,distance:e.loadDistance+"px"},expression:"{ handler: handleLoad, distance: `${loadDistance}px` }"}],key:"load"})],2)},staticRenderFns:[],name:"td-list",mixins:[ee({multiDragTextGenerator:function(e){return De(e)},vertical:!0,multiple:!0,supportScroll:!0,container:function(){return this.$el},getList:function(){return this.listCopy},getMoveIndexs:function(){return this.chosenIndexs},onDragStart:function(e,t){-1===this.chosenKeysCopy.indexOf(t.key)&&this.handleItemClick(e,t),this.doChosenKeysSort()},onDragEnd:function(){this.needSort=!1},onUpdateList:function(){this.updateList(this.listCopy,!1)}})],directives:{load:Le},props:{list:Array,chosenKeys:{required:!0,type:Array},multiple:Boolean,dragSelectable:Boolean,immediate:Boolean,drop:Boolean,loadMore:Function,loadDistance:{type:Number,default:100},clickToFocus:{type:Boolean,default:!0},multiDragTextGenerator:{type:Function,default:function(e){return e.length}}},data:function(){return{lastItem:null,vertical:!0,dragging:!1,startX:0,startY:0,currentX:0,currentY:0,scrollTop:0,scrollLeft:0,itemsPosition:[],startDragChosenKeys:[],chosenKeysCopy:[],extChosenKeys:[],chosenKeysSorted:!1,listCopy:[],scrollHeight:0,chosenIndexs:[],moveTargetKey:null,supportScroll:!0,tdListScrollTop:0,tdListBoundRect:{},draggingDetectCallbackId:0,startTdListScrollTop:0,clientYDiffWithBoundingRect:0,tdListMaxScrollTop:0}},created:function(){},mounted:function(){var e=this;De=this.multiDragTextGenerator,this.$watch("dragSelectable",function(t){t?e.multiple&&e.$el.addEventListener("mousedown",e.onDragStart):e.doDestoryDrag()},{immediate:!0});var t=this.$el;t.addEventListener("scroll",this.listenTdListScroll),this.tdListMaxScrollTop=t.scrollHeight-t.offsetHeight,this.onDraggingThrottle=Object(Q.g)(this.onDragging.bind(this),16)},watch:{list:{handler:function(e){var t=this;this.listCopy=[].concat(N()(e)),this.lastItem=e.find(function(e){return e.key===t.chosenKeys[0]})||e[0],this.chosenKeysCopy=this.chosenKeys.filter(function(t){return-1!==e.findIndex(function(e){return e.key===t})}),this.extChosenKeys=this.chosenKeys.filter(function(t){return-1===e.findIndex(function(e){return e.key===t})}),this.needSort&&(this.doChosenKeysSort(),this.needSort=!1)},immediate:!0},chosenKeys:{handler:function(e){var t=this;this.chosenKeysCopy=e.filter(function(e){return-1!==t.listCopy.findIndex(function(t){return t.key===e})}),this.extChosenKeys=e.filter(function(e){return-1===t.listCopy.findIndex(function(t){return t.key===e})}),e.length?this.lastItem=this.listCopy.find(function(t){return t.key===e[0]})||this.listCopy[0]:this.lastItem=this.listCopy[0]},immediate:!0,sync:!0},chosenKeysCopy:{handler:"updateChosenIndexs",immediate:!0,sync:!0},listCopy:{handler:"updateChosenIndexs",immediate:!0,sync:!0},"list.length":{handler:function(){var e=this;if(this.moving&&this.moveKeys){var t=function(t){if(-1===e.list.findIndex(function(e){return e.key===t}))return{v:e.onSortEnd()}},n=!0,i=!1,r=void 0;try{for(var o,s=Pe()(this.moveKeys);!(n=(o=s.next()).done);n=!0){var a=t(o.value);if("object"===(void 0===a?"undefined":V()(a)))return a.v}}catch(e){i=!0,r=e}finally{try{!n&&s.return&&s.return()}finally{if(i)throw r}}}this.rectUpdated=!1,this.$nextTick(function(){e.scrollHeight=e.$el.scrollHeight,e.updateRect(),e.rectUpdated=!0,e.isScrollable&&e.doScroll(e.lastMouseEvent)})},sync:!0},dragging:function(e){e?this.draggingDetectCallbackId=window.requestAnimationFrame(this.draggingDetect.bind(this)):window.cancelAnimationFrame(this.draggingDetectCallbackId)}},computed:{status:function(){var e=this,t={};return this.listCopy.forEach(function(n,i){t[n.key]={},e.chosenIndexs.indexOf(i)>-1&&(t[n.key].moving=!0,e.moveTargetKey!==n.key&&(t[n.key].hide=!0)),t[n.key].chosen=e.isChosen(n),t[n.key].active=e.isActive(n)}),t},realtimeStartY:function(){return this.startTdListScrollTop-this.tdListScrollTop+this.startY},realtimeCurrentY:function(){return this.currentY+this.tdListScrollTop}},methods:{updateChosenIndexs:function(){var e=this;this.chosenIndexs=this.chosenKeysCopy.map(function(t){return e.listCopy.findIndex(function(e){return e.key===t})})},doChosenKeysSort:function(e){var t=this.moveItem,n=this.chosenKeysCopy,i=this.chosenIndexs,r=this.listCopy;if(!(n.length<=1)){for(var o=!1,s=1;s<i.length;s++)if(i[s]-i[s-1]>1){o=!0;break}if(o){for(var a=[],l=r.findIndex(function(e){return e.key===t.key}),c=0;c<l;c++)-1===i.indexOf(c)&&a.push(r[c]);for(var u=0;u<i.length;u++)a.push(r[i[u]]);for(var d=l+1;d<r.length;d++)-1===i.indexOf(d)&&a.push(r[d]);this.updateList(a,!0)}}},updateList:function(e,t){for(var n=[].concat(N()(e)),i={},r=0,o=void 0;r<n.length;)i[n[r].key]?(Object(Q.h)("List","duplicate key from "+(t?"doChosenKeysSort":"handleSort")+"\n duplicate key: "+n[r].key+", duplicate index: "+r),o=!0,n.splice(r,1)):(i[n[r].key]=!0,r++);o&&console.warn("oldList:",this.list.map(function(e){return e.key}),"newList:",e.map(function(e){return e.key}),"updateList:",JSON.parse(Te()(n)),"updateList.key:",n.map(function(e){return e.key})),this.$emit("update:list",n)},sget:Me.a,clamp:function(e,t,n){return n<e?e:n>t?t:n},onDragStart:function(e){if(this.clickToFocus&&this.$el.focus(),1===e.which){this.setTdListBoundingRect();var t=this.tdListBoundRect,n=t.left,i=t.top;if(e.preventDefault(),this.dragging=!0,this.isClick=!0,this.startX=this.currentX=e.clientX-n,this.startY=this.currentY=e.clientY-i,this.scrollTop=document.documentElement.scrollTop,this.scrollLeft=document.documentElement.scrollLeft,this.startTdListScrollTop=this.tdListScrollTop,this.startDragChosenKeys=this.drop?[]:[].concat(N()(this.chosenKeysCopy)),window.addEventListener("mousemove",this.onDraggingThrottle),window.addEventListener("mouseup",this.onDragEnd),!this.dragArea){var r=this.dragArea=document.createElement("div");r.className="td-drag-area",this.$el.parentNode.appendChild(r)}this.updatePosition()}},onDragging:function(e){var t=this.tdListBoundRect,n=t.left,i=t.right,r=t.bottom,o=t.top;this.dragging&&(this.drop&&this.chosenKeysCopy&&this.chosenKeysCopy.length&&this.updateChosenKeys([]),this.isClick=!1,e.preventDefault(),this.currentX=this.clamp(n,i,e.clientX),this.currentY=this.clamp(o,r,e.clientY),this.clientYDiffWithBoundingRect=e.clientY-this.currentY,this.dragArea.style.cssText="\n          width: "+Math.abs(this.currentX-this.startX-n)+"px;\n          height: "+Math.abs(this.currentY-this.realtimeStartY-o)+"px;\n          top: "+(Math.min(this.currentY-o,this.realtimeStartY)+this.scrollTop)+"px;\n          left: "+(Math.min(this.currentX-n,this.startX)+this.scrollLeft)+"px\n        ",this.immediate&&this.setDragChosen())},onDragEnd:function(e){var t=this;this.dragging&&(window.cancelAnimationFrame(this.draggingDetectCallbackId),setTimeout(function(){t.dragging=!1,t.isClick||(t.setDragChosen(),t.dragArea.style.cssText="display: none")},0)),this.clientYDiffWithBoundingRect=0,window.removeEventListener("mousemove",this.onDraggingThrottle),window.removeEventListener("mouseup",this.onDragEnd)},draggingDetect:function(){if(0!==this.clientYDiffWithBoundingRect){var e=this.clamp(0,this.tdListMaxScrollTop,this.tdListScrollTop+this.clientYDiffWithBoundingRect);this.$el.scrollTop=e,this.setDragChosen()}this.draggingDetectCallbackId=window.requestAnimationFrame(this.draggingDetect.bind(this))},setDragChosen:function(){var e=this.dragArea,t=this.listCopy,n=this.startDragChosenKeys,i=this.itemsPosition,r=[].concat(N()(n)),o=[],s=e.getBoundingClientRect(),a=[s.left,s.left+s.width,s.top,s.top+s.height],l=a[0],c=a[1],u=a[2],d=a[3];i.forEach(function(e,n){var i,r,s,a,h,f,p,m,v,g,y,_,b=e.x1,w=e.x2,S=e.y1,C=e.y2;i=b,r=w,s=S,a=C,h=l,f=c,p=u,m=d,v=Math.abs(i+r-h-f),g=Math.abs(i-r)+Math.abs(h-f),y=Math.abs(s+a-p-m),_=Math.abs(s-a)+Math.abs(p-m),v<=g&&y<=_&&o.push(t[n].key)}),o.forEach(function(e){r.includes(e)||r.push(e)}),function(e,t){if(!Array.isArray(e)||Array.isArray(t))return!1;if(e.length!==t.length)return!1;e=[].concat(N()(e)).sort(),t=[].concat(N()(t)).sort();for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(r,this.chosenKeysCopy)||this.updateChosenKeys(r)},isChosen:function(e){return this.multiple?this.chosenKeysCopy.includes(e.key):this.chosenKeysCopy===e.key},isActive:function(e){return this.multiple?this.chosenKeysCopy.includes(e.key)&&1===this.chosenKeysCopy.length:this.chosenKeysCopy===e.key},handleChooseAll:function(){this.updateChosenKeys(this.listCopy.map(function(e){return e.key}),!0)},handleCleanChosen:function(){this.updateChosenKeys([])},handleItemClick:function(e,t){var n=this;if(this.clickToFocus&&this.$el.focus(),this.multiple){var i=[];if(e.ctrlKey)this.lastItem=t,(i=[].concat(N()(this.chosenKeysCopy))).includes(t.key)?i.splice(i.indexOf(t.key),1):i.push(t.key);else if(e.shiftKey){var r=this.listCopy.findIndex(function(e){return e.key===n.lastItem.key}),o=this.listCopy.indexOf(t);if(o>=r)for(var s=r;s<=o;s++)i.push(this.listCopy[s].key);else for(var a=o;a<=r;a++)i.push(this.listCopy[a].key)}else this.lastItem=t,i=[t.key];this.chosenKeysCopy=i,this.updateChosenKeys(i,!1,!!e.ctrlKey)}else this.updateChosenKeys([t.key])},handleItemClickRight:function(e){this.lastItem=e,this.chosenKeys.includes(e.key)||this.updateChosenKeys([e.key])},handleLoad:function(){var e=this,t=this.loadMore;"function"==typeof t&&t(function(){e.moving&&(e.needSort=!0)})},doDestoryDrag:function(){window.removeEventListener("mousedown",this.onDragStart),this.dragArea&&(document.body.removeChild(this.dragArea),this.dragArea=null)},updatePosition:function(){var e=this,t=$()(this.$el.children);this.itemsPosition=[],t.forEach(function(t){if(t.classList.contains("td-draglist-item")){var n=t.getBoundingClientRect();e.itemsPosition.push({x1:n.left,y1:n.top,x2:n.left+n.width,y2:n.top+n.height})}})},updateChosenKeys:function(e,t,n){var i=this;if(!t&&e.length>=2){var r=e.map(function(e){return i.listCopy.findIndex(function(t){return t.key===e})});r.sort(function(e,t){return e-t}),e=r.map(function(e){return i.listCopy[e].key})}n&&(e=e.concat(this.extChosenKeys)),this.$emit("update:chosenKeys",e)},listenTdListScroll:function(){this.tdListScrollTop=this.$el.scrollTop,this.updatePosition()},setTdListBoundingRect:function(){this.tdListBoundRect=this.$el.getBoundingClientRect()}},beforeDestory:function(){this.dragSelectable&&this.multiple&&this.doDestoryDrag(),this.$el.removeEventListener("scroll",this.listenTdListScroll)},install:function(e){e.component(Ne.name,Ne)}},Ae=Ne,Fe={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"show",rawName:"v-show",value:e.visible,expression:"visible"}],staticClass:"td-loading-mask"},[n("span",{staticClass:"td-loading-mask__icon"},[e._t("default",[n("td-icon",{attrs:{type:"loading"}})])],2),e._v(" "),e.text?n("p",{staticClass:"td-loading-mask__text"},[e._v(e._s(e.text))]):e._e()])},staticRenderFns:[],name:"td-loading",components:{TdIcon:ge.default},props:{loading:[Boolean,F.a],text:String},data:function(){return{visible:!1}},watch:{loading:{handler:function(e){var t=this;"boolean"==typeof e?this.visible=e:e instanceof F.a&&(this.visible=!0,e.finally(function(){t.visible=!1}))},immediate:!0}},install:function(e){e.component(Fe.name,Fe)}},Be=Fe,Ve={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"td-media",class:this.align&&"is-"+this.align},[t("div",{staticClass:"td-media__object"},[this._t("media")],2),this._v(" "),t("div",{staticClass:"td-media__content"},[this._t("default")],2)])},staticRenderFns:[],name:"td-media",props:{align:String},install:function(e){e.component(Ve.name,Ve)}},je=Ve,$e={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-pagination"},[n("td-button",{attrs:{disabled:1===e.value||Boolean(e.loading),secondary:""},on:{click:e.handlePrev}},[e._t("prev",[e._v(e._s(1===e.loading?"加载中...":"上一页"))])],2),e._v(" "),n("div",{staticClass:"td-pager"},[n("span",{staticClass:"td-pager__number is-active"},[e._v(e._s(e.value))]),e._v(" "),n("span",[e._v("/")]),e._v(" "),n("span",{staticClass:"td-pager__number"},[e._v(e._s(e.total))])]),e._v(" "),n("td-button",{attrs:{disabled:e.value===e.total||Boolean(e.loading),secondary:""},on:{click:e.handleNext}},[e._t("next",[e._v(e._s(2===e.loading?"加载中...":"下一页"))])],2)],1)},staticRenderFns:[],name:"td-pagination",components:{TdButton:se.default},model:{prop:"value",event:"change"},props:{value:Number,total:Number,beforeChange:Function},data:function(){return{loading:0}},methods:{prev:function(){this.loading=0,this.$emit("change",this.value-1)},next:function(){this.loading=0,this.$emit("change",this.value+1)},handlePrev:function(){this.value>1&&(this.beforeChange?(this.loading=1,this.beforeChange(this.value-1).then(this.prev)):this.prev(),this.$emit("prev-click"))},handleNext:function(){this.value<this.total&&(this.beforeChange?(this.loading=2,this.beforeChange(this.value+1).then(this.next)):this.next(),this.$emit("next-click"))}},install:function(e){e.component($e.name,$e)}},He=$e,Ue={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-progress",class:["td-progress--"+e.type],style:e.circleSize},["circle"===e.type?n("div",{staticClass:"td-progress-circle"},[n("svg",{staticClass:"td-progress-circle__track",attrs:{viewBox:e.view}},[n("circle",{staticClass:"td-progress-circle__track",style:{stroke:e.outerColor},attrs:{cx:e.circleDistance,cy:e.circleDistance,r:e.radius,"stroke-width":e.strokeWidth}}),e._v(" "),n("circle",{staticClass:"td-progress-circle__path",style:e.circlePathStyle,attrs:{cx:e.circleDistance,cy:e.circleDistance,r:e.radius,"stroke-width":e.strokeWidth,"stroke-linecap":e.linecap}})])]):n("div",{staticClass:"td-progress-bar",style:{height:e.height+"px"}},[n("div",{staticClass:"td-progress-bar__outer",style:{backgroundColor:e.outerColor}},[n("div",{staticClass:"td-progress-bar__inner",style:{width:e.value+"%",backgroundColor:e.color}})])]),e._v(" "),n("p",{directives:[{name:"show",rawName:"v-show",value:e.textVisible,expression:"textVisible"}],staticClass:"td-progress__text"},[e._t("default",[e._v(e._s(e.value)+"%")])],2)])},staticRenderFns:[],name:"td-progress",props:{type:{type:String,default:"line",validator:function(e){return["line","circle"].includes(e)}},color:String,height:Number,outerColor:String,textVisible:{type:Boolean,default:!1},value:{type:[Number,String],default:0},width:{type:Number,default:120},strokeWidth:{type:Number,default:6},linecap:{type:String,default:"round"}},computed:{circleSize:function(){return"circle"===this.type?{width:this.width+"px",height:this.height+"px"}:{}},radius:function(){return"circle"===this.type?this.width/2-this.strokeWidth:0},circleDistance:function(){return this.radius+this.strokeWidth},perimeter:function(){return 2*Math.PI*this.radius},view:function(){var e="0 0 "+this.width+" "+this.width;return e},circlePathStyle:function(){var e=this.color;return 0===this.value&&(e="transparent"),{strokeDasharray:this.perimeter*(this.value/100)+"px, "+this.perimeter+"px",transform:"rotate(-90deg)",stroke:e,transformOrigin:this.circleDistance+"px "+this.circleDistance+"px",transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease"}}},install:function(e){e.component(Ue.name,Ue)}},Ge=Ue,Ke={render:function(){var e=this.$createElement,t=this._self._c||e;return t("label",{staticClass:"td-radio",class:{"is-checked":this.checked,"is-disabled":this.disabled}},[t("input",{staticClass:"td-radio__inner",attrs:{type:"radio",disabled:this.disabled},domProps:{checked:this.checked},on:{change:this.handleInput}}),this._v(" "),t("span",{staticClass:"td-radio__label"},[this._t("default")],2)])},staticRenderFns:[],name:"td-radio",props:{label:[Number,String],value:[Number,String],disabled:{type:Boolean,default:!1}},computed:{checked:function(){return this.value===this.label}},methods:{handleInput:function(){this.$emit("input",this.label)}},install:function(e){e.component(Ke.name,Ke)}},Xe=Ke,ze={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-rate",class:{"td-rate--readonly":e.readonly}},[e._l(5,function(t){return n("span",{key:t,staticClass:"td-rate__item",class:{"is-on":e.v>=t,"is-half":e.v>t-1&&e.v<t},on:{mouseenter:function(n){return e.setCurrentValue(t)},mouseleave:e.resetCurrentValue,click:function(n){return e.handleRate(t)}}},[e._m(0,!0)])}),e._v(" "),e.textVisible||e.showScore?n("span",{staticClass:"td-rate__text"},[e._t("default",[e._v(e._s(e.text))])],2):e.showText?n("span",{staticClass:"td-rate__text"},[e._v(" "+e._s(e.text)+" ")]):e._e()],2)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("i",{staticClass:"td-icon-star"},[t("i",{staticClass:"td-icon-star-half"})])}],name:"td-rate",props:{value:{type:Number,default:0},total:{type:Number,default:5},readonly:{type:Boolean,default:!1},textVisible:{type:Boolean,default:!1},showScore:{type:Boolean,default:!1},showText:{type:Boolean,default:!1},texts:{type:Array,default:function(){return["不满意","一般","一般","满意","非常满意"]}}},data:function(){return{currentValue:this.value}},computed:{v:function(){return this.readonly?this.value/this.total*5:this.currentValue/this.total*5},text:function(){var e="";return this.showScore||this.textVisible?e=this.v:this.showText&&(e=this.texts[Math.ceil(this.v)-1]),e}},methods:{setCurrentValue:function(e){this.readonly||(this.currentValue=e*this.total/5)},resetCurrentValue:function(){this.readonly||(this.currentValue=this.value)},handleRate:function(){this.readonly||(this.$emit("input",this.currentValue),this.$emit("change",this.currentValue))}},install:function(e){e.component(ze.name,ze)}},Ye=ze,Ze={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-select",class:{"is-disabled":e.disabled,"is-top":"top"===e.position},attrs:{tabindex:"0"},on:{focusin:function(t){e.focused=!0},focusout:function(t){e.focused=!1}}},[n("div",{ref:"select",staticClass:"td-select-group",class:{"is-choose":e.value,"is-focus":e.focused}},[e._t("prefix"),e._v(" "),e.editable?n("td-input",{attrs:{value:e.value,placeholder:e.placeholder},on:{input:e.handleInput}}):n("span",{staticClass:"td-select-group__label",on:{click:function(t){e.menuVisible=!e.menuVisible}}},[e._v(" "+e._s(e.value||e.placeholder)+" ")]),e._v(" "),e._t("suffix"),e._v(" "),n("a",{staticClass:"td-select__drop",attrs:{href:"javascript:;"},on:{click:function(t){e.menuVisible=!e.menuVisible}}},[n("td-icon",{attrs:{type:"arrow-drop"}})],1),e._v(" "),"file"===e.type?n("a",{staticClass:"td-select__choose",attrs:{href:"javascript:;"},on:{click:e.handleChooseFile}},[n("td-icon",{attrs:{type:"file",svg:""}})],1):e._e()],2),e._v(" "),e.customMenuEnabled?e._e():n("ul",{directives:[{name:"show",rawName:"v-show",value:e.menuVisible,expression:"menuVisible"}],staticClass:"td-dropdown-menu",class:{"td-dropdown-menu--select":e.markSelected}},e._l(e.options,function(t){return n("li",{key:t,staticClass:"td-dropdown-menu__item",class:t===e.value&&"is-selected",on:{click:function(n){return e.handleInput(t)}}},[e._v(" "+e._s(t)+" ")])}),0)])},staticRenderFns:[],name:"td-select",components:{TdIcon:ge.default,TdInput:we},props:{value:[Number,String],placeholder:String,options:Array,type:{type:String,default:"select"},customMenuEnabled:Boolean,disabled:Boolean,editable:Boolean,position:String,markSelected:{type:Boolean,default:!1}},data:function(){return{focused:!1,menuVisible:!1}},watch:{focused:function(e){this.$emit(e?"focus":"blur")},menuVisible:function(e){this.$emit(e?"menu-show":"menu-hide")}},methods:{handleChooseFile:function(){this.$emit("choose-file")},handleDocClick:function(e){e.target.closest(".td-select-group")!==this.$refs.select&&(this.menuVisible=!1)},handleInput:function(e){this.$emit("input",e)}},mounted:function(){document.addEventListener("click",this.handleDocClick)},destroyed:function(){document.removeEventListener("click",this.handleDocClick)},install:function(e){e.component(Ze.name,Ze)}},qe=Ze,Je=n(217),Qe=n.n(Je),et=n(252),tt=n.n(et),nt=n(33),it=n.n(nt),rt=n(253),ot=n.n(rt),st={render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"td-tree"})},staticRenderFns:[],name:"td-tree"},at={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-tree-node",class:{"is-expanded":e.expanded},style:{"padding-left":20*e.level+"px"}},[n("div",{staticClass:"td-tree-node__content"},[e.treeEnabled?n("td-icon",{staticClass:"td-tree-node__expand-icon",class:{"is-expanded":e.expanded,"is-hidden":!e.$slots.default&&!e.expandable},attrs:{type:"arrow-drop"},nativeOn:{click:function(t){return e.handleExpandIconClick(t)}}}):e._e(),e._v(" "),e.checkable?n("td-checkbox",{attrs:{indeterminate:e.indeterminate,value:e.checked,disabled:e.disabled},on:{input:e.handleInput}},[e.$slots.icon?n("span",{staticClass:"td-tree-node__image-icon"},[e._t("icon")],2):e._e(),e._v(" "),n("span",{staticClass:"td-tree-node__label"},[e._t("label",[e._v(e._s(e.label))])],2)]):e._e()],1),e._v(" "),e.$slots.default?n("div",{staticClass:"td-tree-node__children"},[e._t("default")],2):e._e()])},staticRenderFns:[],name:"td-tree-node",components:{TdCheckbox:ae.default,TdIcon:ge.default},props:{label:String,level:Number,checked:Boolean,disabled:Boolean,expanded:Boolean,indeterminate:Boolean,checkable:Boolean,expandable:Boolean,treeEnabled:Boolean},methods:{handleExpandIconClick:function(){this.$emit("update:expanded",!this.expanded)},handleInput:function(e){this.$emit("change",e)}}};st.install=function(e){e.component(st.name,st),e.component(at.name,at)};var lt=st,ct={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-table",class:{"td-table--border":e.bordered,"td-table--stripe":e.striped,"td-table-tree":e.treeEnabled,"td-table--checkbox":e.checkable,"td-table--hover":e.hoverable}},[n("div",{staticClass:"td-table__header-wrapper"},[n("table",{staticClass:"td-table__header"},[n("colgroup",e._l(e.columns,function(e){return n("col",{key:e.prop,attrs:{width:e.width}})}),0),e._v(" "),n("thead",[n("tr",e._l(e.columns,function(t,i){return n("th",{key:t.prop},[t.sortable?n("a",{staticClass:"td-table__text",attrs:{href:"javascript:;"},on:{click:function(n){return e.handleSort(t)}}},[e.treeEnabled&&0===i?n("td-icon",{staticClass:"td-tree-node__expand-icon",class:{"is-expanded":e.allExpanded,"is-hidden":!e.allExpandable},attrs:{type:"arrow-drop"},nativeOn:{click:function(t){return t.stopPropagation(),e.expandAll(!e.allExpanded)}}}):e._e(),e._v(" "+e._s(t.label)+" "),n("td-icon",{class:it()({"is-show":t===e.sorting.column},"is-"+e.sorting.order,t===e.sorting.column),attrs:{type:"sequence"}})],1):n("p",{staticClass:"td-table__text"},[e.treeEnabled&&0===i?n("td-icon",{staticClass:"td-tree-node__expand-icon",class:{"is-expanded":e.allExpanded,"is-hidden":!e.allExpandable},attrs:{type:"arrow-drop"},nativeOn:{click:function(t){return e.expandAll(!e.allExpanded)}}}):e._e(),e._v(" "+e._s(t.label)+" ")],1)])}),0)])])]),e._v(" "),n("div",{staticClass:"td-table__body-wrapper",style:{height:e.height+"px"}},[n("table",{staticClass:"td-table__body"},[n("colgroup",e._l(e.columns,function(e){return n("col",{key:e.prop,attrs:{width:e.width}})}),0),e._v(" "),n("tbody",e._l(e.sortedRows,function(t){return e.status[t.key].visible||e.status[t.key].mounted?n("tr",{key:t.key,class:{"is-checked":e.status[t.key].checked},style:{display:e.status[t.key].visible&&e.status[t.key].mounted?"table-row":"none"}},e._l(e.columns,function(i,r){return n("td",{key:i.prop},[0===r&&(e.checkable||e.treeEnabled)?n("td-tree-node",{attrs:{label:t[i.prop],level:t._level,checked:e.status[t.key].checked,disabled:e.status[t.key].disabled,expanded:e.status[t.key].expanded,indeterminate:e.status[t.key].indeterminate,checkable:e.checkable,expandable:e.sget(t._children,"length")>0,"tree-enabled":e.treeEnabled},on:{change:function(n){return e.check(t.key)},"update:expanded":function(n){return e.expand(t.key)}}},[e._t("icon",null,{slot:"icon",prop:i.prop,value:t._row[i.prop],row:t._row}),e._v(" "),e._t("default",[e._v(" "+e._s(t._row[i.prop])+" ")],{slot:"label",prop:i.prop,value:t._row[i.prop],row:t._row})],2):n("p",{staticClass:"td-table__text"},[e._t("default",[e._v(" "+e._s(t._row[i.prop])+" ")],{prop:i.prop,value:t._row[i.prop],row:t._row})],2)],1)}),0):e._e()}),0)])]),e._v(" "),e.footerEnabled?n("div",{staticClass:"td-table__footer-wrapper"},[n("table",{staticClass:"td-table__footer"},[n("colgroup",[n("col"),e._v(" "),n("col",{attrs:{width:e.footerColWidth}})]),e._v(" "),n("tbody",[n("tr",[n("td",{staticClass:"td-table-tree__cell"},[n("td-checkbox",{attrs:{value:e.allChecked,indeterminate:e.allIndeterminate,disabled:e.allSelectedDisabled},on:{input:function(t){return e.checkAll()}}},[e._v("全选")])],1),e._v(" "),n("td",{staticClass:"td-table-tree__cell"},[e._t("footer")],2)])])])]):e._e()])},staticRenderFns:[],name:"td-table",components:{TdIcon:ge.default,TdTreeNode:at,TdCheckBox:ae.default},props:{columns:Array,data:Array,height:Number,defaultCheckedKeys:{type:Array,default:function(){return[]}},defaultExpandedKeys:{type:Array,default:function(){return[]}},disabledKeys:{type:Array,default:function(){return[]}},bordered:Boolean,striped:Boolean,checkable:Boolean,footerEnabled:Boolean,hoverable:Boolean,treeEnabled:Boolean,footerColWidth:{type:Number,default:function(){return 110}},allSelectedDisabled:{type:Boolean,default:!1}},data:function(){return{rows:[],checkedKeysCache:null,expandedKeysCache:null,disabledKeysCache:null,checkedRow:[],sorting:{column:null,order:""},status:{}}},computed:{sortedRows:function(){var e=this,t=[];t.length=this.rows.length;var n=0,i=this.status;return he()(i).forEach(function(e){var r=i[e];r.visible&&(t[n++]=r.row)}),t=t.slice(0,n),this.sorting.column&&t.sort(function(t,n){return e.compare(t,n,e.rows)}),t},allChecked:function(){return this.checkedRow.length===this.rows.length},allIndeterminate:function(){var e=this;return!this.allChecked&&Boolean(this.rows.find(function(t){return e.status[t.key].checked||e.status[t.key].indeterminate}))},allExpanded:function(){var e=this;return this.rows.every(function(t){return e.status[t.key].visible})},allExpandable:function(){return this.rows.find(function(e){return e._children})}},watch:{data:{handler:function(e){this.rows=tt()(this.getRows(e)),this.initStatus()},immediate:!0},defaultCheckedKeys:"initStatus",defaultExpandedKeys:"initStatus",disabledKeys:"initStatus"},methods:{sget:Me.a,compare:function(e,t,n){for(var i=0,r=e._path[i],o=t._path[i],s="descending"===this.sorting.order,a=this.sorting.column;r===o;)i++,r=e._path[i],o=t._path[i];return ot()(function(e,t){return e?t?0:1:-1},function(e,t){var n=0;if(a.sorter)n=a.sorter(e._row,t._row);else{var i=e._row[a.prop],r=t._row[a.prop];i<r?n=-1:i>r&&(n=1)}return s?-n:n},function(e,t){return n.indexOf(e)<n.indexOf(t)?s?1:-1:s?-1:1})(r,o)},getRows:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=[];return e.forEach(function(e){var o={_level:n,_parent:i,_row:e,key:e.key};if(o._path=i?[].concat(N()(i._path),[o]):[o],r.push(o),e.children){var s=t.getRows(e.children,n+1,o);r=[].concat(N()(r),N()(s)),o._children=s.filter(function(e){return e._level===n+1}),o._childrenRows=s}}),r},getStatus:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.forEach(function(e){var i=n[e._parent&&e._parent.key]||{};n[e.key]={checked:Boolean(t.checkedKeysCache[e.key]),disabled:i.disabled||t.disabledKeysCache[e.key],expanded:t.expandedKeysCache[e.key],indeterminate:!1,visible:0===e._level||i.expanded&&i.visible,mounted:0===e._level||i.expanded&&i.visible,row:e},e._children&&(t.getStatus(e._children,n),n[e.key].checked=e._children.every(function(e){return n[e.key].checked}),!n[e.key].checked&&e._children.find(function(e){return n[e.key].checked||n[e.key].indeterminate})&&(n[e.key].indeterminate=!0))}),n},initStatus:function(){var e=this;this.checkedKeysCache={},this.expandedKeysCache={},this.disabledKeysCache={},this.defaultCheckedKeys.forEach(function(t){e.checkedKeysCache[t]=!0}),this.defaultExpandedKeys.forEach(function(t){e.expandedKeysCache[t]=!0}),this.disabledKeys.forEach(function(t){e.disabledKeysCache[t]=!0}),this.status=this.getStatus(this.rows.filter(function(e){return 0===e._level}));var t=this.getCheckedRowAndCheckedKey(),n=Qe()(t,2),i=(n[0],n[1]);this.checkedRow=i},check:function(e,t,n){var i=this,r=this.rows.find(function(t){return t.key===e});!function e(t,n){t._children?(t._children.forEach(function(t){e(t,n)}),i.status[t.key].checked=t._children.every(function(e){return i.status[e.key].checked}),i.status[t.key].checked?i.status[t.key].indeterminate=!1:i.status[t.key].indeterminate=Boolean(t._children.find(function(e){return i.status[e.key].checked||i.status[e.key].indeterminate}))):i.status[t.key].disabled||(i.status[t.key].checked=n)}(r,t=Object(Q.c)(t)?t:r._children?!r._childrenRows.filter(function(e){return!e._children&&!i.status[e.key].disabled}).every(function(e){return i.status[e.key].checked}):!this.status[r.key].checked),function e(t){Object(Q.e)(t)||(i.status[t.key].checked=t._children.every(function(e){return i.status[e.key].checked}),i.status[t.key].checked?i.status[t.key].indeterminate=!1:i.status[t.key].indeterminate=Boolean(t._children.find(function(e){return i.status[e.key].checked||i.status[e.key].indeterminate})),e(t._parent))}(r._parent),n||this.emitCheckedChange(e,t)},checkAll:function(e){var t=this;e=Object(Q.c)(e)?e:!this.rows.filter(function(e){return!e._children&&!t.status[e.key].disabled}).every(function(e){return t.status[e.key].checked}),this.rows.filter(function(e){return 0===e._level}).forEach(function(n){t.check(n.key,e,!0)}),this.$emit("checkAll",e),this.emitCheckedChange()},expand:function(e,t){var n=this,i=this.rows.find(function(t){return t.key===e});t=Object(Q.c)(t)?t:!this.status[e].expanded;this.status[e].expanded=t,this.status[e].visible&&function e(t,i){t._children.forEach(function(t){n.status[t.key].visible=i&&n.status[t._parent.key].expanded,n.status[t.key].visible&&!n.status[t.key].mounted&&(n.status[t.key].mounted=!0),t._children&&e(t,i)})}(i,t),this.emitExpandedChange(e,t)},expandAll:function(e){var t=this;e=Object(Q.c)(e)?e:!this.allExpanded,this.rows.forEach(function(n){n._children&&(t.status[n.key].expanded=e),n._level>0&&(t.status[n.key].visible=e)}),this.emitExpandedChange()},getCheckedRowAndCheckedKey:function(){var e=[],t=this.status,n=[];for(var i in t)t[i].checked&&(e.push(i),n.push(t[i].row));return[e,n]},emitCheckedChange:function(e,t){var n=this.getCheckedRowAndCheckedKey(),i=Qe()(n,2),r=i[0],o=i[1];this.checkedRow=o,this.$emit("checked-change",r,e,t)},emitExpandedChange:function(e,t){var n=[],i=this.status;for(var r in i)i[r].checked&&n.push(r);this.$emit("expanded-change",n,e,t)},handleSort:function(e){this.sorting.column===e?this.sorting.order="ascending"===this.sorting.order?"descending":"ascending":this.sorting={column:e,order:"ascending"}}},install:function(e){e.component(ct.name,ct)}},ut=ct,dt={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-tabs"},[n("div",{staticClass:"td-tabs__title",class:e.customTitleClass},[n("div",{staticClass:"td-tabs__nav"},e._l(e.tabs,function(t){return n("div",{key:t.key,staticClass:"td-tabs__item",class:{"is-active":t.key===e.activeKey},on:{click:function(n){return e.handleTitleClick(t)},mousedown:function(n){return e.handleTitleClick(t,!0)}}},[e._t("title",[e._v(e._s(t.title))],{tab:t})],2)}),0),e._v(" "),n("div",{staticClass:"td-tabs__extra"},[e._t("extra")],2)]),e._v(" "),n("div",{staticClass:"td-tabs__content"},e._l(e.tabs,function(t){return t.key===e.activeKey?n("div",{key:t.key,staticClass:"td-tabs__pane"},[e._t("default",null,{tab:t})],2):e._e()}),0)])},staticRenderFns:[],name:"td-tabs",props:{tabs:Array,activeKey:null,customTitleClass:String,triggerChangeTabByMouseDown:{type:Boolean,default:!0}},methods:{handleTitleClick:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.triggerChangeTabByMouseDown===t&&this.$emit("update:activeKey",e.key)}},install:function(e){e.component(dt.name,dt)}},ht=dt,ft={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",e._g({staticClass:"td-tooltip-wrapper"},e.listeners),[e._t("default"),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.tooltipVisible,expression:"tooltipVisible"}],staticClass:"td-tooltip",class:"is-"+e.placement,style:e.position},[n("div",{staticClass:"td-tooltip__inner"},[e._t("content",[e._v(e._s(e.content))])],2),e._v(" "),n("span",{staticClass:"td-poper__arrow"})])],2)},staticRenderFns:[],name:"td-tooltip",props:{content:String,offset:{type:Object,default:function(){return{x:0,y:0}}},placement:{type:String,default:"bottom"},trigger:String,visible:Boolean,delay:{type:Number,default:0}},data:function(){return{tooltipVisible:this.visible}},computed:{listeners:function(){var e,t=this,n={focus:"focusin",hover:"mouseenter"};return n[this.trigger]?(e={},it()(e,n[this.trigger],function(){setTimeout(function(){t.tooltipVisible=!0},t.delay)}),it()(e,{focus:"focusout",hover:"mouseleave"}[this.trigger],function(){setTimeout(function(){t.tooltipVisible=!1},t.delay)}),e):null},position:function(){var e=this.offset,t=e.x,n=void 0===t?0:t,i=e.y,r=void 0===i?0:i;switch(this.placement){case"top":return{margin:"0 0 -"+r+"px "+n+"px"};case"right":case"bottom":return{margin:r+"px 0 0 "+n+"px"};case"left":return{margin:r+"px -"+n+"px 0 0"}}}},watch:{visible:function(e){this.tooltipVisible=e},tooltipVisible:function(e){this.$emit(e?"show":"hide")},trigger:function(e){this.tooltipVisible=!1}},install:function(e){e.component(ft.name,ft)}},pt=ft,mt=n(90),vt={props:{value:Number,disabled:Boolean},inject:["slider"],data:function(){return{dragging:!1,currentX:void 0,currentY:void 0,startX:void 0,startY:void 0,sliderSize:void 0,style:{}}},watch:{value:function(e){this.setStyle()}},computed:{vertical:function(){return this.slider.vertical}},created:function(){this.onDraggingThrottle=Object(Q.g)(this.onDragging.bind(this),16)},methods:{handleClick:function(e){this.currentX=e.clientX,this.currentY=e.clientY,this.setValue(!0)},onDragStart:function(e){this.disabled||(this.$emit("dragStart"),this.slider.$refs.slider&&(e.preventDefault(),this.initPosition(),this.dragging=!0,this.isClick=!0,window.addEventListener("mousemove",this.onDraggingThrottle),window.addEventListener("mouseup",this.onDragEnd),window.addEventListener("contextmenu",this.onDragEnd)))},onMouseEnter:function(){this.$emit("mouseEnter")},onMouseLeave:function(){this.$emit("mouseLeave")},onDragging:function(e){this.dragging&&(e.preventDefault(),this.isClick=!1,this.vertical?this.currentY=e.clientY:this.currentX=e.clientX,this.setValue())},onDragEnd:function(e){var t=this;this.dragging&&setTimeout(function(){t.dragging=!1,t.$emit("dragEnd"),t.isClick||t.setValue()},0),window.removeEventListener("mousemove",this.onDraggingThrottle),window.removeEventListener("mouseup",this.onDragEnd),window.removeEventListener("contextmenu",this.onDragEnd)},setStyle:function(e){var t=this.slider,n=t.vertical,i=t.scales,r=this.sliderSize,o=this.value;if(void 0!==o&&null!==o){void 0===r&&(e?r=100:(this.initPosition(),r=this.sliderSize));for(var s,a=r/(i.length-1),l=0,c=1;o>i[c];)l+=a,c++;s=(l+=(o-i[c-1])/(i[c]-i[c-1])*a)/r*100+"%",this.style=it()({},n?"bottom":"left",s),this.$emit("update-inner-style",s)}else this.style=it()({},n?"bottom":"left",0)},setValue:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.currentY,n=this.currentX,i=this.vertical,r=this.slider.scales,o=this.startX,s=this.startY,a=this.sliderSize;void 0!==a&&void 0!==o&&void 0!==s||(this.initPosition(),o=this.startX,s=this.startY,a=this.sliderSize);var l=a/(r.length-1),c=void 0;c=i?Math.max(Math.min(a,s-t),0):Math.max(Math.min(a,n-o),0);for(var u=r[0],d=1;c>l;)c-=l,u+=r[d]-r[d-1],d++;(u+=parseInt(c/l*(r[d]-r[d-1])))!==this.value&&(this.$emit("input",u),e&&this.$emit("release",u))},initPosition:function(){var e=this.slider.$refs.slider;if(e){var t=e.getBoundingClientRect();this.vertical?(this.sliderSize=t.height,this.startY=t.top+t.height):(this.sliderSize=t.width,this.startX=t.left)}},setSliderSize:function(e,t){var n=this;this.$nextTick(function(){n.slider.$refs.slider&&(t||n.initPosition(),e&&n.setStyle(t))})}},mounted:function(){this.setSliderSize(!0,!0),this.setSliderSizeDebounce=Object(Q.b)(this.setSliderSize.bind(this,!1),60),window.addEventListener("resize",this.setSliderSizeDebounce)},render:function(){var e=arguments[0],t=this.style,n=this.vertical;return e("button",{class:["td-slider__button",this.disabled?"is-disabled":""],style:[t,n?{top:"auto"}:""],on:{mousedown:this.onDragStart.bind(this),mouseenter:this.onMouseEnter,mouseleave:this.onMouseLeave,click:function(e){return e.stopPropagation()}}})}},gt={name:"td-slider",props:{showTooltip:{type:Boolean,default:!1},vertical:Boolean,marks:Array,scales:{type:Array,default:function(){return[0,100]}},value:Number,disabled:Boolean,renderMarksLabel:Function,renderMarks:Function},components:{SliderButton:vt},data:function(){return{valueCache:void 0,innerStyle:{height:0},isSliderButtonDragging:!1,isHovering:!1}},created:function(){var e=this;this.$watch("value",function(t){e.valueCache=t},{immediate:!0})},provide:function(){return{slider:this}},methods:{handleSliderButtonDragEnd:function(){this.isSliderButtonDragging=!1,this.handleRelease(this.valueCache)},handleRelease:function(e){this.$emit("release",e)},handleSliderButtonDragStart:function(){this.isSliderButtonDragging=!0},handleUpdate:function(e){this.valueCache=e,this.$emit("input",e),this.$emit("change",e)},handleClick:function(e){this.disabled||this.$refs.button.handleClick(e)},handleUpdateInnerStyle:function(e){this.innerStyle=it()({},this.vertical?"height":"width",e)},handleMouseEnter:function(){this.isHovering=!0},handleMouseLeave:function(){this.isHovering=!1}},render:function(e){var t=this.vertical,n=this.innerStyle,i=this.marks,r=this.valueCache,o=this.disabled,s=this.scales,a=this.renderMarks,l=this.renderMarksLabel,c={value:r,disabled:o};return e("div",{class:["td-slider",t?"is-vertical":"",o?"is-disabled":""],ref:"slider"},[e("div",{class:"td-slider__bar",on:{click:this.handleClick}},[e("div",{class:"td-slider__bar-inner",style:n}),a?a.call(this.$parent._renderProxy,e,r):i&&i.map(function(n,o){var a=1/(i.length-1)*100*o+"%",l=it()({},t?"top":"left",a);return e("div",{class:["td-slider__dot",r>s[o]?"is-active":""],style:l})}),e(vt,L()([{on:{input:this.handleUpdate,release:this.handleRelease,mouseEnter:this.handleMouseEnter,mouseLeave:this.handleMouseLeave,"update-inner-style":this.handleUpdateInnerStyle,dragEnd:this.handleSliderButtonDragEnd,dragStart:this.handleSliderButtonDragStart}},{props:c},{ref:"button"}])),this.showTooltip&&(this.isSliderButtonDragging||this.isHovering)&&e("div",{class:"td-slider__tips is-active",style:this.vertical?{bottom:""+this.innerStyle.height}:{left:this.innerStyle.width}},[~~(this.innerStyle[this.vertical?"height":"width"]||"").slice(0,-1)])]),l?e("div",{class:"td-slider__mark"},[l.call(this.$parent._renderProxy,e,r)]):i&&i.length&&e("div",{class:"td-slider__mark"},[i&&i.map(function(n,r){var o=1/(i.length-1)*100*r+"%",s={};return t?s.top=o:s.left=o,e("div",{class:"td-slider__mark-text",style:s},[n])})])])},install:function(e){return e.component(gt.name,gt)}},yt=gt,_t={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-switch",class:{"is-disabled":e.disabled,"is-checeked":e.checked}},[n("div",{staticClass:"td-switch__inner",on:{click:function(t){return t.preventDefault(),e.switchValue(t)}}}),e._v(" "),n("span",{staticClass:"td-switch__text"},[e.checked?e._t("open"):e._e(),e._v(" "),e.checked?e._e():e._t("close")],2)])},staticRenderFns:[],name:"td-switch",props:{value:{type:[String,Number,Boolean],default:!1},disabled:{type:Boolean,default:!1}},data:function(){return{checked:this.value}},methods:{switchValue:function(){!this.disabled&&this.handleChange()},handleChange:function(){this.checked=!this.checked,this.$emit("input",this.checked),this.$emit("change",this.checked)}},install:function(e){e.component(_t.name,_t)}},bt=_t,wt={install:function(e){e.directive("loading",{bind:function(t,n,i){var r=n.value,o=i.context.$loadingInstance=new e({el:t.appendChild(document.createElement("div")),components:{TdLoading:Be},data:{loading:!1,text:""},render:function(e){return e("td-loading",{props:{loading:this.loading,text:this.text}})}});"boolean"==typeof r||r instanceof F.a?o.loading=r:(o.loading=r.loading,o.text=r.text)},update:function(e,t,n){var i=t.value,r=n.context.$loadingInstance;"boolean"==typeof i||i instanceof F.a?(r.loading=i,r.text=""):(r.loading=i.loading,r.text=i.text)}})}},St=n(91),Ct=n(92),xt={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-breadcrumb"},[n("div",{staticClass:"td-breadcrumb__items"},[n("div",{staticClass:"td-breadcrumb__item"},[n("span",{staticClass:"td-breadcrumb__text",on:{click:function(t){return e.handleClickShrinkRouteListItem(-1)}}},[n("a",{attrs:{href:"javascript:;"}},[e._v(e._s(e.fullRoutes[0].title))])]),e._v(" "),n("span",{directives:[{name:"show",rawName:"v-show",value:e.shrinkRouteList.length,expression:"shrinkRouteList.length"}],staticClass:"td-breadcrumb__separator"},[n("i",{staticClass:"td-icon-arrow-right"})])]),e._v(" "),n("div",{staticClass:"td-breadcrumb__item"},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.shrinkRouteList.length,expression:"shrinkRouteList.length"}],staticClass:"td-select"},[e._m(0),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.shrinkSelectVisible,expression:"shrinkSelectVisible"}],staticClass:"td-dropdown-menu"},[n("ul",e._l(e.shrinkRouteList,function(t,i){return n("li",{key:t.id+i,staticClass:"td-dropdown-menu__item",attrs:{title:t.title},on:{click:function(t){return e.handleClickShrinkRouteListItem(i)}}},[e._v(" "+e._s(t.title)+" ")])}),0)])]),e._v(" "),n("span",{directives:[{name:"show",rawName:"v-show",value:e.restRouteList.length,expression:"restRouteList.length"}],staticClass:"td-breadcrumb__separator"},[n("i",{staticClass:"td-icon-arrow-right"})])]),e._v(" "),e._l(e.restRouteList,function(t,i){return n("div",{key:t.id+i,staticClass:"td-breadcrumb__item"},[i===e.restRouteList.length-1?[n("div",{staticClass:"td-breadcrumb__item"},[n("span",{staticClass:"td-breadcrumb__text",attrs:{title:t.title}},[e._v(e._s(t.title))])])]:[n("div",{staticClass:"td-breadcrumb__item"},[n("span",{staticClass:"td-breadcrumb__text",attrs:{title:t.title},on:{click:function(t){return e.handleClickRestRouteListItem(i)}}},[n("a",{attrs:{href:"javascript:;"}},[e._v(e._s(t.title))])]),e._v(" "),e._m(1,!0)])]],2)})],2)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"td-select-group"},[t("span",{staticClass:"td-select-group__label"}),this._v(" "),t("a",{staticClass:"td-select__drop",attrs:{href:"javascript:;"}},[t("i",{staticClass:"td-icon-arrow-drop"})])])},function(){var e=this.$createElement,t=this._self._c||e;return t("span",{staticClass:"td-breadcrumb__separator"},[t("i",{staticClass:"td-icon-arrow-right"})])}],name:"td-breadcrumb",props:{initRouteList:{type:Array,required:!0}},data:function(){return{shrinkListVisible:!1,shrinkSelectVisible:!1,shrinkRouteList:[],restRouteList:[]}},methods:{init:function(){var e=this;this.shrinkRouteList=this.initRouteList.slice(1),this.restRouteList=[],this.$nextTick(function(){e.autoExpandAlign()})},handleClickDropdown:function(){this.shrinkSelectVisible=!this.shrinkSelectVisible},autoShrinkAlign:function(){var e=this,t=this.$el,n=t.offsetWidth;t.scrollWidth>n&&this.restRouteList.length&&(this.shrinkRouteList.push(this.restRouteList.shift()),this.$nextTick(function(){e.autoShrinkAlign()}))},autoExpandAlign:function(){var e=this,t=this.$el,n=t.offsetWidth;t.scrollWidth<=n&&this.shrinkRouteList.length?(this.restRouteList.unshift(this.shrinkRouteList.pop()),this.$nextTick(function(){e.autoExpandAlign()})):this.autoShrinkAlign()},handleClickRestRouteListItem:function(e){this.restRouteList=this.restRouteList.slice(0,e+1),this.$emit("onRoutelistChange",this.fullRoutes)},handleClickShrinkRouteListItem:function(e){this.restRouteList=[],this.shrinkRouteList=this.shrinkRouteList.slice(0,e+1),this.$emit("onRoutelistChange",this.fullRoutes)}},computed:{fullRoutes:function(){return[this.initRouteList[0]].concat(N()(this.shrinkRouteList),N()(this.restRouteList))}},mounted:function(){try{this.restRouteList=JSON.parse(Te()(this.initRouteList))}catch(e){console.warn(e)}this.init()},watch:{initRouteList:{handler:function(e,t){this.init()}}},install:function(e){e.component(xt.name,xt)}},Et=xt,Tt=n(81),Ot=n.n(Tt),Pt=n(82),kt=n.n(Pt),Mt=n(167),Rt=n.n(Mt),It={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-tree td-tree--hover"},e._l(e.copyData,function(t,i){return n("td-tree-node-plus",{key:e.bindingKeyGenerator(t),attrs:{node:t,lazy:e.lazy,load:e.load,levelPadding:e.levelPadding,checkable:e.checkable},model:{value:e.copyData[i],callback:function(t){e.$set(e.copyData,i,t)},expression:"copyData[index]"}})}),1)},staticRenderFns:[],name:"td-tree-plus",props:{data:{type:Array,required:!0},lazy:{type:Boolean,default:!1},load:{type:Function},levelPadding:{type:Number,default:16},checkable:{type:Boolean,default:!1},disableFunction:{type:Function,default:function(){return!1}},disableClass:{type:String,default:"disabled-node"},bindingKeyGenerator:{type:Function,default:function(e){return(e.__level||"")+" "+Math.random()+" "+e.title}},defaultPlaceholder:{type:String,default:""},nodeInsertionIconManifest:{type:Object,default:function(){return{visible:!0,icon:"https://backstage-img-ssl.a.88cdn.com/019fc2a136a2881181e73fea74a4836efc02195d"}}}},provide:function(){return{root:this}},data:function(){return{copyData:[],chosenNode:null}},created:function(){try{this.copyData=JSON.parse(Te()(this.data))}catch(e){this.copyData=[]}for(var e=0;e<this.data.length;e++)this.autoGeneratedKey(this.copyData[e],null)},methods:{handleChangeNode:function(e,t){this.$set(this.copyData,t,e)},autoGeneratedKey:function(e,t){e.__level=t?t.__level+1:1;var n=e.children;if(n&&n.length)for(var i=0;i<n.length;i++)this.autoGeneratedKey(n[i],e)}},watch:{chosenNode:function(e){this.$emit("changeChosenNode",e)}}},Wt={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-tree-node is-expanded",class:it()({},e.root.disableClass,e.isDisabledNode)},[n("div",{staticClass:"td-tree-node__content",class:{"is-leaf":e.node.leaf,"is-chosen":e.isChosen},style:{paddingLeft:(e.node.__level-1)*e.levelPadding+"px"},on:{click:e.handleClickNodeContent}},[n("span",{staticClass:"td-tree-node__expand-icon td-icon-arrow-drop",class:[{"is-expanded":e.node.expand}],on:{click:e.handelClickExpandIcon}}),e._v(" "),n("label",{directives:[{name:"show",rawName:"v-show",value:e.checkable&&!e.isInputLabel,expression:"checkable && !isInputLabel"}],staticClass:"td-checkbox",class:{"is-indeterminate":e.indeterminate}},[n("input",{staticClass:"td-checkbox__inner",attrs:{type:"checkbox"},domProps:{checked:!!e.node.checked},on:{click:e.handleClickCheckbox}})]),e._v(" "),e.lazy&&e.loading?n("i",{staticClass:"td-icon-loading"}):n("span",{directives:[{name:"show",rawName:"v-show",value:!e.isInputLabel||e.inputLabelIconVisible,expression:"!isInputLabel || inputLabelIconVisible"}],staticClass:"td-tree-node__image-icon",class:[e.node.iconClass],style:e.iconStyle}),e._v(" "),n("span",{staticClass:"td-tree-node__label is-checked"},[e.isInputLabel?[n("label",{staticClass:"td-input"},[n("span",{staticClass:"td-input__label"}),e._v(" "),n("input",{ref:"createInput",staticClass:"td-input__inner",attrs:{type:"text",placeholder:"请输入文字"},domProps:{value:e.root.defaultPlaceholder},on:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.preventDefault(),e.handleConfirmInputCreate(t))},blur:e.handleConfirmInputCreate}})])]:[e._v(" "+e._s(e.node.title)+" ")]],2)]),e._v(" "),n("div",{staticClass:"td-tree-node__children"},e._l(e.node.children,function(t,i){return n("td-tree-node-plus",{directives:[{name:"show",rawName:"v-show",value:e.node.expand,expression:"node.expand"}],key:e.root.bindingKeyGenerator(t),attrs:{node:t,load:e.load,lazy:e.lazy,levelPadding:e.levelPadding,isInputLabel:!!t.isInputLabel,checkable:e.checkable},on:{createInputFinish:e.handleChildConfirmInputCreate},model:{value:e.node.children[i],callback:function(t){e.$set(e.node.children,i,t)},expression:"node.children[index]"}})}),1)])},staticRenderFns:[],name:"td-tree-node-plus",model:{prop:"node",event:"change"},props:{node:{type:Object,required:!0},lazy:{type:Boolean,default:!1},load:{type:Function},levelPadding:{type:Number,default:16},isInputLabel:{type:Boolean,default:!1},checkable:{type:Boolean}},data:function(){return{indeterminate:!1,loading:!1,innerLazy:!1,mode:""}},inject:["root"],watch:{mode:function(e,t){"create"===e&&this.handleInCreateMode()},"node.children.length":{handler:function(e){this.node.leaf=!this.node.children.length,this.node.children.sort(function(e,t){return e.__order-t.__order})}},"node.children":{deep:!0,immediate:!0,handler:function(){if(this.$children&&this.$children.length){for(var e=!0,t=!0,n=!1,i=0;i<this.$children.length;i++)this.$children[i].isInputLabel||(this.$children[i].indeterminate&&(n=!0),this.$children[i].node.checked?t=!1:e=!1);var r=n||!e&&!t;if(r){if(!e)for(var o=this;o&&"td-tree-node-plus"===o.$options._componentTag;)o.indeterminate=r,o=o.$parent}else for(var s=this;s&&"td-tree-node-plus"===s.$options._componentTag;){var a=s.$children.every(function(e){return e.node.checked}),l=s.$children.some(function(e){return e.indeterminate||e.node.checked});s.indeterminate=!a&&l,s=s.$parent}var c=e;c!==!!this.node.checked&&(this.$set(this.node,"checked",c),this.$emit("change",this.node))}}}},methods:{handleClickNodeContent:function(e){this.isDisabledNode?e.preventDefault():this.root.chosenNode=this},updateChildren:function(e){for(var t=0;t<this.$children.length;t++)this.$children[t].updateChildren(e),this.$set(this.$children[t].node,"checked",e)},handleChildConfirmInputCreate:function(e){if(this.node.children&&this.node.children.length){var t=this.node.children.findIndex(function(t){return t===e});-1!==t&&this.node.children.splice(t,1)}this.mode=""},handleClickCheckbox:function(){this.indeterminate?(this.indeterminate=!1,this.updateChildren(!0),this.$set(this.node,"checked",!0)):(this.updateChildren(!this.node.checked),this.$set(this.node,"checked",!this.node.checked)),this.$emit("change",this.node)},handleConfirmInputCreate:Object(Q.b)(Rt()(kt.a.mark(function e(){var t,n;return kt.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=this.$refs.createInput)){e.next=10;break}if(n=t.value,e.t0=this.$parent.innerLazy,!e.t0){e.next=7;break}return e.next=7,this.$parent.loadData();case 7:this.root.$emit("create",{chosenNode:this.$parent,content:n}),this.$emit("createInputFinish",this.node),this.$parent.mode="";case 10:case"end":return e.stop()}},e,this)})),10,void 0),handleInCreateMode:function(){var e={__level:this.node.__level+1,isInputLabel:!0,leaf:!0,checked:this.node.checked,__order:this.node.children?-this.node.children.length:0};this.node.children?this.node.children.push(e):this.$set(this.node,"children",[e]),this.$set(this.node,"expand",!0)},loadData:function(){var e=this;return Rt()(kt.a.mark(function t(){var n,i;return kt.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,t.next=4,e.load(e.node);case 4:if((n=t.sent)&&n.length){for(i=0;i<n.length;i++)e.root.autoGeneratedKey(n[i],e.node);e.$set(e.node,"children",n),e.innerLazy=!1}t.next=11;break;case 8:t.prev=8,t.t0=t.catch(0),console.error(t.t0);case 11:return t.prev=11,e.loading=!1,t.finish(11);case 14:case"end":return t.stop()}},t,e,[[0,8,11,14]])}))()},handelClickExpandIcon:function(){var e=this;return Rt()(kt.a.mark(function t(){var n;return kt.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.isDisabledNode){t.next=2;break}return t.abrupt("return");case 2:if(!(n=!e.node.expand)||!e.innerLazy||e.node.children&&e.node.children.length){t.next=6;break}return t.next=6,e.loadData();case 6:e.$set(e.node,"expand",n),e.$emit("change",e.node);case 8:case"end":return t.stop()}},t,e)}))()}},computed:{inputLabelIconVisible:function(){return this.root.nodeInsertionIconManifest.visible},iconStyle:function(){var e=Ot()(null);if(this.node.icon&&(e["background-image"]="url("+this.node.icon+")"),this.isInputLabel){var t=this.root.nodeInsertionIconManifest,n=t.visible,i=t.icon;n&&(e["background-image"]="url("+i+")")}return e},isDisabledNode:function(){return this.root.disableFunction(this.node)},isChosen:function(){return this.root.chosenNode===this}},mounted:function(){var e=!!this.node.expand;this.$set(this.node,"expand",!0),this.$set(this.node,"expand",e),this.innerLazy=this.lazy,this.$refs.createInput&&this.$refs.createInput.focus(),this.$refs.createInput&&this.$refs.createInput.select()}};It.install=function(e){e.component(It.name,It),e.component(Wt.name,Wt)};var Lt=It;var Dt=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},Nt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},At="object"==typeof Nt&&Nt&&Nt.Object===Object&&Nt,Ft="object"==typeof self&&self&&self.Object===Object&&self,Bt=At||Ft||Function("return this")(),Vt=function(){return Bt.Date.now()},jt=Bt.Symbol,$t=Object.prototype,Ht=$t.hasOwnProperty,Ut=$t.toString,Gt=jt?jt.toStringTag:void 0;var Kt=function(e){var t=Ht.call(e,Gt),n=e[Gt];try{e[Gt]=void 0;var i=!0}catch(e){}var r=Ut.call(e);return i&&(t?e[Gt]=n:delete e[Gt]),r},Xt=Object.prototype.toString;var zt=function(e){return Xt.call(e)},Yt="[object Null]",Zt="[object Undefined]",qt=jt?jt.toStringTag:void 0;var Jt=function(e){return null==e?void 0===e?Zt:Yt:qt&&qt in Object(e)?Kt(e):zt(e)};var Qt=function(e){return null!=e&&"object"==typeof e},en="[object Symbol]";var tn=function(e){return"symbol"==typeof e||Qt(e)&&Jt(e)==en},nn=NaN,rn=/^\s+|\s+$/g,on=/^[-+]0x[0-9a-f]+$/i,sn=/^0b[01]+$/i,an=/^0o[0-7]+$/i,ln=parseInt;var cn=function(e){if("number"==typeof e)return e;if(tn(e))return nn;if(Dt(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Dt(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(rn,"");var n=sn.test(e);return n||an.test(e)?ln(e.slice(2),n?2:8):on.test(e)?nn:+e},un="Expected a function",dn=Math.max,hn=Math.min;var fn=function(e,t,n){var i,r,o,s,a,l,c=0,u=!1,d=!1,h=!0;if("function"!=typeof e)throw new TypeError(un);function f(t){var n=i,o=r;return i=r=void 0,c=t,s=e.apply(o,n)}function p(e){var n=e-l;return void 0===l||n>=t||n<0||d&&e-c>=o}function m(){var e=Vt();if(p(e))return v(e);a=setTimeout(m,function(e){var n=t-(e-l);return d?hn(n,o-(e-c)):n}(e))}function v(e){return a=void 0,h&&i?f(e):(i=r=void 0,s)}function g(){var e=Vt(),n=p(e);if(i=arguments,r=this,l=e,n){if(void 0===a)return function(e){return c=e,a=setTimeout(m,t),u?f(e):s}(l);if(d)return a=setTimeout(m,t),f(l)}return void 0===a&&(a=setTimeout(m,t)),s}return t=cn(t)||0,Dt(n)&&(u=!!n.leading,o=(d="maxWait"in n)?dn(cn(n.maxWait)||0,t):o,h="trailing"in n?!!n.trailing:h),g.cancel=function(){void 0!==a&&clearTimeout(a),c=0,i=l=r=a=void 0},g.flush=function(){return void 0===a?s:v(Vt())},g},pn="Expected a function";var mn={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"show",rawName:"v-show",value:e.visible,expression:"visible"}],staticClass:"td-viewer",on:{mousewheel:e.onMouseWheel}},[n("div",{staticClass:"td-viewer__header"},[n("div",{staticClass:"td-viewer__operate"},[e.canFullscreen?[n("a",{attrs:{href:"javascript:;",title:"最小化"},on:{click:function(t){return e.$emit("minimize")}}},[n("i",{staticClass:"td-icon-minimize"})]),e._v(" "),e.fullscreen?n("a",{attrs:{href:"javascript:;"}},[n("i",{staticClass:"td-icon-restore",attrs:{title:"还原"},on:{click:function(t){return e.$emit("restore")}}})]):n("a",{attrs:{href:"javascript:;",title:"最大化"},on:{click:function(t){return e.$emit("fullscreen")}}},[n("i",{staticClass:"td-icon-maximize"})])]:e._e(),e._v(" "),e._m(0)],2)]),e._v(" "),n("div",{ref:"container",staticClass:"td-viewer__body"},[n("img",{directives:[{name:"show",rawName:"v-show",value:!e.isLoading,expression:"!isLoading"}],ref:"image",class:{"is-origin-ratio":e.isShowingActualRatio},style:e.imageStyle,attrs:{draggable:"true",alt:"",src:e.currentShowingImg},on:{load:e.onImageLoaded,dragstart:e.onDragStart,dragover:e.onDragOver}}),e._v(" "),e.isSrcPropArray?[n("span",{staticClass:"td-viewer__button td-viewer__button--left",class:{"is-disabled":e.isArrowDisabled.left},on:{click:function(t){return e.changeImage(-1)}}},[n("i",{staticClass:"td-icon-arrow-left"})]),e._v(" "),n("span",{staticClass:"td-viewer__button td-viewer__button--right",class:{"is-disabled":e.isArrowDisabled.right},on:{click:function(t){return e.changeImage(1)}}},[n("i",{staticClass:"td-icon-arrow-right"})])]:e._e(),e._v(" "),e.isLoading?n("div",{staticClass:"td-viewer__loading"},[n("i",{staticClass:"td-icon-loading"}),e._v(" "),n("p",[e._v("图片加载中...")])]):e._e()],2),e._v(" "),n("div",{staticClass:"td-viewer__footer"},[n("a",{attrs:{href:"javacript:;",title:e.buttonText.zoomIn},on:{click:e.zoomIn}},[n("i",{staticClass:"td-icon-magnify"})]),e._v(" "),n("a",{attrs:{href:"javacript:;",title:e.buttonText.zoomOut},on:{click:e.zoomOut}},[n("i",{staticClass:"td-icon-minify"})]),e._v(" "),e.isShowingActualRatio?n("a",{attrs:{href:"javacript:;",title:e.buttonText.ratioOptimal},on:{click:e.showOptimalRatio}},[n("i",{staticClass:"td-icon-ratio-optimal"})]):n("a",{attrs:{href:"javacript:;",title:e.buttonText.ratioOptimal},on:{click:e.showActualRatio}},[n("i",{staticClass:"td-icon-ratio"})]),e._v(" "),n("a",{attrs:{href:"javacript:;",title:e.buttonText.rotate},on:{click:e.rotate}},[n("i",{staticClass:"td-icon-refresh"})]),e._v(" "),n("a",{attrs:{href:"javascript:;",title:e.buttonText.download},on:{click:function(t){return e.download(e.currentShowingImg)}}},[n("i",{staticClass:"td-icon-download"})])])])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("a",{attrs:{href:"javascript:;"}},[t("i",{staticClass:"td-icon-close",attrs:{title:"关闭"}})])}],_scopeId:"data-v-59204da6",name:"td-viewer",props:{src:{type:[String,Array],require:!0},maxScaleRatio:{type:Number,default:5},minScaleRatio:{type:Number,default:.107374},visible:{type:Boolean,default:!0},fullscreen:{type:Boolean,default:!1},canFullscreen:{type:Boolean,default:!0},customButtonText:{type:Object,default:function(){return{}}},useBrowserDownload:{type:Boolean,default:!1}},data:function(){return{isLoading:!0,isShowingActualRatio:!1,scaleRatio:1,rotateDegree:0,index:0,dragPosition:{x:0,y:0},deltaPostion:{x:0,y:0},transparentImage:null,buttonText:{zoomIn:"放大",zoomOut:"缩小",ratioOptimal:"最佳比例",ratioOrigin:"实际大小",rotate:"旋转",download:"取回"}}},computed:{isSrcPropArray:function(){return Array.isArray(this.src)},imgCount:function(){return this.isSrcPropArray?this.src.length:1},currentShowingImg:function(){return this.isSrcPropArray?this.src[this.index]:this.src},isArrowDisabled:function(){return{left:0===this.index,right:this.index===this.imgCount-1}},imageStyle:function(){return"transform: "+(this.isShowingActualRatio?"translate(-50%, -50%)":"")+" "+("scale("+this.scaleRatio+")")+" "+("rotate("+this.rotateDegree+"deg)")+" "+("translate("+this.deltaPostion.x+"px, "+this.deltaPostion.y+"px)")+";"}},mounted:function(){var e=new Image;e.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",this.transparentImage=e,this.buttonText=U()({},this.buttonText,this.customButtonText)},watch:{currentShowingImg:{immediate:!0,handler:function(){this.isLoading=!0}}},methods:{showActualRatio:function(){this.isShowingActualRatio=!0,this.scaleRatio=1,this.resetDragPosition()},showOptimalRatio:function(){this.isShowingActualRatio=!1,this.scaleRatio=1,this.resetDragPosition()},zoomIn:function(){var e=this.scaleRatio;(e=e>=1?e+.2:1.25*e)<=this.maxScaleRatio&&(this.scaleRatio=e)},zoomOut:function(){var e=this.scaleRatio;(e=e>=1.2?e-.2:.8*e)>=this.minScaleRatio&&(this.scaleRatio=e)},changeImage:function(e){var t=this.index+e;t>=0&&t<this.imgCount&&(this.index=t,this.reset())},rotate:function(){this.rotateDegree=(this.rotateDegree+90)%360},onMouseWheel:function(e){e.preventDefault(),e.deltaY>0?this.zoomOut():this.zoomIn()},reset:function(){this.isLoading=!0,this.scaleRatio=1,this.rotateDegree=0,this.resetDragPosition()},download:function(e){if(this.$emit("download",e),this.useBrowserDownload){var t=new Image;t.setAttribute("crossOrigin","anonymous"),t.onload=function(){var e=document.createElement("canvas");e.width=t.width,e.height=t.height,e.getContext("2d").drawImage(t,0,0,t.width,t.height);var n=e.toDataURL("image/png"),i=document.createElement("a"),r=new MouseEvent("click");i.download="图片",i.href=n,i.dispatchEvent(r)},t.src=e+"?time="+(new Date).valueOf()}},onImageLoaded:function(){this.isLoading=!1},onDragStart:function(e){e.dataTransfer.setDragImage(this.transparentImage,10,10),this.dragPosition.x=e.clientX,this.dragPosition.y=e.clientY},onDragOver:function(e,t,n){var i=!0,r=!0;if("function"!=typeof e)throw new TypeError(pn);return Dt(n)&&(i="leading"in n?!!n.leading:i,r="trailing"in n?!!n.trailing:r),fn(e,t,{leading:i,maxWait:t,trailing:r})}(function(e){this.deltaPostion.x=this.deltaPostion.x+(e.clientX-this.dragPosition.x),this.deltaPostion.y=this.deltaPostion.y+(e.clientY-this.dragPosition.y),this.dragPosition.x=e.clientX,this.dragPosition.y=e.clientY},10),onClose:function(){this.visible=!1},resetDragPosition:function(){this.deltaPostion={x:0,y:0},this.dragPosition={x:0,y:0}}},install:function(e){e.component(mn.name,mn)}},vn=mn,gn={install:function(e,t){e.use(I),e.use(oe),e.use(ie),e.use(se.default),e.use(St.default),e.use(ae.default),e.use(ue),e.use(me),e.use(ve.default),e.use(_e),e.use(ge.default),e.use(we),e.use(xe),e.use(Ae),e.use(Be),e.use(je),e.use(He),e.use(Ge),e.use(Xe),e.use(Ye),e.use(qe),e.use(ut),e.use(ht),e.use(pt),e.use(lt),e.use(mt.default),e.use(yt),e.use(bt),e.use(Le),e.use(wt),e.use(Ct.default),e.use(Et),e.use(Lt),e.use(vn)}};"undefined"!=typeof window&&window.Vue&&window.Vue.use(gn);t.default=gn},function(e,t){},,,,,function(e,t){},,function(e,t){},,function(e,t){},,,,function(e,t){},,function(e,t){}]);