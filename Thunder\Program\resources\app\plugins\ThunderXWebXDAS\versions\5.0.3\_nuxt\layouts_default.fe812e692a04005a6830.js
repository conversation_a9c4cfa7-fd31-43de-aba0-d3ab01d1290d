webpackJsonp([1],{"9YJX":function(e,t,n){"use strict";t.a=function(){document.addEventListener("drop",function(e){e.preventDefault();var t=e.dataTransfer,n=t.files,r=t.items;if(void 0!==r&&null!==r&&r.length>0)for(var i=0;i<r.length;i++){var a=r[i];"string"===a.kind&&"text/uri-list"===a.type&&a.getAsString(function(e){Object(o.b)("DropOpenUrl",e)})}if(void 0!==n&&null!==n&&n.length>0)for(var s=0;s<n.length;s++){var c=n[s],d=c.path;void 0!==d&&null!==d&&""!==d&&Object(o.b)("DropOpenFile",d)}},!1)};var o=n("/8Fg")},Ma2J:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n("jrvh"),r=n("STrE"),i=n("VU/8")(o.a,r.a,!1,null,null,null);i.options.__file="layouts\\default.vue",t.default=i.exports},STrE:function(e,t,n){"use strict";var o=function(){var e=this.$createElement,t=this._self._c||e;return t("div",[t("nuxt")],1)};o._withStripped=!0;var r={render:o,staticRenderFns:[]};t.a=r},jrvh:function(e,t,n){"use strict";var o=n("Xxa5"),r=n.n(o),i=n("exGp"),a=n.n(i),s=n("9YJX"),c=n("uw3b"),d=n("Cb+C"),u=n("Rhsk");document.addEventListener("dragstart",function(e){e.preventDefault()},!1),document.addEventListener("dragover",function(e){e.preventDefault()},!1),document.addEventListener("drop",function(e){e.preventDefault()},!1),Object(s.a)(),t.a={data:function(){return{eventIdOnChangeSkin:void 0,eventIdOnChangeSpeedCurveSkin:void 0,eventIdOnVipTaskInfoChanged:void 0,eventIdOnConfigValueChanaged:void 0}},computed:{isDefaultSkin:function(){var e=this.$sget(this.$store.state.skin,"skinInfo"),t=!1;return 0===this.$sget(e,"type")?(t=!0,document.body.classList.remove("is-theme")):document.body.classList.add("is-theme"),t},bannerRecommendInfo:function(){return this.$sget(this.$store.state.home.configModules,"thunderxVersionFilter","banner")||{filter:[],switch:"off"}},thunderVersion:function(){return this.$store.state.thunderVersion},isSpeedCurveDefaultSkin:function(){var e=this.$sget(this.$store.state.skin,"speedCurveSkinInfo"),t=!1;return 1!==this.$sget(e,"id")&&Object(u.B)(this.$sget(e,"icon_link"))?document.body.classList.add("is-theme-speed"):(t=!0,document.body.classList.remove("is-theme-speed")),t}},created:function(){var e=a()(r.a.mark(function e(){var t,n,o=this;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c.a.getUserInfo();case 2:t=e.sent,n=t.thunderVersion,this.$store.commit("setThunderVersion",n),this.$store.dispatch("home/getConfigModules").then(function(){o.$store.dispatch("reviews/getHomeBanner",o.thunderVersion),o.$store.dispatch("reviews/getSuperCinemaConfig"),o.$store.dispatch("reviews/getZuiyouAdConfig")}),this.$store.dispatch("comments/getQuickReview"),this.initFetchConfig(),this.$store.dispatch("skin/getSkinInfo").then(function(e){o.setCssVariables(e)}),this.$store.dispatch("skin/getSpeedCurveSkinInfo").then(function(e){o.setSpeedCurveCssVariables(e)}),this.$store.dispatch("skin/getDefaultSpeedSkinInfo");case 11:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}(),mounted:function(){var e=this;this.eventIdOnChangeSkin=d.a.api.attach("OnChangeSkin",function(t){e.$store.commit("skin/setSkinInfo",t),e.setCssVariables(t)}),this.eventIdOnChangeSpeedCurveSkin=d.a.api.attach("OnChangeSpeedCurveSkin",function(t){e.$store.commit("skin/setSpeedCurveSkinInfo",t),e.setSpeedCurveCssVariables(t);var n=e.$sget(e.$store.state.tasks,"curTaskID");if(n){var o=e.$sget(e.$store.state.tasks.all,n,"VipTaskInfo"),r=o.taskType,i=o.taskStatus;1===r&&9===i&&e.$store.commit("skin/setUserSetSpeedSkin",!0)}}),this.eventIdOnVipTaskInfoChanged=d.a.api.attach("OnVipTaskInfoChanged",function(t,n){try{n=JSON.parse(n)}catch(e){n={}}e.$store.commit("tasks/setVipTaskInfo",{taskID:t,VipTaskInfo:n})})},beforeDestroy:function(){d.a.api.detach("OnChangeSkin",this.eventIdOnChangeSkin),d.a.api.detach("OnChangeSpeedCurveSkin",this.eventIdOnChangeSpeedCurveSkin),d.a.api.detach("OnVipTaskInfoChanged",this.eventIdOnVipTaskInfoChanged),d.a.api.detach("OnConfigValueChanaged",this.eventIdOnConfigValueChanaged)},methods:{initFetchConfig:function(){var e=this;this.eventIdOnConfigValueChanaged=d.a.api.attach("OnConfigValueChanaged",function(t,n,o,r){t in e.$store.state.config&&n in e.$store.state.config[t]&&o!==r&&e.$store.commit("config/setConfigValue",{section:t,key:n,value:r})}),this.$store.dispatch("config/getConfigValue",{section:"GenericSettings",key:"AnimationLevel",defaultValue:"1"})},setCssVariables:function(e){if(void 0!==this.$sget(e,"colorID"))if(this.isDefaultSkin)Object(u.v)(document.body,{"--color-primary-theme":"","--color-primary-control-1":"","--color-primary-control-2":"","--color-primary-control-3":"","--color-primary-control-4":"","--color-primary-gradient-1":"","--color-primary-gradient-2":"","--color-primary-text":"","--color-search":"","--color-secondary":"","--color-gradient-background-1":"","--color-gradient-background-2":"","--color-gradient-foreground-1":"","--color-gradient-foreground-2":"","--color-accordion-1":"","--color-accordion-2":"","--default-opacity-1":"","--default-opacity-2":""});else{var t=e.colors,n=t.colorPrimaryControl1,o=t.colorGradientBackground,r=t.colorPrimaryText,i=t.colorSecondary,a=t.colorPrimary,s=t.colorPrimaryGradient,c=t.colorPrimaryControl2,d=t.colorPrimaryControl3,l=t.colorPrimaryControl4,h=t.colorSearch,f=t.colorGradientForeground,p=t.colorAccordion,g=e.opacity,v=void 0===g?.35:g;Object(u.v)(document.body,{"--color-primary-theme":""+a,"--color-primary-control-1":""+n,"--color-primary-control-2":""+c,"--color-primary-control-3":""+d,"--color-primary-control-4":""+l,"--color-primary-gradient-1":""+s[0],"--color-primary-gradient-2":""+s[1],"--color-primary-text":""+r,"--color-search":""+h,"--color-secondary":""+i,"--color-gradient-background-1":""+o[0],"--color-gradient-background-2":""+o[1],"--color-gradient-foreground-1":""+f[0],"--color-gradient-foreground-2":""+f[1],"--color-accordion-1":""+p[0],"--color-accordion-2":""+p[1],"--default-opacity-1":""+(v-.1),"--default-opacity-2":""+(v+.1)})}},setSpeedCurveCssVariables:function(e){if(this.isSpeedCurveDefaultSkin)Object(u.v)(document.body,{"--chart-bg":""});else{var t=this.$sget(e,"icon_link");Object(u.v)(document.body,{"--chart-bg":"url('"+t+"')"})}}}}}});