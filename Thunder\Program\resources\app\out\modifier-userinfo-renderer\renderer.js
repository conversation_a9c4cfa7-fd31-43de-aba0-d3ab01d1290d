module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=968)}({0:function(e,t,n){"use strict";function r(e,t,n,r,i,o,s,a){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),o&&(c._scopeId="data-v-"+o),s?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},c._ssrRegister=l):i&&(l=a?function(){i.call(this,this.$root.$options.shadowRoot)}:i),l)if(c.functional){c._injectStyles=l;var u=c.render;c.render=function(e,t){return l.call(t),u(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}n.d(t,"a",function(){return r})},1:function(e,t,n){e.exports=n(9)(137)},10:function(e,t,n){"use strict";var r=n(66),i=n(116),o=Object.prototype.toString;function s(e){return"[object Array]"===o.call(e)}function a(e){return null!==e&&"object"==typeof e}function l(e){return"[object Function]"===o.call(e)}function c(e,t){if(null!==e&&void 0!==e)if("object"!=typeof e&&(e=[e]),s(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:s,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:i,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:l,isStream:function(e){return a(e)&&l(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:c,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,i=arguments.length;r<i;r++)c(arguments[r],n);return t},extend:function(e,t,n){return c(t,function(t,i){e[i]=n&&"function"==typeof t?r(t,n):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},11:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(8),s=n(1).default.getLogger("XLStat");let a=o.default(i.join(__rootDir,"../bin/ThunderHelper.node"));function l(e=""){let t;if("string"==typeof e)t=e;else if(c(e)&&"object"==typeof e){let n=[];for(let t in e)c(e[t])&&n.push(t+"="+encodeURIComponent(e[t]));t=n.join(",")}return t}function c(e){return void 0!==e&&null!==e}!function(e){let t=null;function n(){return t||(t=a.xlstat4),t}function i(e,t="",i="",o=0,a=0,c=0,u=0,d="",f=0){return r(this,void 0,void 0,function*(){let r=0;do{if(void 0===e){r=1;break}let h=l(d);r="browser"===process.type?yield new Promise(s=>{r=n().asyncTrackEvent(e,t,i,o,a,c,u,h,f,e=>{s(e)})}):n().trackEvent(e,t,i,o,a,c,u,h,f),s.information(e,t,i,o,a,c,u,h,f)}while(0);return r})}function o(e,t=0){do{if(void 0===e)break;"browser"!==process.type&&n().trackClick(e,t)}while(0)}e.asyncTrackEvent=i,e.trackEvent=function(e,t="",n="",r=0,o=0,s=0,a=0,l="",c=0){i(e,t,n,r,o,s,a,l,c).catch()},e.trackEventEx=function(e,t="",n="",r=0){i(e,t,"",0,0,0,0,n,r).catch()},e.trackClick=o,e.trackShow=function(e,t=0){o(e,t)},e.setUserID=function(e=0,t=0){"browser"!==process.type&&n().setUserID(e,t)},e.initParam=function(e){return r(this,void 0,void 0,function*(){let t=-1;return t="browser"===process.type?yield new Promise(t=>{n().asyncInitParam(e,(e,n)=>{t(e?n:-1)})}):yield new Promise(t=>{n().initParamRemote(e,e=>{t(e)})})})},e.asyncUninit=function(e){return r(this,void 0,void 0,function*(){"browser"===process.type&&(yield new Promise(t=>{n().asyncUninit(e,()=>{t()})}))})},e.uninit=function(){"browser"===process.type&&n().waitFinish()}}(t.XLStatNS||(t.XLStatNS={}))},115:function(e,t,n){"use strict";var r=n(10),i=n(66),o=n(117),s=n(45);function a(e){var t=new o(e),n=i(o.prototype.request,t);return r.extend(n,o.prototype,t),r.extend(n,t),n}var l=a(s);l.Axios=o,l.create=function(e){return a(r.merge(s,e))},l.Cancel=n(72),l.CancelToken=n(133),l.isCancel=n(71),l.all=function(e){return Promise.all(e)},l.spread=n(134),e.exports=l,e.exports.default=l},116:function(e,t){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},117:function(e,t,n){"use strict";var r=n(45),i=n(10),o=n(128),s=n(129);function a(e){this.defaults=e,this.interceptors={request:new o,response:new o}}a.prototype.request=function(e){"string"==typeof e&&(e=i.merge({url:arguments[0]},arguments[1])),(e=i.merge(r,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var t=[s,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},i.forEach(["delete","get","head","options"],function(e){a.prototype[e]=function(t,n){return this.request(i.merge(n||{},{method:e,url:t}))}}),i.forEach(["post","put","patch"],function(e){a.prototype[e]=function(t,n,r){return this.request(i.merge(r||{},{method:e,url:t,data:n}))}}),e.exports=a},118:function(e,t,n){"use strict";var r=n(10);e.exports=function(e,t){r.forEach(e,function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])})}},119:function(e,t,n){"use strict";var r=n(10),i=n(67),o=n(69),s=n(120),a=n(121),l=n(46),c="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(122);e.exports=function(e){return new Promise(function(t,u){var d=e.data,f=e.headers;r.isFormData(d)&&delete f["Content-Type"];var h=new XMLHttpRequest,p="onreadystatechange",m=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in h||a(e.url)||(h=new window.XDomainRequest,p="onload",m=!0,h.onprogress=function(){},h.ontimeout=function(){}),e.auth){var g=e.auth.username||"",v=e.auth.password||"";f.Authorization="Basic "+c(g+":"+v)}if(h.open(e.method.toUpperCase(),o(e.url,e.params,e.paramsSerializer),!0),h.timeout=e.timeout,h[p]=function(){if(h&&(4===h.readyState||m)&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in h?s(h.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?h.response:h.responseText,status:1223===h.status?204:h.status,statusText:1223===h.status?"No Content":h.statusText,headers:n,config:e,request:h};i(t,u,r),h=null}},h.onerror=function(){u(l("Network Error",e,null,h)),h=null},h.ontimeout=function(){u(l("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",h)),h=null},r.isStandardBrowserEnv()){var _=n(123),y=(e.withCredentials||a(e.url))&&e.xsrfCookieName?_.read(e.xsrfCookieName):void 0;y&&(f[e.xsrfHeaderName]=y)}if("setRequestHeader"in h&&r.forEach(f,function(e,t){void 0===d&&"content-type"===t.toLowerCase()?delete f[t]:h.setRequestHeader(t,e)}),e.withCredentials&&(h.withCredentials=!0),e.responseType)try{h.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&h.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&h.upload&&h.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){h&&(h.abort(),u(e),h=null)}),void 0===d&&(d=null),h.send(d)})}},12:function(e,t){e.exports=require("events")},120:function(e,t,n){"use strict";var r=n(10),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,o,s={};return e?(r.forEach(e.split("\n"),function(e){if(o=e.indexOf(":"),t=r.trim(e.substr(0,o)).toLowerCase(),n=r.trim(e.substr(o+1)),t){if(s[t]&&i.indexOf(t)>=0)return;s[t]="set-cookie"===t?(s[t]?s[t]:[]).concat([n]):s[t]?s[t]+", "+n:n}}),s):s}},121:function(e,t,n){"use strict";var r=n(10);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function i(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=i(window.location.href),function(t){var n=r.isString(t)?i(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},122:function(e,t,n){"use strict";var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function i(){this.message="String contains an invalid character"}i.prototype=new Error,i.prototype.code=5,i.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,n,o=String(e),s="",a=0,l=r;o.charAt(0|a)||(l="=",a%1);s+=l.charAt(63&t>>8-a%1*8)){if((n=o.charCodeAt(a+=.75))>255)throw new i;t=t<<8|n}return s}},123:function(e,t,n){"use strict";var r=n(10);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,i,o,s){var a=[];a.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),r.isString(i)&&a.push("path="+i),r.isString(o)&&a.push("domain="+o),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},124:function(e,t,n){function r(){var e;try{e=t.storage.debug}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e}(t=e.exports=n(125)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n=this.useColors;if(e[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+e[0]+(n?"%c ":" ")+"+"+t.humanize(this.diff),!n)return;var r="color: "+this.color;e.splice(1,0,r,"color: inherit");var i=0,o=0;e[0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&"%c"===e&&(o=++i)}),e.splice(o,0,r)},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=r,t.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(r())},125:function(e,t,n){function r(e){var n;function r(){if(r.enabled){var e=r,i=+new Date,o=i-(n||i);e.diff=o,e.prev=n,e.curr=i,n=i;for(var s=new Array(arguments.length),a=0;a<s.length;a++)s[a]=arguments[a];s[0]=t.coerce(s[0]),"string"!=typeof s[0]&&s.unshift("%O");var l=0;s[0]=s[0].replace(/%([a-zA-Z%])/g,function(n,r){if("%%"===n)return n;l++;var i=t.formatters[r];if("function"==typeof i){var o=s[l];n=i.call(e,o),s.splice(l,1),l--}return n}),t.formatArgs.call(e,s),(r.log||t.log||console.log.bind(console)).apply(e,s)}}return r.namespace=e,r.enabled=t.enabled(e),r.useColors=t.useColors(),r.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),r.destroy=i,"function"==typeof t.init&&t.init(r),t.instances.push(r),r}function i(){var e=t.instances.indexOf(this);return-1!==e&&(t.instances.splice(e,1),!0)}(t=e.exports=r.debug=r.default=r).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){var n;t.save(e),t.names=[],t.skips=[];var r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(n=0;n<i;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")));for(n=0;n<t.instances.length;n++){var o=t.instances[n];o.enabled=t.enabled(o.namespace)}},t.enabled=function(e){if("*"===e[e.length-1])return!0;var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(126),t.instances=[],t.names=[],t.skips=[],t.formatters={}},126:function(e,t){var n=1e3,r=60*n,i=60*r,o=24*i,s=365.25*o;function a(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var l,c=typeof e;if("string"===c&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var a=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return a*s;case"days":case"day":case"d":return a*o;case"hours":case"hour":case"hrs":case"hr":case"h":return a*i;case"minutes":case"minute":case"mins":case"min":case"m":return a*r;case"seconds":case"second":case"secs":case"sec":case"s":return a*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}(e);if("number"===c&&!1===isNaN(e))return t.long?a(l=e,o,"day")||a(l,i,"hour")||a(l,r,"minute")||a(l,n,"second")||l+" ms":function(e){if(e>=o)return Math.round(e/o)+"d";if(e>=i)return Math.round(e/i)+"h";if(e>=r)return Math.round(e/r)+"m";if(e>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},127:function(e){e.exports={_args:[["axios@0.18.0","D:\\jenkins\\workspace\\ThunderPC_AP_Build_Pack\\channel\\trunk\\build\\app"]],_from:"axios@0.18.0",_id:"axios@0.18.0",_inBundle:!1,_integrity:"sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=",_location:"/axios",_phantomChildren:{},_requested:{type:"version",registry:!0,raw:"axios@0.18.0",name:"axios",escapedName:"axios",rawSpec:"0.18.0",saveSpec:null,fetchSpec:"0.18.0"},_requiredBy:["/","/@types/axios","/@xunlei/thunderx-login-main"],_resolved:"http://xnpm.repo.xunlei.cn/axios/-/axios-0.18.0.tgz",_spec:"0.18.0",_where:"D:\\jenkins\\workspace\\ThunderPC_AP_Build_Pack\\channel\\trunk\\build\\app",author:{name:"Matt Zabriskie"},browser:{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},bugs:{url:"https://github.com/axios/axios/issues"},bundlesize:[{path:"./dist/axios.min.js",threshold:"5kB"}],dependencies:{"follow-redirects":"^1.3.0","is-buffer":"^1.1.5"},description:"Promise based HTTP client for the browser and node.js",devDependencies:{bundlesize:"^0.5.7",coveralls:"^2.11.9","es6-promise":"^4.0.5",grunt:"^1.0.1","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.0.0","grunt-contrib-nodeunit":"^1.0.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^19.0.0","grunt-karma":"^2.0.0","grunt-ts":"^6.0.0-beta.3","grunt-webpack":"^1.0.18","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1",karma:"^1.3.0","karma-chrome-launcher":"^2.0.0","karma-coverage":"^1.0.0","karma-firefox-launcher":"^1.0.0","karma-jasmine":"^1.0.2","karma-jasmine-ajax":"^0.1.13","karma-opera-launcher":"^1.0.0","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^1.1.0","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.7","karma-webpack":"^1.7.0","load-grunt-tasks":"^3.5.2",minimist:"^1.2.0",sinon:"^1.17.4",typescript:"^2.0.3","url-search-params":"^0.6.1",webpack:"^1.13.1","webpack-dev-server":"^1.14.1"},homepage:"https://github.com/axios/axios",keywords:["xhr","http","ajax","promise","node"],license:"MIT",main:"index.js",name:"axios",repository:{type:"git",url:"git+https://github.com/axios/axios.git"},scripts:{build:"NODE_ENV=production grunt build",coveralls:"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",examples:"node ./examples/server.js",postversion:"git push && git push --tags",preversion:"npm test",start:"node ./sandbox/server.js",test:"grunt test && bundlesize",version:"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json"},typings:"./index.d.ts",version:"0.18.0"}},1279:function(e,t){},128:function(e,t,n){"use strict";var r=n(10);function i(){this.handlers=[]}i.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},i.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},i.prototype.forEach=function(e){r.forEach(this.handlers,function(t){null!==t&&e(t)})},e.exports=i},1281:function(e,t){},1283:function(e,t){},1285:function(e,t){},1287:function(e,t){},1289:function(e,t){},129:function(e,t,n){"use strict";var r=n(10),i=n(130),o=n(71),s=n(45),a=n(131),l=n(132);function c(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return c(e),e.baseURL&&!a(e.url)&&(e.url=l(e.baseURL,e.url)),e.headers=e.headers||{},e.data=i(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||s.adapter)(e).then(function(t){return c(e),t.data=i(t.data,t.headers,e.transformResponse),t},function(t){return o(t)||(c(e),t&&t.response&&(t.response.data=i(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},1291:function(e,t){},13:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const r=n(2);let i,o=console;function s(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,i=(new Error).stack.split("\n").slice(e+1);i.shift();let o=[];return i.forEach((e,i)=>{let s=t.exec(e)||n.exec(e),a={};s&&5===s.length&&(a.method=s[1],a.path=s[2],a.line=s[3],a.pos=s[4],a.file=r.basename(a.path),o.push(a))}),o}if(i="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return s(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){o.time(...e)},t.timeEnd=function(...e){o.timeEnd(...e)},t.trace=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.trace(i,...e)},t.info=function(...e){let t=s(),n="anonymous";t[0]&&t[0].method&&(n=n),o.info(i,"["+n+"]",e.join(","))},t.warn=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.warn("<WARN>"+i,"["+n+"]",e.join(","))},t.error=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.error("<ERROR>"+i,"["+n+"]",e.join(","))},t.log=function(...e){o.log(i,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},130:function(e,t,n){"use strict";var r=n(10);e.exports=function(e,t,n){return r.forEach(n,function(n){e=n(e,t)}),e}},131:function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},132:function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},133:function(e,t,n){"use strict";var r=n(72);function i(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new r(e),t(n.reason))})}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var e;return{token:new i(function(t){e=t}),cancel:e}},e.exports=i},134:function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},139:function(e,t,n){"use strict";n.r(t);var r=n(89),i=n(76);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var s=n(0),a=Object(s.a)(i.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\common\\components\\select-native.vue",t.default=a.exports},14:function(e,t){e.exports=require("os")},15:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n,r,i,o,s,a,l,c,u,d,f,h,p,m,g,v,_,y,b,C,R;!function(e){e[e.Unkown=0]="Unkown",e[e.Create=1]="Create",e[e.InvaldParam=2]="InvaldParam",e[e.InvaldLink=3]="InvaldLink",e[e.InvaldConfig=4]="InvaldConfig",e[e.Timeout=5]="Timeout",e[e.VerifyData=6]="VerifyData",e[e.Forbidden=7]="Forbidden",e[e.RangeDispatch=8]="RangeDispatch",e[e.FilePathOverRanging=9]="FilePathOverRanging",e[e.FileCreate=201]="FileCreate",e[e.FileWrite=202]="FileWrite",e[e.FileRead=203]="FileRead",e[e.FileRename=204]="FileRename",e[e.FileFull=205]="FileFull",e[e.FileOccupied=211]="FileOccupied",e[e.FileAccessDenied=212]="FileAccessDenied",e[e.BtUploadExist=601]="BtUploadExist",e[e.ForbinddenResource=701]="ForbinddenResource",e[e.ForbinddenAccount=702]="ForbinddenAccount",e[e.ForbinddenArea=703]="ForbinddenArea",e[e.ForbinddenCopyright=704]="ForbinddenCopyright",e[e.ForbinddenReaction=705]="ForbinddenReaction",e[e.ForbinddenPorn=706]="ForbinddenPorn",e[e.DownloadSDKCrash=10001]="DownloadSDKCrash",e[e.torrentFileNotExist=10002]="torrentFileNotExist"}(t=e.TaskError||(e.TaskError={})),function(e){e[e.Unkown=-1]="Unkown",e[e.Success=0]="Success",e[e.QueryFailed=1]="QueryFailed",e[e.ServerError=2]="ServerError",e[e.ResourceNotFound=3]="ResourceNotFound",e[e.AuthorizingFailed=4]="AuthorizingFailed",e[e.ForbidByCopyright=5]="ForbidByCopyright",e[e.ForbidByPorNoGraphy=6]="ForbidByPorNoGraphy",e[e.ForbidByReactionary=7]="ForbidByReactionary",e[e.ForbidByOtherFilter=8]="ForbidByOtherFilter"}(n=e.DcdnStatusCode||(e.DcdnStatusCode={})),function(e){e[e.Begin=-1]="Begin",e[e.Unkown=0]="Unkown",e[e.StandBy=1]="StandBy",e[e.PreDownloading=2]="PreDownloading",e[e.StartWaiting=3]="StartWaiting",e[e.StartPending=4]="StartPending",e[e.Started=5]="Started",e[e.StopPending=6]="StopPending",e[e.Stopped=7]="Stopped",e[e.Succeeded=8]="Succeeded",e[e.Failed=9]="Failed",e[e.Seeding=10]="Seeding",e[e.DestroyPending=11]="DestroyPending",e[e.End=12]="End"}(r=e.TaskStatus||(e.TaskStatus={})),function(e){e[e.Begin=-1]="Begin",e[e.StandBy=0]="StandBy",e[e.Stopped=1]="Stopped",e[e.Started=2]="Started",e[e.Complete=3]="Complete",e[e.Forbidden=4]="Forbidden",e[e.Error=5]="Error",e[e.End=6]="End"}(i=e.BtFileStatus||(e.BtFileStatus={})),function(e){e[e.DispatchStrategyNone=0]="DispatchStrategyNone",e[e.DispatchStrategyOrigin=1]="DispatchStrategyOrigin",e[e.DispatchStrategyP2s=2]="DispatchStrategyP2s",e[e.DispatchStrategyP2p=4]="DispatchStrategyP2p",e[e.DispatchStrategyAll=-1]="DispatchStrategyAll"}(o=e.DispatchStrategy||(e.DispatchStrategy={})),function(e){e[e.Unkown=0]="Unkown",e[e.P2sp=1]="P2sp",e[e.Bt=2]="Bt",e[e.Emule=3]="Emule",e[e.Group=4]="Group",e[e.Magnet=5]="Magnet"}(s=e.TaskType||(e.TaskType={})),function(e){e[e.Invalid=0]="Invalid",e[e.P2sp=1]="P2sp",e[e.Emule=2]="Emule"}(a=e.TaskCfgType||(e.TaskCfgType={})),function(e){e.Unkown="Unkown",e.Downloading="Downloading",e.Completed="Completed",e.Recycle="Recycle"}(l=e.CategroyViewID||(e.CategroyViewID={})),function(e){e[e.Unknow=0]="Unknow",e[e.TaskCreated=1]="TaskCreated",e[e.InsertToCategoryView=2]="InsertToCategoryView",e[e.RemoveFromCategoryView=3]="RemoveFromCategoryView",e[e.StatusChanged=4]="StatusChanged",e[e.DetailChanged=5]="DetailChanged",e[e.ChannelInfoChanged=6]="ChannelInfoChanged",e[e.DcdnStatusChanged=7]="DcdnStatusChanged",e[e.TaskDescriptionChanged=8]="TaskDescriptionChanged",e[e.TaskUserRead=9]="TaskUserRead",e[e.TaskRenamed=10]="TaskRenamed",e[e.TaskMovedEnd=11]="TaskMovedEnd",e[e.TaskMovingStateChange=12]="TaskMovingStateChange",e[e.BtSubFileDetailChanged=13]="BtSubFileDetailChanged",e[e.BtSubFileLoaded=14]="BtSubFileLoaded",e[e.BtSubFileForbidden=15]="BtSubFileForbidden",e[e.BtSubFileDcdnStatusChanged=16]="BtSubFileDcdnStatusChanged",e[e.TaskCategoryMovedEnd=17]="TaskCategoryMovedEnd",e[e.GroupTaskSubFileDetailChanged=18]="GroupTaskSubFileDetailChanged",e[e.TaskDestroying=19]="TaskDestroying",e[e.TaskDestroyed=20]="TaskDestroyed"}(c=e.TaskEventType||(e.TaskEventType={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.VirtualId=2]="VirtualId",e[e.SrcTotal=3]="SrcTotal",e[e.SrcUsing=4]="SrcUsing",e[e.FileSize=5]="FileSize",e[e.ReceivedSize=6]="ReceivedSize",e[e.DownloadSize=7]="DownloadSize",e[e.TotalDownloadSize=8]="TotalDownloadSize",e[e.CreateTime=9]="CreateTime",e[e.CompletionTime=10]="CompletionTime",e[e.DownloadingPeriod=11]="DownloadingPeriod",e[e.Progress=12]="Progress",e[e.RecycleTime=13]="RecycleTime",e[e.FileCreated=14]="FileCreated",e[e.Forbidden=15]="Forbidden",e[e.CategoryId=16]="CategoryId",e[e.UserRead=17]="UserRead",e[e.OpenOnComplete=18]="OpenOnComplete",e[e.GroupTaskId=19]="GroupTaskId",e[e.DownloadSubTask=20]="DownloadSubTask",e[e.NameType=21]="NameType",e[e.OwnerProduct=22]="OwnerProduct",e[e.FileIndex=23]="FileIndex",e[e.NameFixed=24]="NameFixed",e[e.ValidDownloadSize=25]="ValidDownloadSize",e[e.RealDownloadSize=26]="RealDownloadSize",e[e.ResourceLegal=27]="ResourceLegal",e[e.TaskType=28]="TaskType",e[e.ErrorCode=29]="ErrorCode",e[e.PlayPosition=30]="PlayPosition",e[e.Duration=31]="Duration",e[e.NumberEnd=32]="NumberEnd",e[e.BooleanStart=4096]="BooleanStart",e[e.Destroyed=4097]="Destroyed",e[e.Background=4098]="Background",e[e.Moving=4099]="Moving",e[e.BooleanEnd=4100]="BooleanEnd",e[e.StringStart=8192]="StringStart",e[e.TaskName=8193]="TaskName",e[e.SavePath=8194]="SavePath",e[e.RelativePath=8195]="RelativePath",e[e.TaskUrl=8196]="TaskUrl",e[e.RefUrl=8197]="RefUrl",e[e.Cid=8198]="Cid",e[e.Gcid=8199]="Gcid",e[e.Cookie=8200]="Cookie",e[e.ProductInfo=8201]="ProductInfo",e[e.Origin=8202]="Origin",e[e.Description=8203]="Description",e[e.UserData=8204]="UserData",e[e.OriginName=8205]="OriginName",e[e.HTTPContentType=8206]="HTTPContentType",e[e.CategoryViewId=8207]="CategoryViewId",e[e.YunTaskId=8208]="YunTaskId",e[e.StringEnd=8209]="StringEnd",e[e.ObjectStart=12288]="ObjectStart",e[e.ObjectEnd=12289]="ObjectEnd"}(u=e.TaskAttribute||(e.TaskAttribute={})),function(e){e[e.UnKnown=0]="UnKnown",e[e.SrcTotal=1]="SrcTotal",e[e.SrcUsing=2]="SrcUsing",e[e.FileSize=4]="FileSize",e[e.ReceivedSize=8]="ReceivedSize",e[e.DownloadSize=16]="DownloadSize",e[e.CompletionTime=32]="CompletionTime",e[e.DownloadingPeriod=64]="DownloadingPeriod",e[e.Progress=128]="Progress",e[e.RecycleTime=256]="RecycleTime",e[e.FileCreated=512]="FileCreated",e[e.Forbidden=1024]="Forbidden",e[e.UserRead=2048]="UserRead",e[e.OpenOnComplete=4096]="OpenOnComplete",e[e.DownloadSubTask=8192]="DownloadSubTask",e[e.TaskName=16384]="TaskName",e[e.SavePath=32768]="SavePath",e[e.Cid=65536]="Cid",e[e.Gcid=131072]="Gcid",e[e.UserData=262144]="UserData",e[e.CategoryViewId=524288]="CategoryViewId",e[e.ErrorCode=1048576]="ErrorCode",e[e.TaskSpeed=2097152]="TaskSpeed",e[e.ChannelInfo=4194304]="ChannelInfo",e[e.ValidDownloadSize=8388608]="ValidDownloadSize",e[e.OriginName=16777216]="OriginName",e[e.HTTPContentType=33554432]="HTTPContentType",e[e.PlayPosition=67108864]="PlayPosition",e[e.Duration=134217728]="Duration",e[e.YunTaskId=268435456]="YunTaskId"}(d=e.TaskDetailChangedFlags||(e.TaskDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(f=e.BtSubFileDetailChangedFlags||(e.BtSubFileDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(h=e.GroupTaskSubFileDetailChangedFlags||(e.GroupTaskSubFileDetailChangedFlags={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.FileStatus=2]="FileStatus",e[e.DownloadSize=3]="DownloadSize",e[e.FileSize=4]="FileSize",e[e.EnableDcdn=5]="EnableDcdn",e[e.ErrorCode=6]="ErrorCode",e[e.DcdnStatus=7]="DcdnStatus",e[e.RealIndex=8]="RealIndex",e[e.FileOffset=9]="FileOffset",e[e.Visible=10]="Visible",e[e.Download=11]="Download",e[e.UserRead=12]="UserRead",e[e.PlayPosition=13]="PlayPosition",e[e.Duration=14]="Duration",e[e.NumberEnd=15]="NumberEnd",e[e.StringStart=4096]="StringStart",e[e.FinalName=4097]="FinalName",e[e.RelativePath=4098]="RelativePath",e[e.FileName=4099]="FileName",e[e.FilePath=4100]="FilePath",e[e.Cid=4101]="Cid",e[e.Gcid=4102]="Gcid",e[e.StringEnd=4103]="StringEnd"}(p=e.BtFileAttribute||(e.BtFileAttribute={})),function(e){e[e.P2spUrl=0]="P2spUrl",e[e.BtInfoId=1]="BtInfoId",e[e.EmuleFileHash=2]="EmuleFileHash",e[e.MagnetUrl=3]="MagnetUrl",e[e.GroupTaskName=4]="GroupTaskName"}(m=e.KeyType||(e.KeyType={})),function(e){e[e.NameInclude=1]="NameInclude",e[e.BtDisplayNameInclude=2]="BtDisplayNameInclude"}(g=e.SearchKeyType||(e.SearchKeyType={})),function(e){e[e.ExtEqual=1]="ExtEqual",e[e.NameLikeAndExtEqual=2]="NameLikeAndExtEqual",e[e.TaskTypeEqual=4]="TaskTypeEqual"}(v=e.FilterKeyType||(e.FilterKeyType={})),function(e){e[e.All=0]="All",e[e.CommonForeground=1]="CommonForeground",e[e.CommonBackground=2]="CommonBackground",e[e.Temporary=3]="Temporary",e[e.PreDownload=4]="PreDownload",e[e.PrivateForeground=5]="PrivateForeground"}(_=e.KeyFilter||(e.KeyFilter={})),function(e){e[e.Unknown=-1]="Unknown",e[e.LoadTaskBasic=0]="LoadTaskBasic",e[e.Create=1]="Create",e[e.Complete=2]="Complete",e[e.Recycle=3]="Recycle",e[e.Recover=4]="Recover",e[e.ReDownload=5]="ReDownload",e[e.MoveThoughCategory=6]="MoveThoughCategory"}(y=e.TaskInsertReason||(e.TaskInsertReason={})),function(e){e[e.Unknown=-1]="Unknown",e[e.Manual=0]="Manual",e[e.PauseAll=1]="PauseAll",e[e.DeleteTask=2]="DeleteTask",e[e.TaskJammed=3]="TaskJammed",e[e.LowSpeed=4]="LowSpeed",e[e.MaxDownloadReduce=5]="MaxDownloadReduce",e[e.MoveTask=6]="MoveTask",e[e.SelectDownloadLists=7]="SelectDownloadLists",e[e.PrivateLoginOut=8]="PrivateLoginOut",e[e.FreeDownload=9]="FreeDownload",e[e.Exit=10]="Exit"}(b=e.TaskStopReason||(e.TaskStopReason={})),function(e){e[e.RESOURCE_FROM_MEMBER=1]="RESOURCE_FROM_MEMBER",e[e.RESOURCE_FROM_OFFLINE=2]="RESOURCE_FROM_OFFLINE",e[e.RESOURCE_FROM_CRYSTAL_LARGE=4]="RESOURCE_FROM_CRYSTAL_LARGE",e[e.RESOURCE_FROM_CRYSTAL_SMALL=8]="RESOURCE_FROM_CRYSTAL_SMALL",e[e.RESOURCE_FROM_DCDN=16]="RESOURCE_FROM_DCDN",e[e.RESOURCE_FROM_FREEDCDN=32]="RESOURCE_FROM_FREEDCDN"}(C=e.XLResourceFrom||(e.XLResourceFrom={})),function(e){e[e.XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD=0]="XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD",e[e.XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING=1]="XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING",e[e.XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING=2]="XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING"}(R=e.XLDownloadStrategy||(e.XLDownloadStrategy={}))}(t.DownloadKernel||(t.DownloadKernel={}))},16:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(2),s=n(1),a=n(18),l=n(25),c=s.default.getLogger("Thunder.Util"),u="Thunder Network\\Thunder7.9\\";function d(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(){let e=l.ThunderHelper.getSystemTempPath(),t=l.ThunderHelper.getLogicalDriveStrings(),n=0;for(let r=0;r<t.length;r++){if(l.ThunderHelper.getDriveType(t[r])===l.ThunderHelper.DriverType.DRIVE_FIXED){let i=l.ThunderHelper.getDriveInfo(t[r]);n<i.freeBytes&&t[r]!==e&&(n=i.freeBytes,e=t[r])}}return e.substring(0,1)+":\\迅雷下载"}function s(e){let t=(e.style.webkitTransform||getComputedStyle(e,"").getPropertyValue("-webkit-transform")||e.style.transform||getComputedStyle(e,"").getPropertyValue("transform")).match(/\-?[0-9]+\.?[0-9]*/g);return{x:parseInt(t&&(t[12]||t[4])||"0",10),y:parseInt(t&&(t[13]||t[5])||"0",10)}}function f(e){let t=!1;do{let n="",r="";if(/^[a-zA-Z]:\\/.test(e))n=e.slice(3);else{if(0!==e.indexOf("\\\\"))break;{let t=e.indexOf("\\",2);if(-1===t||t===e.length-1)break;if(""===(r=(n=e.slice(2)).substr(0,t-2)))break}}if(/[\*\"<>\?:\|]/i.test(n))break;if(e.length>256)break;if(""===r){t=!0;break}let i=r.indexOf(".ipv6-literal.net");-1!==i?(-1!==(i=(r=r.substr(0,i)).indexOf("%"))&&(r=r.substr(0,i)),r=r.replace(/\-/g,":"),/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/.test(r)&&(t=!0)):/(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))/.test(r)&&(t=!0)}while(0);return t}e.formatSize=function(e,t){0===t||(t=t||2);let n="0B";if("number"==typeof e&&e>0){let r=["B","KB","MB","GB","TB"],i=0,o=e;for(;o>=1e3&&!(i>=4);)o/=1024,i+=1;n=-1===String(o).indexOf(".")?o+r[i]:o.toFixed(t)+r[i]}return n},e.formatSizeCustom=function(e,t=2,n=5){let r="0B";if("number"==typeof e&&e>0){let i=["B","KB","MB","GB","TB"],o=0,s=e;for(;s>=1e3&&!(o>=4);)s/=1024,o+=1;if(-1===String(s).indexOf("."))r=s+i[o];else{let e=s.toFixed(t);e.length<=n?r="KB"!==i[o]&&"MB"!==i[o]||1===t?e+i[o]:s.toFixed(1)+i[o]:("."===(e=e.substr(0,n))[n-1]&&(e=e.substr(0,n-1)),r=e+i[o])}}return r},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.getDefaultDownloadDir=t,e.getMaxFreeDriver=function(){return t().substring(0,1)},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){c.warning(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",r=!1,i=[],o=e.length;for(let s=0;s<o;s++){let a=e[s];if('"'!==a&&"'"!==a||(""===n?(r=!0,n=a):n===a&&(r=!1,n=""))," "!==a||r||s===o-1){if(s===o-1){let n=e.substring(t);""!==n.trim()&&i.push(d(n))}}else{let n=e.substring(t,s);""!==n.trim()&&i.push(d(n)),t=s+1}}return i},e.getThunderTempPath=function(e,t){return r(this,void 0,void 0,function*(){const r=yield Promise.resolve().then(()=>n(14));let i=o.join(r.tmpdir(),u);return t&&(i=o.join(i,t)),void 0!==e&&e&&(yield a.FileSystemAWNS.mkdirsAW(i)),i})},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,r)=>{e+=0===r?"?":"&",e+=`${n}=${t[n]}`}),e},e.setQueryStringEx=function(e,t){for(let n in t)e+=-1===e.indexOf("?")?"?":"&",e+=`${n}=${t[n]}`;return e},e.getQueryString=function(e,t){return e&&t&&e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:""},e.isClipboardTextFormatAvailable=function(){let e=!1,t=i.clipboard.availableFormats();for(let n of t)if("text/plain"===n){e=!0;break}return e},e.keywordsHighLight=function(e,t,n){if(!e)return"";if(!t)return e;if(0===e.length)return e;if(0===t.length)return e;let r=/\\/,i=t.split(" ");if(0===(i=i.filter(e=>e.trim().length>0)).length)return e;for(let t=0;t<i.length;t++)if(i[t].search(r)>0)return e;n=void 0===n||null===n?"#FF0000":n;let o="",s=["\\[","\\^","\\*","\\(","\\)","\\|","\\?","\\$","\\.","\\+"],a="",l="|";for(let e=0;e<i.length;e++){for(let t=0;t<s.length;t++){let n=new RegExp(s[t],"g");i[e]=i[e].replace(n,s[t])}e===i.length-1&&(l=""),a=a.concat(i[e],l)}let c=new RegExp(a,"gi");return o=e.replace(c,e=>'<span style= "color:'+n+'">'+e+"</span>")},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setStyle=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style[t]=n})},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),r=t.split("."),i=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(r[e])>0){i=1;break}if(Number(n[e])-Number(r[e])<0){i=-1;break}}return i},e.throttle=function(e,t){let n,r=0;return(...i)=>{const o=Date.now();clearTimeout(n),o-r>t?(e(...i),r=o):n=setTimeout(()=>{e(...i),r=o},t)}},e.debounce=function(e,t){let n=null;return(...r)=>{n&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}},e.getElementFixed=function(e){let t=e.offsetLeft,n=e.offsetTop,r=e.offsetParent;for(;null!==r;){let e=s(r);t+=r.offsetLeft+e.x,n+=r.offsetTop+e.y,r=r.offsetParent}return{x:t,y:n}},e.escapeHTML=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")},e.unescapeHTML=function(e){return e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")},e.isValidPath=f,e.isValidDownloadPath=function(e){return r(this,void 0,void 0,function*(){let t=!1;do{if(e.length<3)break;if("私人空间"===e){t=!0;break}if(l.ThunderHelper.getDriveType(e)===l.ThunderHelper.DriverType.DRIVE_REMOTE){t=!0;break}if(!f(e))break;if(!(yield a.FileSystemAWNS.dirExistsAW(e))&&!(yield a.FileSystemAWNS.mkdirsAW(e)))break;t=!0}while(0);return t})};let h=void 0;function p(e,t="normal 12px sans-serif"){h||(h=document.createElement("canvas"));let n=h.getContext("2d");return n.font=t,n.measureText(e).width}function m(e,t,n="normal 12px sans-serif",r=1){function i(e,t,r,o,s){let a=-1;do{if(e>t){a=t;break}let l=Math.round((e+t)/2),c=p(`${r.substr(0,l)}...${o}`,n);if(s===c){a=l;break}if(s>c){if(Math.round(s)===Math.round(c)){a=l;break}a=i(l+1,t,r,o,s)}else if(c>s){if(Math.round(s)===Math.round(c)){a=l-1;break}a=i(e,l-1,r,o,s)}}while(0);return a}let s=e;do{if(!t)break;if(!e)break;let a=t.offsetWidth*r;if(a>p(e,n))break;let l=o.extname(e);""!==l&&(l=l.substring(1));let c=e.substr(0,e.length-l.length-1);if(!c)break;let u=i(0,c.length,c,l,a);if(-1===u)break;s=`${c.substr(0,u-2*(r-1))}...${l}`}while(0);return s}e.getTextWidth=p,e.getOmitName=m,e.getOmitNameMultiLine=function(e,t,n){return m(e,t,"normal 12px microsoft yahei",2)},e.setTimeoutAw=function(e,t){return new Promise((n,r)=>{setTimeout(()=>{t&&t(),n()},e)})}}(t.ThunderUtil||(t.ThunderUtil={}))},17:function(e,t,n){"use strict";var r=n(10),i=n(67),o=n(69),s=n(44),a=n(40),l=n(70).http,c=n(70).https,u=n(34),d=n(48),f=n(127),h=n(46),p=n(68);e.exports=function(e){return new Promise(function(t,n){var m,g=e.data,v=e.headers;if(v["User-Agent"]||v["user-agent"]||(v["User-Agent"]="axios/"+f.version),g&&!r.isStream(g)){if(Buffer.isBuffer(g));else if(r.isArrayBuffer(g))g=new Buffer(new Uint8Array(g));else{if(!r.isString(g))return n(h("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",e));g=new Buffer(g,"utf-8")}v["Content-Length"]=g.length}var _=void 0;e.auth&&(_=(e.auth.username||"")+":"+(e.auth.password||""));var y=u.parse(e.url),b=y.protocol||"http:";if(!_&&y.auth){var C=y.auth.split(":");_=(C[0]||"")+":"+(C[1]||"")}_&&delete v.Authorization;var R="https:"===b,w=R?e.httpsAgent:e.httpAgent,E={path:o(y.path,e.params,e.paramsSerializer).replace(/^\?/,""),method:e.method,headers:v,agent:w,auth:_};e.socketPath?E.socketPath=e.socketPath:(E.hostname=y.hostname,E.port=y.port);var S,T=e.proxy;if(!T&&!1!==T){var M=b.slice(0,-1)+"_proxy",k=process.env[M]||process.env[M.toUpperCase()];if(k){var x=u.parse(k);if(T={host:x.hostname,port:x.port},x.auth){var N=x.auth.split(":");T.auth={username:N[0],password:N[1]}}}}if(T&&(E.hostname=T.host,E.host=T.host,E.headers.host=y.hostname+(y.port?":"+y.port:""),E.port=T.port,E.path=b+"//"+y.hostname+(y.port?":"+y.port:"")+E.path,T.auth)){var O=new Buffer(T.auth.username+":"+T.auth.password,"utf8").toString("base64");E.headers["Proxy-Authorization"]="Basic "+O}e.transport?S=e.transport:0===e.maxRedirects?S=R?a:s:(e.maxRedirects&&(E.maxRedirects=e.maxRedirects),S=R?c:l),e.maxContentLength&&e.maxContentLength>-1&&(E.maxBodyLength=e.maxContentLength);var I=S.request(E,function(r){if(!I.aborted){clearTimeout(m),m=null;var o=r;switch(r.headers["content-encoding"]){case"gzip":case"compress":case"deflate":o=o.pipe(d.createUnzip()),delete r.headers["content-encoding"]}var s=r.req||I,a={status:r.statusCode,statusText:r.statusMessage,headers:r.headers,config:e,request:s};if("stream"===e.responseType)a.data=o,i(t,n,a);else{var l=[];o.on("data",function(t){l.push(t),e.maxContentLength>-1&&Buffer.concat(l).length>e.maxContentLength&&n(h("maxContentLength size of "+e.maxContentLength+" exceeded",e,null,s))}),o.on("error",function(t){I.aborted||n(p(t,e,null,s))}),o.on("end",function(){var r=Buffer.concat(l);"arraybuffer"!==e.responseType&&(r=r.toString("utf8")),a.data=r,i(t,n,a)})}}});I.on("error",function(t){I.aborted||n(p(t,e,null,I))}),e.timeout&&!m&&(m=setTimeout(function(){I.abort(),n(h("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",I))},e.timeout)),e.cancelToken&&e.cancelToken.promise.then(function(e){I.aborted||(I.abort(),n(e))}),r.isStream(g)?g.pipe(I):I.end(g)})}},170:function(e,t,n){e.exports=n(9)(51)},171:function(e,t,n){e.exports=n(9)(24)},172:function(e,t,n){e.exports=n(9)(63)},173:function(e,t,n){e.exports=n(9)(8)},174:function(e,t,n){e.exports=n(9)(60)},18:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(21),o=n(2),s=n(6),a=n(60),l=s.promisify,c=n(1).default.getLogger("Thunder.base.fs-utilities");!function(e){function t(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.access);try{yield n(e),t=!0}catch(e){c.information(e)}}return t})}function s(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.mkdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function u(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.rmdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function d(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.unlink);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function f(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.readdir);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function h(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.lstat);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function p(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let r=o.join(e,t),i=yield h(r);n=null!==i&&i.isDirectory()?yield m(r):yield d(r)}return n})}function m(e){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e){if(yield t(e)){n=!0;let t=(yield f(e))||[];for(let r=0;r<t.length;r++)n=(yield p(e,t[r]))&&n;(n||0===t.length)&&(n=(yield u(e))&&n)}}return n})}function g(e){return r(this,void 0,void 0,function*(){let n=!1;return c.information("mkdirsAW",e),void 0!==e&&((yield t(e))?n=!0:o.dirname(e)===e?n=!1:(yield g(o.dirname(e)))&&(n=yield s(e))),n})}function v(e,n){return r(this,void 0,void 0,function*(){let r;if(e.toLowerCase()!==n.toLowerCase()&&(yield t(e))){let t=i.createReadStream(e),o=i.createWriteStream(n);r=new Promise(e=>{t.pipe(o).on("finish",()=>{e(!0)})})}else r=new Promise(e=>{e(!1)});return r})}e.readFileAW=function(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.readFile);try{t=yield n(e)}catch(e){c.warning(e)}}return t})},e.readLineAw=function(e){return r(this,void 0,void 0,function*(){let n=null;do{if(!e)break;if(!t(e))break;n=yield new Promise(t=>{let n=[];const r=i.createReadStream(e),o=a.createInterface({input:r});o.on("line",e=>{n.push(e)}),o.on("close",()=>{t(n)})})}while(0);return n})},e.writeFileAW=function(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const r=l(i.writeFile);try{yield r(e,t),n=!0}catch(e){c.warning(e)}}return n})},e.existsAW=t,e.dirExistsAW=function(e){return r(this,void 0,void 0,function*(){let n=!1;do{if(!(n=yield t(e)))break;let r=yield h(e);if(!r)break;n=r.isDirectory()}while(0);return n})},e.mkdirAW=s,e.rmdirAW=u,e.unlinkAW=d,e.readdirAW=f,e.lstatAW=h,e.rmdirsAW=m,e.mkdirsAW=g,e.renameAW=function(e,t){return r(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=l(i.rename);try{yield n(e,t)}catch(e){c.warning(e)}}})},e.copyFileAW=v,e.copyDirsAW=function e(n,i){return r(this,void 0,void 0,function*(){let r=!1,s=yield h(n);if(s.isDirectory()){r=yield g(i);let a=(yield f(n))||[];for(let l=0;l<a.length;l++){let c=o.join(n,a[l]),u=o.join(i,a[l]);(r=yield t(c))&&(r=(s=yield h(c)).isDirectory()?yield e(c,u):yield v(c,u))}}return r})},e.mkdtempAW=function(){return r(this,void 0,void 0,function*(){let e=!1;const t=l(i.mkdtemp),r=(yield Promise.resolve().then(()=>n(14))).tmpdir();try{e=yield t(`${r}${o.sep}`)}catch(e){c.warning(e)}return e})},e.deleteEmptySubDirs=function(e,n){return r(this,void 0,void 0,function*(){let r=!0;e=o.normalize(e),n=o.normalize(n),e.length>3&&"\\"===e[e.length-1]&&(e=e.slice(0,e.length-1)),n.length>3&&"\\"===n[n.length-1]&&(n=n.slice(0,n.length-1));do{if(0!==e.indexOf(n)){r=!1;break}let i=e;for(;i!==n;){if((yield t(i))&&!(yield u(i))){r=!1;break}i=o.dirname(i)}}while(0);return r})},e.getFileSize=function e(n){return r(this,void 0,void 0,function*(){let r=0;do{if(!n)break;if(!(yield t(n)))break;let i=yield h(n);if(i)if(i.isDirectory()){let t=yield f(n);for(let i=0;i<t.length;i++)r+=(yield e(o.join(n,t[i])))}else r=i.size}while(0);return r})},e.isDirectoryEmptyAW=function(e,n=!0){return r(this,void 0,void 0,function*(){let r=!0;do{if(!e){r=!1;break}if(!(yield t(e))){r=n;break}let i=yield h(e);if(!i){r=!1;break}if(!i.isDirectory()){r=!1;break}if((yield f(e)).length>0){r=!1;break}}while(0);return r})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},19:function(e,t,n){e.exports=n(115)},2:function(e,t){e.exports=require("path")},20:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.channelRMNewTaskReadyForSetTaskData="RM_NEWTASK_READYRORSETTASKDATA",e.channelRMNewTaskSetTaskData="RM_NEWTASK_SETTASKDATA",e.channelRMPreNewTaskSetTaskData="RM_PRENEWTASK_SETTASKDATA",e.channelRMNewTaskCreateNewTask="RM_NEWTASK_CREATENEWTASK",e.channelRMNewTaskClose="RM_NEWTASK_CLOSE",e.channelRMPreNewTaskClose="RM_PRENEWTASK_CLOSE",e.channelRMNewTaskSetBTInfo="RM_NEWTASK_SETBTINFO",e.channelRMNewTaskDownloadTorrent="RM_NEW_TASK_DOWNLOAD_TORRENT",e.channelRMNewTaskCreateBtTask="RM_NEWTASK_CRATEBTTASK",e.channelRMNewTaskCancleMagnet="RM_NEWTASK_CANCLE_MAGNET",e.channelRMImportTorrent="RM_NEWTASK_IMPORT_TORRENT",e.channelRMGetConfigValueResolve="RM_GET_CONFIG_VALUE_RESOLVE",e.channelRMGetConfigValueReject="RM_GET_CONFIG_VALUE_REJECT",e.channelRMSetConfigValueReject="RM_SET_CONFIG_VALUE_REJECT",e.channelMRTrayMenuClick="MR_TRAY_MENU_CLICK",e.channelMRNewTaskMagnetTaskCreated="MR_NEW_TASK_MAGNET_TASK_CREATED",e.channelMRNewTaskDownloadTorrentResult="MR_NEW_TASK_DOWNLOAD_TORRENT_RESULT",e.channelMRNewTaskCreateNewTaskResult="MR_NEWTASK_CREATENEWTASK_RESULT",e.channelMRNewTaskCreateBtTaskResult="RM_NEWTASK_CRATEBTTASK_RESULT",e.channelMRGetConfigValue="MR_GET_CONFIG_VALUE",e.channelMRSetConfigValue="MR_SET_CONFIG_VALUE",e.channelRMCommitPlanTask="RM_PLANTASK_COMMIT",e.channelRMPerformePlanTask="RM_PLANTASK_PERFORME",e.channelRMProcessSend="RM_RENDER_PROCESS_INFO",e.channelRMGetPrivateSpaceInfo="RM_RENDER_GET_PRIVATE_SPACE_INFO",e.channelMRGetPrivateSpaceInfoResult="MR_RENDER_GET_PRIVATE_SPACE_INFO_RESULT",e.channelRMFileCopy="RM_FILE_COPY",e.channelRMFileMove="RM_FILE_MOVE",e.channelMRFileCopyResult="MR_FILE_COPY_RESULT",e.channelMRFileMoveResult="MR_FILE_MOVE_RESULT",e.channelRMGetSutitleByCid="RM_RENDER_GET_SUBTITLE_BY_CID",e.channelMRGetSutitleByCidResult="MR_RENDER_GET_SUBTITLE_BY_CID_RESULT",e.channelRMGetSutitleByName="RM_RENDER_GET_SUBTITLE_BY_NAME",e.channelMRGetSutitleByNameResult="MR_RENDER_GET_SUBTITLE_BY_NAME_RESULT",e.channelRMDownloadSutitle="RM_RENDER_DOWNLOAD_SUBTITLE",e.channelMRDownloadSutitleSuc="MR_RENDER_DOWNLOAD_SUBTITLE_SUCCESS",e.channelMRDownloadSutitleFail="MR_RENDER_DOWNLOAD_SUBTITLE_FAIL",e.channelRMGetDisplayName="RM_RENDER_GET_SUBTITLE_DISPLAYNAME",e.channelMRGetDisplayNameResult="MR_RENDER_GET_SUBTITLE_DISPLAYNAME_RESULT",e.channelMRBringWindowToTop="MR_RENDER_BRING_WINDOW_TO_TOP",e.channelRMDownloadXmp="RM_RENDER_DOWNLOAD_XMP",e.channelRMXmpFixBoxCreated="RM_RENDER_XMPFIXBOX_CREATED",e.channelMRFixXmpSuc="MR_RENDER_FIX_XMP_SUC",e.channelMRFixXMPFail="MR_RENDER_FIX_XMP_FAIL",e.channelRMDownloadXmpEx="RM_RENDER_DOWNLOAD_XMP_EX",e.channelRMSetPosition="RM_SET_POSITION",e.channelRMSetFoucs="RM_SET_FOCUS",e.channelRMCreateAddressDropWnd="RM_CREATE_ADDRESS_DROPWND",e.channelRMRefreshAddressDropWnd="RM_REFRESH_ADDRESS_DROPWND",e.channelRMSelectAddressDropItem="RM_SELECT_ADDRESS_DROPITEM",e.channelRMCreateSearchWindow="RM_CREATE_SEARCH_WINDOW",e.channelRMShowSearchWindow="RM_SHOW_SEARCH_WINDOW",e.channelRMAddressKeyDown="RM_ADDRESS_BAR_KEYDOWN",e.channelMRFWAddressKeyDown="MR_ADDRESS_FW_BAR_KEYDOWN",e.channelMRFWSelectAddressDropItem="MR_FW_SELECT_ADDRESS_DROPITEM",e.channelRMAddressDropWndKeyDown="RM_ADDRESS_DROPWND_KEYDOWN",e.channelRMClickMouse="RM_CLICK_MOUSE",e.channelMRSearchBarFocusChange="MR_SEARCHBAR_FOCUS_CHANGE",e.channelMRFWAddressDropWndKeyDown="MR_FW_ADDRESS_DROPWND_KEYDOWN",e.channelMRClickAddressDropItem="MR_CLICK_ADDRESS_DROPITEM",e.channelMRMainWndMax="MR_MAINWINDOW_MAX",e.channelMRMainWndMin="MR_MAINWINDOW_MIN",e.channelMRMainWndRestore="MR_MAINWINDOW_RESTORE",e.channelRMGetBrowserStartType="RM_GET_BROWSER_START_TYPE",e.channelMRGetBrowserStartTypeResult="MR_GET_BROWSER_START_TYPE_RESULT",e.channelRMExecute="RM_SHELL_EXECUTE",e.channelMRExecuteResult="MR_SHELL_EXECUTE_RESULT",e.channelMRAdTipsClick="MR_AD_TIPS_CLICK",e.channelMRNotificationMsg="MR_NOTIFICATION_MSG",e.channelRMSetProgressBar="RM_SET_PROGRESS_BAR",e.channelRMRoundWindow="RM_ROUND_WINDOW",e.channelMRShowOrHideWindow="MR_SHOW_OR_HIDE_WINDOW",e.channelMRSuspensionWindowShowOrHide="MR_SUSPENSION_WINDOW_SHOW_OR_HIDE",e.channelRMConfigInitFinished="RM_CONFIG_INIT_FINISHED",e.channelRMConfigValueChanged="RM_CONFIG_VALUE_CHANGED",e.channelRMIndividuationBrowserMsg="RM_INDIVIDUATION_BROWSER_MSG",e.channelMRIndividuationBrowserMsg="MR_INDIVIDUATION_BROWSER_MSG",e.channelRMSetEnvironmentVariable="RM_SET_ENVIRONMENT_VARIABLE",e.channelMREmbedPlayerPos="MR_EMBED_PLAYER_POSITION",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},21:function(e,t){e.exports=require("fs")},22:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=function(...e){},t.error=function(...e){},t.warning=function(...e){},t.critical=function(...e){},t.verbose=function(...e){},"development"===process.env.LOGGER_ENV&&(t.information=function(...e){console.log("information",e)},t.error=function(...e){console.log("error",e)},t.warning=function(...e){console.log("warning",e)},t.critical=function(...e){console.log("critical",e)},t.verbose=function(...e){console.log("verbose",e)})},23:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.msgIPCCommunicatorForward="ipc_communicator_forward",e.msgIPCSendToMain="ipc_send_to_main",e.msgIPCSendToRenderer="ipc_send_to_renderer",e.msgIPCRendererConnect="ipc_renderer_connect",e.msgIPCRendererDisconnect="ipc_renderer_disconnect",e.msgNCCallNativeFunction="nc_call_native_function",e.msgNCCheckNativeFunction="nc_check_native_function",e.msgNCCallJsFunctionById="nc_call_js_function_by_id",e.msgNCCallJsFunctionByName="nc_call_js_function_by_name",e.msgNCNativeFireEvent="nc_native_fire_event",e.msgNCNativeCallReady="nc_native_call_ready"}(t.CommonIPCMessage||(t.CommonIPCMessage={}))},231:function(e,t){e.exports="data:image/png;base64,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"},25:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(33),s=n(14),a=n(15),l=n(8).default(i.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){let t,n,i,c;function u(e){let t=e;return/^[a-zA-Z]:\\/.test(e)?t=e.slice(0,3):e&&"\\"!==e[e.length-1]&&(t=e+"\\"),t}!function(e){e[e.DRIVE_UNKNOWN=0]="DRIVE_UNKNOWN",e[e.DRIVE_NO_ROOT_DIR=1]="DRIVE_NO_ROOT_DIR",e[e.DRIVE_REMOVABLE=2]="DRIVE_REMOVABLE",e[e.DRIVE_FIXED=3]="DRIVE_FIXED",e[e.DRIVE_REMOTE=4]="DRIVE_REMOTE",e[e.DRIVE_CDROM=5]="DRIVE_CDROM",e[e.DRIVE_RAMDISK=6]="DRIVE_RAMDISK"}(t=e.DriverType||(e.DriverType={})),function(e){e[e.Unspecified=0]="Unspecified",e[e.HDD=3]="HDD",e[e.SSD=4]="SSD",e[e.SCM=5]="SCM"}(n=e.MediaType||(e.MediaType={})),function(e){e.HKEY_CLASSES_ROOT="HKEY_CLASSES_ROOT",e.HKEY_CURRENT_USER="HKEY_CURRENT_USER",e.HKEY_LOCAL_MACHINE="HKEY_LOCAL_MACHINE",e.HKEY_USERS="HKEY_USERS"}(i=e.RegistryHKey||(e.RegistryHKey={})),function(e){e[e.REG_NONE=0]="REG_NONE",e[e.REG_SZ=1]="REG_SZ",e[e.REG_EXPAND_SZ=2]="REG_EXPAND_SZ",e[e.REG_BINARY=3]="REG_BINARY",e[e.REG_DWORD=4]="REG_DWORD",e[e.REG_DWORD_LITTLE_ENDIAN=4]="REG_DWORD_LITTLE_ENDIAN",e[e.REG_DWORD_BIG_ENDIAN=5]="REG_DWORD_BIG_ENDIAN",e[e.REG_LINK=6]="REG_LINK",e[e.REG_MULTI_SZ=7]="REG_MULTI_SZ",e[e.REG_RESOURCE_LIST=8]="REG_RESOURCE_LIST",e[e.REG_FULL_RESOURCE_DESCRIPTOR=9]="REG_FULL_RESOURCE_DESCRIPTOR",e[e.REG_RESOURCE_REQUIREMENTS_LIST=10]="REG_RESOURCE_REQUIREMENTS_LIST",e[e.REG_QWORD=11]="REG_QWORD",e[e.REG_QWORD_LITTLE_ENDIAN=11]="REG_QWORD_LITTLE_ENDIAN"}(c=e.RegistryDataType||(e.RegistryDataType={})),e.getDriveType=function(e){return e=u(e),l.getDriveType(e)},e.getDriveInfo=function(e){return e=u(e),l.getDriveInfo(e)},e.getFreePartitionSpace=function(e){return e=u(e),l.getFreePartitionSpace(e)},e.getLogicalDriveStrings=function(){return l.getLogicalDriveStrings()},e.getPartitionSpace=function(e){return e=u(e),l.getPartitionSpace(e)},e.getSystemTempPath=function(){return l.getSystemTempPath()},e.getTaskTypeFromUrl=function(e){let t=l.getTaskTypeFromUrl(e);if(t===a.DownloadKernel.TaskType.Unkown&&function(e){e=e.toLowerCase();let t=!1;do{if("http://"===e.substr(0,"http://".length)){t=!0;break}if("https://"===e.substr(0,"https://".length)){t=!0;break}if("ftp://"===e.substr(0,"ftp://".length)){t=!0;break}}while(0);return t}(e)){let n=/:\/\/\[(.+?)\].*/.exec(e);n||(n=/.+?:\/\/.*?\[(.+?)\].*/.exec(e)),n&&n[1]&&o.isIPv6(n[1])&&(t=a.DownloadKernel.TaskType.P2sp)}return t},e.extractIcon=function(e,t){return l.extractIcon(e,t)},e.isWindow7=function(){return 1===l.isWin7()},e.isWindow8OrLater=function(){let e=!1;do{let t=s.release();if(!t)break;let n=t.indexOf(".",0);if(n<0)break;let r=t.substring(0,n);if(!r)break;let i=parseInt(r,10);i&&i>=8&&(e=!0)}while(0);return e},e.isWindows10=function(){let e=!1;do{let t=s.release();if(!t)break;if(0===t.indexOf("10.0.")){e=!0;break}}while(0);return e},e.compareStr=function(e,t){return l.compareStr(e,t)},e.getTickCount=function(){return l.getTickCount()},e.setScreenSaveActive=function(e,t){return l.setScreenSaveActive(e,t)},e.isSparseDriver=function(e){return e=u(e),l.isSparseDriver(e)},e.getAppList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getAppList(t=>{e(t)})})})},e.isSSD=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.isSSD((t,n)=>{e(n)})})})},e.getMemoryInfo=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getMemoryInfo((t,n)=>{e({totalPhy:t,totalVir:n})})})})},e.getHardDiskSpaceList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getHardDiskSpaceList(t=>{e(t)})})})},e.getCpuList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getCpuList(t=>{e(t)})})})},e.getFixedDriveMediaType=function(e){return r(this,void 0,void 0,function*(){return new Promise(t=>{e.length>1&&(e=e.slice(0,1)),l.getDriveMediaType(e.toUpperCase(),(e,n)=>{t(n)})})})},e.sleep=function(e){return r(this,void 0,void 0,function*(){yield new Promise((t,n)=>{setTimeout(t,e)})})},e.getTextScale=function(){let e=100,t=l.readRegString(i.HKEY_CURRENT_USER,"SOFTWARE\\Microsoft\\Accessibility","TextScaleFactor");return t&&(e=Number(t)),isNaN(e)&&(e=100),e},e.getWindowRect=function(e){return e?l.getWindowRect(e):{x:0,y:0,width:0,height:0}},e.moveWindow=function(e,t){e&&l.moveWindow(e,t.x,t.y,t.width,t.height,!0)},e.getSystemDirectory=function(){return l.getSystemDirectory()},e.getVersionBlockString=function(e,t){return l.getVersionBlockString(e,t)},e.getOwnerName=function(e){return l.getOwnerName(e)},e.createRegKey=function(e,t){return l.createRegKey(e,t)},e.deleteRegKey=function(e,t){return l.deleteRegKey(e,t)},e.readRegString=function(e,t,n){return l.readRegString(e,t,n)},e.queryRegValue=function(e,t,n){return l.queryRegValue(e,t,n)},e.writeRegValue=function(e,t,n,r,i){return l.writeRegValue(e,t,n,r,i)},e.deleteRegValue=function(e,t,n){return l.deleteRegValue(e,t,n)}}(t.ThunderHelper||(t.ThunderHelper={}))},28:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),i=n(6),o=n(22),s=n(23);!function(e){e.mainProcessContext="main-process",e.mainRendererContext="main-renderer",e.mainPageWebviewRendererContext="main-page-webview-renderer",e.newTaskRendererContext="new-task-renderer",e.preNewTaskRendererContext="pre-new-task-renderer",e.loginRendererContext="login-renderer";class t{constructor(){this.isConnected=!1,this.isOnIPCEvent=!1,this.rendererInfos=[],this.listeners=new Map,t.intervalIPCModuleMsgs=[s.CommonIPCMessage.msgIPCRendererConnect,s.CommonIPCMessage.msgIPCRendererDisconnect]}onMessage(e,t){do{if(!i.isString(e)||0===e.length){o.error("msgName is null");break}if(i.isNullOrUndefined(t)){o.error("listener is null");break}this.listeners.has(e)?this.listeners.get(e).push(t):this.listeners.set(e,[t])}while(0)}getCommunicatorInfo(){return this.currInfo}getAllRenderer(){return this.rendererInfos}getCommunicatorInfoById(e){for(let t of this.rendererInfos)if(t.id===e)return t;return null}getCommunicatorInfoByContext(e){for(let t of this.rendererInfos)if(t.context===e)return t;return null}startListenIPCMessage(e){this.isOnIPCEvent||(this.isOnIPCEvent=!0,e&&this.ListenSendToMainMsg(),this.ListenSendToRendererMsg(e))}ListenSendToMainMsg(){r.ipcMain.on(s.CommonIPCMessage.msgIPCSendToMain,(e,t)=>{let n=void 0;do{if(i.isNullOrUndefined(t)){o.error("msgInfo is empty");break}if(!this.isConnected){o.warning("hasnot been connected yet");break}let r=t.msg.name;if(this.isIPCModuleIntervalMsg(r)){o.information(`IPC module interval msg : ${r}`);let i=this.handleIPCModuleIntervalMsg(e.sender,t);if(n=i[1],!i[0])break;o.information("need to dispatch msg:"+r)}i.isNullOrUndefined(n)?n=this.NotifyListener(t):this.NotifyListener(t)}while(0);i.isNullOrUndefined(n)||(e.returnValue=n),t=null})}ListenSendToRendererMsg(e){(e?r.ipcMain:r.ipcRenderer).on(s.CommonIPCMessage.msgIPCSendToRenderer,(t,n)=>{let r=void 0;do{if(i.isNullOrUndefined(n)){o.error("msgInfo is empty");break}if(!this.isConnected){o.warning("hasnot been connected yet");break}let s=n.msg.name;if(this.isIPCModuleIntervalMsg(s)){o.information(`IPC module interval msg : ${s}`);let e=this.handleIPCModuleIntervalMsg(t.sender,n);if(r=e[1],!e[0])break;o.information("need to dispatch msg:"+s)}e?(o.information("is main, handle forward msg"),this.handleForwardRendererToRendererMsg(n)):(o.information("is renderer, handle business msg"),i.isNullOrUndefined(r)?r=this.NotifyListener(n):this.NotifyListener(n))}while(0);i.isNullOrUndefined(r)||(t.returnValue=r),n=null})}isIPCModuleIntervalMsg(e){for(let n of t.intervalIPCModuleMsgs)if(e===n)return!0;return!1}handleIPCModuleIntervalMsg(e,t){let n=[!1,void 0];do{let r=t.msg.name;if(r===s.CommonIPCMessage.msgIPCRendererConnect){n=[!0,this.handleRendererConnectMsg(e,t)];break}if(r===s.CommonIPCMessage.msgIPCRendererDisconnect){n=[!0,this.handleRendererDisconnectMsg(e,t)];break}}while(0);return n}handleRendererConnectMsg(e,t){o.verbose(e),o.verbose(t)}handleRendererDisconnectMsg(e,t){o.verbose(e),o.verbose(t)}handleForwardRendererToRendererMsg(e){this.sendForwardRendererToRendererMsg(e)}sendForwardRendererToRendererMsg(e){o.verbose(e)}NotifyListener(e){let t=void 0,n=e.msg.name;if(this.listeners.has(n)){let r=this.listeners.get(n),i=!0;for(let n of r)i?(i=!1,t=n(e)):n(e)}return t}}e.Communicator=t}(t.CommonIPCBase||(t.CommonIPCBase={}))},29:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(14),i=n(2);t.getDefaultPrex=function(){return i.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=i.basename(process.execPath,".exe"));let o=i.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(o="\\\\.\\pipe\\"+(o=(o=o.replace(/^\//,"")).replace(/\//g,"-"))),o},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},3:function(e,t){e.exports=require("electron")},30:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},31:function(e,t,n){e.exports=n(9)(45)},32:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(8).default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(){let e=!0;{0;let t=r.resolve("C:\\ETW_LOG\\log.ini");e="1"===i.readINI(t,"Log","enable")}return e}e.isDevToolsEnable=function(){return t()},e.isLogEnable=t,e.getLogOutput=function(){let e=process.env.TL_OUTPUT;do{if(e&&""!==e)break;let t=r.resolve("C:\\ETW_LOG\\log.ini");e=i.readINI(t,"Log","output")}while(0);return e}}(t.DevEnvHelperNS||(t.DevEnvHelperNS={}))},33:function(e,t){e.exports=require("net")},34:function(e,t){e.exports=require("url")},35:function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(r||(r={})),t.default=r},36:function(e,t,n){"use strict";var r;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(r||(r={})),e.exports=r},38:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),i=n(6),o=n(22),s=n(23),a=n(28);!function(e){class t extends a.CommonIPCBase.Communicator{constructor(){super()}initialize(e){this.currInfo={id:void 0,context:e,isMainCommunicator:!1}}connect(){this.isConnected?o.warning("has been connected"):(this.sendConnectMsgToMain(),this.isConnected=!0,this.startListenIPCMessage(!1))}disconnect(){this.isConnected?(this.isConnected=!1,this.sendDisconnectMsgToMain()):o.warning("hasnot been connected yet")}sendMessageToMain(e){this.sendIPCMsgToMain(e)}sendMessageToMainSync(e){return this.sendIPCMsgToMain(e,!0)}sendMessageToRenderer(e,t){this.sendIPCMsgToRenderer(e,t)}handleRendererConnectMsg(e,t){do{if(i.isNullOrUndefined(t)){o.error("msgInfo is null");break}let e=t.msg.args[0];if(i.isNullOrUndefined(e)){o.error("connectRendererInfo is null");break}o.information(`Renderer: new renderer will connect, id = ${e.id}, context = ${e.context}`),this.rendererInfos.push(e)}while(0)}handleRendererDisconnectMsg(e,t){do{if(i.isNullOrUndefined(t)){o.error("msgInfo is null");break}let e=t.msg.args[0];if(i.isNullOrUndefined(e)){o.error("disconnectRendererInfo is null");break}o.information(`renderer will disconnect, id = ${e.id}, context = ${e.context}`);for(let t=0;t<this.rendererInfos.length;++t)if(this.rendererInfos[t]===e){this.rendererInfos.splice(t,1);break}}while(0)}sendConnectMsgToMain(){let e=this.sendMessageToMainSync({name:s.CommonIPCMessage.msgIPCRendererConnect,args:[]});this.currInfo.id=e[0],this.rendererInfos=e[1]}sendDisconnectMsgToMain(){this.sendMessageToMain({name:s.CommonIPCMessage.msgIPCRendererDisconnect,args:[]})}sendIPCMsgToMain(e,t=!1){let n=void 0;do{if(i.isNullOrUndefined(e)){o.error("msg is null");break}n=(t?r.ipcRenderer.sendSync:r.ipcRenderer.send)(s.CommonIPCMessage.msgIPCSendToMain,{msg:e,senderInfo:this.currInfo})}while(0);return n}sendIPCMsgToRenderer(e,t){do{if(i.isNullOrUndefined(e)){o.error("rendererId is null");break}if(i.isNullOrUndefined(t)){o.error("msg is null");break}let n=[e].concat(t.args);t.args=n,r.ipcRenderer.send(s.CommonIPCMessage.msgIPCSendToRenderer,{msg:t,senderInfo:this.currInfo})}while(0)}}e.RendererCommunicator=t,e.rendererCommunicator=new t}(t.CommonIPCRenderer||(t.CommonIPCRenderer={}))},39:function(e,t){e.exports=require("crypto")},4:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(12),o=n(50),s=n(29),a=n(30);function l(e){a.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let c=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends i.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,r){do{if(t||(t=s.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(r)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))r("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{r("error",e)}),e.on("connect",()=>{r("connect")}),e.on("end",()=>{let t=e.isInprocess();r("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===s.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let i=new o.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=i,n||(this.client=i),i.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(i,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(i,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(i,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(i,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===i&&this.emit("stat_call_function_back",i.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let r=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,r));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),i.on("error",e=>{r&&r("error",e),this.emit("socket-error",e,i.getContext(),n,i.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),i.isInprocess()?(this.connectedMap[t.toLowerCase()]=i,r&&r("connect"),this.emit("connect",i.getContext(),n,!0)):i.on("connect",()=>{this.connectedMap[t.toLowerCase()]=i,r&&r("connect"),this.emit("connect",i.getContext(),n,!1)}),i.on("end",()=>{let e=i.isInprocess();a.information("server is ended, and this client emit end",t,n,e),r&&r("end",i.getContext(),n,e),this.emit("end",i.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(i)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return r(this,void 0,void 0,function*(){let r=null,i=yield this.crossCallServerFunctionEx(e,t,...n);return i&&(r=i[0]),r})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(r,t,...n)}}crossIsRemoteClientExist(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(r,t,n)}})}crossCallRemoteClientFunction(e,t,n,...r){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(i,t,n,...r)}}crossAttachServerEvent(e,t,n){let r=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");r=this.internalAttachServerEvent(i,t,n)}return r}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(r,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(r,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.generateId();const i={alias:t,action:"register",rid:r};let o=(e,r)=>{e?(a.error("register error",e.message),n(r)):n(t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(a.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===s.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,r)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let r=this.generateId();const i={action:"check_server_function_exist",method:t,rid:r};let o=(e,t)=>{n(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((r,i)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let i=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const o={rid:i,method:t,args:n};let s=(t,n)=>{t?(a.error("callServerFunction error",t,e.getContext()),r([null,t])):r([n,void 0])};this.retCallbackMap[i]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,r)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let r=this.generateId();const i={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:r};let o=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((r,i)=>{do{if(!e){r(!1);break}if(!t){r(!1);break}if(!n){r(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){r(!(!this.apis||!this.apis[n]));break}let i=this.generateId();const o={action:"check_client_function_exist",method:n,rid:i,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let s=(e,t)=>{r(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalCallRemoteClientFunction(e,t,n,...r){return new Promise((i,o)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"remote client alias is not specifed"]);break}if(!n){i([null,"funcName is not specifed"]);break}let o=(e,t)=>{e?(a.information("callRemoteClientFunction",e.message),i([null,e])):i([t,void 0])};if(r)for(let e=0;e<r.length;e++)r[e]=this.convertFunction2IdEx(r[e]);let s=this.generateId();const l={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:r,rid:s};this.retCallbackMap[s]=Object.assign({callback:o},l),this.sendAdapter(e,l)}while(0)})}internalAttachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(r)||(this.eventCallbackMaps[r]={}),this.eventCallbackMaps[r].hasOwnProperty(t)||(this.eventCallbackMaps[r][t]={}),s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let i=this.generateId();return this.eventCallbackMaps[r][t][i]=n,i}internalDetachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;delete this.eventCallbackMaps[r][t][n],s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;let e=this.eventCallbackMaps[r][t];for(let t in e){let r=e[t];r&&r.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...r){let i=void 0,o=!1;do{const s=this.contextCallbackMap[t];if(!s){a.error("the context function has been freeer",t),i={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let l=void 0,c=void 0;try{l=s.apply(null,...r)}catch(e){c=e.message;break}if(void 0===n||null===n)break;if(i={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==c){i.error=c;break}if(l&&l.then){l.then(t=>{i.data=this.convertFunction2IdEx(t),i.success=!0,this.sendAdapter(e,i)}).catch(t=>{i.error=t instanceof Error?t.message:t,this.sendAdapter(e,i)}),o=!0;break}i.success=!0,i.data=this.convertFunction2IdEx(l)}while(0);!o&&i&&this.sendAdapter(e,i)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":r}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let r=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(r){n=((...e)=>{this.callRemoteContextById(t,r,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:r},l);break}for(let n in e){let r=e[n];e[n]=this.decodeParameter(r,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,r=!1;do{if(!t)break;let i=t.method;if(!i)break;let o=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(o):[].concat(o)},!this.apis||!this.apis[i]){n.error=`callRegisterFunction ${i} is undefined`;break}let s=void 0,a=this.decodeParameter(t.args,t.src);try{s=this.apis[i].apply(null,[t.src].concat(a))}catch(e){n.error=e.message;break}if(s&&s.then){s.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),r=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(s)}while(0);a.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(e,n)}}),c=global.__xdasIPCClienInstance,t.client=c},40:function(e,t){e.exports=require("https")},41:function(e,t){e.exports=require("buffer")},42:function(e,t,n){"use strict";const r=n(13);if("renderer"===process.type){if(r.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(53);e.require=t.remoteRequire,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,r)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,r)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(r.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(57);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},43:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(6),i=n(22),o=n(23),s=n(28),a=n(38);!function(e){let t;!function(e){e[e.Unknown=-1]="Unknown",e[e.Success=0]="Success",e[e.FunctionNotExist=1]="FunctionNotExist",e[e.ParamaterError=2]="ParamaterError",e[e.CallFailed=3]="CallFailed"}(t=e.NativeCallErrorCode||(e.NativeCallErrorCode={}));class n{constructor(e,t,n){this.maxId=e,this.minId=t,this.invalidId=n}generateId(){return this.minId===this.maxId?this.invalidId:this.minId++}isInvalidId(e){return e===this.invalidId}}e.IdGenerator=n;const l=0;e.idGenerator=new n(1e7,1,l);class c{constructor(){this.jsCallbacks=new Map,this.jsReturnCallbacks=new Map,this.eventJsCallbakcs=new Map,this.jsRegisterFunctions=new Map,this.targetCommunitorInfo=a.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfoByContext(s.CommonIPCBase.mainRendererContext),this.bindMsgListeners(),this.notifyNativeCallReady()}CallNativeFunction(t,...n){do{if(r.isNullOrUndefined(t)||0===t.length){i.error("funcName is empty");break}if(!this.targetCommunitorInfo){i.error("CallNativeFunction but targetCommunitorInfo null");break}i.information("funcName = ",t),this.printArgs(n);let s=l;for(let t=0;t<n.length;++t)if(r.isFunction(n[t])){let r=e.idGenerator.generateId(),i=n[t];t===n.length-1?(this.jsReturnCallbacks.set(r,i),s=r,n.pop()):(this.jsCallbacks.set(r,i),n[t]=r)}a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:o.CommonIPCMessage.msgNCCallNativeFunction,args:[t,s].concat(n)})}while(0)}AttachNativeEvent(t,n){let o=void 0;do{if(r.isNullOrUndefined(t)||0===t.length){i.error("eventName is empty");break}if(r.isNullOrUndefined(n)){i.error("callback is empty");break}let s=e.idGenerator.generateId();if(e.idGenerator.isInvalidId(s)){i.error("id error");break}if(this.eventJsCallbakcs.has(t))this.eventJsCallbakcs.get(t).set(s,n);else{let e=new Map;e.set(s,n),this.eventJsCallbakcs.set(t,e)}o=s}while(0);return o}DetachNativeEvent(e,t){do{if(r.isNullOrUndefined(e)||0===e.length){i.error("eventName is empty");break}if(r.isNullOrUndefined(t)){i.error("callback is empty");break}if(!this.eventJsCallbakcs.has(e)){i.error(`event: ${e} doesnot have listener`);break}if(!this.eventJsCallbakcs.get(e).has(t)){i.error(`event: ${e} doesnot have the listener of id=${t}`);break}this.eventJsCallbakcs.get(e).delete(t)}while(0)}CheckNativeFunction(t,n){do{if(r.isNullOrUndefined(t)||0===t.length){i.error("funcName is empty");break}if(r.isNullOrUndefined(n)){i.error("callback is empty");break}if(!this.targetCommunitorInfo){i.error("CheckNativeFunction but targetCommunitorInfo null");break}i.information("funcName = ",t);let s=e.idGenerator.generateId();this.jsReturnCallbacks.set(s,n),a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:o.CommonIPCMessage.msgNCCheckNativeFunction,args:[t,s]})}while(0)}RegisterJSFunction(e,n){let o=t.ParamaterError;do{if(r.isNullOrUndefined(e)||0===e.length){i.error("funcName is empty");break}if(r.isNullOrUndefined(n)){i.error("jsFunc is empty");break}this.jsRegisterFunctions.set(e,n),o=t.Success}while(0);return o}bindMsgListeners(){a.CommonIPCRenderer.rendererCommunicator.onMessage(o.CommonIPCMessage.msgNCCallJsFunctionById,e=>{this.handleCallJsFunctionById(e.msg.args)}),a.CommonIPCRenderer.rendererCommunicator.onMessage(o.CommonIPCMessage.msgNCCallJsFunctionByName,e=>{this.handleCallJsFunctionByName(e.msg.args)}),a.CommonIPCRenderer.rendererCommunicator.onMessage(o.CommonIPCMessage.msgNCNativeFireEvent,e=>{this.handleNativeFireEvent(e.msg.args)})}handleCallJsFunctionById(t){do{let n=t[0];if(!r.isNumber(n)){i.error(`id error id = ${n}`);break}if(e.idGenerator.isInvalidId(n)){i.error(`id = ${n} invalid`);break}let o=null,s=0;if(this.jsCallbacks.has(n)&&(s=1,o=this.jsCallbacks.get(n)),this.jsReturnCallbacks.has(n)&&(s=2,o=this.jsReturnCallbacks.get(n)),0===s){i.error(`callbacks[${n}] is null`);break}t.splice(0,1),o.apply(null,t),2===s&&this.jsReturnCallbacks.delete(n)}while(0)}handleCallJsFunctionByName(e){do{let t=e[0];if(!r.isString(t)){i.error(`funcName error funcName = ${t}`);break}if(!this.jsRegisterFunctions.has(t)){i.error(`jsRegisterFunctions[${t}] is null`);break}e.splice(0,1),this.jsRegisterFunctions.get(t).apply(null,e)}while(0)}handleNativeFireEvent(e){do{let t=e[0];if(!r.isString(t)){i.warning(`eventName error eventName = ${t}`);break}if(!this.eventJsCallbakcs.has(t)){i.warning(`eventJsCallbakcs[${t}] is null`);break}e.shift(),this.eventJsCallbakcs.get(t).forEach((t,n,o)=>{i.information(`value = ${t}, key = ${n}, map = ${o}`),r.isNullOrUndefined(t)||t.apply(null,e)})}while(0)}notifyNativeCallReady(){do{if(!this.targetCommunitorInfo){i.error("notifyNativeCallReady but targetCommunitorInfo null");break}a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:o.CommonIPCMessage.msgNCNativeCallReady,args:[a.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfo()]})}while(0)}printArgs(e){for(let t in e)i.information(`index ${t} = `,e[t])}}e.NativeCallImpl=c,e.nativeCall=new c}(t.NativeCallModule||(t.NativeCallModule={}))},44:function(e,t){e.exports=require("http")},45:function(e,t,n){"use strict";var r=n(10),i=n(118),o={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a,l={adapter:("undefined"!=typeof XMLHttpRequest?a=n(119):"undefined"!=typeof process&&(a=n(17)),a),transformRequest:[function(e,t){return i(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(s(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};l.headers={common:{Accept:"application/json, text/plain, */*"}},r.forEach(["delete","get","head"],function(e){l.headers[e]={}}),r.forEach(["post","put","patch"],function(e){l.headers[e]=r.merge(o)}),e.exports=l},46:function(e,t,n){"use strict";var r=n(68);e.exports=function(e,t,n,i,o){var s=new Error(e);return r(s,t,n,i,o)}},47:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(51),i=n(12);t.Parser=class extends i.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),i=JSON.parse(e);this.emit("message",i),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},473:function(e,t,n){"use strict";n.r(t);var r=n(474),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},474:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,s=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(o<3?i(s):o>3?i(t,n,s):i(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(7),s=n(5),a=n(43),l=n(4),c=n(139),u=n(971),d=n(1),f=n(16);n(64).FixTextScale.fixZoomFactory();const h=d.default.getLogger("ModifierUserInfo"),p=n(231);let m=class extends s.Vue{constructor(){super(...arguments),this.preModifierUserInfo={},this.lastModifierUserInfo={},this.curUserAccount="",this.curUserNick="",this.curUserSex="",this.curUserBirthdayYear="",this.curUserBirthdayMonth="",this.curUserBirthdayDay="",this.curUserCityP="",this.curUserCity="",this.curUserHeader="",this.curUserid="",this.picked="man",this.monthList=[],this.yearList=[],this.cityListJson=null,this.cityPList=[],this.okBtnStr="确定",this.disabled=!1,this.isShowTipsNick=!1,this.isNickWarn=!1,this.nickTipsText="",this.isVip=0,this.messageType="",this.messageContent=""}get dayList(){let e=[],t=0,n=0,r=this.curUserBirthdayYear.match(/^[0-9]+/);null!==r&&void 0!==r&&(t=Number(r[0])),null!==(r=this.curUserBirthdayMonth.match(/^[0-9]+/))&&void 0!==r&&(n=Number(r[0]));let i=31;if(4===n||6===n||9===n||11===n?i=30:2===n&&(i=28,this.isLeapYear(t)&&(i=29)),null!==(r=this.curUserBirthdayDay.match(/^[0-9]+/))&&void 0!==r){Number(r[0])>i&&(this.curUserBirthdayDay="01日")}for(let t=1;t<=i;++t){let n="";n=t<10?"0"+t+"日":t+"日",e.push(n)}return e}get cityList(){let e=[],t=this.curUserCityP;if(""!==t&&null!==this.cityListJson){let n=this.cityListJson[t];if(null!==n&&void 0!==n){for(let t in n)e.push(t);let t=n[this.curUserCity];null!==t&&void 0!==t&&""!==t||e.length>0&&(this.curUserCity=e[0])}}return e}isLeapYear(e){let t=!1;return e<=0?t=!1:e%100==0?e%400==0&&(t=!0):e%4==0&&(t=!0),t}calculateChineseCount(e){let t=0,n=e.match(/[\u4E00-\u9FFF]/g);return null!==n&&(t=n.length),t}handleInputChange(e){if(this.isNullNick(e))this.isShowTipsNick=!0,this.isNickWarn=!0,this.nickTipsText="昵称不能为空";else{let t=this.calculateChineseCount(e);2*t+(e.length-t)>30?(this.isShowTipsNick=!0,this.isNickWarn=!0,this.nickTipsText="昵称仅支持输入最多30个字符"):(this.isShowTipsNick=!1,this.isNickWarn=!1,this.nickTipsText="")}}showTips(e,t){this.messageType=t,this.messageContent=e,this.$refs.message.show()}clickHeader(){this.statClient("basicinfo_head_click"),l.client.callServerFunction("OpenNewTab","https://i.xunlei.com/xluser/account/acc_info.html"),window.close()}clickMoreSet(){this.statClient("basicinfo_moreset_click");l.client.callServerFunction("OpenNewTab","https://i.xunlei.com/xluser/home.html").catch(),this.closeWnd("more")}closeWnd(e){"closeBtn"===e?this.statClient("basicinfo_close_click"):"cancel"===e&&this.statClient("basicinfo_cancel_click"),window.close()}okClick(){this.isShowTipsNick||(this.disabled=!0,this.showTips("正在提交...","warning"),this.statClient("basicinfo_confirm_click"),this.setLastModifierUserInfo(),this.lastModifierUserInfo.nickName===this.preModifierUserInfo.nickName&&this.lastModifierUserInfo.sex===this.preModifierUserInfo.sex&&this.lastModifierUserInfo.birthday===this.preModifierUserInfo.birthday&&this.lastModifierUserInfo.year===this.preModifierUserInfo.year&&this.lastModifierUserInfo.month===this.preModifierUserInfo.month&&this.lastModifierUserInfo.day===this.preModifierUserInfo.day&&this.lastModifierUserInfo.province===this.preModifierUserInfo.province&&this.lastModifierUserInfo.city===this.preModifierUserInfo.city?(this.disabled=!1,this.showTips("没有用户信息修改","warning")):this.isNullNick(this.lastModifierUserInfo.nickName)?(this.disabled=!1,this.showTips("昵称不能为空","warning")):u.modifierUserinfoHelper.updateModifierUserinfo(this.lastModifierUserInfo).then(e=>{e?(this.disabled=!1,this.showTips("提交成功！","success"),a.NativeCallModule.nativeCall.CallNativeFunction("NativeFireEvent","onUserDetailInfoChange",JSON.stringify(this.lastModifierUserInfo)),setTimeout(()=>{this.closeWnd("succ")},500)):(this.disabled=!1,this.showTips("提交失败！","error"))}).catch())}setLastModifierUserInfo(){this.lastModifierUserInfo.nickName=f.ThunderUtil.unescapeHTML(this.curUserNick),this.lastModifierUserInfo.sex=this.picked,this.lastModifierUserInfo.year=this.curUserBirthdayYear,this.lastModifierUserInfo.month=this.curUserBirthdayMonth,this.lastModifierUserInfo.day=this.curUserBirthdayDay,this.lastModifierUserInfo.province=this.curUserCityP,this.lastModifierUserInfo.city=this.curUserCity,this.lastModifierUserInfo.header=this.curUserHeader,this.lastModifierUserInfo.birthday=this.getBirthday()}isNullNick(e){let t=!1;if(null===e||void 0===e||""===e)t=!0;else{let n=e.trim();null!==n&&void 0!==n&&""!==n||(t=!0)}return t}getBirthday(){let e="",t="";e=t=this.curUserBirthdayYear.substring(0,4);let n=this.curUserBirthdayMonth.lastIndexOf("月");return t=this.curUserBirthdayMonth.substring(0,n),Number(t)<=9&&(t="0"+t),e+=t,e+=t=this.curUserBirthdayDay.substring(0,2)}getBirthdayInfo(e){if(""===e||void 0===e||null===e)return;this.preModifierUserInfo.birthday=e;let t="";t=e.substring(0,4)+"年",this.curUserBirthdayYear=t,this.preModifierUserInfo.year=t,t=Number(e.substring(4,6))+"月",this.curUserBirthdayMonth=t,this.preModifierUserInfo.month=t,t=e.substring(6,8)+"日",this.curUserBirthdayDay=t,this.preModifierUserInfo.day=t}getProvince(e){let t=e;if(""===(t=u.modifierUserinfoHelper.getMapProvinceOrCityValue("province",e))||null===t||void 0===t){let n="省",r="市",i=e.lastIndexOf(n);t=i<=0?(i=e.lastIndexOf(r))<=0?e:e.length===i+r.length?e.substring(0,i):e:e.length===i+n.length?e.substring(0,i):e}return this.curUserCityP=t,t}init(){for(let e=1;e<=12;++e){let t=e+"月";this.monthList.push(t)}let e=(new Date).getFullYear();(null===e||void 0===e||e<2018)&&(e=2018);for(let t=1901;t<=e;++t){let e=t+"年";this.yearList.push(e),t%10==0&&this.yearList.push("separator")}u.modifierUserinfoHelper.getCityListStrAw().then(e=>{try{this.cityListJson=JSON.parse(e)}catch(e){h.warning(e)}for(let e in this.cityListJson)this.cityPList.push(e)}).catch()}getCurUserHeader(){this.curUserHeader=p,this.preModifierUserInfo.header=p,a.NativeCallModule.nativeCall.CallNativeFunction("GetUserHeaderByUserID",this.curUserid,(e,t)=>{0===e&&null!==t&&void 0!==t&&""!==t&&(this.curUserHeader=t,this.preModifierUserInfo.header=t)})}statClient(e){let t=`is_login=1,is_vip=${this.isVip}`;h.information(e,t),l.client.callServerFunction("TrackEvent","user_info",e,"",0,0,0,0,t).catch()}mounted(){return i(this,void 0,void 0,function*(){this.init(),l.client.callServerFunction("GetAllUserInfo").then(e=>{if(null!==e&&void 0!==e){if(this.curUserid=e.userID,null!==e.vipList&&void 0!==e.vipList&&e.vipList.length>0){let t=e.vipList[0];null!==t&&void 0!==t&&(this.isVip=Number(t.isVip))}this.curUserAccount=e.userNewNo,this.curUserNick=f.ThunderUtil.unescapeHTML(e.nickName),this.preModifierUserInfo.nickName=this.curUserNick,this.curUserSex=e.sex,this.getBirthdayInfo(e.birthday),this.preModifierUserInfo.province=this.getProvince(e.province),this.curUserCity=e.city,this.preModifierUserInfo.city=e.city,this.getCurUserHeader(),"f"===e.sex||"female"===e.sex?(this.picked="woman",this.preModifierUserInfo.sex="woman"):(this.picked="man",this.preModifierUserInfo.sex="man")}this.statClient("basicinfo_show")}).catch(),(yield o.asyncRemoteCall.getCurrentWindow()).hookWindowMessage(528,()=>{this.isShowTipsNick=!1})})}destroyed(){}};m=r([s.Component({components:{SelectNative:c.default}})],m),t.default=m},48:function(e,t){e.exports=require("zlib")},49:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(6),o=n(1),s=n(8),a=n(32),l=n(3),c=n(20),u=s.default(r.join(__rootDir,"../bin/ThunderHelper.node"));function d(){"console"===process.env.TL_OUTPUT?o.default.outputLogger=o.outputLoggerConsole:o.default.outputLogger=function(){function e(e){return function(...t){u.printEtwLog(e,function(...e){return e.map(e=>i.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[o.LogLevel.Critical]:e(o.LogLevel.Critical),[o.LogLevel.Error]:e(o.LogLevel.Error),[o.LogLevel.Warning]:e(o.LogLevel.Warning),[o.LogLevel.Information]:e(o.LogLevel.Information),[o.LogLevel.Verbose]:e(o.LogLevel.Verbose)}}()}function f(){let e=a.DevEnvHelperNS.isLogEnable();"1"===process.env.TL_ENABLE!==e&&(process.env.TL_ENABLE=e?"1":"0",o.default.enableLogger=e,u.enableETWLogger(e));let t=a.DevEnvHelperNS.getLogOutput();t&&t!==process.env.TL_OUTPUT&&(process.env.TL_OUTPUT=t,d())}process.env.TL_ENABLE="0",o.default.enableLogger="1"===process.env.TL_ENABLE,d(),f(),"browser"===process.type?l.ipcMain.on(c.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{l.BrowserWindow.getAllWindows().forEach(e=>{e.isDestroyed()||e.webContents.send(c.ThunderChannelList.channelMRUpdateLogEnviroment)}),f()}):"renderer"===process.type&&l.ipcRenderer.on(c.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{f()})},5:function(e,t,n){e.exports=n(9)(213)},50:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(33),i=n(12),o=n(30),s=n(47),a=n(29);t.Client=class extends i.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=a.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new s.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{o.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){o.error(e.message)}else o.information("This socket has been ended by the other party",this.context&&this.context.name)}}},51:function(e,t){e.exports=require("string_decoder")},52:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(14),o=(n(21),n(2)),s=n(6);let a=null;const l=n(11),c=n(3),u=n(1),d=n(8),f=n(20),h=n(7),p="xdas_profile_stat";let m="",g=void 0,v=null,_=void 0,y=null,b=d.default(o.join(__rootDir,"../bin/ThunderHelper.node")),C=new Set;function R(){return r(this,void 0,void 0,function*(){return new Promise(e=>r(this,void 0,void 0,function*(){void 0===_&&(null===y&&(y=new Promise(e=>{e(_=function(e){let t="";if(0===e.length&&"renderer"===process.type){let e=o.normalize(decodeURIComponent(window.location.href)),n=e.indexOf(process.resourcesPath);n=n>-1?n+process.resourcesPath.length+1:n;let r=e.length-1,i=e.indexOf("?"),s=e.indexOf("#");r=i>-1?Math.min(i-1,r):r,r=s>-1?Math.min(s-1,r):r,n>-1&&r>=n&&(t=e.substr(n,r-n+1))}return 0===t.length&&(t=0!==e.length?e:process.type),t=t.replace(/\||,|;/g,"_")}(""))})),_=yield y),e(_)}))})}function w(e){let t="";do{if(null===e||void 0===e)break;switch(typeof e){case"string":t=e;break;case"object":t=s.inspect(e)||"";break;case"number":case"boolean":t=e.toString()||""}}while(0);return t}function E(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function S(e){return r(this,void 0,void 0,function*(){return new Promise(t=>r(this,void 0,void 0,function*(){let r=void 0;null===a&&(a=yield Promise.resolve().then(()=>n(39)));let i=a.createHash("md5");null!==i&&(r=i.update(e).digest("hex")),t(r)}))})}function T(){return new Promise(e=>r(this,void 0,void 0,function*(){let t="";t=void 0===g?"browser"===process.type?function(){if(void 0===g){let e=process.argv.length,t=process.argv;for(let n=0;n<e;n++){let e=t[n];if(e.startsWith("-StartType:")){g=e.substring("-StartType:".length);break}}void 0===g&&(g="")}return g}():yield function(){return r(this,void 0,void 0,function*(){return null===v&&(v=new Promise(e=>{c.ipcRenderer.send(f.ThunderChannelList.channelRMGetBrowserStartType),c.ipcRenderer.once(f.ThunderChannelList.channelMRGetBrowserStartTypeResult,(t,n)=>{g=n,e(n),v=null})})),v})}():g,e(t)}))}function M(e,t,n,i){return r(this,void 0,void 0,function*(){let o=w(t),s=w(n),a=yield S(s),c=function(e){let t=new RegExp(E("file:///"),"g"),n=new RegExp(E(process.resourcesPath+"\\"),"g"),r=new RegExp(E(encodeURI(process.resourcesPath.replace(/\\/g,"/")+"/")),"g");return e.replace(t,"").replace(n,"").replace(r,"")}(w(i)),u=yield S(c),d=`${e}:${a}:${u}`;C.has(d)||(C.add(d),l.XLStatNS.trackEvent(p,"uncaught_exception","",0,0,0,0,`type=${e},business_name=${yield R()},code=${o},message_hash=${a},message=${encodeURI(s)},stack_hash=${u},stack=${encodeURI(c)}`)),function(e,t,n,i){return r(this,void 0,void 0,function*(){})}().catch()})}function k(e){console.error(e);let t=e||{};M("unhandledRejection",t.code,t instanceof Error?t.message:t,t.stack).catch()}!function(e){e.init=function(e){m=e},e.trackColdStartUpEvent=function(e){return r(this,void 0,void 0,function*(){let t=b.iSColdStartUp()?1:0,n=i.release(),r=b.performanceMonitorReporter.getProcessUptime(),o=yield T(),s=`key=${e},start_type=${o},cold_start_up=${t},os_version=${n},cost_time=${r}`;l.XLStatNS.trackEvent(p,"cold_start_up","",0,0,0,0,s)})}}(t.PerformanceMonitorStatNS||(t.PerformanceMonitorStatNS={})),function(){return r(this,void 0,void 0,function*(){if(process.on("uncaughtException",e=>{console.error(e);let t=e||{};M("uncaughtException",t.code,t.message,t.stack).catch()}),"browser"===process.type)process.on("unhandledRejection",(e,t)=>{k(e)}),c.ipcMain.on(f.ThunderChannelList.channelRMGetBrowserStartType,function(e){return r(this,void 0,void 0,function*(){let t=yield T();e.sender.send(f.ThunderChannelList.channelMRGetBrowserStartTypeResult,t)})});else if("browser"!==process.type){window.addEventListener("unhandledrejection",e=>{k(e&&e.reason||{})});let e=yield h.asyncRemoteCall.getCurrentWebContents();null!==e&&void 0!==e&&e.once("did-finish-load",()=>{(function(){return r(this,void 0,void 0,function*(){do{if("browser"===process.type)break;if(null===window.performance.timing||void 0===window.performance.timing)break;let e=b.iSColdStartUp()?1:0,t=i.release(),n=window.performance.timing,r=n.loadEventEnd-n.navigationStart,o=n.fetchStart-n.navigationStart,s=n.domainLookupEnd-n.domainLookupStart,a=n.connectEnd-n.connectStart,c=n.responseStart-n.requestStart,u=n.responseEnd-n.responseStart,d=n.domComplete-n.domLoading,f=yield T();l.XLStatNS.trackEvent(p,"page_load_time","",0,0,0,0,`start_type=${f},cold_start_up=${e},os_version=${t},load_time=${r},before_fetch_time=${o},domin_lookup_time=${s},connect_time=${a},first_response_time=${c},responseTime=${u},domTime=${d},process=${m}`)}while(0)})})().catch()})}u.default.hook("beforeLog",(e,t,...n)=>{e===u.LogLevel.Critical&&l.XLStatNS.trackEvent(p,"critical_error","",0,0,0,0,`module_name=${t},messages=${n}`)})})}().catch()},53:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const r=n(3),i=n(41),o=n(54),s=n(55),a=n(35),l=n(56),c=n(13),u=n(36),d=r.ipcRenderer,f=process.electronBinding("v8_util"),h=new o.default,p=f.createIDWeakMap(),m=f.getHiddenValue(global,"contextId");class g{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function v(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let r=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:v(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(r))return{type:"buffer",value:i.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(u.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(f.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:f.getHiddenValue(e,"__remote_id__")};let r={type:e instanceof g?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)r.members.push({name:t,value:n(e[t])});return t.delete(e),r}if("function"==typeof e){return{type:"function",id:h.add(e),location:f.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function _(e,t,n){u.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function y(e,t,n,r=!1){const i=t=>{if(e.hasOwnProperty(t.name)&&!r)return;let n,i={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(p.has(t.value.refId)&&(n=p.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=w(t.value,e,t.name);i.get=(()=>n),i.set=(e=>n=e)}else"get"===t.type&&(i.get=(()=>n),t.writable&&(i.set=(e=>{n=e})),n=w(t.value));Object.defineProperty(e,t.name,i)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)i(n[t])}}function b(e,t,n){if(n){let t=w(n);Object.setPrototypeOf(e,t)}}function C(e,t){f.setHiddenValue(e,"__remote_id__",t)}function R(e){return f.getHiddenValue(e,"__remote_id__")}function w(e,t,n){const r={value:()=>e.value,array:()=>e.members.map(e=>w(e)),buffer:()=>i.Buffer.from(e.value),promise:()=>Promise.resolve({then:w(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:r,value:i}=e.members[n];t[r]=i}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let o;return e.type in r?o=r[e.type]():e.refId?p.has(e.refId)?(f.addRemoteObjectRef(m,e.refId),o=p.get(e.refId)):(c.warn("[metaToValue] refId point to null"+e.refId),o="function"===e.type?()=>{}:{}):e.id?p.has(e.id)?(f.addRemoteObjectRef(m,e.id),y(o=p.get(e.id),e.id,e.members,!0),b(o,e.id,e.proto)):(o="function"===e.type?t?function(e,t,n){if(p.has(n.id))return p.get(n.id);let r=R(e),i=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(i,"name",{value:t,writable:!1,enumerable:!0});let o=new Proxy(i,{apply:(e,n,i)=>new Promise((e,o)=>{let c=R(n);if(c||(c=R(n.__remoteObj_)),!c)throw Error("is this function was a bound function?");let u=a.default.browser.memberCall,f=l.default(u),h=v(i);d.send(u,m,f,c,r,t,h),s.default.add(f,t=>{try{_(w(t),e,o)}catch(e){o(e)}})}),construct:(e,n,i)=>new Promise((e,i)=>{let o=a.default.browser.memberConstruct,c=l.default(o);d.send(o,m,c,r,t,v(n)),s.default.add(c,t=>{try{let n=w(t);e(n)}catch(e){i(e)}})})});return f.setHiddenValue(i,"__remote_id__",n.id),o}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return C(n,t),new Proxy(n,{apply:(e,n,r)=>new Promise((e,i)=>{let o=a.default.browser.functionCall,c=l.default(o),u=R(n);d.send(o,m,c,u,t,v(r)),s.default.add(c,t=>{try{_(w(t),e,i)}catch(e){i(e)}})}),construct:(e,n,r)=>new Promise((e,r)=>{let i=a.default.browser.construct,o=l.default(i);d.send(i,m,o,t,v(n)),s.default.add(o,t=>{try{let n=w(t);e(n)}catch(e){r(e)}})})})}(e):{},f.setRemoteObjectFreer(o,m,e.id),p.set(e.id,o),f.setHiddenValue(o,"__remote_id__",e.id),f.addRemoteObjectRef(m,e.id),function(e){let t=R(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,r){if("function"==typeof r)throw new Error("set a function to a remote member is dangerous");return new Promise((i,o)=>{let c=a.default.browser.memberSet,u=l.default(c),f=v([r]);d.send(c,m,u,t,n,f),s.default.add(u,t=>{try{let s=w(t);e[n]=r,i(s)}catch(e){o(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((r,i)=>{let o=a.default.browser.memberGet,c=l.default(o);d.send(o,m,c,t,n),s.default.add(c,t=>{try{const o=w(t);e[n]=o,r(o)}catch(e){i(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let r=a.default.browser.sync,i=l.default(r);d.send(r,m,i,t),s.default.add(i,r=>{try{if(r.id!==t)throw Error("SYNC_RETURN: remote id not match");let i=w(r);e(i)}catch(e){n(e)}})})}}})}(o),y(o,e.id,e.members),b(o,e.id,e.proto),Object.defineProperty(o.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=o[e],r=o.__proto__;for(;r;){if(Object.prototype.hasOwnProperty.call(r,e)){delete r[e];break}r=r.__proto__}Object.defineProperty(o,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):c.error("no id of meta:",e),o}t.Interest=g;class E{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,r)=>{let i=this.getChannel(e),o=l.default(i);d.send(i,m,o,t),s.default.add(o,e=>{try{let t=w(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){r(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=a.default.browser.require;break;case"builtin":t=a.default.browser.builtIn;break;case"global":t=a.default.browser.global;break;case"current_window":t=a.default.browser.currentWindow;break;case"current_web_contents":t=a.default.browser.currentWebContents;break;case"client_web_contents":t=a.default.browser.clientWebContents;break;case"web_contents":t=a.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function S(e,t,n){try{s.default.invoke(t,n).remove(t)}finally{s.default.remove(t)}}d.on(a.default.renderer.requireReturn,S),d.on(a.default.renderer.getBuiltInReturn,S),d.on(a.default.renderer.getGlobalReturn,S),d.on(a.default.renderer.currentWindowReturn,S),d.on(a.default.renderer.currentWebContentsReturn,S),d.on(a.default.renderer.functionCallReturn,S),d.on(a.default.renderer.constructReturn,S),d.on(a.default.renderer.memberCallReturn,S),d.on(a.default.renderer.memberSetReturn,S),d.on(a.default.renderer.memberGetReturn,S),d.on(a.default.renderer.memberConstructReturn,S),d.on(a.default.renderer.callback,(e,t,n)=>{h.apply(t,w(n))}),d.on(a.default.renderer.syncReturn,S),d.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{c.info("[RELEASE_CALLBACK]: callbackId:",t),h.remove(t)}),process.on("exit",()=>{d.send(a.default.browser.contextRelease)});const T=["__resolve","__isResolved"],M=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],k=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function x(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(k(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(k(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(k(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(k(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(k(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(k(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,r)=>(k(e),Reflect.defineProperty(e.__remoteObj_,n,r)),get:(t,n,r)=>{if("string"==typeof n){if(String.prototype.includes.call(M,n)){return e[n]}if(String.prototype.includes.call(T,n)){return e[n]}}return k(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,r,i)=>(k(e),Reflect.set(e.__remoteObj_,n,r,i)),ownKeys:t=>(k(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,r)=>{k(e),Reflect.apply(e.__remoteObj_,n,r)},construct:(t,n,r)=>{if(k(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,r)=>{let i=a.default.browser.construct,o=l.default(i),c=f.getHiddenValue(e.__remoteObj_,"__remote_id__");d.send(i,m,o,c,v(n)),s.default.add(o,e=>{try{t(w(e))}catch(e){r(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=R(e);t&&C(n,t)}}),n}t.remoteRequire=function(e){return x(new E("module",e))},t.getBuiltin=function(e){return x(new E("builtin",e))},t.getGlobal=function(e){return x(new E("global",e))},t.getCurrentWindow=function(){return x(new E("current_window"))},t.getCurrentWebContents=function(){return x(new E("current_web_contents"))},t.getWebContents=function(){return x(new E("web_contents"))}},54:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=r.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,i=(new Error).stack;let o,s=n.exec(i);for(;null!==s;){const e=s[1];if(!e.includes("native")&&!e.includes("electron.asar")){o=/([^/^)]*)\)?$/gi.exec(e)[1];break}s=n.exec(i)}return this.callbacks[t]=e,r.setHiddenValue(e,"__remote_callback_id__",t),r.setHiddenValue(e,"__remote_call_location__",o),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(r.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}},55:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13);var i;!function(e){let t={};e.add=function(e,n,r){t[e]={func:n,thisArg:r}},e.invoke=function(n,...i){let o=t[n];return o?o.thisArg?o.func.apply(o.thisArg,...i):o.func(...i):r.error(`Cannot invoke function by unrecognize id. ${n}`),e},e.remove=function(e){delete t[e]}}(i||(i={})),t.default=i},56:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=0;t.default=function(e){return e?e.concat(".").concat(String(++r)):String(++r)}},57:function(e,t,n){"use strict";const r=n(3),i=n(58),o=n(35),s=n(59),a=n(13),l=n(36),c=r.ipcMain,u=process.electronBinding("v8_util");let d=u.createDoubleIDWeakMap();const f=new i.default;function h(e,t,n,r,i,o){let s,l=!1,c=null,u=!1;do{try{s=t[r]}catch(e){l=!0}if(l)try{s=n.value[r],l=!1,n.meta.extendedMemberNames.push(r),u=!0}catch(e){a.error(`property ${r} untouchable, even try root[name]`)}if(l)break;let i=Object.getOwnPropertyDescriptor(t,r);if(void 0===i){a.warn(`descriptor of property ${r} is undefined`);break}c={name:r,enumerable:i.enumerable,writable:!1,type:"get"},void 0===i.get&&"function"==typeof s?c.type="method":((i.set||i.writable)&&(c.writable=!0),c.type="get"),u?(c.configurable=!0,c.value=g(e,s,o,!1,null)):c.value=g(e,s,o,!1,n)}while(0);return c}function p(e,t,n,r=null){let i=Object.getOwnPropertyNames(t);"function"==typeof t&&(i=i.filter(function(e){return!String.prototype.includes.call(s.propertiesOfFunction,e)}));let o=[];do{if(0===i.length)break;let s=i.length;for(let a=0;a<s;a++){let s=h(e,t,n,i[a],0,r);s&&o.push(s)}}while(0);return o}function m(e,t,n,r=null){let i=null,o=Object.getPrototypeOf(t);return i=null===o||o===Object.prototype||o===Function.prototype?null:g(e,o,r,!1,n)}function g(e,t,n=null,r=!1,i=null){n=null===n?{}:n;const o={type:typeof t};"object"===o.type&&(o.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":l.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&u.getHiddenValue(e,"simple")&&(n="value"),n}(t,r));do{if("object"===o.type||"function"===o.type){let r=f.getIdOfObject(t);if(r&&n[r]){o.refId=r,f.add(e,t);break}}"array"===o.type?o.members=t.map(t=>g(e,t,n)):"object"===o.type||"function"===o.type?(null==i&&(o.extendedMemberNames=[],i={value:t,meta:o}),o.name=t.constructor?t.constructor.name:"",o.id=f.add(e,t),n[o.id]=!0,o.members=p(e,t,i,n),o.proto=m(e,t,i,n)):"buffer"===o.type?o.value=Buffer.from(t):"promise"===o.type?(t.then(function(){},function(){}),o.then=g(e,function(e,n){t.then(e,n)})):"error"===o.type?(o.members=v(t),o.members.push({name:"name",value:t.name})):"date"===o.type?o.value=t.getTime():(o.type="value",o.value=t)}while(0);return o}function v(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function _(e,t,n,i){const s=function(i){let l,c,h=0,p=0;switch(i.type){case"value":return i.value;case"remote-object":return f.get(i.id);case"array":return _(e,t,n,i.value);case"buffer":return Buffer.from(i.value);case"date":return new Date(i.value);case"promise":return Promise.resolve({then:s(i.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:i.name}),h=0,p=(c=i.members).length;h<p;h++)e[(l=c[h]).name]=s(l.value);return e}case"function":{const s=e.id,l=[n,i.id];if(a.info("renderer function id:"+l),d.has(l))return d.get(l);let c=function(...t){a.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||s!==e.id?function(e,t,n){let i="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof r.webContents.constructor){const{sender:e}=t[0],r=e.eventNames().filter(t=>{let r=e.listeners(t),i=!1;return r.forEach(e=>{e===n&&(i=!0)}),i});r.length>0&&(i+=`\nRemote event names: ${r.join(", ")}`,r.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}a.warn(i)}(i,n,c):e.send(o.default.renderer.callback,i.id,g(e,n))};return Object.defineProperty(c,"length",{value:i.length}),u.setRemoteCallbackFreer(c,t,n,i.id,e),d.set(l,c),c}default:throw new TypeError(`Unknown type: ${i.type}`)}};return i.map(s)}function y(e,t,n,r){let i,o;try{return t.apply(n,r)}catch(e){return o=t.name,new Error(`Could not call remote function '${i=null!=o?o:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function b(e){return{type:"exception",message:e.message,stack:e.stack||e}}function C(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var R;!function(e){const t=(e,t,...n)=>{const r=e.sender;r.isDestroyed()?a.warn("webcontext is destroyed."):r.send(t,...n)};e.startServer=function(){c.on(o.default.browser.require,(e,n,r,i)=>{a.info(`[REQUIRE] module=${i} `);let s=process.mainModule.require(i),l=g(e.sender,s);t(e,o.default.renderer.requireReturn,r,l)}),c.on(o.default.browser.builtIn,(e,n,i,s)=>{a.info(`[BUILTIN]: property=${s} contextId=${n}`);let l=r[s],c=g(e.sender,l);a.info(`[BUILTIN]: returns remoteId:${c.id}, type: ${typeof l}`),t(e,o.default.renderer.getBuiltInReturn,i,c)}),c.on(o.default.browser.global,(e,n,r,i)=>{a.info(`[GLOBAL]: proerty:${i} contextId=${n}`);let s,l=global[i];s=g(e.sender,l),a.info(`[GLOBAL]: returns remoteid=${s.id}, obj=`+typeof l),t(e,o.default.renderer.getGlobalReturn,r,s)}),c.on(o.default.browser.currentWindow,(e,n,r,i)=>{a.info(`[CURRENT_WINDOW]: property=${i} contextId=${n}`);let s=e.sender.getOwnerBrowserWindow.call(e.sender),l=g(e.sender,s);a.info(`[CURRENT_WINDOW]: returns remoteid=${l.id}, obj=`+s),t(e,o.default.renderer.currentWindowReturn,r,l)}),c.on(o.default.browser.currentWebContents,(e,n,r,i)=>{t(e,o.default.renderer.currentWebContentsReturn,r,g(e.sender,e.sender))}),c.on(o.default.browser.webContents,(e,n,i,s)=>{a.info(`[WebContents]: proerty:${s} contextId=${n}`);let l,c=r.webContents;l=g(e.sender,c),a.info(`[WebContents]: returns remoteid=${l.id}, obj=`+typeof c),t(e,o.default.renderer.webContentsReturn,i,l)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(r=>{n?e.once(r,t[r]):e.on(r,t[r])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};c.on(o.default.browser.construct,(n,r,i,s,l)=>{let c,u=null;try{a.info(`[CONSTRUCTOR]: remoteId=${s} `);let d=l.length>0?l[l.length-1]:null;l=_(n.sender,n.frameId,r,l);let h=f.get(s);null==h&&C(`Cannot call constructor on missing remote object ${s}`),d&&"interest"===d.type&&(u=l.pop());let p=new(Function.prototype.bind.apply(h,[null,...l]));p&&u&&e(p,u),c=g(n.sender,p,null,!1),a.info(`[CONSTRUCTOR]: returns remoteId =${c.id} name=${h.name} `)}catch(e){c=b(e)}finally{t(n,o.default.renderer.constructReturn,i,c)}}),c.on(o.default.browser.functionCall,function(e,n,r,i,s,l){let c;try{a.info(`[FUNCTION_CALL]: remoteId=${s}`),l=_(e.sender,e.frameId,n,l);let u=f.get(s);if(null==u)a.error(`Cannot call function on missing remote object ${s}`),c=g(e.sender,void 0);else{let t=i?f.get(i):global;if(t){let n=y(0,u,t,l);c=g(e.sender,n)}else a.error(`Cannot call function(${s}) on missing context(${i})`),c=g(e.sender,void 0)}a.info(`[FUNCTION_CALL]: name=${u.name}`)}catch(e){c=b(e)}finally{t(e,o.default.renderer.functionCallReturn,r,c)}}),c.on(o.default.browser.memberCall,function(e,n,r,i,s,l,c){let u;a.info(`[MEMBER_CALL]: thisArg=${i}, remoteId=${s}, method=${l}, args count=${c.length}`);try{c=_(e.sender,e.frameId,n,c);let d=f.get(s);null==d&&C(`Cannot call function '${l}' on missing remote object ${s}`);let h=i?f.get(i):d;if(h){let t=y(0,d[l],h,c);u=g(e.sender,t),a.info("[MEMBER_CALL]: return="+t)}else u=g(e.sender,void 0)}catch(e){u=b(e)}finally{t(e,o.default.renderer.memberCallReturn,r,u)}}),c.on(o.default.browser.memberGet,function(e,n,r,i,s){let l;try{a.info(`[MEMBER_GET]: remoteId=${i}, property=`,s);let n=f.get(i);null==n&&C(`Cannot get property '${Object.toString.call(s)}' on missing remote object ${i}`);let c=n[s];l=g(e.sender,c)}catch(e){l=b(e)}finally{t(e,o.default.renderer.memberGetReturn,r,l)}}),c.on(o.default.browser.memberSet,function(e,n,r,i,s,l){try{a.info(`[MEMBER_SET]: remoteId=${i}, property=`+s),l=_(e.sender,e.frameId,n,l);let c=f.get(i);null==c&&C(`Cannot set property '${Object.toString.call(s)}' on missing remote object ${i}`),c[s]=l[0],t(e,o.default.renderer.memberSetReturn,r,{type:"value",value:!0})}catch(n){t(e,o.default.renderer.memberSetReturn,r,b(n))}}),c.on(o.default.browser.memberConstruct,function(n,r,i,s,l,c){let u,d=null;try{a.info(`[MEMBER_CONSTRUCTOR]: regId=${s}, method=${l}`);let h=c.length>0?c[c.length-1]:null;c=_(n.sender,n.frameId,r,c);let p=f.get(s);null==p&&C(`Cannot call constructor '${l}' on missing remote object ${s}`),h&&"interest"===h.type&&(d=c.pop());let m=p[l],v=new(Function.prototype.bind.apply(m,[null,...c]));v&&d&&e(v,d),u=g(n.sender,v)}catch(e){u=b(e)}finally{t(n,o.default.renderer.memberConstructReturn,i,u)}}),c.on(o.default.browser.sync,function(e,n,r,i){let s=f.get(i);t(e,o.default.renderer.syncReturn,r,g(e.sender,s))}),c.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=f.get(t);if(r.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let r=n.name;r||(r=n.constructor?n.constructor.name:""),f.remove(e.sender.id,t)}else t<0&&a.warn("remote id reference to nothing:",t)}),c.on(o.default.browser.contextRelease,e=>{f.clear(e.sender.id)})},e.getObjectRegistry=function(){return f}}(R||(R={})),e.exports=R},570:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-dialog",{attrs:{fullscreen:"","custom-class":"xlx-personal-edit xlx-dialog-setting",visible:""},on:{close:function(t){e.closeWnd("closeBtn")}}},[n("div",{staticClass:"xlx-personal-edit__avatar"},[n("a",{staticClass:"xlx-personal-edit__picture",attrs:{href:"javascript:;",draggable:"false"},on:{click:e.clickHeader}},[n("img",{attrs:{src:e.curUserHeader,alt:"头像"}})]),e._v(" "),n("a",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],staticClass:"xlx-personal-edit__link",attrs:{href:"javascript:;",draggable:"false"}},[n("i",{staticClass:"td-icon-rename"}),e._v("编辑头像\n    ")])]),e._v(" "),n("div",{staticClass:"xlx-dialog-setting__content"},[n("ul",{staticClass:"xlx-dialog-setting__form"},[n("li",{staticClass:"xlx-dialog-setting__item"},[n("span",{staticClass:"xlx-dialog-setting__label"},[e._v("迅雷帐号")]),e._v(" "),n("span",{staticClass:"xlx-dialog-setting__text"},[e._v(e._s(e.curUserAccount))])]),e._v(" "),n("li",{staticClass:"xlx-dialog-setting__item"},[n("span",{staticClass:"xlx-dialog-setting__label"},[e._v("昵称")]),e._v(" "),n("td-tooltip",{staticStyle:{display:"flex",flex:"1"},attrs:{visible:e.isShowTipsNick,placement:"top"}},[n("template",{slot:"content"},[n("td-icon",{attrs:{type:"error"}}),e._v("\n            "+e._s(e.nickTipsText)+"\n          ")],1),e._v(" "),n("td-input",{attrs:{disabled:e.disabled,warn:e.isNickWarn},on:{input:e.handleInputChange},model:{value:e.curUserNick,callback:function(t){e.curUserNick=t},expression:"curUserNick"}})],2)],1),e._v(" "),n("li",{staticClass:"xlx-dialog-setting__item"},[n("span",{staticClass:"xlx-dialog-setting__label"},[e._v("性别")]),e._v(" "),n("ul",{staticClass:"xlx-setting-content__horizontal"},[n("li",[n("td-radio",{attrs:{label:"man",disabled:e.disabled},model:{value:e.picked,callback:function(t){e.picked=t},expression:"picked"}},[e._v("男")])],1),e._v(" "),n("li",[n("td-radio",{attrs:{label:"woman",disabled:e.disabled},model:{value:e.picked,callback:function(t){e.picked=t},expression:"picked"}},[e._v("女")])],1)])]),e._v(" "),n("li",{staticClass:"xlx-dialog-setting__item"},[n("span",{staticClass:"xlx-dialog-setting__label"},[e._v("生日")]),e._v(" "),n("div",{staticClass:"xlx-dialog-setting__select"},[n("select-native",{staticClass:"xlx-select-size-5",attrs:{disabled:e.disabled,options:e.yearList},model:{value:e.curUserBirthdayYear,callback:function(t){e.curUserBirthdayYear=t},expression:"curUserBirthdayYear"}}),e._v(" "),n("select-native",{staticClass:"xlx-select-size-5",attrs:{disabled:e.disabled,options:e.monthList},model:{value:e.curUserBirthdayMonth,callback:function(t){e.curUserBirthdayMonth=t},expression:"curUserBirthdayMonth"}}),e._v(" "),n("select-native",{staticClass:"xlx-select-size-5",attrs:{disabled:e.disabled,options:e.dayList},model:{value:e.curUserBirthdayDay,callback:function(t){e.curUserBirthdayDay=t},expression:"curUserBirthdayDay"}})],1)]),e._v(" "),n("li",{staticClass:"xlx-dialog-setting__item"},[n("span",{staticClass:"xlx-dialog-setting__label"},[e._v("城市")]),e._v(" "),n("div",{staticClass:"xlx-dialog-setting__select"},[n("select-native",{staticClass:"xlx-select-size-5",attrs:{disabled:e.disabled,options:e.cityPList},model:{value:e.curUserCityP,callback:function(t){e.curUserCityP=t},expression:"curUserCityP"}}),e._v(" "),n("select-native",{staticClass:"xlx-select-size-6",attrs:{disabled:e.disabled,options:e.cityList},model:{value:e.curUserCity,callback:function(t){e.curUserCity=t},expression:"curUserCity"}})],1)])]),e._v(" "),n("a",{staticClass:"xlx-personal-edit__link",attrs:{href:"javascript:;"},on:{click:e.clickMoreSet}},[e._v("更多设置")])]),e._v(" "),n("template",{slot:"footer"},[n("td-button",{attrs:{disabled:e.disabled},nativeOn:{click:function(t){return t.stopPropagation(),e.okClick(t)}}},[e._v(e._s(e.okBtnStr))]),e._v(" "),n("td-button",{attrs:{secondary:"",disabled:e.disabled},on:{click:function(t){e.closeWnd("cancel")}}},[e._v("取消")])],1),e._v(" "),n("td-message",{ref:"message",attrs:{type:e.messageType}},[e._v("\n    "+e._s(e.messageContent)+"\n    "),n("a",{attrs:{href:"javascript:;"}})])],2)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},58:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13),i=-1*Math.pow(2,31),o=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),r=e.id;let i=this.owners.get(r);return i||(i=new Set,this.owners.set(r,i),this.registerDeleteListener(e,r)),i.has(n)||(i.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return o.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=o.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=i)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),o.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(o.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),i=(o,s)=>{s===n&&(r.info("render-view-deleted: processid="+n),(()=>{r.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",i),this.clear(t))};e.on("render-view-deleted",i)}}},59:function(e,t,n){"use strict";var r;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(r||(r={})),e.exports=r},6:function(e,t){e.exports=require("util")},60:function(e,t){e.exports=require("readline")},61:function(e,t,n){e.exports=n(9)(216)},62:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(7),o=n(18),s=n(4),a=n(32);!function(e){function t(e,t){return r(this,void 0,void 0,function*(){if(null!==e){let n=e.webContents;(yield n.isDevToolsOpened())?yield n.closeDevTools():yield n.openDevTools(t)}})}e.openDevTool=t,e.enableDevTools=function(e){return r(this,void 0,void 0,function*(){window.addEventListener("keyup",n=>r(this,void 0,void 0,function*(){if("F12"===n.key&&n.ctrlKey)a.DevEnvHelperNS.isLogEnable()&&(yield t(yield i.asyncRemoteCall.getCurrentWindow(),e));else if(("t"===n.key||"T"===n.key)&&n.altKey&&a.DevEnvHelperNS.isLogEnable()){let e=document.getElementById("DevProcessPid");if(e)document.body.removeChild(e);else{(e=document.createElement("p")).id="DevProcessPid",e.style.position="absolute",e.style.left="0px",e.style.top="0px",e.style.width="100%",e.style.zIndex="10000",e.style.color="rgb(255,0,0)",document.body.appendChild(e);let t="process.pid:"+process.pid;t+="\r\nlocation.href:"+location.href,t+="\r\nprocess.argv:"+process.argv,e.innerText=t}}}),!0)})},e.enableDragOpenFile=function(e){void 0===e&&(e=!1),document.addEventListener("dragover",e=>(e.preventDefault(),e.stopPropagation(),!1),!0),document.addEventListener("drop",e=>r(this,void 0,void 0,function*(){e.preventDefault(),e.stopPropagation();let t=e.dataTransfer,n=t.files,r=t.items;if(void 0!==r&&null!==r&&r.length>0)for(let e=0;e<r.length;e++){let t=r[e];"string"===t.kind&&"text/uri-list"===t.type?t.getAsString(e=>{s.client.callServerFunction("DropOpenUrl",e).catch()}):t.kind}if(void 0!==n&&null!==n&&n.length>0)for(let e=0;e<n.length;e++){let t=n[e].path;void 0!==t&&null!==t&&""!==t&&(yield o.FileSystemAWNS.existsAW(t))&&s.client.callServerFunction("DropOpenFile",t).catch()}return!1}),!0)}}(t.ThunderToolsNS||(t.ThunderToolsNS={}))},64:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.getTextScale=function(){return 1},e.fixWindowSize=function(e,t){return r(this,void 0,void 0,function*(){window.resizeTo(e,t)})},e.autoFixWindowSize=function(){},e.fixZoomFactory=function(){}}(t.FixTextScale||(t.FixTextScale={}))},65:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(4),i=n(1).default.getLogger("common/skin"),o=!0;let s=null;function a(e){if(!o||null===e||void 0===e)return;let t=localStorage.getItem("skin_body_classes");s&&s.classes===e.classes||(localStorage.removeItem("skin_body_classes"),e&&e.classes&&(document.body.classList.add(e.classes),localStorage.setItem("skin_body_classes",e.classes)),s&&s.classes?document.body.classList.remove(s.classes):t!==e.classes&&document.body.classList.remove(t),s=Object.freeze(Object.assign({},e)))}r.client.callServerFunction("GetSkinInfo").then(a).catch(e=>{i.warning(e)}),r.client.attachServerEvent("OnChangeSkin",(e,...t)=>{a(t[0])})},66:function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},67:function(e,t,n){"use strict";var r=n(46);e.exports=function(e,t,n){var i=n.config.validateStatus;n.status&&i&&!i(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},68:function(e,t,n){"use strict";e.exports=function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e}},69:function(e,t,n){"use strict";var r=n(10);function i(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var o;if(n)o=n(t);else if(r.isURLSearchParams(t))o=t.toString();else{var s=[];r.forEach(t,function(e,t){null!==e&&void 0!==e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),s.push(i(t)+"="+i(e))}))}),o=s.join("&")}return o&&(e+=(-1===e.indexOf("?")?"?":"&")+o),e}},7:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(1).default.getLogger("async-remote-call"),o=n(42),s=n(12),a=n(6);t.asyncRemoteCall=new class extends s.EventEmitter{constructor(){super(),this.mapObj=new Map,this.mapObjIniting=new Map,"renderer"!==process.type&&i.warning('can not import "renderer-process-call" module in non-renderer process',process.type)}getAppName(){return r(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}getAppVersion(){return r(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return r(this,void 0,void 0,function*(){return o.global.process.__resolve()})}getIpcMain(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getBrowserWindow(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getWebContents(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("webContents")})}getGlobalShortcut(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield o.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emit("OnInitCurrentWebContents",e),this.listeners("OnInitCurrentWebContents").forEach(e=>{this.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield o.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emit("OnInitCurrentWindow",e),this.listeners("OnInitCurrentWindow").forEach(e=>{this.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return r(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return a.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>r(this,void 0,void 0,function*(){this.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield o.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emit(e,t),this.listeners(e).forEach(t=>{this.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}},70:function(e,t,n){var r=n(34),i=n(44),o=n(40),s=n(83),a=n(75).Writable,l=n(124)("follow-redirects"),c={GET:!0,HEAD:!0,OPTIONS:!0,TRACE:!0},u=Object.create(null);function d(e,t){a.call(this),e.headers=e.headers||{},this._options=e,this._redirectCount=0,this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var n=this;if(this._onNativeResponse=function(e){n._processResponse(e)},!e.pathname&&e.path){var r=e.path.indexOf("?");r<0?e.pathname=e.path:(e.pathname=e.path.substring(0,r),e.search=e.path.substring(r))}this._performRequest()}function f(e){var t={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(e).forEach(function(i){var o=i+":",a=n[o]=e[i],c=t[i]=Object.create(a);c.request=function(e,i){return"string"==typeof e?(e=r.parse(e)).maxRedirects=t.maxRedirects:e=Object.assign({protocol:o,maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},e),e.nativeProtocols=n,s.equal(e.protocol,o,"protocol mismatch"),l("options",e),new d(e,i)},c.get=function(e,t){var n=c.request(e,t);return n.end(),n}}),t}["abort","aborted","error","socket","timeout"].forEach(function(e){u[e]=function(t){this._redirectable.emit(e,t)}}),d.prototype=Object.create(a.prototype),d.prototype.write=function(e,t,n){this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,n)):(this.emit("error",new Error("Request body larger than maxBodyLength limit")),this.abort())},d.prototype.end=function(e,t,n){var r=this._currentRequest;e?this.write(e,t,function(){r.end(null,null,n)}):r.end(null,null,n)},d.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)},d.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},["abort","flushHeaders","getHeader","setNoDelay","setSocketKeepAlive","setTimeout"].forEach(function(e){d.prototype[e]=function(t,n){return this._currentRequest[e](t,n)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(d.prototype,e,{get:function(){return this._currentRequest[e]}})}),d.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(this._options.agents){var n=e.substr(0,e.length-1);this._options.agent=this._options.agents[n]}var i=this._currentRequest=t.request(this._options,this._onNativeResponse);for(var o in this._currentUrl=r.format(this._options),i._redirectable=this,u)o&&i.on(o,u[o]);if(this._isRedirect){var s=this._requestBodyBuffers;!function e(){if(0!==s.length){var t=s.pop();i.write(t.data,t.encoding,e)}else i.end()}()}},d.prototype._processResponse=function(e){var t=e.headers.location;if(t&&!1!==this._options.followRedirects&&e.statusCode>=300&&e.statusCode<400){if(++this._redirectCount>this._options.maxRedirects)return void this.emit("error",new Error("Max redirects exceeded."));var n,i=this._options.headers;if(307!==e.statusCode&&!(this._options.method in c))for(n in this._options.method="GET",this._requestBodyBuffers=[],i)/^content-/i.test(n)&&delete i[n];if(!this._isRedirect)for(n in i)/^host$/i.test(n)&&delete i[n];var o=r.resolve(this._currentUrl,t);l("redirecting to",o),Object.assign(this._options,r.parse(o)),this._isRedirect=!0,this._performRequest()}else e.responseUrl=this._currentUrl,this.emit("response",e),this._requestBodyBuffers=[]},e.exports=f({http:i,https:o}),e.exports.wrap=f},71:function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},72:function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},75:function(e,t){e.exports=require("stream")},76:function(e,t,n){"use strict";n.r(t);var r=n(77),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},77:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,s=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(o<3?i(s):o>3?i(t,n,s):i(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5);let s=class extends o.Vue{onOptionsChanged(){return i(this,void 0,void 0,function*(){})}get filterOptions(){return this.options.filter(e=>"separator"!==e)}onSelect(e){this.$emit("input",e.srcElement.value,!0)}mounted(){let e=this.$refs.selectNative;if(e)for(let t in this.filterOptions)if(String(this.filterOptions[t])===this.value){e.selectedIndex=t;break}}focusEdit(){if(this.$refs.select){let e=this.$refs.select.$el.querySelector("input");null!==e&&void 0!==e&&e.focus()}}};r([o.Prop()],s.prototype,"value",void 0),r([o.Prop({default:()=>[]})],s.prototype,"options",void 0),r([o.Prop({default:!1})],s.prototype,"useNative",void 0),r([o.Prop({default:"bottom"})],s.prototype,"position",void 0),r([o.Watch("options")],s.prototype,"onOptionsChanged",null),s=r([o.Component],s),t.default=s},8:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},83:function(e,t){e.exports=require("assert")},86:function(e,t){!function(e){var t,n='<svg><symbol id="td-icon-svg-file" viewBox="0 0 1204 1024"><path d="M180.705882 1024c-102.4 0-180.705882-78.305882-180.705882-180.705882V180.705882c0-102.4 78.305882-180.705882 180.705882-180.705882h240.941177c102.4 0 180.705882 78.305882 180.705882 180.705882h421.647059c102.4 0 180.705882 78.305882 180.705882 180.705883v481.882353c0 102.4-78.305882 180.705882-180.705882 180.705882H180.705882z" fill="#FFC25A" ></path><path d="M301.176471 361.411765h602.352941c66.258824 0 120.470588 54.211765 120.470588 120.470588v361.411765c0 66.258824-54.211765 120.470588-120.470588 120.470588H301.176471c-66.258824 0-120.470588-54.211765-120.470589-120.470588V481.882353c0-66.258824 54.211765-120.470588 120.470589-120.470588z" fill="#FFFFFF" ></path><path d="M180.705882 542.117647h843.294118c102.4 0 180.705882 78.305882 180.705882 180.705882v120.470589c0 102.4-78.305882 180.705882-180.705882 180.705882H180.705882c-102.4 0-180.705882-78.305882-180.705882-180.705882v-120.470589c0-102.4 78.305882-180.705882 180.705882-180.705882z" fill="#FFD68F" ></path></symbol></svg>',r=function(e,t){t.firstChild?function(e,t){t.parentNode.insertBefore(e,t)}(e,t.firstChild):t.appendChild(e)};if((t=document.getElementsByTagName("script"))[t.length-1].getAttribute("data-injectcss")&&!e.__iconfont__svg__cssinject__){e.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}!function(t){if(document.addEventListener)if(~["complete","loaded","interactive"].indexOf(document.readyState))setTimeout(t,0);else{var n=function(){document.removeEventListener("DOMContentLoaded",n,!1),t()};document.addEventListener("DOMContentLoaded",n,!1)}else document.attachEvent&&function(e,t){var n=e.document,r=!1,i=function(){r||(r=!0,t())},o=function(){try{n.documentElement.doScroll("left")}catch(e){return void setTimeout(o,50)}i()};o(),n.onreadystatechange=function(){"complete"==n.readyState&&(n.onreadystatechange=null,i())}}(e,t)}(function(){var e,t;(e=document.createElement("div")).innerHTML=n,n=null,(t=e.getElementsByTagName("svg")[0])&&(t.setAttribute("aria-hidden","true"),t.style.position="absolute",t.style.width=0,t.style.height=0,t.style.overflow="hidden",r(t,document.body))})}(window)},89:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.useNative?n("select",{ref:"selectNative",staticClass:"xly-select",on:{change:e.onSelect}},e._l(e.filterOptions,function(t){return n("option",{key:t,domProps:{value:t}},[e._v(e._s(t))])})):n("td-select",e._g(e._b({ref:"select",attrs:{value:e.value,options:e.filterOptions,position:e.position}},"td-select",e.$attrs,!1),e.$listeners),[e._t("suffix",null,{slot:"suffix"}),e._v(" "),e._t("append",null,{slot:"append"})],2)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},9:function(e,t){e.exports=vendor_0aff229d1d3a2d2be355},968:function(e,t,n){n(49),e.exports=n(969)},969:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(38);n(4).client.start({name:"modifierUserinfoRendererContext"}),r.CommonIPCRenderer.rendererCommunicator.initialize("modifierUserinfoRendererContext"),r.CommonIPCRenderer.rendererCommunicator.connect();const i=n(52),o=n(31),s=n(62),a=n(970);n(65);const l=n(1);n(979);const c=l.default.getLogger("ModifierUserInfoRenderer");i.PerformanceMonitorStatNS.init("modifier-userinfo-renderer"),s.ThunderToolsNS.enableDragOpenFile(),s.ThunderToolsNS.enableDevTools().catch(e=>{c.warning(e)}),o.default.config.ignoredElements=["webview"],new o.default({components:{ModifierUserInfo:a.default},render:e=>e("modifier-user-info")}).$mount("#app")},970:function(e,t,n){"use strict";n.r(t);var r=n(570),i=n(473);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);n(972),n(973),n(974),n(975),n(976),n(977),n(978);var s=n(0),a=Object(s.a)(i.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\modifier-userinfo-renderer\\app.vue",t.default=a.exports},971:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(19),o=n(18),s=n(2),a=n(1).default.getLogger("ModifierUserInfo"),l=n(4);t.modifierUserinfoHelper=new class{constructor(){this.cityListStr="",this.maxTime=604800,this.dir=s.resolve(__profilesDir,"Community"),this.cityFileName=s.resolve(__profilesDir,"Community/citylist.json"),this.init()}getCityListStrAw(){return r(this,void 0,void 0,function*(){let e="";do{if(""!==this.cityListStr)break;if(yield o.FileSystemAWNS.existsAW(this.cityFileName)){let t=yield this.getConfigValueAw("Community","CitiesJsonOverTime",0);e=Math.round((new Date).getTime()/1e3)<=t?(yield o.FileSystemAWNS.readFileAW(this.cityFileName)).toString():yield this.getCityListDataByServicesAw()}else e=yield this.getCityListDataByServicesAw()}while(0);if(""!==e){let t=null;try{t=JSON.parse(e)}catch(e){a.warning(e)}null===t||void 0===t.cities||null===t.cities||""===t.cities?this.cityListStr=e:this.cityListStr=JSON.stringify(t.cities)}return this.cityListStr})}updateModifierUserinfo(e){return r(this,void 0,void 0,function*(){let t=!1,n=e;"woman"===n.sex?n.sex="f":n.sex="m";let r=null;try{let e=yield this.getParametersAw();a.information(e),r=yield i.default({method:"post",url:"https://interface-account-ssl.xunlei.com/service/user_info?request=set",data:{birthday:n.birthday,province:n.province,nickname:n.nickName,city:n.city,sex:n.sex,businesstype:e.businesstype,deviceid:e.deviceid,platform:e.platform,protocolversion:e.protocolversion,sessionid:e.sessionid,userid:e.userID}})}catch(e){a.information(e)}return null!==r&&void 0!==r.status&&200===r.status&&void 0!==r.data&&null!==r.data&&""!==r.data&&(a.information("updateModifierUserinfo:",r.data.result,r.data.message),200===r.data.result&&"success"===r.data.message&&(t=!0)),t})}getMapProvinceOrCityValue(e,t){let n="";return"province"===e&&(n=this.provinceMap.get(t)),n}init(){this.provinceMap=new Map,this.provinceMap.set("内蒙古自治区","内蒙古"),this.provinceMap.set("广西壮族自治区","广西"),this.provinceMap.set("西藏自治区","西藏"),this.provinceMap.set("宁夏回族自治区","宁夏"),this.provinceMap.set("新疆维吾尔自治区","新疆"),this.provinceMap.set("香港特别行政区","香港"),this.provinceMap.set("澳门特别行政区","澳门")}getCityListDataByServicesAw(){return r(this,void 0,void 0,function*(){let e="",t=null;try{let e=yield this.getParametersAw();t=yield i.default({method:"post",url:"https://interface-account-ssl.xunlei.com/service/user_info?request=city",data:{nickname:e.nickName,userid:e.userID,sessionid:e.sessionid,businesstype:e.businesstype,deviceid:e.deviceid,protocolversion:e.protocolversion,platform:e.platform,clientVersion:e.clientVersion}})}catch(e){a.information(e)}if(null!==t&&void 0!==t.status&&200===t.status&&void 0!==t.data&&null!==t.data&&""!==t.data&&200===t.data.result){e=JSON.stringify(t.data);let n=yield o.FileSystemAWNS.mkdirsAW(this.dir);if(n&&(n=yield o.FileSystemAWNS.writeFileAW(this.cityFileName,e))){let e=Math.round((new Date).getTime()/1e3)+this.maxTime;yield this.setConfigValueAw("Community","CitiesJsonOverTime",e)}}return e})}getParametersAw(){return r(this,void 0,void 0,function*(){let e={platform:"pc-xdas",businesstype:0,protocolversion:100};e.clientVersion=yield this.getThunderVersionAW();let t=yield this.getAllUserInfoAw();e.userID=t.userID,e.nickName=t.nickName;let n=yield this.getLoginInfoAw();return e.sessionid=n.sessionid,e.deviceid=n.deviceid,e})}getThunderVersionAW(){return r(this,void 0,void 0,function*(){return yield l.client.callServerFunction("GetThunderVersion")})}getAllUserInfoAw(){return r(this,void 0,void 0,function*(){return yield l.client.callServerFunction("GetAllUserInfo")})}getLoginInfoAw(){return r(this,void 0,void 0,function*(){return yield l.client.callServerFunction("GetLoginInfo")})}getConfigValueAw(e,t,n){return r(this,void 0,void 0,function*(){return yield l.client.callServerFunction("GetConfigValue",e,t,n)})}setConfigValueAw(e,t,n){return r(this,void 0,void 0,function*(){return yield l.client.callServerFunction("SetConfigValue",e,t,n),!0})}}},972:function(e,t,n){"use strict";var r=n(1279);n.n(r).a},973:function(e,t,n){"use strict";var r=n(1281);n.n(r).a},974:function(e,t,n){"use strict";var r=n(1283);n.n(r).a},975:function(e,t,n){"use strict";var r=n(1285);n.n(r).a},976:function(e,t,n){"use strict";var r=n(1287);n.n(r).a},977:function(e,t,n){"use strict";var r=n(1289);n.n(r).a},978:function(e,t,n){"use strict";var r=n(1291);n.n(r).a},979:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(31);n(61),n(86);const i=n(980),o=n(170),s=n(171),a=n(172),l=n(173),c=n(981),u=n(982),d=n(174);r.default.use(i.default),r.default.use(o.default),r.default.use(s.default),r.default.use(a.default),r.default.use(l.default),r.default.use(c.default),r.default.use(u.default),r.default.use(d.default)},980:function(e,t,n){e.exports=n(9)(3)},981:function(e,t,n){e.exports=n(9)(58)},982:function(e,t,n){e.exports=n(9)(64)}});
//# sourceMappingURL=renderer.js.map