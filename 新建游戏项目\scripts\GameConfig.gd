extends Resource
class_name GameConfig

# 游戏配置类
# 用于存储和管理游戏的各种设置参数

# 游戏基础设置
@export var game_speed: float = 1.0  # 游戏速度倍率
@export var auto_sort_cards: bool = true  # 是否自动排序手牌
@export var show_card_hints: bool = true  # 是否显示出牌提示

# AI设置
@export var ai_thinking_time: float = 1.0  # AI思考时间（秒）
@export var ai_difficulty: int = 1  # AI难度等级 (1-3)

# 界面设置
@export var card_animation_speed: float = 0.3  # 卡牌动画速度
@export var show_other_player_card_count: bool = true  # 是否显示其他玩家手牌数量
@export var enable_sound_effects: bool = true  # 是否启用音效

# 卡牌显示设置
@export var card_spacing: float = 60.0  # 卡牌间距
@export var card_scale: float = 1.0  # 卡牌缩放
@export var selection_offset: float = 20.0  # 选中卡牌的偏移量

# 游戏规则设置
@export var enable_joker_as_any: bool = false  # 王牌是否可以当任意牌使用
@export var landlord_gets_bottom_cards: bool = true  # 地主是否获得底牌

# 创建默认配置
static func create_default() -> GameConfig:
	var config = GameConfig.new()
	return config

# 保存配置到文件
func save_to_file(path: String = "user://game_config.tres"):
	ResourceSaver.save(self, path)
	print("游戏配置已保存到: ", path)

# 从文件加载配置
static func load_from_file(path: String = "user://game_config.tres") -> GameConfig:
	if ResourceLoader.exists(path):
		var config = ResourceLoader.load(path) as GameConfig
		if config:
			print("游戏配置已加载: ", path)
			return config
	
	print("未找到配置文件，使用默认配置")
	return create_default()

# 应用配置到游戏
func apply_to_game(game_manager):
	if game_manager:
		# 这里可以将配置应用到游戏管理器
		print("配置已应用到游戏")

# 重置为默认值
func reset_to_default():
	var default_config = create_default()
	game_speed = default_config.game_speed
	auto_sort_cards = default_config.auto_sort_cards
	show_card_hints = default_config.show_card_hints
	ai_thinking_time = default_config.ai_thinking_time
	ai_difficulty = default_config.ai_difficulty
	card_animation_speed = default_config.card_animation_speed
	show_other_player_card_count = default_config.show_other_player_card_count
	enable_sound_effects = default_config.enable_sound_effects
	card_spacing = default_config.card_spacing
	card_scale = default_config.card_scale
	selection_offset = default_config.selection_offset
	enable_joker_as_any = default_config.enable_joker_as_any
	landlord_gets_bottom_cards = default_config.landlord_gets_bottom_cards
	print("配置已重置为默认值")

# 获取配置摘要
func get_config_summary() -> String:
	var summary = "游戏配置摘要:\n"
	summary += "- 游戏速度: " + str(game_speed) + "x\n"
	summary += "- AI难度: " + str(ai_difficulty) + "\n"
	summary += "- 自动排序: " + ("开启" if auto_sort_cards else "关闭") + "\n"
	summary += "- 音效: " + ("开启" if enable_sound_effects else "关闭") + "\n"
	summary += "- 卡牌动画速度: " + str(card_animation_speed) + "秒\n"
	return summary
