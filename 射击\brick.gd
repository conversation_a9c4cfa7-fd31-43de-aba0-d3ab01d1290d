# 砖块脚本
extends StaticBody2D

# 砖块的生命值
var health = 1
# 砖块的颜色
var brick_color = Color(1, 0.5, 0.5)
# 音效管理器引用
var sound_manager

# 初始化
func _ready():
	# 将砖块添加到"bricks"组，便于碰撞检测
	add_to_group("bricks")
	
	# 设置砖块颜色
	$ColorRect.color = brick_color
	
	# 获取音效管理器
	sound_manager = get_node("/root/BreakoutGame/SoundManager")
	print("砖块初始化，音效管理器：", sound_manager)

# 处理砖块被击中
func hit():
	# 调试输出
	print("砖块hit()方法被调用，当前生命值：", health)
	
	# 减少生命值
	health -= 1
	print("砖块生命值减少，现在为：", health)
	
	# 如果生命值为0，销毁砖块
	if health <= 0:
		# 调试输出
		print("砖块被击中，生命值为0，准备销毁")
		
		# 播放音效
		if sound_manager:
			print("使用音效管理器播放音效")
			# 创建一个独立的AudioStreamPlayer节点来播放音效
			var sound_player = AudioStreamPlayer.new()
			# 将其添加到场景树中的根节点，这样即使砖块被销毁，音效也能继续播放
			get_tree().root.add_child(sound_player)
			# 播放音效
			sound_manager.play_break_sound(sound_player)
			# 设置音效播放完成后自动销毁
			sound_player.finished.connect(sound_player.queue_free)
		else:
			print("音效管理器不存在")
		
		# 强制从组中移除
		remove_from_group("bricks")
		
		# 立即销毁砖块
		print("调用queue_free()销毁砖块")
		queue_free()
		print("砖块已销毁")
	else:
		# 否则改变颜色，表示砖块受损
		$ColorRect.color = brick_color.darkened(0.3)
		print("砖块颜色变暗，表示受损")
