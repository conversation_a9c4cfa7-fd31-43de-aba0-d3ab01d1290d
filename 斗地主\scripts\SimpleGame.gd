extends Node2D

# 简化的游戏启动脚本，用于快速测试

var game_manager: GameManager

func _ready():
	print("启动简化斗地主游戏...")
	
	# 创建游戏管理器
	game_manager = GameManager.new()
	add_child(game_manager)
	
	# 等待一帧后开始游戏
	await get_tree().process_frame
	
	print("游戏初始化完成！")
	print("使用说明：")
	print("- 鼠标左键点击卡牌选择")
	print("- 按空格键出牌")
	print("- 按P键过牌")
	print("- 按R键重新开始")

func _input(event):
	if not game_manager:
		return
	
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_SPACE:
				# 出牌
				var current_player = game_manager.get_current_player()
				if current_player and current_player.player_type == Player.PlayerType.HUMAN:
					if current_player.play_selected_cards():
						print("出牌成功")
					else:
						print("出牌失败")
			
			KEY_P:
				# 过牌
				var current_player = game_manager.get_current_player()
				if current_player and current_player.player_type == Player.PlayerType.HUMAN:
					current_player.pass_turn()
					print("过牌")
			
			KEY_R:
				# 重新开始
				game_manager.restart_game()
				print("重新开始游戏")
			
			KEY_H:
				# 显示帮助
				show_help()

func show_help():
	print("\n=== 斗地主游戏帮助 ===")
	print("游戏规则：")
	print("1. 三人游戏，一个地主对两个农民")
	print("2. 地主先出牌")
	print("3. 必须压过上一手牌才能出牌")
	print("4. 最先出完手牌的玩家获胜")
	print("\n操作说明：")
	print("- 鼠标左键：选择/取消选择卡牌")
	print("- 空格键：出选中的牌")
	print("- P键：过牌")
	print("- R键：重新开始游戏")
	print("- H键：显示此帮助")
	print("========================\n")
