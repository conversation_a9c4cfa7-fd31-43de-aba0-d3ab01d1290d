"""
高安全性卡密验证应用程序
集成多重反逆向和加密保护机制
卡密: 555555
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time
import threading
import os
import sys
import random
import hashlib
import base64

# 导入高级安全模块
try:
    from advanced_security import (
        SecurityValidator, CodeObfuscator, AntiAnalysis, 
        AdvancedAntiDebug, SecureStorage, validate_secure_key
    )
except ImportError:
    print("安全模块加载失败")
    sys.exit(1)

class SecureApp:
    """安全应用程序主类"""
    
    def __init__(self):
        # 初始化安全检查
        self._init_security()
        
        # 应用程序状态
        self.authenticated = False
        self.login_attempts = 0
        self.max_attempts = 3
        self.lockout_time = 300  # 5分钟
        self.locked_until = 0
        
        # 混淆的关键数据
        self._obfuscated_key = CodeObfuscator.obfuscate_string("555555")
        self._session_token = None
        
        # 初始化UI
        self._init_ui()
        
        # 启动安全监控
        self._start_security_monitoring()
    
    def _init_security(self):
        """初始化安全机制"""
        # 执行反分析检查
        if not AntiAnalysis.check_execution_time():
            self._security_exit()
        
        if not AntiAnalysis.check_memory_breakpoints():
            self._security_exit()
        
        # 检查调试器
        if AdvancedAntiDebug.check_hardware_breakpoints():
            self._security_exit()
        
        if AdvancedAntiDebug.timing_check():
            self._security_exit()
    
    def _security_exit(self):
        """安全退出"""
        # 清理敏感数据
        for attr in dir(self):
            if not attr.startswith('__'):
                try:
                    delattr(self, attr)
                except:
                    pass
        
        os._exit(1)
    
    def _start_security_monitoring(self):
        """启动安全监控"""
        def monitor():
            while True:
                try:
                    # 定期安全检查
                    if AdvancedAntiDebug.check_hardware_breakpoints():
                        self._security_exit()
                    
                    if AdvancedAntiDebug.timing_check():
                        self._security_exit()
                    
                    time.sleep(1)
                except:
                    self._security_exit()
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()
    
    def _init_ui(self):
        """初始化用户界面"""
        self.root = tk.Tk()
        self.root.title("🔐 高级安全验证系统")
        self.root.geometry("450x350")
        self.root.resizable(False, False)
        
        # 设置窗口图标和样式
        self._setup_window_style()
        
        # 创建主界面
        self._create_main_interface()
        
        # 绑定安全事件
        self._bind_security_events()
    
    def _setup_window_style(self):
        """设置窗口样式"""
        # 居中显示
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
        # 设置主题
        style = ttk.Style()
        style.theme_use('clam')
    
    def _create_main_interface(self):
        """创建主界面"""
        # 主容器
        main_container = ttk.Frame(self.root, padding="30")
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 标题区域
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(title_frame, text="🛡️ 高级安全验证系统", 
                               font=("Arial", 18, "bold"))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="Advanced Security Authentication", 
                                  font=("Arial", 10, "italic"))
        subtitle_label.pack(pady=(5, 0))
        
        # 安全状态指示器
        self.security_frame = ttk.LabelFrame(main_container, text="安全状态", padding="10")
        self.security_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.security_status = ttk.Label(self.security_frame, text="🟢 系统安全检查通过", 
                                        foreground="green")
        self.security_status.pack()
        
        # 登录区域
        login_frame = ttk.LabelFrame(main_container, text="身份验证", padding="15")
        login_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 卡密输入
        ttk.Label(login_frame, text="请输入您的安全卡密:", font=("Arial", 11)).pack(anchor=tk.W)
        
        self.key_var = tk.StringVar()
        self.key_entry = ttk.Entry(login_frame, textvariable=self.key_var, 
                                  font=("Consolas", 12), show="●", width=25)
        self.key_entry.pack(fill=tk.X, pady=(8, 15))
        
        # 按钮区域
        button_frame = ttk.Frame(login_frame)
        button_frame.pack(fill=tk.X)
        
        self.verify_button = ttk.Button(button_frame, text="🔓 验证登录", 
                                       command=self._verify_credentials)
        self.verify_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="❌ 退出", command=self._safe_exit).pack(side=tk.LEFT)
        
        # 状态显示区域
        status_frame = ttk.Frame(main_container)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="", font=("Arial", 10))
        self.status_label.pack()
        
        self.attempts_label = ttk.Label(status_frame, text="", font=("Arial", 9), 
                                       foreground="orange")
        self.attempts_label.pack(pady=(5, 0))
        
        # 进度条（隐藏）
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
    
    def _bind_security_events(self):
        """绑定安全事件"""
        # 键盘事件
        self.key_entry.bind('<Return>', lambda e: self._verify_credentials())
        self.root.bind('<Control-c>', self._security_violation)
        self.root.bind('<Control-v>', self._security_violation)
        self.root.bind('<F12>', self._security_violation)
        self.root.bind('<Alt-F4>', self._safe_exit)
        
        # 窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self._safe_exit)
        
        # 焦点事件（检测调试器）
        self.root.bind('<FocusOut>', self._check_focus_loss)
    
    def _security_violation(self, event=None):
        """安全违规处理"""
        self.security_status.config(text="🔴 检测到安全威胁", foreground="red")
        self.root.update()
        time.sleep(0.5)
        self._security_exit()
    
    def _check_focus_loss(self, event=None):
        """检查焦点丢失（可能的调试器活动）"""
        # 简单的反调试检查
        if random.random() < 0.1:  # 10%的概率检查
            if AdvancedAntiDebug.timing_check():
                self._security_violation()
    
    def _verify_credentials(self):
        """验证凭据"""
        # 检查锁定状态
        if time.time() < self.locked_until:
            remaining = int(self.locked_until - time.time())
            self.status_label.config(text=f"⏰ 账户已锁定，请等待 {remaining} 秒", 
                                   foreground="red")
            return
        
        # 显示验证进度
        self.progress.pack(pady=(10, 0))
        self.progress.start()
        self.verify_button.config(state=tk.DISABLED)
        self.status_label.config(text="🔍 正在验证...", foreground="blue")
        self.root.update()
        
        # 异步验证
        threading.Thread(target=self._async_verify, daemon=True).start()
    
    def _async_verify(self):
        """异步验证"""
        try:
            input_key = self.key_var.get().strip()
            
            # 增加尝试次数
            self.login_attempts += 1
            
            # 添加随机延迟防止时间攻击
            time.sleep(random.uniform(0.5, 1.5))
            
            # 使用高级安全验证
            is_valid = validate_secure_key(input_key)
            
            # 额外的本地验证
            if is_valid:
                # 解混淆真实密钥进行二次验证
                real_key = CodeObfuscator.deobfuscate_string(self._obfuscated_key)
                is_valid = (input_key == real_key)
            
            # 更新UI
            self.root.after(0, self._handle_verification_result, is_valid)
            
        except Exception:
            self.root.after(0, self._handle_verification_result, False)
    
    def _handle_verification_result(self, is_valid):
        """处理验证结果"""
        self.progress.stop()
        self.progress.pack_forget()
        self.verify_button.config(state=tk.NORMAL)
        
        if is_valid:
            self._authentication_success()
        else:
            self._authentication_failed()
    
    def _authentication_success(self):
        """认证成功"""
        self.authenticated = True
        self.status_label.config(text="✅ 验证成功！正在进入系统...", foreground="green")
        self.security_status.config(text="🟢 身份验证通过", foreground="green")
        
        # 生成会话令牌
        self._session_token = hashlib.sha256(
            (str(time.time()) + "555555" + "session").encode()
        ).hexdigest()
        
        self.root.update()
        time.sleep(1.5)
        
        # 打开主应用
        self._open_main_application()
    
    def _authentication_failed(self):
        """认证失败"""
        remaining_attempts = self.max_attempts - self.login_attempts
        
        if remaining_attempts > 0:
            self.status_label.config(text="❌ 卡密错误，请重试", foreground="red")
            self.attempts_label.config(text=f"剩余尝试次数: {remaining_attempts}")
            self.security_status.config(text="🟡 身份验证失败", foreground="orange")
        else:
            # 锁定账户
            self.locked_until = time.time() + self.lockout_time
            self.status_label.config(text=f"🔒 尝试次数过多，账户已锁定 {self.lockout_time//60} 分钟", 
                                   foreground="red")
            self.attempts_label.config(text="")
            self.security_status.config(text="🔴 账户已锁定", foreground="red")
            self.verify_button.config(state=tk.DISABLED)
            
            # 定时解锁
            self.root.after(self.lockout_time * 1000, self._unlock_account)
        
        # 清空输入
        self.key_var.set("")
    
    def _unlock_account(self):
        """解锁账户"""
        self.login_attempts = 0
        self.locked_until = 0
        self.verify_button.config(state=tk.NORMAL)
        self.status_label.config(text="🔓 账户已解锁，请重新尝试")
        self.attempts_label.config(text="")
        self.security_status.config(text="🟢 系统安全检查通过", foreground="green")
    
    def _open_main_application(self):
        """打开主应用程序"""
        # 关闭登录窗口
        self.root.destroy()
        
        # 创建主应用窗口
        main_app = tk.Tk()
        main_app.title("🎉 个人中心 - VIP用户")
        main_app.geometry("600x500")
        
        # 居中显示
        main_app.update_idletasks()
        width = main_app.winfo_width()
        height = main_app.winfo_height()
        x = (main_app.winfo_screenwidth() // 2) - (width // 2)
        y = (main_app.winfo_screenheight() // 2) - (height // 2)
        main_app.geometry(f'{width}x{height}+{x}+{y}')
        
        # 创建主应用内容
        self._create_main_app_content(main_app)
        
        main_app.mainloop()
    
    def _create_main_app_content(self, window):
        """创建主应用内容"""
        # 主容器
        main_frame = ttk.Frame(window, padding="25")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 欢迎标题
        welcome_frame = ttk.Frame(main_frame)
        welcome_frame.pack(fill=tk.X, pady=(0, 25))
        
        ttk.Label(welcome_frame, text="🎊 欢迎进入个人页面！", 
                 font=("Arial", 20, "bold"), foreground="green").pack()
        
        ttk.Label(welcome_frame, text="您已成功通过高级安全验证", 
                 font=("Arial", 12)).pack(pady=(5, 0))
        
        # 用户信息卡片
        info_card = ttk.LabelFrame(main_frame, text="👤 用户信息", padding="20")
        info_card.pack(fill=tk.X, pady=(0, 20))
        
        info_data = [
            ("🆔 用户ID", "VIP-555555"),
            ("🏆 权限级别", "高级VIP用户"),
            ("⏰ 登录时间", time.strftime("%Y-%m-%d %H:%M:%S")),
            ("🔑 会话令牌", self._session_token[:16] + "..." if self._session_token else "N/A"),
            ("📅 有效期", "永久有效"),
            ("🛡️ 安全等级", "最高级")
        ]
        
        for label, value in info_data:
            row_frame = ttk.Frame(info_card)
            row_frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(row_frame, text=label, font=("Arial", 10, "bold")).pack(side=tk.LEFT)
            ttk.Label(row_frame, text=value, font=("Consolas", 10)).pack(side=tk.RIGHT)
        
        # 功能区域
        function_frame = ttk.LabelFrame(main_frame, text="🚀 功能中心", padding="15")
        function_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 功能按钮
        buttons_data = [
            ("📊 查看详细信息", lambda: messagebox.showinfo("详细信息", "这里显示您的详细个人资料和使用统计")),
            ("⚙️ 系统设置", lambda: messagebox.showinfo("系统设置", "这里可以配置个人偏好和安全设置")),
            ("📈 使用统计", lambda: messagebox.showinfo("使用统计", "登录次数: 1\n在线时长: 刚刚登录\n上次登录: 首次登录")),
            ("🔒 安全中心", lambda: messagebox.showinfo("安全中心", f"当前安全状态: 优秀\n会话令牌: {self._session_token[:32]}...\n加密级别: AES-256"))
        ]
        
        button_container = ttk.Frame(function_frame)
        button_container.pack(fill=tk.BOTH, expand=True)
        
        for i, (text, command) in enumerate(buttons_data):
            row = i // 2
            col = i % 2
            
            btn = ttk.Button(button_container, text=text, command=command, width=25)
            btn.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
        
        button_container.grid_columnconfigure(0, weight=1)
        button_container.grid_columnconfigure(1, weight=1)
        
        # 底部操作栏
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X)
        
        ttk.Button(bottom_frame, text="🔄 刷新页面", 
                  command=lambda: messagebox.showinfo("刷新", "页面已刷新")).pack(side=tk.LEFT)
        
        ttk.Button(bottom_frame, text="🚪 安全退出", 
                  command=window.destroy).pack(side=tk.RIGHT)
    
    def _safe_exit(self):
        """安全退出"""
        # 清理敏感数据
        if hasattr(self, '_session_token'):
            self._session_token = None
        if hasattr(self, '_obfuscated_key'):
            self._obfuscated_key = None
        
        self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        except Exception:
            self._security_exit()

def main():
    """主函数"""
    try:
        # 添加启动延迟
        time.sleep(random.uniform(0.2, 0.8))
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            print("不支持命令行参数")
            sys.exit(1)
        
        # 创建并运行应用
        app = SecureApp()
        app.run()
        
    except KeyboardInterrupt:
        os._exit(1)
    except Exception:
        os._exit(1)

if __name__ == "__main__":
    main()
