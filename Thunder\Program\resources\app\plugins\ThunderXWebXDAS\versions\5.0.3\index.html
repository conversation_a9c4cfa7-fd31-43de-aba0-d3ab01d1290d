<!DOCTYPE html>
<!--[if IE 9]>
<html lang="en-US" class="lt-ie9 ie9" data-n-head=""><![endif]-->
<!--[if (gt IE 9)|!(IE)]><!-->
<html data-n-head="">
<!--<![endif]-->

<head>
  <meta data-n-head="true" charset="utf-8"/><meta data-n-head="true" name="viewport" content="width=device-width, initial-scale=1"/><meta data-n-head="true" data-hid="description" name="description" content="迅雷X任务详情页"/><title data-n-head="true">迅雷X任务详情页</title><script data-n-head="true" src="http://static1.ssp.xunlei.com/js/sdk.min.js"></script><link rel="preload" href="././_nuxt/manifest.fc05ca3a62d5f5c3d3db.js" as="script" /><link rel="preload" href="././_nuxt/vendor.c74052ec6afd168a7b02.js" as="script" /><link rel="preload" href="././_nuxt/app.950db028c45db9b81d3f.js" as="script" /><link rel="prefetch" href="././_nuxt/pages_index.0d5cda69864e5b111049.js" /><link rel="prefetch" href="././_nuxt/layouts_default.fe812e692a04005a6830.js" />

  <script>
    (function () {
      xla = window.xla || (window.xla = []);
      xla.push({
        type: 'config',
        appid: 20100,
        secret: '5b48cef241779d98c45819d9431f5d26'
      });
      var s = document.createElement('script');
      s.type = 'text/javascript';
      s.async = true;
      s.src = './xla.min.js';
      var x = document.getElementsByTagName('script')[0];
      x.parentNode.insertBefore(s, x);
    })();
  </script>
</head>

<body data-n-head="">
  <div id="__nuxt"><style>body, html, #__nuxt {  background-color: transparent;  width: 100%;  height: 100%;  display: flex;  justify-content: center;  align-items: center;  margin: 0;  padding: 0;}.spinner {  width: 40px;  height: 40px;  margin: 100px auto;  background-color: #dbe1ec;  border-radius: 100%;  -webkit-animation: sk-scaleout 1.0s infinite ease-in-out;  animation: sk-scaleout 1.0s infinite ease-in-out;}@-webkit-keyframes sk-scaleout {  0% { -webkit-transform: scale(0) }  100% {    -webkit-transform: scale(1.0);    opacity: 0;  }}@keyframes sk-scaleout {  0% {    -webkit-transform: scale(0);    transform: scale(0);  } 100% {    -webkit-transform: scale(1.0);    transform: scale(1.0);    opacity: 0;  }}</style><div class="spinner">  <div class="double-bounce1"></div>  <div class="double-bounce2"></div></div><!-- http://tobiasahlin.com/spinkit --></div>
<script type="text/javascript" src="././_nuxt/manifest.fc05ca3a62d5f5c3d3db.js"></script><script type="text/javascript" src="././_nuxt/vendor.c74052ec6afd168a7b02.js"></script><script type="text/javascript" src="././_nuxt/app.950db028c45db9b81d3f.js"></script></body>
</html>
