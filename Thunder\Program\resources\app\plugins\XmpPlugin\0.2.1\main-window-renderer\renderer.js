module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=353)}({0:function(e,t,n){e.exports=n(29)(21)},1:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{s(r.next(e))}catch(e){o(e)}}function l(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,l)}s((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(11),o=n(96),a=n(20),l=n(39);function s(e){l.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let c=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends i.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,r){do{if(t||(t=a.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(r)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))r("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{r("error",e)}),e.on("connect",()=>{r("connect")}),e.on("end",()=>{let t=e.isInprocess();r("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===a.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let i=new o.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=i,n||(this.client=i),i.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(i,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(i,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(i,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(i,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===i&&this.emit("stat_call_function_back",i.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let r=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,r));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),i.on("error",e=>{r&&r("error",e),this.emit("socket-error",e,i.getContext(),n,i.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),i.isInprocess()?(this.connectedMap[t.toLowerCase()]=i,r&&r("connect"),this.emit("connect",i.getContext(),n,!0)):i.on("connect",()=>{this.connectedMap[t.toLowerCase()]=i,r&&r("connect"),this.emit("connect",i.getContext(),n,!1)}),i.on("end",()=>{let e=i.isInprocess();l.information("server is ended, and this client emit end",t,n,e),r&&r("end",i.getContext(),n,e),this.emit("end",i.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(i)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return console.info(this.client.getContext().name,"attachServerEvent",e),this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return r(this,void 0,void 0,function*(){let r=null,i=yield this.crossCallServerFunctionEx(e,t,...n);return i&&(r=i[0]),r})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(r,t,...n)}}crossIsRemoteClientExist(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(r,t,n)}})}crossCallRemoteClientFunction(e,t,n,...r){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(i,t,n,...r)}}crossAttachServerEvent(e,t,n){let r=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");r=this.internalAttachServerEvent(i,t,n)}return r}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(r,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(r,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.generateId();const i={alias:t,action:"register",rid:r};let o=(e,r)=>{e?(l.error("register error",e.message),n(r)):n(t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(l.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===a.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,r)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let r=this.generateId();const i={action:"check_server_function_exist",method:t,rid:r};let o=(e,t)=>{n(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((r,i)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let i=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const o={rid:i,method:t,args:n};let a=(t,n)=>{t?(l.error("callServerFunction error",t,e.getContext()),r([null,t])):r([n,void 0])};this.retCallbackMap[i]=Object.assign({callback:a},o),this.sendAdapter(e,o)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,r)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let r=this.generateId();const i={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:r};let o=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((r,i)=>{do{if(!e){r(!1);break}if(!t){r(!1);break}if(!n){r(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){r(!(!this.apis||!this.apis[n]));break}let i=this.generateId();const o={action:"check_client_function_exist",method:n,rid:i,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let a=(e,t)=>{r(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:a},o),this.sendAdapter(e,o)}while(0)})}internalCallRemoteClientFunction(e,t,n,...r){return new Promise((i,o)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"remote client alias is not specifed"]);break}if(!n){i([null,"funcName is not specifed"]);break}let o=(e,t)=>{e?(l.information("callRemoteClientFunction",e.message),i([null,e])):i([t,void 0])};if(r)for(let e=0;e<r.length;e++)r[e]=this.convertFunction2IdEx(r[e]);let a=this.generateId();const s={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:r,rid:a};this.retCallbackMap[a]=Object.assign({callback:o},s),this.sendAdapter(e,s)}while(0)})}internalAttachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(r)||(this.eventCallbackMaps[r]={}),this.eventCallbackMaps[r].hasOwnProperty(t)||(this.eventCallbackMaps[r][t]={}),a.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let i=this.generateId();return this.eventCallbackMaps[r][t][i]=n,i}internalDetachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;delete this.eventCallbackMaps[r][t][n],a.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;let e=this.eventCallbackMaps[r][t];for(let t in e){let r=e[t];r&&r.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...r){let i=void 0,o=!1;do{const a=this.contextCallbackMap[t];if(!a){l.error("the context function has been freeer",t),i={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let s=void 0,c=void 0;try{s=a.apply(null,...r)}catch(e){c=e.message;break}if(void 0===n||null===n)break;if(i={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==c){i.error=c;break}if(s&&s.then){s.then(t=>{i.data=this.convertFunction2IdEx(t),i.success=!0,this.sendAdapter(e,i)}).catch(t=>{i.error=t instanceof Error?t.message:t,this.sendAdapter(e,i)}),o=!0;break}i.success=!0,i.data=this.convertFunction2IdEx(s)}while(0);!o&&i&&this.sendAdapter(e,i)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":r}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let r=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(r){n=((...e)=>{this.callRemoteContextById(t,r,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:r},s);break}for(let n in e){let r=e[n];e[n]=this.decodeParameter(r,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,r=!1;do{if(!t)break;let i=t.method;if(!i)break;let o=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(o):[].concat(o)},!this.apis||!this.apis[i]){n.error=`callRegisterFunction ${i} is undefined`;break}let a=void 0,l=this.decodeParameter(t.args,t.src);try{a=this.apis[i].apply(null,[t.src].concat(l))}catch(e){n.error=e.message;break}if(a&&a.then){a.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),r=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(a)}while(0);l.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(e,n)}}),c=global.__xdasIPCClienInstance,t.client=c},100:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(17);var i;!function(e){let t={};e.add=function(e,n,r){t[e]={func:n,thisArg:r}},e.invoke=function(n,...i){let o=t[n];return o?o.thisArg?o.func.apply(o.thisArg,...i):o.func(...i):r.error(`Cannot invoke function by unrecognize id. ${n}`),e},e.remove=function(e){delete t[e]}}(i||(i={})),t.default=i},101:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=0;t.default=function(e){return e?e.concat(".").concat(String(++r)):String(++r)}},102:function(e,t,n){"use strict";const r=n(8),i=n(103),o=n(54),a=n(104),l=n(17),s=n(55),c=r.ipcMain,u=process.electronBinding("v8_util");let d=u.createDoubleIDWeakMap();const f=new i.default;function p(e,t,n,r,i,o){let a,s=!1,c=null,u=!1;do{try{a=t[r]}catch(e){s=!0}if(s)try{a=n.value[r],s=!1,n.meta.extendedMemberNames.push(r),u=!0}catch(e){l.error(`property ${r} untouchable, even try root[name]`)}if(s)break;let i=Object.getOwnPropertyDescriptor(t,r);if(void 0===i){l.warn(`descriptor of property ${r} is undefined`);break}c={name:r,enumerable:i.enumerable,writable:!1,type:"get"},void 0===i.get&&"function"==typeof a?c.type="method":((i.set||i.writable)&&(c.writable=!0),c.type="get"),u?(c.configurable=!0,c.value=_(e,a,o,!1,null)):c.value=_(e,a,o,!1,n)}while(0);return c}function h(e,t,n,r=null){let i=Object.getOwnPropertyNames(t);"function"==typeof t&&(i=i.filter(function(e){return!String.prototype.includes.call(a.propertiesOfFunction,e)}));let o=[];do{if(0===i.length)break;let a=i.length;for(let l=0;l<a;l++){let a=p(e,t,n,i[l],0,r);a&&o.push(a)}}while(0);return o}function g(e,t,n,r=null){let i=null,o=Object.getPrototypeOf(t);return i=null===o||o===Object.prototype||o===Function.prototype?null:_(e,o,r,!1,n)}function _(e,t,n=null,r=!1,i=null){n=null===n?{}:n;const o={type:typeof t};"object"===o.type&&(o.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":s.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&u.getHiddenValue(e,"simple")&&(n="value"),n}(t,r));do{if("object"===o.type||"function"===o.type){let r=f.getIdOfObject(t);if(r&&n[r]){o.refId=r,f.add(e,t);break}}"array"===o.type?o.members=t.map(t=>_(e,t,n)):"object"===o.type||"function"===o.type?(null==i&&(o.extendedMemberNames=[],i={value:t,meta:o}),o.name=t.constructor?t.constructor.name:"",o.id=f.add(e,t),n[o.id]=!0,o.members=h(e,t,i,n),o.proto=g(e,t,i,n)):"buffer"===o.type?o.value=Buffer.from(t):"promise"===o.type?(t.then(function(){},function(){}),o.then=_(e,function(e,n){t.then(e,n)})):"error"===o.type?(o.members=m(t),o.members.push({name:"name",value:t.name})):"date"===o.type?o.value=t.getTime():(o.type="value",o.value=t)}while(0);return o}function m(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function S(e,t,n,i){const a=function(i){let s,c,p=0,h=0;switch(i.type){case"value":return i.value;case"remote-object":return f.get(i.id);case"array":return S(e,t,n,i.value);case"buffer":return Buffer.from(i.value);case"date":return new Date(i.value);case"promise":return Promise.resolve({then:a(i.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:i.name}),p=0,h=(c=i.members).length;p<h;p++)e[(s=c[p]).name]=a(s.value);return e}case"function":{const a=e.id,s=[n,i.id];if(l.info("renderer function id:"+s),d.has(s))return d.get(s);let c=function(...t){l.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||a!==e.id?function(e,t,n){let i="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof r.webContents.constructor){const{sender:e}=t[0],r=e.eventNames().filter(t=>{let r=e.listeners(t),i=!1;return r.forEach(e=>{e===n&&(i=!0)}),i});r.length>0&&(i+=`\nRemote event names: ${r.join(", ")}`,r.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}l.warn(i)}(i,n,c):e.send(o.default.renderer.callback,i.id,_(e,n))};return Object.defineProperty(c,"length",{value:i.length}),u.setRemoteCallbackFreer(c,t,n,i.id,e),d.set(s,c),c}default:throw new TypeError(`Unknown type: ${i.type}`)}};return i.map(a)}function w(e,t,n,r){let i,o;try{return t.apply(n,r)}catch(e){return o=t.name,new Error(`Could not call remote function '${i=null!=o?o:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function b(e){return{type:"exception",message:e.message,stack:e.stack||e}}function C(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var E;!function(e){const t=(e,t,...n)=>{const r=e.sender;r.isDestroyed()?l.warn("webcontext is destroyed."):r.send(t,...n)};e.startServer=function(){c.on(o.default.browser.require,(e,n,r,i)=>{l.info(`[REQUIRE] module=${i} `);let a=process.mainModule.require(i),s=_(e.sender,a);t(e,o.default.renderer.requireReturn,r,s)}),c.on(o.default.browser.builtIn,(e,n,i,a)=>{l.info(`[BUILTIN]: property=${a} contextId=${n}`);let s=r[a],c=_(e.sender,s);l.info(`[BUILTIN]: returns remoteId:${c.id}, type: ${typeof s}`),t(e,o.default.renderer.getBuiltInReturn,i,c)}),c.on(o.default.browser.global,(e,n,r,i)=>{l.info(`[GLOBAL]: proerty:${i} contextId=${n}`);let a,s=global[i];a=_(e.sender,s),l.info(`[GLOBAL]: returns remoteid=${a.id}, obj=`+typeof s),t(e,o.default.renderer.getGlobalReturn,r,a)}),c.on(o.default.browser.currentWindow,(e,n,r,i)=>{l.info(`[CURRENT_WINDOW]: property=${i} contextId=${n}`);let a=e.sender.getOwnerBrowserWindow.call(e.sender),s=_(e.sender,a);l.info(`[CURRENT_WINDOW]: returns remoteid=${s.id}, obj=`+a),t(e,o.default.renderer.currentWindowReturn,r,s)}),c.on(o.default.browser.currentWebContents,(e,n,r,i)=>{t(e,o.default.renderer.currentWebContentsReturn,r,_(e.sender,e.sender))}),c.on(o.default.browser.webContents,(e,n,i,a)=>{l.info(`[WebContents]: proerty:${a} contextId=${n}`);let s,c=r.webContents;s=_(e.sender,c),l.info(`[WebContents]: returns remoteid=${s.id}, obj=`+typeof c),t(e,o.default.renderer.webContentsReturn,i,s)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(r=>{n?e.once(r,t[r]):e.on(r,t[r])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};c.on(o.default.browser.construct,(n,r,i,a,s)=>{let c,u=null;try{l.info(`[CONSTRUCTOR]: remoteId=${a} `);let d=s.length>0?s[s.length-1]:null;s=S(n.sender,n.frameId,r,s);let p=f.get(a);null==p&&C(`Cannot call constructor on missing remote object ${a}`),d&&"interest"===d.type&&(u=s.pop());let h=new(Function.prototype.bind.apply(p,[null,...s]));h&&u&&e(h,u),c=_(n.sender,h,null,!1),l.info(`[CONSTRUCTOR]: returns remoteId =${c.id} name=${p.name} `)}catch(e){c=b(e)}finally{t(n,o.default.renderer.constructReturn,i,c)}}),c.on(o.default.browser.functionCall,function(e,n,r,i,a,s){let c;try{l.info(`[FUNCTION_CALL]: remoteId=${a}`),s=S(e.sender,e.frameId,n,s);let u=f.get(a);if(null==u)l.error(`Cannot call function on missing remote object ${a}`),c=_(e.sender,void 0);else{let t=i?f.get(i):global;if(t){let n=w(0,u,t,s);c=_(e.sender,n)}else l.error(`Cannot call function(${a}) on missing context(${i})`),c=_(e.sender,void 0)}l.info(`[FUNCTION_CALL]: name=${u.name}`)}catch(e){c=b(e)}finally{t(e,o.default.renderer.functionCallReturn,r,c)}}),c.on(o.default.browser.memberCall,function(e,n,r,i,a,s,c){let u;l.info(`[MEMBER_CALL]: thisArg=${i}, remoteId=${a}, method=${s}, args count=${c.length}`);try{c=S(e.sender,e.frameId,n,c);let d=f.get(a);null==d&&C(`Cannot call function '${s}' on missing remote object ${a}`);let p=i?f.get(i):d;if(p){let t=w(0,d[s],p,c);u=_(e.sender,t),l.info("[MEMBER_CALL]: return="+t)}else u=_(e.sender,void 0)}catch(e){u=b(e)}finally{t(e,o.default.renderer.memberCallReturn,r,u)}}),c.on(o.default.browser.memberGet,function(e,n,r,i,a){let s;try{l.info(`[MEMBER_GET]: remoteId=${i}, property=`,a);let n=f.get(i);null==n&&C(`Cannot get property '${Object.toString.call(a)}' on missing remote object ${i}`);let c=n[a];s=_(e.sender,c)}catch(e){s=b(e)}finally{t(e,o.default.renderer.memberGetReturn,r,s)}}),c.on(o.default.browser.memberSet,function(e,n,r,i,a,s){try{l.info(`[MEMBER_SET]: remoteId=${i}, property=`+a),s=S(e.sender,e.frameId,n,s);let c=f.get(i);null==c&&C(`Cannot set property '${Object.toString.call(a)}' on missing remote object ${i}`),c[a]=s[0],t(e,o.default.renderer.memberSetReturn,r,{type:"value",value:!0})}catch(n){t(e,o.default.renderer.memberSetReturn,r,b(n))}}),c.on(o.default.browser.memberConstruct,function(n,r,i,a,s,c){let u,d=null;try{l.info(`[MEMBER_CONSTRUCTOR]: regId=${a}, method=${s}`);let p=c.length>0?c[c.length-1]:null;c=S(n.sender,n.frameId,r,c);let h=f.get(a);null==h&&C(`Cannot call constructor '${s}' on missing remote object ${a}`),p&&"interest"===p.type&&(d=c.pop());let g=h[s],m=new(Function.prototype.bind.apply(g,[null,...c]));m&&d&&e(m,d),u=_(n.sender,m)}catch(e){u=b(e)}finally{t(n,o.default.renderer.memberConstructReturn,i,u)}}),c.on(o.default.browser.sync,function(e,n,r,i){let a=f.get(i);t(e,o.default.renderer.syncReturn,r,_(e.sender,a))}),c.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=f.get(t);if(r.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let r=n.name;r||(r=n.constructor?n.constructor.name:""),f.remove(e.sender.id,t)}else t<0&&l.warn("remote id reference to nothing:",t)}),c.on(o.default.browser.contextRelease,e=>{f.clear(e.sender.id)})},e.getObjectRegistry=function(){return f}}(E||(E={})),e.exports=E},103:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(17),i=-1*Math.pow(2,31),o=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),r=e.id;let i=this.owners.get(r);return i||(i=new Set,this.owners.set(r,i),this.registerDeleteListener(e,r)),i.has(n)||(i.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return o.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=o.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=i)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),o.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(o.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),i=(o,a)=>{a===n&&(r.info("render-view-deleted: processid="+n),(()=>{r.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",i),this.clear(t))};e.on("render-view-deleted",i)}}},104:function(e,t,n){"use strict";var r;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(r||(r={})),e.exports=r},105:function(e,t,n){e.exports=n(29)(5)},106:function(e,t){e.exports=require("child_process")},11:function(e,t){e.exports=require("events")},12:function(e,t,n){e.exports=n(29)(50)},164:function(e,t,n){"use strict";n.r(t);var r=n(165),i=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},165:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var l=e.length-1;l>=0;l--)(i=e[l])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{s(r.next(e))}catch(e){o(e)}}function l(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(12),a=n(62);let l=class extends o.Vue{constructor(){super(...arguments),this.argsObj=null,this.isEmbed=!1}created(){return i(this,void 0,void 0,function*(){let e=window.location.href;this.argsObj=a.ThunderUtil.getUrlArgs(e)})}};l=r([o.Component({components:{}})],l),t.default=l},17:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const r=n(5);let i,o=console;function a(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,i=(new Error).stack.split("\n").slice(e+1);i.shift();let o=[];return i.forEach((e,i)=>{let a=t.exec(e)||n.exec(e),l={};a&&5===a.length&&(l.method=a[1],l.path=a[2],l.line=a[3],l.pos=a[4],l.file=r.basename(l.path),o.push(l))}),o}if(i="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return a(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){o.time(...e)},t.timeEnd=function(...e){o.timeEnd(...e)},t.trace=function(...e){let t=a(),n="";t[0]&&t[0].method&&(n=n),o.trace(i,...e)},t.info=function(...e){let t=a(),n="anonymous";t[0]&&t[0].method&&(n=n),o.info(i,"["+n+"]",e.join(","))},t.warn=function(...e){let t=a(),n="";t[0]&&t[0].method&&(n=n),o.warn("<WARN>"+i,"["+n+"]",e.join(","))},t.error=function(...e){let t=a(),n="";t[0]&&t[0].method&&(n=n),o.error("<ERROR>"+i,"["+n+"]",e.join(","))},t.log=function(...e){o.log(i,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},18:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SubtitleFunction=t.TimeTipFunction=t.VipBufferTipFunction=t.XmpPluginFunction=t.XmpSubtitleFunction=t.PlayListFunction=t.ZipFunction=t.XmpPlayerFunction=t.XmpPlayType=t.VideoTipFunction=t.UIFunction=t.MainMenuFunction=t.SnapshotFunction=t.LoginSDKFunction=t.LoginFunction=t.DKFunction=t.ConfigFunction=void 0,function(e){e.saveConfig="SaveConfig",e.getConfigValue="GetConfigValue",e.setConfigValue="SetConfigValue",e.isConfigInitFinish="IsConfigInitFinish",e.getConfigModules="GetConfigModules",e.setXmpAndThunderConfigValue="SetXmpAndThunderConfigValue"}(t.ConfigFunction||(t.ConfigFunction={})),function(e){e.getTaskChannelInfo="GetTaskChannelInfo",e.getTaskBaseInfo="GetTaskBaseInfo",e.getTaskBaseInfos="GetTaskBaseInfos",e.getTaskAttribute="GetTaskAttribute",e.getTaskStatus="GetTaskStatus",e.findTask="FindTask",e.isTaskExist="IsTaskExist",e.isSupportPlay="IsSupportPlay",e.getTaskDownloadSpeed="GetTaskDownloadSpeed",e.enableDcdnWithVipCert="EnableDcdnWithVipCert",e.updateDcdnWithVipCert="UpdateDcdnWithVipCert",e.disableDcdnWithVipCert="DisableDcdnWithVipCert",e.getTpPeerId="GetTpPeerId",e.execSqlite="ExecSqlite",e.querySqlite="QuerySqlite",e.getCategoryViewTaskList="GetCategoryViewTaskList",e.getCategoryViewTaskListForSuspensionWnd="GetCategoryViewTaskListForSuspensionWnd",e.isLoadStorageTaskFinish="IsLoadStorageTaskFinish",e.isSingleBtTask="IsSingleBtTask",e.setTaskExtStat="SetTaskExtStat"}(t.DKFunction||(t.DKFunction={})),function(e){e.login="Login",e.logout="Logout",e.getUserID="GetUserID",e.getSessionID="GetSessionID",e.getSessionIDAw="GetSessionIDAw",e.getLoginOption="GetLoginOption",e.isLogined="IsLogined",e.getAllUserInfo="GetAllUserInfo",e.getAllUserInfoAw="GetAllUserInfoAw",e.getUserInfoEx="GetUserInfoEx",e.getUserInfo="GetUserInfo",e.getUserInfoAw="GetUserInfoAw",e.getUserHeader="GetUserHeader",e.showLoginDlg="ShowLoginDlg",e.createPersonalInfoWnd="CreatePersonalInfoWnd",e.createRetryLoginWnd="CreateRetryLoginWnd",e.switchAccount="SwitchAccount",e.updateHeader="UpdateHeader",e.pingThroughXLUserServer="PingThroughXLUserServer",e.retryLogIn="RetryLogIn",e.createWebWnd="CreateWebWnd",e.isAutoLoginAw="IsAutoLoginAw",e.channel="Channel",e.getVipInfo="GetVipInfo",e.getUserInfoByKeyAw="GetUserInfoByKeyAw"}(t.LoginFunction||(t.LoginFunction={})),function(e){e.getDeviceID="GetDeviceID",e.getInitData="GetInitData",e.saveData="SaveData"}(t.LoginSDKFunction||(t.LoginSDKFunction={})),function(e){e.subtitleIsSupportJpeg="XmpSubtitleIsSupportJpeg",e.queryMediaInfo="QueryMediaInfo",e.openVideoEdit="OpenVideoEdit",e.openGifEdit="OpenGifEdit",e.createSnapShotEx="CreateSnapShotEx",e.createSnapShot="CreateSnapShot",e.createHDPicture="CreateHDPicture",e.isHDPicCreating="IsHDPicCreating",e.openClearBlack="OpenClearBlack",e.closeClearBlack="CloseClearBlack",e.clearBlackWindow="ClearBlackWindow",e.getBlackCollectCount="GetBlackCollectCount",e.getCollectData="GetCollectData",e.getClearBlackRect="GetClearBlackRect",e.createPreViewSnapshot="CreatePreViewSnapshot",e.getPreViewSnapshot="GetPreViewSnapshot"}(t.SnapshotFunction||(t.SnapshotFunction={})),function(e){e.showSystemMenu="ShowSystemMenu"}(t.MainMenuFunction||(t.MainMenuFunction={})),function(e){e.createButton="CreateButton",e.updateButton="UpdateButton",e.createWebview="CreateWebview",e.setDefaultVipSpeedStatus="SetDefaultVipSpeedStatus",e.updateVipSpeedStatus="UpdateVipSpeedStatus",e.getWebviewInfo="GetWebviewInfo",e.createVipView="CreateVipview",e.showCurveChart="showCurveChart",e.applyBrowserView="ApplyBrowserView",e.getBrowserViewId="GetBrowserViewId",e.initBrowserViewConfig="initBrowserViewConfig",e.createVipMenu="CreateVipMenu",e.getMenuSlot="GetMenuSlot",e.openNewTab="OpenNewTab",e.videoPause="DoVideoPause",e.videoPlay="DoVideoPlay",e.videoPauseEx="DoVideoPauseEx",e.videoPlayEx="DoVideoPlayEx",e.setPlaySpeed="SetPlaySpeed",e.setProgressColor="SetProgressColor",e.getProgressColor="GetProgressColor",e.showBufferingEntry="ShowBufferingEntry",e.isBufferingEntryShow="IsBufferingEntryShow",e.showOpenVipEntry="ShowOpenVipEntry",e.registerFunctionCall="RegisterFunctionCall",e.executeFunctionCall="ExecuteFunctionCall",e.videoGetPlayState="VideoGetPlayState",e.setXmpFocus="SetXmpFocus",e.leaveFullScreen="LeaveFullScreen"}(t.UIFunction||(t.UIFunction={})),function(e){e.addItem="VideoTipLeftTopAddItem",e.addLinkItem="VideoTipLeftTopAddLinkItem",e.updateItemText="VideoTipLeftTopUpdateItemText",e.removeItem="VideoTipLeftTopRemoveItem",e.showTip="VideoTipLeftTopShowTip",e.closeTip="VideoTipLeftTopCloseTip",e.hideTip="HideTip"}(t.VideoTipFunction||(t.VideoTipFunction={})),function(e){e.unKnow="UnKnow",e.localFile="LocalFile",e.downloadAndPlay="DownloadAndPlay",e.yunBo="YunBo"}(t.XmpPlayType||(t.XmpPlayType={})),function(e){e.init="XmpPlayerInit",e.showAPlayerWindow="XmpShowAPlayerWindow",e.createXmpMedia="XmpPlayerCreateXmpMedia",e.open="XmpPlayerOpen",e.openFile="XmpPlayerOpenFile",e.addFile="XmpPlayerAddFile",e.openFolder="XmpPlayerOpenFolder",e.openBt="XmpPlayerOpenBt",e.openUrl="XmpPlayerOpenUrl",e.openMedia="XmpPlayerOpenMedia",e.close="XmpPlayerClose",e.stop="XmpPlayerStop",e.play="XmpPlayerPlay",e.pause="XmpPlayerPause",e.getVersion="XmpPlayerGetVersion",e.getState="XmpPlayerGetState",e.isPlaying="XmpPlayerIsPlaying",e.getDuration="XmpPlayerGetDuration",e.getPosition="XmpPlayerGetPosition",e.setPosition="XmpPlayerSetPosition",e.getVideoWidth="XmpPlayerGetVideoWidth",e.getVideoHeight="XmpPlayerGetVideoHeight",e.getVolume="XmpPlayerGetVolume",e.setVolume="XmpPlayerSetVolume",e.isSeeking="XmpPlayerIsSeeking",e.getBufferProgress="XmpPlayerGetBufferProgress",e.getConfig="XmpPlayerGetConfig",e.setConfig="XmpPlayerSetConfig",e.setCurrentMedia="XmpPlayerSetCurrentMedia",e.getCurrentMedia="XmpPlayerGetCurrentMedia",e.getLastMediaId="XmpPlayerGetLastMediaId",e.getMediaAttribute="XmpPlayerMediaAttribute",e.getCurrentMediaAttribute="XmpPlayerGetCurrentMediaAttribute",e.clearCurrentMedia="XmpPlayerClearCurrentMedia",e.openPreMedia="XmpPlayerOpenPreMedia",e.openNextMedia="XmpPlayerOpenNextMedia",e.isPlayOrderFirstMedia="XmpPlayerIsPlayOrderFirstMedia",e.isPlayOrderLastMedia="XmpPlayerIsPlayOrderLastMedia",e.isFirstMedia="XmpPlayerIsFirstMedia",e.isLastMedia="XmpPlayerIsLastMedia",e.isMediasLoadFinished="XmpPlayerIsMediasLoadFinished",e.getMediasInfo="XmpPlayerGetMediasInfo",e.removeMedia="XmpPlayerRemoveMedia",e.removeAllMedia="XmpPlayerRemoveAllMedia",e.setASpectRatioAtive="XmpPlayerSetASpectRatioAtive",e.setPlayForward="XmpPlayerSetPlayForward",e.postionChange="XmpPlayerPostionChange",e.getBufferRange="XmpPlayerGetBufferRange",e.fireKeyDown="XmpPlayerFireKeyDown",e.fireKeyUp="XmpPlayerFireKeyUp",e.setAudioTime="XmpPlayerSetAudioTime",e.setProportion="XmpPlayerSetProportion",e.setVlomue="XmpPlayerSetVlomue",e.getCurView="XmpPlayerGetCurView",e.recordStat="XmpPlayerRecordStat",e.getHostWnd="XmpPlayerGetHostWnd",e.startWindowZorderChecker="StartWindowZorderChecker",e.stopWindowZorderChecker="StopWindowZorderChecker",e.setZOrderCheckerWindowHandle="SetZOrderCheckerWindowHandle",e.removeZOrderCheckerWindowHandle="RemoveZOrderCheckerWindowHandle",e.getVideoDuration="GetVideoDuration",e.getDownloadState="GetDownloadState",e.getResConfig="GetResConfig",e.queryTaskState="QueryTaskState",e.shareMediaInfo="ShareMediaInfo",e.getThunderDirState="GetThunderDirState",e.setThunderDirState="SetThunderDirState",e.showSyncDig="ShowSyncDig",e.isXmpLite="IsXmpLite",e.handleVipPlaySpeed="handleVipPlaySpeed",e.getMediaInfo="GetMediaInfo",e.getCurrentMediaInfo="GetCurrentMediaInfo",e.setMediaAttribute="XmpPlayerSetMediaAttribute",e.updateDownloadEmbedRect="UpdateDownloadEmbedRect",e.updatePanEmbedRect="UpdatePanEmbedRect",e.createIndependentWindow="CreateIndependentWindow",e.thunderResize="ThunderResize",e.updateScrollShowOrHide="UpdateScrollShowOrHide",e.updateFloatWindow="IpdateFloatWindow",e.updateAplayerHwnd="UpdateAplayerHwnd",e.updatePlaySource="UpdatePlaySource",e.setXmpCurrentMode="SetXmpCurrentMode",e.getXmpCurrentMode="GetXmpCurrentMode",e.isSwitchXmpMode="IsSwitchXmpMode",e.destroy="XmpDestroy",e.maximize="XmpMaximize",e.unmaximize="XmpUnmaximize",e.minmized="XmpMinmized",e.setPlaySpeed="SetPlaySpeed",e.getPlaySpeed="GetPlaySpeed",e.setWindowTopMode="SetWindowTopMode",e.calcelWindowTopMode="CalcelWindowTopMode",e.enterFullScreen="EnterFullScreen",e.leaveFullScreen="LeaveFullScreen",e.setFullcScreen="SetFullcScreen",e.getIsFullScreen="GetIsFullScreen",e.getIsFullScreenEx="GetIsFullScreenEx",e.setPlayingTaskId="SetPlayingTaskId",e.setlayPingPanFileId="SetPlayingPanFileId",e.setSelectTaskId="SetSelectTaskId",e.setSelectPanel="SetSelectPanel",e.setSelectTab="SetSelectTab",e.setSelectPanFileId="SetSelectPanFileId",e.getPlayingTaskId="GetPlayingTaskId",e.getPlayingPanFileId="GetPlayingPanFileId",e.getSelectTaskId="GetSelectTaskId",e.getSelectPanel="GetSelectPanel",e.getSelectTab="GetSelectTab",e.getSelectPanFileId="GetSelectPanFileId",e.getXmpPlayType="GetXmpPlayType",e.getDownloadAndPlayTaskData="GetDownloadAndPlayTaskData",e.openDLNA="OpenDLNA",e.disConnectDLNA="DisConnectDLNA",e.getCurrentDLNADeviceName="GetCurrentDLNADeviceName",e.addXmpVideoTipPos="AddXmpVideoTipPos",e.removeXmpVideoTipPos="RemoveXmpVideoTipPos",e.addXmpVideoTipPosMainRenderer="AddXmpVideoTipPosRenderer",e.removeXmpVideoTipPosMainRenderer="RemoveXmpVideoTipPosRenderer",e.switchSilent="XmpSwitchSilent",e.isSilent="XmpIsSilent",e.cancelDownloadCodecs="OnCancelDownloadCodecs",e.getCachedDownloadCodecInfo="OnGetCachedDownloadCodecInfo",e.onTaskStopped="OnTaskStopped",e.showOrHideXmpWindow="OnShowOrHideXmpWindow",e.getPlayingSoure="OnGetPlayingSoure",e.userLogout="OnUserLogout",e.isCurrentBuffingIsByDrag="IsCurrentBuffingIsByDrag",e.thunderQuit="ThunderQuit",e.getLastPlayError="GetLastPlayError",e.showAplayerAndFloatWindow="ShowAplayerAndFloatWindow",e.hideAplayerAndFloatWindow="HideAplayerAndFloatWindow",e.hideEmbedAplayerAndFloatWindow="HideEmbedAplayerAndFloatWindow",e.isPlayMusic="IsPlayMusic",e.getMediaById="GetMediaById",e.setPlayEndInfo="SetPlayEndInfo",e.getPlayId="GetPlayId",e.getPlayTick="GetPlayTick",e.setAudioTrack="SetAudioTrack",e.vipTellPopupWindowShow="VipTellPopupWindowShow",e.vipTellPopupWindowClose="VipTellPopupWindowClose"}(t.XmpPlayerFunction||(t.XmpPlayerFunction={})),function(e){e.doGetFileList="ZipDoGetFileList",e.doDelFileList="ZipDoDelFileList",e.abortGetFileList="ZipAbortGetFileList",e.openFile="ZipOpenFile",e.openPassWord="ZipOpenPassWord",e.openIndex="ZipOpenIndex",e.makeIndexFile="ZipMakeIndexFile",e.abortMakeIndexFile="ZipAbortMakeIndexFile",e.needWaitIndexFile="ZipNeedWaitIndexFile",e.isIndexError="ZipIsIndexError",e.isPasswordError="ZipIsPasswordError",e.isFilePlaying="ZipIsFilePlaying"}(t.ZipFunction||(t.ZipFunction={})),function(e){e.getMediaPlayed="PlayList.GetMediaPlayed",e.setMediaPlayed="PlayList.SetMediaPlayed"}(t.PlayListFunction||(t.PlayListFunction={})),function(e){e.isSubtitleShow="XmpSubtitleIsSubtitleShow",e.isSubtitle2Show="XmpSubtitleIsSubtitle2Show",e.showSubtitle="XmpSubtitleShowSubtitle",e.showSubtitle2="XmpSubtitleShowSubtitle2",e.getCurrentSubtitle="XmpSubtitleGetCurrentSubtitle",e.getCurrentSubtitle2="XmpSubtitleGetCurrentSubtitle2",e.getSubtitleList="XmpSubtitleGetSubtitleList",e.loadSubtitleFile="XmpSubtitleLoadSubtitleFile",e.setCurrentSubtitle="XmpSubtitleSetCurrentSubtitle",e.setSubtitleLanguageIndex="XmpSubtitleSetSubtitleLanguageIndex",e.setSubtitlePosition="XmpSubtitleSetSubtitlePosition",e.getSubtitlePosition="XmpSubtitleGetSubtitlePosition",e.setSubtitleFontStyle="XmpSubtitleSetSubtitleFontStyle",e.getSubtitleSize="XmpSubtitleGetSubtitleSize",e.getSubtitleColor="XmpSubtitleGetSubtitleColor",e.getSubtitleFont="XmpSubtitleGetSubtitleFont",e.setSubtitleTimming="XmpSubtitleSetSubtitleTimming",e.getSubtitleTimming="XmpSubtitleGetSubtitleTimming",e.setSubtitle3DMode="XmpSubtitleSetSubtitle3DMode",e.setUseHardware="XmpSubtitleSetUseHardware",e.reportSubTitle="ReportSubTitle",e.uploadSubTitle="UploadubTitle",e.onSubtitleInit="XmpSubtitleInit",e.onSubtitleLoadSuc="XmpSubtitleLoadSuc",e.createSuppress="CreateSuppress",e.getSuppressState="GetSuppressState",e.getSuppressPlayUrl="GetSuppressPlayUrl",e.getSuppressProgress="GetSuppressProgress",e.getSuppressNeedTime="GetSuppressNeedTime",e.showSubtitleSuppressDialog="ShowSubtitleSuppressDialog",e.continueSuppress="ContinueSuppress",e.getFullState="GetFullState",e.isSettingDefaultSecondSubtitle="IsSettingDefaultSecondSubtitle",e.isSecondSubtitle="IsSecondSubtitle"}(t.XmpSubtitleFunction||(t.XmpSubtitleFunction={})),function(e){e.getTaskInfo="GetTaskInfo",e.shellOpen="ShellOpen",e.openFolderWithSelectFile="OpenFolderWithSelectFile",e.getFileMd5="GetFileMd5",e.getPeerID="GetPeerID",e.getDeviceID="GetDeviceID",e.getThunderVersion="GetThunderVersion",e.getXmpVersion="GetXmpVersion",e.trackEvent="TrackEvent",e.getCurInternetState="GetCurInternetState",e.getCurPlatform="GetCurPlatform",e.getXmpShowMode="GetXmpShowMode",e.isModuleEnable="IsModuleEnable",e.updateTabSelectState="UpdateTabSelectState",e.getMouseShow="getMouseShow",e.updateFullFill="UpdateFullFill",e.getWindowTopMode="GetWindowTopMode",e.isVideoEditMode="IsVideoEditMode",e.showCtrlView="ShowCtrlView",e.showTopView="ShowTopView",e.showMoreAbout="ShowMoreAbout",e.setMenuPopUp="SetMenuPopUp",e.setWindowPopUp="SetWindowPopUp",e.isListExpand="IsListExpand",e.isMenuPopUp="IsMenuPopUp",e.isWindowPopUp="IsWindowPopUp",e.isMouseInFloat="IsMouseInFloat",e.switchtoNormalMode="SwitchtoNormalMode",e.switchToTransparentMode="SwitchToTransparentMode",e.dropOpenFiles="DropOpenFiles",e.dropOpenUrl="DropOpenUrl",e.getShowCondition="GetShowCondition",e.utf8StringEncodeToBinary="Utf8StringEncodeToBinary",e.utf8StringDecodeFromBinary="Utf8StringDecodeFromBinary",e.getErrorCodeConfigMessage="GetErrorCodeConfigMessage",e.quitProcess="QuitProcess",e.associate="Associate",e.checkXmpUpdate="CheckXmpUpdate",e.changeVideoRatio="ChangeVideoRatio",e.getVideoScaleRate="GetVideoScaleRate",e.getHoverPos="GetHoverPos",e.setHoverPos="SetHoverPos",e.getCurrentDevice="GetCurrentDevice",e.activeDevice="ActiveDevice",e.setCurrentDevice="SetCurrentDevice",e.getDeviceList="GetDeviceList",e.searchDevice="SearchDevice",e.isDeviceSupport="IsDeviceSupport",e.getInitUserLoginParam="GetInitUserLoginParam",e.handleMouseTimeOut="HandleMouseTimeOut",e.getOpenFrom="GetOpenFrom",e.setHideAndPauseWindow="SetHideAndPauseWindow",e.setHideWindowNotPause="SetHideWindowNotPause",e.setFullWindowMouseEnter="SetFullWindowMouseHover",e.setDragMove="SetDragMove",e.isEmbedMode="IsEmbedMode",e.getBackUpValue="getBackUpValue"}(t.XmpPluginFunction||(t.XmpPluginFunction={})),function(e){e.getVideoStuckVipData="GetVideoStuckVipData",e.openPayVipUrl="OpenPayVipUrl",e.actionPreOpen="ActionPreOpen"}(t.VipBufferTipFunction||(t.VipBufferTipFunction={})),function(e){e.showTimeTips="ShowTimeTips",e.destroyTimeTips="DestroyTimeTips"}(t.TimeTipFunction||(t.TimeTipFunction={})),function(e){e.loadSubtitle="LoadSubtitle",e.onlineSubtitleMatch="OnlineSubtitleMatch",e.showOrHideSubtitle="ShowOrHideSubtitle",e.isEnableHideSubtitle="IsEnableHideSubtitle",e.isSubtitleHided="IsSubtitleHided",e.loadEmbedSubtitle="LoadEmbedSubtitle",e.loadOnlineSubtitle="LoadOnlineSubtitle",e.loadLocalSubtitle="LoadLocalSubtitle",e.getOnlineSubtitleList="GetOnlineSubtitleList",e.getEmbedSubtitleList="GetEmbedSubtitleList",e.getLocalSubtitleList="GetLocalSubtitleList",e.getManualSubtitleList="GetManualSubtitleList",e.getCurrentTitle="GetCurrentTitle",e.getCurrentTitle2="GetCurrentTitle2",e.manualLoadSubtitle="ManualLoadSubtitle",e.setSubtitleFontStyle="SetSubtitleFontStyle",e.getSubtitleFontStyle="GetSubtitleFontStyle",e.setSubtitlePosition="SetSubtitlePosition",e.getSubtitlePosition="GetSubtitlePosition",e.setUseHardware="SetUseHardware",e.getSubtitleTimming="GetSubtitleTimming",e.setSubtitleTimming="SetSubtitleTimming"}(t.SubtitleFunction||(t.SubtitleFunction={}))},2:function(e,t,n){"use strict";function r(e,t,n,r,i,o,a,l){var s,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),o&&(c._scopeId="data-v-"+o),a?(s=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},c._ssrRegister=s):i&&(s=l?function(){i.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:i),s)if(c.functional){c._injectStyles=s;var u=c.render;c.render=function(e,t){return s.call(t),u(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,s):[s]}return{exports:e,options:c}}n.d(t,"a",function(){return r})},20:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(34),i=n(5);t.getDefaultPrex=function(){return i.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=i.basename(process.execPath,".exe"));let o=i.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(o="\\\\.\\pipe\\"+(o=(o=o.replace(/^\//,"")).replace(/\//g,"-"))),o},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},207:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{s(r.next(e))}catch(e){o(e)}}function l(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderToolsNS=void 0;const i=n(41),o=n(1),a=n(5),l=n(25),s=n(18),c=n(42),u=l.default(a.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(e,t){return r(this,void 0,void 0,function*(){if(null!==e){let n=e.webContents;(yield n.isDevToolsOpened())?yield n.closeDevTools():yield n.openDevTools(t)}})}e.openDevTool=t,e.enableDevTools=function(e){return r(this,void 0,void 0,function*(){(yield i.FileSystemAWNS.existsAW(a.resolve(__rootDir,"../../enableDevTools")))&&window.addEventListener("keyup",n=>r(this,void 0,void 0,function*(){"F12"===n.key&&n.ctrlKey&&t(yield c.rpc.getCurrentWindow(),e).catch()}),!0)})},e.enableDragOpenFile=function(e){void 0===e&&(e=!1),document.addEventListener("dragover",e=>{e.preventDefault()},!1),document.addEventListener("drop",e=>r(this,void 0,void 0,function*(){e.preventDefault();let t=e.dataTransfer,n=t.files,r=t.items;if(void 0!==r&&null!==r&&r.length>0)for(let e=0;e<r.length;e++){let t=r[e];"string"===t.kind&&"text/uri-list"===t.type?t.getAsString(e=>{o.client.callServerFunctionEx(s.XmpPluginFunction.dropOpenUrl,e).catch()}):t.kind}if(void 0!==n&&null!==n&&n.length>0){let e=[];for(let t=0;t<n.length;t++){let r=n[t].path;void 0!==r&&null!==r&&""!==r&&e.push(r)}e.length>0&&o.client.callServerFunctionEx(s.XmpPluginFunction.dropOpenFiles,e).catch()}}),!1)},e.setBugreportUrlInfo=function(){return r(this,void 0,void 0,function*(){let e=yield c.rpc.getCurrentWindow();if(e&&!(yield e.isDestroyed())){let t=yield e.webContents.getURL();u.setBugreportCustomInfo(t)}})},e.setBugreportCustomInfo=function(e){u.setBugreportCustomInfo(e||"xmp-xdas-renderer")},e.setBugreportSilentMode=function(){u.setBugreportShowMode(1)}}(t.ThunderToolsNS||(t.ThunderToolsNS={}))},220:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i});var r=function(){var e=this.$createElement;this._self._c;return this._m(0)},i=[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"aplayer-padding"},[t("div",{staticClass:"aplayer-draggable"})])}];r._withStripped=!0},24:function(e,t){e.exports=require("util")},25:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},26:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{s(r.next(e))}catch(e){o(e)}}function l(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderHelper=void 0;const i=n(5),o=n(25),a=n(63),l=n(0).default.getLogger("XMP.ThunderHelper"),s=o.default(i.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(e){return r(this,void 0,void 0,function*(){if(!e||(yield e.isDestroyed()))return 0;let t=0,n=yield e.getNativeWindowHandle();return n&&(t=n.readUIntLE(0,4)),t})}function i(e,t,n,r,i,o,l,c){if(!s||!e)return;let u="";c&&c.length>3&&(u=c.substring(0,c.length-3)),l|=a.Uflag.SWP_ASYNCWINDOWPOS,s.setWindowPos(e,t,n,r,i,o,l)}function o(){return s.getDPIAwareSupport()}function c(e){return s.getMonitorDPIFactor(e)}function u(){return s.getSysDPIFactor()}e.getDriveInfo=function(e){return s.getDriveInfo(e)},e.getFreePartitionSpace=function(e){return s.getFreePartitionSpace(e)},e.getLogicalDriveStrings=function(){return s.getLogicalDriveStrings()},e.getPartitionSpace=function(e){return s.getPartitionSpace(e)},e.getSystemTempPath=function(){return s.getSystemTempPath()},e.getTaskTypeFromUrl=function(e){return s.getTaskTypeFromUrl(e)},e.dwmIsCompositionEnabled=function(){return s.dwmIsCompositionEnabled()},e.setWindowLong=function(e,t,n){return s.setWindowLong(e,t,n)},e.showCursor=function(e){s&&s.showCursor(e)},e.isWindowVisible=function(e){return!!s&&s.isWindowVisible(e)},e.isWindowEnable=function(e){return!!s&&s.isWindowEnable(e)},e.enableWindow=function(e,t){return!!s&&s.enableWindow(e,t)},e.shChangeNotify=function(){if(s)return s.refreshIcon()},e.getBrowserWindowHandle=t,e.setTopMostWindow=function(e){return r(this,void 0,void 0,function*(){s&&e&&i(yield t(e),a.OptionOfHWNDInAfter.HWND_TOPMOST,0,0,0,0,a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE)})},e.getGraphicsAdapterList=function(){return s?s.getGraphicsAdapterList():null},e.getFileVersion=function(e){return s&&e?s.getFileVersion(e):""},e.getCursorPos=function(){return s?s.getCursorPos():{x:0,y:0}},e.getPowerInfo=function(){return s?s.getPowerInfo():null},e.setWindowRectRgn=function(e,t,n,r,i){s&&s.setWindowRectRgn(e,t,n,r,i)},e.createRectRgn=function(e,t,n,r){return s?s.createRectRgn(e,t,n,r):0},e.combineRgn=function(e,t,n=a.CombineRgnType.RGN_OR){return s?s.combineRgn(e,t,n):-1},e.deleteObject=function(e){return!!s&&s.deleteObject(e)},e.setWindowRgn=function(e,t,n=!0){return s?s.setWindowRgn(e,t,n):-1},e.hitCaption=function(e){return!!s&&s.hitCaption(e)},e.isWindowForeGround=function(e){return r(this,void 0,void 0,function*(){if(!s)return!1;let t=s.getForegroundWindow();return e===t})},e.getForegroundWindow=function(){return s?s.getForegroundWindow():0},e.isWindowForceGroundEx=function(e){return!!s&&e===s.getForegroundWindow()},e.setListExpandMode=function(e){return s?s.setListExpandMode(e):0},e.setTopMostWindowEx=function(e){s&&e&&i(e,a.OptionOfHWNDInAfter.HWND_TOPMOST,0,0,0,0,a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE)},e.cancelTopMostWindow=function(e,n){return r(this,void 0,void 0,function*(){if(!s||!e)return;let r=yield t(e),o=a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE|a.Uflag.SWP_NOACTIVATE;n&&(o|=n),i(r,a.OptionOfHWNDInAfter.HWND_NOTOPMOST,0,0,0,0,o)})},e.cancelTopMostWindowEx=function(e,t){if(!s||!e)return;let n=a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE|a.Uflag.SWP_NOACTIVATE;t&&(n|=t),i(e,a.OptionOfHWNDInAfter.HWND_NOTOPMOST,0,0,0,0,n)},e.getTempPath=function(){return s?s.getSystemTempPath():""},e.switchToThisWindow=function(e,t=!1){s&&s.switchToThisWindow(e,t)},e.setScreenSaveActive=function(e){if(s)return s.setScreenSaveActive(e)},e.getDiskFreeSpace=function(e){return s?s.getFreePartitionSpace(e):0},e.getMemInfoByPointer=function(e,t){return s?s.getMemInfoByPointer(e,t):null},e.setMinTrackSize=function(e,t,n){return s?s.setMinTrackSize(e,t,n):null},e.setWindowPos=function(e,t,n,r,o,a,l,c){if(!s||!e||e.isDestroyed())return;i(e.getNativeWindowHandle().readUIntLE(0,4),t,n,r,o,a,l,c)},e.moveWindowGently=function(e,t,n,r,i,o,a){s&&t&&s.moveWindowGently(e,t,n,r,i,o,a)},e.moveWindow=function(e,t){s&&e&&s.moveWindow(e,t.x,t.y,t.width,t.height,!0)},e.setWindowFocus=function(e,t=!0){s&&e&&s.setWindowFocus(e,t)},e.getWindowRect=function(e){return s&&e?s.getWindowRect(e):{x:0,y:0,width:0,height:0}},e.getClientRect=function(e){return s&&e?s.getClientRect(e):{x:0,y:0,width:0,height:0}},e.getCurrentScreenRect=function(e){return s&&e?s.getCurrentScreenRect(e):{x:0,y:0,width:0,height:0}},e.getCurrentWorkRect=function(e){return s&&e?s.getCurrentScreenRect(e,!0):{x:0,y:0,width:0,height:0}},e.getChildWindow=function(e){return s&&e?s.getChildWindow(e):0},e.getParentWindow=function(e){return s&&e?s.getParentWindow(e):0},e.getKeyState=function(e){return s?s.getKeyState(e):0},e.setWindowPosEx=i,e.beginDeferWindowPos=function(e){return s&&e?s.beginDeferWindowPos(e):0},e.endDeferWindowPos=function(e){return s&&e?s.endDeferWindowPos(e):0},e.deferWindowPos=function(e,t,n,r,i,o,a,l){return s&&e?s.deferWindowPos(e,t,n,r,i,o,a,l):0},e.bindChildWindow=function(e,t,n){if(!s||!t||!e||t.isDestroyed()||e.isDestroyed())return;let r=t.getNativeWindowHandle().readUIntLE(0,4),i=e.getNativeWindowHandle().readUIntLE(0,4);n?s.bindChildWindow(i,r,n.x,n.y,n.width,n.height):s.bindChildWindow(i,r)},e.bindChildWindowEx=function(e,t,n){s&&t&&e&&(n?s.bindChildWindow(e,t,n.x,n.y,n.width,n.height):s.bindChildWindow(e,t))},e.setDllDirectory=function(e){s&&e&&s.setDllDirectory(e)},e.bringWindowToTop=function(e){if(!e||e.isDestroyed())return;let t=e.getNativeWindowHandle().readUIntLE(0,4);s.bringWindowToTop(t)},e.bringWindowToTopEx=function(e){e&&s.bringWindowToTop(e)},e.modifyStyleEx=function(e,t,n){e&&s.modifyStyle(e,t,n)},e.modifyStyle=function(e,t,n){if(!e||e.isDestroyed())return;let r=e.getNativeWindowHandle().readUIntLE(0,4);s.modifyStyle(r,t,n)},e.registerSliderCreate=function(e){s.registerSliderCreate(e)},e.showSlider=function(e){s.showSlider(e)},e.initSideBar=function(e,t,n,r){s.initSideBar(e,t,n,r)},e.uninitSideBar=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{s.uninitSideBar(()=>{e()})})})},e.createSideBar=function(){s.createSideBar()},e.destroySideBar=function(){s.destroySideBar()},e.readRegValue=function(e,t,n){return s?s.readRegString(e,t,n):""},e.setForcegroundWindow=function(e){if(!e||e.isDestroyed())return;let t=e.getNativeWindowHandle().readUIntLE(0,4);s.setForegroundWindow(t)},e.setActiveWindowEx=function(e){s&&e&&s.setActiveWindow(e)},e.setForcegroundWindowEx=function(e){e&&s.setForegroundWindow(e)},e.is64BitWindows=function(){return!!s&&s.is64bitSystem()},e.setSideBarExpandState=function(e){s.setExpandState(e)},e.unBindChildWindow=function(e,t){if(!s||!t||!e||t.isDestroyed()||e.isDestroyed())return;l.information("unBindChildWindow");let n=t.getNativeWindowHandle().readUIntLE(0,4),r=e.getNativeWindowHandle().readUIntLE(0,4);s.unBindChildWindow(r,n)},e.unBindChildWindowEx=function(e,t){s&&t&&e&&(l.information("unBindChildWindowEx"),s.unBindChildWindow(e,t))},e.addBrowserWindowClipStyle=function(e){if(!s||!e||e.isDestroyed())return!1;let t=e.getNativeWindowHandle().readUIntLE(0,4),n=s.findChildWindow(t,"Intermediate D3D Window");return n&&s.setWindowLong(n,a.SetWindowType.GWL_STYLE,a.WindowStyle.WS_CLIPSIBLINGS),s.setWindowLong(t,a.SetWindowType.GWL_STYLE,a.WindowStyle.WS_CLIPCHILDREN),!!n},e.addBrowserWindowClipStyleEx=function(e){if(!s)return!1;let t=s.findChildWindow(e,"Intermediate D3D Window");return t&&s.setWindowLong(t,a.SetWindowType.GWL_STYLE,a.WindowStyle.WS_CLIPSIBLINGS),s.setWindowLong(e,a.SetWindowType.GWL_STYLE,a.WindowStyle.WS_CLIPCHILDREN),!!t},e.getDoubleClickTime=function(){return s?s.getDoubleClickTime():50},e.invalidateRect=function(e,t,n){s&&s.invalidateRect(e,t,n)},e.isIconic=function(e){return!!s&&s.isIconic(e)},e.isZoomed=function(e){return!!s&&s.isZoomed(e)},e.showWindow=function(e){return!!s&&s.showWindow(e,a.ShowWindowCmd.SW_SHOW)},e.hideWindow=function(e){return!!s&&s.showWindow(e,a.ShowWindowCmd.SW_HIDE)},e.setZorderWindow=function(e,t,n){if(s)return s.setZorderWindow(e,t,n)},e.getWindowLong=function(e,t){return s?s.getWindowLong(e,t):0},e.getHelperObject=function(){return s},e.getPeerID=function(){return s.getPeerID()},e.getDPIAwareSupport=o,e.getMonitorDPIFactor=c,e.getSysDPIFactor=u,e.getDpi=function(e){let t=1;return t=o()?c(e):u()},e.killProcess=function(e){return r(this,void 0,void 0,function*(){let t=null;{let r="tasklist",i=n(106).exec;t=new Promise(t=>{i(r,function(n,r,i){let o=!1;do{if(n){l.warning(n),o=!1;break}r.split("\n").filter(function(t){let n=t.trim().split(/\s+/);if(n.length<2)return;let r=n[0],i=Number(n[1]);r.toLowerCase().indexOf(e.toLowerCase())>=0&&i&&(process.kill(i,"SIGTERM"),o=!0)})}while(0);t(o)})})}return t})},e.sleep=function(e){return r(this,void 0,void 0,function*(){yield new Promise((t,n)=>{setTimeout(t,e)})})},e.calculateFileGCID=function(e){return r(this,void 0,void 0,function*(){return new Promise(t=>{s.calculateFileGCID(e,e=>{l.warning("getVideoMediaInfo calculateFileGCID result",e),t(e)})})})},e.getTickCount=function(){return s.getTickCount()},e.getTextScale=function(){return Number(s.readRegString("HKEY_CURRENT_USER","SOFTWARE\\Microsoft\\Accessibility","TextScaleFactor"))||100}}(t.ThunderHelper||(t.ThunderHelper={}))},29:function(e,t){e.exports=vendor_67f12d0c83789636af43},34:function(e,t){e.exports=require("os")},353:function(e,t,n){n(95),e.exports=n(354)},354:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(105);n(373);const i=n(355),o=n(1),a=n(207);o.client.start({name:"main-window",version:""},"thunder",!1,(e,...t)=>{}),new r.default({components:{App:i.default},render:e=>e("app")}).$mount("#app"),a.ThunderToolsNS.enableDevTools().catch()},355:function(e,t,n){"use strict";n.r(t);var r=n(220),i=n(164);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return i[e]})}(o);n(356);var a=n(2),l=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);l.options.__file="src/main-window-renderer/app.vue",t.default=l.exports},356:function(e,t,n){"use strict";var r=n(393);n.n(r).a},373:function(e,t){},39:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},393:function(e,t){},40:function(e,t,n){"use strict";const r=n(17);if("renderer"===process.type){if(r.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(98);e.require=t.remoteRequire,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,r)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,r)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(r.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(102);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},41:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{s(r.next(e))}catch(e){o(e)}}function l(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.FileSystemAWNS=void 0;const i=n(45),o=n(5),a=n(24),l=n(34),s=a.promisify,c=n(0).default.getLogger("Thunder.base.fs-utilities");!function(e){function t(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.open);try{t=yield n(e,"r")}catch(e){c.warning(e)}}return t})}function n(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.access);try{yield n(e),t=!0}catch(e){c.information(e)}}return t})}function a(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.mkdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function u(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.rmdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function d(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=s(i.unlink);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function f(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.readdir);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function p(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.lstat);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function h(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let r=o.join(e,t),i=yield p(r);n=null!==i&&i.isDirectory()?yield g(r):yield d(r)}return n})}function g(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){if(yield n(e)){t=!0;let n=yield f(e);for(let r=0;r<n.length;r++)t=(yield h(e,n[r]))&&t;(t||0===n.length)&&(t=(yield u(e))&&t)}}return t})}function _(e){return r(this,void 0,void 0,function*(){let t=!1;return c.information("mkdirsAW",e),void 0!==e&&((yield n(e))?t=!0:o.dirname(e)===e?t=!1:(yield _(o.dirname(e)))&&(t=yield a(e))),t})}function m(e,t){return r(this,void 0,void 0,function*(){let r;if(e.toLowerCase()!==t.toLowerCase()&&(yield n(e))){let n=i.createReadStream(e),o=i.createWriteStream(t);r=new Promise(e=>{n.pipe(o).on("finish",()=>{e(!0)})})}else r=new Promise(e=>{e(!1)});return r})}e.readFileAW=function(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=s(i.readFile);try{t=yield n(e)}catch(e){c.warning(e)}}return t})},e.openFile=t,e.readFilePartAw=function(e,n,o,a=0){return r(this,void 0,void 0,function*(){let r={bytesRead:0,buffer:new Buffer(0)},l=yield t(e);if(!l)return r;const u=s(i.read);try{let e=new Buffer(o);r=yield u(l,e,n,o,a),e=void 0}catch(e){c.warning(e)}return yield s(i.close)(l),r})},e.writeFileAW=function(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const r=s(i.writeFile);try{yield r(e,t),n=!0}catch(e){c.warning(e)}}return n})},e.existsAW=n,e.mkdirAW=a,e.rmdirAW=u,e.unlinkAW=d,e.readdirAW=f,e.readdirAllFiles=function e(t){return r(this,void 0,void 0,function*(){let r=!1,i=[],a=yield p(t),l=yield f(t);if(!l)return i;for(let s=0;s<l.length;s++){let c=o.join(t,l[s]);(r=yield n(c))&&((a=yield p(c)).isDirectory()?i.push(...yield e(c)):i.push(c))}return i})},e.lstatAW=p,e.rmdirsAW=g,e.mkdirsAW=_,e.renameAW=function(e,t){return r(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=s(i.rename);try{yield n(e,t)}catch(e){c.warning(e)}}})},e.copyFileAW=m,e.copyDirsAW=function e(t,i){return r(this,void 0,void 0,function*(){let r=!1,a=yield p(t);if(a.isDirectory()){r=yield _(i);let l=yield f(t);for(let s=0;s<l.length;s++){let c=o.join(t,l[s]),u=o.join(i,l[s]);(r=yield n(c))&&(r=(a=yield p(c)).isDirectory()?yield e(c,u):yield m(c,u))}}return r})},e.mkdtempAW=function(){return r(this,void 0,void 0,function*(){let e=!1;const t=s(i.mkdtemp),n=l.tmpdir();try{e=yield t(`${n}${o.sep}`)}catch(e){c.warning(e)}return e})},e.getFileSize=function e(t){return r(this,void 0,void 0,function*(){let r=0;do{if(!t)break;if(!(yield n(t)))break;let i=yield p(t);if(i)if(i.isDirectory()){let n=yield f(t);for(let i=0;i<n.length;i++)r+=(yield e(o.join(t,n[i])))}else r=i.size}while(0);return r})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},42:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{s(r.next(e))}catch(e){o(e)}}function l(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.rpc=void 0;const i=n(40),o=n(8),a=n(11),l=n(24);t.rpc=new class{constructor(){this.mapObj=new Map,this.mapObjIniting=new Map,this.emitter=new a.EventEmitter}isXmpLiteEx(){return"renderer"!==process.type&&"xmplite"===o.app.getName()}getAppName(){return r(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}isXmpLite(){return r(this,void 0,void 0,function*(){return"xmplite"===(yield this.getAppName())})}getAppVersion(){return r(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return r(this,void 0,void 0,function*(){return i.global.process.__resolve()})}getIpcMain(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getWebContents(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("webContents")})}getBrowserWindow(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getGlobalShortcut(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.emitter.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield i.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emitter.emit("OnInitCurrentWebContents",e),this.emitter.listeners("OnInitCurrentWebContents").forEach(e=>{this.emitter.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.emitter.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield i.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emitter.emit("OnInitCurrentWindow",e),this.emitter.listeners("OnInitCurrentWindow").forEach(e=>{this.emitter.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return r(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return l.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>r(this,void 0,void 0,function*(){this.emitter.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield i.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emitter.emit(e,t),this.emitter.listeners(e).forEach(t=>{this.emitter.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}},45:function(e,t){e.exports=require("fs")},5:function(e,t){e.exports=require("path")},54:function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(r||(r={})),t.default=r},55:function(e,t,n){"use strict";var r;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(r||(r={})),e.exports=r},60:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderChannelList=void 0,function(e){e.channelBase="ChannelBase",e.channelRMNewTaskSetTaskData=e.channelBase+"1",e.channelRMPreNewTaskSetTaskData=e.channelBase+"2",e.channelRMNewTaskCreateNewTask=e.channelBase+"3",e.channelRMNewTaskSetBTInfo=e.channelBase+"4",e.channelRMNewTaskDownloadTorrent=e.channelBase+"5",e.channelRMNewTaskCreateBtTask=e.channelBase+"6",e.channelRMNewTaskCancleMagnet=e.channelBase+"7",e.channelRMImportTorrent=e.channelBase+"8",e.channelRMGetConfigValueResolve=e.channelBase+"9",e.channelRMGetConfigValueReject=e.channelBase+"10",e.channelMRTrayMenuClick=e.channelBase+"11",e.channelMRNewTaskMagnetTaskCreated=e.channelBase+"12",e.channelMRNewTaskDownloadTorrentResult=e.channelBase+"13",e.channelMRNewTaskCreateNewTaskResult=e.channelBase+"14",e.channelMRNewTaskCreateBtTaskResult=e.channelBase+"15",e.channelMRGetConfigValue=e.channelBase+"16",e.channelMRSetConfigValue=e.channelBase+"17",e.channelRMCommitPlanTask=e.channelBase+"18",e.channelRMPerformePlanTask=e.channelBase+"19",e.channelRMProcessSend=e.channelBase+"20",e.channelRMGetPrivateSpaceInfo=e.channelBase+"21",e.channelMRGetPrivateSpaceInfoResult=e.channelBase+"22",e.channelRMFileCopy=e.channelBase+"23",e.channelRMFileMove=e.channelBase+"24",e.channelMRFileCopyResult=e.channelBase+"25",e.channelMRFileMoveResult=e.channelBase+"26",e.channelRMGetSutitleByCid=e.channelBase+"27",e.channelMRGetSutitleByCidResult=e.channelBase+"28",e.channelRMGetSutitleByName=e.channelBase+"29",e.channelMRGetSutitleByNameResult=e.channelBase+"30",e.channelRMDownloadSutitle=e.channelBase+"31",e.channelMRDownloadSutitleSuc=e.channelBase+"32",e.channelMRDownloadSutitleFail=e.channelBase+"33",e.channelRMGetDisplayName=e.channelBase+"34",e.channelMRGetDisplayNameResult=e.channelBase+"35",e.channelMRBringWindowToTop=e.channelBase+"36",e.channelRMDownloadXmp=e.channelBase+"37",e.channelMRFixXmpSuc=e.channelBase+"38",e.channelMRFixXMPFail=e.channelBase+"39",e.channelMRAPlayerCreated=e.channelBase+"41",e.channelMRMainRendererNativeCallInited=e.channelBase+"42",e.channelMRShowWindow=e.channelBase+"43",e.channelMRCreateTopView=e.channelBase+"44",e.channelMRSendTopView=e.channelBase+"44.1",e.channelMRDestroyTopView=e.channelBase+"44.5",e.channelMRCreateTopViewReadyFinish=e.channelBase+"45",e.channelMRCreateTopViewFinish=e.channelBase+"45.5",e.channelMRCreateCtrlView=e.channelBase+"46",e.channelMRSendCtrlView=e.channelBase+"46.1",e.channelMRDestroyCtrlView=e.channelBase+"46.5",e.channelMRCreateCtrlViewReadyFinish=e.channelBase+"47",e.channelMRCreateCtrlViewFinish=e.channelBase+"47.5",e.channelMRCreateSideBar=e.channelBase+"48",e.channelMRCreateSideBarFinish=e.channelBase+"49",e.channelMRExpandListWindow=e.channelBase+"52",e.channelMRExpandListVue=e.channelBase+"53",e.channelMRSetChangeViewSize=e.channelBase+"54",e.channelMRSyncShowMode=e.channelBase+"55",e.channelMRAPWindowUpdate=e.channelBase+"56",e.channelMRUpdateAPlayerPos=e.channelBase+"57",e.channelMRXmpVideoTipCreated=e.channelBase+"58",e.channelMRXmpVideoTipDestroy=e.channelBase+"59",e.channelMRShowSettingCenterWindow=e.channelBase+"60",e.channelRMSetPosition=e.channelBase+"61",e.channelRMShowPlaySettingWindow=e.channelBase+"62",e.channelRMShowCursor=e.channelBase+"63",e.channelRMAPlayerState=e.channelBase+"64",e.channelRMAPlayerProgress=e.channelBase+"65",e.channelRMPlayAction=e.channelBase+"66",e.channelRMSetFoucs=e.channelBase+"67",e.channelMRSetZorder=e.channelBase+"68",e.channelRMGetBrowserStartType=e.channelBase+"69",e.channelMRGetBrowserStartTypeResult=e.channelBase+"70",e.channelMRWindowPopUp=e.channelBase+"71",e.channelMRUpdateTipWindowZorder=e.channelBase+"72",e.channelMRSetShadowWindowResize=e.channelBase+"73",e.channelMRBrowserWindowChange=e.channelBase+"74",e.channelMRChangeFullScreen=e.channelBase+"75",e.channelMRTabNumberChange=e.channelBase+"76",e.channelMRThumbTaskBarAction=e.channelBase+"77",e.channelMRThumbTaskBarButtonStatus=e.channelBase+"78",e.channelRMOpenFolder=e.channelBase+"79",e.channelRReCreateCtrlWindow=e.channelBase+"80",e.channelRReCreateTopWindow=e.channelBase+"81",e.channelRMSetEnvironmentVariable=e.channelBase+"82",e.channelRMServerStarted=e.channelBase+"83",e.channelRMOpenDevTools=e.channelBase+"84",e.channelEnterEditMode=e.channelBase+"85",e.channelUpdateEditRect=e.channelBase+"86",e.channelMessageBoxClose=e.channelBase+"87",e.channelPreventSleep=e.channelBase+"88",e.channelCancelPreventSleep=e.channelBase+"89",e.channelCloseEffectWindow=e.channelBase+"90",e.channelPopUpMenu=e.channelBase+"91",e.channelHideTray=e.channelBase+"92",e.channelEmbedMoveResize=e.channelBase+"94",e.channelTimeTipPos=e.channelBase+"95",e.channelUpdateVideoTip=e.channelBase+"96",e.channelGetBrowserView=e.channelBase+"97",e.channelGetBrowserViewResult=e.channelBase+"98",e.channelBrowserViewLoad=e.channelBase+"99",e.channelBrowserViewOpenDev=e.channelBase+"100",e.channelApplyBrowserView=e.channelBase+"101",e.channelCreateBrowserView=e.channelBase+"102",e.channelCreateBrowserViewResult=e.channelBase+"103",e.channelBrowserViewCall=e.channelBase+"104",e.channelBrowserViewCallRet=e.channelBase+"105",e.channelEmbedWindowRgn=e.channelBase+"106",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},61:function(e,t){e.exports=require("net")},62:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{s(r.next(e))}catch(e){o(e)}}function l(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderUtil=void 0;const i=n(8),o=n(5),a=n(0),l=n(41),s=n(26),c=n(1),u=n(18),d=a.default.getLogger("Thunder.Util"),f="Thunder Network\\Xmp6\\";function p(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(){let e=s.ThunderHelper.getSystemTempPath(),t=s.ThunderHelper.getLogicalDriveStrings(),n=0;for(let r=0;r<t.length;r++){let i=s.ThunderHelper.getDriveInfo(t[r]);3===i.type&&n<i.freeBytes&&t[r]!==e&&(n=i.freeBytes,e=t[r])}return e.substring(0,1)+":\\迅雷下载"}function a(e){let t="00";return t=e>=10?e.toString():"0"+e.toString()}function h(e){let t=(e.style.webkitTransform||getComputedStyle(e,"").getPropertyValue("-webkit-transform")||e.style.transform||getComputedStyle(e,"").getPropertyValue("transform")).match(/\-?[0-9]+\.?[0-9]*/g);return{x:parseInt(t&&(t[12]||t[4])||"0",10),y:parseInt(t&&(t[13]||t[5])||"0",10)}}function g(e){let t=null;do{if(void 0===e||null===e)break;t=e.match(/[\/]?([^?]*)\?([^\s]*)/)?RegExp.$2:""}while(0);return t}function _(e){let t={};do{if(void 0===e||null===e)break;let n=/([^&=?]+)=([^&]*)/g;for(;n.exec(e);)t[RegExp.$1]=RegExp.$2}while(0);return t}e.formatSize=function(e,t){t=t||2;let n="0B";if("number"==typeof e&&e>0){let r=["B","KB","MB","GB","TB"],i=0,o=e;for(;o>=1e3&&!(i>=4);)o/=1024,i+=1;n=-1===String(o).indexOf(".")?o+r[i]:o.toFixed(t)+r[i]}return n},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.getConfigValueAW=function(e,t,n){return r(this,void 0,void 0,function*(){return c.client.callServerFunction(u.ConfigFunction.getConfigValue,e,t,n)})},e.getDefaultDownloadDir=t,e.getMaxFreeDriver=function(){return t().substring(0,1)},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){d.warning(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",r=!1,i=[],o=e.length;for(let a=0;a<o;a++){let l=e[a];if('"'!==l&&"'"!==l||(""===n?(r=!0,n=l):n===l&&(r=!1,n=""))," "!==l||r||a===o-1){if(a===o-1){let n=e.substring(t);""!==n.trim()&&i.push(p(n))}}else{let n=e.substring(t,a);""!==n.trim()&&i.push(p(n)),t=a+1}}return i},e.getXmpTempPath=function(e,t){return r(this,void 0,void 0,function*(){const r=yield Promise.resolve().then(()=>n(34));let i=o.join(r.tmpdir(),f);return t&&(i=o.join(i,t)),void 0!==e&&e&&(yield l.FileSystemAWNS.mkdirsAW(i)),i})},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,r)=>{e+=0===r?"?":"&",e+=`${n}=${encodeURIComponent(t[n])}`}),e},e.getQueryString=function(e,t){let n="";if(e&&t){n=e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:"";try{n=decodeURIComponent(n)}catch(e){}}return n},e.isClipboardTextFormatAvailable=function(){let e=!1,t=i.clipboard.availableFormats();for(let n of t)if("text/plain"===n){e=!0;break}return e},e.resizeToFitContent=function(){let e=document.querySelector(".td-dialog");window.resizeTo(e.offsetWidth,e.offsetHeight)},e.keywordsHighLight=function(e,t,n){if(!e)return"";if(!t)return e;if(0===e.length)return e;if(0===t.length)return e;let r=/\\/,i=t.split(" ");if(0===(i=i.filter(e=>e.trim().length>0)).length)return e;for(let t=0;t<i.length;t++)if(i[t].search(r)>0)return e;n=void 0===n||null===n?"#FF0000":n;let o="",a=["\\[","\\^","\\*","\\(","\\)","\\|","\\?","\\$","\\.","\\+"],l="",s="|";for(let e=0;e<i.length;e++){for(let t=0;t<a.length;t++){let n=new RegExp(a[t],"g");i[e]=i[e].replace(n,a[t])}e===i.length-1&&(s=""),l=l.concat(i[e],s)}let c=new RegExp(l,"gi");return o=e.replace(c,e=>'<span style= "color:'+n+'">'+e+"</span>")},e.convertTimeToMinutes=function(e,t,n){return 3600*e+60*t+n},e.convertMinuteToTime=function(e){return[Math.floor(e/3600),Math.floor(e/60%60),Math.floor(e%60)]},e.formatTimeNumber=a,e.createGUID=function(){let e;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t,n)=>(e=16*Math.random()|0,("x"===t?e:3&e|8).toString(16)))},e.formatDate=function(e,t){let n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(let t in n)new RegExp("("+t+")").test(e)&&(e=e.replace(RegExp.$1,1===RegExp.$1.length?n[t]:("00"+n[t]).substr((""+n[t]).length)));return e},e.formatMilliSecondTime2=function(e){let t=((e=Math.floor(e))%1e3).toString();1===t.length?t="00"+t:2===t.length&&(t="0"+t),e=Math.floor(e/1e3);let n=[];for(;e>0;){let t=Math.floor(e/60);if(n.push(e-60*t),e=t,n.length>=3)break}let r="";return r=1===n.length?a(n[0])+"秒":2===n.length?a(n[1])+"分"+a(n[0])+"秒":3===n.length?a(n[2])+"时"+a(n[1])+"分"+a(n[0])+"秒":"0秒"},e.formatMilliSecondTime=function(e,t,n=":"){let r=((e=Math.floor(e))%1e3).toString();1===r.length?r="00"+r:2===r.length&&(r="0"+r),e=Math.floor(e/1e3);let i=[];for(;e>0;){let t=Math.floor(e/60);if(i.push(e-60*t),e=t,i.length>=3)break}let o="";return o=1===i.length?`00${n}00${n}`+a(i[0]):2===i.length?`00${n}`+a(i[1])+`${n}`+a(i[0]):3===i.length?a(i[2])+n+a(i[1])+n+a(i[0]):`00${n}00${n}00`,t&&(o+=n+r),o},e.formatSeconds=function(e){let t="";do{if(e<=0){t="00分00秒";break}let n=Math.floor(e/3600),r=Math.floor(e/60)%60,i=Math.floor(e%60);n>0&&(t=n<10?"0"+n+"时":n+"时"),t=(t+=r<10?"0"+r+"分":r+"分")+(i<10?"0"+i:""+i)+"秒"}while(0);return t},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setStyle=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style[t]=n})},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),r=t.split("."),i=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(r[e])>0){i=1;break}if(Number(n[e])-Number(r[e])<0){i=-1;break}}return i},e.throttle=function(e,t){let n,r=0;return(...i)=>{const o=Date.now();clearTimeout(n),o-r>t?(e(...i),r=o):n=setTimeout(()=>{e(...i),r=o},t)}},e.getElementFixed=function(e){let t=e.offsetLeft,n=e.offsetTop,r=e.offsetParent;for(;null!==r;){let e=h(r);t+=r.offsetLeft+e.x,n+=r.offsetTop+e.y,r=r.offsetParent}return{x:t,y:n}},e.getElementRelative=function(e){let t=e.offsetLeft,n=e.offsetTop,r=e.offsetParent;for(;null!==r;)t+=r.offsetLeft,n+=r.offsetTop,r=r.offsetParent;return{x:t,y:n}},e.getCmdParam=function(e,t,n){if(!e)return{};let r,i={};for(let o=0;o<e.length;o++)if(e[o].includes(t)){i.key=t,i.index=o,n&&(r=e[o].split(n))&&(i.val=r[1]);break}return i},e.parseDynamicUrlPath=g,e.parseDynamicUrlArgs=_,e.getUrlArgs=function(e){return _(g(e))},e.debounce=function(e,t){let n=null;return(...r)=>{n&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}},e.implode=function(e,t){let n="";return e.forEach(e=>{""===n?n=e:n+=t+e}),n}}(t.ThunderUtil||(t.ThunderUtil={}))},63:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DisplayCardInfo=t.DetailIndex=t.PanelID=t.XmpPlaySoure=t.WindowParams=t.CombineRgnType=t.ZOrderChangedType=t.WindowTopMode=t.XmpMode=t.ShowWindowCmd=t.VirtualKeyCode=t.HitTestPositionCode=t.GWCmd=t.WindowMessage=t.SetWindowType=t.WindowStyleEx=t.WindowStyle=t.CmdShow=t.Uflag=t.OptionOfHWNDInAfter=void 0,function(e){e[e.HWND_NOTOPMOST=-2]="HWND_NOTOPMOST",e[e.HWND_TOPMOST=-1]="HWND_TOPMOST",e[e.HWND_TOP=0]="HWND_TOP",e[e.HWND_BOTTOM=1]="HWND_BOTTOM"}(t.OptionOfHWNDInAfter||(t.OptionOfHWNDInAfter={})),function(e){e[e.SWP_ASYNCWINDOWPOS=16384]="SWP_ASYNCWINDOWPOS",e[e.SWP_DEFERERASE=8192]="SWP_DEFERERASE",e[e.SWP_DRAWFRAME=32]="SWP_DRAWFRAME",e[e.SWP_FRAMECHANGED=32]="SWP_FRAMECHANGED",e[e.SWP_HIDEWINDOW=128]="SWP_HIDEWINDOW",e[e.SWP_NOACTIVATE=16]="SWP_NOACTIVATE",e[e.SWP_NOCOPYBITS=256]="SWP_NOCOPYBITS",e[e.SWP_NOMOVE=2]="SWP_NOMOVE",e[e.SWP_NOOWNERZORDER=512]="SWP_NOOWNERZORDER",e[e.SWP_NOREDRAW=8]="SWP_NOREDRAW",e[e.SWP_NOREPOSITION=512]="SWP_NOREPOSITION",e[e.SWP_NOSENDCHANGING=1024]="SWP_NOSENDCHANGING",e[e.SWP_NOSIZE=1]="SWP_NOSIZE",e[e.SWP_NOZORDER=4]="SWP_NOZORDER",e[e.SWP_SHOWWINDOW=64]="SWP_SHOWWINDOW"}(t.Uflag||(t.Uflag={})),function(e){e[e.SW_FORCEMINIMIZE=11]="SW_FORCEMINIMIZE",e[e.SW_HIDE=0]="SW_HIDE",e[e.SW_MAXIMIZE=3]="SW_MAXIMIZE",e[e.SW_MINIMIZE=6]="SW_MINIMIZE",e[e.SW_RESTORE=9]="SW_RESTORE",e[e.SW_SHOW=5]="SW_SHOW",e[e.SW_SHOWDEFAULT=10]="SW_SHOWDEFAULT",e[e.SW_SHOWMAXIMIZED=3]="SW_SHOWMAXIMIZED",e[e.SW_SHOWMINIMIZED=2]="SW_SHOWMINIMIZED",e[e.SW_SHOWMINNOACTIVE=7]="SW_SHOWMINNOACTIVE",e[e.SW_SHOWNA=8]="SW_SHOWNA",e[e.SW_SHOWNOACTIVATE=4]="SW_SHOWNOACTIVATE",e[e.SW_SHOWNORMAL=1]="SW_SHOWNORMAL"}(t.CmdShow||(t.CmdShow={})),function(e){e[e.WS_BORDER=8388608]="WS_BORDER",e[e.WS_CAPTION=12582912]="WS_CAPTION",e[e.WS_CHILD=1073741824]="WS_CHILD",e[e.WS_CHILDWINDOW=1073741824]="WS_CHILDWINDOW",e[e.WS_CLIPCHILDREN=33554432]="WS_CLIPCHILDREN",e[e.WS_CLIPSIBLINGS=67108864]="WS_CLIPSIBLINGS",e[e.WS_POPUP=2147483648]="WS_POPUP",e[e.WS_THICKFRAME=262144]="WS_THICKFRAME"}(t.WindowStyle||(t.WindowStyle={})),function(e){e[e.WS_EX_TOOLWINDOW=128]="WS_EX_TOOLWINDOW",e[e.WS_EX_APPWINDOW=262144]="WS_EX_APPWINDOW",e[e.WS_EX_TOPMOST=8]="WS_EX_TOPMOST",e[e.WS_EX_WINDOWEDGE=256]="WS_EX_WINDOWEDGE",e[e.WS_EX_NOACTIVATE=134217728]="WS_EX_NOACTIVATE"}(t.WindowStyleEx||(t.WindowStyleEx={})),function(e){e[e.GWL_EXSTYLE=-20]="GWL_EXSTYLE",e[e.GWL_HINSTANCE=-6]="GWL_HINSTANCE",e[e.GWL_ID=-12]="GWL_ID",e[e.GWL_STYLE=-16]="GWL_STYLE",e[e.GWL_USERDATA=-21]="GWL_USERDATA",e[e.GWL_WNDPROC=-4]="GWL_WNDPROC"}(t.SetWindowType||(t.SetWindowType={})),function(e){e[e.WM_CREATE=1]="WM_CREATE",e[e.WM_DESTROY=2]="WM_DESTROY",e[e.WM_MOVE=3]="WM_MOVE",e[e.WM_SIZE=5]="WM_SIZE",e[e.WM_ACTIVATE=6]="WM_ACTIVATE",e[e.WM_SETFOCUS=7]="WM_SETFOCUS",e[e.WM_KILLFOCUS=8]="WM_KILLFOCUS",e[e.WM_ENABLE=10]="WM_ENABLE",e[e.WM_KEYDOWN=256]="WM_KEYDOWN",e[e.WM_KEYUP=257]="WM_KEYUP",e[e.WM_SYSKEYDOWN=260]="WM_SYSKEYDOWN",e[e.WM_MOUSEMOVE=512]="WM_MOUSEMOVE",e[e.WM_SETCURSOR=32]="WM_SETCURSOR",e[e.WM_LBUTTONDOWN=513]="WM_LBUTTONDOWN",e[e.WM_LBUTTONUP=514]="WM_LBUTTONUP",e[e.WM_LBUTTONDBLCLK=515]="WM_LBUTTONDBLCLK",e[e.WM_RBUTTONUP=517]="WM_RBUTTONUP",e[e.WM_MOUSEWHEEL=522]="WM_MOUSEWHEEL",e[e.WM_MOUSELEAVE=675]="WM_MOUSELEAVE",e[e.WM_DPICHANGED=736]="WM_DPICHANGED",e[e.WM_GETMINMAXINFO=36]="WM_GETMINMAXINFO",e[e.WM_NCHITTEST=132]="WM_NCHITTEST",e[e.WM_NCMOUSEMOVE=160]="WM_NCMOUSEMOVE",e[e.WM_NCLBUTTONDOWN=161]="WM_NCLBUTTONDOWN",e[e.WM_NCLBUTTONUP=162]="WM_NCLBUTTONUP",e[e.WM_NCLBUTTONDBLCLK=163]="WM_NCLBUTTONDBLCLK",e[e.WM_NCRBUTTONDOWN=164]="WM_NCRBUTTONDOWN",e[e.WM_NCRBUTTONUP=165]="WM_NCRBUTTONUP",e[e.WM_NCRBUTTONDBLCLK=166]="WM_NCRBUTTONDBLCLK",e[e.WM_NCMBUTTONDOWN=167]="WM_NCMBUTTONDOWN",e[e.WM_NCMBUTTONUP=168]="WM_NCMBUTTONUP",e[e.WM_NCMBUTTONDBLCLK=169]="WM_NCMBUTTONDBLCLK",e[e.WM_WINDOWPOSCHANGED=71]="WM_WINDOWPOSCHANGED",e[e.WM_WINDOWPOSCHANGING=70]="WM_WINDOWPOSCHANGING",e[e.WM_ACTIVATEAPP=28]="WM_ACTIVATEAPP",e[e.WM_DWMCOMPOSITIONCHANGED=798]="WM_DWMCOMPOSITIONCHANGED"}(t.WindowMessage||(t.WindowMessage={})),function(e){e[e.GW_HWNDFIRST=0]="GW_HWNDFIRST",e[e.GW_HWNDLAST=1]="GW_HWNDLAST",e[e.GW_HWNDNEXT=2]="GW_HWNDNEXT",e[e.GW_HWNDPREV=3]="GW_HWNDPREV",e[e.GW_OWNER=4]="GW_OWNER",e[e.GW_CHILD=5]="GW_CHILD",e[e.GW_ENABLEDPOPUP=6]="GW_ENABLEDPOPUP"}(t.GWCmd||(t.GWCmd={})),function(e){e[e.HTERROR=-2]="HTERROR",e[e.HTTRANSPARENT=-1]="HTTRANSPARENT",e[e.HTNOWHERE=0]="HTNOWHERE",e[e.HTCLIENT=1]="HTCLIENT",e[e.HTCAPTION=2]="HTCAPTION",e[e.HTSYSMENU=3]="HTSYSMENU",e[e.HTGROWBOX=4]="HTGROWBOX",e[e.HTSIZE=4]="HTSIZE",e[e.HTMENU=5]="HTMENU",e[e.HTHSCROLL=6]="HTHSCROLL",e[e.HTVSCROLL=7]="HTVSCROLL",e[e.HTMINBUTTON=8]="HTMINBUTTON",e[e.HTMAXBUTTON=9]="HTMAXBUTTON",e[e.HTLEFT=10]="HTLEFT",e[e.HTRIGHT=11]="HTRIGHT",e[e.HTTOP=12]="HTTOP",e[e.HTTOPLEFT=13]="HTTOPLEFT",e[e.HTTOPRIGHT=14]="HTTOPRIGHT",e[e.HTBOTTOM=15]="HTBOTTOM",e[e.HTBOTTOMLEFT=16]="HTBOTTOMLEFT",e[e.HTBOTTOMRIGHT=17]="HTBOTTOMRIGHT",e[e.HTBORDER=18]="HTBORDER",e[e.HTREDUCE=8]="HTREDUCE",e[e.HTZOOM=9]="HTZOOM",e[e.HTSIZEFIRST=10]="HTSIZEFIRST",e[e.HTSIZELAST=17]="HTSIZELAST",e[e.HTOBJECT=19]="HTOBJECT",e[e.HTCLOSE=20]="HTCLOSE",e[e.HTHELP=21]="HTHELP"}(t.HitTestPositionCode||(t.HitTestPositionCode={})),function(e){e[e.VK_LBUTTON=1]="VK_LBUTTON",e[e.VK_RBUTTON=2]="VK_RBUTTON",e[e.VK_MBUTTON=4]="VK_MBUTTON",e[e.VK_BACK=8]="VK_BACK",e[e.VK_TAB=9]="VK_TAB",e[e.VK_RETURN=13]="VK_RETURN",e[e.VK_SHIFT=16]="VK_SHIFT",e[e.VK_CONTROL=17]="VK_CONTROL",e[e.VK_MENU=18]="VK_MENU",e[e.VK_ESCAPE=27]="VK_ESCAPE",e[e.VK_SPACE=32]="VK_SPACE",e[e.VK_PRIOR=33]="VK_PRIOR",e[e.VK_NEXT=34]="VK_NEXT",e[e.VK_END=35]="VK_END",e[e.VK_LEFT=37]="VK_LEFT",e[e.VK_UP=38]="VK_UP",e[e.VK_RIGHT=39]="VK_RIGHT",e[e.VK_DOWN=40]="VK_DOWN",e[e.VK_DELETE=46]="VK_DELETE",e[e.VK_F1=112]="VK_F1",e[e.VK_F2=113]="VK_F2",e[e.VK_F3=114]="VK_F3",e[e.VK_F4=115]="VK_F4",e[e.VK_F5=116]="VK_F5",e[e.VK_F6=117]="VK_F6",e[e.VK_F7=118]="VK_F7",e[e.VK_F8=119]="VK_F8",e[e.VK_F9=120]="VK_F9",e[e.VK_F10=121]="VK_F10",e[e.VK_F11=122]="VK_F11",e[e.VK_F12=123]="VK_F12",e[e.VK_OEM_1=186]="VK_OEM_1",e[e.VK_OEM_PLUS=187]="VK_OEM_PLUS",e[e.VK_OEM_COMMA=188]="VK_OEM_COMMA",e[e.VK_OEM_MINUS=189]="VK_OEM_MINUS",e[e.VK_OEM_PERIOD=190]="VK_OEM_PERIOD",e[e.VK_OEM_2=191]="VK_OEM_2",e[e.VK_OEM_3=192]="VK_OEM_3",e[e.VK_OEM_4=219]="VK_OEM_4",e[e.VK_OEM_5=220]="VK_OEM_5",e[e.VK_OEM_6=221]="VK_OEM_6",e[e.VK_OEM_7=222]="VK_OEM_7",e[e.VK_PROCESSKEY=229]="VK_PROCESSKEY"}(t.VirtualKeyCode||(t.VirtualKeyCode={})),function(e){e[e.SW_HIDE=0]="SW_HIDE",e[e.SW_SHOWMAXIMIZED=3]="SW_SHOWMAXIMIZED",e[e.SW_SHOW=5]="SW_SHOW"}(t.ShowWindowCmd||(t.ShowWindowCmd={})),function(e){e[e.UNKNOWN_MODE=-1]="UNKNOWN_MODE",e[e.INDEPENDENT_MODE=0]="INDEPENDENT_MODE",e[e.MAGNETIC_MODE=1]="MAGNETIC_MODE",e[e.EMBED_MODE=2]="EMBED_MODE"}(t.XmpMode||(t.XmpMode={})),function(e){e[e.NO_TOPMOST_MODE=0]="NO_TOPMOST_MODE",e[e.PLAYING_TOPMOST_MODE=1]="PLAYING_TOPMOST_MODE",e[e.ALWAYLS_TOPMOST_MODE=2]="ALWAYLS_TOPMOST_MODE"}(t.WindowTopMode||(t.WindowTopMode={})),function(e){e[e.ZORDER_CHANGED_TYPE_UNKNOW=-1]="ZORDER_CHANGED_TYPE_UNKNOW",e[e.ZORDER_CHANGED_TYPE_BEFORE=0]="ZORDER_CHANGED_TYPE_BEFORE",e[e.ZORDER_CHANGED_TYPE_AFTER=1]="ZORDER_CHANGED_TYPE_AFTER"}(t.ZOrderChangedType||(t.ZOrderChangedType={})),function(e){e[e.RGN_AND=1]="RGN_AND",e[e.RGN_OR=2]="RGN_OR",e[e.RGN_XOR=3]="RGN_XOR",e[e.RGN_DIFF=4]="RGN_DIFF",e[e.RGN_COPY=5]="RGN_COPY"}(t.CombineRgnType||(t.CombineRgnType={})),function(e){e[e.DEFAULT_WIDTH=850]="DEFAULT_WIDTH",e[e.DEFAULT_HEIGHT=550]="DEFAULT_HEIGHT",e[e.MIN_WINDOW_WIDTH=350]="MIN_WINDOW_WIDTH",e[e.MIN_WINDOW_HEIGHT=200]="MIN_WINDOW_HEIGHT",e[e.TOPCTRL_HEIGHT=42]="TOPCTRL_HEIGHT",e[e.PLAYCTRL_HEIGHT=62]="PLAYCTRL_HEIGHT"}(t.WindowParams||(t.WindowParams={})),function(e){e[e.PLAY_UNKNOWN=0]="PLAY_UNKNOWN",e[e.PLAY_BY_DOWNLOAD=1]="PLAY_BY_DOWNLOAD",e[e.PLAY_BY_PAN=2]="PLAY_BY_PAN"}(t.XmpPlaySoure||(t.XmpPlaySoure={})),function(e){e.Download="download-panel",e.Cloud="pan-plugin-view",e.Browser="find-page",e.Message="message-page"}(t.PanelID||(t.PanelID={})),function(e){e.Preview="Preview",e.Speed="TaskChart",e.Attribute="Attribute"}(t.DetailIndex||(t.DetailIndex={}));t.DisplayCardInfo=class{}},8:function(e,t){e.exports=require("electron")},83:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(97),i=n(11);t.Parser=class extends i.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),i=JSON.parse(e);this.emit("message",i),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},84:function(e,t){e.exports=require("buffer")},95:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(0),i=n(24),o=n(5),a=n(25).default(o.join(__rootDir,"../bin/ThunderHelper.node")),l=n(60),s=n(8);function c(){"console"===process.env.TL_OUTPUT?r.default.outputLogger=r.outputLoggerConsole:r.default.outputLogger=function(){function e(e){return function(...t){a.printEtwLog(e,function(...e){return e.map(e=>i.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[r.LogLevel.Critical]:e(r.LogLevel.Critical),[r.LogLevel.Error]:e(r.LogLevel.Error),[r.LogLevel.Warning]:e(r.LogLevel.Warning),[r.LogLevel.Information]:e(r.LogLevel.Information),[r.LogLevel.Verbose]:e(r.LogLevel.Verbose)}}()}function u(){r.default.enableLogger="1"===process.env.TL_ENABLE,c()}c(),"browser"===process.type?s.ipcMain.on(l.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{u()}):"renderer"===process.type&&s.ipcRenderer.on(l.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{u()})},96:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(61),i=n(11),o=n(39),a=n(83),l=n(20);t.Client=class extends i.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=l.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new a.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{o.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){o.error(e.message)}else o.information("This socket has been ended by the other party",this.context&&this.context.name)}}},97:function(e,t){e.exports=require("string_decoder")},98:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const r=n(8),i=n(84),o=n(99),a=n(100),l=n(54),s=n(101),c=n(17),u=n(55),d=r.ipcRenderer,f=process.electronBinding("v8_util"),p=new o.default,h=f.createIDWeakMap(),g=f.getHiddenValue(global,"contextId");class _{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function m(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let r=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:m(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(r))return{type:"buffer",value:i.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(u.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(f.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:f.getHiddenValue(e,"__remote_id__")};let r={type:e instanceof _?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)r.members.push({name:t,value:n(e[t])});return t.delete(e),r}if("function"==typeof e){return{type:"function",id:p.add(e),location:f.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function S(e,t,n){u.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function w(e,t,n,r=!1){const i=t=>{if(e.hasOwnProperty(t.name)&&!r)return;let n,i={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(h.has(t.value.refId)&&(n=h.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=y(t.value,e,t.name);i.get=(()=>n),i.set=(e=>n=e)}else"get"===t.type&&(i.get=(()=>n),t.writable&&(i.set=(e=>{n=e})),n=y(t.value));Object.defineProperty(e,t.name,i)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)i(n[t])}}function b(e,t,n){if(n){let t=y(n);Object.setPrototypeOf(e,t)}}function C(e,t){f.setHiddenValue(e,"__remote_id__",t)}function E(e){return f.getHiddenValue(e,"__remote_id__")}function y(e,t,n){const r={value:()=>e.value,array:()=>e.members.map(e=>y(e)),buffer:()=>i.Buffer.from(e.value),promise:()=>Promise.resolve({then:y(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:r,value:i}=e.members[n];t[r]=i}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let o;return e.type in r?o=r[e.type]():e.refId?h.has(e.refId)?(f.addRemoteObjectRef(g,e.refId),o=h.get(e.refId)):(c.warn("[metaToValue] refId point to null"+e.refId),o="function"===e.type?()=>{}:{}):e.id?h.has(e.id)?(f.addRemoteObjectRef(g,e.id),w(o=h.get(e.id),e.id,e.members,!0),b(o,e.id,e.proto)):(o="function"===e.type?t?function(e,t,n){if(h.has(n.id))return h.get(n.id);let r=E(e),i=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(i,"name",{value:t,writable:!1,enumerable:!0});let o=new Proxy(i,{apply:(e,n,i)=>new Promise((e,o)=>{let c=E(n);if(c||(c=E(n.__remoteObj_)),!c)throw Error("is this function was a bound function?");let u=l.default.browser.memberCall,f=s.default(u),p=m(i);d.send(u,g,f,c,r,t,p),a.default.add(f,t=>{try{S(y(t),e,o)}catch(e){o(e)}})}),construct:(e,n,i)=>new Promise((e,i)=>{let o=l.default.browser.memberConstruct,c=s.default(o);d.send(o,g,c,r,t,m(n)),a.default.add(c,t=>{try{let n=y(t);e(n)}catch(e){i(e)}})})});return f.setHiddenValue(i,"__remote_id__",n.id),o}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return C(n,t),new Proxy(n,{apply:(e,n,r)=>new Promise((e,i)=>{let o=l.default.browser.functionCall,c=s.default(o),u=E(n);d.send(o,g,c,u,t,m(r)),a.default.add(c,t=>{try{S(y(t),e,i)}catch(e){i(e)}})}),construct:(e,n,r)=>new Promise((e,r)=>{let i=l.default.browser.construct,o=s.default(i);d.send(i,g,o,t,m(n)),a.default.add(o,t=>{try{let n=y(t);e(n)}catch(e){r(e)}})})})}(e):{},f.setRemoteObjectFreer(o,g,e.id),h.set(e.id,o),f.setHiddenValue(o,"__remote_id__",e.id),f.addRemoteObjectRef(g,e.id),function(e){let t=E(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,r){if("function"==typeof r)throw new Error("set a function to a remote member is dangerous");return new Promise((i,o)=>{let c=l.default.browser.memberSet,u=s.default(c),f=m([r]);d.send(c,g,u,t,n,f),a.default.add(u,t=>{try{let a=y(t);e[n]=r,i(a)}catch(e){o(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((r,i)=>{let o=l.default.browser.memberGet,c=s.default(o);d.send(o,g,c,t,n),a.default.add(c,t=>{try{const o=y(t);e[n]=o,r(o)}catch(e){i(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let r=l.default.browser.sync,i=s.default(r);d.send(r,g,i,t),a.default.add(i,r=>{try{if(r.id!==t)throw Error("SYNC_RETURN: remote id not match");let i=y(r);e(i)}catch(e){n(e)}})})}}})}(o),w(o,e.id,e.members),b(o,e.id,e.proto),Object.defineProperty(o.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=o[e],r=o.__proto__;for(;r;){if(Object.prototype.hasOwnProperty.call(r,e)){delete r[e];break}r=r.__proto__}Object.defineProperty(o,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):c.error("no id of meta:",e),o}t.Interest=_;class T{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,r)=>{let i=this.getChannel(e),o=s.default(i);d.send(i,g,o,t),a.default.add(o,e=>{try{let t=y(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){r(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=l.default.browser.require;break;case"builtin":t=l.default.browser.builtIn;break;case"global":t=l.default.browser.global;break;case"current_window":t=l.default.browser.currentWindow;break;case"current_web_contents":t=l.default.browser.currentWebContents;break;case"client_web_contents":t=l.default.browser.clientWebContents;break;case"web_contents":t=l.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function O(e,t,n){try{a.default.invoke(t,n).remove(t)}finally{a.default.remove(t)}}d.on(l.default.renderer.requireReturn,O),d.on(l.default.renderer.getBuiltInReturn,O),d.on(l.default.renderer.getGlobalReturn,O),d.on(l.default.renderer.currentWindowReturn,O),d.on(l.default.renderer.currentWebContentsReturn,O),d.on(l.default.renderer.functionCallReturn,O),d.on(l.default.renderer.constructReturn,O),d.on(l.default.renderer.memberCallReturn,O),d.on(l.default.renderer.memberSetReturn,O),d.on(l.default.renderer.memberGetReturn,O),d.on(l.default.renderer.memberConstructReturn,O),d.on(l.default.renderer.callback,(e,t,n)=>{p.apply(t,y(n))}),d.on(l.default.renderer.syncReturn,O),d.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{c.info("[RELEASE_CALLBACK]: callbackId:",t),p.remove(t)}),process.on("exit",()=>{d.send(l.default.browser.contextRelease)});const W=["__resolve","__isResolved"],R=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],P=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function v(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(P(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(P(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(P(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(P(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(P(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(P(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,r)=>(P(e),Reflect.defineProperty(e.__remoteObj_,n,r)),get:(t,n,r)=>{if("string"==typeof n){if(String.prototype.includes.call(R,n)){return e[n]}if(String.prototype.includes.call(W,n)){return e[n]}}return P(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,r,i)=>(P(e),Reflect.set(e.__remoteObj_,n,r,i)),ownKeys:t=>(P(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,r)=>{P(e),Reflect.apply(e.__remoteObj_,n,r)},construct:(t,n,r)=>{if(P(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,r)=>{let i=l.default.browser.construct,o=s.default(i),c=f.getHiddenValue(e.__remoteObj_,"__remote_id__");d.send(i,g,o,c,m(n)),a.default.add(o,e=>{try{t(y(e))}catch(e){r(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=E(e);t&&C(n,t)}}),n}t.remoteRequire=function(e){return v(new T("module",e))},t.getBuiltin=function(e){return v(new T("builtin",e))},t.getGlobal=function(e){return v(new T("global",e))},t.getCurrentWindow=function(){return v(new T("current_window"))},t.getCurrentWebContents=function(){return v(new T("current_web_contents"))},t.getWebContents=function(){return v(new T("web_contents"))}},99:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=r.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,i=(new Error).stack;let o,a=n.exec(i);for(;null!==a;){const e=a[1];if(!e.includes("native")&&!e.includes("electron.asar")){o=/([^/^)]*)\)?$/gi.exec(e)[1];break}a=n.exec(i)}return this.callbacks[t]=e,r.setHiddenValue(e,"__remote_callback_id__",t),r.setHiddenValue(e,"__remote_call_location__",o),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(r.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}}});