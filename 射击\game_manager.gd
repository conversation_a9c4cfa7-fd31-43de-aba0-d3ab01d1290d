# 游戏管理器脚本
extends Node2D

# 砖块场景引用
var brick_scene = preload("res://brick.tscn")
# 当前得分
var score = 0
# 砖块行数和列数
var rows = 5
var columns = 8
# 砖块间距
var brick_spacing = Vector2(100, 40)

# 初始化
func _ready():
	# 生成砖块
	generate_bricks()
	# 更新分数显示
	update_score_display()

# 生成砖块阵列
func generate_bricks():
	# 获取砖块容器节点
	var bricks_container = $Bricks
	
	# 清除现有砖块
	for child in bricks_container.get_children():
		child.queue_free()
	
	# 生成新的砖块阵列
	for row in range(rows):
		for col in range(columns):
			# 创建砖块实例
			var brick = brick_scene.instantiate()
			# 设置砖块位置
			brick.position = Vector2(col * brick_spacing.x, row * brick_spacing.y)
			# 根据行数设置不同颜色
			var color_value = float(row) / rows
			brick.brick_color = Color(1.0 - color_value, 0.5, color_value + 0.5)
			# 设置砖块生命值为1，确保碰到立刻消失
			brick.health = 1
			# 添加到容器中
			bricks_container.add_child(brick)

# 增加分数
func increase_score():
	score += 10
	update_score_display()
	
	# 检查是否所有砖块都被清除
	if get_tree().get_nodes_in_group("bricks").size() == 0:
		# 所有砖块清除，进入下一关
		next_level()

# 更新分数显示
func update_score_display():
	$Label.text = "得分: " + str(score)

# 进入下一关
func next_level():
	# 增加难度
	rows += 1
	if rows > 8:
		rows = 8
	
	# 重新生成砖块
	generate_bricks()
	
	# 重置球
	$Ball.reset()

# 游戏结束
func game_over():
	# 重置分数
	score = 0
	update_score_display()
	
	# 重置关卡难度
	rows = 5
	
	# 重新生成砖块
	generate_bricks()