/*! For license information please see ../../LICENSES */
(window.webpackJsonp=window.webpackJsonp||[]).push([[17],{1329:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1332);function o(e){return Object(r.createDecorator)((function(t,n){if(!t.methods)throw new Error("This decorator must be used on a vue component method.");var r="number"==typeof e?e:e.time,o=t.methods[n],i=0,a=function(){i&&(clearTimeout(i),i=0)};t.methods[n]=function(){for(var e=this,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];a(),i=setTimeout((function(){i=0,o.apply(e,t)}),r)}}))}},1332:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=(r=n(5))&&"object"==typeof r&&"default"in r?r.default:r,i="undefined"!=typeof Reflect&&Reflect.defineMetadata;function a(e,t,n){(n?Reflect.getOwnMetadataKeys(t,n):Reflect.getOwnMetadataKeys(t)).forEach((function(r){var o=n?Reflect.getOwnMetadata(r,t,n):Reflect.getOwnMetadata(r,t);n?Reflect.defineMetadata(r,o,e,n):Reflect.defineMetadata(r,o,e)}))}var c={__proto__:[]}instanceof Array;var s=["data","beforeCreate","created","beforeMount","mounted","beforeDestroy","destroyed","beforeUpdate","updated","activated","deactivated","render","errorCaptured"];function d(e,t){void 0===t&&(t={}),t.name=t.name||e._componentTag||e.name;var n=e.prototype;Object.getOwnPropertyNames(n).forEach((function(e){if("constructor"!==e)if(s.indexOf(e)>-1)t[e]=n[e];else{var r=Object.getOwnPropertyDescriptor(n,e);void 0!==r.value?"function"==typeof r.value?(t.methods||(t.methods={}))[e]=r.value:(t.mixins||(t.mixins=[])).push({data:function(){var t;return(t={})[e]=r.value,t}}):(r.get||r.set)&&((t.computed||(t.computed={}))[e]={get:r.get,set:r.set})}})),(t.mixins||(t.mixins=[])).push({data:function(){return function(e,t){var n=t.prototype._init;t.prototype._init=function(){var t=this,n=Object.getOwnPropertyNames(e);if(e.$options.props)for(var r in e.$options.props)e.hasOwnProperty(r)||n.push(r);n.forEach((function(n){"_"!==n.charAt(0)&&Object.defineProperty(t,n,{get:function(){return e[n]},set:function(t){e[n]=t},configurable:!0})}))};var r=new t;t.prototype._init=n;var o={};return Object.keys(r).forEach((function(e){void 0!==r[e]&&(o[e]=r[e])})),o}(this,e)}});var r=e.__decorators__;r&&(r.forEach((function(e){return e(t)})),delete e.__decorators__);var d,l,u=Object.getPrototypeOf(e.prototype),f=u instanceof o?u.constructor:o,p=f.extend(t);return function(e,t,n){Object.getOwnPropertyNames(t).forEach((function(r){if("prototype"!==r){var o=Object.getOwnPropertyDescriptor(e,r);if(!o||o.configurable){var i,a,s=Object.getOwnPropertyDescriptor(t,r);if(!c){if("cid"===r)return;var d=Object.getOwnPropertyDescriptor(n,r);if(i=s.value,a=typeof i,null!=i&&("object"===a||"function"===a)&&d&&d.value===s.value)return}0,Object.defineProperty(e,r,s)}}}))}(p,e,f),i&&(a(d=p,l=e),Object.getOwnPropertyNames(l.prototype).forEach((function(e){a(d.prototype,l.prototype,e)})),Object.getOwnPropertyNames(l).forEach((function(e){a(d,l,e)}))),p}function l(e){return"function"==typeof e?d(e):function(t){return d(t,e)}}l.registerHooks=function(e){s.push.apply(s,e)},t.default=l,t.createDecorator=function(e){return function(t,n,r){var o="function"==typeof t?t:t.constructor;o.__decorators__||(o.__decorators__=[]),"number"!=typeof r&&(r=void 0),o.__decorators__.push((function(t){return e(t,n,r)}))}},t.mixins=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o.extend({mixins:e})}},1376:function(e,t,n){var r=n(1447);"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,n(90).default)("28c488d2",r,!0,{sourceMap:!1})},1377:function(e,t,n){var r=n(1449);"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,n(90).default)("47d474a5",r,!0,{sourceMap:!1})},1446:function(e,t,n){"use strict";var r=n(1376);n.n(r).a},1447:function(e,t,n){(t=n(89)(!1)).push([e.i,"body .xly-dialog-path{width:450px;padding:0 18px 18px;--tree-background:var(--color-item-hover)}body .xly-dialog-path h2{padding:13px 0;text-align:left}.xly-dialog-path__content{border-radius:4px;border:1px solid var(--color-border-form)}.xly-dialog-path__content .td-tree{margin-right:2px;height:200px;overflow-y:auto}.xly-dialog-path__content .td-tree::-webkit-scrollbar{width:8px;background:transparent}.xly-dialog-path__content .td-tree::-webkit-scrollbar:horizontal{height:8px}.xly-dialog-path__content .td-tree::-webkit-scrollbar-thumb{border-radius:2px;width:8px;background:var(--color-scrollbar)}.xly-dialog-path__content .td-tree::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}.xly-dialog-path__content .td-tree::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-path .td-tree-node{display:table;width:100%;cursor:default}body .xly-dialog-path .td-tree-node__label{margin-left:6px}body .xly-dialog-path .td-tree-node.is-disabled{color:var(--color-secondary)}body .xly-dialog-path .td-tree-node.is-disabled .td-tree-node__image-icon{opacity:.5}body .xly-dialog-path .td-tree-node__image-icon{margin:0;vertical-align:-3px}body .xly-dialog-path .td-tree-node__content.is-chosen{background:var(--color-item-active)}body .xly-dialog-path .td-tree-node__expand-icon{margin-left:6px}body.is-dark .xly-dialog-path .td-tree-node__content.is-chosen{background:#1a1a1a}",""]),e.exports=t},1448:function(e,t,n){"use strict";var r=n(1377);n.n(r).a},1449:function(e,t,n){(t=n(89)(!1)).push([e.i,".td-tree-node__image-icon{background-size:cover}",""]),e.exports=t},1509:function(e,t,n){"use strict";n.r(t);n(10),n(11),n(32),n(105),n(689),n(41),n(44),n(20),n(12),n(27),n(7);var r=n(33),o=n(40),i=n(70),a=n(71),c=n(37),s=(n(4),n(29)),d=n(18),l=n(8),u=n(5),f=n(1329),p=n(1),h=n(3),v=n(139);function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Object(c.a)(e);if(t){var o=Object(c.a)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Object(a.a)(this,n)}}var b=function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"===("undefined"==typeof Reflect?"undefined":Object(s.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var c=e.length-1;c>=0;c--)(o=e[c])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a},_=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},g=function(e){Object(i.a)(n,e);var t=y(n);function n(){var e;return Object(r.a)(this,n),(e=t.apply(this,arguments)).visible=!1,e.selectVueTreeNode=null,e.visibleCreateOrRenameDialog=!1,e.eventId=0,e}return Object(o.a)(n,[{key:"show",value:function(){this.visible=!0}},{key:"emit",value:function(){this.visible=!1,this.$destroy(),this.resolve(this.selectVueTreeNode)}},{key:"handleCreateDirectory",value:function(){this.callerInstance.$eventTrack("yunpan_move_copy_pop_click",{type:this.operationType,clickid:"new_floder",is_safebox:+this.inCoffer,vip_type:this.vipType,is_year:+this.isYearlySuper});var e=this.$refs.tree;e&&e.chosenNode&&(e.chosenNode.mode="create")}},{key:"mounted",value:function(){var e=this,t=this.$refs.tree,n=t.$children;t.chosenNode=n[0],this.handleChangeChosenNode(t.chosenNode),this.eventId=h.a.attachServerEvent("OnLogout",(function(){e.close()}))}},{key:"beforeDestroy",value:function(){h.a.detachServerEvent("OnLogout",this.eventId)}},{key:"handleChangeChosenNode",value:function(e){this.selectVueTreeNode=e}},{key:"handleConfirmCreateDirectory",value:function(e){var t=e.chosenNode,n=e.content;return _(this,void 0,void 0,regeneratorRuntime.mark((function e(){var r,o,i=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.callerInstance.$store.dispatch("drive/addFolderWithParentId",{name:n,parentId:t.node.id,space:this.inCoffer?"SPACE_SAFE":"",option:{headers:{"space-authorization":this.callerInstance.state.password.safeToken}}});case 3:if(!(r=e.sent).file){e.next=8;break}return e.next=7,h.a.callRemoteClientFunction("ThunderPanPluginWebview","IpcRefreshDirectory",{id:t.node.id});case 7:this.$nextTick((function(){var e=Object.assign(Object.assign({},r.file),{leaf:!0,__order:-t.node.children.length,__level:t.node.__level+1,title:r.file.name,icon:r.file.icon_link});t.node.children.push(e),t.node.children.sort((function(e,t){return e.__order-t.__order})),i.$nextTick((function(){var t=i.findVueInstanceNodeById(e.id);i.handleChangeChosenNode(t),i.$refs.tree.chosenNode=t}))}));case 8:e.next=14;break;case 10:e.prev=10,e.t0=e.catch(0),o=Object(p.safeJSONParse)(e.t0.message),this.$message.error(o.error_description||e.t0.error_description);case 14:case"end":return e.stop()}}),e,this,[[0,10]])})))}},{key:"loadFunction",value:function(e){var t;return _(this,void 0,void 0,regeneratorRuntime.mark((function n(){var r,o;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(""!==e.id){n.next=2;break}return n.abrupt("return");case 2:return n.prev=2,n.next=5,Object(l.n)(e.id);case 5:if(r=n.sent,(null===(t=null==r?void 0:r.files)||void 0===t?void 0:t.length)||(e.leaf=!0),o=r.files){n.next=10;break}return n.abrupt("return");case 10:return o.forEach((function(e,t){e.__order=t,e.title=e.name,e.icon=e.icon_link,e.leaf="drive#file"===e.kind})),n.abrupt("return",o);case 14:n.prev=14,n.t0=n.catch(2),console.log(n.t0);case 17:return n.abrupt("return",[]);case 18:case"end":return n.stop()}}),n,null,[[2,14]])})))}},{key:"bindingKeyGenerator",value:function(e){return e.id}},{key:"close",value:function(){this.visible=!1,this.$destroy(),this.reject()}},{key:"findVueInstanceNodeById",value:function(e){for(var t=[this.$refs.tree];t.length>0;)for(var n=t.shift(),r=0;r<n.$children.length;r++){var o=n.$children[r];if(o.node){if(o.node.id===e)return o;t.push(o)}}}},{key:"vipType",get:function(){return"1"===this.callerInstance.state.users.curUser.isVip&&void 0!==this.callerInstance.state.users.curUser.vasType?this.callerInstance.state.users.curUser.vasType:v.VipType.normal}},{key:"isYearlySuper",get:function(){var e,t,n;return this.vipType===v.VipType.superv&&"1"===(null===(n=null===(t=null===(e=this.callerInstance.state)||void 0===e?void 0:e.users)||void 0===t?void 0:t.curUser)||void 0===n?void 0:n.isYear)}}]),n}(d.Vue);b([Object(d.Prop)({})],g.prototype,"inCoffer",void 0),b([Object(d.Prop)({})],g.prototype,"resolve",void 0),b([Object(d.Prop)({default:"选择云盘保存路径"})],g.prototype,"title",void 0),b([Object(d.Prop)({})],g.prototype,"reject",void 0),b([Object(d.Prop)({})],g.prototype,"data",void 0),b([Object(d.Prop)({})],g.prototype,"callerInstance",void 0),b([Object(d.Prop)({})],g.prototype,"disableFunction",void 0),b([Object(d.Prop)({})],g.prototype,"disableClass",void 0),b([Object(d.Prop)({})],g.prototype,"operationType",void 0),b([Object(f.a)({time:10})],g.prototype,"handleConfirmCreateDirectory",null);var m,x=g=b([Object(d.Component)({})],g),O=(n(280),n(1446),n(1448),n(72)),w=Object(O.a)(x,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-dialog",{attrs:{"custom-class":"xly-dialog-path",visible:e.visible},on:{"update:visible":function(t){e.visible=t},ok:function(t){e.visible=!1},close:e.close}},[n("h2",{staticStyle:{"-webkit-app-region":"drag"},attrs:{slot:"header"},slot:"header"},[e._v("\n    "+e._s(e.title)+"\n  ")]),e._v(" "),n("div",{staticClass:"xly-dialog-path__content"},[n("td-tree-plus",{ref:"tree",attrs:{data:e.data,"level-padding":20,lazy:!0,load:e.loadFunction,"binding-key-generator":e.bindingKeyGenerator,"disable-function":e.disableFunction,"expand-when-double-click":"","disable-class":e.disableClass,"default-placeholder":"新建文件夹"},on:{changeChosenNode:e.handleChangeChosenNode,create:e.handleConfirmCreateDirectory}})],1),e._v(" "),n("div",{staticClass:"xly-dialog__footer",attrs:{slot:"footer"},slot:"footer"},[n("div",{staticClass:"xly-dialog__footer-operate"},[n("td-button",{staticClass:"td-button--other xly-dialog-path__new",on:{click:e.handleCreateDirectory}},[e._v("\n        新建文件夹\n      ")])],1),e._v(" "),n("div",{staticClass:"xly-dialog__button"},[n("td-button",{on:{click:e.emit}},[e._v("\n        确定\n      ")]),e._v(" "),n("td-button",{staticClass:"td-button--other",on:{click:e.close}},[e._v("\n        取消\n      ")])],1)])])}),[],!1,null,null,null).exports,k=u.default.extend(w),j=function(e,t,n){!function(e){m=new k({el:document.createElement("div"),propsData:Object.assign({},e.props)})}(e),m.resolve=t,m.reject=n,document.body.appendChild(m.$el),u.default.nextTick((function(){m.show()}))},P=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Promise((function(t,n){j(e,t,n)}))},R=n(82),C=n.n(R),T=n(52);function N(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Object(c.a)(e);if(t){var o=Object(c.a)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Object(a.a)(this,n)}}var I=function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"===("undefined"==typeof Reflect?"undefined":Object(s.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var c=e.length-1;c>=0;c--)(o=e[c])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a},E=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,c)}s((r=r.apply(e,t||[])).next())}))},D=function(e){Object(i.a)(n,e);var t=N(n);function n(){var e;return Object(r.a)(this,n),(e=t.apply(this,arguments)).data=[],e.disabledNodeIdList=[],e.outPromise=null,e.state={},e}return Object(o.a)(n,[{key:"inSafeSpace",value:function(){return this.state.drive.driveRouteList.length>1&&"SAFE"===this.state.drive.driveRouteList[1].folder_type}},{key:"getPanState",value:function(){return E(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,h.a.callRemoteClientFunction("ThunderPanPluginWebview","IpcGetPanState");case 3:if((t=e.sent)[0]){e.next=8;break}throw new Error("get pan state error");case 8:this.state=t[0];case 9:e.next=15;break;case 11:e.prev=11,e.t0=e.catch(0),console.error(e.t0),this.state={};case 15:case"end":return e.stop()}}),e,this,[[0,11]])})))}},{key:"mounted",value:function(){var e,t,n,r,o;return E(this,void 0,void 0,regeneratorRuntime.mark((function i(){var a,c,s,d,u,f,p,h,v,y,b,_,g=this;return regeneratorRuntime.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return a=null!==(e=window.__extra__)&&void 0!==e?e:{},c=a.autoDetectPath,s=a.operationType,this.disabledNodeIdList=(null==a?void 0:a.disableIdList)||[],d=[],i.next=7,C.a.getCurrentWindow().__resolve();case 7:return u=i.sent,this.outPromise=u.webContents.browserWindowOptions.customProperties,f=this.outPromise.title,p={id:"",name:"我的云盘"},i.next=13,this.getPanState();case 13:if(Object.keys(this.state).length){i.next=15;break}return i.abrupt("return");case 15:return c&&this.inSafeSpace()&&(p=this.state.drive.driveRouteList[1]),i.prev=16,i.next=19,Object(l.n)(null!==(t=null==p?void 0:p.id)&&void 0!==t?t:"");case 19:h=i.sent,d=h.files.filter((function(e){return"SAFE"!==e.folder_type})),i.next=26;break;case 23:i.prev=23,i.t0=i.catch(16),console.warn(i.t0);case 26:return i.prev=26,this.data=[{id:p.id,children:d,title:p.name,expand:!0,icon:"https://backstage-img-ssl.a.88cdn.com/019fc2a136a2881181e73fea74a4836efc02195d"}],this.data[0].children.forEach((function(e,t){g.systemFolderTypeList.includes(e.folder_type)?e.__order=Number.MIN_SAFE_INTEGER+g.systemFolderTypeList.indexOf(e.folder_type):e.__order=t,e.title=e.name,e.icon=e.icon_link,e.leaf="drive#file"===e.kind})),this.data[0].children.sort((function(e,t){return e.__order-t.__order})),i.prev=30,i.next=33,P({props:{title:f,data:this.data,callerInstance:this,bindingKeyGenerator:function(e){return e.id},disableClass:"is-disabled",disableFunction:function(e){return g.disabledNodeIdList.includes(e.id)},defaultPlaceholder:"新建文件夹",inCoffer:""!==p.id,operationType:s}});case 33:if(v=i.sent){for(y=[],b=v.node.id,_=v;_.$parent.node;)y.push({id:_.node.id,title:_.node.title}),_=_.$parent;y.push({id:_.node.id,title:_.node.title}),y.reverse(),null===(n=this.outPromise)||void 0===n||n.resolve({parentId:b,path:y,chosenNode:v.node})}else null===(r=this.outPromise)||void 0===r||r.resolve({error:"chosenNode is empty"});window.close(),i.next=42;break;case 38:i.prev=38,i.t1=i.catch(30),null===(o=this.outPromise)||void 0===o||o.resolve({error:"close window"}),window.close();case 42:i.next=47;break;case 44:i.prev=44,i.t2=i.catch(26),console.error(i.t2);case 47:case"end":return i.stop()}}),i,this,[[16,23],[26,44],[30,38]])})))}},{key:"systemFolderTypeList",get:function(){var e,t,n,r;return null!==(r=null===(n=null===(t=null===(e=this.state)||void 0===e?void 0:e.panGlobalRemoteConfig)||void 0===t?void 0:t.common)||void 0===n?void 0:n.system_folder_list)&&void 0!==r?r:T.SYSTEM_FOLDER_TYPE_LIST}}]),n}(d.Vue),S=D=I([Object(d.Component)({head:{title:"选择保存路径"}})],D),$=Object(O.a)(S,(function(){var e=this.$createElement;return(this._self._c||e)("div")}),[],!1,null,null,null);t.default=$.exports}}]);
//# sourceMappingURL=tree-path.05bd742.js.map