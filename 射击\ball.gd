# 球体控制脚本
extends CharacterBody2D

# 球的初始速度
var speed = 300
# 是否已经开始游戏
var game_started = false
# 球的速度向量
var ball_velocity = Vector2.ZERO

# 初始化
func _ready():
	# 设置随机数种子
	randomize()
	# 设置初始位置
	position = Vector2(400, 500)
	print("球初始化完成")
	# 确保球的碰撞检测正常工作
	set_collision_mask_value(1, true)  # 确保可以与默认层碰撞
	set_collision_layer_value(1, true)  # 确保在默认层上可被检测

# 处理物理更新
func _physics_process(delta):
	# 如果游戏还没开始
	if not game_started:
		# 检测空格键启动游戏
		if Input.is_key_pressed(KEY_SPACE):
			game_started = true
			# 设置初始方向（向上并稍微偏向一侧）
			var direction = Vector2(randf_range(-0.5, 0.5), -1).normalized()
			ball_velocity = direction * speed
			print("游戏开始，球的初始速度：", ball_velocity)
	else:
		# 设置球的速度
		velocity = ball_velocity
		
		# 游戏已开始，处理球的移动和碰撞
		var collision = move_and_collide(ball_velocity * delta)
		
		# 如果发生碰撞
		if collision:
			# 获取碰撞对象
			var collider = collision.get_collider()
			print("球发生碰撞，碰撞对象：", collider.name)
			
			# 如果碰到砖块
			if collider.is_in_group("bricks"):
				print("球碰到砖块：", collider.name, "，调用hit()方法")
				# 通知砖块被击中
				collider.call_deferred("hit")
				# 获取游戏管理器并增加分数
				var game_manager = get_parent()
				game_manager.increase_score()
				print("分数增加")
			
			# 反弹 - 根据碰撞法线计算反射向量
			ball_velocity = ball_velocity.bounce(collision.get_normal())
			print("球反弹，新速度：", ball_velocity)
			
			# 稍微增加速度，增加游戏难度
			speed += 2
			ball_velocity = ball_velocity.normalized() * speed
		
		# 检测是否掉出屏幕底部（游戏失败）
		if position.y > get_viewport_rect().size.y:
			# 重置游戏
			reset()
			# 通知游戏管理器游戏结束
			var game_manager = get_parent()
			game_manager.game_over()

# 重置球的状态
func reset():
	game_started = false
	speed = 300
	ball_velocity = Vector2.ZERO
	position = Vector2(400, 500)
	print("球重置")
