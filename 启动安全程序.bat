@echo off
chcp 65001 >nul
title 安全卡密验证程序

echo ========================================
echo 🔐 安全卡密验证程序
echo ========================================
echo.
echo 卡密: 555555
echo.
echo 请选择要运行的版本:
echo 1. 简化版 (推荐) - 基础安全保护
echo 2. 标准版 - 中等安全级别  
echo 3. 高级版 - 最高安全级别
echo 4. 退出
echo.

:choice
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto simple
if "%choice%"=="2" goto standard  
if "%choice%"=="3" goto advanced
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto choice

:simple
echo.
echo 正在启动简化版安全程序...
python simple_secure_login.py
goto end

:standard
echo.
echo 正在启动标准版安全程序...
python secure_login.py
goto end

:advanced
echo.
echo 正在启动高级版安全程序...
python secure_app.py
goto end

:exit
echo 再见！
exit

:end
echo.
echo 程序已结束
pause
