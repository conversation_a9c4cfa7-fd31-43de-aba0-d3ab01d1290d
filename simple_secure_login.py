"""
简化版安全卡密验证程序
卡密: 555555
包含基础的反逆向和加密保护
"""

import tkinter as tk
from tkinter import ttk, messagebox
import hashlib
import base64
import time
import threading
import random
import os
import sys

class BasicSecurity:
    """基础安全类"""
    
    def __init__(self):
        self.debug_detected = False
        self.start_time = time.time()
        self._init_protection()
    
    def _init_protection(self):
        """初始化保护机制"""
        # 启动反调试检测
        self._start_anti_debug()
        
        # 检查执行环境
        self._check_environment()
    
    def _start_anti_debug(self):
        """启动反调试检测"""
        def debug_monitor():
            while True:
                try:
                    # 检测执行时间异常
                    if self._check_timing_attack():
                        self.debug_detected = True
                        self._trigger_protection()
                    
                    # 检查可疑进程
                    if self._check_suspicious_processes():
                        self.debug_detected = True
                        self._trigger_protection()
                    
                    time.sleep(1)
                except:
                    pass
        
        thread = threading.Thread(target=debug_monitor, daemon=True)
        thread.start()
    
    def _check_timing_attack(self):
        """检查时间攻击"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        # 如果程序运行时间异常（可能在调试器中暂停）
        if elapsed > 300:  # 5分钟
            return True
        
        return False
    
    def _check_suspicious_processes(self):
        """检查可疑进程"""
        suspicious = [
            'ollydbg', 'x64dbg', 'ida', 'cheatengine', 
            'processhacker', 'procmon', 'wireshark'
        ]
        
        try:
            import psutil
            for proc in psutil.process_iter(['name']):
                proc_name = proc.info['name'].lower()
                if any(sus in proc_name for sus in suspicious):
                    return True
        except:
            pass
        
        return False
    
    def _check_environment(self):
        """检查运行环境"""
        # 检查是否在虚拟机中
        vm_indicators = ['vmware', 'virtualbox', 'qemu']
        
        try:
            import platform
            system_info = platform.platform().lower()
            if any(vm in system_info for vm in vm_indicators):
                self._trigger_protection()
        except:
            pass
    
    def _trigger_protection(self):
        """触发保护机制"""
        # 清理内存中的敏感数据
        for var_name in list(globals().keys()):
            if 'key' in var_name.lower() or 'pass' in var_name.lower():
                try:
                    del globals()[var_name]
                except:
                    pass
        
        # 退出程序
        os._exit(1)

class EncryptionManager:
    """加密管理器"""
    
    def __init__(self):
        self.salt = b'\x9f\x4e\x8a\x6c\x3d\x7b\x2f\x5e'
        self.key_hash = self._generate_key_hash()
    
    def _generate_key_hash(self):
        """生成密钥哈希"""
        # 真实卡密
        real_key = "555555"
        
        # 时间戳（小时级别）
        timestamp = str(int(time.time() / 3600))
        
        # 多重哈希
        combined = real_key + timestamp + self.salt.hex()
        hash1 = hashlib.sha256(combined.encode()).hexdigest()
        hash2 = hashlib.md5(hash1.encode()).hexdigest()
        
        return base64.b64encode(hash2.encode()).decode()
    
    def verify_key(self, input_key):
        """验证密钥"""
        if not input_key or len(input_key) != 6:
            return False
        
        # 生成输入密钥的哈希
        timestamp = str(int(time.time() / 3600))
        combined = input_key + timestamp + self.salt.hex()
        hash1 = hashlib.sha256(combined.encode()).hexdigest()
        hash2 = hashlib.md5(hash1.encode()).hexdigest()
        test_hash = base64.b64encode(hash2.encode()).decode()
        
        return test_hash == self.key_hash
    
    def encrypt_message(self, message):
        """加密消息"""
        key = hashlib.md5(b"secure_key_2024").digest()
        encrypted = bytearray()
        
        for i, char in enumerate(message):
            encrypted.append(ord(char) ^ key[i % len(key)])
        
        return base64.b64encode(encrypted).decode()
    
    def decrypt_message(self, encrypted_message):
        """解密消息"""
        try:
            encrypted = base64.b64decode(encrypted_message)
            key = hashlib.md5(b"secure_key_2024").digest()
            decrypted = ""
            
            for i, byte in enumerate(encrypted):
                decrypted += chr(byte ^ key[i % len(key)])
            
            return decrypted
        except:
            return "解密失败"

class SecureLoginApp:
    """安全登录应用"""
    
    def __init__(self):
        # 初始化安全机制
        self.security = BasicSecurity()
        self.encryption = EncryptionManager()
        
        # 应用状态
        self.login_attempts = 0
        self.max_attempts = 3
        self.locked_until = 0
        
        # 混淆的数据
        self._encrypted_welcome = self.encryption.encrypt_message("🎉 欢迎进入VIP个人页面！")
        
        # 初始化界面
        self._init_ui()
    
    def _init_ui(self):
        """初始化用户界面"""
        self.root = tk.Tk()
        self.root.title("🔐 安全验证系统")
        self.root.geometry("420x320")
        self.root.resizable(False, False)
        
        # 窗口居中
        self._center_window()
        
        # 创建界面
        self._create_interface()
        
        # 绑定事件
        self._bind_events()
    
    def _center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding="25")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🛡️ 安全验证系统", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="Advanced Security Authentication", 
                                  font=("Arial", 9, "italic"))
        subtitle_label.pack(pady=(0, 20))
        
        # 安全状态
        self.security_frame = ttk.LabelFrame(main_frame, text="系统状态", padding="10")
        self.security_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.security_status = ttk.Label(self.security_frame, text="🟢 安全检查通过", 
                                        foreground="green")
        self.security_status.pack()
        
        # 登录区域
        login_frame = ttk.LabelFrame(main_frame, text="身份验证", padding="15")
        login_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(login_frame, text="请输入您的卡密:", font=("Arial", 10)).pack(anchor=tk.W)
        
        self.key_var = tk.StringVar()
        self.key_entry = ttk.Entry(login_frame, textvariable=self.key_var, 
                                  font=("Consolas", 11), show="●", width=20)
        self.key_entry.pack(fill=tk.X, pady=(8, 12))
        
        # 按钮
        button_frame = ttk.Frame(login_frame)
        button_frame.pack(fill=tk.X)
        
        self.verify_button = ttk.Button(button_frame, text="🔓 验证", 
                                       command=self._verify_login)
        self.verify_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="❌ 退出", command=self._exit_app).pack(side=tk.LEFT)
        
        # 状态显示
        self.status_label = ttk.Label(main_frame, text="", font=("Arial", 9))
        self.status_label.pack(pady=(10, 0))
        
        self.attempts_label = ttk.Label(main_frame, text="", font=("Arial", 8), 
                                       foreground="orange")
        self.attempts_label.pack()
    
    def _bind_events(self):
        """绑定事件"""
        self.key_entry.bind('<Return>', lambda e: self._verify_login())
        self.root.protocol("WM_DELETE_WINDOW", self._exit_app)
        
        # 反调试快捷键
        self.root.bind('<Control-c>', self._security_check)
        self.root.bind('<F12>', self._security_check)
    
    def _security_check(self, event=None):
        """安全检查"""
        self.security.debug_detected = True
        self.security._trigger_protection()
    
    def _verify_login(self):
        """验证登录"""
        # 检查锁定状态
        if time.time() < self.locked_until:
            remaining = int(self.locked_until - time.time())
            self.status_label.config(text=f"⏰ 账户锁定中，剩余 {remaining} 秒", foreground="red")
            return
        
        # 检查安全状态
        if self.security.debug_detected:
            self.status_label.config(text="🚫 安全检查失败", foreground="red")
            return
        
        input_key = self.key_var.get().strip()
        self.login_attempts += 1
        
        # 验证密钥
        if self.encryption.verify_key(input_key):
            self._login_success()
        else:
            self._login_failed()
    
    def _login_success(self):
        """登录成功"""
        self.status_label.config(text="✅ 验证成功！正在进入...", foreground="green")
        self.security_status.config(text="🟢 身份验证通过", foreground="green")
        self.root.update()
        
        # 添加延迟
        time.sleep(1.2)
        
        # 打开个人页面
        self._open_personal_page()
    
    def _login_failed(self):
        """登录失败"""
        remaining = self.max_attempts - self.login_attempts
        
        if remaining > 0:
            self.status_label.config(text="❌ 卡密错误", foreground="red")
            self.attempts_label.config(text=f"剩余尝试: {remaining} 次")
        else:
            # 锁定账户
            self.locked_until = time.time() + 180  # 3分钟
            self.status_label.config(text="🔒 尝试次数过多，账户已锁定3分钟", foreground="red")
            self.attempts_label.config(text="")
            self.verify_button.config(state=tk.DISABLED)
            
            # 定时解锁
            self.root.after(180000, self._unlock_account)
        
        self.key_var.set("")
    
    def _unlock_account(self):
        """解锁账户"""
        self.login_attempts = 0
        self.locked_until = 0
        self.verify_button.config(state=tk.NORMAL)
        self.status_label.config(text="🔓 账户已解锁")
        self.attempts_label.config(text="")
    
    def _open_personal_page(self):
        """打开个人页面"""
        # 关闭登录窗口
        self.root.destroy()
        
        # 创建个人页面
        personal_window = tk.Tk()
        personal_window.title("🎊 VIP个人中心")
        personal_window.geometry("550x450")
        
        # 居中显示
        personal_window.update_idletasks()
        width = personal_window.winfo_width()
        height = personal_window.winfo_height()
        x = (personal_window.winfo_screenwidth() // 2) - (width // 2)
        y = (personal_window.winfo_screenheight() // 2) - (height // 2)
        personal_window.geometry(f'{width}x{height}+{x}+{y}')
        
        # 创建内容
        self._create_personal_content(personal_window)
        
        personal_window.mainloop()
    
    def _create_personal_content(self, window):
        """创建个人页面内容"""
        main_frame = ttk.Frame(window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 解密欢迎消息
        welcome_msg = self.encryption.decrypt_message(self._encrypted_welcome)
        
        # 欢迎标题
        ttk.Label(main_frame, text=welcome_msg, 
                 font=("Arial", 18, "bold"), foreground="green").pack(pady=(0, 15))
        
        ttk.Label(main_frame, text="您已成功通过安全验证系统", 
                 font=("Arial", 11)).pack(pady=(0, 20))
        
        # 用户信息
        info_frame = ttk.LabelFrame(main_frame, text="👤 用户信息", padding="15")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        user_info = [
            ("🆔 用户ID:", "VIP-555555"),
            ("🏆 会员等级:", "超级VIP"),
            ("⏰ 登录时间:", time.strftime("%Y-%m-%d %H:%M:%S")),
            ("🔑 会话ID:", hashlib.md5(str(time.time()).encode()).hexdigest()[:16]),
            ("📅 有效期:", "永久有效"),
            ("🛡️ 安全级别:", "最高")
        ]
        
        for label, value in user_info:
            row = ttk.Frame(info_frame)
            row.pack(fill=tk.X, pady=2)
            
            ttk.Label(row, text=label, font=("Arial", 9, "bold")).pack(side=tk.LEFT)
            ttk.Label(row, text=value, font=("Consolas", 9)).pack(side=tk.RIGHT)
        
        # 功能区域
        func_frame = ttk.LabelFrame(main_frame, text="🚀 功能中心", padding="15")
        func_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 功能按钮
        functions = [
            ("📊 个人资料", "查看和编辑您的详细个人信息"),
            ("⚙️ 系统设置", "配置个人偏好和安全选项"),
            ("📈 使用统计", "查看登录记录和使用情况"),
            ("🔒 安全中心", "管理密码和安全设置")
        ]
        
        for i, (title, desc) in enumerate(functions):
            btn = ttk.Button(func_frame, text=title, width=15,
                           command=lambda t=title, d=desc: messagebox.showinfo(t, d))
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky="ew")
        
        func_frame.grid_columnconfigure(0, weight=1)
        func_frame.grid_columnconfigure(1, weight=1)
        
        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X)
        
        ttk.Button(bottom_frame, text="🔄 刷新", 
                  command=lambda: messagebox.showinfo("刷新", "页面已刷新")).pack(side=tk.LEFT)
        
        ttk.Button(bottom_frame, text="🚪 退出", command=window.destroy).pack(side=tk.RIGHT)
    
    def _exit_app(self):
        """退出应用"""
        self.root.destroy()
    
    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        except Exception:
            os._exit(1)

def main():
    """主函数"""
    try:
        # 随机启动延迟
        time.sleep(random.uniform(0.1, 0.3))
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            sys.exit(1)
        
        # 运行应用
        app = SecureLoginApp()
        app.run()
        
    except KeyboardInterrupt:
        os._exit(1)
    except Exception:
        os._exit(1)

if __name__ == "__main__":
    main()
