@echo off
chcp 65001 >nul
title 植物大战僵尸内存工具

echo ========================================
echo 植物大战僵尸内存工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 正在检查依赖包...

REM 检查并安装依赖包
python -c "import psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 psutil...
    pip install psutil
)

python -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: tkinter 未安装，请重新安装Python并确保包含tkinter
    pause
    exit /b 1
)

echo 依赖检查完成
echo.

REM 检查是否以管理员权限运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 建议以管理员权限运行此程序
    echo 右键点击此文件，选择"以管理员身份运行"
    echo.
)

echo 正在启动内存工具...
echo.

REM 启动程序
python memory_tool.py

if %errorlevel% neq 0 (
    echo.
    echo 程序运行出错，错误代码: %errorlevel%
    pause
)
