; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="打砖块游戏"
run/main_scene="res://breakout_game.tscn"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="res://icon.svg"

[input]

paddle_left={"deadzone":0.5,"events":[{"device":0,"keycode":65,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null}]}
paddle_right={"deadzone":0.5,"events":[{"device":0,"keycode":68,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null}]}
start_game={"deadzone":0.5,"events":[{"device":0,"keycode":32,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null}]}
