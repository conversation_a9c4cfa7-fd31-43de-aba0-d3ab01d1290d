module.exports=function(e){var o={};function t(r){if(o[r])return o[r].exports;var n=o[r]={i:r,l:!1,exports:{}};return e[r].call(n.exports,n,n.exports,t),n.l=!0,n.exports}return t.m=e,t.c=o,t.d=function(e,o,r){t.o(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,o){if(1&o&&(e=t(e)),8&o)return e;if(4&o&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&o&&"string"!=typeof e)for(var n in e)t.d(r,n,function(o){return e[o]}.bind(null,n));return r},t.n=function(e){var o=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(o,"a",o),o},t.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},t.p="",t(t.s=1)}([function(e,o){e.exports=require("path")},function(e,o,t){e.exports=t(2)},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),t(3),("browser"===process.type?global.xlDesktopApplicationSolution:window.xlDesktopApplicationSolution).GetPerformanceMonitorReport().initPerformanceMonitor("",{mainLoopHangCheckEnabled:!0,cpuUsageCheckEnabled:!0,memoryUsageCheckEnabled:!0})},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0});const r=t(4);"browser"===process.type?global.xlDesktopApplicationSolution=r.xlDesktopApplicationSolution:(window.xlDesktopApplicationSolution=r.xlDesktopApplicationSolution,process.once("loaded",()=>{window.xlDesktopApplicationSolution=r.xlDesktopApplicationSolution}))},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0});const r=t(5);var n;!function(e){e.XDAS=class{constructor(){this.performanceMonitorReport=null}GetPerformanceMonitorReport(){return null===this.performanceMonitorReport&&(this.performanceMonitorReport=new r.PerformanceMonitorReport),this.performanceMonitorReport}}}(n||(n={})),o.xlDesktopApplicationSolution=new n.XDAS},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0});const r=t(0),n=t(6),i=t(7);let l=t(8).default(r.join(i.GlobalDataNS.getRootDir(),"../bin/ThunderHelper.node")).performanceMonitorReporter;o.PerformanceMonitorReport=class{initPerformanceMonitor(e,o){l.init("browser"===process.type,process.pid,function(e){let o="";if(0===e.length&&"renderer"===process.type){let e=r.normalize(decodeURIComponent(window.location.href)),t=e.indexOf(process.resourcesPath);t=t>-1?t+process.resourcesPath.length+1:t;let n=e.length-1,i=e.indexOf("?"),l=e.indexOf("#");n=i>-1?Math.min(i-1,n):n,n=l>-1?Math.min(l-1,n):n,t>-1&&n>=t&&(o=e.substr(t,n-t+1))}return 0===o.length&&(o=0!==e.length?e:process.type),o=o.replace(/\||,|;/g,"_")}(e),n.release(),o),l.start()}uninitPerformanceMonitor(){l.stop()}}},function(e,o){e.exports=require("os")},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0});const r=t(0);let n=r.normalize(r.join(process.execPath,"../resources/app")),i=r.resolve(n,"../../../profiles").replace(/\\/g,"/");!function(e){e.getRootDir=function(){return n},e.getProfilesDir=function(){return i}}(o.GlobalDataNS||(o.GlobalDataNS={}))},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=function(e){return require(e)}}]);
//# sourceMappingURL=common-preload.js.map