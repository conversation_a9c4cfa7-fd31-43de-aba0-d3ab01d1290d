# 斗地主游戏开发者指南

本文档为开发者提供详细的代码结构说明和扩展指南。

## 项目架构

### 核心架构模式
- **MVC模式**: Model(数据) - View(UI) - Controller(逻辑)
- **组件化设计**: 每个功能模块独立封装
- **事件驱动**: 使用Godot信号系统进行通信

### 主要组件

#### 1. 游戏管理器 (GameManager.gd)
**职责**: 游戏流程控制、状态管理
```gdscript
# 主要方法
start_new_game()     # 开始新游戏
play_cards()         # 处理出牌
next_turn()          # 切换玩家
end_game()           # 结束游戏
```

#### 2. 卡牌系统 (Card.gd, Deck.gd)
**职责**: 卡牌数据和牌堆管理
```gdscript
# Card类主要属性
suit: Suit           # 花色
rank: Rank           # 点数
is_selected: bool    # 选中状态

# Deck类主要方法
create_deck()        # 创建54张牌
shuffle()            # 洗牌
deal_cards()         # 发牌
```

#### 3. 玩家系统 (Player.gd)
**职责**: 玩家数据和AI逻辑
```gdscript
# 主要方法
add_cards()          # 添加手牌
select_card()        # 选择卡牌
play_selected_cards() # 出选中的牌
ai_make_decision()   # AI决策
```

#### 4. UI控制器 (MainController.gd)
**职责**: 连接游戏逻辑和用户界面
```gdscript
# 主要职责
- 处理用户输入
- 更新UI显示
- 管理卡牌区域
- 播放音效
```

#### 5. 辅助系统
- **AudioManager**: 音效管理
- **GameStats**: 游戏统计
- **SaveSystem**: 存档系统
- **GameConfig**: 配置管理

## 扩展指南

### 1. 添加新的牌型

#### 步骤1: 定义牌型枚举
```gdscript
# 在适当的脚本中添加
enum CardPattern {
    SINGLE,      # 单张
    PAIR,        # 对子
    TRIPLE,      # 三张
    STRAIGHT,    # 顺子
    BOMB         # 炸弹
}
```

#### 步骤2: 实现牌型检测
```gdscript
func detect_card_pattern(cards: Array[Card]) -> CardPattern:
    if cards.size() == 1:
        return CardPattern.SINGLE
    elif cards.size() == 2:
        # 检查是否为对子
        if cards[0].rank == cards[1].rank:
            return CardPattern.PAIR
    # 添加更多牌型检测逻辑
    return CardPattern.SINGLE
```

#### 步骤3: 更新出牌验证
```gdscript
func can_play_cards(cards: Array[Card], last_cards: Array[Card]) -> bool:
    var pattern = detect_card_pattern(cards)
    var last_pattern = detect_card_pattern(last_cards)
    
    # 检查牌型是否匹配
    if pattern != last_pattern:
        return false
    
    # 检查大小比较
    return compare_card_patterns(cards, last_cards)
```

### 2. 改进AI系统

#### 当前AI限制
- 只能处理单张牌
- 没有策略规划
- 不考虑手牌组合

#### 改进方向
```gdscript
func advanced_ai_decision(hand_cards: Array[Card], last_played: Array[Card]) -> Array[Card]:
    # 1. 分析手牌组合
    var combinations = analyze_hand_combinations(hand_cards)
    
    # 2. 评估出牌策略
    var strategies = evaluate_strategies(combinations, last_played)
    
    # 3. 选择最优策略
    return select_best_strategy(strategies)
```

### 3. 添加网络多人游戏

#### 网络架构建议
```gdscript
# 创建网络管理器
extends Node
class_name NetworkManager

# 主要功能
func create_server()         # 创建服务器
func join_server()           # 加入服务器
func send_card_data()        # 发送出牌数据
func sync_game_state()       # 同步游戏状态
```

### 4. 美化界面

#### 卡牌贴图系统
```gdscript
# 在Card.gd中添加
func load_card_texture():
    var texture_path = "res://textures/cards/" + get_card_filename()
    if ResourceLoader.exists(texture_path):
        card_sprite.texture = load(texture_path)
    else:
        # 使用默认文字显示
        update_card_text()
```

#### 动画系统
```gdscript
# 添加卡牌动画
func animate_card_play(card: Card, target_position: Vector2):
    var tween = create_tween()
    tween.parallel().tween_property(card, "position", target_position, 0.5)
    tween.parallel().tween_property(card, "rotation", deg_to_rad(360), 0.5)
    tween.tween_callback(card.queue_free)
```

## 调试技巧

### 1. 游戏状态调试
```gdscript
# 在GameManager中添加调试信息
func print_game_state():
    print("=== 游戏状态 ===")
    print("当前玩家: ", current_player_index)
    print("游戏状态: ", current_state)
    print("上次出牌: ", get_cards_string(last_played_cards))
    for i in range(players.size()):
        print("玩家", i, "手牌数: ", players[i].get_hand_count())
```

### 2. 卡牌数据验证
```gdscript
# 验证牌堆完整性
func validate_deck():
    var total_cards = 0
    for player in players:
        total_cards += player.get_hand_count()
    total_cards += bottom_cards.size()
    total_cards += last_played_cards.size()
    
    assert(total_cards == 54, "牌数不正确: " + str(total_cards))
```

## 性能优化

### 1. 对象池
```gdscript
# 卡牌对象池
class CardPool:
    var available_cards: Array[Card] = []
    var used_cards: Array[Card] = []
    
    func get_card() -> Card:
        if available_cards.is_empty():
            return Card.new()
        return available_cards.pop_back()
    
    func return_card(card: Card):
        available_cards.append(card)
```

### 2. 减少信号连接
```gdscript
# 批量处理信号
var pending_updates: Array = []

func queue_ui_update(update_type: String):
    pending_updates.append(update_type)
    if not is_processing_updates:
        call_deferred("process_ui_updates")

func process_ui_updates():
    # 批量处理UI更新
    for update in pending_updates:
        handle_ui_update(update)
    pending_updates.clear()
```

## 测试策略

### 1. 单元测试
```gdscript
# 测试卡牌比较
func test_card_comparison():
    var card1 = Card.new()
    card1.rank = Card.Rank.ACE
    var card2 = Card.new()
    card2.rank = Card.Rank.KING
    
    assert(card1.get_card_value() > card2.get_card_value())
```

### 2. 集成测试
```gdscript
# 测试完整游戏流程
func test_full_game():
    var game = GameManager.new()
    game.initialize_game()
    game.start_new_game()
    
    # 模拟游戏过程
    simulate_game_turns(game)
    
    # 验证结果
    assert(game.current_state == GameManager.GameState.GAME_OVER)
```

## 发布准备

### 1. 资源优化
- 压缩贴图文件
- 优化音效文件大小
- 移除未使用的资源

### 2. 构建配置
```gdscript
# 在project.godot中配置
[application]
config/name="斗地主游戏"
config/version="1.0.0"

[rendering]
renderer/rendering_method="mobile"  # 移动端优化
```

### 3. 平台适配
- Windows桌面版
- Android移动版
- Web版本

这个开发者指南提供了扩展游戏功能的基础框架和最佳实践。
