(window.webpackJsonp=window.webpackJsonp||[]).push([[18],{1334:function(e,t,i){var o=i(1363);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("1162dbe2",o,!0,{sourceMap:!1})},1335:function(e,t,i){var o=i(1372);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("3639ee88",o,!0,{sourceMap:!1})},1337:function(e,t,i){var o=i(1375);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("14c6a960",o,!0,{sourceMap:!1})},1338:function(e,t,i){"use strict";var o=i(1334);i.n(o).a},1347:function(e,t,i){"use strict";var o=i(1379);i.n(o).a},1354:function(e,t,i){"use strict";i(7),i(4);var o=i(5),r=i(82),n=i.n(r),a=i(203),l=function(e,t,i,o){return new(i||(i=Promise))((function(r,n){function a(e){try{s(o.next(e))}catch(e){n(e)}}function l(e){try{s(o.throw(e))}catch(e){n(e)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,l)}s((o=o.apply(e,t||[])).next())}))};t.a=o.default.extend({mounted:function(){return l(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(a.isLogEnable)();case 2:if(!e.sent){e.next=4;break}window.addEventListener("keyup",this.openDevTools);case 4:case"end":return e.stop()}}),e,this)})))},beforeDestroy:function(){window.removeEventListener("keyup",this.openDevTools)},methods:{openDevTools:function(e){return l(this,void 0,void 0,regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("F12"!==e.key||!e.shiftKey){t.next=5;break}return t.next=3,n.a.getCurrentWindow().__resolve();case 3:t.sent.webContents.openDevTools();case 5:case"end":return t.stop()}}),t)})))}}})},1363:function(e,t,i){var o=i(89),r=i(126),n=i(684);t=o(!1);var a=r(n);t.push([e.i,".xly-icon-type{display:inline-block;width:30px;height:30px;background:url("+a+") -125px -125px no-repeat;background-size:210px auto;vertical-align:top}.xly-icon-type.is-middle{zoom:.8}.xly-icon-type.is-small{zoom:.5333}.xly-icon-type.xly-type-apk{background-position:-5px -5px}.xly-icon-type.xly-type-bt-file{background-position:-45px -5px}.xly-icon-type.xly-type-bt-link{background-position:-5px -45px}.xly-icon-type.xly-type-doc{background-position:-45px -45px}.xly-icon-type.xly-type-group{background-position:-85px -5px}.xly-icon-type.xly-type-link{background-position:-85px -45px}.xly-icon-type.xly-type-magnetic{background-position:-5px -85px}.xly-icon-type.xly-type-music{background-position:-45px -85px}.xly-icon-type.xly-type-pdf{background-position:-85px -85px}.xly-icon-type.xly-type-pic{background-position:-125px -5px}.xly-icon-type.xly-type-ppt{background-position:-125px -45px}.xly-icon-type.xly-type-rar{background-position:-125px -85px}.xly-icon-type.xly-type-save{background-position:-45px -125px}.xly-icon-type.xly-type-txt{background-position:-85px -125px}.xly-icon-type.xly-type-unknow{background-position:-125px -125px}.xly-icon-type.xly-type-avi,.xly-icon-type.xly-type-flv,.xly-icon-type.xly-type-mkv,.xly-icon-type.xly-type-mov,.xly-icon-type.xly-type-mp4,.xly-icon-type.xly-type-mpg,.xly-icon-type.xly-type-rm,.xly-icon-type.xly-type-rmvb,.xly-icon-type.xly-type-video,.xly-icon-type.xly-type-wmv{background-position:-165px -5px}.xly-icon-type.xly-type-word{background-position:-165px -45px}.xly-icon-type.xly-type-xls{background-position:-165px -85px}.xly-icon-type.xly-type-saveto{background-position:-45px -125px}.xly-icon-type.xly-type-resource{background-position:-5px -125px}.xly-icon-type.xly-type-gif{background-position:-165px -125px}.xly-icon-type.xly-type-ipa{background-position:-5px -165px}.xly-icon-type.xly-type-ipsw{background-position:-45px -165px}.xly-icon-type.xly-type-dll{background-position:-85px -165px}.xly-icon-type.xly-type-chm{background-position:-125px -165px}.xly-icon-type.xly-type-text{background-position:-165px -165px}.xly-icon-type.xly-type-exe,.xly-icon-type.xly-type-install{background-position:-5px -205px}.xly-icon-type.xly-type-iso{background-position:-45px -205px}.xly-icon-type.xly-type-safe{background-position:-85px -205px}",""]),e.exports=t},1371:function(e,t,i){"use strict";var o=i(1335);i.n(o).a},1372:function(e,t,i){(t=i(89)(!1)).push([e.i,".xly-dialog-vip .td-dialog__header{-webkit-app-region:drag}",""]),e.exports=t},1374:function(e,t,i){"use strict";var o=i(1337);i.n(o).a},1375:function(e,t,i){(t=i(89)(!1)).push([e.i,"body .xly-dialog-vip{width:420px}body .xly-dialog-vip .td-dialog__body{min-height:196px}.xly-dialog-vip__image{margin:18px auto 0;width:140px;height:100px}.xly-dialog-vip__image img{width:100%;height:100%;-o-object-fit:contain;object-fit:contain}.xly-dialog-vip__banner{margin:18px auto 0;width:298px;height:108px}.xly-dialog-vip__banner img{width:100%;height:100%;-o-object-fit:contain;object-fit:contain}.xly-dialog-vip__text{margin-top:20px;text-align:center;font-size:16px}.xly-dialog-vip__note{margin-top:8px;font-size:12px;color:var(--color-auxiliary)}.xly-dialog-vip__tips{margin:18px 0 -15px;color:#d69a57;line-height:20px;font-size:12px;text-align:center}body .xly-dialog-vip .td-button{color:#4d4d4d;font-size:14px;background-image:linear-gradient(90deg,#f5d7a2 11%,#f1c880 95%);border-radius:4px}body .xly-dialog-vip .td-button.xly-button--super{color:#ffe7a9;background-image:linear-gradient(270deg,#1c1815 2%,#2e2a27 98%)}",""]),e.exports=t},1378:function(e,t,i){var o=i(1451);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("7aeabf7e",o,!0,{sourceMap:!1})},1379:function(e,t,i){var o=i(1452);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("071c1e9c",o,!0,{sourceMap:!1})},1380:function(e,t,i){var o=i(1454);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("3339c2c5",o,!0,{sourceMap:!1})},1381:function(e,t,i){var o=i(1456);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("5c0816a1",o,!0,{sourceMap:!1})},1382:function(e,t,i){var o=i(1457);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("6b412dd5",o,!0,{sourceMap:!1})},1385:function(e,t,i){var o=i(1463);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("66d79d41",o,!0,{sourceMap:!1})},1386:function(e,t,i){var o=i(1465);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("4d3c067e",o,!0,{sourceMap:!1})},1387:function(e,t,i){var o=i(1467);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);(0,i(90).default)("30b1c5c3",o,!0,{sourceMap:!1})},1400:function(e,t,i){"use strict";i(10),i(11),i(32),i(234),i(7);var o=i(33),r=i(40),n=i(70),a=i(71),l=i(37),s=(i(4),i(29)),c=i(18),d=i(1330),p=i.n(d),u=i(82),y=i.n(u),f=i(2);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,o=Object(l.a)(e);if(t){var r=Object(l.a)(this).constructor;i=Reflect.construct(o,arguments,r)}else i=o.apply(this,arguments);return Object(a.a)(this,i)}}var x=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===("undefined"==typeof Reflect?"undefined":Object(s.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var l=e.length-1;l>=0;l--)(r=e[l])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a},b=function(e,t,i,o){return new(i||(i=Promise))((function(r,n){function a(e){try{s(o.next(e))}catch(e){n(e)}}function l(e){try{s(o.throw(e))}catch(e){n(e)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,l)}s((o=o.apply(e,t||[])).next())}))},g=function(e){Object(n.a)(i,e);var t=h(i);function i(){return Object(o.a)(this,i),t.apply(this,arguments)}return Object(r.a)(i,[{key:"close",value:function(){return b(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y.a.getCurrentWindow().__resolve();case 2:e.sent.close();case 4:case"end":return e.stop()}}),e)})))}},{key:"openVip",value:function(){if("好的"===this.buttonText)this.close(),this.$eventTrack("cslist_tab_hytq_popup_click",{vip_type:this.$store.getters["users/curVipType"],tab:"cloud_add",clickid:"known"},"xlx_vip_event");else{this.$eventTrack("cslist_tab_hytq_popup_click",{vip_type:this.$store.getters["users/curVipType"],tab:"cloud_add",clickid:"开通超级会员"===this.buttonText?"open_super":"upgrade_super"},"xlx_vip_event");var e=this.$sget(this.promotionConfig,"urlTask","button","url")||"开通超级会员"===this.buttonText?"https://pay.xunlei.com/pay.html?payconfid=bc7b8925ba":"https://pay.xunlei.com/pay.html",t="referfrom=v_pc_xlx_hytq_yp_cloudadd&aidfrom=fast_add_pop",i=""===new URL(e).search?"".concat(e,"?").concat(t):"".concat(e,"&").concat(t);Object(f.callServer)("OpenNewTab",i),this.close()}}},{key:"mounted",value:function(){this.$eventTrack("cslist_tab_hytq_popup_show",{vip_type:this.$store.getters["users/curVipType"],tab:"cloud_add",pop_type:"fast_add"},"xlx_vip_event")}},{key:"isSuperV",get:function(){return this.$store.getters["users/isSuperV"]}},{key:"buttonText",get:function(){return this.isSuperV?"好的":this.$sget(this.promotionConfig,"urlTask","tc_words","text")||"开通超级会员"}}]),i}(c.Vue);x([Object(c.Prop)({type:Object})],g.prototype,"promotionConfig",void 0);var _=g=x([Object(c.Component)({data:function(){return{img:p.a}}})],g),v=(i(1371),i(1328),i(279),i(1339),i(1346),i(1374),i(72)),m=Object(v.a)(_,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("td-dialog",{attrs:{visible:!0,"custom-class":"xly-dialog-vip"},on:{close:e.close}},[i("div",{staticClass:"xly-dialog-vip__banner"},[i("img",{attrs:{src:e.img,alt:"云添加"}})]),e._v(" "),i("div",{staticClass:"xly-dialog-vip__text"},[e.isSuperV?[i("div",{staticClass:"xly-dialog-vip__text"},[i("p",[e._v("正在为您进行极速云添加")]),e._v(" "),i("p",{staticClass:"xly-dialog-vip__note"},[e._v("\n          添加完成后即可观看\n        ")])])]:[i("div",{staticClass:"xly-dialog-vip__text"},[i("p",[e._v("正在进行云添加")]),e._v(" "),i("p",{staticClass:"xly-dialog-vip__note"},[e._v("\n          添加完成即可观看\n        ")]),e._v(" "),i("p",{staticClass:"xly-dialog-vip__tips"},[e._v("\n          开启极速云添加，效率最高提升10倍\n        ")])])]],2),e._v(" "),i("td-button",{attrs:{slot:"footer",size:"large"},on:{click:e.openVip},slot:"footer"},[e._v("\n    "+e._s(e.buttonText)+"\n  ")])],1)}),[],!1,null,null,null);t.a=m.exports},1405:function(e,t,i){"use strict";var o=i(1382);i.n(o).a},1450:function(e,t,i){"use strict";var o=i(1378);i.n(o).a},1451:function(e,t,i){(t=i(89)(!1)).push([e.i,".td-dialog__header{-webkit-app-region:drag}",""]),e.exports=t},1452:function(e,t,i){(t=i(89)(!1)).push([e.i,"body .td-dialog .td-dialog__close:hover{background-color:#ff7b5f}body .td-dialog h2{color:var(--color-title)}body .td-dialog .xly-dialog__footer{position:relative;display:flex;justify-content:flex-end;width:100%}body .td-dialog .xly-dialog__footer .xly-dialog-path__new{width:90px}body .td-dialog .xly-dialog__footer-operate{position:absolute;left:0;display:flex;align-items:center;height:30px}body .td-dialog .xly-dialog__footer-link{color:var(--color-primary)}body .td-dialog .xly-dialog__button{display:flex}body .td-dialog .xly-dialog__button .td-button{width:80px;font-size:12px}body .xly-dialog-site{position:relative;margin-top:15px}body .xly-dialog-site .td-select{margin-top:12px}body .xly-dialog-site .td-select .td-checkbox{margin-left:13px}body .xly-dialog-site .td-select .td-checkbox__label{margin:0 12px}body .xly-dialog-site .td-select .td-select__drop{margin-right:34px}body .xly-dialog-site .xly-select__file{position:absolute;right:8px;color:var(--color-defalut)}body .xly-dialog-site .xly-select__file:hover{color:var(--color-hover)}body .xly-dialog-site .xly-select__size{margin-right:10px;color:var(--color-secondary)}body .xly-dialog-site .xly-select__size.is-warn{color:#ff9700}body .xly-dialog-site .xly-select__size.is-danger{color:var(--color-danger)}body .xly-dialog-site .xly-select__link{position:absolute;top:2px;left:80px;width:calc(100% - 88px);line-height:26px;text-indent:5px;color:var(--color-primary);background:var(--background-module)}body .xly-dialog-site .td-input__inner{padding:6px 10px}.xly-dialog-site__checkbox{margin:12px 0 0}",""]),e.exports=t},1453:function(e,t,i){"use strict";var o=i(1380);i.n(o).a},1454:function(e,t,i){(t=i(89)(!1)).push([e.i,'body .xly-dialog-task{display:flex;flex-direction:column;flex-shrink:0;width:100%;height:100%}body .xly-dialog-task .td-dropdown-group .td-button:first-child{text-indent:36px}body .xly-dialog-task .td-dropdown-group .td-icon-arrow-drop:before{font-family:"xly-icon";content:"\\e7c7"}body .xly-dialog-task .td-tooltip-wrapper{display:block;width:100%}body .xly-dialog-task .xly-icon-arrow-right{margin-left:-5px;color:var(--color-secondary);cursor:pointer}body .xly-dialog-task .td-dialog__body,body .xly-dialog-task .xly-file-list{display:flex;flex-direction:column;flex:1;flex-basis:0}body .xly-dialog-task .xly-file-list{position:relative;border:1px solid var(--color-border);border-radius:4px}body .xly-dialog-task .xly-file-list .td-table{display:flex;flex-direction:column;margin:0;width:100%;height:100%}body .xly-dialog-task .xly-file-list .td-table__body-wrapper{flex:1;flex-basis:0;height:unset;max-height:unset}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar{width:0;background:transparent}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar:horizontal{height:0}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar-thumb{border-radius:2px;width:0;background:var(--color-scrollbar)}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-task .xly-file-list .td-table__body-wrapper::-webkit-scrollbar-thumb{border-right:0}body .xly-dialog-task .xly-file-list .td-icon-arrow-drop{position:relative;left:-2px}body .xly-dialog-task .xly-file-list td .td-table__text{color:var(--color-secondary);padding-left:5px}body .xly-dialog-task .xly-file-list .td-table tr th{height:23px}body .xly-dialog-task .xly-file-list .td-table tr th:first-child .td-table__text{padding-left:0}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text{position:relative;justify-content:center}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text .td-icon-sequence{position:absolute;top:4px;right:-2px}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text:after,body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text:before{position:absolute;top:7px;left:-5px;width:1px;height:10px;background:var(--color-border);content:""}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(2) .td-table__text:after{left:auto;right:-5px}body .xly-dialog-task .xly-file-list .td-table tr th:nth-child(3){padding-left:20px}body .xly-dialog-task .xly-file-list tr td:nth-child(2) .td-table__text{position:relative;display:block;padding-left:0;max-width:46px;height:28px;line-height:28px;text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}body .xly-dialog-task .xly-file-list .td-tree-node__content{height:28px;cursor:pointer}body .xly-dialog-task .xly-file-list .td-checkbox__inner,body .xly-dialog-task .xly-file-list .td-tree-node__content .td-tree-node__label{cursor:pointer}body .xly-dialog-task .td-dropdown-group .td-button--large:last-child:before{opacity:.2}body .xly-dialog-task .td-button--large{font-size:14px}body .xly-dialog-task .td-table__body-wrapper{height:128px}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar{width:6px;background:transparent}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar:horizontal{height:6px}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .xly-dialog-task .td-table__body-wrapper::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-task .td-select__drop:hover{color:var(--color-primary)}body .xly-dialog-task .td-select__choose{width:36px}body .xly-dialog-task .td-select__choose:before{display:none}body .xly-dialog-task .td-dialog__footer{margin-top:30px}body .xly-dialog-task .td-loading.is-stop .td-loading-bar__inner{animation:none}body .xly-dialog-task .td-loading-bar{height:4px}body .xly-dialog-task .xly-icon-resize{position:absolute;right:0;bottom:0;color:var(--color-primary-gray-2);cursor:nwse-resize}.xly-dialog-task__button{display:flex;align-items:center;width:100%;height:40px;background:var(--color-primary);border-radius:4px}.xly-dialog-task__button.is-disabled{background:var(--background-main)}.xly-dialog-task__button.is-disabled .xly-button-down,.xly-dialog-task__button.is-disabled .xly-button-wait{color:var(--color-disabled);background:var(--background-main);cursor:default}.xly-dialog-task__button .xly-line{margin:0;height:16px;opacity:.5}.xly-dialog-task__button .xly-button-down{flex:1;border-radius:4px 0 0 4px}.xly-dialog-task__button .xly-button-wait{width:40px;border-radius:0 4px 4px 0}body .xly-dialog-resource{position:relative;display:flex;height:32px;margin-bottom:20px}.xly-dialog-resource__name{display:flex;align-items:center;width:335px}.xly-dialog-resource__text{-webkit-line-clamp:2;display:-webkit-box;margin-left:12px;font-size:13px;word-break:break-all;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;white-space:normal;line-height:1.3}.xly-dialog-resource__text.is-fail{color:var(--color-danger)}.xly-dialog-resource__text.is-more{display:flex;width:100%;word-break:keep-all}.xly-dialog-resource__text.is-more span{margin-right:6px;max-width:250px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}.xly-dialog-resource__rename{display:inline-flex;align-items:center;margin:0 8px 0 6px;width:16px;height:17px;color:var(--color-primary-gray-2)}.xly-dialog-resource__rename:hover{color:var(--color-primary)}.xly-dialog-resource__size{position:absolute;right:0;display:flex;align-items:center;color:var(--color-secondary);line-height:30px}.xly-dialog-resource__size i{margin-left:10px;cursor:pointer}.xly-dialog-resource__size i:hover{color:var(--color-primary)}.xly-dialog-resource__operate{display:flex;align-items:center}body .xly-dialog-resource .td-select,body .xly-dialog-resource .td-textarea{margin-left:12px;flex:1}body .xly-dialog-resource .td-textarea__inner{height:44px}body .xly-dialog-resource .td-dropdown-menu{display:none}body .xly-dialog-resource .xly-down-setting{top:0}body .xly-dialog-tasks{border:1px solid var(--color-border)}.xly-dialog-tasks__list{height:70px;overflow-y:auto;padding:5px 4px 0;font-size:0}.xly-dialog-tasks__list::-webkit-scrollbar{width:6px;background:transparent}.xly-dialog-tasks__list::-webkit-scrollbar:horizontal{height:6px}.xly-dialog-tasks__list::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}.xly-dialog-tasks__list::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}.xly-dialog-tasks__list::-webkit-scrollbar-corner{background:transparent}.xly-dialog-tasks__size{height:30px;text-indent:10px;line-height:30px;background:var(--background-main);border-radius:0 0 4px 4px}body .xly-tag-tasks{display:inline-block;width:54px;height:20px;margin:6px 0 0 6px;font-size:12px;line-height:18px;color:var(--color-primary);text-align:center;background:var(--background-secondary);border:1px solid var(--color-primary);border-radius:2px;box-sizing:border-box;overflow:hidden}body .xly-tag-tasks.is-disabled{color:var(--color-secondary);background:var(--background-main);border-color:var(--background-main)}body .xly-dialog-link{position:relative;z-index:2}.xly-dialog-link__add{display:flex;align-items:center;padding-left:12px;height:30px;color:var(--color-primary);border:1px solid var(--color-border);border-radius:4px;cursor:pointer}.xly-dialog-link__add i{margin-right:8px}.xly-dialog-link__add:hover{border-color:var(--color-primary)}.xly-dialog-link__content{position:absolute;top:0;left:0;width:100%;height:0;background:var(--background-module);border-radius:4px;box-shadow:0 0 10px 0 var(--color-shadow);z-index:1;overflow:hidden;transition:height .4s}.xly-dialog-link__content .td-textarea__inner{padding:12px;height:127px;border:unset;white-space:nowrap}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar{width:8px;background:transparent}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar:horizontal{height:8px}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-thumb{width:6px;background:var(--color-scrollbar)}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-corner{background:transparent}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-thumb{border:0;border-radius:2px;cursor:default}.xly-dialog-link__content .td-textarea__inner::-webkit-scrollbar-track{background:var(--background-module)}.xly-dialog-link__content.is-show{height:164px}.xly-dialog-link__operate{display:flex;justify-content:space-between;align-items:center;padding:0 12px;height:36px;border-top:1px solid var(--color-border)}.xly-dialog-link__operate a{color:var(--color-secondary)}body .xly-down-setting{position:absolute;top:23px;left:-1px;width:calc(100% + 2px);height:250px;z-index:1;padding:12px 12px 0;background:var(--background-module);box-shadow:0 0 20px var(--color-shadow);box-sizing:border-box;border-radius:4px}.xly-down-setting__item{display:flex;justify-content:space-between;margin-bottom:8px}.xly-down-setting__item .td-input__inner{width:90px}.xly-down-setting__item--link:first-child .td-input__label{display:block;margin-bottom:6px}.xly-down-setting__item--link .td-input{width:100%}.xly-down-setting__item--link .td-input__inner{display:block;width:100%}.xly-down-setting__operate{display:flex;justify-content:space-between;align-items:center;height:36px}.xly-down-setting__operate a{font-size:14px;color:var(--color-primary)}body .xly-dialog-normal{position:relative;display:flex;flex-direction:column;margin-top:12px;flex:1;flex-basis:0;border:1px solid var(--color-border);border-radius:4px}.xly-dialog-normal__title{display:flex;line-height:23px;align-items:center;box-sizing:border-box;color:var(--color-secondary)}.xly-dialog-normal__title .xly-dialog-normal__name{text-indent:60px}.xly-dialog-normal__title .xly-dialog-normal__type:after,.xly-dialog-normal__title .xly-dialog-normal__type:before{position:absolute;top:7px;left:0;width:1px;height:10px;background:var(--color-border);content:""}.xly-dialog-normal__title .xly-dialog-normal__type:after{left:auto;right:0}.xly-dialog-normal__title .xly-dialog-normal__size{text-indent:20px}.xly-dialog-normal__name{display:flex;align-items:center;width:calc(100% - 149px)}.xly-dialog-normal__name .td-checkbox{overflow:hidden}.xly-dialog-normal__name .td-checkbox__label{display:none}.xly-dialog-normal__type{position:relative;width:50px;display:block;text-align:center;white-space:nowrap;overflow:hidden;color:var(--color-secondary)}.xly-dialog-normal__size{width:100px;position:relative;color:var(--color-secondary);text-indent:5px}.xly-dialog-normal__item{display:flex;height:32px;line-height:32px;padding-left:13px}.xly-dialog-normal__item .td-dropdown-menu__item{padding:0 0 0 8px}.xly-dialog-normal__choose{position:absolute;right:14px;color:var(--color-secondary)}.xly-dialog-normal__choose:hover{color:var(--color-primary)}.xly-dialog-normal__content{flex:1;flex-basis:0;overflow-y:hidden}.xly-dialog-normal__content::-webkit-scrollbar{width:6px;background:transparent}.xly-dialog-normal__content::-webkit-scrollbar:horizontal{height:6px}.xly-dialog-normal__content::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}.xly-dialog-normal__content::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}.xly-dialog-normal__content::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-normal .xly-dialog__down-unordered{overflow-y:auto;height:100%}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar{width:6px;background:transparent}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar:horizontal{height:6px}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar-thumb{border-radius:2px;width:6px;background:var(--color-scrollbar)}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .xly-dialog-normal .xly-dialog__down-unordered::-webkit-scrollbar-corner{background:transparent}body .xly-dialog-normal .xly-dialog-normal__label{max-width:calc(100% - 80px);margin-left:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}.xly-dialog-normal__group{position:relative;display:flex;align-items:center;height:40px;padding:0 8px 0 14px}.xly-dialog-normal__group .td-input{margin-left:7px}.xly-dialog-normal__group .td-input .td-input__inner{width:180px}.xly-dialog-normal__group .td-input .td-input__label{display:none}.xly-dialog-normal__group .xly-dialog-normal__label{max-width:calc(100% - 220px)!important}.xly-dialog-normal__text{position:absolute;right:14px;line-height:40px;color:var(--color-secondary)}body .xly-dialog-normal .xly-icon-type{margin-left:15px}.xly-dialog-normal__label{max-width:100%}body .xly-dialog-normal .td-select{margin-left:7px}body .xly-dialog-normal .td-select-group{width:220px}body .xly-dialog-normal .td-input__inner{padding:6px 10px}body .xly-dialog-back{width:450px}body .xly-dialog-back .td-dialog__footer{margin-top:12px}body .xly-dialog-back .xly-dialog-resource__name{width:290px}body .xly-dialog-acquire{padding:0 20px 20px;width:500px;height:150px}body .xly-dialog-acquire .td-dialog__footer{margin-top:14px}.xly-guide-tips{position:absolute;display:flex;align-items:center;left:40px;bottom:27px;padding:0 6px 0 12px;height:30px;color:#fff;background:#ff9700;box-shadow:0 0 10px 0 rgba(26,26,26,.2);border-radius:4px}.xly-guide-tips [class^=xly-icon-]{margin-right:6px;font-size:24px}.xly-guide-tips .td-icon-close{margin-left:6px;opacity:.8;cursor:pointer}.xly-guide-tips .td-icon-close:hover{opacity:1}.xly-guide-tips:after{position:absolute;top:100%;left:10px;width:12px;height:6px;background:#ff9700;-webkit-clip-path:polygon(0 0,100% 0,0 100%);clip-path:polygon(0 0,100% 0,0 100%);content:""}.xly-guide-tag{position:absolute;bottom:0;left:82px;width:calc(100% - 83px);cursor:default;padding:4px 0}.xly-guide-tag span{display:inline-flex;align-items:center;padding:0 8px;height:22px;color:#f99300;background:#fff2ce;border-radius:2px;cursor:pointer}.xly-guide-tag span:before{top:11px;left:-8px;position:absolute;width:0;height:0;font-size:0;overflow:hidden;border-color:transparent #fff2ce transparent transparent;border-style:dashed;border-width:3px 4px;content:""}.xly-guide-tag.is-login{width:calc(100% - 205px)}',""]),e.exports=t},1455:function(e,t,i){"use strict";var o=i(1381);i.n(o).a},1456:function(e,t,i){(t=i(89)(!1)).push([e.i,".td-dialog__header{-webkit-app-region:drag}",""]),e.exports=t},1457:function(e,t,i){(t=i(89)(!1)).push([e.i,'body .xly-dialog-verify{width:450px;padding:0 18px 18px}body .xly-dialog-verify.is-safe{--color-primary:#cc8542;--button-default-color:#7d523a;--button-default-fill:#ffdc8f;--button-default-fill-hover:#fbd581;--color-item-active:rgba(204,133,66,0.1)}body .xly-dialog-verify .td-tooltip-wrapper{width:100%}body .xly-dialog-verify .td-media__content p{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}body .xly-dialog-verify h2{text-align:left}body .xly-dialog-verify .xly-icon-eye{position:absolute;right:12px;top:50%;margin-top:-8px;line-height:1;color:#ccc;cursor:pointer}body .xly-dialog-verify .xly-icon-eye:hover{color:var(--color-primary)}body .xly-dialog-verify .xly-icon-eye.is-active:before{content:"\\e79d"}.xly-dialog-verify__item{position:relative;display:flex;margin-bottom:12px}.xly-dialog-verify__item .td-input{display:flex;justify-content:space-between;align-items:center;width:100%}.xly-dialog-verify__item .td-input__label{margin-right:0}.xly-dialog-verify__item .td-input__inner{flex:1;margin-left:6px}.xly-dialog-verify__item .td-tooltip.is-top{left:48px}.xly-dialog-verify__item--simple .td-input__inner{margin-left:0}.xly-dialog-verify__item--simple .td-tooltip.is-top{left:0}.xly-dialog-verify__tips{margin-bottom:24px}.xly-dialog-verify__code{display:flex;align-items:center;position:absolute;top:2px;right:12px;line-height:26px;color:var(--color-primary);cursor:pointer}.xly-dialog-verify__code .xly-line{margin-right:12px}.xly-dialog-verify__code.is-disabled{color:var(--color-secondary);cursor:default}.xly-dialog-verify__verify .td-input,.xly-dialog-verify__verify .td-input__inner{width:100%}.xly-dialog-verify__progress{margin-top:18px;--color-primary:#3f85ff;--progress-height:4px}.xly-dialog-verify__progress p{display:flex;justify-content:space-between;margin-bottom:6px;color:var(--color-secondary)}.xly-dialog-verify__progress.is-fail .td-progress-bar__inner{background:var(--color-danger)}.xly-dialog-verify__title{font-size:13px}.xly-dialog-verify__detail{color:var(--color-secondary)}',""]),e.exports=t},1462:function(e,t,i){"use strict";var o=i(1385);i.n(o).a},1463:function(e,t,i){(t=i(89)(!1)).push([e.i,".td-dialog__header{-webkit-app-region:drag}",""]),e.exports=t},1464:function(e,t,i){"use strict";var o=i(1386);i.n(o).a},1465:function(e,t,i){(t=i(89)(!1)).push([e.i,".td-dialog__header{-webkit-app-region:drag;flex-shrink:0}.td-dialog__footer{flex-shrink:0}.td-dialog{overflow:hidden}body .xly-unzip .td-dialog__body{flex-shrink:1}",""]),e.exports=t},1466:function(e,t,i){"use strict";var o=i(1387);i.n(o).a},1467:function(e,t,i){(t=i(89)(!1)).push([e.i,'body .xly-unzip{display:flex;flex-direction:column;width:100%;height:100%}body .xly-unzip .td-dropdown-menu__item{padding:0 12px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;word-break:break-all}body .xly-unzip .xly-dialog__button .td-button{width:90px}body .xly-unzip .xly-dialog-site .td-select__drop{display:none}body .xly-unzip .td-select-group{pointer-events:none}body .xly-unzip .xly-select__file{pointer-events:visible}body .xly-unzip .td-table{display:flex;flex-direction:column}body .xly-unzip .td-table .td-tree-node{margin-left:6px}body .xly-unzip .td-dialog__body{display:flex;flex-direction:column;flex:1}body .xly-unzip .td-tree-node__label{display:inline;max-width:unset;white-space:unset}body .xly-unzip .td-table__body{table-layout:fixed}body .xly-unzip .td-tree-node{overflow:hidden;display:flex;margin-left:0;width:100%;box-sizing:border-box}body .xly-unzip .td-checkbox__inner{cursor:pointer}body .xly-unzip .td-tree-node__content{display:flex;align-items:center;width:100%;height:28px;line-height:28px;cursor:pointer;overflow:hidden}body .xly-unzip .td-table__body-wrapper{flex:1;overflow-y:scroll}body .xly-unzip .td-table__body-wrapper::-webkit-scrollbar{width:0;background:transparent}body .xly-unzip .td-table__body-wrapper::-webkit-scrollbar:horizontal{height:0}body .xly-unzip .td-table__body-wrapper::-webkit-scrollbar-thumb{border-radius:2px;width:0;background:var(--color-scrollbar)}body .xly-unzip .td-table__body-wrapper::-webkit-scrollbar-thumb:hover{background:var(--color-scrollbar-hover)}body .xly-unzip .td-table__body-wrapper::-webkit-scrollbar-corner{background:transparent}body .xly-unzip .td-table__body-wrapper::-webkit-scrollbar-thumb{border-right:0}body .xly-unzip-form{display:flex;flex-direction:column;flex:1;border:1px solid var(--color-border);border-radius:4px}.xly-unzip-form__header{padding-left:12px;height:30px;border-bottom:1px solid var(--color-border)}.xly-unzip-form__header .td-icon-arrow-drop:before{font-family:"thunder-icon"!important;content:"\\e75f"}.xly-unzip-form__header .td-breadcrumb{height:30px}.xly-unzip-form__header .td-breadcrumb__text a:hover{color:var(--color-primary)}.xly-unzip-form__body{flex:1}.xly-unzip-form__body .td-table{height:100%;border:unset}.xly-unzip-form__body .td-table tr th{color:var(--color-secondary)}.xly-unzip-form__body .td-table tr th:first-child .td-table__text{padding:0 6px}.xly-unzip-form__body .td-table tr th:nth-child(2){position:relative}.xly-unzip-form__body .td-table tr th:nth-child(2):before{position:absolute;top:8px;width:1px;height:10px;background:var(--color-border);content:""}.xly-unzip-form__body .td-table tr td:nth-child(2){color:var(--color-secondary)}.xly-unzip-form__body .td-table-tree .td-table__footer .td-checkbox{margin-left:16px}.xly-unzip-form__body .td-table__body tr:hover{background:var(--color-item-hover)}.xly-unzip-form__body .td-tree-node__image-icon{margin:0;vertical-align:-6px}.xly-unzip-form__body .td-table__body-wrapper{height:196px}.xly-unzip-form__body .td-table__footer-wrapper{background:unset;border-top:unset}.xly-unzip-form__body .td-table__footer{height:30px;color:var(--color-secondary)}.xly-unzip-form__body .td-table__footer .td-checkbox__label{margin-left:10px}',""]),e.exports=t},1506:function(e,t,i){"use strict";i.r(t);i(10),i(11),i(32),i(12),i(27);var o=i(9),r=i(38),n=(i(20),i(7),i(471),i(33)),a=i(40),l=i(70),s=i(71),c=i(37),d=(i(4),i(29)),p=i(5),u=i(168),y=i(6),f=i(22),h=i(19),x=i(93),b=i(1400),g=i(18),_=i(1330),v=i.n(_),m=i(82),w=i.n(m),k=(i(62),i(45),i(30),i(61),i(105),i(15)),z=i(1);function j(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return O(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return O(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,a=!0,l=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return a=e.done,e},e:function(e){l=!0,n=e},f:function(){try{a||null==i.return||i.return()}finally{if(l)throw n}}}}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,o=new Array(t);i<t;i++)o[i]=e[i];return o}var R=function(e,t,i,o){return new(i||(i=Promise))((function(r,n){function a(e){try{s(o.next(e))}catch(e){n(e)}}function l(e){try{s(o.throw(e))}catch(e){n(e)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,l)}s((o=o.apply(e,t||[])).next())}))};function P(e){var t=e.path,i=void 0===t?"":t,o=e.file_id,r=e.gcid,n=e.password,a=void 0===n?"":n,l=e.file_space,s=void 0===l?"":l;return Object(k.a)("decompress/v1/list",{path:i,file_id:o,gcid:r,password:a,file_space:s},{method:"POST",service:"shoulei"})}function S(e,t,i){return{type:"branch",name:e.filename,children:t,fullPath:e.path.slice(0,-1),key:e.filename,parent:i,size:0}}function C(e,t){return Object.assign({type:"leaf",fullPath:e.path,name:e.filename,size:e.filesize,formatSize:Object(z.formatSize2)(e.filesize),suffix:Object(z.getFileExtension)(e.filename).slice(1),key:e.path,parent:t,default:!0},e)}function E(e){var t=e.path,i=void 0===t?"":t,o=e.file_id,n=e.gcid,a=e.password,l=void 0===a?"":a,s=e.file_space,c=void 0===s?"":s,d=e.parent,p=void 0===d?{type:"branch",name:"",children:[],fullPath:"",key:"",parent:null,size:0}:d;return R(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t,a,s,d,u,y,f,h,x,b,g;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,P({path:i,file_id:o,gcid:n,password:l,file_space:c});case 2:if(t=e.sent,a=[],"OK"===t.status){e.next=6;break}return e.abrupt("return",t);case 6:s={},d=j(t.files),e.prev=8,d.s();case 10:if((u=d.n()).done){e.next=33;break}if("drive#folder"!==(y=u.value).kind){e.next=26;break}return f=S(y,[],p),e.next=16,E({path:y.path,file_id:o,gcid:n,password:l,file_space:c,parent:f});case 16:h=e.sent,x=Object(r.a)(h,2),x[0],b=x[1],p.size+=Number(f.size),p.formatSize=Object(z.formatSize2)(p.size),s=Object.assign(Object.assign({},s),b),a.push(f),e.next=31;break;case 26:g=C(y,p),s[g.fullPath]=g,p.size+=Number(g.size),p.formatSize=Object(z.formatSize2)(p.size),a.push(g);case 31:e.next=10;break;case 33:e.next=38;break;case 35:e.prev=35,e.t0=e.catch(8),d.e(e.t0);case 38:return e.prev=38,d.f(),e.finish(38);case 41:return p.children=a,e.abrupt("return",[a,s]);case 43:case"end":return e.stop()}}),e,null,[[8,35,38,41]])})))}function T(e){var t=e.gcid,i=e.file_id,o=e.password,r=void 0===o?"":o,n=e.default_parent,a=void 0!==n&&n,l=e.parent_id,s=e.files,c=void 0===s?[]:s,d=e.parent_full_path,p=e.file_space,u=e.parent_space,y=void 0===u?"":u;return Object(k.a)("decompress/v1/decompress",{gcid:t,file_id:i,password:r,default_parent:a,parent_id:l,files:c,parent_full_path:d,file_space:p,parent_space:y},{method:"POST",service:"shoulei"})}var $=i(26);function D(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,o=Object(c.a)(e);if(t){var r=Object(c.a)(this).constructor;i=Reflect.construct(o,arguments,r)}else i=o.apply(this,arguments);return Object(s.a)(this,i)}}var I=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===("undefined"==typeof Reflect?"undefined":Object(d.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var l=e.length-1;l>=0;l--)(r=e[l])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a},V=function(e,t,i,o){return new(i||(i=Promise))((function(r,n){function a(e){try{s(o.next(e))}catch(e){n(e)}}function l(e){try{s(o.throw(e))}catch(e){n(e)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,l)}s((o=o.apply(e,t||[])).next())}))},L=function(e){Object(l.a)(i,e);var t=D(i);function i(){var e;return Object(n.a)(this,i),(e=t.apply(this,arguments)).disabled=!0,e.tooltipShow=!1,e.isFail=!1,e.show=!0,e}return Object(a.a)(i,[{key:"close",value:function(){return V(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Object($.statVipEvent)("online_decompress_parse_popup_click",{zip_gcid:this.gcid,type:this.isFail?"fail":"parsing",click_id:"close"}),e.next=3,w.a.getCurrentWindow().__resolve();case 3:e.sent.close();case 5:case"end":return e.stop()}}),e,this)})))}},{key:"parse",value:function(){return V(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t,i,o,n,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.isFail=!1,e.prev=1,e.next=4,E({file_id:this.file_id,gcid:this.gcid,password:this.password,parent:{type:"branch",name:"",children:[],fullPath:"",key:"",parent:null,size:0}});case 4:t=e.sent,i=t.status,Array.isArray(t)?(o=Object(r.a)(t,2),n=o[0],a=o[1],this.outPromise.resolve([n,a]),this.show=!1,this.$destroy()):"PASS_WORD_EMPTY"===i?this.outPromise.reject("parse_password"):"PASS_WORD_ERROR"===i?this.outPromise.reject("parse_password_error"):(this.isFail=!0,Object($.statVipEvent)("online_decompress_parse_popup_show",{zip_gcid:this.gcid,type:"fail",zip_size:this.file_size})),e.next=14;break;case 9:e.prev=9,e.t0=e.catch(1),console.log(e.t0),this.isFail=!0,Object($.statVipEvent)("online_decompress_parse_popup_show",{zip_gcid:this.gcid,type:"fail",zip_size:this.file_size});case 14:case"end":return e.stop()}}),e,this,[[1,9]])})))}},{key:"mounted",value:function(){console.log(this.file_id,this.gcid,"解析文件"),this.parse(),Object($.statVipEvent)("online_decompress_parse_popup_show",{zip_gcid:this.gcid,type:this.isFail?"fail":"parsing",zip_size:this.file_size})}},{key:"confirm",value:function(){return V(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w.a.getCurrentWindow().__resolve();case 2:e.sent,this.close();case 4:case"end":return e.stop()}}),e,this)})))}},{key:"retry",value:function(){Object($.statVipEvent)("online_decompress_parse_popup_click",{zip_gcid:this.gcid,type:this.isFail?"fail":"parsing",click_id:"retry"}),this.parse()}}]),i}(g.Vue);I([Object(g.Prop)()],L.prototype,"file_id",void 0),I([Object(g.Prop)()],L.prototype,"gcid",void 0),I([Object(g.Prop)()],L.prototype,"file_size",void 0),I([Object(g.Prop)()],L.prototype,"password",void 0),I([Object(g.Prop)()],L.prototype,"outPromise",void 0);var W=L=I([Object(g.Component)({data:function(){return{img:v.a}}})],L),F=(i(1450),i(1328),i(279),i(685),i(1338),i(1347),i(1453),i(72)),A=Object(F.a)(W,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.show?i("td-dialog",{attrs:{"custom-class":"xly-dialog-task xly-dialog-acquire",visible:!0},on:{close:e.close}},[i("div",{staticClass:"xly-dialog-resource"},[i("i",{staticClass:"xly-icon-type xly-type-rar"}),e._v(" "),i("div",{staticClass:"xly-dialog-resource__name"},[i("p",{staticClass:"xly-dialog-resource__text",class:{"is-fail":e.isFail}},[e._v("\n        "+e._s(e.isFail?"解析压缩包失败":"正在云解析压缩包...")+"\n      ")])])]),e._v(" "),i("div",{staticClass:"td-loading",class:{"is-stop":e.isFail}},[i("div",{staticClass:"td-loading-bar"},[i("div",{staticClass:"td-loading-bar__inner"})])]),e._v(" "),i("div",{staticClass:"xly-dialog__footer",attrs:{slot:"footer"},slot:"footer"},[i("div",{staticClass:"xly-dialog__button"},[i("td-button",{staticClass:"td-button--other",attrs:{disabled:!e.isFail},on:{click:e.retry}},[e._v("\n        重试\n      ")])],1)])]):e._e()}),[],!1,null,null,null).exports;function M(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,o=Object(c.a)(e);if(t){var r=Object(c.a)(this).constructor;i=Reflect.construct(o,arguments,r)}else i=o.apply(this,arguments);return Object(s.a)(this,i)}}var N=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===("undefined"==typeof Reflect?"undefined":Object(d.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var l=e.length-1;l>=0;l--)(r=e[l])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a},K=function(e,t,i,o){return new(i||(i=Promise))((function(r,n){function a(e){try{s(o.next(e))}catch(e){n(e)}}function l(e){try{s(o.throw(e))}catch(e){n(e)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,l)}s((o=o.apply(e,t||[])).next())}))},U=function(e){Object(l.a)(i,e);var t=M(i);function i(){var e;return Object(n.a)(this,i),(e=t.apply(this,arguments)).password="",e.isShowPassword=!1,e.show=!0,e}return Object(a.a)(i,[{key:"handlePassword",value:function(){this.isShowPassword=!this.isShowPassword}},{key:"close",value:function(e){return K(this,void 0,void 0,regeneratorRuntime.mark((function t(){var i;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.stat(e),t.next=3,w.a.getCurrentWindow().__resolve();case 3:i=t.sent,this.outPromise.reject("close"),i.close();case 6:case"end":return t.stop()}}),t,this)})))}},{key:"confirm",value:function(){return K(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.stat("confirm"),this.outPromise.resolve(this.password),this.show=!1,this.$destroy();case 4:case"end":return e.stop()}}),e,this)})))}},{key:"stat",value:function(e){Object($.statVipEvent)("online_decompress_pwd_popup_click",{zip_gcid:this.gcid,zip_size:this.file_size,click_id:e})}},{key:"mounted",value:function(){}}]),i}(g.Vue);N([Object(g.Prop)({type:Number})],U.prototype,"size",void 0),N([Object(g.Prop)()],U.prototype,"outPromise",void 0),N([Object(g.Prop)()],U.prototype,"gcid",void 0),N([Object(g.Prop)()],U.prototype,"file_size",void 0),N([Object(g.Prop)()],U.prototype,"iserror",void 0);var q=U=N([Object(g.Component)({data:function(){return{img:v.a}}})],U),B=(i(1455),i(1405),Object(F.a)(q,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.show?i("td-dialog",{attrs:{"custom-class":"xly-dialog-verify",visible:!0},on:{close:function(t){return e.close("close")}}},[i("h2",{attrs:{slot:"header"},slot:"header"},[e._v("\n    该文件已加密，请输入密码后查看\n  ")]),e._v(" "),i("div",{staticClass:"xly-dialog-verify__form"},[i("ul",[i("li",{staticClass:"xly-dialog-verify__item xly-dialog-verify__item--simple"},[i("td-tooltip",{attrs:{content:"密码错误",placement:"top",visible:e.iserror}},[i("td-input",{attrs:{type:e.isShowPassword?"text":"password",placeholder:"请输入密码"},on:{focus:function(t){e.iserror=!1}},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}})],1),e._v(" "),i("i",{staticClass:"xly-icon-eye",class:{"is-active":e.isShowPassword},on:{click:e.handlePassword}})],1)])]),e._v(" "),i("div",{staticClass:"xly-dialog__footer",attrs:{slot:"footer"},slot:"footer"},[i("div",{staticClass:"xly-dialog__button"},[i("td-button",{on:{click:e.confirm}},[e._v("\n        确定\n      ")]),e._v(" "),i("td-button",{staticClass:"td-button--other",on:{click:function(t){return e.close("cancel")}}},[e._v("\n        取消\n      ")])],1)])]):e._e()}),[],!1,null,null,null).exports),G=i(1410),H=i(2),J=i(3),X=i(284),Y=i(283);function Q(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,o=Object(c.a)(e);if(t){var r=Object(c.a)(this).constructor;i=Reflect.construct(o,arguments,r)}else i=o.apply(this,arguments);return Object(s.a)(this,i)}}var Z=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===("undefined"==typeof Reflect?"undefined":Object(d.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var l=e.length-1;l>=0;l--)(r=e[l])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a},ee=function(e,t,i,o){return new(i||(i=Promise))((function(r,n){function a(e){try{s(o.next(e))}catch(e){n(e)}}function l(e){try{s(o.throw(e))}catch(e){n(e)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,l)}s((o=o.apply(e,t||[])).next())}))},te=function(e){Object(l.a)(i,e);var t=Q(i);function i(){var e;return Object(n.a)(this,i),(e=t.apply(this,arguments)).progress=0,e.name="",e.unzipPath="",e.completeSize="",e.totalSize="",e.file_id="",e.isminimize=!1,e.win=null,e.titleMap=Object.freeze({running:"在线解压中",outOfSpace:"由于空间不足，解压失败",otherError:"解压失败，请稍后重试",finish:"解压完成"}),e.phase="running",e}return Object(a.a)(i,[{key:"getProgress",value:function(){return ee(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t,i=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,z.log.info("Process","获取进度",this.taskId),e.next=4,o={taskId:this.taskId},r=void 0,r=o.taskId,Object(k.a)("decompress/v1/progress",{task_id:r},{method:"GET",service:"shoulei"});case 4:if((t=e.sent).phase!==Y.Phase.ERROR){e.next=9;break}return z.log.info("Process","失败",t),"file_space_not_enough"===t.params.error_detail?(this.stat("fail-lack","","online_decompress_popup_show"),this.phase="outOfSpace"):(this.stat("fail-other","","online_decompress_popup_show"),this.phase="otherError"),e.abrupt("return");case 9:if(z.log.info("Process","解压进度",t),this.progress=t.progress,this.completeSize=t.task_size_completed,this.totalSize=t.task_size,this.name=t.file_name,this.unzipPath=t.decompress_path.join("\\"),this.file_id=t.file_id,100!==Number(this.progress)){e.next=23;break}return this.phase="finish",this.stat("complete","","online_decompress_popup_show"),clearTimeout(this.timer),this.outPromise.resolve(t),this.isminimize&&this.win.flashFrame(!0),e.abrupt("return");case 23:e.next=31;break;case 25:return e.prev=25,e.t0=e.catch(0),this.phase="otherError",z.log.error("Process","获取进度异常",e.t0),this.stat("fail-other","","online_decompress_popup_show"),e.abrupt("return");case 31:this.timer=setTimeout((function(){i.getProgress()}),1e3);case 32:case"end":return e.stop()}var o,r}),e,this,[[0,25]])})))}},{key:"focus",value:function(){this.isminimize=!1}},{key:"close",value:function(){return ee(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t,i,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w.a.getCurrentWindow().__resolve();case 2:t=e.sent,(i=t.webContents.browserWindowOptions.customProperties).reject&&i.reject(new Error("close")),o={running:"decompressing",finish:"complete",outOfSpace:"fail_lack",otherError:"fail-other"}[this.phase],this.stat(o,"close"),t.close();case 8:case"end":return e.stop()}}),e,this)})))}},{key:"confirm",value:function(){return ee(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t={finish:"complete",outOfSpace:"fail_lack",otherError:"fail-other"}[this.phase],this.stat(t,"confirm"),this.outPromise.resolve(),this.close();case 4:case"end":return e.stop()}}),e,this)})))}},{key:"view",value:function(){this.stat("complete","view"),Object(H.callServer)("BringMainWndToTop"),Object(H.callRemote)(J.d,"IpcRedirectCurrentDirectoryById",{id:this.file_id,needSync:!0}),this.close()}},{key:"stat",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"online_decompress_popup_click",o={zip_gcid:this.gcid,zip_size:this.file_size,type:e,click_id:t};t||delete o.click_id,Object($.statVipEvent)(i,o)}},{key:"minimize",value:function(){this.isminimize=!0,this.$bus.$emit("minimize"),this.stat("decompressing","minimize")}},{key:"created",value:function(){var e,t;return ee(this,void 0,void 0,regeneratorRuntime.mark((function i(){return regeneratorRuntime.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return this.stat("decompressing","","online_decompress_popup_show"),this.getProgress(),i.next=4,w.a.getCurrentWindow().__resolve();case 4:this.win=i.sent,this.win.on("focus",this.focus),this.name=null===(e=this.$options.propsData)||void 0===e?void 0:e.name,this.unzipPath=null===(t=this.$options.propsData)||void 0===t?void 0:t.unzipPath;case 8:case"end":return i.stop()}}),i,this)})))}},{key:"destroyed",value:function(){this.win.off("focus",this.focus)}},{key:"title",get:function(){return this.titleMap[this.phase]}}]),i}(g.Vue);Z([Object(g.Prop)({type:Number})],te.prototype,"size",void 0),Z([Object(g.Prop)()],te.prototype,"outPromise",void 0),Z([Object(g.Prop)()],te.prototype,"taskId",void 0),Z([Object(g.Prop)()],te.prototype,"file_size",void 0),Z([Object(g.Prop)()],te.prototype,"gcid",void 0),Z([X.Bind],te.prototype,"focus",null);var ie=te=Z([Object(g.Component)({data:function(){return{img:v.a}},methods:{formatSize2:z.formatSize2}})],te),oe=(i(1462),Object(F.a)(ie,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("td-dialog",{attrs:{"custom-class":"xly-dialog-verify",visible:!0},on:{close:e.close}},[i("h2",{attrs:{slot:"header"},slot:"header"},[e._v("\n    "+e._s(e.title)+"\n  ")]),e._v(" "),i("td-media",{attrs:{align:"middle"}},[i("i",{staticClass:"xly-icon-type xly-type-rar",attrs:{slot:"media"},slot:"media"}),e._v(" "),i("p",{staticClass:"xly-dialog-verify__title"},[e._v("\n      "+e._s(e.name)+"\n    ")]),e._v(" "),i("p",{staticClass:"xly-dialog-verify__detail"},[e._v("\n      解压到："+e._s(e.unzipPath)+"\n    ")])]),e._v(" "),i("div",{staticClass:"xly-dialog-verify__progress",class:{"is-fail":["otherError","outOfSpace"].includes(e.phase)}},[i("p",{staticClass:"xly-dialog-verify__progress-text"},[i("span",[e._v("解压进度 "+e._s(e.progress)+"%")]),e._v(" "),i("span",[e._v(e._s(e.formatSize2(e.completeSize))+"/"+e._s(e.formatSize2(e.totalSize)))])]),e._v(" "),i("td-progress",{attrs:{value:e.progress+1}})],1),e._v(" "),i("div",{staticClass:"xly-dialog__footer",attrs:{slot:"footer"},slot:"footer"},[["running","finish"].includes(e.phase)?i("div",{staticClass:"xly-dialog__button"},[100!==Number(e.progress)?i("td-button",{on:{click:e.minimize}},[e._v("\n        最小化窗口\n      ")]):[i("td-button",{on:{click:e.view}},[e._v("\n          查看\n        ")]),e._v(" "),i("td-button",{staticClass:"td-button--other",on:{click:e.confirm}},[e._v("\n          确定\n        ")])]],2):i("div",{staticClass:"xly-dialog__button"},[i("td-button",{staticClass:"td-button--other",on:{click:e.confirm}},[e._v("\n        确定\n      ")])],1)])],1)}),[],!1,null,null,null).exports),re=(i(196),i(54),i(58)),ne=i(281),ae=i(475);function le(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return se(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return se(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,a=!0,l=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return a=e.done,e},e:function(e){l=!0,n=e},f:function(){try{a||null==i.return||i.return()}finally{if(l)throw n}}}}function se(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,o=new Array(t);i<t;i++)o[i]=e[i];return o}function ce(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,o=Object(c.a)(e);if(t){var r=Object(c.a)(this).constructor;i=Reflect.construct(o,arguments,r)}else i=o.apply(this,arguments);return Object(s.a)(this,i)}}var de=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===("undefined"==typeof Reflect?"undefined":Object(d.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var l=e.length-1;l>=0;l--)(r=e[l])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a},pe=function(e,t,i,o){return new(i||(i=Promise))((function(r,n){function a(e){try{s(o.next(e))}catch(e){n(e)}}function l(e){try{s(o.throw(e))}catch(e){n(e)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,l)}s((o=o.apply(e,t||[])).next())}))},ue=function(e){Object(l.a)(i,e);var t=ce(i);function i(){var e;return Object(n.a)(this,i),(e=t.apply(this,arguments)).columns=Object.freeze([{label:"文件名称",prop:"name",sortable:!0},{label:"大小",prop:"formatSize",width:100,sortable:!0}]),e.show=!0,e.tableHeight=120,e.level=0,e.stack=e.filesTree.length>0?[e.filesTree]:[],e.selctedUnipPath=[],e.checkedKeys=e.defaultCheckedKeys,e.disabledKeys=[],e.defaultExpandedKeys=[],e.zipRouteList=[{title:e.fileName,id:e.fileName}],e}return Object(a.a)(i,[{key:"WatchFileTree",value:function(e){this.stack=[e]}},{key:"mounted",value:function(){return pe(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w.a.getCurrentWindow().__resolve();case 2:t=e.sent,this.win=t,window.addEventListener("resize",this.resize),Object($.statVipEvent)("online_decompress_file_window_show",{zip_gcid:this.gcid,zip_size:this.file_size,path:"root"});case 6:case"end":return e.stop()}}),e,this)})))}},{key:"resize",value:function(){var e=this.$refs["table-container"];if(e){var t=e.getBoundingClientRect();this.tableHeight=t.height-55}}},{key:"close",value:function(){return pe(this,void 0,void 0,regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Object($.statVipEvent)("online_decompress_file_window_click",{zip_gcid:this.gcid,zip_size:this.file_size,click_id:"close",path:this.stack.length>1?"branch":"root"}),e.next=3,w.a.getCurrentWindow().__resolve();case 3:e.sent.close();case 5:case"end":return e.stop()}}),e,this)})))}},{key:"destroy",value:function(){this.show=!1,window.removeEventListener("resize",this.resize),this.$destroy()}},{key:"confirm",value:function(){return pe(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t,i,o,r,n,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Object($.statVipEvent)("online_decompress_file_window_click",{zip_gcid:this.gcid,zip_size:this.file_size,click_id:"decompress",path:this.stack.length>1?"branch":"root"}),e.prev=1,this.selctedUnipPath.length>0?(i=this.selctedUnipPath.slice(-1)[0].id,t=this.selctedUnipPath.map((function(e){return e.title})).slice(1)):(i=this.driveRouteList.slice(-1)[0].id,t=this.driveRouteList.map((function(e){return e.title})).slice(1)),o=this.$refs.tab,console.log(o.allChecked,"是否全部选中"),0===o.selectLeafCount&&this.outPromise.reject("您未选中任何文件"),!o.allChecked){e.next=12;break}return e.next=9,T({gcid:this.gcid,file_id:this.file_id,password:this.password,parent_id:i,parent_full_path:t}).catch((function(e){throw console.log(e),new Error("网络异常, 解压文件失败")}));case 9:r=e.sent,e.next=21;break;case 12:if(!((n=this.genSelectFileInfos()).length>500)){e.next=18;break}return this.$message.warning("当前目录自定义解压文件数过多，请返回根目录全部解压"),e.abrupt("return");case 18:return e.next=20,T({gcid:this.gcid,file_id:this.file_id,password:this.password,parent_id:i,parent_full_path:t,files:n});case 20:r=e.sent;case 21:if(console.log(r),"OK"===r.status){e.next=27;break}return"RUNNING_TASK"===r.status&&Object($.statVipEvent)("online_decompress_toast_show",{type:"decompressing"}),l=r.status,a={PASS_WORD_ERROR:"解压密码错误",RUNNING_TASK:"当前正在进行该压缩包的解压任务，请稍后重试",EXPIRED:"已过期",DELETED:"已删除",INVALID_FILE_FORMAT:"非法压缩包",FILE_LIST_TIMEOUT:"大文件超时",NOT_FOUND:"未找到",AUDITING:"审核中",SENSITIVE_WORD:"文件名包含敏感词",SENSITIVE_RESOURCE:"包含敏感资源"}[l]||"无法查询压缩文件列表",this.$message.warning(a),e.abrupt("return");case 27:this.outPromise.resolve(Object.assign(r,{unzipPath:this.unzipPath})),this.destroy(),e.next=35;break;case 31:e.prev=31,e.t0=e.catch(1),this.$message.error("网络异常, 解压文件失败"),this.outPromise.reject(new Error("网络异常, 解压文件失败"));case 35:case"end":return e.stop()}var l}),e,this,[[1,31]])})))}},{key:"handleRoutelistChange",value:function(e,t){z.log.info(e,t),this.zipRouteList=e,this.stack=this.stack.slice(0,e.length),this.level=this.stack.length-1,Object($.statVipEvent)("online_decompress_file_window_click",{zip_gcid:this.gcid,zip_size:this.file_size,click_id:"path",path:this.stack.length>1?"branch":"root"})}},{key:"dbclickItem",value:function(e){var t;(console.log(e),e._children)&&(this.zipRouteList=this.zipRouteList.concat(e._path.map((function(e){return{title:e.key,id:e.key}}))),(t=this.stack).push.apply(t,Object(o.a)(e._path.map((function(e){return e._row.children})))),this.level=this.stack.length-1,Object($.statVipEvent)("online_decompress_file_window_click",{zip_gcid:this.gcid,zip_size:this.file_size,click_id:"file",path:this.stack.length>1?"branch":"root"}))}},{key:"clickItem",value:function(e){var t;null===(t=this.$refs.tab)||void 0===t||t.check(e)}},{key:"openSelectPath",value:function(){return pe(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Object($.statVipEvent)("online_decompress_file_window_click",{zip_gcid:this.gcid,zip_size:this.file_size,click_id:"change_path",path:this.stack.length>1?"branch":"root"}),e.prev=1,e.next=4,Object(re.createTreePathBrowserWindow)(this.win.id,{},{title:"选择解压文件保存路径"});case 4:(t=e.sent).error||(this.selctedUnipPath=t.path),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),z.log.error(e.t0),this.$message.error("选择保存路径失败");case 12:case"end":return e.stop()}}),e,this,[[1,8]])})))}},{key:"getTaskIcon",value:function(e){return"branch"===e.type?"xly-type-group":"xly-type-".concat(ne.default[Object(z.getFileExtension)(e.key).slice(1)])}},{key:"updateCheckedKeys",value:function(e,t,i){console.log(e,t,i),this.checkedKeys=e}},{key:"genSelectFileInfos",value:function(){for(var e,t=this.$refs.tab.status,i=Object.assign({},t),o=0,r=Object.keys(i);o<r.length;o++){var n=i[r[o]];if(n)if(n.checked){var a,l=le(null!==(e=n.row._childrenRows)&&void 0!==e?e:[]);try{for(l.s();!(a=l.n()).done;){delete i[a.value.key]}}catch(e){l.e(e)}finally{l.f()}}else delete i[n.row.key]}console.log(Object.keys(i));for(var s=[],c=0,d=Object.keys(i);c<d.length;c++){var p=i[d[c]].row;p.children?s.push({kind:"drive#folder",path:p._row.fullPath+"/"}):s.push({index:p._row.index,kind:"drive#file",path:p._row.fullPath+"/"})}return s}},{key:"unzipPath",get:function(){return this.selctedUnipPath.length>0?this.selctedUnipPath.map((function(e){return e.title.length>8?"".concat(e.title.slice(0,6),"..."):e.title})).join("\\"):this.driveRouteList.map((function(e){return e.title.length>8?"".concat(e.title.slice(0,6),"..."):e.title})).join("\\")}},{key:"tree",get:function(){return this.stack.length>0?this.stack[this.level]:[]}},{key:"fileName",get:function(){if("string"==typeof this.name){var e=ae.a.supportUzip.join("|").replace(/\./g,"\\.");return this.name.replace(new RegExp(e,"g"),"")}return"我的云盘"}}]),i}(g.Vue);de([Object(g.Prop)()],ue.prototype,"filesTree",void 0),de([Object(g.Prop)()],ue.prototype,"driveRouteList",void 0),de([Object(g.Prop)()],ue.prototype,"file_id",void 0),de([Object(g.Prop)()],ue.prototype,"gcid",void 0),de([Object(g.Prop)()],ue.prototype,"file_size",void 0),de([Object(g.Prop)()],ue.prototype,"password",void 0),de([Object(g.Prop)()],ue.prototype,"name",void 0),de([Object(g.Prop)()],ue.prototype,"outPromise",void 0),de([Object(g.Prop)()],ue.prototype,"defaultCheckedKeys",void 0),de([Object(g.Watch)("filesTree"),X.Once],ue.prototype,"WatchFileTree",null),de([X.Bind],ue.prototype,"resize",null);var ye=ue=de([Object(g.Component)({data:function(){return{img:v.a}}})],ue),fe=(i(1464),i(1466),Object(F.a)(ye,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.show?i("td-dialog",{attrs:{"custom-class":"xly-unzip",visible:!0},on:{close:e.close}},[i("h2",{attrs:{slot:"header"},slot:"header"},[e._v("\n    在线解压\n  ")]),e._v(" "),i("div",{staticClass:"xly-unzip-form"},[i("div",{staticClass:"xly-unzip-form__header"},[i("td-breadcrumb",{attrs:{"init-route-list":e.zipRouteList},on:{onRoutelistChange:e.handleRoutelistChange}})],1),e._v(" "),i("div",{ref:"table-container",staticClass:"xly-unzip-form__body"},[[i("td-table",{ref:"tab",attrs:{columns:e.columns,data:e.tree,"default-checked-keys":e.defaultCheckedKeys,"default-expanded-keys":e.defaultExpandedKeys,"disabled-keys":e.disabledKeys,"row-height":30,height:e.tableHeight,checkable:"","footer-enabled":"","tree-enabled":""},on:{"checked-change":e.updateCheckedKeys,"dbclick-label":e.dbclickItem,"click-label":e.clickItem},scopedSlots:e._u([{key:"footer-checkbox",fn:function(t){var i=t.selectLeafCount;return[e._v("\n            "+e._s(i>0?"已选中"+i+"个文件":"全选")+"\n          ")]}},{key:"icon",fn:function(t){t.prop,t.value;var o=t.row;return[i("i",{staticClass:"xly-icon-type is-small",class:e.getTaskIcon(o)})]}}],null,!1,1826628935)})]],2)]),e._v(" "),i("div",{staticClass:"xly-dialog-site"},[i("p",{staticClass:"xly-dialog-site__title"},[e._v("\n      解压到：\n    ")]),e._v(" "),i("td-select",{attrs:{placeholder:"D:\\迅雷下载\\我的电影"},model:{value:e.unzipPath,callback:function(t){e.unzipPath=t},expression:"unzipPath"}},[i("a",{staticClass:"xly-select__file",attrs:{slot:"suffix",href:"javascript:;"},on:{click:e.openSelectPath},slot:"suffix"},[i("i",{staticClass:"xly-icon-file"})])])],1),e._v(" "),i("div",{staticClass:"xly-dialog__footer",attrs:{slot:"footer"},slot:"footer"},[i("div",{staticClass:"xly-dialog__button"},[i("td-button",{attrs:{disabled:0===e.checkedKeys.length},on:{click:e.confirm}},[e._v("\n        解压全部文件\n      ")])],1)])]):e._e()}),[],!1,null,null,null).exports);function he(e){p.default.mixin({$stat:h.eventTrack,$bus:f.default,$message:x.showMessage});var t,i=p.default.extend(e),o=function(e,o,r){!function(e){t=new i({store:Object(y.b)(),el:document.createElement("div"),propsData:Object.assign({},e)})}(e),t.resolve=o,t.reject=r,t.outPromise={reject:r,resolve:o},document.body.appendChild(t.$el),p.default.nextTick((function(){"function"==typeof t.show&&t.show()}))};return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Promise((function(t,i){o(e,t,i)}))}}p.default.use(u.a);he(b.a);var xe=he(fe),be=he(oe),ge=(he(G.a),he(B)),_e=he(A),ve=i(1354);function me(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,o=Object(c.a)(e);if(t){var r=Object(c.a)(this).constructor;i=Reflect.construct(o,arguments,r)}else i=o.apply(this,arguments);return Object(s.a)(this,i)}}var we=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===("undefined"==typeof Reflect?"undefined":Object(d.a)(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var l=e.length-1;l>=0;l--)(r=e[l])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a},ke=function(e,t,i,o){return new(i||(i=Promise))((function(r,n){function a(e){try{s(o.next(e))}catch(e){n(e)}}function l(e){try{s(o.throw(e))}catch(e){n(e)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,l)}s((o=o.apply(e,t||[])).next())}))},ze=function(e){Object(l.a)(i,e);var t=me(i);function i(){var e;return Object(n.a)(this,i),(e=t.apply(this,arguments)).win=null,e.winSize=Object.freeze({files:[500,424],process:[450,213],psw:[450,160],parse:[500,150]}),e.password="",e.file_id="",e.gcid="",e.file_size="",e.driveRouteList=[],e}return Object(a.a)(i,[{key:"mounted",value:function(){return ke(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w.a.getCurrentWindow().__resolve();case 2:this.win=e.sent,t=this.win.webContents.browserWindowOptions.customProperties,this.outPromise=t,this.file_id=t.file_id,this.gcid=t.gcid,this.file_size=t.file_size,this.name=t.name,this.driveRouteList=t.driveRouteList,this.begin(),this.$bus.$on("minimize",this.minimize);case 12:case"end":return e.stop()}}),e,this)})))}},{key:"destroyed",value:function(){this.$bus.$off("minimize",this.minimize)}},{key:"minimize",value:function(){this.win.minimize()}},{key:"begin",value:function(){return ke(this,void 0,void 0,regeneratorRuntime.mark((function e(){var t,i,n,a,l,s,c,d=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.reSizeWindow.apply(this,Object(o.a)(this.winSize.parse).concat([function(){return d.showParse({file_id:d.file_id,gcid:d.gcid,password:d.password,file_size:d.file_size})}]));case 3:return t=e.sent,i=Object(r.a)(t,2),n=i[0],a=i[1],this.password&&Object($.statVipEvent)("online_decompress_pwd_confirm_result",{zip_gcid:this.gcid,zip_size:this.file_size,result:1}),e.next=10,this.reSizeWindow.apply(this,Object(o.a)(this.winSize.files).concat([function(){return ke(d,void 0,void 0,regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(t=this.win).setMinimumSize.apply(t,Object(o.a)(this.winSize.files)),e.abrupt("return",this.showFiles({file_id:this.file_id,gcid:this.gcid,driveRouteList:this.driveRouteList,filesTree:n,defaultCheckedKeys:Object.keys(a)}));case 2:case"end":return e.stop()}}),e,this)})))},!0]));case 10:return l=e.sent,e.next=13,this.reSizeWindow.apply(this,Object(o.a)(this.winSize.process).concat([function(){return d.showProgress({taskId:l.task_id,name:d.name,unzipPath:l.unzipPath})}]));case 13:this.outPromise.resolve(),e.next=33;break;case 16:if(e.prev=16,e.t0=e.catch(0),"parse_password"!==e.t0){e.next=26;break}return e.next=21,this.reSizeWindow.apply(this,Object(o.a)(this.winSize.psw).concat([function(){return d.showPsw()}])).catch(console.error);case 21:s=e.sent,this.password=s,this.begin(),e.next=33;break;case 26:if("parse_password_error"!==e.t0){e.next=33;break}return Object($.statVipEvent)("online_decompress_pwd_confirm_result",{zip_gcid:this.gcid,zip_size:this.file_size,result:0,error:e.t0}),e.next=30,this.reSizeWindow.apply(this,Object(o.a)(this.winSize.psw).concat([function(){return d.showPsw(!0)}])).catch(console.error);case 30:c=e.sent,this.password=c,this.begin();case 33:case"end":return e.stop()}}),e,this,[[0,16]])})))}},{key:"showParse",value:function(e){var t=e.file_id,i=void 0===t?this.file_id:t,o=e.gcid,r=void 0===o?this.gcid:o,n=e.password,a=void 0===n?"":n,l=e.file_size,s=void 0===l?this.file_size:l;return _e({file_id:i,gcid:r,password:a,file_size:s})}},{key:"showFiles",value:function(e){var t=e.file_id,i=void 0===t?this.file_id:t,o=e.gcid,r=void 0===o?this.gcid:o,n=e.driveRouteList,a=void 0===n?this.driveRouteList:n,l=e.filesTree,s=void 0===l?[]:l,c=e.password,d=void 0===c?this.password:c,p=e.defaultCheckedKeys,u=void 0===p?[]:p,y=e.file_size,f=void 0===y?this.file_size:y,h=e.name,x=void 0===h?this.name:h;return xe({file_id:i,gcid:r,driveRouteList:a,filesTree:s,password:d,defaultCheckedKeys:u,file_size:f,name:x})}},{key:"showPsw",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return Object($.statVipEvent)("online_decompress_pwd_popup_show",{zip_gcid:this.gcid,zip_size:this.file_size}),ge({iserror:e,file_size:this.file_size,gcid:this.gcid})}},{key:"showProgress",value:function(e){var t=e.taskId,i=void 0===t?0:t,o=e.file_size,r=void 0===o?this.file_size:o,n=e.gcid,a=void 0===n?this.gcid:n,l=e.name,s=void 0===l?this.name:l,c=e.unzipPath;return be({taskId:i,file_size:r,gcid:a,name:s,unzipPath:c})}},{key:"reSizeWindow",value:function(e,t,i){var o=this,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return new Promise((function(n,a){return ke(o,void 0,void 0,regeneratorRuntime.mark((function o(){return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,this.win.hide();case 2:return o.next=4,this.win.setResizable(!0);case 4:return i().then(n,a),o.next=7,this.win.setMinimumSize(e,t);case 7:return o.next=9,this.win.setSize(e,t);case 9:if(r){o.next=12;break}return o.next=12,this.win.setResizable(!1);case 12:return o.next=14,this.win.show();case 14:case"end":return o.stop()}}),o,this)})))}))}}]),i}(g.Vue);we([X.Bind],ze.prototype,"minimize",null);var je=ze=we([Object(g.Component)({components:{UnzipParseDialog:A},mixins:[ve.a]})],ze),Oe=Object(F.a)(je,(function(){var e=this.$createElement;return(this._self._c||e)("div")}),[],!1,null,null,null);t.default=Oe.exports}}]);