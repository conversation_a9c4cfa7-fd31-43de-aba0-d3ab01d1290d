<!doctype html>
<html>
  <head>
    <title>迅雷云盘</title><meta data-n-head="1" charset="utf-8"><meta data-n-head="1" name="viewport" content="width=device-width,initial-scale=1"><meta data-n-head="1" data-hid="description" name="description" content="thunder11-plugin-pan"><link data-n-head="1" rel="icon" type="image/x-icon" href="/favicon.ico"><link rel="preload" href="./static/runtime.4923a63.js" as="script"><link rel="preload" href="./static/commons/app.46f27e5.js" as="script"><link rel="preload" href="./static/app.7253809.js" as="script">
  </head>
  <body>
    <div id="__nuxt"><div class="xly-loading">  <svg viewBox="0 0 52 52" class="xly-loading__svg"><polyline points="23.5,27.6 7.5,18.6 23.5,10.6 39.5,18.6 "/><polyline points="23.5,27.5 39.5,18.5 39.5,36.5 23.5,44.5 "/><polyline points="23.5,27.5 23.5,44.5 7.5,36.5 7.5,18.5 "/><path class="xly-path-out" d="M23.5,44.5l-16-8v-18l16-8l16,8v18L23.5,44.5z"/><polyline class="xly-path-inner-2" points="23.5,27.5 39.5,18.5 39.5,36.5 23.5,44.5 "/><polyline class="xly-path-inner-1" points="23.5,27.6 7.5,18.6 23.5,10.6 39.5,18.6 "/><polyline class="xly-path-inner-3" points="23.5,27.5 23.5,44.5 7.5,36.5 7.5,18.5 "/>  </svg></div><style>:root{--background-area:#ffffff}.is-dark{--background-area:#232526}path,polyline{fill:none;stroke-width:2;stroke:#dae1ec;stroke-linecap:round;stroke-linejoin:round}.xly-loading{position:absolute;left:0;top:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--background-area)}.xly-loading i.img-icon-loading{width:48px;height:48px;background:url(../img/icons/icon-loading.png) no-repeat;background-size:100%;animation:rotate 2s linear both infinite}@keyframes rotate{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}.xly-loading__img{display:block;width:40px;height:40px;background:url(../img/icons/xly-loading.gif) no-repeat}.xly-loading__svg{width:45px;height:45px}.xly-path-out{stroke-width:2;fill:none;stroke:#3f85ff;stroke-dasharray:0,108;stroke-dashoffset:0;animation:pathOut 1s 1s linear both infinite;transform-origin:45% 53%;transform:rotate(-180deg)}.xly-path-inner-1{stroke:#3f85ff;stroke-dasharray:55;stroke-dashoffset:55;animation:path2 2s linear both infinite}.xly-path-inner-2{stroke:#3f85ff;stroke-dasharray:55;stroke-dashoffset:55;animation:path2 2s linear both infinite}.xly-path-inner-3{stroke:#3f85ff;stroke-dasharray:55;stroke-dashoffset:55;animation:path2 linear 2s both infinite}@keyframes path2{0%{stroke-dashoffset:55}100%,70%{stroke-dashoffset:-55}}@keyframes pathOut{0%{opacity:0}1%{opacity:1;stroke-dasharray:0,108;stroke-dashoffset:0}50%{stroke-dasharray:45,108;stroke-dashoffset:0}100%{stroke-dasharray:90,108;stroke-dashoffset:-108}}</style></div><script>window.__NUXT__={config:{},staticAssetsBase:void 0}</script>
  <script src="./static/runtime.4923a63.js"></script><script src="./static/commons/app.46f27e5.js"></script><script src="./static/app.7253809.js"></script></body>
  <script>try{let e=JSON.parse(window.localStorage.getItem("pan/skin"))||"";document.body.className=e}catch(e){console.error("皮肤应用出错")}window.addEventListener("dragover",e=>{e.preventDefault()}),window.addEventListener("drop",(function(e){e.preventDefault()})),window.__pluginDir=__dirname.replace(/\\/g,"/"),window.__pluginEntry=__filename.replace(/\\/g,"/")</script>
</html>
