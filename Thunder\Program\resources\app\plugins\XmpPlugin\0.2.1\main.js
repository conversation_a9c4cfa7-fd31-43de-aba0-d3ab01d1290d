module.exports=function(e){var t={};function n(i){if(t[i])return t[i].exports;var o=t[i]={i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(i,o,function(t){return e[t]}.bind(null,o));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=11)}([function(e,t,n){"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.Critical=1]="Critical",e[e.Error=2]="Error",e[e.Warning=3]="Warning",e[e.Information=4]="Information",e[e.Verbose=5]="Verbose"}(i=t.LogLevel||(t.LogLevel={})),t.outputLoggerConsole=((o={})[i.Critical]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return console.error.apply(console,e)},o[i.Error]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return console.error.apply(console,e)},o[i.Warning]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return console.warn.apply(console,e)},o[i.Information]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return console.info.apply(console,e)},o[i.Verbose]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return console.log.apply(console,e)},o);var o,r=function(){function e(e){this.level=process.env.TL_LEVEL?parseInt(process.env.TL_LEVEL,10):i.Information,this.moduleName="",this.moduleName=e}return e.getLogger=function(t){return new e(t)},e.hook=function(t,n){t&&n&&"function"==typeof n&&(e.hooks[t]=(e.hooks[t]||[]).concat([n]))},e.callHook=function(t,n,i){for(var o=[],r=3;r<arguments.length;r++)o[r-3]=arguments[r];t in e.hooks&&e.hooks[t].forEach(function(e){return e.apply(void 0,[n,i].concat(o))})},e.prototype.critical=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this.log.apply(this,[i.Critical].concat(e))},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this.log.apply(this,[i.Error].concat(e))},e.prototype.warning=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this.log.apply(this,[i.Warning].concat(e))},e.prototype.information=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this.log.apply(this,[i.Information].concat(e))},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this.log.apply(this,[i.Verbose].concat(e))},e.prototype.log=function(t){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];e.callHook.apply(e,["beforeLog",t,this.moduleName].concat(n)),this.checkShouldLog(t)&&(this.writeLog.apply(this,[t,"["+this.moduleName+"]"].concat(n)),e.callHook.apply(e,["logged",t,this.moduleName].concat(n)))},e.prototype.writeLog=function(t){for(var n,i=[],o=1;o<arguments.length;o++)i[o-1]=arguments[o];(n=e.outputLogger)[t].apply(n,i)},e.prototype.checkShouldLog=function(t){return e.enableLogger&&t<=this.level&&this.checkfilter()},e.prototype.checkfilter=function(){return"all"===e.moduleFilter||e.moduleFilter.includes(this.moduleName)},e.outputLogger=t.outputLoggerConsole,e.hooks={},e.enableLogger="1"===process.env.TL_ENABLE,e.moduleFilter=process.env.TL_MODULE_FILTER||"all",e}();t.default=r},function(e,t){e.exports=require("path")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderChannelList=void 0,function(e){e.channelBase="ChannelBase",e.channelRMNewTaskSetTaskData=e.channelBase+"1",e.channelRMPreNewTaskSetTaskData=e.channelBase+"2",e.channelRMNewTaskCreateNewTask=e.channelBase+"3",e.channelRMNewTaskSetBTInfo=e.channelBase+"4",e.channelRMNewTaskDownloadTorrent=e.channelBase+"5",e.channelRMNewTaskCreateBtTask=e.channelBase+"6",e.channelRMNewTaskCancleMagnet=e.channelBase+"7",e.channelRMImportTorrent=e.channelBase+"8",e.channelRMGetConfigValueResolve=e.channelBase+"9",e.channelRMGetConfigValueReject=e.channelBase+"10",e.channelMRTrayMenuClick=e.channelBase+"11",e.channelMRNewTaskMagnetTaskCreated=e.channelBase+"12",e.channelMRNewTaskDownloadTorrentResult=e.channelBase+"13",e.channelMRNewTaskCreateNewTaskResult=e.channelBase+"14",e.channelMRNewTaskCreateBtTaskResult=e.channelBase+"15",e.channelMRGetConfigValue=e.channelBase+"16",e.channelMRSetConfigValue=e.channelBase+"17",e.channelRMCommitPlanTask=e.channelBase+"18",e.channelRMPerformePlanTask=e.channelBase+"19",e.channelRMProcessSend=e.channelBase+"20",e.channelRMGetPrivateSpaceInfo=e.channelBase+"21",e.channelMRGetPrivateSpaceInfoResult=e.channelBase+"22",e.channelRMFileCopy=e.channelBase+"23",e.channelRMFileMove=e.channelBase+"24",e.channelMRFileCopyResult=e.channelBase+"25",e.channelMRFileMoveResult=e.channelBase+"26",e.channelRMGetSutitleByCid=e.channelBase+"27",e.channelMRGetSutitleByCidResult=e.channelBase+"28",e.channelRMGetSutitleByName=e.channelBase+"29",e.channelMRGetSutitleByNameResult=e.channelBase+"30",e.channelRMDownloadSutitle=e.channelBase+"31",e.channelMRDownloadSutitleSuc=e.channelBase+"32",e.channelMRDownloadSutitleFail=e.channelBase+"33",e.channelRMGetDisplayName=e.channelBase+"34",e.channelMRGetDisplayNameResult=e.channelBase+"35",e.channelMRBringWindowToTop=e.channelBase+"36",e.channelRMDownloadXmp=e.channelBase+"37",e.channelMRFixXmpSuc=e.channelBase+"38",e.channelMRFixXMPFail=e.channelBase+"39",e.channelMRAPlayerCreated=e.channelBase+"41",e.channelMRMainRendererNativeCallInited=e.channelBase+"42",e.channelMRShowWindow=e.channelBase+"43",e.channelMRCreateTopView=e.channelBase+"44",e.channelMRSendTopView=e.channelBase+"44.1",e.channelMRDestroyTopView=e.channelBase+"44.5",e.channelMRCreateTopViewReadyFinish=e.channelBase+"45",e.channelMRCreateTopViewFinish=e.channelBase+"45.5",e.channelMRCreateCtrlView=e.channelBase+"46",e.channelMRSendCtrlView=e.channelBase+"46.1",e.channelMRDestroyCtrlView=e.channelBase+"46.5",e.channelMRCreateCtrlViewReadyFinish=e.channelBase+"47",e.channelMRCreateCtrlViewFinish=e.channelBase+"47.5",e.channelMRCreateSideBar=e.channelBase+"48",e.channelMRCreateSideBarFinish=e.channelBase+"49",e.channelMRExpandListWindow=e.channelBase+"52",e.channelMRExpandListVue=e.channelBase+"53",e.channelMRSetChangeViewSize=e.channelBase+"54",e.channelMRSyncShowMode=e.channelBase+"55",e.channelMRAPWindowUpdate=e.channelBase+"56",e.channelMRUpdateAPlayerPos=e.channelBase+"57",e.channelMRXmpVideoTipCreated=e.channelBase+"58",e.channelMRXmpVideoTipDestroy=e.channelBase+"59",e.channelMRShowSettingCenterWindow=e.channelBase+"60",e.channelRMSetPosition=e.channelBase+"61",e.channelRMShowPlaySettingWindow=e.channelBase+"62",e.channelRMShowCursor=e.channelBase+"63",e.channelRMAPlayerState=e.channelBase+"64",e.channelRMAPlayerProgress=e.channelBase+"65",e.channelRMPlayAction=e.channelBase+"66",e.channelRMSetFoucs=e.channelBase+"67",e.channelMRSetZorder=e.channelBase+"68",e.channelRMGetBrowserStartType=e.channelBase+"69",e.channelMRGetBrowserStartTypeResult=e.channelBase+"70",e.channelMRWindowPopUp=e.channelBase+"71",e.channelMRUpdateTipWindowZorder=e.channelBase+"72",e.channelMRSetShadowWindowResize=e.channelBase+"73",e.channelMRBrowserWindowChange=e.channelBase+"74",e.channelMRChangeFullScreen=e.channelBase+"75",e.channelMRTabNumberChange=e.channelBase+"76",e.channelMRThumbTaskBarAction=e.channelBase+"77",e.channelMRThumbTaskBarButtonStatus=e.channelBase+"78",e.channelRMOpenFolder=e.channelBase+"79",e.channelRReCreateCtrlWindow=e.channelBase+"80",e.channelRReCreateTopWindow=e.channelBase+"81",e.channelRMSetEnvironmentVariable=e.channelBase+"82",e.channelRMServerStarted=e.channelBase+"83",e.channelRMOpenDevTools=e.channelBase+"84",e.channelEnterEditMode=e.channelBase+"85",e.channelUpdateEditRect=e.channelBase+"86",e.channelMessageBoxClose=e.channelBase+"87",e.channelPreventSleep=e.channelBase+"88",e.channelCancelPreventSleep=e.channelBase+"89",e.channelCloseEffectWindow=e.channelBase+"90",e.channelPopUpMenu=e.channelBase+"91",e.channelHideTray=e.channelBase+"92",e.channelEmbedMoveResize=e.channelBase+"94",e.channelTimeTipPos=e.channelBase+"95",e.channelUpdateVideoTip=e.channelBase+"96",e.channelGetBrowserView=e.channelBase+"97",e.channelGetBrowserViewResult=e.channelBase+"98",e.channelBrowserViewLoad=e.channelBase+"99",e.channelBrowserViewOpenDev=e.channelBase+"100",e.channelApplyBrowserView=e.channelBase+"101",e.channelCreateBrowserView=e.channelBase+"102",e.channelCreateBrowserViewResult=e.channelBase+"103",e.channelBrowserViewCall=e.channelBase+"104",e.channelBrowserViewCallRet=e.channelBase+"105",e.channelEmbedWindowRgn=e.channelBase+"106",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},function(e,t){e.exports=require("electron")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DisplayCardInfo=t.DetailIndex=t.PanelID=t.XmpPlaySoure=t.WindowParams=t.CombineRgnType=t.ZOrderChangedType=t.WindowTopMode=t.XmpMode=t.ShowWindowCmd=t.VirtualKeyCode=t.HitTestPositionCode=t.GWCmd=t.WindowMessage=t.SetWindowType=t.WindowStyleEx=t.WindowStyle=t.CmdShow=t.Uflag=t.OptionOfHWNDInAfter=void 0,function(e){e[e.HWND_NOTOPMOST=-2]="HWND_NOTOPMOST",e[e.HWND_TOPMOST=-1]="HWND_TOPMOST",e[e.HWND_TOP=0]="HWND_TOP",e[e.HWND_BOTTOM=1]="HWND_BOTTOM"}(t.OptionOfHWNDInAfter||(t.OptionOfHWNDInAfter={})),function(e){e[e.SWP_ASYNCWINDOWPOS=16384]="SWP_ASYNCWINDOWPOS",e[e.SWP_DEFERERASE=8192]="SWP_DEFERERASE",e[e.SWP_DRAWFRAME=32]="SWP_DRAWFRAME",e[e.SWP_FRAMECHANGED=32]="SWP_FRAMECHANGED",e[e.SWP_HIDEWINDOW=128]="SWP_HIDEWINDOW",e[e.SWP_NOACTIVATE=16]="SWP_NOACTIVATE",e[e.SWP_NOCOPYBITS=256]="SWP_NOCOPYBITS",e[e.SWP_NOMOVE=2]="SWP_NOMOVE",e[e.SWP_NOOWNERZORDER=512]="SWP_NOOWNERZORDER",e[e.SWP_NOREDRAW=8]="SWP_NOREDRAW",e[e.SWP_NOREPOSITION=512]="SWP_NOREPOSITION",e[e.SWP_NOSENDCHANGING=1024]="SWP_NOSENDCHANGING",e[e.SWP_NOSIZE=1]="SWP_NOSIZE",e[e.SWP_NOZORDER=4]="SWP_NOZORDER",e[e.SWP_SHOWWINDOW=64]="SWP_SHOWWINDOW"}(t.Uflag||(t.Uflag={})),function(e){e[e.SW_FORCEMINIMIZE=11]="SW_FORCEMINIMIZE",e[e.SW_HIDE=0]="SW_HIDE",e[e.SW_MAXIMIZE=3]="SW_MAXIMIZE",e[e.SW_MINIMIZE=6]="SW_MINIMIZE",e[e.SW_RESTORE=9]="SW_RESTORE",e[e.SW_SHOW=5]="SW_SHOW",e[e.SW_SHOWDEFAULT=10]="SW_SHOWDEFAULT",e[e.SW_SHOWMAXIMIZED=3]="SW_SHOWMAXIMIZED",e[e.SW_SHOWMINIMIZED=2]="SW_SHOWMINIMIZED",e[e.SW_SHOWMINNOACTIVE=7]="SW_SHOWMINNOACTIVE",e[e.SW_SHOWNA=8]="SW_SHOWNA",e[e.SW_SHOWNOACTIVATE=4]="SW_SHOWNOACTIVATE",e[e.SW_SHOWNORMAL=1]="SW_SHOWNORMAL"}(t.CmdShow||(t.CmdShow={})),function(e){e[e.WS_BORDER=8388608]="WS_BORDER",e[e.WS_CAPTION=12582912]="WS_CAPTION",e[e.WS_CHILD=1073741824]="WS_CHILD",e[e.WS_CHILDWINDOW=1073741824]="WS_CHILDWINDOW",e[e.WS_CLIPCHILDREN=33554432]="WS_CLIPCHILDREN",e[e.WS_CLIPSIBLINGS=67108864]="WS_CLIPSIBLINGS",e[e.WS_POPUP=2147483648]="WS_POPUP",e[e.WS_THICKFRAME=262144]="WS_THICKFRAME"}(t.WindowStyle||(t.WindowStyle={})),function(e){e[e.WS_EX_TOOLWINDOW=128]="WS_EX_TOOLWINDOW",e[e.WS_EX_APPWINDOW=262144]="WS_EX_APPWINDOW",e[e.WS_EX_TOPMOST=8]="WS_EX_TOPMOST",e[e.WS_EX_WINDOWEDGE=256]="WS_EX_WINDOWEDGE",e[e.WS_EX_NOACTIVATE=134217728]="WS_EX_NOACTIVATE"}(t.WindowStyleEx||(t.WindowStyleEx={})),function(e){e[e.GWL_EXSTYLE=-20]="GWL_EXSTYLE",e[e.GWL_HINSTANCE=-6]="GWL_HINSTANCE",e[e.GWL_ID=-12]="GWL_ID",e[e.GWL_STYLE=-16]="GWL_STYLE",e[e.GWL_USERDATA=-21]="GWL_USERDATA",e[e.GWL_WNDPROC=-4]="GWL_WNDPROC"}(t.SetWindowType||(t.SetWindowType={})),function(e){e[e.WM_CREATE=1]="WM_CREATE",e[e.WM_DESTROY=2]="WM_DESTROY",e[e.WM_MOVE=3]="WM_MOVE",e[e.WM_SIZE=5]="WM_SIZE",e[e.WM_ACTIVATE=6]="WM_ACTIVATE",e[e.WM_SETFOCUS=7]="WM_SETFOCUS",e[e.WM_KILLFOCUS=8]="WM_KILLFOCUS",e[e.WM_ENABLE=10]="WM_ENABLE",e[e.WM_KEYDOWN=256]="WM_KEYDOWN",e[e.WM_KEYUP=257]="WM_KEYUP",e[e.WM_SYSKEYDOWN=260]="WM_SYSKEYDOWN",e[e.WM_MOUSEMOVE=512]="WM_MOUSEMOVE",e[e.WM_SETCURSOR=32]="WM_SETCURSOR",e[e.WM_LBUTTONDOWN=513]="WM_LBUTTONDOWN",e[e.WM_LBUTTONUP=514]="WM_LBUTTONUP",e[e.WM_LBUTTONDBLCLK=515]="WM_LBUTTONDBLCLK",e[e.WM_RBUTTONUP=517]="WM_RBUTTONUP",e[e.WM_MOUSEWHEEL=522]="WM_MOUSEWHEEL",e[e.WM_MOUSELEAVE=675]="WM_MOUSELEAVE",e[e.WM_DPICHANGED=736]="WM_DPICHANGED",e[e.WM_GETMINMAXINFO=36]="WM_GETMINMAXINFO",e[e.WM_NCHITTEST=132]="WM_NCHITTEST",e[e.WM_NCMOUSEMOVE=160]="WM_NCMOUSEMOVE",e[e.WM_NCLBUTTONDOWN=161]="WM_NCLBUTTONDOWN",e[e.WM_NCLBUTTONUP=162]="WM_NCLBUTTONUP",e[e.WM_NCLBUTTONDBLCLK=163]="WM_NCLBUTTONDBLCLK",e[e.WM_NCRBUTTONDOWN=164]="WM_NCRBUTTONDOWN",e[e.WM_NCRBUTTONUP=165]="WM_NCRBUTTONUP",e[e.WM_NCRBUTTONDBLCLK=166]="WM_NCRBUTTONDBLCLK",e[e.WM_NCMBUTTONDOWN=167]="WM_NCMBUTTONDOWN",e[e.WM_NCMBUTTONUP=168]="WM_NCMBUTTONUP",e[e.WM_NCMBUTTONDBLCLK=169]="WM_NCMBUTTONDBLCLK",e[e.WM_WINDOWPOSCHANGED=71]="WM_WINDOWPOSCHANGED",e[e.WM_WINDOWPOSCHANGING=70]="WM_WINDOWPOSCHANGING",e[e.WM_ACTIVATEAPP=28]="WM_ACTIVATEAPP",e[e.WM_DWMCOMPOSITIONCHANGED=798]="WM_DWMCOMPOSITIONCHANGED"}(t.WindowMessage||(t.WindowMessage={})),function(e){e[e.GW_HWNDFIRST=0]="GW_HWNDFIRST",e[e.GW_HWNDLAST=1]="GW_HWNDLAST",e[e.GW_HWNDNEXT=2]="GW_HWNDNEXT",e[e.GW_HWNDPREV=3]="GW_HWNDPREV",e[e.GW_OWNER=4]="GW_OWNER",e[e.GW_CHILD=5]="GW_CHILD",e[e.GW_ENABLEDPOPUP=6]="GW_ENABLEDPOPUP"}(t.GWCmd||(t.GWCmd={})),function(e){e[e.HTERROR=-2]="HTERROR",e[e.HTTRANSPARENT=-1]="HTTRANSPARENT",e[e.HTNOWHERE=0]="HTNOWHERE",e[e.HTCLIENT=1]="HTCLIENT",e[e.HTCAPTION=2]="HTCAPTION",e[e.HTSYSMENU=3]="HTSYSMENU",e[e.HTGROWBOX=4]="HTGROWBOX",e[e.HTSIZE=4]="HTSIZE",e[e.HTMENU=5]="HTMENU",e[e.HTHSCROLL=6]="HTHSCROLL",e[e.HTVSCROLL=7]="HTVSCROLL",e[e.HTMINBUTTON=8]="HTMINBUTTON",e[e.HTMAXBUTTON=9]="HTMAXBUTTON",e[e.HTLEFT=10]="HTLEFT",e[e.HTRIGHT=11]="HTRIGHT",e[e.HTTOP=12]="HTTOP",e[e.HTTOPLEFT=13]="HTTOPLEFT",e[e.HTTOPRIGHT=14]="HTTOPRIGHT",e[e.HTBOTTOM=15]="HTBOTTOM",e[e.HTBOTTOMLEFT=16]="HTBOTTOMLEFT",e[e.HTBOTTOMRIGHT=17]="HTBOTTOMRIGHT",e[e.HTBORDER=18]="HTBORDER",e[e.HTREDUCE=8]="HTREDUCE",e[e.HTZOOM=9]="HTZOOM",e[e.HTSIZEFIRST=10]="HTSIZEFIRST",e[e.HTSIZELAST=17]="HTSIZELAST",e[e.HTOBJECT=19]="HTOBJECT",e[e.HTCLOSE=20]="HTCLOSE",e[e.HTHELP=21]="HTHELP"}(t.HitTestPositionCode||(t.HitTestPositionCode={})),function(e){e[e.VK_LBUTTON=1]="VK_LBUTTON",e[e.VK_RBUTTON=2]="VK_RBUTTON",e[e.VK_MBUTTON=4]="VK_MBUTTON",e[e.VK_BACK=8]="VK_BACK",e[e.VK_TAB=9]="VK_TAB",e[e.VK_RETURN=13]="VK_RETURN",e[e.VK_SHIFT=16]="VK_SHIFT",e[e.VK_CONTROL=17]="VK_CONTROL",e[e.VK_MENU=18]="VK_MENU",e[e.VK_ESCAPE=27]="VK_ESCAPE",e[e.VK_SPACE=32]="VK_SPACE",e[e.VK_PRIOR=33]="VK_PRIOR",e[e.VK_NEXT=34]="VK_NEXT",e[e.VK_END=35]="VK_END",e[e.VK_LEFT=37]="VK_LEFT",e[e.VK_UP=38]="VK_UP",e[e.VK_RIGHT=39]="VK_RIGHT",e[e.VK_DOWN=40]="VK_DOWN",e[e.VK_DELETE=46]="VK_DELETE",e[e.VK_F1=112]="VK_F1",e[e.VK_F2=113]="VK_F2",e[e.VK_F3=114]="VK_F3",e[e.VK_F4=115]="VK_F4",e[e.VK_F5=116]="VK_F5",e[e.VK_F6=117]="VK_F6",e[e.VK_F7=118]="VK_F7",e[e.VK_F8=119]="VK_F8",e[e.VK_F9=120]="VK_F9",e[e.VK_F10=121]="VK_F10",e[e.VK_F11=122]="VK_F11",e[e.VK_F12=123]="VK_F12",e[e.VK_OEM_1=186]="VK_OEM_1",e[e.VK_OEM_PLUS=187]="VK_OEM_PLUS",e[e.VK_OEM_COMMA=188]="VK_OEM_COMMA",e[e.VK_OEM_MINUS=189]="VK_OEM_MINUS",e[e.VK_OEM_PERIOD=190]="VK_OEM_PERIOD",e[e.VK_OEM_2=191]="VK_OEM_2",e[e.VK_OEM_3=192]="VK_OEM_3",e[e.VK_OEM_4=219]="VK_OEM_4",e[e.VK_OEM_5=220]="VK_OEM_5",e[e.VK_OEM_6=221]="VK_OEM_6",e[e.VK_OEM_7=222]="VK_OEM_7",e[e.VK_PROCESSKEY=229]="VK_PROCESSKEY"}(t.VirtualKeyCode||(t.VirtualKeyCode={})),function(e){e[e.SW_HIDE=0]="SW_HIDE",e[e.SW_SHOWMAXIMIZED=3]="SW_SHOWMAXIMIZED",e[e.SW_SHOW=5]="SW_SHOW"}(t.ShowWindowCmd||(t.ShowWindowCmd={})),function(e){e[e.UNKNOWN_MODE=-1]="UNKNOWN_MODE",e[e.INDEPENDENT_MODE=0]="INDEPENDENT_MODE",e[e.MAGNETIC_MODE=1]="MAGNETIC_MODE",e[e.EMBED_MODE=2]="EMBED_MODE"}(t.XmpMode||(t.XmpMode={})),function(e){e[e.NO_TOPMOST_MODE=0]="NO_TOPMOST_MODE",e[e.PLAYING_TOPMOST_MODE=1]="PLAYING_TOPMOST_MODE",e[e.ALWAYLS_TOPMOST_MODE=2]="ALWAYLS_TOPMOST_MODE"}(t.WindowTopMode||(t.WindowTopMode={})),function(e){e[e.ZORDER_CHANGED_TYPE_UNKNOW=-1]="ZORDER_CHANGED_TYPE_UNKNOW",e[e.ZORDER_CHANGED_TYPE_BEFORE=0]="ZORDER_CHANGED_TYPE_BEFORE",e[e.ZORDER_CHANGED_TYPE_AFTER=1]="ZORDER_CHANGED_TYPE_AFTER"}(t.ZOrderChangedType||(t.ZOrderChangedType={})),function(e){e[e.RGN_AND=1]="RGN_AND",e[e.RGN_OR=2]="RGN_OR",e[e.RGN_XOR=3]="RGN_XOR",e[e.RGN_DIFF=4]="RGN_DIFF",e[e.RGN_COPY=5]="RGN_COPY"}(t.CombineRgnType||(t.CombineRgnType={})),function(e){e[e.DEFAULT_WIDTH=850]="DEFAULT_WIDTH",e[e.DEFAULT_HEIGHT=550]="DEFAULT_HEIGHT",e[e.MIN_WINDOW_WIDTH=350]="MIN_WINDOW_WIDTH",e[e.MIN_WINDOW_HEIGHT=200]="MIN_WINDOW_HEIGHT",e[e.TOPCTRL_HEIGHT=42]="TOPCTRL_HEIGHT",e[e.PLAYCTRL_HEIGHT=62]="PLAYCTRL_HEIGHT"}(t.WindowParams||(t.WindowParams={})),function(e){e[e.PLAY_UNKNOWN=0]="PLAY_UNKNOWN",e[e.PLAY_BY_DOWNLOAD=1]="PLAY_BY_DOWNLOAD",e[e.PLAY_BY_PAN=2]="PLAY_BY_PAN"}(t.XmpPlaySoure||(t.XmpPlaySoure={})),function(e){e.Download="download-panel",e.Cloud="pan-plugin-view",e.Browser="find-page",e.Message="message-page"}(t.PanelID||(t.PanelID={})),function(e){e.Preview="Preview",e.Speed="TaskChart",e.Attribute="Attribute"}(t.DetailIndex||(t.DetailIndex={}));t.DisplayCardInfo=class{}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(20),o=n(1);t.getDefaultPrex=function(){return o.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=i.tmpdir();let n=e;e||(n=o.basename(process.execPath,".exe"));let r=o.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(r="\\\\.\\pipe\\"+(r=(r=r.replace(/^\//,"")).replace(/\//g,"-"))),r},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},function(e,t){e.exports=require("events")},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function a(e){try{s(i.next(e))}catch(e){r(e)}}function l(e){try{s(i.throw(e))}catch(e){r(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.ThunderHelper=void 0;const o=n(1),r=n(4),a=n(5),l=n(0).default.getLogger("XMP.ThunderHelper"),s=r.default(o.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(e){return i(this,void 0,void 0,function*(){if(!e||(yield e.isDestroyed()))return 0;let t=0,n=yield e.getNativeWindowHandle();return n&&(t=n.readUIntLE(0,4)),t})}function o(e,t,n,i,o,r,l,d){if(!s||!e)return;let c="";d&&d.length>3&&(c=d.substring(0,d.length-3)),l|=a.Uflag.SWP_ASYNCWINDOWPOS,s.setWindowPos(e,t,n,i,o,r,l)}function r(){return s.getDPIAwareSupport()}function d(e){return s.getMonitorDPIFactor(e)}function c(){return s.getSysDPIFactor()}e.getDriveInfo=function(e){return s.getDriveInfo(e)},e.getFreePartitionSpace=function(e){return s.getFreePartitionSpace(e)},e.getLogicalDriveStrings=function(){return s.getLogicalDriveStrings()},e.getPartitionSpace=function(e){return s.getPartitionSpace(e)},e.getSystemTempPath=function(){return s.getSystemTempPath()},e.getTaskTypeFromUrl=function(e){return s.getTaskTypeFromUrl(e)},e.dwmIsCompositionEnabled=function(){return s.dwmIsCompositionEnabled()},e.setWindowLong=function(e,t,n){return s.setWindowLong(e,t,n)},e.showCursor=function(e){s&&s.showCursor(e)},e.isWindowVisible=function(e){return!!s&&s.isWindowVisible(e)},e.isWindowEnable=function(e){return!!s&&s.isWindowEnable(e)},e.enableWindow=function(e,t){return!!s&&s.enableWindow(e,t)},e.shChangeNotify=function(){if(s)return s.refreshIcon()},e.getBrowserWindowHandle=t,e.setTopMostWindow=function(e){return i(this,void 0,void 0,function*(){s&&e&&o(yield t(e),a.OptionOfHWNDInAfter.HWND_TOPMOST,0,0,0,0,a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE)})},e.getGraphicsAdapterList=function(){return s?s.getGraphicsAdapterList():null},e.getFileVersion=function(e){return s&&e?s.getFileVersion(e):""},e.getCursorPos=function(){return s?s.getCursorPos():{x:0,y:0}},e.getPowerInfo=function(){return s?s.getPowerInfo():null},e.setWindowRectRgn=function(e,t,n,i,o){s&&s.setWindowRectRgn(e,t,n,i,o)},e.createRectRgn=function(e,t,n,i){return s?s.createRectRgn(e,t,n,i):0},e.combineRgn=function(e,t,n=a.CombineRgnType.RGN_OR){return s?s.combineRgn(e,t,n):-1},e.deleteObject=function(e){return!!s&&s.deleteObject(e)},e.setWindowRgn=function(e,t,n=!0){return s?s.setWindowRgn(e,t,n):-1},e.hitCaption=function(e){return!!s&&s.hitCaption(e)},e.isWindowForeGround=function(e){return i(this,void 0,void 0,function*(){if(!s)return!1;let t=s.getForegroundWindow();return e===t})},e.getForegroundWindow=function(){return s?s.getForegroundWindow():0},e.isWindowForceGroundEx=function(e){return!!s&&e===s.getForegroundWindow()},e.setListExpandMode=function(e){return s?s.setListExpandMode(e):0},e.setTopMostWindowEx=function(e){s&&e&&o(e,a.OptionOfHWNDInAfter.HWND_TOPMOST,0,0,0,0,a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE)},e.cancelTopMostWindow=function(e,n){return i(this,void 0,void 0,function*(){if(!s||!e)return;let i=yield t(e),r=a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE|a.Uflag.SWP_NOACTIVATE;n&&(r|=n),o(i,a.OptionOfHWNDInAfter.HWND_NOTOPMOST,0,0,0,0,r)})},e.cancelTopMostWindowEx=function(e,t){if(!s||!e)return;let n=a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE|a.Uflag.SWP_NOACTIVATE;t&&(n|=t),o(e,a.OptionOfHWNDInAfter.HWND_NOTOPMOST,0,0,0,0,n)},e.getTempPath=function(){return s?s.getSystemTempPath():""},e.switchToThisWindow=function(e,t=!1){s&&s.switchToThisWindow(e,t)},e.setScreenSaveActive=function(e){if(s)return s.setScreenSaveActive(e)},e.getDiskFreeSpace=function(e){return s?s.getFreePartitionSpace(e):0},e.getMemInfoByPointer=function(e,t){return s?s.getMemInfoByPointer(e,t):null},e.setMinTrackSize=function(e,t,n){return s?s.setMinTrackSize(e,t,n):null},e.setWindowPos=function(e,t,n,i,r,a,l,d){if(!s||!e||e.isDestroyed())return;o(e.getNativeWindowHandle().readUIntLE(0,4),t,n,i,r,a,l,d)},e.moveWindowGently=function(e,t,n,i,o,r,a){s&&t&&s.moveWindowGently(e,t,n,i,o,r,a)},e.moveWindow=function(e,t){s&&e&&s.moveWindow(e,t.x,t.y,t.width,t.height,!0)},e.setWindowFocus=function(e,t=!0){s&&e&&s.setWindowFocus(e,t)},e.getWindowRect=function(e){return s&&e?s.getWindowRect(e):{x:0,y:0,width:0,height:0}},e.getClientRect=function(e){return s&&e?s.getClientRect(e):{x:0,y:0,width:0,height:0}},e.getCurrentScreenRect=function(e){return s&&e?s.getCurrentScreenRect(e):{x:0,y:0,width:0,height:0}},e.getCurrentWorkRect=function(e){return s&&e?s.getCurrentScreenRect(e,!0):{x:0,y:0,width:0,height:0}},e.getChildWindow=function(e){return s&&e?s.getChildWindow(e):0},e.getParentWindow=function(e){return s&&e?s.getParentWindow(e):0},e.getKeyState=function(e){return s?s.getKeyState(e):0},e.setWindowPosEx=o,e.beginDeferWindowPos=function(e){return s&&e?s.beginDeferWindowPos(e):0},e.endDeferWindowPos=function(e){return s&&e?s.endDeferWindowPos(e):0},e.deferWindowPos=function(e,t,n,i,o,r,a,l){return s&&e?s.deferWindowPos(e,t,n,i,o,r,a,l):0},e.bindChildWindow=function(e,t,n){if(!s||!t||!e||t.isDestroyed()||e.isDestroyed())return;let i=t.getNativeWindowHandle().readUIntLE(0,4),o=e.getNativeWindowHandle().readUIntLE(0,4);n?s.bindChildWindow(o,i,n.x,n.y,n.width,n.height):s.bindChildWindow(o,i)},e.bindChildWindowEx=function(e,t,n){s&&t&&e&&(n?s.bindChildWindow(e,t,n.x,n.y,n.width,n.height):s.bindChildWindow(e,t))},e.setDllDirectory=function(e){s&&e&&s.setDllDirectory(e)},e.bringWindowToTop=function(e){if(!e||e.isDestroyed())return;let t=e.getNativeWindowHandle().readUIntLE(0,4);s.bringWindowToTop(t)},e.bringWindowToTopEx=function(e){e&&s.bringWindowToTop(e)},e.modifyStyleEx=function(e,t,n){e&&s.modifyStyle(e,t,n)},e.modifyStyle=function(e,t,n){if(!e||e.isDestroyed())return;let i=e.getNativeWindowHandle().readUIntLE(0,4);s.modifyStyle(i,t,n)},e.registerSliderCreate=function(e){s.registerSliderCreate(e)},e.showSlider=function(e){s.showSlider(e)},e.initSideBar=function(e,t,n,i){s.initSideBar(e,t,n,i)},e.uninitSideBar=function(){return i(this,void 0,void 0,function*(){return new Promise(e=>{s.uninitSideBar(()=>{e()})})})},e.createSideBar=function(){s.createSideBar()},e.destroySideBar=function(){s.destroySideBar()},e.readRegValue=function(e,t,n){return s?s.readRegString(e,t,n):""},e.setForcegroundWindow=function(e){if(!e||e.isDestroyed())return;let t=e.getNativeWindowHandle().readUIntLE(0,4);s.setForegroundWindow(t)},e.setActiveWindowEx=function(e){s&&e&&s.setActiveWindow(e)},e.setForcegroundWindowEx=function(e){e&&s.setForegroundWindow(e)},e.is64BitWindows=function(){return!!s&&s.is64bitSystem()},e.setSideBarExpandState=function(e){s.setExpandState(e)},e.unBindChildWindow=function(e,t){if(!s||!t||!e||t.isDestroyed()||e.isDestroyed())return;l.information("unBindChildWindow");let n=t.getNativeWindowHandle().readUIntLE(0,4),i=e.getNativeWindowHandle().readUIntLE(0,4);s.unBindChildWindow(i,n)},e.unBindChildWindowEx=function(e,t){s&&t&&e&&(l.information("unBindChildWindowEx"),s.unBindChildWindow(e,t))},e.addBrowserWindowClipStyle=function(e){if(!s||!e||e.isDestroyed())return!1;let t=e.getNativeWindowHandle().readUIntLE(0,4),n=s.findChildWindow(t,"Intermediate D3D Window");return n&&s.setWindowLong(n,a.SetWindowType.GWL_STYLE,a.WindowStyle.WS_CLIPSIBLINGS),s.setWindowLong(t,a.SetWindowType.GWL_STYLE,a.WindowStyle.WS_CLIPCHILDREN),!!n},e.addBrowserWindowClipStyleEx=function(e){if(!s)return!1;let t=s.findChildWindow(e,"Intermediate D3D Window");return t&&s.setWindowLong(t,a.SetWindowType.GWL_STYLE,a.WindowStyle.WS_CLIPSIBLINGS),s.setWindowLong(e,a.SetWindowType.GWL_STYLE,a.WindowStyle.WS_CLIPCHILDREN),!!t},e.getDoubleClickTime=function(){return s?s.getDoubleClickTime():50},e.invalidateRect=function(e,t,n){s&&s.invalidateRect(e,t,n)},e.isIconic=function(e){return!!s&&s.isIconic(e)},e.isZoomed=function(e){return!!s&&s.isZoomed(e)},e.showWindow=function(e){return!!s&&s.showWindow(e,a.ShowWindowCmd.SW_SHOW)},e.hideWindow=function(e){return!!s&&s.showWindow(e,a.ShowWindowCmd.SW_HIDE)},e.setZorderWindow=function(e,t,n){if(s)return s.setZorderWindow(e,t,n)},e.getWindowLong=function(e,t){return s?s.getWindowLong(e,t):0},e.getHelperObject=function(){return s},e.getPeerID=function(){return s.getPeerID()},e.getDPIAwareSupport=r,e.getMonitorDPIFactor=d,e.getSysDPIFactor=c,e.getDpi=function(e){let t=1;return t=r()?d(e):c()},e.killProcess=function(e){return i(this,void 0,void 0,function*(){let t=null;{let i="tasklist",o=n(18).exec;t=new Promise(t=>{o(i,function(n,i,o){let r=!1;do{if(n){l.warning(n),r=!1;break}i.split("\n").filter(function(t){let n=t.trim().split(/\s+/);if(n.length<2)return;let i=n[0],o=Number(n[1]);i.toLowerCase().indexOf(e.toLowerCase())>=0&&o&&(process.kill(o,"SIGTERM"),r=!0)})}while(0);t(r)})})}return t})},e.sleep=function(e){return i(this,void 0,void 0,function*(){yield new Promise((t,n)=>{setTimeout(t,e)})})},e.calculateFileGCID=function(e){return i(this,void 0,void 0,function*(){return new Promise(t=>{s.calculateFileGCID(e,e=>{l.warning("getVideoMediaInfo calculateFileGCID result",e),t(e)})})})},e.getTickCount=function(){return s.getTickCount()},e.getTextScale=function(){return Number(s.readRegString("HKEY_CURRENT_USER","SOFTWARE\\Microsoft\\Accessibility","TextScaleFactor"))||100}}(t.ThunderHelper||(t.ThunderHelper={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hideRoundRectWindow=t.showRoundRectWindow=t.detachAllRoundRectWindow=t.setShadowWindowResizable=t.detachRoundRectWindow=t.roundRectWindow=void 0;const i=n(1),o=n(3),r=n(5),a=n(0),l=n(4),s=n(2),d=n(8),c=l.default(i.join(__rootDir,"../bin/ThunderHelper.node")),u=a.default.getLogger("round-rect-window");let h=new Map;function p(e){let t=1;return t=c.getDPIAwareSupport()?c.getMonitorDPIFactor(e):c.getSysDPIFactor()}class S{constructor(){this.win=null,this.shadowHandle=0,this.winHandle=0,this.area=16,this.closeHandler=null,this.resizeHandler=null,this.moveHandler=null,this.focusHandler=null,this.blurHandler=null,this.showHandler=null,this.hideHandler=null,this.updateTipWindowZorderHandler=null}cancelTopMost(){d.ThunderHelper.setWindowPosEx(this.shadowHandle,r.OptionOfHWNDInAfter.HWND_NOTOPMOST,0,0,0,0,r.Uflag.SWP_NOMOVE|r.Uflag.SWP_NOSIZE|r.Uflag.SWP_NOACTIVATE)}getParentWindowRect(){let e=this.area/2,t=c.getWindowRect(this.winHandle),n=p(this.winHandle),i={x:0,y:0,width:0,height:0};return i.x=Math.floor(t.x-e*n),i.y=Math.floor(t.y-e*n),i.width=Math.floor(t.width+2*e*n),i.height=Math.floor(t.height+2*e*n),i}updateShadowWindowPos(e){let t=this.getParentWindowRect(),n=c.getWindowRect(this.shadowHandle);if(n.x===t.x&&n.y===t.y&&n.width===t.width&&n.height===t.height||c.moveWindow(this.shadowHandle,t.x,t.y,t.width,t.height,!0),d.ThunderHelper.setWindowPosEx(this.shadowHandle,this.winHandle,0,0,0,0,r.Uflag.SWP_NOSIZE|r.Uflag.SWP_NOMOVE|r.Uflag.SWP_NOACTIVATE|r.Uflag.SWP_NOSENDCHANGING|r.Uflag.SWP_NOOWNERZORDER|r.Uflag.SWP_NOREDRAW),e){let e=p(this.winHandle);u.information("drawShadowWindow dpi",e),c.drawShadowWindow(this.winHandle,100*e)}}onWindowClose(){f(this.win)}onWindowResize(){this.updateShadowWindowPos(!0)}onWindowMove(){this.updateShadowWindowPos()}onWindowFocus(){this.updateShadowWindowPos()}onWindowBlur(){this.updateShadowWindowPos()}onWindowShow(){this.updateShadowWindowPos(),c.showWindow(this.shadowHandle,r.CmdShow.SW_SHOWNOACTIVATE)}onWindowHide(){c.showWindow(this.shadowHandle,r.CmdShow.SW_HIDE)}onUpdateTipWindowZorder(e,t){t||this.cancelTopMost(),this.updateShadowWindowPos()}onWindowPosChanged(){if(this.shadowHandle&&!this.win.isDestroyed()&&this.win.isVisible()){c.getNextWindow(this.shadowHandle,r.GWCmd.GW_HWNDPREV)!==this.winHandle&&this.updateShadowWindowPos()}}attach(e,t,n,i,a,l,d){do{this.win=e;let h=e.getNativeWindowHandle();this.winHandle=h.readUIntLE(0,4);let S=0;t&&(S=t.getNativeWindowHandle().readUIntLE(0,4));let w=p(this.winHandle);if(u.information("attachShadowWindow",this.winHandle,a,S,n,i,100*w,l,d),this.shadowHandle=c.attachShadowWindow(this.winHandle,a,S,n,i,100*w,l,d),!this.shadowHandle){u.warning("attachShadowWindow failed!",this.winHandle);break}if(c.setShadowWindowResizable(this.winHandle,e.isResizable()),this.closeHandler=this.onWindowClose.bind(this),e.on("close",this.closeHandler),n)break;this.updateShadowWindowPos(!0),e.isVisible()||c.showWindow(this.shadowHandle,r.CmdShow.SW_HIDE),this.resizeHandler=this.onWindowResize.bind(this),this.moveHandler=this.onWindowMove.bind(this),this.focusHandler=this.onWindowFocus.bind(this),this.blurHandler=this.onWindowBlur.bind(this),this.showHandler=this.onWindowShow.bind(this),this.hideHandler=this.onWindowHide.bind(this),this.updateTipWindowZorderHandler=this.onUpdateTipWindowZorder.bind(this),e.on("resize",this.resizeHandler),e.on("move",this.moveHandler),e.on("focus",this.focusHandler),e.on("blur",this.blurHandler),e.on("show",this.showHandler),e.on("hide",this.hideHandler),o.ipcMain.on(s.ThunderChannelList.channelMRUpdateTipWindowZorder,this.updateTipWindowZorderHandler),e.hookWindowMessage(r.WindowMessage.WM_WINDOWPOSCHANGED,this.onWindowPosChanged.bind(this))}while(0);return this.shadowHandle}detach(){this.win&&!this.win.isDestroyed()&&(u.information("detach win.id:",this.win.id),this.closeHandler&&(this.win.removeListener("close",this.closeHandler),this.closeHandler=null),this.resizeHandler&&(this.win.removeListener("resize",this.resizeHandler),this.resizeHandler=null),this.moveHandler&&(this.win.removeListener("move",this.moveHandler),this.moveHandler=null),this.focusHandler&&(this.win.removeListener("focus",this.focusHandler),this.focusHandler=null),this.blurHandler&&(this.win.removeListener("blur",this.blurHandler),this.blurHandler=null),this.showHandler&&(this.win.removeListener("show",this.showHandler),this.showHandler=null),this.hideHandler&&(this.win.removeListener("hide",this.hideHandler),this.hideHandler=null),this.updateTipWindowZorderHandler&&(o.ipcMain.removeListener(s.ThunderChannelList.channelMRUpdateTipWindowZorder,this.updateTipWindowZorderHandler),this.updateTipWindowZorderHandler=null),this.win.unhookWindowMessage(r.WindowMessage.WM_WINDOWPOSCHANGED),c.destroyShadowWindow(this.winHandle))}}function w(e,t,n=!0,i=!0,o=16,r="",a=null){let l=0;do{if(!e||e.isDestroyed()){u.warning("win is already destroyed! win id:",e.id);break}let s=h.get(e.id);if(s){u.warning("shadowWindow is already attached! win id:",e.id);break}if(s=new S,!e||e.isDestroyed()){u.warning("win is already destroyed! win id:",e.id);break}h.set(e.id,s),l=s.attach(e,t,n,i,o,r,a)}while(0);return l}function f(e,t){do{let n=0,i=null;if(e&&!e.isDestroyed()?(n=e.id,i=h.get(e.id)):(n=t,i=h.get(t)),!i){u.warning("shadowWindow is not attached! win id:",n);break}i.detach(),h.delete(n)}while(0)}t.roundRectWindow=function(e,t,n=!0,i=!0,o=16,r=0){let a=0,l=i?`${__rootDir}/static/shadow-corner.png`:`${__rootDir}/static/shadow.png`,s={left:17,top:17,right:17,bottom:17};return r>0?setTimeout(()=>{w(e,t,n,i,o,l,s)},r):a=w(e,t,n,i,o,l,s),a},t.detachRoundRectWindow=f,t.setShadowWindowResizable=function(e,t,n){if(!e||e.isDestroyed())return;let i=e.getNativeWindowHandle().readUIntLE(0,4);t.setShadowWindowResizable(i,n)},t.detachAllRoundRectWindow=function(){h.forEach(e=>{e.detach()}),h.clear()},t.showRoundRectWindow=function(e){if(!e||e.isDestroyed())return;let t=h.get(e.id);t&&t.onWindowShow()},t.hideRoundRectWindow=function(e){if(!e||e.isDestroyed())return;let t=h.get(e.id);t&&t.onWindowHide()}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},function(e,t,n){n(12),n(13),e.exports=n(15)},function(e,t,n){"use strict"},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(0),o=n(14),r=n(1),a=n(4).default(r.join(__rootDir,"../bin/ThunderHelper.node")),l=n(2),s=n(3);function d(){"console"===process.env.TL_OUTPUT?i.default.outputLogger=i.outputLoggerConsole:i.default.outputLogger=function(){function e(e){return function(...t){a.printEtwLog(e,function(...e){return e.map(e=>o.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[i.LogLevel.Critical]:e(i.LogLevel.Critical),[i.LogLevel.Error]:e(i.LogLevel.Error),[i.LogLevel.Warning]:e(i.LogLevel.Warning),[i.LogLevel.Information]:e(i.LogLevel.Information),[i.LogLevel.Verbose]:e(i.LogLevel.Verbose)}}()}function c(){i.default.enableLogger="1"===process.env.TL_ENABLE,d()}d(),"browser"===process.type?s.ipcMain.on(l.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{c()}):"renderer"===process.type&&s.ipcRenderer.on(l.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{c()})},function(e,t){e.exports=require("util")},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function a(e){try{s(i.next(e))}catch(e){r(e)}}function l(e){try{s(i.throw(e))}catch(e){r(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(0).default.getLogger("XMP.MainProcess");(function(){return i(this,void 0,void 0,function*(){o.information("main enter"),n(16)})})().catch()},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function a(e){try{s(i.next(e))}catch(e){r(e)}}function l(e){try{s(i.throw(e))}catch(e){r(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(17),r=n(8),a=n(5),l=n(19),s=n(6),d=n(9),c=n(21),u=n(0),h=n(3),p=n(26),S=n(2),w=n(28),f=n(1),g=u.default.getLogger("XmpPluginMain");let m=!1,W=a.XmpMode.UNKNOWN_MODE,T=null,C=null,E=0,O=a.XmpPlaySoure.PLAY_UNKNOWN,P=null,_=null,M=null,y=null,I=void 0,v=!1,D=new Map,b=0,N=!1,R=!1,L=null;function H(){if(W===a.XmpMode.INDEPENDENT_MODE&&P){let e=P.getNativeWindowHandle().readUIntLE(0,4),t=r.ThunderHelper.getClientRect(e),n=r.ThunderHelper.getDpi(e);g.information("independWindowResize independWindow clientRect",t,"dpi",n),r.ThunderHelper.setWindowPosEx(E,0,0,0,t.width,t.height,a.Uflag.SWP_NOZORDER|a.Uflag.SWP_NOACTIVATE);let i=r.ThunderHelper.getWindowRect(E),o=P.isFullScreen(),l=P.isMaximized();if(g.warning("cqm independWindowResize isFullScreen",o,"isMaximized",l,"floatWindowRect",i),o||l){K({x:-99999,y:-99999,width:i.width,height:i.height}),setTimeout(()=>{K(i)},100)}else K(i);G()}}function F(){if(W===a.XmpMode.INDEPENDENT_MODE&&P){let e=P.getNativeWindowHandle().readUIntLE(0,4),t=r.ThunderHelper.getClientRect(e),n=r.ThunderHelper.getDpi(e);g.information("independWindowMove independWindow clientRect",t,"dpi",n),K(r.ThunderHelper.getWindowRect(E))}}function A(){if(!M)return;let e=M.getNativeWindowHandle().readUIntLE(0,4);L=r.ThunderHelper.getWindowRect(e)}function x(e){return i(this,void 0,void 0,function*(){if(L||A(),!M)return;let t=M.getNativeWindowHandle().readUIntLE(0,4),n=L,i=r.ThunderHelper.getWindowRect(E);g.warning(e,n,i);let l={x:n.x,y:n.y,width:i.width,height:i.height};r.ThunderHelper.setWindowPosEx(t,0,l.x,l.y,l.width,l.height,a.Uflag.SWP_NOZORDER|a.Uflag.SWP_NOACTIVATE),r.ThunderHelper.setWindowPosEx(E,0,l.x,l.y,l.width,l.height,a.Uflag.SWP_NOZORDER|a.Uflag.SWP_NOACTIVATE),K(l),yield c.client.callRemoteClientFunction(s.mainRendererContext,o.XmpPlayerFunction.showOrHideXmpWindow)})}function B(e,...t){T=t[0],g.warning(e,"updateDownloadEmbedRect rect",T)}function V(e,...t){C=t[0],g.warning("updatePanEmbedRect rect",C)}function k(e,...t){return i(this,void 0,void 0,function*(){y&&clearTimeout(y),y=setTimeout(()=>i(this,void 0,void 0,function*(){do{if(g.information("thunderResize mode",W,"aplayerHwnd",E,"downloadEmbedRect",T,"panEmbedRect",C),W!==a.XmpMode.EMBED_MODE)break;if(A(),M.isFullScreen())break;let e=M.getNativeWindowHandle().readUIntLE(0,4),t=O===a.XmpPlaySoure.PLAY_BY_DOWNLOAD?T:C,n=r.ThunderHelper.getDpi(e);if(t={x:Math.floor(t.x*n),y:Math.floor(t.y*n),width:Math.floor(t.width*n),height:Math.floor(t.height*n)},g.warning("cqm enterModeChanging",N),N)break;g.information("thunderResize playSoure",O,"targetRect",t,"dpi",n),r.ThunderHelper.setWindowPosEx(E,0,t.x,t.y,t.width,t.height,a.Uflag.SWP_NOZORDER|a.Uflag.SWP_NOACTIVATE),K(r.ThunderHelper.getWindowRect(E)),G()}while(0)}),5)})}function U(){return i(this,void 0,void 0,function*(){R||O!==a.XmpPlaySoure.PLAY_BY_DOWNLOAD||42!==T.y?(c.client.callRemoteClientFunction(s.mainRendererContext,o.XmpPlayerFunction.showAplayerAndFloatWindow).catch(),g.information("#### showAplayerAndFloatWindow",R)):(c.client.callRemoteClientFunction(s.mainRendererContext,o.XmpPlayerFunction.hideEmbedAplayerAndFloatWindow).catch(),g.information("#### hideEmbedAplayerAndFloatWindow",R))})}function G(){r.ThunderHelper.setWindowPosEx(E,a.OptionOfHWNDInAfter.HWND_TOP,0,0,0,0,a.Uflag.SWP_NOACTIVATE|a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOMOVE,"thunderResize_"+__filename)}function X(){let e=r.ThunderHelper.getWindowRect(E);g.warning("cqm independWindowRestore restore rect",e),K(e)}function K(e){let t=e;if(0===e.x&&0===e.y&&0===e.width&&0===e.height&&P){let t=P.getNativeWindowHandle().readUIntLE(0,4);e=r.ThunderHelper.getClientRect(t)}g.warning("cqm setFloatWindowRect rect",t,e),r.ThunderHelper.setWindowPos(_,0,e.x,e.y,e.width,e.height,a.Uflag.SWP_NOACTIVATE|a.Uflag.SWP_NOZORDER)}function Z(e,...t){let n=t[0];_&&!_.isDestroyed()&&_.id===n||((_=h.BrowserWindow.fromId(n)).once("close",()=>{_=null}),_.on("resize",()=>{$()}),_.on("move",()=>{$()}),$())}function j(e,...t){E=t[0]}function z(e,...t){O=t[0],g.warning("updatePlaySource playSoure",O)}function Y(e,t,n){g.information("addXmpVideoTipPos hwnd",t,"pos",n),D.set(t,n)}function q(e,t){D.delete(t)}function $(){if(D&&D.size>0){let e=r.ThunderHelper.getWindowRect(E),t=0;D.forEach((n,o)=>i(this,void 0,void 0,function*(){let i=Math.floor(e.x+n.x),l=Math.floor(t||e.y+n.y);-99999===n.x&&(i=e.x+(e.width-n.width)/2),-99999===n.y&&(l=e.y+(e.height-n.height)/2);let s=Math.floor(n.width>0?n.width:e.width+n.width),d=Math.floor(n.height),c=r.ThunderHelper.getWindowRect(o);if(c.x===i&&c.y===l&&c.width===s&&c.height===d)return;let u=r.ThunderHelper.getDpi(E);if(g.warning("hwnd",o,"x",i,"y",l,"width",s,"height",d),r.ThunderHelper.setWindowPosEx(o,0,i,l,Math.floor(s*u),Math.floor(d*u),a.Uflag.SWP_NOZORDER|a.Uflag.SWP_NOACTIVATE,"updateXmpVideoTipPos_"+__filename),_&&!_.isDestroyed()){let e=_.getNativeWindowHandle().readUIntLE(0,4);r.ThunderHelper.setWindowPosEx(e,o,0,0,0,0,a.Uflag.SWP_NOMOVE|a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOACTIVATE,"updateXmpVideoTipPos_"+__filename)}}))}}function Q(e){return!!(r.ThunderHelper.getWindowLong(e,a.SetWindowType.GWL_EXSTYLE)&a.WindowStyleEx.WS_EX_TOPMOST)}function J(){if(!v&&I&&P){v=!0;let e=P.getNativeWindowHandle().readUIntLE(0,4);r.ThunderHelper.setZorderWindow(a.ZOrderChangedType.ZORDER_CHANGED_TYPE_AFTER,I,"shadow-window"),c.client.attachServerEvent(l.XmpEvent.onWindowZOrderChanged,(t,n,i)=>{if(n===a.ZOrderChangedType.ZORDER_CHANGED_TYPE_AFTER&&i&&i.length>0)for(let t=0;t<i.length;++t)I===i[t]&&(Q(I)&&!Q(e)&&r.ThunderHelper.setWindowPosEx(i[t],a.OptionOfHWNDInAfter.HWND_NOTOPMOST,0,0,0,0,a.Uflag.SWP_NOMOVE|a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOACTIVATE,"cancelTopMost_"+__filename),r.ThunderHelper.setWindowPosEx(i[t],e,0,0,0,0,a.Uflag.SWP_NOMOVE|a.Uflag.SWP_NOSIZE|a.Uflag.SWP_NOACTIVATE,"updateShadowWindowPos_"+__filename))})}}function ee(e=!0,t){return i(this,void 0,void 0,function*(){if(I)return void g.error("cqm doShadowWindow is exist",I);if(!r.ThunderHelper)return void g.error("cqm ThunderHelper is not exist");let n=r.ThunderHelper.getHelperObject();n?e?setTimeout(()=>i(this,void 0,void 0,function*(){I=d.roundRectWindow(P,t,!1,!1),J(),d.setShadowWindowResizable(P,n,!0)}),500):(I=d.roundRectWindow(P,t,!1,!1),J(),d.setShadowWindowResizable(P,n,!0)):g.error("cqm helpObject is not exist")})}function te(e){do{if(W!==a.XmpMode.INDEPENDENT_MODE)break;let t=r.ThunderHelper.getHelperObject();if(!t){g.error("cqm helpObject is not exist");break}P&&!P.isDestroyed()&&(g.warning("cqm setIndependShadowWindowResizable resize",e),d.setShadowWindowResizable(P,t,e),e?d.showRoundRectWindow(P):d.hideRoundRectWindow(P))}while(0)}(function(){return i(this,void 0,void 0,function*(){if(g.information("registerPluginFunction isRegister",m),!m){m=!0;let e={[o.XmpPlayerFunction.updateDownloadEmbedRect]:B,[o.XmpPlayerFunction.updatePanEmbedRect]:V,[o.XmpPlayerFunction.thunderResize]:k,[o.XmpPlayerFunction.updateFloatWindow]:Z,[o.XmpPlayerFunction.updateAplayerHwnd]:j,[o.XmpPlayerFunction.updatePlaySource]:z,[o.XmpPlayerFunction.addXmpVideoTipPos]:Y,[o.XmpPlayerFunction.removeXmpVideoTipPos]:q,[o.XmpPlayerFunction.updateScrollShowOrHide]:U};c.client.registerFunctions(e),c.client.attachServerEvent("CanShowStateByScroll",(e,...t)=>{R=t[0]})}c.client.attachServerEvent(l.XmpEvent.onMaxmized,()=>{te(!1)}),c.client.attachServerEvent(l.XmpEvent.onUnMaxmized,()=>{te(!0)}),c.client.attachServerEvent(l.ModeChangeEvent.onXmpEnterFullScreen,()=>{te(!1)}),c.client.attachServerEvent(l.ModeChangeEvent.onXmpLeaveFullScreen,()=>{te(!0)}),c.client.attachServerEvent(l.XmpEvent.onXmpClose,()=>i(this,void 0,void 0,function*(){O=a.XmpPlaySoure.PLAY_UNKNOWN,P&&(P.removeListener("resize",H),P.removeListener("move",F),P.removeListener("focus",G),P.removeListener("restore",X)),W=a.XmpMode.UNKNOWN_MODE})),c.client.attachServerEvent(l.ModeChangeEvent.onXmpModeChanging,(e,t,n,o)=>i(this,void 0,void 0,function*(){N=!0,g.warning("cqm onXmpModeChanging enterModeChanging",N)})),c.client.attachServerEvent(l.ModeChangeEvent.onXmpModeChanged,(e,t,n,o)=>i(this,void 0,void 0,function*(){if(g.information("onXmpModeChanged oldMode",t,"newMode",n,o),N=!1,W=n,n===a.XmpMode.INDEPENDENT_MODE||n===a.XmpMode.UNKNOWN_MODE)do{if(g.information("onXmpModeChanged independWindow ",P),P&&(P.removeListener("resize",H),P.removeListener("move",F),P.removeListener("focus",G),P.removeListener("restore",X),d.detachRoundRectWindow(null,b),I=null,P=null,g.information("independWindow destory")),void 0===o)break;if(!(P=h.BrowserWindow.fromId(o))){g.information("independWindow is null");break}g.information("shadowHandle",I),b=o,P.on("resize",H),P.on("move",F),P.on("focus",G),P.on("restore",X),ee(!1).catch()}while(0)}));do{let e=global;if(!e)break;if(!(M=e.mainRendererWindow))break;M.on("move",()=>i(this,void 0,void 0,function*(){if(!E||!_||_.isDestroyed())return;if(W!==a.XmpMode.EMBED_MODE)return;A();let e=M.isFullScreen();g.warning("mainWindowObject move isFullScreen",e),e||(K(r.ThunderHelper.getWindowRect(E)),G())})),M.on("focus",()=>i(this,void 0,void 0,function*(){g.warning("mainWindowObject focus"),W===a.XmpMode.EMBED_MODE&&M.isFullScreen()&&(yield x("cqm mainWindowObject focus rect")),G()})),M.on("hide",()=>i(this,void 0,void 0,function*(){W===a.XmpMode.EMBED_MODE&&(yield c.client.callRemoteClientFunction(s.mainRendererContext,o.XmpPlayerFunction.showOrHideXmpWindow))})),M.on("show",()=>i(this,void 0,void 0,function*(){W===a.XmpMode.EMBED_MODE&&(g.information("mainWindowObject show"),yield c.client.callRemoteClientFunction(s.mainRendererContext,o.XmpPlayerFunction.showOrHideXmpWindow))})),M.on("minimize",()=>i(this,void 0,void 0,function*(){W===a.XmpMode.EMBED_MODE&&(g.information("mainWindowObject minimize"),yield c.client.callRemoteClientFunction(s.mainRendererContext,o.XmpPlayerFunction.showOrHideXmpWindow))})),M.on("maximize",()=>i(this,void 0,void 0,function*(){W===a.XmpMode.EMBED_MODE&&(g.information("mainWindowObject maximize"),yield c.client.callRemoteClientFunction(s.mainRendererContext,o.XmpPlayerFunction.showOrHideXmpWindow))})),M.on("restore",()=>i(this,void 0,void 0,function*(){if(W===a.XmpMode.EMBED_MODE)if(M.isFullScreen())yield x("cqm mainWindowObject restore rect");else{let e=M.getNativeWindowHandle().readUIntLE(0,4),t=O===a.XmpPlaySoure.PLAY_BY_DOWNLOAD?T:C,n=r.ThunderHelper.getDpi(e);t={x:Math.floor(t.x*n),y:Math.floor(t.y*n),width:Math.floor(t.width*n),height:Math.floor(t.height*n)},g.information("thunder restore playSoure",O,"targetRect",t,"dpi",n),r.ThunderHelper.setWindowPosEx(E,0,t.x,t.y,t.width,t.height,a.Uflag.SWP_NOZORDER|a.Uflag.SWP_NOACTIVATE),K(r.ThunderHelper.getWindowRect(E)),yield c.client.callRemoteClientFunction(s.mainRendererContext,o.XmpPlayerFunction.showOrHideXmpWindow)}G()})),p.initMessageBox(null,r.ThunderHelper)}while(0)})})().catch(),h.ipcMain.on(S.ThunderChannelList.channelRMShowCursor,(e,t)=>{r.ThunderHelper.showCursor(t)}),function(){let e=f.join(__dirname,"bin/APlayer.exe"),t=`aplayer_version=${r.ThunderHelper.getFileVersion(e)},xmp_version=${w.version}`;c.client.callServerFunction("TrackEvent","core_event","player_startup","",0,0,0,0,t).catch()}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SubtitleFunction=t.TimeTipFunction=t.VipBufferTipFunction=t.XmpPluginFunction=t.XmpSubtitleFunction=t.PlayListFunction=t.ZipFunction=t.XmpPlayerFunction=t.XmpPlayType=t.VideoTipFunction=t.UIFunction=t.MainMenuFunction=t.SnapshotFunction=t.LoginSDKFunction=t.LoginFunction=t.DKFunction=t.ConfigFunction=void 0,function(e){e.saveConfig="SaveConfig",e.getConfigValue="GetConfigValue",e.setConfigValue="SetConfigValue",e.isConfigInitFinish="IsConfigInitFinish",e.getConfigModules="GetConfigModules",e.setXmpAndThunderConfigValue="SetXmpAndThunderConfigValue"}(t.ConfigFunction||(t.ConfigFunction={})),function(e){e.getTaskChannelInfo="GetTaskChannelInfo",e.getTaskBaseInfo="GetTaskBaseInfo",e.getTaskBaseInfos="GetTaskBaseInfos",e.getTaskAttribute="GetTaskAttribute",e.getTaskStatus="GetTaskStatus",e.findTask="FindTask",e.isTaskExist="IsTaskExist",e.isSupportPlay="IsSupportPlay",e.getTaskDownloadSpeed="GetTaskDownloadSpeed",e.enableDcdnWithVipCert="EnableDcdnWithVipCert",e.updateDcdnWithVipCert="UpdateDcdnWithVipCert",e.disableDcdnWithVipCert="DisableDcdnWithVipCert",e.getTpPeerId="GetTpPeerId",e.execSqlite="ExecSqlite",e.querySqlite="QuerySqlite",e.getCategoryViewTaskList="GetCategoryViewTaskList",e.getCategoryViewTaskListForSuspensionWnd="GetCategoryViewTaskListForSuspensionWnd",e.isLoadStorageTaskFinish="IsLoadStorageTaskFinish",e.isSingleBtTask="IsSingleBtTask",e.setTaskExtStat="SetTaskExtStat"}(t.DKFunction||(t.DKFunction={})),function(e){e.login="Login",e.logout="Logout",e.getUserID="GetUserID",e.getSessionID="GetSessionID",e.getSessionIDAw="GetSessionIDAw",e.getLoginOption="GetLoginOption",e.isLogined="IsLogined",e.getAllUserInfo="GetAllUserInfo",e.getAllUserInfoAw="GetAllUserInfoAw",e.getUserInfoEx="GetUserInfoEx",e.getUserInfo="GetUserInfo",e.getUserInfoAw="GetUserInfoAw",e.getUserHeader="GetUserHeader",e.showLoginDlg="ShowLoginDlg",e.createPersonalInfoWnd="CreatePersonalInfoWnd",e.createRetryLoginWnd="CreateRetryLoginWnd",e.switchAccount="SwitchAccount",e.updateHeader="UpdateHeader",e.pingThroughXLUserServer="PingThroughXLUserServer",e.retryLogIn="RetryLogIn",e.createWebWnd="CreateWebWnd",e.isAutoLoginAw="IsAutoLoginAw",e.channel="Channel",e.getVipInfo="GetVipInfo",e.getUserInfoByKeyAw="GetUserInfoByKeyAw"}(t.LoginFunction||(t.LoginFunction={})),function(e){e.getDeviceID="GetDeviceID",e.getInitData="GetInitData",e.saveData="SaveData"}(t.LoginSDKFunction||(t.LoginSDKFunction={})),function(e){e.subtitleIsSupportJpeg="XmpSubtitleIsSupportJpeg",e.queryMediaInfo="QueryMediaInfo",e.openVideoEdit="OpenVideoEdit",e.openGifEdit="OpenGifEdit",e.createSnapShotEx="CreateSnapShotEx",e.createSnapShot="CreateSnapShot",e.createHDPicture="CreateHDPicture",e.isHDPicCreating="IsHDPicCreating",e.openClearBlack="OpenClearBlack",e.closeClearBlack="CloseClearBlack",e.clearBlackWindow="ClearBlackWindow",e.getBlackCollectCount="GetBlackCollectCount",e.getCollectData="GetCollectData",e.getClearBlackRect="GetClearBlackRect",e.createPreViewSnapshot="CreatePreViewSnapshot",e.getPreViewSnapshot="GetPreViewSnapshot"}(t.SnapshotFunction||(t.SnapshotFunction={})),function(e){e.showSystemMenu="ShowSystemMenu"}(t.MainMenuFunction||(t.MainMenuFunction={})),function(e){e.createButton="CreateButton",e.updateButton="UpdateButton",e.createWebview="CreateWebview",e.setDefaultVipSpeedStatus="SetDefaultVipSpeedStatus",e.updateVipSpeedStatus="UpdateVipSpeedStatus",e.getWebviewInfo="GetWebviewInfo",e.createVipView="CreateVipview",e.showCurveChart="showCurveChart",e.applyBrowserView="ApplyBrowserView",e.getBrowserViewId="GetBrowserViewId",e.initBrowserViewConfig="initBrowserViewConfig",e.createVipMenu="CreateVipMenu",e.getMenuSlot="GetMenuSlot",e.openNewTab="OpenNewTab",e.videoPause="DoVideoPause",e.videoPlay="DoVideoPlay",e.videoPauseEx="DoVideoPauseEx",e.videoPlayEx="DoVideoPlayEx",e.setPlaySpeed="SetPlaySpeed",e.setProgressColor="SetProgressColor",e.getProgressColor="GetProgressColor",e.showBufferingEntry="ShowBufferingEntry",e.isBufferingEntryShow="IsBufferingEntryShow",e.showOpenVipEntry="ShowOpenVipEntry",e.registerFunctionCall="RegisterFunctionCall",e.executeFunctionCall="ExecuteFunctionCall",e.videoGetPlayState="VideoGetPlayState",e.setXmpFocus="SetXmpFocus",e.leaveFullScreen="LeaveFullScreen"}(t.UIFunction||(t.UIFunction={})),function(e){e.addItem="VideoTipLeftTopAddItem",e.addLinkItem="VideoTipLeftTopAddLinkItem",e.updateItemText="VideoTipLeftTopUpdateItemText",e.removeItem="VideoTipLeftTopRemoveItem",e.showTip="VideoTipLeftTopShowTip",e.closeTip="VideoTipLeftTopCloseTip",e.hideTip="HideTip"}(t.VideoTipFunction||(t.VideoTipFunction={})),function(e){e.unKnow="UnKnow",e.localFile="LocalFile",e.downloadAndPlay="DownloadAndPlay",e.yunBo="YunBo"}(t.XmpPlayType||(t.XmpPlayType={})),function(e){e.init="XmpPlayerInit",e.showAPlayerWindow="XmpShowAPlayerWindow",e.createXmpMedia="XmpPlayerCreateXmpMedia",e.open="XmpPlayerOpen",e.openFile="XmpPlayerOpenFile",e.addFile="XmpPlayerAddFile",e.openFolder="XmpPlayerOpenFolder",e.openBt="XmpPlayerOpenBt",e.openUrl="XmpPlayerOpenUrl",e.openMedia="XmpPlayerOpenMedia",e.close="XmpPlayerClose",e.stop="XmpPlayerStop",e.play="XmpPlayerPlay",e.pause="XmpPlayerPause",e.getVersion="XmpPlayerGetVersion",e.getState="XmpPlayerGetState",e.isPlaying="XmpPlayerIsPlaying",e.getDuration="XmpPlayerGetDuration",e.getPosition="XmpPlayerGetPosition",e.setPosition="XmpPlayerSetPosition",e.getVideoWidth="XmpPlayerGetVideoWidth",e.getVideoHeight="XmpPlayerGetVideoHeight",e.getVolume="XmpPlayerGetVolume",e.setVolume="XmpPlayerSetVolume",e.isSeeking="XmpPlayerIsSeeking",e.getBufferProgress="XmpPlayerGetBufferProgress",e.getConfig="XmpPlayerGetConfig",e.setConfig="XmpPlayerSetConfig",e.setCurrentMedia="XmpPlayerSetCurrentMedia",e.getCurrentMedia="XmpPlayerGetCurrentMedia",e.getLastMediaId="XmpPlayerGetLastMediaId",e.getMediaAttribute="XmpPlayerMediaAttribute",e.getCurrentMediaAttribute="XmpPlayerGetCurrentMediaAttribute",e.clearCurrentMedia="XmpPlayerClearCurrentMedia",e.openPreMedia="XmpPlayerOpenPreMedia",e.openNextMedia="XmpPlayerOpenNextMedia",e.isPlayOrderFirstMedia="XmpPlayerIsPlayOrderFirstMedia",e.isPlayOrderLastMedia="XmpPlayerIsPlayOrderLastMedia",e.isFirstMedia="XmpPlayerIsFirstMedia",e.isLastMedia="XmpPlayerIsLastMedia",e.isMediasLoadFinished="XmpPlayerIsMediasLoadFinished",e.getMediasInfo="XmpPlayerGetMediasInfo",e.removeMedia="XmpPlayerRemoveMedia",e.removeAllMedia="XmpPlayerRemoveAllMedia",e.setASpectRatioAtive="XmpPlayerSetASpectRatioAtive",e.setPlayForward="XmpPlayerSetPlayForward",e.postionChange="XmpPlayerPostionChange",e.getBufferRange="XmpPlayerGetBufferRange",e.fireKeyDown="XmpPlayerFireKeyDown",e.fireKeyUp="XmpPlayerFireKeyUp",e.setAudioTime="XmpPlayerSetAudioTime",e.setProportion="XmpPlayerSetProportion",e.setVlomue="XmpPlayerSetVlomue",e.getCurView="XmpPlayerGetCurView",e.recordStat="XmpPlayerRecordStat",e.getHostWnd="XmpPlayerGetHostWnd",e.startWindowZorderChecker="StartWindowZorderChecker",e.stopWindowZorderChecker="StopWindowZorderChecker",e.setZOrderCheckerWindowHandle="SetZOrderCheckerWindowHandle",e.removeZOrderCheckerWindowHandle="RemoveZOrderCheckerWindowHandle",e.getVideoDuration="GetVideoDuration",e.getDownloadState="GetDownloadState",e.getResConfig="GetResConfig",e.queryTaskState="QueryTaskState",e.shareMediaInfo="ShareMediaInfo",e.getThunderDirState="GetThunderDirState",e.setThunderDirState="SetThunderDirState",e.showSyncDig="ShowSyncDig",e.isXmpLite="IsXmpLite",e.handleVipPlaySpeed="handleVipPlaySpeed",e.getMediaInfo="GetMediaInfo",e.getCurrentMediaInfo="GetCurrentMediaInfo",e.setMediaAttribute="XmpPlayerSetMediaAttribute",e.updateDownloadEmbedRect="UpdateDownloadEmbedRect",e.updatePanEmbedRect="UpdatePanEmbedRect",e.createIndependentWindow="CreateIndependentWindow",e.thunderResize="ThunderResize",e.updateScrollShowOrHide="UpdateScrollShowOrHide",e.updateFloatWindow="IpdateFloatWindow",e.updateAplayerHwnd="UpdateAplayerHwnd",e.updatePlaySource="UpdatePlaySource",e.setXmpCurrentMode="SetXmpCurrentMode",e.getXmpCurrentMode="GetXmpCurrentMode",e.isSwitchXmpMode="IsSwitchXmpMode",e.destroy="XmpDestroy",e.maximize="XmpMaximize",e.unmaximize="XmpUnmaximize",e.minmized="XmpMinmized",e.setPlaySpeed="SetPlaySpeed",e.getPlaySpeed="GetPlaySpeed",e.setWindowTopMode="SetWindowTopMode",e.calcelWindowTopMode="CalcelWindowTopMode",e.enterFullScreen="EnterFullScreen",e.leaveFullScreen="LeaveFullScreen",e.setFullcScreen="SetFullcScreen",e.getIsFullScreen="GetIsFullScreen",e.getIsFullScreenEx="GetIsFullScreenEx",e.setPlayingTaskId="SetPlayingTaskId",e.setlayPingPanFileId="SetPlayingPanFileId",e.setSelectTaskId="SetSelectTaskId",e.setSelectPanel="SetSelectPanel",e.setSelectTab="SetSelectTab",e.setSelectPanFileId="SetSelectPanFileId",e.getPlayingTaskId="GetPlayingTaskId",e.getPlayingPanFileId="GetPlayingPanFileId",e.getSelectTaskId="GetSelectTaskId",e.getSelectPanel="GetSelectPanel",e.getSelectTab="GetSelectTab",e.getSelectPanFileId="GetSelectPanFileId",e.getXmpPlayType="GetXmpPlayType",e.getDownloadAndPlayTaskData="GetDownloadAndPlayTaskData",e.openDLNA="OpenDLNA",e.disConnectDLNA="DisConnectDLNA",e.getCurrentDLNADeviceName="GetCurrentDLNADeviceName",e.addXmpVideoTipPos="AddXmpVideoTipPos",e.removeXmpVideoTipPos="RemoveXmpVideoTipPos",e.addXmpVideoTipPosMainRenderer="AddXmpVideoTipPosRenderer",e.removeXmpVideoTipPosMainRenderer="RemoveXmpVideoTipPosRenderer",e.switchSilent="XmpSwitchSilent",e.isSilent="XmpIsSilent",e.cancelDownloadCodecs="OnCancelDownloadCodecs",e.getCachedDownloadCodecInfo="OnGetCachedDownloadCodecInfo",e.onTaskStopped="OnTaskStopped",e.showOrHideXmpWindow="OnShowOrHideXmpWindow",e.getPlayingSoure="OnGetPlayingSoure",e.userLogout="OnUserLogout",e.isCurrentBuffingIsByDrag="IsCurrentBuffingIsByDrag",e.thunderQuit="ThunderQuit",e.getLastPlayError="GetLastPlayError",e.showAplayerAndFloatWindow="ShowAplayerAndFloatWindow",e.hideAplayerAndFloatWindow="HideAplayerAndFloatWindow",e.hideEmbedAplayerAndFloatWindow="HideEmbedAplayerAndFloatWindow",e.isPlayMusic="IsPlayMusic",e.getMediaById="GetMediaById",e.setPlayEndInfo="SetPlayEndInfo",e.getPlayId="GetPlayId",e.getPlayTick="GetPlayTick",e.setAudioTrack="SetAudioTrack",e.vipTellPopupWindowShow="VipTellPopupWindowShow",e.vipTellPopupWindowClose="VipTellPopupWindowClose"}(t.XmpPlayerFunction||(t.XmpPlayerFunction={})),function(e){e.doGetFileList="ZipDoGetFileList",e.doDelFileList="ZipDoDelFileList",e.abortGetFileList="ZipAbortGetFileList",e.openFile="ZipOpenFile",e.openPassWord="ZipOpenPassWord",e.openIndex="ZipOpenIndex",e.makeIndexFile="ZipMakeIndexFile",e.abortMakeIndexFile="ZipAbortMakeIndexFile",e.needWaitIndexFile="ZipNeedWaitIndexFile",e.isIndexError="ZipIsIndexError",e.isPasswordError="ZipIsPasswordError",e.isFilePlaying="ZipIsFilePlaying"}(t.ZipFunction||(t.ZipFunction={})),function(e){e.getMediaPlayed="PlayList.GetMediaPlayed",e.setMediaPlayed="PlayList.SetMediaPlayed"}(t.PlayListFunction||(t.PlayListFunction={})),function(e){e.isSubtitleShow="XmpSubtitleIsSubtitleShow",e.isSubtitle2Show="XmpSubtitleIsSubtitle2Show",e.showSubtitle="XmpSubtitleShowSubtitle",e.showSubtitle2="XmpSubtitleShowSubtitle2",e.getCurrentSubtitle="XmpSubtitleGetCurrentSubtitle",e.getCurrentSubtitle2="XmpSubtitleGetCurrentSubtitle2",e.getSubtitleList="XmpSubtitleGetSubtitleList",e.loadSubtitleFile="XmpSubtitleLoadSubtitleFile",e.setCurrentSubtitle="XmpSubtitleSetCurrentSubtitle",e.setSubtitleLanguageIndex="XmpSubtitleSetSubtitleLanguageIndex",e.setSubtitlePosition="XmpSubtitleSetSubtitlePosition",e.getSubtitlePosition="XmpSubtitleGetSubtitlePosition",e.setSubtitleFontStyle="XmpSubtitleSetSubtitleFontStyle",e.getSubtitleSize="XmpSubtitleGetSubtitleSize",e.getSubtitleColor="XmpSubtitleGetSubtitleColor",e.getSubtitleFont="XmpSubtitleGetSubtitleFont",e.setSubtitleTimming="XmpSubtitleSetSubtitleTimming",e.getSubtitleTimming="XmpSubtitleGetSubtitleTimming",e.setSubtitle3DMode="XmpSubtitleSetSubtitle3DMode",e.setUseHardware="XmpSubtitleSetUseHardware",e.reportSubTitle="ReportSubTitle",e.uploadSubTitle="UploadubTitle",e.onSubtitleInit="XmpSubtitleInit",e.onSubtitleLoadSuc="XmpSubtitleLoadSuc",e.createSuppress="CreateSuppress",e.getSuppressState="GetSuppressState",e.getSuppressPlayUrl="GetSuppressPlayUrl",e.getSuppressProgress="GetSuppressProgress",e.getSuppressNeedTime="GetSuppressNeedTime",e.showSubtitleSuppressDialog="ShowSubtitleSuppressDialog",e.continueSuppress="ContinueSuppress",e.getFullState="GetFullState",e.isSettingDefaultSecondSubtitle="IsSettingDefaultSecondSubtitle",e.isSecondSubtitle="IsSecondSubtitle"}(t.XmpSubtitleFunction||(t.XmpSubtitleFunction={})),function(e){e.getTaskInfo="GetTaskInfo",e.shellOpen="ShellOpen",e.openFolderWithSelectFile="OpenFolderWithSelectFile",e.getFileMd5="GetFileMd5",e.getPeerID="GetPeerID",e.getDeviceID="GetDeviceID",e.getThunderVersion="GetThunderVersion",e.getXmpVersion="GetXmpVersion",e.trackEvent="TrackEvent",e.getCurInternetState="GetCurInternetState",e.getCurPlatform="GetCurPlatform",e.getXmpShowMode="GetXmpShowMode",e.isModuleEnable="IsModuleEnable",e.updateTabSelectState="UpdateTabSelectState",e.getMouseShow="getMouseShow",e.updateFullFill="UpdateFullFill",e.getWindowTopMode="GetWindowTopMode",e.isVideoEditMode="IsVideoEditMode",e.showCtrlView="ShowCtrlView",e.showTopView="ShowTopView",e.showMoreAbout="ShowMoreAbout",e.setMenuPopUp="SetMenuPopUp",e.setWindowPopUp="SetWindowPopUp",e.isListExpand="IsListExpand",e.isMenuPopUp="IsMenuPopUp",e.isWindowPopUp="IsWindowPopUp",e.isMouseInFloat="IsMouseInFloat",e.switchtoNormalMode="SwitchtoNormalMode",e.switchToTransparentMode="SwitchToTransparentMode",e.dropOpenFiles="DropOpenFiles",e.dropOpenUrl="DropOpenUrl",e.getShowCondition="GetShowCondition",e.utf8StringEncodeToBinary="Utf8StringEncodeToBinary",e.utf8StringDecodeFromBinary="Utf8StringDecodeFromBinary",e.getErrorCodeConfigMessage="GetErrorCodeConfigMessage",e.quitProcess="QuitProcess",e.associate="Associate",e.checkXmpUpdate="CheckXmpUpdate",e.changeVideoRatio="ChangeVideoRatio",e.getVideoScaleRate="GetVideoScaleRate",e.getHoverPos="GetHoverPos",e.setHoverPos="SetHoverPos",e.getCurrentDevice="GetCurrentDevice",e.activeDevice="ActiveDevice",e.setCurrentDevice="SetCurrentDevice",e.getDeviceList="GetDeviceList",e.searchDevice="SearchDevice",e.isDeviceSupport="IsDeviceSupport",e.getInitUserLoginParam="GetInitUserLoginParam",e.handleMouseTimeOut="HandleMouseTimeOut",e.getOpenFrom="GetOpenFrom",e.setHideAndPauseWindow="SetHideAndPauseWindow",e.setHideWindowNotPause="SetHideWindowNotPause",e.setFullWindowMouseEnter="SetFullWindowMouseHover",e.setDragMove="SetDragMove",e.isEmbedMode="IsEmbedMode",e.getBackUpValue="getBackUpValue"}(t.XmpPluginFunction||(t.XmpPluginFunction={})),function(e){e.getVideoStuckVipData="GetVideoStuckVipData",e.openPayVipUrl="OpenPayVipUrl",e.actionPreOpen="ActionPreOpen"}(t.VipBufferTipFunction||(t.VipBufferTipFunction={})),function(e){e.showTimeTips="ShowTimeTips",e.destroyTimeTips="DestroyTimeTips"}(t.TimeTipFunction||(t.TimeTipFunction={})),function(e){e.loadSubtitle="LoadSubtitle",e.onlineSubtitleMatch="OnlineSubtitleMatch",e.showOrHideSubtitle="ShowOrHideSubtitle",e.isEnableHideSubtitle="IsEnableHideSubtitle",e.isSubtitleHided="IsSubtitleHided",e.loadEmbedSubtitle="LoadEmbedSubtitle",e.loadOnlineSubtitle="LoadOnlineSubtitle",e.loadLocalSubtitle="LoadLocalSubtitle",e.getOnlineSubtitleList="GetOnlineSubtitleList",e.getEmbedSubtitleList="GetEmbedSubtitleList",e.getLocalSubtitleList="GetLocalSubtitleList",e.getManualSubtitleList="GetManualSubtitleList",e.getCurrentTitle="GetCurrentTitle",e.getCurrentTitle2="GetCurrentTitle2",e.manualLoadSubtitle="ManualLoadSubtitle",e.setSubtitleFontStyle="SetSubtitleFontStyle",e.getSubtitleFontStyle="GetSubtitleFontStyle",e.setSubtitlePosition="SetSubtitlePosition",e.getSubtitlePosition="GetSubtitlePosition",e.setUseHardware="SetUseHardware",e.getSubtitleTimming="GetSubtitleTimming",e.setSubtitleTimming="SetSubtitleTimming"}(t.SubtitleFunction||(t.SubtitleFunction={}))},function(e,t){e.exports=require("child_process")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ModeChangeEvent=t.ClientProcessEvent=t.ZipEvent=t.XmpEvent=void 0,function(e){e.onWindowMessage="OnWindowMessage",e.onStateChanged="OnStateChanged",e.onCreateMediaError="OnCreateMediaError",e.onOpenSucceeded="OnOpenSucceeded",e.onVideoSizeChanged="OnVideoSizeChanged",e.onAPlayerCreated="OnAPlayerCreated",e.onAllStoped="OnAllStoped",e.onSeekCompleted="OnSeekCompleted",e.onBuffer="OnBuffer",e.onDownloadCodec="OnDownloadCodec",e.onDownloadCodecProgress="OnDownloadCodecProgress",e.onDropFiles="OnDropFiles",e.onEvent="OnEvent",e.onGetPlayUrl="OnGetPlayUrl",e.onOpenFailed="OnOpenFailed",e.onPlayFailed="OnPlayFailed",e.onClearError="OnClearError",e.onError="OnError",e.onPlaying="OnPlaying",e.onPreOpen="OnPreOpen",e.onPaused="OnPaused",e.onPlayCompleted="OnPlayCompleted",e.onPreStop="OnPreStop",e.onStop="OnStop",e.onForceLoadingState="OnForceLoadingState",e.onVipStruckPlayState="OnVipStruckPlayState",e.onShowFloatWindow="OnShowFloatWindow",e.onTimeDesChange="OnTimeDesChange",e.onPositionChanged="OnPositionChanged",e.onSnapShotCreated="OnSnapShotCreated",e.onXmpPlayerEvent="OnXmpPlayerEvent",e.onXmpAplayerCrash="OnXmpAplayerCrash",e.onUpdateMediaCid="OnUpdateMediaCid",e.onUpdateMediaGcid="OnUpdateMediaGcid",e.onSpeedChange="OnSpeedChange",e.onFullScreenStateChange="OnFullScreenStateChange",e.onSetConfig="OnSetConfig",e.onVolumeChange="OnVolumeChange",e.onPlayStateChange="OnPlayStateChange",e.onCurViewChanged="OnCurViewChanged",e.onUserLogin="OnUserLogin",e.onDwmCompositionChanged="OnDwmCompositionChanged",e.onDpiChanged="OnDpiChanged",e.onGPUProcessCrashed="OnGPUProcessCrashed",e.onSetCurrentDevice="OnSetCurrentDevice",e.onXmpPlayerRemoveMedias="OnXmpPlayerRemoveMedias",e.onOpenMediaNotExist="OnOpenMediaNotExist",e.onSwitchModeError="OnSwitchModeError",e.onDlnaEvent="OnDlnaEvent",e.onSubtitleInit="OnSubtitleInit",e.onSubtitleUnInit="onSubtitleUnInit",e.onEmbedSubInitFinish="OnEmbedSubInitFinish",e.onEntertCurrentDevice="OnEntertCurrentDevice",e.onWindowZOrderChanged="OnWindowZOrderChanged",e.onAplayerSeekFrameChanged="OnAplayerSeekFrameChanged",e.onModeBarChange="OnModeBarChange",e.onThunderTaskRemove="OnThunderTaskRemove",e.onXmpClose="OnXmpClose",e.onXmpStartFailed="OnXmpStartFailed",e.onVipViewInitFinish="OnVipViewInitFinish",e.onModeEvent="OnModeEvent",e.onMaxmized="OnMaxmized",e.onUnMaxmized="OnUnMaxmized",e.onSetWindowTopMost="OnWindowTopMost",e.onCancelWindowTopMost="OnCancelWindowTopMost",e.onOpenOrCloseSettingCenter="OnOpenOrCloseSettingCenter",e.onUpdateBufferingUI="OnUpdateBufferingUI",e.onOpenSucceededCreateTask="OnOpenSucceededCreateTask"}(t.XmpEvent||(t.XmpEvent={})),function(e){e.listCompleted="OnZipListCompleted",e.listCompletedError="OnZipListCompletedError",e.indexCompleted="OnZipIndexCompleted",e.passWordError="OnZipPassWordError",e.indexProgress="OnZipIndexProgress",e.indexError="OnZipIndexError",e.indexProgressBegin="OnZipIndexProgressBegin"}(t.ZipEvent||(t.ZipEvent={})),function(e){e.onMainWindowSize="OnMainWindowSize",e.onTabSelectChange="OnTabSelectChange",e.onThunderQuit="OnThunderQuit"}(t.ClientProcessEvent||(t.ClientProcessEvent={})),function(e){e.onXmpModeChanging="OnXmpModeChanging",e.onXmpModeChanged="OnXmpModeChanged",e.onXmpEnterFullScreen="OnXmpEnterFullScreen",e.onXmpLeaveFullScreen="OnXmpLeaveFullScreen"}(t.ModeChangeEvent||(t.ModeChangeEvent={}))},function(e,t){e.exports=require("os")},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function a(e){try{s(i.next(e))}catch(e){r(e)}}function l(e){try{s(i.throw(e))}catch(e){r(e)}}function s(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(a,l)}s((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(7),r=n(22),a=n(6),l=n(10);function s(e){l.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let d=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends o.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,i){do{if(t||(t=a.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(i)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))i("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{i("error",e)}),e.on("connect",()=>{i("connect")}),e.on("end",()=>{let t=e.isInprocess();i("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===a.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let o=new r.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=o,n||(this.client=o),o.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(o,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(o,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(o,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(o,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===o&&this.emit("stat_call_function_back",o.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let i=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,i));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),o.on("error",e=>{i&&i("error",e),this.emit("socket-error",e,o.getContext(),n,o.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),o.isInprocess()?(this.connectedMap[t.toLowerCase()]=o,i&&i("connect"),this.emit("connect",o.getContext(),n,!0)):o.on("connect",()=>{this.connectedMap[t.toLowerCase()]=o,i&&i("connect"),this.emit("connect",o.getContext(),n,!1)}),o.on("end",()=>{let e=o.isInprocess();l.information("server is ended, and this client emit end",t,n,e),i&&i("end",o.getContext(),n,e),this.emit("end",o.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(o)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return i(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return i(this,void 0,void 0,function*(){let n=null,i=yield this.callServerFunctionEx(e,...t);return i&&(n=i[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return console.info(this.client.getContext().name,"attachServerEvent",e),this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return i(this,void 0,void 0,function*(){let i=null,o=yield this.crossCallServerFunctionEx(e,t,...n);return o&&(i=o[0]),i})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(i,t,...n)}}crossIsRemoteClientExist(e,t){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(i,t,n)}})}crossCallRemoteClientFunction(e,t,n,...i){{if(!e)throw new Error("An argument for 'productId' was not provided");let o=this.singletonMap[e.toLowerCase()];if(!o)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(o,t,n,...i)}}crossAttachServerEvent(e,t,n){let i=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let o=this.singletonMap[e.toLowerCase()];if(!o)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");i=this.internalAttachServerEvent(o,t,n)}return i}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(i,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(i,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,i)=>{do{if(!t){n(!1);break}let i=this.generateId();const o={alias:t,action:"register",rid:i};let r=(e,i)=>{e?(l.error("register error",e.message),n(i)):n(t)};this.retCallbackMap[i]=Object.assign({callback:r},o),this.sendAdapter(e,o)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(l.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===a.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,i)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let i=this.generateId();const o={action:"check_server_function_exist",method:t,rid:i};let r=(e,t)=>{n(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:r},o),this.sendAdapter(e,o)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((i,o)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let o=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const r={rid:o,method:t,args:n};let a=(t,n)=>{t?(l.error("callServerFunction error",t,e.getContext()),i([null,t])):i([n,void 0])};this.retCallbackMap[o]=Object.assign({callback:a},r),this.sendAdapter(e,r)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,i)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let i=this.generateId();const o={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:i};let r=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[i]=Object.assign({callback:r},o),this.sendAdapter(e,o)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((i,o)=>{do{if(!e){i(!1);break}if(!t){i(!1);break}if(!n){i(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){i(!(!this.apis||!this.apis[n]));break}let o=this.generateId();const r={action:"check_client_function_exist",method:n,rid:o,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let a=(e,t)=>{i(!e&&t)};this.retCallbackMap[o]=Object.assign({callback:a},r),this.sendAdapter(e,r)}while(0)})}internalCallRemoteClientFunction(e,t,n,...i){return new Promise((o,r)=>{do{if(!e){o([null,"client doesn't ready"]);break}if(!t){o([null,"remote client alias is not specifed"]);break}if(!n){o([null,"funcName is not specifed"]);break}let r=(e,t)=>{e?(l.information("callRemoteClientFunction",e.message),o([null,e])):o([t,void 0])};if(i)for(let e=0;e<i.length;e++)i[e]=this.convertFunction2IdEx(i[e]);let a=this.generateId();const s={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:i,rid:a};this.retCallbackMap[a]=Object.assign({callback:r},s),this.sendAdapter(e,s)}while(0)})}internalAttachServerEvent(e,t,n){let i=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(i)||(this.eventCallbackMaps[i]={}),this.eventCallbackMaps[i].hasOwnProperty(t)||(this.eventCallbackMaps[i][t]={}),a.isObjectEmpty(this.eventCallbackMaps[i][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let o=this.generateId();return this.eventCallbackMaps[i][t][o]=n,o}internalDetachServerEvent(e,t,n){let i=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(i))break;if(!this.eventCallbackMaps[i].hasOwnProperty(t))break;delete this.eventCallbackMaps[i][t][n],a.isObjectEmpty(this.eventCallbackMaps[i][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let i=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(i))break;if(!this.eventCallbackMaps[i].hasOwnProperty(t))break;let e=this.eventCallbackMaps[i][t];for(let t in e){let i=e[t];i&&i.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...i){let o=void 0,r=!1;do{const a=this.contextCallbackMap[t];if(!a){l.error("the context function has been freeer",t),o={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let s=void 0,d=void 0;try{s=a.apply(null,...i)}catch(e){d=e.message;break}if(void 0===n||null===n)break;if(o={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==d){o.error=d;break}if(s&&s.then){s.then(t=>{o.data=this.convertFunction2IdEx(t),o.success=!0,this.sendAdapter(e,o)}).catch(t=>{o.error=t instanceof Error?t.message:t,this.sendAdapter(e,o)}),r=!0;break}o.success=!0,o.data=this.convertFunction2IdEx(s)}while(0);!r&&o&&this.sendAdapter(e,o)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let i=this.generateId();this.contextCallbackMap[i]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":i}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let i=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(i){n=((...e)=>{this.callRemoteContextById(t,i,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:i},s);break}for(let n in e){let i=e[n];e[n]=this.decodeParameter(i,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,i=!1;do{if(!t)break;let o=t.method;if(!o)break;let r=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(r):[].concat(r)},!this.apis||!this.apis[o]){n.error=`callRegisterFunction ${o} is undefined`;break}let a=void 0,l=this.decodeParameter(t.args,t.src);try{a=this.apis[o].apply(null,[t.src].concat(l))}catch(e){n.error=e.message;break}if(a&&a.then){a.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),i=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(a)}while(0);l.information("callRegisterFunction",n),!i&&n&&this.sendAdapter(e,n)}}),d=global.__xdasIPCClienInstance,t.client=d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(23),o=n(7),r=n(10),a=n(24),l=n(6);t.Client=class extends o.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=l.getSockPath(e.socketPrex);this.socket=i.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new a.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{r.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){r.error(e.message)}else r.information("This socket has been ended by the other party",this.context&&this.context.name)}}},function(e,t){e.exports=require("net")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(25),o=n(7);t.Parser=class extends o.EventEmitter{constructor(){super(),this.decoder=new i.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,i=(t+=this.decoder.write(e)).indexOf("\n",n);for(;i>=0;){const e=t.slice(n,i),o=JSON.parse(e);this.emit("message",o),n=i+1,i=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},function(e,t){e.exports=require("string_decoder")},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function a(e){try{s(i.next(e))}catch(e){r(e)}}function l(e){try{s(i.throw(e))}catch(e){r(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,l)}s((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.confirm=t.initMessageBox=void 0;const o=n(3),r=n(9),a=n(27),l=n(2);let s={};function d(e,t={},n,l,d){return i(this,void 0,void 0,function*(){let i=Object.assign({width:400,height:174,frame:!1,resizable:!1,maximizable:!1,parent:n,show:!1,modal:!0,alwaysOnTop:!0,title:"消息提示",webPreferences:{nodeIntegration:!0}},t);if(i.modal&&s.confirm)return;i.modal&&(s.confirm=!0);let l="development"===process.env.RUN_ENV?"http://localhost:9080/message-box-renderer/":`file:///${__dirname}/message-box-renderer/index.html`;if(l+=`?boxId=${e.boxId}`,e.parentID){let t=o.BrowserWindow.fromId(e.parentID);t&&(i.parent=t,n=t)}let d=new o.BrowserWindow(i);yield d.loadURL(l),d.on("ready-to-show",()=>{d.setAlwaysOnTop(!1),r.roundRectWindow(d,n),d.webContents.once("crashed",(e,t)=>{d.isDestroyed()||d.close(),t||a.CrashHandlerNS.recordCrashEvent("confirm-msg-crashed",l)})}),d.on("close",()=>{s.confirm=!1,n.focus()})})}t.initMessageBox=function(e,t){const n={};let c=new Map;o.ipcMain.on("xmp-message-box-confirm-create",(t,n)=>i(this,void 0,void 0,function*(){yield d(n.options,n.dialogConf,e)})),o.ipcMain.on("xmp-message-box-pop-create",(t,n)=>i(this,void 0,void 0,function*(){let t=Object.assign({frame:!1,resizable:!1,maximizable:!1,parent:e,show:!1,modal:!0,alwaysOnTop:!0,title:"消息提示",webPreferences:{nodeIntegration:!0}},n.dialogConf);if(t.modal&&s.pop)return;t.modal&&(s.pop=!0);let i="development"===process.env.RUN_ENV?"http://localhost:9080/message-box-renderer/":`file:///${__dirname}/message-box-renderer/index.html`;i+=`?boxId=${n.boxId}`;let l=new o.BrowserWindow(t);yield l.loadURL(i),l.on("ready-to-show",()=>{l.setAlwaysOnTop(!1),r.roundRectWindow(l,e),l.webContents.once("crashed",(e,t)=>{l.isDestroyed()||l.close(),t||a.CrashHandlerNS.recordCrashEvent("pop-msg-crashed",i)}),l.show()}),l.on("close",()=>{s.pop=!1,e.focus()})})),o.ipcMain.on("xmp-message-box-custom-create",(t,d)=>i(this,void 0,void 0,function*(){let t="#FFF";d.backgroundColor&&(t=d.backgroundColor);let i=Object.assign({width:300,height:100,frame:!1,resizable:!1,maximizable:!1,parent:e,show:!1,modal:!0,alwaysOnTop:!1,backgroundColor:t,title:"消息提示",webPreferences:{nodeIntegration:!0}},d.dialogConf);if(d.options.singleton){if(c.get(d.popType))return void n[d.popType].show();c.set(d.popType,!0)}if(i.modal&&s[d.popType])return;if(i.modal&&(s[d.popType]=!0),d.options.parentID){let e=o.BrowserWindow.fromId(d.options.parentID);e&&(i.parent=e)}let u="development"===process.env.RUN_ENV?"http://localhost:9080/message-box-renderer/":`file:///${__dirname}/message-box-renderer/index.html`;u+=`?boxId=${d.boxId}`;let h=new o.BrowserWindow(i);yield h.loadURL(u),h.on("ready-to-show",()=>{h.setAlwaysOnTop(!!d.options.topMost),d.options.noshadow||r.roundRectWindow(h,e),h.webContents.once("crashed",(e,t)=>{h.isDestroyed()||h.close(),t||a.CrashHandlerNS.recordCrashEvent("custom-msg-crashed",u)})}),h.on("close",()=>{s[d.popType]=!1,d.options.singleton&&c.set(d.popType,!1);let t=global;t.mainRendererWindow&&!t.mainRendererWindow.isDestroyed()&&t.mainRendererWindow.send(l.ThunderChannelList.channelMessageBoxClose),e&&!e.isDestroyed()&&e.focus()}),n[d.popType]=h})),o.ipcMain.on("xmp-message-box-open",(e,t)=>{const i=t.name,o=t.position;n[i]&&(o&&n[i].setPosition(o.x,o.y),n[i].show())})},t.confirm=function(e,t,n,r){return i(this,void 0,void 0,function*(){let i=String(Math.random()).replace(/\D/,"");return o.ipcMain.once(`xmp-message-box-init-${i}`,t=>{t.sender.send("xmp-message-box-init-reply",{popType:"confirm",options:e})}),yield d(e,t,n),new Promise(e=>{o.ipcMain.once(`xmp-message-box-resolve-${i}`,(t,n,i)=>{e({action:n,checkboxChecked:i})})})})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CrashHandlerNS=void 0,function(e){e.recordCrashEvent=function(e,t){e&&(e=e+"-"+(new Date).getTime().toString())}}(t.CrashHandlerNS||(t.CrashHandlerNS={}))},function(e){e.exports=JSON.parse('{"name":"XmpPlugin","version":"0.2.1","author":"Xunlei","license":"","main":"0.2.1/loadPlugin.js","clear":true,"monitor":false}')}]);