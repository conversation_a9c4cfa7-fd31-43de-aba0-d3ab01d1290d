"""
植物大战僵尸内存工具 - 主程序
功能完整的游戏内存读取和修改工具，支持图形界面操作
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import sys
import os
from typing import Dict, List, Any, Optional

# 导入自定义模块
from memory_engine import MemoryEngine
from game_definitions import PVZDefinitions, GameVersion
from cheat_functions import CheatManager
from utils import logger, config_manager, performance_monitor, DataFormatter, safe_execute

class MemoryToolGUI:
    """内存工具图形界面类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.memory_engine = MemoryEngine()
        self.game_definitions = PVZDefinitions()
        self.cheat_manager = None
        
        # 界面状态
        self.is_attached = False
        self.monitoring_active = False
        self.update_thread = None
        self.stop_monitoring = False
        
        # 数据存储
        self.current_values = {}
        self.entity_data = {"plants": [], "zombies": []}
        
        self.setup_ui()
        self.setup_bindings()
        
        # 自动附加进程
        if config_manager.get("game.auto_attach", True):
            self.auto_attach_process()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主窗口配置
        self.root.title(config_manager.get("window.title", "植物大战僵尸内存工具"))
        self.root.geometry(f"{config_manager.get('window.width', 800)}x{config_manager.get('window.height', 600)}")
        self.root.resizable(True, True)
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建顶部控制面板
        self.create_control_panel()
        
        # 创建标签页
        self.create_notebook()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_control_panel(self):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(self.main_frame, text="进程控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 进程信息
        info_frame = ttk.Frame(control_frame)
        info_frame.pack(fill=tk.X)
        
        ttk.Label(info_frame, text="进程名称:").pack(side=tk.LEFT)
        self.process_name_var = tk.StringVar(value=self.game_definitions.process_name)
        process_entry = ttk.Entry(info_frame, textvariable=self.process_name_var, width=20)
        process_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        # 控制按钮
        self.attach_button = ttk.Button(info_frame, text="附加进程", command=self.attach_process)
        self.attach_button.pack(side=tk.LEFT, padx=5)
        
        self.detach_button = ttk.Button(info_frame, text="分离进程", command=self.detach_process, state=tk.DISABLED)
        self.detach_button.pack(side=tk.LEFT, padx=5)
        
        self.refresh_button = ttk.Button(info_frame, text="刷新数据", command=self.refresh_data, state=tk.DISABLED)
        self.refresh_button.pack(side=tk.LEFT, padx=5)
        
        # 状态指示器
        self.status_label = ttk.Label(info_frame, text="未连接", foreground="red")
        self.status_label.pack(side=tk.RIGHT, padx=10)
    
    def create_notebook(self):
        """创建标签页"""
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 游戏状态标签页
        self.create_game_status_tab()
        
        # 外挂功能标签页
        self.create_cheat_tab()
        
        # 实体监控标签页
        self.create_entity_tab()
        
        # 内存编辑标签页
        self.create_memory_tab()
        
        # 日志标签页
        self.create_log_tab()
    
    def create_game_status_tab(self):
        """创建游戏状态标签页"""
        self.game_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.game_frame, text="游戏状态")
        
        # 资源信息
        resources_frame = ttk.LabelFrame(self.game_frame, text="游戏资源", padding=10)
        resources_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 创建资源显示网格
        self.resource_vars = {}
        resource_items = [
            ("阳光", "sun_count"),
            ("金钱", "money"),
            ("关卡", "level"),
            ("波数", "wave_count")
        ]
        
        for i, (label, key) in enumerate(resource_items):
            row = i // 2
            col = (i % 2) * 2
            
            ttk.Label(resources_frame, text=f"{label}:").grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)
            var = tk.StringVar(value="--")
            self.resource_vars[key] = var
            ttk.Label(resources_frame, textvariable=var, font=("Consolas", 10)).grid(row=row, column=col+1, sticky=tk.W, padx=5, pady=2)
        
        # 实体统计
        entities_frame = ttk.LabelFrame(self.game_frame, text="实体统计", padding=10)
        entities_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.entity_vars = {}
        entity_items = [
            ("植物数量", "plant_count"),
            ("僵尸数量", "zombie_count")
        ]
        
        for i, (label, key) in enumerate(entity_items):
            ttk.Label(entities_frame, text=f"{label}:").grid(row=0, column=i*2, sticky=tk.W, padx=5, pady=2)
            var = tk.StringVar(value="--")
            self.entity_vars[key] = var
            ttk.Label(entities_frame, textvariable=var, font=("Consolas", 10)).grid(row=0, column=i*2+1, sticky=tk.W, padx=5, pady=2)
    
    def create_cheat_tab(self):
        """创建外挂功能标签页"""
        self.cheat_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.cheat_frame, text="外挂功能")
        
        # 基础外挂
        basic_frame = ttk.LabelFrame(self.cheat_frame, text="基础功能", padding=10)
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.cheat_vars = {}
        
        # 无限阳光
        sun_frame = ttk.Frame(basic_frame)
        sun_frame.pack(fill=tk.X, pady=2)
        
        self.cheat_vars["infinite_sun"] = tk.BooleanVar()
        sun_check = ttk.Checkbutton(sun_frame, text="无限阳光", variable=self.cheat_vars["infinite_sun"], 
                                   command=lambda: self.toggle_cheat("infinite_sun"))
        sun_check.pack(side=tk.LEFT)
        
        ttk.Label(sun_frame, text="数量:").pack(side=tk.LEFT, padx=(20, 5))
        self.sun_amount_var = tk.StringVar(value=str(config_manager.get("cheats.default_sun_amount", 9990)))
        sun_entry = ttk.Entry(sun_frame, textvariable=self.sun_amount_var, width=10)
        sun_entry.pack(side=tk.LEFT)
        
        # 无限金钱
        money_frame = ttk.Frame(basic_frame)
        money_frame.pack(fill=tk.X, pady=2)
        
        self.cheat_vars["infinite_money"] = tk.BooleanVar()
        money_check = ttk.Checkbutton(money_frame, text="无限金钱", variable=self.cheat_vars["infinite_money"],
                                     command=lambda: self.toggle_cheat("infinite_money"))
        money_check.pack(side=tk.LEFT)
        
        ttk.Label(money_frame, text="数量:").pack(side=tk.LEFT, padx=(20, 5))
        self.money_amount_var = tk.StringVar(value=str(config_manager.get("cheats.default_money_amount", 99999)))
        money_entry = ttk.Entry(money_frame, textvariable=self.money_amount_var, width=10)
        money_entry.pack(side=tk.LEFT)
        
        # 其他外挂功能
        other_cheats = [
            ("plant_invincible", "植物无敌"),
            ("zombie_weak", "僵尸秒杀"),
            ("freeze_zombies", "冻结僵尸"),
            ("no_cooldown", "无冷却时间")
        ]
        
        for cheat_key, cheat_name in other_cheats:
            self.cheat_vars[cheat_key] = tk.BooleanVar()
            check = ttk.Checkbutton(basic_frame, text=cheat_name, variable=self.cheat_vars[cheat_key],
                                   command=lambda k=cheat_key: self.toggle_cheat(k))
            check.pack(anchor=tk.W, pady=2)
        
        # 快捷操作
        quick_frame = ttk.LabelFrame(self.cheat_frame, text="快捷操作", padding=10)
        quick_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(quick_frame, text="启用所有外挂", command=self.enable_all_cheats).pack(side=tk.LEFT, padx=5)
        ttk.Button(quick_frame, text="禁用所有外挂", command=self.disable_all_cheats).pack(side=tk.LEFT, padx=5)
        ttk.Button(quick_frame, text="保存配置", command=self.save_cheat_config).pack(side=tk.LEFT, padx=5)
    
    def create_entity_tab(self):
        """创建实体监控标签页"""
        self.entity_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.entity_frame, text="实体监控")
        
        # 创建分割窗口
        paned = ttk.PanedWindow(self.entity_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 植物列表
        plant_frame = ttk.LabelFrame(paned, text="植物列表", padding=5)
        paned.add(plant_frame, weight=1)
        
        self.plant_tree = ttk.Treeview(plant_frame, columns=("type", "pos", "health"), show="tree headings")
        self.plant_tree.heading("#0", text="ID")
        self.plant_tree.heading("type", text="类型")
        self.plant_tree.heading("pos", text="位置")
        self.plant_tree.heading("health", text="生命值")
        
        self.plant_tree.column("#0", width=50)
        self.plant_tree.column("type", width=100)
        self.plant_tree.column("pos", width=80)
        self.plant_tree.column("health", width=80)
        
        plant_scroll = ttk.Scrollbar(plant_frame, orient=tk.VERTICAL, command=self.plant_tree.yview)
        self.plant_tree.configure(yscrollcommand=plant_scroll.set)
        
        self.plant_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        plant_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 僵尸列表
        zombie_frame = ttk.LabelFrame(paned, text="僵尸列表", padding=5)
        paned.add(zombie_frame, weight=1)
        
        self.zombie_tree = ttk.Treeview(zombie_frame, columns=("type", "pos", "health"), show="tree headings")
        self.zombie_tree.heading("#0", text="ID")
        self.zombie_tree.heading("type", text="类型")
        self.zombie_tree.heading("pos", text="位置")
        self.zombie_tree.heading("health", text="生命值")
        
        self.zombie_tree.column("#0", width=50)
        self.zombie_tree.column("type", width=100)
        self.zombie_tree.column("pos", width=80)
        self.zombie_tree.column("health", width=80)
        
        zombie_scroll = ttk.Scrollbar(zombie_frame, orient=tk.VERTICAL, command=self.zombie_tree.yview)
        self.zombie_tree.configure(yscrollcommand=zombie_scroll.set)
        
        self.zombie_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        zombie_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_memory_tab(self):
        """创建内存编辑标签页"""
        self.memory_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.memory_frame, text="内存编辑")
        
        # 地址输入
        addr_frame = ttk.LabelFrame(self.memory_frame, text="内存地址", padding=10)
        addr_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(addr_frame, text="地址:").pack(side=tk.LEFT)
        self.address_var = tk.StringVar()
        addr_entry = ttk.Entry(addr_frame, textvariable=self.address_var, width=15)
        addr_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(addr_frame, text="类型:").pack(side=tk.LEFT, padx=(10, 5))
        self.data_type_var = tk.StringVar(value="int32")
        type_combo = ttk.Combobox(addr_frame, textvariable=self.data_type_var, 
                                 values=["int32", "float", "string", "bytes"], width=10, state="readonly")
        type_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(addr_frame, text="读取", command=self.read_memory_value).pack(side=tk.LEFT, padx=5)
        ttk.Button(addr_frame, text="写入", command=self.write_memory_value).pack(side=tk.LEFT, padx=5)
        
        # 值显示和编辑
        value_frame = ttk.LabelFrame(self.memory_frame, text="数值编辑", padding=10)
        value_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(value_frame, text="当前值:").pack(anchor=tk.W)
        self.current_value_var = tk.StringVar()
        current_entry = ttk.Entry(value_frame, textvariable=self.current_value_var, state="readonly", width=50)
        current_entry.pack(fill=tk.X, pady=2)
        
        ttk.Label(value_frame, text="新值:").pack(anchor=tk.W, pady=(10, 0))
        self.new_value_var = tk.StringVar()
        new_entry = ttk.Entry(value_frame, textvariable=self.new_value_var, width=50)
        new_entry.pack(fill=tk.X, pady=2)
    
    def create_log_tab(self):
        """创建日志标签页"""
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="日志")
        
        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(self.log_frame, wrap=tk.WORD, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 日志控制
        log_control_frame = ttk.Frame(self.log_frame)
        log_control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(log_control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        
        # 重定向日志输出到GUI
        self.setup_log_redirect()
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_text = tk.StringVar(value="就绪")
        status_label = ttk.Label(self.status_frame, textvariable=self.status_text)
        status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 进度条
        self.progress = ttk.Progressbar(self.status_frame, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=5, pady=2)
    
    def setup_bindings(self):
        """设置事件绑定"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 键盘快捷键
        self.root.bind("<F5>", lambda e: self.refresh_data())
        self.root.bind("<Control-q>", lambda e: self.on_closing())
    
    def setup_log_redirect(self):
        """设置日志重定向"""
        class LogRedirector:
            def __init__(self, text_widget):
                self.text_widget = text_widget
            
            def write(self, message):
                if message.strip():
                    self.text_widget.insert(tk.END, message)
                    self.text_widget.see(tk.END)
            
            def flush(self):
                pass
        
        # 重定向标准输出到日志窗口
        sys.stdout = LogRedirector(self.log_text)

    def auto_attach_process(self):
        """自动附加进程"""
        def try_attach():
            process_name = self.process_name_var.get()
            if self.memory_engine.find_process(process_name):
                self.on_process_attached()
                logger.info(f"自动附加到进程: {process_name}")
            else:
                # 如果没找到进程，5秒后重试
                self.root.after(5000, try_attach)

        try_attach()

    def attach_process(self):
        """附加到进程"""
        process_name = self.process_name_var.get()
        if not process_name:
            messagebox.showerror("错误", "请输入进程名称")
            return

        self.status_text.set("正在附加进程...")
        self.progress.start()

        def attach_thread():
            try:
                if self.memory_engine.find_process(process_name):
                    self.root.after(0, self.on_process_attached)
                    logger.info(f"成功附加到进程: {process_name} (PID: {self.memory_engine.process_id})")
                else:
                    self.root.after(0, lambda: self.on_attach_failed(f"未找到进程: {process_name}"))
            except Exception as e:
                self.root.after(0, lambda: self.on_attach_failed(str(e)))

        threading.Thread(target=attach_thread, daemon=True).start()

    def detach_process(self):
        """分离进程"""
        if self.cheat_manager:
            self.cheat_manager.stop_all_cheats()

        self.stop_monitoring = True
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1)

        self.memory_engine.close()
        self.cheat_manager = None
        self.is_attached = False
        self.monitoring_active = False

        # 更新UI状态
        self.attach_button.config(state=tk.NORMAL)
        self.detach_button.config(state=tk.DISABLED)
        self.refresh_button.config(state=tk.DISABLED)
        self.status_label.config(text="未连接", foreground="red")
        self.status_text.set("已断开连接")
        self.progress.stop()

        # 清空数据显示
        self.clear_data_display()

        logger.info("已断开进程连接")

    def on_process_attached(self):
        """进程附加成功回调"""
        self.is_attached = True
        self.cheat_manager = CheatManager(self.memory_engine, self.game_definitions)

        # 更新UI状态
        self.attach_button.config(state=tk.DISABLED)
        self.detach_button.config(state=tk.NORMAL)
        self.refresh_button.config(state=tk.NORMAL)
        self.status_label.config(text=f"已连接 (PID: {self.memory_engine.process_id})", foreground="green")
        self.status_text.set("连接成功")
        self.progress.stop()

        # 开始监控
        self.start_monitoring()

    def on_attach_failed(self, error_msg: str):
        """进程附加失败回调"""
        self.status_text.set("连接失败")
        self.progress.stop()
        messagebox.showerror("连接失败", error_msg)
        logger.error(f"进程附加失败: {error_msg}")

    def start_monitoring(self):
        """开始监控游戏数据"""
        if self.monitoring_active:
            return

        self.monitoring_active = True
        self.stop_monitoring = False

        def monitor_loop():
            while not self.stop_monitoring and self.is_attached:
                try:
                    if not self.memory_engine.is_process_running():
                        self.root.after(0, self.on_process_lost)
                        break

                    # 更新游戏数据
                    self.update_game_data()

                    # 更新实体数据
                    if self.notebook.index(self.notebook.select()) == 2:  # 实体监控标签页
                        self.update_entity_data()

                    time.sleep(config_manager.get("game.scan_interval", 100) / 1000.0)

                except Exception as e:
                    logger.error(f"监控循环错误: {e}")
                    time.sleep(1)

            self.monitoring_active = False

        self.update_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.update_thread.start()

    def on_process_lost(self):
        """进程丢失回调"""
        messagebox.showwarning("进程丢失", "目标进程已结束")
        self.detach_process()

    def update_game_data(self):
        """更新游戏数据"""
        if not self.is_attached:
            return

        try:
            # 更新资源信息
            for key, var in self.resource_vars.items():
                addr_info = self.game_definitions.get_address_by_name(key)
                if addr_info:
                    actual_addr = self.memory_engine.get_pointer_chain(
                        self.memory_engine.base_address + addr_info.address,
                        addr_info.offsets
                    )

                    if actual_addr:
                        if addr_info.data_type == "int32":
                            value = self.memory_engine.read_int32(actual_addr)
                        elif addr_info.data_type == "float":
                            value = self.memory_engine.read_float(actual_addr)
                        else:
                            value = None

                        if value is not None:
                            self.current_values[key] = value
                            formatted_value = DataFormatter.format_number(value)
                            self.root.after(0, lambda v=formatted_value: var.set(v))

            # 更新实体统计
            for key, var in self.entity_vars.items():
                addr_info = self.game_definitions.get_address_by_name(key)
                if addr_info:
                    actual_addr = self.memory_engine.get_pointer_chain(
                        self.memory_engine.base_address + addr_info.address,
                        addr_info.offsets
                    )

                    if actual_addr:
                        value = self.memory_engine.read_int32(actual_addr)
                        if value is not None:
                            self.current_values[key] = value
                            str_value = str(value)
                            self.root.after(0, lambda v=str_value: var.set(v))

        except Exception as e:
            logger.error(f"更新游戏数据失败: {e}")

    def update_entity_data(self):
        """更新实体数据"""
        if not self.is_attached or not self.cheat_manager:
            return

        try:
            # 获取植物数据
            plants = self.cheat_manager.get_all_plants()
            self.entity_data["plants"] = plants

            # 获取僵尸数据
            zombies = self.cheat_manager.get_all_zombies()
            self.entity_data["zombies"] = zombies

            # 更新UI显示
            self.root.after(0, self.update_entity_display)

        except Exception as e:
            logger.error(f"更新实体数据失败: {e}")

    def update_entity_display(self):
        """更新实体显示"""
        # 清空现有数据
        for item in self.plant_tree.get_children():
            self.plant_tree.delete(item)

        for item in self.zombie_tree.get_children():
            self.zombie_tree.delete(item)

        # 添加植物数据
        for i, plant in enumerate(self.entity_data["plants"]):
            plant_type = plant.get("type", 0)
            type_name = self.game_definitions.get_plant_type_name(plant_type)
            pos = f"({plant.get('x', 0)}, {plant.get('y', 0)})"
            health = f"{plant.get('health', 0)}/{plant.get('max_health', 0)}"

            self.plant_tree.insert("", tk.END, text=str(i), values=(type_name, pos, health))

        # 添加僵尸数据
        for i, zombie in enumerate(self.entity_data["zombies"]):
            zombie_type = zombie.get("type", 0)
            type_name = self.game_definitions.get_zombie_type_name(zombie_type)
            pos = f"({zombie.get('x', 0):.1f}, {zombie.get('y', 0):.1f})"
            health = f"{zombie.get('health', 0)}/{zombie.get('max_health', 0)}"

            self.zombie_tree.insert("", tk.END, text=str(i), values=(type_name, pos, health))

    def refresh_data(self):
        """刷新数据"""
        if not self.is_attached:
            return

        self.status_text.set("正在刷新数据...")

        def refresh_thread():
            try:
                self.update_game_data()
                self.update_entity_data()
                self.root.after(0, lambda: self.status_text.set("数据刷新完成"))
            except Exception as e:
                self.root.after(0, lambda: self.status_text.set("刷新失败"))
                logger.error(f"数据刷新失败: {e}")

        threading.Thread(target=refresh_thread, daemon=True).start()

    def clear_data_display(self):
        """清空数据显示"""
        for var in self.resource_vars.values():
            var.set("--")

        for var in self.entity_vars.values():
            var.set("--")

        for item in self.plant_tree.get_children():
            self.plant_tree.delete(item)

        for item in self.zombie_tree.get_children():
            self.zombie_tree.delete(item)

        self.current_value_var.set("")
        self.new_value_var.set("")

    # 外挂功能方法
    def toggle_cheat(self, cheat_name: str):
        """切换外挂功能"""
        if not self.is_attached or not self.cheat_manager:
            messagebox.showwarning("警告", "请先附加到游戏进程")
            self.cheat_vars[cheat_name].set(False)
            return

        enabled = self.cheat_vars[cheat_name].get()

        try:
            if cheat_name == "infinite_sun":
                sun_amount = int(self.sun_amount_var.get())
                success = self.cheat_manager.infinite_sun(enabled, sun_amount)
            elif cheat_name == "infinite_money":
                money_amount = int(self.money_amount_var.get())
                success = self.cheat_manager.infinite_money(enabled, money_amount)
            elif cheat_name == "plant_invincible":
                success = self.cheat_manager.plant_invincible(enabled)
            elif cheat_name == "zombie_weak":
                success = self.cheat_manager.zombie_weak(enabled)
            elif cheat_name == "freeze_zombies":
                success = self.cheat_manager.freeze_zombies(enabled)
            elif cheat_name == "no_cooldown":
                success = self.cheat_manager.no_cooldown(enabled)
            else:
                success = False

            if success:
                status = "启用" if enabled else "禁用"
                logger.info(f"{status}外挂功能: {cheat_name}")
                self.status_text.set(f"外挂功能 {cheat_name} 已{status}")
            else:
                self.cheat_vars[cheat_name].set(not enabled)
                messagebox.showerror("错误", f"外挂功能 {cheat_name} 操作失败")

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数值")
            self.cheat_vars[cheat_name].set(False)
        except Exception as e:
            logger.error(f"外挂功能 {cheat_name} 操作失败: {e}")
            self.cheat_vars[cheat_name].set(False)
            messagebox.showerror("错误", f"操作失败: {str(e)}")

    def enable_all_cheats(self):
        """启用所有外挂功能"""
        if not self.is_attached:
            messagebox.showwarning("警告", "请先附加到游戏进程")
            return

        for cheat_name in self.cheat_vars:
            if not self.cheat_vars[cheat_name].get():
                self.cheat_vars[cheat_name].set(True)
                self.toggle_cheat(cheat_name)

        logger.info("已启用所有外挂功能")

    def disable_all_cheats(self):
        """禁用所有外挂功能"""
        if not self.is_attached:
            return

        for cheat_name in self.cheat_vars:
            if self.cheat_vars[cheat_name].get():
                self.cheat_vars[cheat_name].set(False)
                self.toggle_cheat(cheat_name)

        logger.info("已禁用所有外挂功能")

    def save_cheat_config(self):
        """保存外挂配置"""
        try:
            config_manager.set("cheats.default_sun_amount", int(self.sun_amount_var.get()))
            config_manager.set("cheats.default_money_amount", int(self.money_amount_var.get()))

            active_cheats = []
            for cheat_name, var in self.cheat_vars.items():
                if var.get():
                    active_cheats.append(cheat_name)

            config_manager.set("cheats.auto_enable", active_cheats)
            config_manager.save_config()

            messagebox.showinfo("成功", "外挂配置已保存")
            logger.info("外挂配置已保存")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            logger.error(f"保存外挂配置失败: {e}")

    # 内存编辑方法
    def read_memory_value(self):
        """读取内存值"""
        if not self.is_attached:
            messagebox.showwarning("警告", "请先附加到游戏进程")
            return

        try:
            address_str = self.address_var.get().strip()
            if address_str.startswith("0x") or address_str.startswith("0X"):
                address = int(address_str, 16)
            else:
                address = int(address_str)

            data_type = self.data_type_var.get()

            if data_type == "int32":
                value = self.memory_engine.read_int32(address)
            elif data_type == "float":
                value = self.memory_engine.read_float(address)
            elif data_type == "string":
                value = self.memory_engine.read_string(address)
            elif data_type == "bytes":
                data = self.memory_engine.read_memory(address, 16)
                value = DataFormatter.format_bytes(data) if data else None
            else:
                value = None

            if value is not None:
                self.current_value_var.set(str(value))
                logger.info(f"读取内存 {DataFormatter.format_address(address)}: {value}")
            else:
                self.current_value_var.set("读取失败")
                messagebox.showerror("错误", "读取内存失败")

        except ValueError:
            messagebox.showerror("错误", "请输入有效的内存地址")
        except Exception as e:
            messagebox.showerror("错误", f"读取内存失败: {str(e)}")
            logger.error(f"读取内存失败: {e}")

    def write_memory_value(self):
        """写入内存值"""
        if not self.is_attached:
            messagebox.showwarning("警告", "请先附加到游戏进程")
            return

        try:
            address_str = self.address_var.get().strip()
            if address_str.startswith("0x") or address_str.startswith("0X"):
                address = int(address_str, 16)
            else:
                address = int(address_str)

            new_value = self.new_value_var.get().strip()
            if not new_value:
                messagebox.showerror("错误", "请输入要写入的值")
                return

            data_type = self.data_type_var.get()
            success = False

            if data_type == "int32":
                value = int(new_value)
                success = self.memory_engine.write_int32(address, value)
            elif data_type == "float":
                value = float(new_value)
                success = self.memory_engine.write_float(address, value)
            elif data_type == "string":
                data = new_value.encode('utf-8') + b'\x00'
                success = self.memory_engine.write_memory(address, data)
            elif data_type == "bytes":
                # 解析十六进制字节
                hex_values = new_value.replace(" ", "").replace(",", "")
                if len(hex_values) % 2 != 0:
                    raise ValueError("十六进制字节数必须是偶数")
                data = bytes.fromhex(hex_values)
                success = self.memory_engine.write_memory(address, data)

            if success:
                logger.info(f"写入内存 {DataFormatter.format_address(address)}: {new_value}")
                messagebox.showinfo("成功", "内存写入成功")
                # 重新读取验证
                self.read_memory_value()
            else:
                messagebox.showerror("错误", "内存写入失败")

        except ValueError as e:
            messagebox.showerror("错误", f"数值格式错误: {str(e)}")
        except Exception as e:
            messagebox.showerror("错误", f"写入内存失败: {str(e)}")
            logger.error(f"写入内存失败: {e}")

    # 日志方法
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已保存到: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")

    def on_closing(self):
        """程序关闭事件"""
        if self.is_attached:
            self.detach_process()

        # 保存窗口配置
        config_manager.set("window.width", self.root.winfo_width())
        config_manager.set("window.height", self.root.winfo_height())
        config_manager.save_config()

        self.root.destroy()

    def run(self):
        """运行主程序"""
        try:
            logger.info("植物大战僵尸内存工具启动")
            self.root.mainloop()
        except Exception as e:
            logger.error(f"程序运行错误: {e}")
            messagebox.showerror("错误", f"程序运行错误: {str(e)}")

def main():
    """主函数"""
    try:
        # 检查是否以管理员权限运行
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            messagebox.showwarning("权限警告", "建议以管理员权限运行此程序以获得最佳性能")

        # 创建并运行GUI
        app = MemoryToolGUI()
        app.run()

    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        messagebox.showerror("启动失败", f"程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()
