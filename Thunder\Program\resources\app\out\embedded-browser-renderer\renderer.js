module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=995)}({0:function(e,t,n){"use strict";function r(e,t,n,r,i,o,s,a){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),o&&(c._scopeId="data-v-"+o),s?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},c._ssrRegister=l):i&&(l=a?function(){i.call(this,this.$root.$options.shadowRoot)}:i),l)if(c.functional){c._injectStyles=l;var d=c.render;c.render=function(e,t){return l.call(t),d(e,t)}}else{var u=c.beforeCreate;c.beforeCreate=u?[].concat(u,l):[l]}return{exports:e,options:c}}n.d(t,"a",function(){return r})},1:function(e,t,n){e.exports=n(9)(137)},1000:function(e,t,n){"use strict";var r=n(1299);n.n(r).a},1001:function(e,t,n){"use strict";var r=n(1301);n.n(r).a},11:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(8),s=n(1).default.getLogger("XLStat");let a=o.default(i.join(__rootDir,"../bin/ThunderHelper.node"));function l(e=""){let t;if("string"==typeof e)t=e;else if(c(e)&&"object"==typeof e){let n=[];for(let t in e)c(e[t])&&n.push(t+"="+encodeURIComponent(e[t]));t=n.join(",")}return t}function c(e){return void 0!==e&&null!==e}!function(e){let t=null;function n(){return t||(t=a.xlstat4),t}function i(e,t="",i="",o=0,a=0,c=0,d=0,u="",f=0){return r(this,void 0,void 0,function*(){let r=0;do{if(void 0===e){r=1;break}let h=l(u);r="browser"===process.type?yield new Promise(s=>{r=n().asyncTrackEvent(e,t,i,o,a,c,d,h,f,e=>{s(e)})}):n().trackEvent(e,t,i,o,a,c,d,h,f),s.information(e,t,i,o,a,c,d,h,f)}while(0);return r})}function o(e,t=0){do{if(void 0===e)break;"browser"!==process.type&&n().trackClick(e,t)}while(0)}e.asyncTrackEvent=i,e.trackEvent=function(e,t="",n="",r=0,o=0,s=0,a=0,l="",c=0){i(e,t,n,r,o,s,a,l,c).catch()},e.trackEventEx=function(e,t="",n="",r=0){i(e,t,"",0,0,0,0,n,r).catch()},e.trackClick=o,e.trackShow=function(e,t=0){o(e,t)},e.setUserID=function(e=0,t=0){"browser"!==process.type&&n().setUserID(e,t)},e.initParam=function(e){return r(this,void 0,void 0,function*(){let t=-1;return t="browser"===process.type?yield new Promise(t=>{n().asyncInitParam(e,(e,n)=>{t(e?n:-1)})}):yield new Promise(t=>{n().initParamRemote(e,e=>{t(e)})})})},e.asyncUninit=function(e){return r(this,void 0,void 0,function*(){"browser"===process.type&&(yield new Promise(t=>{n().asyncUninit(e,()=>{t()})}))})},e.uninit=function(){"browser"===process.type&&n().waitFinish()}}(t.XLStatNS||(t.XLStatNS={}))},1165:function(e,t){},12:function(e,t){e.exports=require("events")},1297:function(e,t){},1299:function(e,t){},13:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const r=n(2);let i,o=console;function s(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,i=(new Error).stack.split("\n").slice(e+1);i.shift();let o=[];return i.forEach((e,i)=>{let s=t.exec(e)||n.exec(e),a={};s&&5===s.length&&(a.method=s[1],a.path=s[2],a.line=s[3],a.pos=s[4],a.file=r.basename(a.path),o.push(a))}),o}if(i="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return s(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){o.time(...e)},t.timeEnd=function(...e){o.timeEnd(...e)},t.trace=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.trace(i,...e)},t.info=function(...e){let t=s(),n="anonymous";t[0]&&t[0].method&&(n=n),o.info(i,"["+n+"]",e.join(","))},t.warn=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.warn("<WARN>"+i,"["+n+"]",e.join(","))},t.error=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),o.error("<ERROR>"+i,"["+n+"]",e.join(","))},t.log=function(...e){o.log(i,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},1301:function(e,t){},14:function(e,t){e.exports=require("os")},15:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n,r,i,o,s,a,l,c,d,u,f,h,_,p,g,m,R,v,b,y,w;!function(e){e[e.Unkown=0]="Unkown",e[e.Create=1]="Create",e[e.InvaldParam=2]="InvaldParam",e[e.InvaldLink=3]="InvaldLink",e[e.InvaldConfig=4]="InvaldConfig",e[e.Timeout=5]="Timeout",e[e.VerifyData=6]="VerifyData",e[e.Forbidden=7]="Forbidden",e[e.RangeDispatch=8]="RangeDispatch",e[e.FilePathOverRanging=9]="FilePathOverRanging",e[e.FileCreate=201]="FileCreate",e[e.FileWrite=202]="FileWrite",e[e.FileRead=203]="FileRead",e[e.FileRename=204]="FileRename",e[e.FileFull=205]="FileFull",e[e.FileOccupied=211]="FileOccupied",e[e.FileAccessDenied=212]="FileAccessDenied",e[e.BtUploadExist=601]="BtUploadExist",e[e.ForbinddenResource=701]="ForbinddenResource",e[e.ForbinddenAccount=702]="ForbinddenAccount",e[e.ForbinddenArea=703]="ForbinddenArea",e[e.ForbinddenCopyright=704]="ForbinddenCopyright",e[e.ForbinddenReaction=705]="ForbinddenReaction",e[e.ForbinddenPorn=706]="ForbinddenPorn",e[e.DownloadSDKCrash=10001]="DownloadSDKCrash",e[e.torrentFileNotExist=10002]="torrentFileNotExist"}(t=e.TaskError||(e.TaskError={})),function(e){e[e.Unkown=-1]="Unkown",e[e.Success=0]="Success",e[e.QueryFailed=1]="QueryFailed",e[e.ServerError=2]="ServerError",e[e.ResourceNotFound=3]="ResourceNotFound",e[e.AuthorizingFailed=4]="AuthorizingFailed",e[e.ForbidByCopyright=5]="ForbidByCopyright",e[e.ForbidByPorNoGraphy=6]="ForbidByPorNoGraphy",e[e.ForbidByReactionary=7]="ForbidByReactionary",e[e.ForbidByOtherFilter=8]="ForbidByOtherFilter"}(n=e.DcdnStatusCode||(e.DcdnStatusCode={})),function(e){e[e.Begin=-1]="Begin",e[e.Unkown=0]="Unkown",e[e.StandBy=1]="StandBy",e[e.PreDownloading=2]="PreDownloading",e[e.StartWaiting=3]="StartWaiting",e[e.StartPending=4]="StartPending",e[e.Started=5]="Started",e[e.StopPending=6]="StopPending",e[e.Stopped=7]="Stopped",e[e.Succeeded=8]="Succeeded",e[e.Failed=9]="Failed",e[e.Seeding=10]="Seeding",e[e.DestroyPending=11]="DestroyPending",e[e.End=12]="End"}(r=e.TaskStatus||(e.TaskStatus={})),function(e){e[e.Begin=-1]="Begin",e[e.StandBy=0]="StandBy",e[e.Stopped=1]="Stopped",e[e.Started=2]="Started",e[e.Complete=3]="Complete",e[e.Forbidden=4]="Forbidden",e[e.Error=5]="Error",e[e.End=6]="End"}(i=e.BtFileStatus||(e.BtFileStatus={})),function(e){e[e.DispatchStrategyNone=0]="DispatchStrategyNone",e[e.DispatchStrategyOrigin=1]="DispatchStrategyOrigin",e[e.DispatchStrategyP2s=2]="DispatchStrategyP2s",e[e.DispatchStrategyP2p=4]="DispatchStrategyP2p",e[e.DispatchStrategyAll=-1]="DispatchStrategyAll"}(o=e.DispatchStrategy||(e.DispatchStrategy={})),function(e){e[e.Unkown=0]="Unkown",e[e.P2sp=1]="P2sp",e[e.Bt=2]="Bt",e[e.Emule=3]="Emule",e[e.Group=4]="Group",e[e.Magnet=5]="Magnet"}(s=e.TaskType||(e.TaskType={})),function(e){e[e.Invalid=0]="Invalid",e[e.P2sp=1]="P2sp",e[e.Emule=2]="Emule"}(a=e.TaskCfgType||(e.TaskCfgType={})),function(e){e.Unkown="Unkown",e.Downloading="Downloading",e.Completed="Completed",e.Recycle="Recycle"}(l=e.CategroyViewID||(e.CategroyViewID={})),function(e){e[e.Unknow=0]="Unknow",e[e.TaskCreated=1]="TaskCreated",e[e.InsertToCategoryView=2]="InsertToCategoryView",e[e.RemoveFromCategoryView=3]="RemoveFromCategoryView",e[e.StatusChanged=4]="StatusChanged",e[e.DetailChanged=5]="DetailChanged",e[e.ChannelInfoChanged=6]="ChannelInfoChanged",e[e.DcdnStatusChanged=7]="DcdnStatusChanged",e[e.TaskDescriptionChanged=8]="TaskDescriptionChanged",e[e.TaskUserRead=9]="TaskUserRead",e[e.TaskRenamed=10]="TaskRenamed",e[e.TaskMovedEnd=11]="TaskMovedEnd",e[e.TaskMovingStateChange=12]="TaskMovingStateChange",e[e.BtSubFileDetailChanged=13]="BtSubFileDetailChanged",e[e.BtSubFileLoaded=14]="BtSubFileLoaded",e[e.BtSubFileForbidden=15]="BtSubFileForbidden",e[e.BtSubFileDcdnStatusChanged=16]="BtSubFileDcdnStatusChanged",e[e.TaskCategoryMovedEnd=17]="TaskCategoryMovedEnd",e[e.GroupTaskSubFileDetailChanged=18]="GroupTaskSubFileDetailChanged",e[e.TaskDestroying=19]="TaskDestroying",e[e.TaskDestroyed=20]="TaskDestroyed"}(c=e.TaskEventType||(e.TaskEventType={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.VirtualId=2]="VirtualId",e[e.SrcTotal=3]="SrcTotal",e[e.SrcUsing=4]="SrcUsing",e[e.FileSize=5]="FileSize",e[e.ReceivedSize=6]="ReceivedSize",e[e.DownloadSize=7]="DownloadSize",e[e.TotalDownloadSize=8]="TotalDownloadSize",e[e.CreateTime=9]="CreateTime",e[e.CompletionTime=10]="CompletionTime",e[e.DownloadingPeriod=11]="DownloadingPeriod",e[e.Progress=12]="Progress",e[e.RecycleTime=13]="RecycleTime",e[e.FileCreated=14]="FileCreated",e[e.Forbidden=15]="Forbidden",e[e.CategoryId=16]="CategoryId",e[e.UserRead=17]="UserRead",e[e.OpenOnComplete=18]="OpenOnComplete",e[e.GroupTaskId=19]="GroupTaskId",e[e.DownloadSubTask=20]="DownloadSubTask",e[e.NameType=21]="NameType",e[e.OwnerProduct=22]="OwnerProduct",e[e.FileIndex=23]="FileIndex",e[e.NameFixed=24]="NameFixed",e[e.ValidDownloadSize=25]="ValidDownloadSize",e[e.RealDownloadSize=26]="RealDownloadSize",e[e.ResourceLegal=27]="ResourceLegal",e[e.TaskType=28]="TaskType",e[e.ErrorCode=29]="ErrorCode",e[e.PlayPosition=30]="PlayPosition",e[e.Duration=31]="Duration",e[e.NumberEnd=32]="NumberEnd",e[e.BooleanStart=4096]="BooleanStart",e[e.Destroyed=4097]="Destroyed",e[e.Background=4098]="Background",e[e.Moving=4099]="Moving",e[e.BooleanEnd=4100]="BooleanEnd",e[e.StringStart=8192]="StringStart",e[e.TaskName=8193]="TaskName",e[e.SavePath=8194]="SavePath",e[e.RelativePath=8195]="RelativePath",e[e.TaskUrl=8196]="TaskUrl",e[e.RefUrl=8197]="RefUrl",e[e.Cid=8198]="Cid",e[e.Gcid=8199]="Gcid",e[e.Cookie=8200]="Cookie",e[e.ProductInfo=8201]="ProductInfo",e[e.Origin=8202]="Origin",e[e.Description=8203]="Description",e[e.UserData=8204]="UserData",e[e.OriginName=8205]="OriginName",e[e.HTTPContentType=8206]="HTTPContentType",e[e.CategoryViewId=8207]="CategoryViewId",e[e.YunTaskId=8208]="YunTaskId",e[e.StringEnd=8209]="StringEnd",e[e.ObjectStart=12288]="ObjectStart",e[e.ObjectEnd=12289]="ObjectEnd"}(d=e.TaskAttribute||(e.TaskAttribute={})),function(e){e[e.UnKnown=0]="UnKnown",e[e.SrcTotal=1]="SrcTotal",e[e.SrcUsing=2]="SrcUsing",e[e.FileSize=4]="FileSize",e[e.ReceivedSize=8]="ReceivedSize",e[e.DownloadSize=16]="DownloadSize",e[e.CompletionTime=32]="CompletionTime",e[e.DownloadingPeriod=64]="DownloadingPeriod",e[e.Progress=128]="Progress",e[e.RecycleTime=256]="RecycleTime",e[e.FileCreated=512]="FileCreated",e[e.Forbidden=1024]="Forbidden",e[e.UserRead=2048]="UserRead",e[e.OpenOnComplete=4096]="OpenOnComplete",e[e.DownloadSubTask=8192]="DownloadSubTask",e[e.TaskName=16384]="TaskName",e[e.SavePath=32768]="SavePath",e[e.Cid=65536]="Cid",e[e.Gcid=131072]="Gcid",e[e.UserData=262144]="UserData",e[e.CategoryViewId=524288]="CategoryViewId",e[e.ErrorCode=1048576]="ErrorCode",e[e.TaskSpeed=2097152]="TaskSpeed",e[e.ChannelInfo=4194304]="ChannelInfo",e[e.ValidDownloadSize=8388608]="ValidDownloadSize",e[e.OriginName=16777216]="OriginName",e[e.HTTPContentType=33554432]="HTTPContentType",e[e.PlayPosition=67108864]="PlayPosition",e[e.Duration=134217728]="Duration",e[e.YunTaskId=268435456]="YunTaskId"}(u=e.TaskDetailChangedFlags||(e.TaskDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(f=e.BtSubFileDetailChangedFlags||(e.BtSubFileDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(h=e.GroupTaskSubFileDetailChangedFlags||(e.GroupTaskSubFileDetailChangedFlags={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.FileStatus=2]="FileStatus",e[e.DownloadSize=3]="DownloadSize",e[e.FileSize=4]="FileSize",e[e.EnableDcdn=5]="EnableDcdn",e[e.ErrorCode=6]="ErrorCode",e[e.DcdnStatus=7]="DcdnStatus",e[e.RealIndex=8]="RealIndex",e[e.FileOffset=9]="FileOffset",e[e.Visible=10]="Visible",e[e.Download=11]="Download",e[e.UserRead=12]="UserRead",e[e.PlayPosition=13]="PlayPosition",e[e.Duration=14]="Duration",e[e.NumberEnd=15]="NumberEnd",e[e.StringStart=4096]="StringStart",e[e.FinalName=4097]="FinalName",e[e.RelativePath=4098]="RelativePath",e[e.FileName=4099]="FileName",e[e.FilePath=4100]="FilePath",e[e.Cid=4101]="Cid",e[e.Gcid=4102]="Gcid",e[e.StringEnd=4103]="StringEnd"}(_=e.BtFileAttribute||(e.BtFileAttribute={})),function(e){e[e.P2spUrl=0]="P2spUrl",e[e.BtInfoId=1]="BtInfoId",e[e.EmuleFileHash=2]="EmuleFileHash",e[e.MagnetUrl=3]="MagnetUrl",e[e.GroupTaskName=4]="GroupTaskName"}(p=e.KeyType||(e.KeyType={})),function(e){e[e.NameInclude=1]="NameInclude",e[e.BtDisplayNameInclude=2]="BtDisplayNameInclude"}(g=e.SearchKeyType||(e.SearchKeyType={})),function(e){e[e.ExtEqual=1]="ExtEqual",e[e.NameLikeAndExtEqual=2]="NameLikeAndExtEqual",e[e.TaskTypeEqual=4]="TaskTypeEqual"}(m=e.FilterKeyType||(e.FilterKeyType={})),function(e){e[e.All=0]="All",e[e.CommonForeground=1]="CommonForeground",e[e.CommonBackground=2]="CommonBackground",e[e.Temporary=3]="Temporary",e[e.PreDownload=4]="PreDownload",e[e.PrivateForeground=5]="PrivateForeground"}(R=e.KeyFilter||(e.KeyFilter={})),function(e){e[e.Unknown=-1]="Unknown",e[e.LoadTaskBasic=0]="LoadTaskBasic",e[e.Create=1]="Create",e[e.Complete=2]="Complete",e[e.Recycle=3]="Recycle",e[e.Recover=4]="Recover",e[e.ReDownload=5]="ReDownload",e[e.MoveThoughCategory=6]="MoveThoughCategory"}(v=e.TaskInsertReason||(e.TaskInsertReason={})),function(e){e[e.Unknown=-1]="Unknown",e[e.Manual=0]="Manual",e[e.PauseAll=1]="PauseAll",e[e.DeleteTask=2]="DeleteTask",e[e.TaskJammed=3]="TaskJammed",e[e.LowSpeed=4]="LowSpeed",e[e.MaxDownloadReduce=5]="MaxDownloadReduce",e[e.MoveTask=6]="MoveTask",e[e.SelectDownloadLists=7]="SelectDownloadLists",e[e.PrivateLoginOut=8]="PrivateLoginOut",e[e.FreeDownload=9]="FreeDownload",e[e.Exit=10]="Exit"}(b=e.TaskStopReason||(e.TaskStopReason={})),function(e){e[e.RESOURCE_FROM_MEMBER=1]="RESOURCE_FROM_MEMBER",e[e.RESOURCE_FROM_OFFLINE=2]="RESOURCE_FROM_OFFLINE",e[e.RESOURCE_FROM_CRYSTAL_LARGE=4]="RESOURCE_FROM_CRYSTAL_LARGE",e[e.RESOURCE_FROM_CRYSTAL_SMALL=8]="RESOURCE_FROM_CRYSTAL_SMALL",e[e.RESOURCE_FROM_DCDN=16]="RESOURCE_FROM_DCDN",e[e.RESOURCE_FROM_FREEDCDN=32]="RESOURCE_FROM_FREEDCDN"}(y=e.XLResourceFrom||(e.XLResourceFrom={})),function(e){e[e.XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD=0]="XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD",e[e.XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING=1]="XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING",e[e.XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING=2]="XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING"}(w=e.XLDownloadStrategy||(e.XLDownloadStrategy={}))}(t.DownloadKernel||(t.DownloadKernel={}))},16:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(2),s=n(1),a=n(18),l=n(25),c=s.default.getLogger("Thunder.Util"),d="Thunder Network\\Thunder7.9\\";function u(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(){let e=l.ThunderHelper.getSystemTempPath(),t=l.ThunderHelper.getLogicalDriveStrings(),n=0;for(let r=0;r<t.length;r++){if(l.ThunderHelper.getDriveType(t[r])===l.ThunderHelper.DriverType.DRIVE_FIXED){let i=l.ThunderHelper.getDriveInfo(t[r]);n<i.freeBytes&&t[r]!==e&&(n=i.freeBytes,e=t[r])}}return e.substring(0,1)+":\\迅雷下载"}function s(e){let t=(e.style.webkitTransform||getComputedStyle(e,"").getPropertyValue("-webkit-transform")||e.style.transform||getComputedStyle(e,"").getPropertyValue("transform")).match(/\-?[0-9]+\.?[0-9]*/g);return{x:parseInt(t&&(t[12]||t[4])||"0",10),y:parseInt(t&&(t[13]||t[5])||"0",10)}}function f(e){let t=!1;do{let n="",r="";if(/^[a-zA-Z]:\\/.test(e))n=e.slice(3);else{if(0!==e.indexOf("\\\\"))break;{let t=e.indexOf("\\",2);if(-1===t||t===e.length-1)break;if(""===(r=(n=e.slice(2)).substr(0,t-2)))break}}if(/[\*\"<>\?:\|]/i.test(n))break;if(e.length>256)break;if(""===r){t=!0;break}let i=r.indexOf(".ipv6-literal.net");-1!==i?(-1!==(i=(r=r.substr(0,i)).indexOf("%"))&&(r=r.substr(0,i)),r=r.replace(/\-/g,":"),/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/.test(r)&&(t=!0)):/(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))/.test(r)&&(t=!0)}while(0);return t}e.formatSize=function(e,t){0===t||(t=t||2);let n="0B";if("number"==typeof e&&e>0){let r=["B","KB","MB","GB","TB"],i=0,o=e;for(;o>=1e3&&!(i>=4);)o/=1024,i+=1;n=-1===String(o).indexOf(".")?o+r[i]:o.toFixed(t)+r[i]}return n},e.formatSizeCustom=function(e,t=2,n=5){let r="0B";if("number"==typeof e&&e>0){let i=["B","KB","MB","GB","TB"],o=0,s=e;for(;s>=1e3&&!(o>=4);)s/=1024,o+=1;if(-1===String(s).indexOf("."))r=s+i[o];else{let e=s.toFixed(t);e.length<=n?r="KB"!==i[o]&&"MB"!==i[o]||1===t?e+i[o]:s.toFixed(1)+i[o]:("."===(e=e.substr(0,n))[n-1]&&(e=e.substr(0,n-1)),r=e+i[o])}}return r},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.getDefaultDownloadDir=t,e.getMaxFreeDriver=function(){return t().substring(0,1)},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){c.warning(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",r=!1,i=[],o=e.length;for(let s=0;s<o;s++){let a=e[s];if('"'!==a&&"'"!==a||(""===n?(r=!0,n=a):n===a&&(r=!1,n=""))," "!==a||r||s===o-1){if(s===o-1){let n=e.substring(t);""!==n.trim()&&i.push(u(n))}}else{let n=e.substring(t,s);""!==n.trim()&&i.push(u(n)),t=s+1}}return i},e.getThunderTempPath=function(e,t){return r(this,void 0,void 0,function*(){const r=yield Promise.resolve().then(()=>n(14));let i=o.join(r.tmpdir(),d);return t&&(i=o.join(i,t)),void 0!==e&&e&&(yield a.FileSystemAWNS.mkdirsAW(i)),i})},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,r)=>{e+=0===r?"?":"&",e+=`${n}=${t[n]}`}),e},e.setQueryStringEx=function(e,t){for(let n in t)e+=-1===e.indexOf("?")?"?":"&",e+=`${n}=${t[n]}`;return e},e.getQueryString=function(e,t){return e&&t&&e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:""},e.isClipboardTextFormatAvailable=function(){let e=!1,t=i.clipboard.availableFormats();for(let n of t)if("text/plain"===n){e=!0;break}return e},e.keywordsHighLight=function(e,t,n){if(!e)return"";if(!t)return e;if(0===e.length)return e;if(0===t.length)return e;let r=/\\/,i=t.split(" ");if(0===(i=i.filter(e=>e.trim().length>0)).length)return e;for(let t=0;t<i.length;t++)if(i[t].search(r)>0)return e;n=void 0===n||null===n?"#FF0000":n;let o="",s=["\\[","\\^","\\*","\\(","\\)","\\|","\\?","\\$","\\.","\\+"],a="",l="|";for(let e=0;e<i.length;e++){for(let t=0;t<s.length;t++){let n=new RegExp(s[t],"g");i[e]=i[e].replace(n,s[t])}e===i.length-1&&(l=""),a=a.concat(i[e],l)}let c=new RegExp(a,"gi");return o=e.replace(c,e=>'<span style= "color:'+n+'">'+e+"</span>")},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setStyle=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style[t]=n})},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),r=t.split("."),i=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(r[e])>0){i=1;break}if(Number(n[e])-Number(r[e])<0){i=-1;break}}return i},e.throttle=function(e,t){let n,r=0;return(...i)=>{const o=Date.now();clearTimeout(n),o-r>t?(e(...i),r=o):n=setTimeout(()=>{e(...i),r=o},t)}},e.debounce=function(e,t){let n=null;return(...r)=>{n&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}},e.getElementFixed=function(e){let t=e.offsetLeft,n=e.offsetTop,r=e.offsetParent;for(;null!==r;){let e=s(r);t+=r.offsetLeft+e.x,n+=r.offsetTop+e.y,r=r.offsetParent}return{x:t,y:n}},e.escapeHTML=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")},e.unescapeHTML=function(e){return e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")},e.isValidPath=f,e.isValidDownloadPath=function(e){return r(this,void 0,void 0,function*(){let t=!1;do{if(e.length<3)break;if("私人空间"===e){t=!0;break}if(l.ThunderHelper.getDriveType(e)===l.ThunderHelper.DriverType.DRIVE_REMOTE){t=!0;break}if(!f(e))break;if(!(yield a.FileSystemAWNS.dirExistsAW(e))&&!(yield a.FileSystemAWNS.mkdirsAW(e)))break;t=!0}while(0);return t})};let h=void 0;function _(e,t="normal 12px sans-serif"){h||(h=document.createElement("canvas"));let n=h.getContext("2d");return n.font=t,n.measureText(e).width}function p(e,t,n="normal 12px sans-serif",r=1){function i(e,t,r,o,s){let a=-1;do{if(e>t){a=t;break}let l=Math.round((e+t)/2),c=_(`${r.substr(0,l)}...${o}`,n);if(s===c){a=l;break}if(s>c){if(Math.round(s)===Math.round(c)){a=l;break}a=i(l+1,t,r,o,s)}else if(c>s){if(Math.round(s)===Math.round(c)){a=l-1;break}a=i(e,l-1,r,o,s)}}while(0);return a}let s=e;do{if(!t)break;if(!e)break;let a=t.offsetWidth*r;if(a>_(e,n))break;let l=o.extname(e);""!==l&&(l=l.substring(1));let c=e.substr(0,e.length-l.length-1);if(!c)break;let d=i(0,c.length,c,l,a);if(-1===d)break;s=`${c.substr(0,d-2*(r-1))}...${l}`}while(0);return s}e.getTextWidth=_,e.getOmitName=p,e.getOmitNameMultiLine=function(e,t,n){return p(e,t,"normal 12px microsoft yahei",2)},e.setTimeoutAw=function(e,t){return new Promise((n,r)=>{setTimeout(()=>{t&&t(),n()},e)})}}(t.ThunderUtil||(t.ThunderUtil={}))},167:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.eventPluginLoderPluginReady="event_plugin_loader_plugin_ready",e.eventDownloadItemActive="event_download_item_active",e.eventDownloadItemChosen="event_download_item_chosen",e.eventDetailSwitch2Index="event_detail_switch_to_index",e.eventDownloadSwitchCategory="event_download_switch_category",e.evetDownloadMove2Tail="event_download_move_to_tail",e.eventBringMainWindowToTop="event_bring_main_window_to_top",e.eventDownloadContextMenuClick="event_download_context_menu_click",e.eventOnCommand="event_on_command",e.eventOnCommandline="event_on_command_line",e.eventDownloadKernelInitEnd="event_dk_init_end",e.eventDownloadSDKRecover="event_sdk_crash_recover",e.eventTaskDataBaseLoadEnd="event_dk_task_data_base_load_end",e.eventGlobalDownloadSpeedChanged="event_dk_global_download_speed_changed",e.eventGlobalUploadSpeedChanged="event_dk_global_upload_speed_changed",e.eventConfigInitFinished="event_config_init_finished",e.eventConfigValueChanged="event_config_value_changed",e.eventBHOConfigInitFinished="event_bho_config_init_finished",e.eventBHOConfigValueChanged="event_bho_config_value_changed",e.eventMemoryConfigValueChanged="event_memory_config_value_changed",e.eventShowHomePage="event_show_home_page",e.eventShowSearchTaskPage="event_show_searchtask_page",e.eventConfigBrowserInitFinished="event_config_browser_init_finished",e.eventDownloadContextMenuPopup="event_download_context_menu_popup",e.eventDownloadContextMenuEnd="event_download_context_menu_end",e.eventEmbeddedBrowserWndClick="event_embedded_browser_wnd_click",e.eventLoginWndCreate="event_login_wnd_create",e.eventLoginWndCreated="event_login_wnd_created",e.eventLoginWndShow="event_login_wnd_show",e.eventLoginWndShowResult="event_login_wnd_show_result",e.eventLoginWndClose="event_login_wnd_close",e.eventRetryLoginWndCreate="event_retry_login_wnd_create",e.eventModifierUserinfoWndCreate="event_modifier-userinfo-wnd-create",e.eventBrowserNumberChange="event_browser_number_change",e.eventUploadConfigInitFinished="event_upload_config_init_finished",e.eventUploadConfigValueChanged="event_upload_config_value_changed"}(t.NodeEventMesssageNS||(t.NodeEventMesssageNS={}))},18:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(21),o=n(2),s=n(6),a=n(60),l=s.promisify,c=n(1).default.getLogger("Thunder.base.fs-utilities");!function(e){function t(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.access);try{yield n(e),t=!0}catch(e){c.information(e)}}return t})}function s(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.mkdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function d(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.rmdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function u(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.unlink);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function f(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.readdir);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function h(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.lstat);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function _(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let r=o.join(e,t),i=yield h(r);n=null!==i&&i.isDirectory()?yield p(r):yield u(r)}return n})}function p(e){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e){if(yield t(e)){n=!0;let t=(yield f(e))||[];for(let r=0;r<t.length;r++)n=(yield _(e,t[r]))&&n;(n||0===t.length)&&(n=(yield d(e))&&n)}}return n})}function g(e){return r(this,void 0,void 0,function*(){let n=!1;return c.information("mkdirsAW",e),void 0!==e&&((yield t(e))?n=!0:o.dirname(e)===e?n=!1:(yield g(o.dirname(e)))&&(n=yield s(e))),n})}function m(e,n){return r(this,void 0,void 0,function*(){let r;if(e.toLowerCase()!==n.toLowerCase()&&(yield t(e))){let t=i.createReadStream(e),o=i.createWriteStream(n);r=new Promise(e=>{t.pipe(o).on("finish",()=>{e(!0)})})}else r=new Promise(e=>{e(!1)});return r})}e.readFileAW=function(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.readFile);try{t=yield n(e)}catch(e){c.warning(e)}}return t})},e.readLineAw=function(e){return r(this,void 0,void 0,function*(){let n=null;do{if(!e)break;if(!t(e))break;n=yield new Promise(t=>{let n=[];const r=i.createReadStream(e),o=a.createInterface({input:r});o.on("line",e=>{n.push(e)}),o.on("close",()=>{t(n)})})}while(0);return n})},e.writeFileAW=function(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const r=l(i.writeFile);try{yield r(e,t),n=!0}catch(e){c.warning(e)}}return n})},e.existsAW=t,e.dirExistsAW=function(e){return r(this,void 0,void 0,function*(){let n=!1;do{if(!(n=yield t(e)))break;let r=yield h(e);if(!r)break;n=r.isDirectory()}while(0);return n})},e.mkdirAW=s,e.rmdirAW=d,e.unlinkAW=u,e.readdirAW=f,e.lstatAW=h,e.rmdirsAW=p,e.mkdirsAW=g,e.renameAW=function(e,t){return r(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=l(i.rename);try{yield n(e,t)}catch(e){c.warning(e)}}})},e.copyFileAW=m,e.copyDirsAW=function e(n,i){return r(this,void 0,void 0,function*(){let r=!1,s=yield h(n);if(s.isDirectory()){r=yield g(i);let a=(yield f(n))||[];for(let l=0;l<a.length;l++){let c=o.join(n,a[l]),d=o.join(i,a[l]);(r=yield t(c))&&(r=(s=yield h(c)).isDirectory()?yield e(c,d):yield m(c,d))}}return r})},e.mkdtempAW=function(){return r(this,void 0,void 0,function*(){let e=!1;const t=l(i.mkdtemp),r=(yield Promise.resolve().then(()=>n(14))).tmpdir();try{e=yield t(`${r}${o.sep}`)}catch(e){c.warning(e)}return e})},e.deleteEmptySubDirs=function(e,n){return r(this,void 0,void 0,function*(){let r=!0;e=o.normalize(e),n=o.normalize(n),e.length>3&&"\\"===e[e.length-1]&&(e=e.slice(0,e.length-1)),n.length>3&&"\\"===n[n.length-1]&&(n=n.slice(0,n.length-1));do{if(0!==e.indexOf(n)){r=!1;break}let i=e;for(;i!==n;){if((yield t(i))&&!(yield d(i))){r=!1;break}i=o.dirname(i)}}while(0);return r})},e.getFileSize=function e(n){return r(this,void 0,void 0,function*(){let r=0;do{if(!n)break;if(!(yield t(n)))break;let i=yield h(n);if(i)if(i.isDirectory()){let t=yield f(n);for(let i=0;i<t.length;i++)r+=(yield e(o.join(n,t[i])))}else r=i.size}while(0);return r})},e.isDirectoryEmptyAW=function(e,n=!0){return r(this,void 0,void 0,function*(){let r=!0;do{if(!e){r=!1;break}if(!(yield t(e))){r=n;break}let i=yield h(e);if(!i){r=!1;break}if(!i.isDirectory()){r=!1;break}if((yield f(e)).length>0){r=!1;break}}while(0);return r})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},2:function(e,t){e.exports=require("path")},20:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.channelRMNewTaskReadyForSetTaskData="RM_NEWTASK_READYRORSETTASKDATA",e.channelRMNewTaskSetTaskData="RM_NEWTASK_SETTASKDATA",e.channelRMPreNewTaskSetTaskData="RM_PRENEWTASK_SETTASKDATA",e.channelRMNewTaskCreateNewTask="RM_NEWTASK_CREATENEWTASK",e.channelRMNewTaskClose="RM_NEWTASK_CLOSE",e.channelRMPreNewTaskClose="RM_PRENEWTASK_CLOSE",e.channelRMNewTaskSetBTInfo="RM_NEWTASK_SETBTINFO",e.channelRMNewTaskDownloadTorrent="RM_NEW_TASK_DOWNLOAD_TORRENT",e.channelRMNewTaskCreateBtTask="RM_NEWTASK_CRATEBTTASK",e.channelRMNewTaskCancleMagnet="RM_NEWTASK_CANCLE_MAGNET",e.channelRMImportTorrent="RM_NEWTASK_IMPORT_TORRENT",e.channelRMGetConfigValueResolve="RM_GET_CONFIG_VALUE_RESOLVE",e.channelRMGetConfigValueReject="RM_GET_CONFIG_VALUE_REJECT",e.channelRMSetConfigValueReject="RM_SET_CONFIG_VALUE_REJECT",e.channelMRTrayMenuClick="MR_TRAY_MENU_CLICK",e.channelMRNewTaskMagnetTaskCreated="MR_NEW_TASK_MAGNET_TASK_CREATED",e.channelMRNewTaskDownloadTorrentResult="MR_NEW_TASK_DOWNLOAD_TORRENT_RESULT",e.channelMRNewTaskCreateNewTaskResult="MR_NEWTASK_CREATENEWTASK_RESULT",e.channelMRNewTaskCreateBtTaskResult="RM_NEWTASK_CRATEBTTASK_RESULT",e.channelMRGetConfigValue="MR_GET_CONFIG_VALUE",e.channelMRSetConfigValue="MR_SET_CONFIG_VALUE",e.channelRMCommitPlanTask="RM_PLANTASK_COMMIT",e.channelRMPerformePlanTask="RM_PLANTASK_PERFORME",e.channelRMProcessSend="RM_RENDER_PROCESS_INFO",e.channelRMGetPrivateSpaceInfo="RM_RENDER_GET_PRIVATE_SPACE_INFO",e.channelMRGetPrivateSpaceInfoResult="MR_RENDER_GET_PRIVATE_SPACE_INFO_RESULT",e.channelRMFileCopy="RM_FILE_COPY",e.channelRMFileMove="RM_FILE_MOVE",e.channelMRFileCopyResult="MR_FILE_COPY_RESULT",e.channelMRFileMoveResult="MR_FILE_MOVE_RESULT",e.channelRMGetSutitleByCid="RM_RENDER_GET_SUBTITLE_BY_CID",e.channelMRGetSutitleByCidResult="MR_RENDER_GET_SUBTITLE_BY_CID_RESULT",e.channelRMGetSutitleByName="RM_RENDER_GET_SUBTITLE_BY_NAME",e.channelMRGetSutitleByNameResult="MR_RENDER_GET_SUBTITLE_BY_NAME_RESULT",e.channelRMDownloadSutitle="RM_RENDER_DOWNLOAD_SUBTITLE",e.channelMRDownloadSutitleSuc="MR_RENDER_DOWNLOAD_SUBTITLE_SUCCESS",e.channelMRDownloadSutitleFail="MR_RENDER_DOWNLOAD_SUBTITLE_FAIL",e.channelRMGetDisplayName="RM_RENDER_GET_SUBTITLE_DISPLAYNAME",e.channelMRGetDisplayNameResult="MR_RENDER_GET_SUBTITLE_DISPLAYNAME_RESULT",e.channelMRBringWindowToTop="MR_RENDER_BRING_WINDOW_TO_TOP",e.channelRMDownloadXmp="RM_RENDER_DOWNLOAD_XMP",e.channelRMXmpFixBoxCreated="RM_RENDER_XMPFIXBOX_CREATED",e.channelMRFixXmpSuc="MR_RENDER_FIX_XMP_SUC",e.channelMRFixXMPFail="MR_RENDER_FIX_XMP_FAIL",e.channelRMDownloadXmpEx="RM_RENDER_DOWNLOAD_XMP_EX",e.channelRMSetPosition="RM_SET_POSITION",e.channelRMSetFoucs="RM_SET_FOCUS",e.channelRMCreateAddressDropWnd="RM_CREATE_ADDRESS_DROPWND",e.channelRMRefreshAddressDropWnd="RM_REFRESH_ADDRESS_DROPWND",e.channelRMSelectAddressDropItem="RM_SELECT_ADDRESS_DROPITEM",e.channelRMCreateSearchWindow="RM_CREATE_SEARCH_WINDOW",e.channelRMShowSearchWindow="RM_SHOW_SEARCH_WINDOW",e.channelRMAddressKeyDown="RM_ADDRESS_BAR_KEYDOWN",e.channelMRFWAddressKeyDown="MR_ADDRESS_FW_BAR_KEYDOWN",e.channelMRFWSelectAddressDropItem="MR_FW_SELECT_ADDRESS_DROPITEM",e.channelRMAddressDropWndKeyDown="RM_ADDRESS_DROPWND_KEYDOWN",e.channelRMClickMouse="RM_CLICK_MOUSE",e.channelMRSearchBarFocusChange="MR_SEARCHBAR_FOCUS_CHANGE",e.channelMRFWAddressDropWndKeyDown="MR_FW_ADDRESS_DROPWND_KEYDOWN",e.channelMRClickAddressDropItem="MR_CLICK_ADDRESS_DROPITEM",e.channelMRMainWndMax="MR_MAINWINDOW_MAX",e.channelMRMainWndMin="MR_MAINWINDOW_MIN",e.channelMRMainWndRestore="MR_MAINWINDOW_RESTORE",e.channelRMGetBrowserStartType="RM_GET_BROWSER_START_TYPE",e.channelMRGetBrowserStartTypeResult="MR_GET_BROWSER_START_TYPE_RESULT",e.channelRMExecute="RM_SHELL_EXECUTE",e.channelMRExecuteResult="MR_SHELL_EXECUTE_RESULT",e.channelMRAdTipsClick="MR_AD_TIPS_CLICK",e.channelMRNotificationMsg="MR_NOTIFICATION_MSG",e.channelRMSetProgressBar="RM_SET_PROGRESS_BAR",e.channelRMRoundWindow="RM_ROUND_WINDOW",e.channelMRShowOrHideWindow="MR_SHOW_OR_HIDE_WINDOW",e.channelMRSuspensionWindowShowOrHide="MR_SUSPENSION_WINDOW_SHOW_OR_HIDE",e.channelRMConfigInitFinished="RM_CONFIG_INIT_FINISHED",e.channelRMConfigValueChanged="RM_CONFIG_VALUE_CHANGED",e.channelRMIndividuationBrowserMsg="RM_INDIVIDUATION_BROWSER_MSG",e.channelMRIndividuationBrowserMsg="MR_INDIVIDUATION_BROWSER_MSG",e.channelRMSetEnvironmentVariable="RM_SET_ENVIRONMENT_VARIABLE",e.channelMREmbedPlayerPos="MR_EMBED_PLAYER_POSITION",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},21:function(e,t){e.exports=require("fs")},22:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=function(...e){},t.error=function(...e){},t.warning=function(...e){},t.critical=function(...e){},t.verbose=function(...e){},"development"===process.env.LOGGER_ENV&&(t.information=function(...e){console.log("information",e)},t.error=function(...e){console.log("error",e)},t.warning=function(...e){console.log("warning",e)},t.critical=function(...e){console.log("critical",e)},t.verbose=function(...e){console.log("verbose",e)})},23:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.msgIPCCommunicatorForward="ipc_communicator_forward",e.msgIPCSendToMain="ipc_send_to_main",e.msgIPCSendToRenderer="ipc_send_to_renderer",e.msgIPCRendererConnect="ipc_renderer_connect",e.msgIPCRendererDisconnect="ipc_renderer_disconnect",e.msgNCCallNativeFunction="nc_call_native_function",e.msgNCCheckNativeFunction="nc_check_native_function",e.msgNCCallJsFunctionById="nc_call_js_function_by_id",e.msgNCCallJsFunctionByName="nc_call_js_function_by_name",e.msgNCNativeFireEvent="nc_native_fire_event",e.msgNCNativeCallReady="nc_native_call_ready"}(t.CommonIPCMessage||(t.CommonIPCMessage={}))},242:function(e,t,n){"use strict";var r=n(1165);n.n(r).a},25:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(33),s=n(14),a=n(15),l=n(8).default(i.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){let t,n,i,c;function d(e){let t=e;return/^[a-zA-Z]:\\/.test(e)?t=e.slice(0,3):e&&"\\"!==e[e.length-1]&&(t=e+"\\"),t}!function(e){e[e.DRIVE_UNKNOWN=0]="DRIVE_UNKNOWN",e[e.DRIVE_NO_ROOT_DIR=1]="DRIVE_NO_ROOT_DIR",e[e.DRIVE_REMOVABLE=2]="DRIVE_REMOVABLE",e[e.DRIVE_FIXED=3]="DRIVE_FIXED",e[e.DRIVE_REMOTE=4]="DRIVE_REMOTE",e[e.DRIVE_CDROM=5]="DRIVE_CDROM",e[e.DRIVE_RAMDISK=6]="DRIVE_RAMDISK"}(t=e.DriverType||(e.DriverType={})),function(e){e[e.Unspecified=0]="Unspecified",e[e.HDD=3]="HDD",e[e.SSD=4]="SSD",e[e.SCM=5]="SCM"}(n=e.MediaType||(e.MediaType={})),function(e){e.HKEY_CLASSES_ROOT="HKEY_CLASSES_ROOT",e.HKEY_CURRENT_USER="HKEY_CURRENT_USER",e.HKEY_LOCAL_MACHINE="HKEY_LOCAL_MACHINE",e.HKEY_USERS="HKEY_USERS"}(i=e.RegistryHKey||(e.RegistryHKey={})),function(e){e[e.REG_NONE=0]="REG_NONE",e[e.REG_SZ=1]="REG_SZ",e[e.REG_EXPAND_SZ=2]="REG_EXPAND_SZ",e[e.REG_BINARY=3]="REG_BINARY",e[e.REG_DWORD=4]="REG_DWORD",e[e.REG_DWORD_LITTLE_ENDIAN=4]="REG_DWORD_LITTLE_ENDIAN",e[e.REG_DWORD_BIG_ENDIAN=5]="REG_DWORD_BIG_ENDIAN",e[e.REG_LINK=6]="REG_LINK",e[e.REG_MULTI_SZ=7]="REG_MULTI_SZ",e[e.REG_RESOURCE_LIST=8]="REG_RESOURCE_LIST",e[e.REG_FULL_RESOURCE_DESCRIPTOR=9]="REG_FULL_RESOURCE_DESCRIPTOR",e[e.REG_RESOURCE_REQUIREMENTS_LIST=10]="REG_RESOURCE_REQUIREMENTS_LIST",e[e.REG_QWORD=11]="REG_QWORD",e[e.REG_QWORD_LITTLE_ENDIAN=11]="REG_QWORD_LITTLE_ENDIAN"}(c=e.RegistryDataType||(e.RegistryDataType={})),e.getDriveType=function(e){return e=d(e),l.getDriveType(e)},e.getDriveInfo=function(e){return e=d(e),l.getDriveInfo(e)},e.getFreePartitionSpace=function(e){return e=d(e),l.getFreePartitionSpace(e)},e.getLogicalDriveStrings=function(){return l.getLogicalDriveStrings()},e.getPartitionSpace=function(e){return e=d(e),l.getPartitionSpace(e)},e.getSystemTempPath=function(){return l.getSystemTempPath()},e.getTaskTypeFromUrl=function(e){let t=l.getTaskTypeFromUrl(e);if(t===a.DownloadKernel.TaskType.Unkown&&function(e){e=e.toLowerCase();let t=!1;do{if("http://"===e.substr(0,"http://".length)){t=!0;break}if("https://"===e.substr(0,"https://".length)){t=!0;break}if("ftp://"===e.substr(0,"ftp://".length)){t=!0;break}}while(0);return t}(e)){let n=/:\/\/\[(.+?)\].*/.exec(e);n||(n=/.+?:\/\/.*?\[(.+?)\].*/.exec(e)),n&&n[1]&&o.isIPv6(n[1])&&(t=a.DownloadKernel.TaskType.P2sp)}return t},e.extractIcon=function(e,t){return l.extractIcon(e,t)},e.isWindow7=function(){return 1===l.isWin7()},e.isWindow8OrLater=function(){let e=!1;do{let t=s.release();if(!t)break;let n=t.indexOf(".",0);if(n<0)break;let r=t.substring(0,n);if(!r)break;let i=parseInt(r,10);i&&i>=8&&(e=!0)}while(0);return e},e.isWindows10=function(){let e=!1;do{let t=s.release();if(!t)break;if(0===t.indexOf("10.0.")){e=!0;break}}while(0);return e},e.compareStr=function(e,t){return l.compareStr(e,t)},e.getTickCount=function(){return l.getTickCount()},e.setScreenSaveActive=function(e,t){return l.setScreenSaveActive(e,t)},e.isSparseDriver=function(e){return e=d(e),l.isSparseDriver(e)},e.getAppList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getAppList(t=>{e(t)})})})},e.isSSD=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.isSSD((t,n)=>{e(n)})})})},e.getMemoryInfo=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getMemoryInfo((t,n)=>{e({totalPhy:t,totalVir:n})})})})},e.getHardDiskSpaceList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getHardDiskSpaceList(t=>{e(t)})})})},e.getCpuList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getCpuList(t=>{e(t)})})})},e.getFixedDriveMediaType=function(e){return r(this,void 0,void 0,function*(){return new Promise(t=>{e.length>1&&(e=e.slice(0,1)),l.getDriveMediaType(e.toUpperCase(),(e,n)=>{t(n)})})})},e.sleep=function(e){return r(this,void 0,void 0,function*(){yield new Promise((t,n)=>{setTimeout(t,e)})})},e.getTextScale=function(){let e=100,t=l.readRegString(i.HKEY_CURRENT_USER,"SOFTWARE\\Microsoft\\Accessibility","TextScaleFactor");return t&&(e=Number(t)),isNaN(e)&&(e=100),e},e.getWindowRect=function(e){return e?l.getWindowRect(e):{x:0,y:0,width:0,height:0}},e.moveWindow=function(e,t){e&&l.moveWindow(e,t.x,t.y,t.width,t.height,!0)},e.getSystemDirectory=function(){return l.getSystemDirectory()},e.getVersionBlockString=function(e,t){return l.getVersionBlockString(e,t)},e.getOwnerName=function(e){return l.getOwnerName(e)},e.createRegKey=function(e,t){return l.createRegKey(e,t)},e.deleteRegKey=function(e,t){return l.deleteRegKey(e,t)},e.readRegString=function(e,t,n){return l.readRegString(e,t,n)},e.queryRegValue=function(e,t,n){return l.queryRegValue(e,t,n)},e.writeRegValue=function(e,t,n,r,i){return l.writeRegValue(e,t,n,r,i)},e.deleteRegValue=function(e,t,n){return l.deleteRegValue(e,t,n)}}(t.ThunderHelper||(t.ThunderHelper={}))},28:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),i=n(6),o=n(22),s=n(23);!function(e){e.mainProcessContext="main-process",e.mainRendererContext="main-renderer",e.mainPageWebviewRendererContext="main-page-webview-renderer",e.newTaskRendererContext="new-task-renderer",e.preNewTaskRendererContext="pre-new-task-renderer",e.loginRendererContext="login-renderer";class t{constructor(){this.isConnected=!1,this.isOnIPCEvent=!1,this.rendererInfos=[],this.listeners=new Map,t.intervalIPCModuleMsgs=[s.CommonIPCMessage.msgIPCRendererConnect,s.CommonIPCMessage.msgIPCRendererDisconnect]}onMessage(e,t){do{if(!i.isString(e)||0===e.length){o.error("msgName is null");break}if(i.isNullOrUndefined(t)){o.error("listener is null");break}this.listeners.has(e)?this.listeners.get(e).push(t):this.listeners.set(e,[t])}while(0)}getCommunicatorInfo(){return this.currInfo}getAllRenderer(){return this.rendererInfos}getCommunicatorInfoById(e){for(let t of this.rendererInfos)if(t.id===e)return t;return null}getCommunicatorInfoByContext(e){for(let t of this.rendererInfos)if(t.context===e)return t;return null}startListenIPCMessage(e){this.isOnIPCEvent||(this.isOnIPCEvent=!0,e&&this.ListenSendToMainMsg(),this.ListenSendToRendererMsg(e))}ListenSendToMainMsg(){r.ipcMain.on(s.CommonIPCMessage.msgIPCSendToMain,(e,t)=>{let n=void 0;do{if(i.isNullOrUndefined(t)){o.error("msgInfo is empty");break}if(!this.isConnected){o.warning("hasnot been connected yet");break}let r=t.msg.name;if(this.isIPCModuleIntervalMsg(r)){o.information(`IPC module interval msg : ${r}`);let i=this.handleIPCModuleIntervalMsg(e.sender,t);if(n=i[1],!i[0])break;o.information("need to dispatch msg:"+r)}i.isNullOrUndefined(n)?n=this.NotifyListener(t):this.NotifyListener(t)}while(0);i.isNullOrUndefined(n)||(e.returnValue=n),t=null})}ListenSendToRendererMsg(e){(e?r.ipcMain:r.ipcRenderer).on(s.CommonIPCMessage.msgIPCSendToRenderer,(t,n)=>{let r=void 0;do{if(i.isNullOrUndefined(n)){o.error("msgInfo is empty");break}if(!this.isConnected){o.warning("hasnot been connected yet");break}let s=n.msg.name;if(this.isIPCModuleIntervalMsg(s)){o.information(`IPC module interval msg : ${s}`);let e=this.handleIPCModuleIntervalMsg(t.sender,n);if(r=e[1],!e[0])break;o.information("need to dispatch msg:"+s)}e?(o.information("is main, handle forward msg"),this.handleForwardRendererToRendererMsg(n)):(o.information("is renderer, handle business msg"),i.isNullOrUndefined(r)?r=this.NotifyListener(n):this.NotifyListener(n))}while(0);i.isNullOrUndefined(r)||(t.returnValue=r),n=null})}isIPCModuleIntervalMsg(e){for(let n of t.intervalIPCModuleMsgs)if(e===n)return!0;return!1}handleIPCModuleIntervalMsg(e,t){let n=[!1,void 0];do{let r=t.msg.name;if(r===s.CommonIPCMessage.msgIPCRendererConnect){n=[!0,this.handleRendererConnectMsg(e,t)];break}if(r===s.CommonIPCMessage.msgIPCRendererDisconnect){n=[!0,this.handleRendererDisconnectMsg(e,t)];break}}while(0);return n}handleRendererConnectMsg(e,t){o.verbose(e),o.verbose(t)}handleRendererDisconnectMsg(e,t){o.verbose(e),o.verbose(t)}handleForwardRendererToRendererMsg(e){this.sendForwardRendererToRendererMsg(e)}sendForwardRendererToRendererMsg(e){o.verbose(e)}NotifyListener(e){let t=void 0,n=e.msg.name;if(this.listeners.has(n)){let r=this.listeners.get(n),i=!0;for(let n of r)i?(i=!1,t=n(e)):n(e)}return t}}e.Communicator=t}(t.CommonIPCBase||(t.CommonIPCBase={}))},29:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(14),i=n(2);t.getDefaultPrex=function(){return i.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=i.basename(process.execPath,".exe"));let o=i.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(o="\\\\.\\pipe\\"+(o=(o=o.replace(/^\//,"")).replace(/\//g,"-"))),o},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},3:function(e,t){e.exports=require("electron")},30:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},31:function(e,t,n){e.exports=n(9)(45)},32:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(8).default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(){let e=!0;{0;let t=r.resolve("C:\\ETW_LOG\\log.ini");e="1"===i.readINI(t,"Log","enable")}return e}e.isDevToolsEnable=function(){return t()},e.isLogEnable=t,e.getLogOutput=function(){let e=process.env.TL_OUTPUT;do{if(e&&""!==e)break;let t=r.resolve("C:\\ETW_LOG\\log.ini");e=i.readINI(t,"Log","output")}while(0);return e}}(t.DevEnvHelperNS||(t.DevEnvHelperNS={}))},33:function(e,t){e.exports=require("net")},34:function(e,t){e.exports=require("url")},35:function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(r||(r={})),t.default=r},36:function(e,t,n){"use strict";var r;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(r||(r={})),e.exports=r},38:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),i=n(6),o=n(22),s=n(23),a=n(28);!function(e){class t extends a.CommonIPCBase.Communicator{constructor(){super()}initialize(e){this.currInfo={id:void 0,context:e,isMainCommunicator:!1}}connect(){this.isConnected?o.warning("has been connected"):(this.sendConnectMsgToMain(),this.isConnected=!0,this.startListenIPCMessage(!1))}disconnect(){this.isConnected?(this.isConnected=!1,this.sendDisconnectMsgToMain()):o.warning("hasnot been connected yet")}sendMessageToMain(e){this.sendIPCMsgToMain(e)}sendMessageToMainSync(e){return this.sendIPCMsgToMain(e,!0)}sendMessageToRenderer(e,t){this.sendIPCMsgToRenderer(e,t)}handleRendererConnectMsg(e,t){do{if(i.isNullOrUndefined(t)){o.error("msgInfo is null");break}let e=t.msg.args[0];if(i.isNullOrUndefined(e)){o.error("connectRendererInfo is null");break}o.information(`Renderer: new renderer will connect, id = ${e.id}, context = ${e.context}`),this.rendererInfos.push(e)}while(0)}handleRendererDisconnectMsg(e,t){do{if(i.isNullOrUndefined(t)){o.error("msgInfo is null");break}let e=t.msg.args[0];if(i.isNullOrUndefined(e)){o.error("disconnectRendererInfo is null");break}o.information(`renderer will disconnect, id = ${e.id}, context = ${e.context}`);for(let t=0;t<this.rendererInfos.length;++t)if(this.rendererInfos[t]===e){this.rendererInfos.splice(t,1);break}}while(0)}sendConnectMsgToMain(){let e=this.sendMessageToMainSync({name:s.CommonIPCMessage.msgIPCRendererConnect,args:[]});this.currInfo.id=e[0],this.rendererInfos=e[1]}sendDisconnectMsgToMain(){this.sendMessageToMain({name:s.CommonIPCMessage.msgIPCRendererDisconnect,args:[]})}sendIPCMsgToMain(e,t=!1){let n=void 0;do{if(i.isNullOrUndefined(e)){o.error("msg is null");break}n=(t?r.ipcRenderer.sendSync:r.ipcRenderer.send)(s.CommonIPCMessage.msgIPCSendToMain,{msg:e,senderInfo:this.currInfo})}while(0);return n}sendIPCMsgToRenderer(e,t){do{if(i.isNullOrUndefined(e)){o.error("rendererId is null");break}if(i.isNullOrUndefined(t)){o.error("msg is null");break}let n=[e].concat(t.args);t.args=n,r.ipcRenderer.send(s.CommonIPCMessage.msgIPCSendToRenderer,{msg:t,senderInfo:this.currInfo})}while(0)}}e.RendererCommunicator=t,e.rendererCommunicator=new t}(t.CommonIPCRenderer||(t.CommonIPCRenderer={}))},39:function(e,t){e.exports=require("crypto")},4:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(12),o=n(50),s=n(29),a=n(30);function l(e){a.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let c=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends i.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,r){do{if(t||(t=s.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(r)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))r("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{r("error",e)}),e.on("connect",()=>{r("connect")}),e.on("end",()=>{let t=e.isInprocess();r("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===s.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let i=new o.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=i,n||(this.client=i),i.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(i,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(i,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(i,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(i,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===i&&this.emit("stat_call_function_back",i.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let r=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,r));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),i.on("error",e=>{r&&r("error",e),this.emit("socket-error",e,i.getContext(),n,i.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),i.isInprocess()?(this.connectedMap[t.toLowerCase()]=i,r&&r("connect"),this.emit("connect",i.getContext(),n,!0)):i.on("connect",()=>{this.connectedMap[t.toLowerCase()]=i,r&&r("connect"),this.emit("connect",i.getContext(),n,!1)}),i.on("end",()=>{let e=i.isInprocess();a.information("server is ended, and this client emit end",t,n,e),r&&r("end",i.getContext(),n,e),this.emit("end",i.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(i)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return r(this,void 0,void 0,function*(){let r=null,i=yield this.crossCallServerFunctionEx(e,t,...n);return i&&(r=i[0]),r})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(r,t,...n)}}crossIsRemoteClientExist(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(r,t,n)}})}crossCallRemoteClientFunction(e,t,n,...r){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(i,t,n,...r)}}crossAttachServerEvent(e,t,n){let r=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");r=this.internalAttachServerEvent(i,t,n)}return r}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(r,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(r,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.generateId();const i={alias:t,action:"register",rid:r};let o=(e,r)=>{e?(a.error("register error",e.message),n(r)):n(t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(a.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===s.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,r)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let r=this.generateId();const i={action:"check_server_function_exist",method:t,rid:r};let o=(e,t)=>{n(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((r,i)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let i=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const o={rid:i,method:t,args:n};let s=(t,n)=>{t?(a.error("callServerFunction error",t,e.getContext()),r([null,t])):r([n,void 0])};this.retCallbackMap[i]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,r)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let r=this.generateId();const i={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:r};let o=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((r,i)=>{do{if(!e){r(!1);break}if(!t){r(!1);break}if(!n){r(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){r(!(!this.apis||!this.apis[n]));break}let i=this.generateId();const o={action:"check_client_function_exist",method:n,rid:i,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let s=(e,t)=>{r(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:s},o),this.sendAdapter(e,o)}while(0)})}internalCallRemoteClientFunction(e,t,n,...r){return new Promise((i,o)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"remote client alias is not specifed"]);break}if(!n){i([null,"funcName is not specifed"]);break}let o=(e,t)=>{e?(a.information("callRemoteClientFunction",e.message),i([null,e])):i([t,void 0])};if(r)for(let e=0;e<r.length;e++)r[e]=this.convertFunction2IdEx(r[e]);let s=this.generateId();const l={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:r,rid:s};this.retCallbackMap[s]=Object.assign({callback:o},l),this.sendAdapter(e,l)}while(0)})}internalAttachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(r)||(this.eventCallbackMaps[r]={}),this.eventCallbackMaps[r].hasOwnProperty(t)||(this.eventCallbackMaps[r][t]={}),s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let i=this.generateId();return this.eventCallbackMaps[r][t][i]=n,i}internalDetachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;delete this.eventCallbackMaps[r][t][n],s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;let e=this.eventCallbackMaps[r][t];for(let t in e){let r=e[t];r&&r.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...r){let i=void 0,o=!1;do{const s=this.contextCallbackMap[t];if(!s){a.error("the context function has been freeer",t),i={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let l=void 0,c=void 0;try{l=s.apply(null,...r)}catch(e){c=e.message;break}if(void 0===n||null===n)break;if(i={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==c){i.error=c;break}if(l&&l.then){l.then(t=>{i.data=this.convertFunction2IdEx(t),i.success=!0,this.sendAdapter(e,i)}).catch(t=>{i.error=t instanceof Error?t.message:t,this.sendAdapter(e,i)}),o=!0;break}i.success=!0,i.data=this.convertFunction2IdEx(l)}while(0);!o&&i&&this.sendAdapter(e,i)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":r}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let r=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(r){n=((...e)=>{this.callRemoteContextById(t,r,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:r},l);break}for(let n in e){let r=e[n];e[n]=this.decodeParameter(r,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,r=!1;do{if(!t)break;let i=t.method;if(!i)break;let o=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(o):[].concat(o)},!this.apis||!this.apis[i]){n.error=`callRegisterFunction ${i} is undefined`;break}let s=void 0,a=this.decodeParameter(t.args,t.src);try{s=this.apis[i].apply(null,[t.src].concat(a))}catch(e){n.error=e.message;break}if(s&&s.then){s.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),r=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(s)}while(0);a.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(e,n)}}),c=global.__xdasIPCClienInstance,t.client=c},41:function(e,t){e.exports=require("buffer")},42:function(e,t,n){"use strict";const r=n(13);if("renderer"===process.type){if(r.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(53);e.require=t.remoteRequire,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,r)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,r)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(r.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(57);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},47:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(51),i=n(12);t.Parser=class extends i.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),i=JSON.parse(e);this.emit("message",i),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},489:function(e,t,n){"use strict";n.r(t);var r=n(490),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},49:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(6),o=n(1),s=n(8),a=n(32),l=n(3),c=n(20),d=s.default(r.join(__rootDir,"../bin/ThunderHelper.node"));function u(){"console"===process.env.TL_OUTPUT?o.default.outputLogger=o.outputLoggerConsole:o.default.outputLogger=function(){function e(e){return function(...t){d.printEtwLog(e,function(...e){return e.map(e=>i.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[o.LogLevel.Critical]:e(o.LogLevel.Critical),[o.LogLevel.Error]:e(o.LogLevel.Error),[o.LogLevel.Warning]:e(o.LogLevel.Warning),[o.LogLevel.Information]:e(o.LogLevel.Information),[o.LogLevel.Verbose]:e(o.LogLevel.Verbose)}}()}function f(){let e=a.DevEnvHelperNS.isLogEnable();"1"===process.env.TL_ENABLE!==e&&(process.env.TL_ENABLE=e?"1":"0",o.default.enableLogger=e,d.enableETWLogger(e));let t=a.DevEnvHelperNS.getLogOutput();t&&t!==process.env.TL_OUTPUT&&(process.env.TL_OUTPUT=t,u())}process.env.TL_ENABLE="0",o.default.enableLogger="1"===process.env.TL_ENABLE,u(),f(),"browser"===process.type?l.ipcMain.on(c.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{l.BrowserWindow.getAllWindows().forEach(e=>{e.isDestroyed()||e.webContents.send(c.ThunderChannelList.channelMRUpdateLogEnviroment)}),f()}):"renderer"===process.type&&l.ipcRenderer.on(c.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{f()})},490:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,s=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(o<3?i(s):o>3?i(t,n,s):i(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5),s=n(3),a=n(7),l=n(167),c=n(63),d=n(998),u=n(4),f=n(11),h=n(34),_=n(693);let p=class extends o.Vue{constructor(){super(...arguments),this.browserType=h.parse(location.href,!0).query.browserType,this.title=h.parse(location.href,!0).query.title,this.minimizable=!1,this.resizable=!1,this.maximized=!1,this.type="",this.selfWindow=void 0,this.remoteMenuOptions=null}get canShowMenu(){let e=!1;do{if("news-popup"===this.browserType){e=!0;break}if(!this.remoteMenuOptions)break;if(this.remoteMenuOptions.length>0){e=!0;break}}while(0);return e}maximizeWnd(){this.selfWindow&&this.selfWindow.maximize()}restoreWnd(){this.selfWindow&&this.selfWindow.restore()}showMenu(e){return i(this,void 0,void 0,function*(){if("news-popup"===this.browserType){let t=[];t.push({type:"normal",label:"一周内不再弹出",click:()=>{this.menuItemClick("noshow")}});let n=yield(yield a.asyncRemoteCall.getMenu()).buildFromTemplate(t),r=yield a.asyncRemoteCall.getCurrentWindow();yield c.MenuSkinNS.setStyle(n,{}),d.ContextMenuNS.popupAt(n,{window:r},e.target)}else if(this.remoteMenuOptions&&this.remoteMenuOptions.length>0){let t=yield(yield a.asyncRemoteCall.getMenu()).buildFromTemplate(this.remoteMenuOptions),n=yield a.asyncRemoteCall.getCurrentWindow();yield c.MenuSkinNS.setStyle(t,{}),d.ContextMenuNS.popupAt(t,{window:n},e.target)}})}menuItemClick(e){return i(this,void 0,void 0,function*(){if("noshow"===e){f.XLStatNS.trackEvent("business","popup_close_set","",0,0,0,0,"op=week");let e=Math.round((new Date).getTime()/1e3);yield u.client.callServerFunction("SetConfigValue","ConfigNewsPopUp","NewsShowTime",e),this.closeWnd("noshow")}})}minimiseWnd(){return i(this,void 0,void 0,function*(){yield(yield a.asyncRemoteCall.getCurrentWindow()).minimize(),f.XLStatNS.trackEvent("business","popup_minimize")})}closeWnd(e){s.ipcRenderer.send(l.NodeEventMesssageNS.eventEmbeddedBrowserWndClick,this.browserType,e),"ad-webpagetip"===this.browserType&&s.ipcRenderer.send("ad-webpagetip","click_close"),window.close()}noPopup(e,t){return i(this,void 0,void 0,function*(){if("sign"===t){let t=[];t.push({type:"normal",label:"不再弹出",click:()=>i(this,void 0,void 0,function*(){let e=yield u.client.callServerFunction("GetConfigValue","ConfigPopUpPanel","StartCount",0);yield u.client.callServerFunction("SetConfigValue","ConfigPopUpPanel","StartCount",e+1);let t=Math.round((new Date).getTime()/1e3);yield u.client.callServerFunction("SetConfigValue","ConfigPopUpPanel","ShowTime",t),this.closeWnd("sign_nopop")})});let n=yield(yield a.asyncRemoteCall.getMenu()).buildFromTemplate(t),r=yield a.asyncRemoteCall.getCurrentWindow();yield c.MenuSkinNS.setStyle(n,{}),d.ContextMenuNS.popupAt(n,{window:r},e.target)}else if("news-popup"===t){let e=Math.round((new Date).getTime()/1e3);yield u.client.callServerFunction("SetConfigValue","ConfigNewsPopUp","NewsShowTime",e)}})}mounted(){return i(this,void 0,void 0,function*(){"ad-webpagetip"===this.browserType&&(document.addEventListener("mouseenter",this.handleMouseEnter),document.addEventListener("mouseleave",this.handleMouseLeave)),_.EmbeddedBrowserClientFunctionNS.getInstance().on("add_menu_extra",e=>{this.remoteMenuOptions=e});let e=yield a.asyncRemoteCall.getCurrentWindow();this.resizable=yield e.isResizable(),this.minimizable=yield e.isMinimizable(),this.selfWindow=e,(yield this.selfWindow.isMaximized())&&(this.maximized=!0),this.selfWindow.on("maximize",()=>{this.maximized=!0}),this.selfWindow.on("minimize",()=>{this.maximized=!1}),this.selfWindow.on("unmaximize",()=>{this.maximized=!1})})}handleMouseEnter(){s.ipcRenderer.send("ad-webpagetip","mouse_hover")}handleMouseLeave(){s.ipcRenderer.send("ad-webpagetip","mouse_leave")}};p=r([o.Component({components:{}})],p),t.default=p},5:function(e,t,n){e.exports=n(9)(213)},50:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(33),i=n(12),o=n(30),s=n(47),a=n(29);t.Client=class extends i.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=a.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new s.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{o.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){o.error(e.message)}else o.information("This socket has been ended by the other party",this.context&&this.context.name)}}},51:function(e,t){e.exports=require("string_decoder")},52:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(14),o=(n(21),n(2)),s=n(6);let a=null;const l=n(11),c=n(3),d=n(1),u=n(8),f=n(20),h=n(7),_="xdas_profile_stat";let p="",g=void 0,m=null,R=void 0,v=null,b=u.default(o.join(__rootDir,"../bin/ThunderHelper.node")),y=new Set;function w(){return r(this,void 0,void 0,function*(){return new Promise(e=>r(this,void 0,void 0,function*(){void 0===R&&(null===v&&(v=new Promise(e=>{e(R=function(e){let t="";if(0===e.length&&"renderer"===process.type){let e=o.normalize(decodeURIComponent(window.location.href)),n=e.indexOf(process.resourcesPath);n=n>-1?n+process.resourcesPath.length+1:n;let r=e.length-1,i=e.indexOf("?"),s=e.indexOf("#");r=i>-1?Math.min(i-1,r):r,r=s>-1?Math.min(s-1,r):r,n>-1&&r>=n&&(t=e.substr(n,r-n+1))}return 0===t.length&&(t=0!==e.length?e:process.type),t=t.replace(/\||,|;/g,"_")}(""))})),R=yield v),e(R)}))})}function C(e){let t="";do{if(null===e||void 0===e)break;switch(typeof e){case"string":t=e;break;case"object":t=s.inspect(e)||"";break;case"number":case"boolean":t=e.toString()||""}}while(0);return t}function E(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function S(e){return r(this,void 0,void 0,function*(){return new Promise(t=>r(this,void 0,void 0,function*(){let r=void 0;null===a&&(a=yield Promise.resolve().then(()=>n(39)));let i=a.createHash("md5");null!==i&&(r=i.update(e).digest("hex")),t(r)}))})}function T(){return new Promise(e=>r(this,void 0,void 0,function*(){let t="";t=void 0===g?"browser"===process.type?function(){if(void 0===g){let e=process.argv.length,t=process.argv;for(let n=0;n<e;n++){let e=t[n];if(e.startsWith("-StartType:")){g=e.substring("-StartType:".length);break}}void 0===g&&(g="")}return g}():yield function(){return r(this,void 0,void 0,function*(){return null===m&&(m=new Promise(e=>{c.ipcRenderer.send(f.ThunderChannelList.channelRMGetBrowserStartType),c.ipcRenderer.once(f.ThunderChannelList.channelMRGetBrowserStartTypeResult,(t,n)=>{g=n,e(n),m=null})})),m})}():g,e(t)}))}function M(e,t,n,i){return r(this,void 0,void 0,function*(){let o=C(t),s=C(n),a=yield S(s),c=function(e){let t=new RegExp(E("file:///"),"g"),n=new RegExp(E(process.resourcesPath+"\\"),"g"),r=new RegExp(E(encodeURI(process.resourcesPath.replace(/\\/g,"/")+"/")),"g");return e.replace(t,"").replace(n,"").replace(r,"")}(C(i)),d=yield S(c),u=`${e}:${a}:${d}`;y.has(u)||(y.add(u),l.XLStatNS.trackEvent(_,"uncaught_exception","",0,0,0,0,`type=${e},business_name=${yield w()},code=${o},message_hash=${a},message=${encodeURI(s)},stack_hash=${d},stack=${encodeURI(c)}`)),function(e,t,n,i){return r(this,void 0,void 0,function*(){})}().catch()})}function O(e){console.error(e);let t=e||{};M("unhandledRejection",t.code,t instanceof Error?t.message:t,t.stack).catch()}!function(e){e.init=function(e){p=e},e.trackColdStartUpEvent=function(e){return r(this,void 0,void 0,function*(){let t=b.iSColdStartUp()?1:0,n=i.release(),r=b.performanceMonitorReporter.getProcessUptime(),o=yield T(),s=`key=${e},start_type=${o},cold_start_up=${t},os_version=${n},cost_time=${r}`;l.XLStatNS.trackEvent(_,"cold_start_up","",0,0,0,0,s)})}}(t.PerformanceMonitorStatNS||(t.PerformanceMonitorStatNS={})),function(){return r(this,void 0,void 0,function*(){if(process.on("uncaughtException",e=>{console.error(e);let t=e||{};M("uncaughtException",t.code,t.message,t.stack).catch()}),"browser"===process.type)process.on("unhandledRejection",(e,t)=>{O(e)}),c.ipcMain.on(f.ThunderChannelList.channelRMGetBrowserStartType,function(e){return r(this,void 0,void 0,function*(){let t=yield T();e.sender.send(f.ThunderChannelList.channelMRGetBrowserStartTypeResult,t)})});else if("browser"!==process.type){window.addEventListener("unhandledrejection",e=>{O(e&&e.reason||{})});let e=yield h.asyncRemoteCall.getCurrentWebContents();null!==e&&void 0!==e&&e.once("did-finish-load",()=>{(function(){return r(this,void 0,void 0,function*(){do{if("browser"===process.type)break;if(null===window.performance.timing||void 0===window.performance.timing)break;let e=b.iSColdStartUp()?1:0,t=i.release(),n=window.performance.timing,r=n.loadEventEnd-n.navigationStart,o=n.fetchStart-n.navigationStart,s=n.domainLookupEnd-n.domainLookupStart,a=n.connectEnd-n.connectStart,c=n.responseStart-n.requestStart,d=n.responseEnd-n.responseStart,u=n.domComplete-n.domLoading,f=yield T();l.XLStatNS.trackEvent(_,"page_load_time","",0,0,0,0,`start_type=${f},cold_start_up=${e},os_version=${t},load_time=${r},before_fetch_time=${o},domin_lookup_time=${s},connect_time=${a},first_response_time=${c},responseTime=${d},domTime=${u},process=${p}`)}while(0)})})().catch()})}d.default.hook("beforeLog",(e,t,...n)=>{e===d.LogLevel.Critical&&l.XLStatNS.trackEvent(_,"critical_error","",0,0,0,0,`module_name=${t},messages=${n}`)})})}().catch()},53:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const r=n(3),i=n(41),o=n(54),s=n(55),a=n(35),l=n(56),c=n(13),d=n(36),u=r.ipcRenderer,f=process.electronBinding("v8_util"),h=new o.default,_=f.createIDWeakMap(),p=f.getHiddenValue(global,"contextId");class g{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function m(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let r=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:m(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(r))return{type:"buffer",value:i.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(d.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(f.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:f.getHiddenValue(e,"__remote_id__")};let r={type:e instanceof g?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)r.members.push({name:t,value:n(e[t])});return t.delete(e),r}if("function"==typeof e){return{type:"function",id:h.add(e),location:f.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function R(e,t,n){d.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function v(e,t,n,r=!1){const i=t=>{if(e.hasOwnProperty(t.name)&&!r)return;let n,i={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(_.has(t.value.refId)&&(n=_.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=C(t.value,e,t.name);i.get=(()=>n),i.set=(e=>n=e)}else"get"===t.type&&(i.get=(()=>n),t.writable&&(i.set=(e=>{n=e})),n=C(t.value));Object.defineProperty(e,t.name,i)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)i(n[t])}}function b(e,t,n){if(n){let t=C(n);Object.setPrototypeOf(e,t)}}function y(e,t){f.setHiddenValue(e,"__remote_id__",t)}function w(e){return f.getHiddenValue(e,"__remote_id__")}function C(e,t,n){const r={value:()=>e.value,array:()=>e.members.map(e=>C(e)),buffer:()=>i.Buffer.from(e.value),promise:()=>Promise.resolve({then:C(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:r,value:i}=e.members[n];t[r]=i}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let o;return e.type in r?o=r[e.type]():e.refId?_.has(e.refId)?(f.addRemoteObjectRef(p,e.refId),o=_.get(e.refId)):(c.warn("[metaToValue] refId point to null"+e.refId),o="function"===e.type?()=>{}:{}):e.id?_.has(e.id)?(f.addRemoteObjectRef(p,e.id),v(o=_.get(e.id),e.id,e.members,!0),b(o,e.id,e.proto)):(o="function"===e.type?t?function(e,t,n){if(_.has(n.id))return _.get(n.id);let r=w(e),i=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(i,"name",{value:t,writable:!1,enumerable:!0});let o=new Proxy(i,{apply:(e,n,i)=>new Promise((e,o)=>{let c=w(n);if(c||(c=w(n.__remoteObj_)),!c)throw Error("is this function was a bound function?");let d=a.default.browser.memberCall,f=l.default(d),h=m(i);u.send(d,p,f,c,r,t,h),s.default.add(f,t=>{try{R(C(t),e,o)}catch(e){o(e)}})}),construct:(e,n,i)=>new Promise((e,i)=>{let o=a.default.browser.memberConstruct,c=l.default(o);u.send(o,p,c,r,t,m(n)),s.default.add(c,t=>{try{let n=C(t);e(n)}catch(e){i(e)}})})});return f.setHiddenValue(i,"__remote_id__",n.id),o}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return y(n,t),new Proxy(n,{apply:(e,n,r)=>new Promise((e,i)=>{let o=a.default.browser.functionCall,c=l.default(o),d=w(n);u.send(o,p,c,d,t,m(r)),s.default.add(c,t=>{try{R(C(t),e,i)}catch(e){i(e)}})}),construct:(e,n,r)=>new Promise((e,r)=>{let i=a.default.browser.construct,o=l.default(i);u.send(i,p,o,t,m(n)),s.default.add(o,t=>{try{let n=C(t);e(n)}catch(e){r(e)}})})})}(e):{},f.setRemoteObjectFreer(o,p,e.id),_.set(e.id,o),f.setHiddenValue(o,"__remote_id__",e.id),f.addRemoteObjectRef(p,e.id),function(e){let t=w(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,r){if("function"==typeof r)throw new Error("set a function to a remote member is dangerous");return new Promise((i,o)=>{let c=a.default.browser.memberSet,d=l.default(c),f=m([r]);u.send(c,p,d,t,n,f),s.default.add(d,t=>{try{let s=C(t);e[n]=r,i(s)}catch(e){o(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((r,i)=>{let o=a.default.browser.memberGet,c=l.default(o);u.send(o,p,c,t,n),s.default.add(c,t=>{try{const o=C(t);e[n]=o,r(o)}catch(e){i(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let r=a.default.browser.sync,i=l.default(r);u.send(r,p,i,t),s.default.add(i,r=>{try{if(r.id!==t)throw Error("SYNC_RETURN: remote id not match");let i=C(r);e(i)}catch(e){n(e)}})})}}})}(o),v(o,e.id,e.members),b(o,e.id,e.proto),Object.defineProperty(o.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=o[e],r=o.__proto__;for(;r;){if(Object.prototype.hasOwnProperty.call(r,e)){delete r[e];break}r=r.__proto__}Object.defineProperty(o,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):c.error("no id of meta:",e),o}t.Interest=g;class E{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,r)=>{let i=this.getChannel(e),o=l.default(i);u.send(i,p,o,t),s.default.add(o,e=>{try{let t=C(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){r(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=a.default.browser.require;break;case"builtin":t=a.default.browser.builtIn;break;case"global":t=a.default.browser.global;break;case"current_window":t=a.default.browser.currentWindow;break;case"current_web_contents":t=a.default.browser.currentWebContents;break;case"client_web_contents":t=a.default.browser.clientWebContents;break;case"web_contents":t=a.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function S(e,t,n){try{s.default.invoke(t,n).remove(t)}finally{s.default.remove(t)}}u.on(a.default.renderer.requireReturn,S),u.on(a.default.renderer.getBuiltInReturn,S),u.on(a.default.renderer.getGlobalReturn,S),u.on(a.default.renderer.currentWindowReturn,S),u.on(a.default.renderer.currentWebContentsReturn,S),u.on(a.default.renderer.functionCallReturn,S),u.on(a.default.renderer.constructReturn,S),u.on(a.default.renderer.memberCallReturn,S),u.on(a.default.renderer.memberSetReturn,S),u.on(a.default.renderer.memberGetReturn,S),u.on(a.default.renderer.memberConstructReturn,S),u.on(a.default.renderer.callback,(e,t,n)=>{h.apply(t,C(n))}),u.on(a.default.renderer.syncReturn,S),u.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{c.info("[RELEASE_CALLBACK]: callbackId:",t),h.remove(t)}),process.on("exit",()=>{u.send(a.default.browser.contextRelease)});const T=["__resolve","__isResolved"],M=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],O=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function k(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(O(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(O(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(O(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(O(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(O(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(O(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,r)=>(O(e),Reflect.defineProperty(e.__remoteObj_,n,r)),get:(t,n,r)=>{if("string"==typeof n){if(String.prototype.includes.call(M,n)){return e[n]}if(String.prototype.includes.call(T,n)){return e[n]}}return O(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,r,i)=>(O(e),Reflect.set(e.__remoteObj_,n,r,i)),ownKeys:t=>(O(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,r)=>{O(e),Reflect.apply(e.__remoteObj_,n,r)},construct:(t,n,r)=>{if(O(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,r)=>{let i=a.default.browser.construct,o=l.default(i),c=f.getHiddenValue(e.__remoteObj_,"__remote_id__");u.send(i,p,o,c,m(n)),s.default.add(o,e=>{try{t(C(e))}catch(e){r(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=w(e);t&&y(n,t)}}),n}t.remoteRequire=function(e){return k(new E("module",e))},t.getBuiltin=function(e){return k(new E("builtin",e))},t.getGlobal=function(e){return k(new E("global",e))},t.getCurrentWindow=function(){return k(new E("current_window"))},t.getCurrentWebContents=function(){return k(new E("current_web_contents"))},t.getWebContents=function(){return k(new E("web_contents"))}},54:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=r.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,i=(new Error).stack;let o,s=n.exec(i);for(;null!==s;){const e=s[1];if(!e.includes("native")&&!e.includes("electron.asar")){o=/([^/^)]*)\)?$/gi.exec(e)[1];break}s=n.exec(i)}return this.callbacks[t]=e,r.setHiddenValue(e,"__remote_callback_id__",t),r.setHiddenValue(e,"__remote_call_location__",o),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(r.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}},55:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13);var i;!function(e){let t={};e.add=function(e,n,r){t[e]={func:n,thisArg:r}},e.invoke=function(n,...i){let o=t[n];return o?o.thisArg?o.func.apply(o.thisArg,...i):o.func(...i):r.error(`Cannot invoke function by unrecognize id. ${n}`),e},e.remove=function(e){delete t[e]}}(i||(i={})),t.default=i},56:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=0;t.default=function(e){return e?e.concat(".").concat(String(++r)):String(++r)}},57:function(e,t,n){"use strict";const r=n(3),i=n(58),o=n(35),s=n(59),a=n(13),l=n(36),c=r.ipcMain,d=process.electronBinding("v8_util");let u=d.createDoubleIDWeakMap();const f=new i.default;function h(e,t,n,r,i,o){let s,l=!1,c=null,d=!1;do{try{s=t[r]}catch(e){l=!0}if(l)try{s=n.value[r],l=!1,n.meta.extendedMemberNames.push(r),d=!0}catch(e){a.error(`property ${r} untouchable, even try root[name]`)}if(l)break;let i=Object.getOwnPropertyDescriptor(t,r);if(void 0===i){a.warn(`descriptor of property ${r} is undefined`);break}c={name:r,enumerable:i.enumerable,writable:!1,type:"get"},void 0===i.get&&"function"==typeof s?c.type="method":((i.set||i.writable)&&(c.writable=!0),c.type="get"),d?(c.configurable=!0,c.value=g(e,s,o,!1,null)):c.value=g(e,s,o,!1,n)}while(0);return c}function _(e,t,n,r=null){let i=Object.getOwnPropertyNames(t);"function"==typeof t&&(i=i.filter(function(e){return!String.prototype.includes.call(s.propertiesOfFunction,e)}));let o=[];do{if(0===i.length)break;let s=i.length;for(let a=0;a<s;a++){let s=h(e,t,n,i[a],0,r);s&&o.push(s)}}while(0);return o}function p(e,t,n,r=null){let i=null,o=Object.getPrototypeOf(t);return i=null===o||o===Object.prototype||o===Function.prototype?null:g(e,o,r,!1,n)}function g(e,t,n=null,r=!1,i=null){n=null===n?{}:n;const o={type:typeof t};"object"===o.type&&(o.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":l.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&d.getHiddenValue(e,"simple")&&(n="value"),n}(t,r));do{if("object"===o.type||"function"===o.type){let r=f.getIdOfObject(t);if(r&&n[r]){o.refId=r,f.add(e,t);break}}"array"===o.type?o.members=t.map(t=>g(e,t,n)):"object"===o.type||"function"===o.type?(null==i&&(o.extendedMemberNames=[],i={value:t,meta:o}),o.name=t.constructor?t.constructor.name:"",o.id=f.add(e,t),n[o.id]=!0,o.members=_(e,t,i,n),o.proto=p(e,t,i,n)):"buffer"===o.type?o.value=Buffer.from(t):"promise"===o.type?(t.then(function(){},function(){}),o.then=g(e,function(e,n){t.then(e,n)})):"error"===o.type?(o.members=m(t),o.members.push({name:"name",value:t.name})):"date"===o.type?o.value=t.getTime():(o.type="value",o.value=t)}while(0);return o}function m(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function R(e,t,n,i){const s=function(i){let l,c,h=0,_=0;switch(i.type){case"value":return i.value;case"remote-object":return f.get(i.id);case"array":return R(e,t,n,i.value);case"buffer":return Buffer.from(i.value);case"date":return new Date(i.value);case"promise":return Promise.resolve({then:s(i.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:i.name}),h=0,_=(c=i.members).length;h<_;h++)e[(l=c[h]).name]=s(l.value);return e}case"function":{const s=e.id,l=[n,i.id];if(a.info("renderer function id:"+l),u.has(l))return u.get(l);let c=function(...t){a.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||s!==e.id?function(e,t,n){let i="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof r.webContents.constructor){const{sender:e}=t[0],r=e.eventNames().filter(t=>{let r=e.listeners(t),i=!1;return r.forEach(e=>{e===n&&(i=!0)}),i});r.length>0&&(i+=`\nRemote event names: ${r.join(", ")}`,r.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}a.warn(i)}(i,n,c):e.send(o.default.renderer.callback,i.id,g(e,n))};return Object.defineProperty(c,"length",{value:i.length}),d.setRemoteCallbackFreer(c,t,n,i.id,e),u.set(l,c),c}default:throw new TypeError(`Unknown type: ${i.type}`)}};return i.map(s)}function v(e,t,n,r){let i,o;try{return t.apply(n,r)}catch(e){return o=t.name,new Error(`Could not call remote function '${i=null!=o?o:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function b(e){return{type:"exception",message:e.message,stack:e.stack||e}}function y(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var w;!function(e){const t=(e,t,...n)=>{const r=e.sender;r.isDestroyed()?a.warn("webcontext is destroyed."):r.send(t,...n)};e.startServer=function(){c.on(o.default.browser.require,(e,n,r,i)=>{a.info(`[REQUIRE] module=${i} `);let s=process.mainModule.require(i),l=g(e.sender,s);t(e,o.default.renderer.requireReturn,r,l)}),c.on(o.default.browser.builtIn,(e,n,i,s)=>{a.info(`[BUILTIN]: property=${s} contextId=${n}`);let l=r[s],c=g(e.sender,l);a.info(`[BUILTIN]: returns remoteId:${c.id}, type: ${typeof l}`),t(e,o.default.renderer.getBuiltInReturn,i,c)}),c.on(o.default.browser.global,(e,n,r,i)=>{a.info(`[GLOBAL]: proerty:${i} contextId=${n}`);let s,l=global[i];s=g(e.sender,l),a.info(`[GLOBAL]: returns remoteid=${s.id}, obj=`+typeof l),t(e,o.default.renderer.getGlobalReturn,r,s)}),c.on(o.default.browser.currentWindow,(e,n,r,i)=>{a.info(`[CURRENT_WINDOW]: property=${i} contextId=${n}`);let s=e.sender.getOwnerBrowserWindow.call(e.sender),l=g(e.sender,s);a.info(`[CURRENT_WINDOW]: returns remoteid=${l.id}, obj=`+s),t(e,o.default.renderer.currentWindowReturn,r,l)}),c.on(o.default.browser.currentWebContents,(e,n,r,i)=>{t(e,o.default.renderer.currentWebContentsReturn,r,g(e.sender,e.sender))}),c.on(o.default.browser.webContents,(e,n,i,s)=>{a.info(`[WebContents]: proerty:${s} contextId=${n}`);let l,c=r.webContents;l=g(e.sender,c),a.info(`[WebContents]: returns remoteid=${l.id}, obj=`+typeof c),t(e,o.default.renderer.webContentsReturn,i,l)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(r=>{n?e.once(r,t[r]):e.on(r,t[r])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};c.on(o.default.browser.construct,(n,r,i,s,l)=>{let c,d=null;try{a.info(`[CONSTRUCTOR]: remoteId=${s} `);let u=l.length>0?l[l.length-1]:null;l=R(n.sender,n.frameId,r,l);let h=f.get(s);null==h&&y(`Cannot call constructor on missing remote object ${s}`),u&&"interest"===u.type&&(d=l.pop());let _=new(Function.prototype.bind.apply(h,[null,...l]));_&&d&&e(_,d),c=g(n.sender,_,null,!1),a.info(`[CONSTRUCTOR]: returns remoteId =${c.id} name=${h.name} `)}catch(e){c=b(e)}finally{t(n,o.default.renderer.constructReturn,i,c)}}),c.on(o.default.browser.functionCall,function(e,n,r,i,s,l){let c;try{a.info(`[FUNCTION_CALL]: remoteId=${s}`),l=R(e.sender,e.frameId,n,l);let d=f.get(s);if(null==d)a.error(`Cannot call function on missing remote object ${s}`),c=g(e.sender,void 0);else{let t=i?f.get(i):global;if(t){let n=v(0,d,t,l);c=g(e.sender,n)}else a.error(`Cannot call function(${s}) on missing context(${i})`),c=g(e.sender,void 0)}a.info(`[FUNCTION_CALL]: name=${d.name}`)}catch(e){c=b(e)}finally{t(e,o.default.renderer.functionCallReturn,r,c)}}),c.on(o.default.browser.memberCall,function(e,n,r,i,s,l,c){let d;a.info(`[MEMBER_CALL]: thisArg=${i}, remoteId=${s}, method=${l}, args count=${c.length}`);try{c=R(e.sender,e.frameId,n,c);let u=f.get(s);null==u&&y(`Cannot call function '${l}' on missing remote object ${s}`);let h=i?f.get(i):u;if(h){let t=v(0,u[l],h,c);d=g(e.sender,t),a.info("[MEMBER_CALL]: return="+t)}else d=g(e.sender,void 0)}catch(e){d=b(e)}finally{t(e,o.default.renderer.memberCallReturn,r,d)}}),c.on(o.default.browser.memberGet,function(e,n,r,i,s){let l;try{a.info(`[MEMBER_GET]: remoteId=${i}, property=`,s);let n=f.get(i);null==n&&y(`Cannot get property '${Object.toString.call(s)}' on missing remote object ${i}`);let c=n[s];l=g(e.sender,c)}catch(e){l=b(e)}finally{t(e,o.default.renderer.memberGetReturn,r,l)}}),c.on(o.default.browser.memberSet,function(e,n,r,i,s,l){try{a.info(`[MEMBER_SET]: remoteId=${i}, property=`+s),l=R(e.sender,e.frameId,n,l);let c=f.get(i);null==c&&y(`Cannot set property '${Object.toString.call(s)}' on missing remote object ${i}`),c[s]=l[0],t(e,o.default.renderer.memberSetReturn,r,{type:"value",value:!0})}catch(n){t(e,o.default.renderer.memberSetReturn,r,b(n))}}),c.on(o.default.browser.memberConstruct,function(n,r,i,s,l,c){let d,u=null;try{a.info(`[MEMBER_CONSTRUCTOR]: regId=${s}, method=${l}`);let h=c.length>0?c[c.length-1]:null;c=R(n.sender,n.frameId,r,c);let _=f.get(s);null==_&&y(`Cannot call constructor '${l}' on missing remote object ${s}`),h&&"interest"===h.type&&(u=c.pop());let p=_[l],m=new(Function.prototype.bind.apply(p,[null,...c]));m&&u&&e(m,u),d=g(n.sender,m)}catch(e){d=b(e)}finally{t(n,o.default.renderer.memberConstructReturn,i,d)}}),c.on(o.default.browser.sync,function(e,n,r,i){let s=f.get(i);t(e,o.default.renderer.syncReturn,r,g(e.sender,s))}),c.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=f.get(t);if(r.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let r=n.name;r||(r=n.constructor?n.constructor.name:""),f.remove(e.sender.id,t)}else t<0&&a.warn("remote id reference to nothing:",t)}),c.on(o.default.browser.contextRelease,e=>{f.clear(e.sender.id)})},e.getObjectRegistry=function(){return f}}(w||(w={})),e.exports=w},572:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"td-cover"},[n("div",{staticClass:"td-dialog xlx-dialog-loading",class:{"xlx-dialog-loading--tips":"ad-webpagetip"===e.browserType,"xlx-dialog-loading--news":"ad-webpagetip"!==e.browserType}},[n("div",{staticClass:"td-dialog__header"},[n("h2",[e._v(e._s(e.title))]),e._v(" "),"sign"===e.browserType?n("a",{staticClass:"td-dialog__min",attrs:{href:"javascript:;",title:"其他"},on:{click:function(t){e.noPopup(t,"sign")}}},[n("i",{staticClass:"td-icon-arrow-down"})]):e._e(),e._v(" "),e.canShowMenu?n("a",{staticClass:"td-dialog__other",attrs:{href:"javascript:;",title:"其他"},on:{click:e.showMenu}},[n("i",{staticClass:"td-icon-arrow-down"})]):e._e(),e._v(" "),"news-popup"===e.browserType||e.minimizable?n("a",{staticClass:"td-dialog__min",attrs:{href:"javascript:;",title:"最小化"},on:{click:e.minimiseWnd}},[n("i",{staticClass:"xly-icon-minimize"})]):e._e(),e._v(" "),e.resizable&&!e.maximized?n("a",{staticClass:"td-dialog__min",attrs:{href:"javascript:;",title:"最大化"},on:{click:e.maximizeWnd}},[n("i",{staticClass:"xly-icon-maximize"})]):e._e(),e._v(" "),e.resizable&&e.maximized?n("a",{staticClass:"td-dialog__min",attrs:{href:"javascript:;",title:"还原"},on:{click:e.restoreWnd}},[n("i",{staticClass:"xly-icon-reduce"})]):e._e(),e._v(" "),n("a",{staticClass:"td-dialog__close",attrs:{href:"javascript:;",title:"关闭"},on:{click:function(t){e.closeWnd("btn_close")}}},[n("i",{staticClass:"td-icon-close"})])]),e._v(" "),n("div",{staticClass:"td-dialog__body"},[n("div",{staticClass:"xlx-dialog-loading__load"},[n("i",{staticClass:"td-icon-load"}),e._v(" "),"news-popup"===e.browserType?n("p",[e._v("正在加载...")]):e._e()])])])])},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},58:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13),i=-1*Math.pow(2,31),o=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),r=e.id;let i=this.owners.get(r);return i||(i=new Set,this.owners.set(r,i),this.registerDeleteListener(e,r)),i.has(n)||(i.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return o.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=o.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=i)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),o.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(o.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),i=(o,s)=>{s===n&&(r.info("render-view-deleted: processid="+n),(()=>{r.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",i),this.clear(t))};e.on("render-view-deleted",i)}}},59:function(e,t,n){"use strict";var r;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(r||(r={})),e.exports=r},6:function(e,t){e.exports=require("util")},60:function(e,t){e.exports=require("readline")},61:function(e,t,n){e.exports=n(9)(216)},62:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(7),o=n(18),s=n(4),a=n(32);!function(e){function t(e,t){return r(this,void 0,void 0,function*(){if(null!==e){let n=e.webContents;(yield n.isDevToolsOpened())?yield n.closeDevTools():yield n.openDevTools(t)}})}e.openDevTool=t,e.enableDevTools=function(e){return r(this,void 0,void 0,function*(){window.addEventListener("keyup",n=>r(this,void 0,void 0,function*(){if("F12"===n.key&&n.ctrlKey)a.DevEnvHelperNS.isLogEnable()&&(yield t(yield i.asyncRemoteCall.getCurrentWindow(),e));else if(("t"===n.key||"T"===n.key)&&n.altKey&&a.DevEnvHelperNS.isLogEnable()){let e=document.getElementById("DevProcessPid");if(e)document.body.removeChild(e);else{(e=document.createElement("p")).id="DevProcessPid",e.style.position="absolute",e.style.left="0px",e.style.top="0px",e.style.width="100%",e.style.zIndex="10000",e.style.color="rgb(255,0,0)",document.body.appendChild(e);let t="process.pid:"+process.pid;t+="\r\nlocation.href:"+location.href,t+="\r\nprocess.argv:"+process.argv,e.innerText=t}}}),!0)})},e.enableDragOpenFile=function(e){void 0===e&&(e=!1),document.addEventListener("dragover",e=>(e.preventDefault(),e.stopPropagation(),!1),!0),document.addEventListener("drop",e=>r(this,void 0,void 0,function*(){e.preventDefault(),e.stopPropagation();let t=e.dataTransfer,n=t.files,r=t.items;if(void 0!==r&&null!==r&&r.length>0)for(let e=0;e<r.length;e++){let t=r[e];"string"===t.kind&&"text/uri-list"===t.type?t.getAsString(e=>{s.client.callServerFunction("DropOpenUrl",e).catch()}):t.kind}if(void 0!==n&&null!==n&&n.length>0)for(let e=0;e<n.length;e++){let t=n[e].path;void 0!==t&&null!==t&&""!==t&&(yield o.FileSystemAWNS.existsAW(t))&&s.client.callServerFunction("DropOpenFile",t).catch()}return!1}),!0)}}(t.ThunderToolsNS||(t.ThunderToolsNS={}))},63:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(7),o=n(1),s=n(16),a=o.default.getLogger("MenuSkinNS");!function(e){e.setStyle=function(e,t){return r(this,void 0,void 0,function*(){if(a.information("setStyle",e,t),null!==e){const{WindowPreferenceNS:e}=yield Promise.resolve().then(()=>n(73));let t={windowPreference:e.getWindowPreference()};a.information("skinOpts",t)}})},e.popEditableDefaultContextMenu=function(e,t,n){return r(this,void 0,void 0,function*(){let n=yield i.asyncRemoteCall.getCurrentWebContents();n.once("context-menu",(o,l)=>r(this,void 0,void 0,function*(){if(a.verbose(o),l.isEditable){let r=[{label:"撤销",enabled:l.editFlags.canUndo,click:()=>{n.undo()}},{type:"separator"},{label:"剪切",enabled:l.editFlags.canCut,click:()=>{n.cut()}},{label:"复制",enabled:l.editFlags.canCopy,click:()=>{n.copy()}},{label:"粘贴",enabled:l.editFlags.canPaste&&s.ThunderUtil.isClipboardTextFormatAvailable(),click:()=>{n.paste()}},{label:"删除",enabled:l.editFlags.canDelete,click:()=>{n.delete()}},{type:"separator"},{label:"全选",enabled:l.editFlags.canSelectAll,click:()=>{n.selectAll()}}];if(void 0!==e&&"function"==typeof e){let n=e(l);void 0!==n&&n.length>0&&(void 0===t?t=r.length:(t<0&&(t=r.length+1+t)<0&&(t=0),t>r.length&&(t=r.length)),r.splice(t,0,...n))}let o=yield(yield i.asyncRemoteCall.getMenu()).buildFromTemplate(r),a=yield i.asyncRemoteCall.getCurrentWindow();yield o.popup({window:a})}}))})}}(t.MenuSkinNS||(t.MenuSkinNS={}))},65:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(4),i=n(1).default.getLogger("common/skin"),o=!0;let s=null;function a(e){if(!o||null===e||void 0===e)return;let t=localStorage.getItem("skin_body_classes");s&&s.classes===e.classes||(localStorage.removeItem("skin_body_classes"),e&&e.classes&&(document.body.classList.add(e.classes),localStorage.setItem("skin_body_classes",e.classes)),s&&s.classes?document.body.classList.remove(s.classes):t!==e.classes&&document.body.classList.remove(t),s=Object.freeze(Object.assign({},e)))}r.client.callServerFunction("GetSkinInfo").then(a).catch(e=>{i.warning(e)}),r.client.attachServerEvent("OnChangeSkin",(e,...t)=>{a(t[0])})},693:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12),i=n(1),o=n(4),s=i.default.getLogger("EmbeddedBrowser_PluginFunction");!function(e){const t=new class extends r.EventEmitter{constructor(){super(),this.isRegister=!1}registerClientFunction(){if(!this.isRegister){this.isRegister=!0;let e={AddMenu:this.addMenu.bind(this)};o.client.registerFunctions(e)}}buildMenuTemplate(e,t){let n=[];for(let r=0;r<t.length;r++){let i=null,o=t[r];if(!1!==o.visible){if("submenu"===o.type){let t=this.buildMenuTemplate(e,o.submenu);i={type:o.type,label:o.label,submenu:t}}else i="separator"===o.type?{type:o.type}:{type:o.type,label:o.label,enabled:o.enabled,icon:o.icon,id:o.id,checked:o.checked,click:()=>{o.click()}};null!==i&&n.push(i)}}return n}addMenu(e,...t){do{let n=t[0];if(s.information("addMenu in",t[0]),!n||0===n.length)break;let r=this.buildMenuTemplate(e,n);if(!r||0===r.length)break;s.information("addMenu",r),this.emit("add_menu_extra",r)}while(0)}};e.registerClientFunction=function(){t.registerClientFunction()},e.getInstance=function(){return t}}(t.EmbeddedBrowserClientFunctionNS||(t.EmbeddedBrowserClientFunctionNS={}))},7:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(1).default.getLogger("async-remote-call"),o=n(42),s=n(12),a=n(6);t.asyncRemoteCall=new class extends s.EventEmitter{constructor(){super(),this.mapObj=new Map,this.mapObjIniting=new Map,"renderer"!==process.type&&i.warning('can not import "renderer-process-call" module in non-renderer process',process.type)}getAppName(){return r(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}getAppVersion(){return r(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return r(this,void 0,void 0,function*(){return o.global.process.__resolve()})}getIpcMain(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getBrowserWindow(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getWebContents(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("webContents")})}getGlobalShortcut(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield o.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emit("OnInitCurrentWebContents",e),this.listeners("OnInitCurrentWebContents").forEach(e=>{this.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield o.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emit("OnInitCurrentWindow",e),this.listeners("OnInitCurrentWindow").forEach(e=>{this.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return r(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return a.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>r(this,void 0,void 0,function*(){this.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield o.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emit(e,t),this.listeners(e).forEach(t=>{this.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}},73:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(80),i=n(74);!function(e){e.getWindowPreference=function(e=!1){let t=i.default(),n={};return t&&t.colors&&"string"==typeof t.colors.colorPrimaryControl1&&(n.hoverBackgroundColor=e?parseInt(r.ColorUtilNS.rgbaStringToHexWith0xBegin(t.colors.colorPrimaryControl1),16):r.ColorUtilNS.rgbaStringToHexWith0xBegin(t.colors.colorPrimaryControl1)),n}}(t.WindowPreferenceNS||(t.WindowPreferenceNS={}))},74:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(1),s=n(4),a=o.default.getLogger("GetSkinInfo");let l;(function(){return r(this,void 0,void 0,function*(){"renderer"===process.type?(a.information("renderer process"),s.client.callServerFunction("GetSkinInfo").then(e=>{l=e,a.information("send OnChangeSkin",e)}).catch(e=>{a.warning(e)}),s.client.attachServerEvent("OnChangeSkin",(e,t)=>{l=t,a.information("send OnChangeSkin",t)})):"browser"===process.type&&(a.information("main process"),i.ipcMain.on("OnChangeSkin",(e,t)=>{a.information("OnChangeSkin",t),l=t}))})})().catch(e=>{a.information(e)}),t.default=function(){return l}},8:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},80:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){function t(e){let t=e.toString(16).toUpperCase();return t.length<2&&(t="0"+t),t}function n(e,n,r,i){return"0x"+t(i)+t(e)+t(n)+t(r)}e.rgbaStringToHexWith0xBegin=function(e){if(void 0!==e){let t=e.split(",");return n(parseInt(t[0]||"0",10),parseInt(t[1]||"0",10),parseInt(t[2]||"0",10),parseInt(t[3]||"255",10))}},e.colorNumberToHex=t,e.rgbaToHexWith0xBegin=n}(t.ColorUtilNS||(t.ColorUtilNS={}))},86:function(e,t){!function(e){var t,n='<svg><symbol id="td-icon-svg-file" viewBox="0 0 1204 1024"><path d="M180.705882 1024c-102.4 0-180.705882-78.305882-180.705882-180.705882V180.705882c0-102.4 78.305882-180.705882 180.705882-180.705882h240.941177c102.4 0 180.705882 78.305882 180.705882 180.705882h421.647059c102.4 0 180.705882 78.305882 180.705882 180.705883v481.882353c0 102.4-78.305882 180.705882-180.705882 180.705882H180.705882z" fill="#FFC25A" ></path><path d="M301.176471 361.411765h602.352941c66.258824 0 120.470588 54.211765 120.470588 120.470588v361.411765c0 66.258824-54.211765 120.470588-120.470588 120.470588H301.176471c-66.258824 0-120.470588-54.211765-120.470589-120.470588V481.882353c0-66.258824 54.211765-120.470588 120.470589-120.470588z" fill="#FFFFFF" ></path><path d="M180.705882 542.117647h843.294118c102.4 0 180.705882 78.305882 180.705882 180.705882v120.470589c0 102.4-78.305882 180.705882-180.705882 180.705882H180.705882c-102.4 0-180.705882-78.305882-180.705882-180.705882v-120.470589c0-102.4 78.305882-180.705882 180.705882-180.705882z" fill="#FFD68F" ></path></symbol></svg>',r=function(e,t){t.firstChild?function(e,t){t.parentNode.insertBefore(e,t)}(e,t.firstChild):t.appendChild(e)};if((t=document.getElementsByTagName("script"))[t.length-1].getAttribute("data-injectcss")&&!e.__iconfont__svg__cssinject__){e.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}!function(t){if(document.addEventListener)if(~["complete","loaded","interactive"].indexOf(document.readyState))setTimeout(t,0);else{var n=function(){document.removeEventListener("DOMContentLoaded",n,!1),t()};document.addEventListener("DOMContentLoaded",n,!1)}else document.attachEvent&&function(e,t){var n=e.document,r=!1,i=function(){r||(r=!0,t())},o=function(){try{n.documentElement.doScroll("left")}catch(e){return void setTimeout(o,50)}i()};o(),n.onreadystatechange=function(){"complete"==n.readyState&&(n.onreadystatechange=null,i())}}(e,t)}(function(){var e,t;(e=document.createElement("div")).innerHTML=n,n=null,(t=e.getElementsByTagName("svg")[0])&&(t.setAttribute("aria-hidden","true"),t.style.position="absolute",t.style.width=0,t.style.height=0,t.style.overflow="hidden",r(t,document.body))})}(window)},9:function(e,t){e.exports=vendor_0aff229d1d3a2d2be355},995:function(e,t,n){n(49),e.exports=n(996)},996:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(38),i=n(34),o=n(4),s=n(693);let a=i.parse(location.href,!0).query.browserType;a&&(o.client.start({name:`EmbeddedBrowserRendererContext${a}`}),s.EmbeddedBrowserClientFunctionNS.registerClientFunction()),r.CommonIPCRenderer.rendererCommunicator.initialize("EmbeddedBrowserRendererContext"),r.CommonIPCRenderer.rendererCommunicator.connect();const l=n(52),c=n(3),d=n(31);n(61),n(86);const u=n(62),f=n(997);n(65);const h=n(32);l.PerformanceMonitorStatNS.init("embedded-browser-renderer"),u.ThunderToolsNS.enableDragOpenFile(),u.ThunderToolsNS.enableDevTools().catch(e=>{}),d.default.config.ignoredElements=["webview"],new d.default({components:{EmbeddedBrowserComponent:f.default},render:e=>e("embedded-browser-component")}).$mount("#app"),window.addEventListener("keyup",e=>{"F12"===e.key&&e.ctrlKey?h.DevEnvHelperNS.isDevToolsEnable()&&c.ipcRenderer.send("eventShowDevTool","wnd"):"F12"===e.key&&h.DevEnvHelperNS.isDevToolsEnable()&&c.ipcRenderer.send("eventShowDevTool","browser")},!0)},997:function(e,t,n){"use strict";n.r(t);var r=n(572),i=n(489);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);n(999),n(1e3),n(1001),n(242);var s=n(0),a=Object(s.a)(i.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\embedded-browser-renderer\\app.vue",t.default=a.exports},998:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(s,a)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(7),o=n(63),s=n(1).default.getLogger("ConTextMenu");!function(e){e.popContextMenu=function(e,t){t&&(s.information("params",t),t.isEditable?function(e,t){return r(this,void 0,void 0,function*(){let e=[{label:"撤销",role:"undo",enabled:t.editFlags.canUndo},{type:"separator"},{label:"剪切",role:"cut",enabled:t.editFlags.canCut},{label:"复制",role:"copy",enabled:t.editFlags.canCopy},{label:"粘贴",role:"paste",enabled:t.editFlags.canPaste},{label:"删除",role:"delete",enabled:t.editFlags.canDelete},{type:"separator"},{label:"全选",role:"selectAll",enabled:t.editFlags.canSelectAll}],n=yield(yield i.asyncRemoteCall.getMenu()).buildFromTemplate(e);yield o.MenuSkinNS.setStyle(n,{});let r=yield i.asyncRemoteCall.getCurrentWindow();yield n.popup({window:r})})}(0,t).catch():""!==t.selectionText&&function(e,t){return r(this,void 0,void 0,function*(){let e=[{label:"复制",role:"copy",enabled:t.editFlags.canCopy}],n=yield(yield i.asyncRemoteCall.getMenu()).buildFromTemplate(e);yield o.MenuSkinNS.setStyle(n,{});let r=yield i.asyncRemoteCall.getCurrentWindow();yield n.popup({window:r})})}(0,t).catch())},e.popupAt=function(e,t,n){{let e=n.getBoundingClientRect();t.x=void 0!==t.x?t.x:Math.round(e.left),t.y=void 0!==t.y?t.y:Math.round(e.bottom)}e.popup(t)}}(t.ContextMenuNS||(t.ContextMenuNS={}))},999:function(e,t,n){"use strict";var r=n(1297);n.n(r).a}});
//# sourceMappingURL=renderer.js.map