# 植物大战僵尸内存工具 - 安装使用指南

## 🚀 快速开始

### 第一步：检查系统要求
- ✅ Windows 7/8/10/11 操作系统
- ✅ Python 3.7 或更高版本
- ✅ 植物大战僵尸游戏（原版推荐）

### 第二步：下载工具
确保以下文件都在同一个文件夹中：
```
植物大战僵尸内存工具/
├── memory_tool.py          # 主程序
├── memory_engine.py        # 内存引擎
├── game_definitions.py     # 游戏定义
├── cheat_functions.py      # 外挂功能
├── utils.py               # 工具函数
├── config.json            # 配置文件
├── start_memory_tool.bat  # 启动脚本
├── test_tool.py           # 测试脚本
└── README.md              # 说明文档
```

### 第三步：安装依赖
**方法一：自动安装（推荐）**
1. 双击 `start_memory_tool.bat`
2. 脚本会自动检查并安装依赖

**方法二：手动安装**
```bash
pip install psutil
```

### 第四步：启动工具
**方法一：使用启动脚本**
- 双击 `start_memory_tool.bat`

**方法二：命令行启动**
```bash
python memory_tool.py
```

## 🎮 使用教程

### 连接游戏
1. **启动植物大战僵尸游戏**
   - 确保游戏正在运行
   - 建议进入任意关卡

2. **附加进程**
   - 在工具中点击"附加进程"按钮
   - 等待状态显示为绿色"已连接"
   - 如果连接失败，请以管理员权限运行工具

### 使用外挂功能

#### 无限阳光
1. 切换到"外挂功能"标签页
2. 勾选"无限阳光"
3. 可以修改"数量"框中的值（默认9990）
4. 游戏中的阳光会自动维持在设定值

#### 无限金钱
1. 勾选"无限金钱"
2. 可以修改"数量"框中的值（默认99999）
3. 商店中的金钱会自动维持在设定值

#### 植物无敌
1. 勾选"植物无敌"
2. 所有植物的生命值会自动恢复到满血
3. 植物不会被僵尸吃掉

#### 僵尸秒杀
1. 勾选"僵尸秒杀"
2. 所有僵尸的生命值会被设为1
3. 任何攻击都能秒杀僵尸

#### 冻结僵尸
1. 勾选"冻结僵尸"
2. 所有僵尸的移动速度会被设为0
3. 僵尸会停在原地不动

#### 快捷操作
- **启用所有外挂**：一键开启所有功能
- **禁用所有外挂**：一键关闭所有功能
- **保存配置**：保存当前设置

### 监控游戏状态

#### 游戏状态标签页
- **阳光数量**：当前阳光值
- **金钱数量**：当前金钱值
- **关卡**：当前关卡编号
- **波数**：当前波数
- **植物数量**：场上植物总数
- **僵尸数量**：场上僵尸总数

#### 实体监控标签页
- **植物列表**：显示所有植物的类型、位置、生命值
- **僵尸列表**：显示所有僵尸的类型、位置、生命值
- 数据实时更新，可以观察游戏内部状态

### 内存编辑（高级功能）

#### 读取内存
1. 切换到"内存编辑"标签页
2. 输入内存地址（如：0x6A9EC0）
3. 选择数据类型（int32、float、string、bytes）
4. 点击"读取"查看当前值

#### 写入内存
1. 在"新值"框中输入要写入的值
2. 点击"写入"修改内存
3. 系统会自动验证写入结果

## 🔧 故障排除

### 常见问题

#### 问题1：无法找到游戏进程
**症状**：点击"附加进程"后显示"未找到进程"

**解决方案**：
1. 确保植物大战僵尸游戏正在运行
2. 检查进程名称是否为"PlantsVsZombies.exe"
3. 尝试以管理员权限运行工具
4. 重启游戏后再次尝试

#### 问题2：外挂功能不生效
**症状**：勾选外挂功能后游戏没有变化

**解决方案**：
1. 确保已成功连接到游戏（状态为绿色）
2. 检查游戏版本是否兼容（推荐原版）
3. 查看日志标签页的错误信息
4. 尝试重新连接游戏

#### 问题3：程序启动失败
**症状**：双击启动脚本后程序无法运行

**解决方案**：
1. 检查Python版本（需要3.7+）
   ```bash
   python --version
   ```
2. 安装缺失的依赖包
   ```bash
   pip install psutil
   ```
3. 确保所有文件在同一目录
4. 尝试以管理员权限运行

#### 问题4：杀毒软件误报
**症状**：杀毒软件提示程序有风险

**解决方案**：
1. 这是正常现象，因为程序需要访问其他进程内存
2. 将程序添加到杀毒软件白名单
3. 或者临时关闭实时保护
4. 程序是开源的，可以查看源代码确认安全性

### 性能优化

#### 降低CPU占用
1. 打开 `config.json` 文件
2. 修改 `scan_interval` 值（默认100ms）
3. 增大数值可以降低CPU占用，但会影响响应速度

#### 内存占用优化
1. 关闭不需要的外挂功能
2. 避免同时开启多个内存工具
3. 定期重启程序释放内存

## ⚙️ 配置说明

### 配置文件 (config.json)
```json
{
    "window": {
        "width": 900,           // 窗口宽度
        "height": 700,          // 窗口高度
        "title": "工具标题"      // 窗口标题
    },
    "game": {
        "process_name": "PlantsVsZombies.exe",  // 游戏进程名
        "auto_attach": true,                     // 自动连接
        "scan_interval": 100                     // 扫描间隔(ms)
    },
    "cheats": {
        "default_sun_amount": 9990,    // 默认阳光数量
        "default_money_amount": 99999, // 默认金钱数量
        "auto_enable": []              // 自动启用的外挂
    }
}
```

### 自定义配置
1. 修改默认阳光/金钱数量
2. 设置自动启用的外挂功能
3. 调整界面大小和扫描频率
4. 保存后重启程序生效

## 🛡️ 安全提醒

### 使用建议
1. **仅限单机使用**：不要在联网游戏中使用
2. **备份存档**：使用前备份游戏存档文件
3. **适度使用**：避免过度依赖外挂功能
4. **定期更新**：保持工具版本最新

### 风险提示
1. **游戏崩溃**：错误的内存操作可能导致游戏崩溃
2. **存档损坏**：极少数情况下可能影响存档
3. **检测风险**：虽然是单机游戏，但仍需谨慎使用
4. **法律风险**：遵守相关法律法规和游戏条款

## 📞 技术支持

### 获取帮助
1. **查看日志**：程序运行时会生成详细日志
2. **运行测试**：使用 `test_tool.py` 检查功能
3. **查看文档**：阅读 `README.md` 详细说明
4. **重置配置**：删除 `config.json` 恢复默认设置

### 报告问题
如果遇到问题，请提供以下信息：
1. 操作系统版本
2. Python版本
3. 游戏版本
4. 错误日志内容
5. 具体操作步骤

## 🎉 开始使用

现在你已经了解了工具的基本使用方法，可以开始享受增强的游戏体验了！

**记住**：
- 🎮 合理使用，保持游戏乐趣
- 🛡️ 注意安全，备份重要数据
- 📚 遇到问题时查看文档
- 🔄 定期更新工具版本

**祝你游戏愉快！** 🌻🧟‍♂️
