module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=710)}([function(e,t,n){"use strict";function r(e,t,n,r,i,o,a,s){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),o&&(c._scopeId="data-v-"+o),a?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},c._ssrRegister=l):i&&(l=s?function(){i.call(this,this.$root.$options.shadowRoot)}:i),l)if(c.functional){c._injectStyles=l;var u=c.render;c.render=function(e,t){return l.call(t),u(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}n.d(t,"a",function(){return r})},function(e,t,n){e.exports=n(9)(137)},function(e,t){e.exports=require("path")},function(e,t){e.exports=require("electron")},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(12),o=n(50),a=n(29),s=n(30);function l(e){s.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let c=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends i.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,r){do{if(t||(t=a.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(r)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))r("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{r("error",e)}),e.on("connect",()=>{r("connect")}),e.on("end",()=>{let t=e.isInprocess();r("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===a.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let i=new o.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=i,n||(this.client=i),i.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(i,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(i,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(i,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(i,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===i&&this.emit("stat_call_function_back",i.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let r=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,r));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),i.on("error",e=>{r&&r("error",e),this.emit("socket-error",e,i.getContext(),n,i.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),i.isInprocess()?(this.connectedMap[t.toLowerCase()]=i,r&&r("connect"),this.emit("connect",i.getContext(),n,!0)):i.on("connect",()=>{this.connectedMap[t.toLowerCase()]=i,r&&r("connect"),this.emit("connect",i.getContext(),n,!1)}),i.on("end",()=>{let e=i.isInprocess();s.information("server is ended, and this client emit end",t,n,e),r&&r("end",i.getContext(),n,e),this.emit("end",i.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(i)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return r(this,void 0,void 0,function*(){let r=null,i=yield this.crossCallServerFunctionEx(e,t,...n);return i&&(r=i[0]),r})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(r,t,...n)}}crossIsRemoteClientExist(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(r,t,n)}})}crossCallRemoteClientFunction(e,t,n,...r){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(i,t,n,...r)}}crossAttachServerEvent(e,t,n){let r=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");r=this.internalAttachServerEvent(i,t,n)}return r}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(r,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(r,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.generateId();const i={alias:t,action:"register",rid:r};let o=(e,r)=>{e?(s.error("register error",e.message),n(r)):n(t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(s.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===a.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,r)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let r=this.generateId();const i={action:"check_server_function_exist",method:t,rid:r};let o=(e,t)=>{n(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((r,i)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let i=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const o={rid:i,method:t,args:n};let a=(t,n)=>{t?(s.error("callServerFunction error",t,e.getContext()),r([null,t])):r([n,void 0])};this.retCallbackMap[i]=Object.assign({callback:a},o),this.sendAdapter(e,o)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,r)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let r=this.generateId();const i={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:r};let o=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[r]=Object.assign({callback:o},i),this.sendAdapter(e,i)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((r,i)=>{do{if(!e){r(!1);break}if(!t){r(!1);break}if(!n){r(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){r(!(!this.apis||!this.apis[n]));break}let i=this.generateId();const o={action:"check_client_function_exist",method:n,rid:i,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let a=(e,t)=>{r(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:a},o),this.sendAdapter(e,o)}while(0)})}internalCallRemoteClientFunction(e,t,n,...r){return new Promise((i,o)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"remote client alias is not specifed"]);break}if(!n){i([null,"funcName is not specifed"]);break}let o=(e,t)=>{e?(s.information("callRemoteClientFunction",e.message),i([null,e])):i([t,void 0])};if(r)for(let e=0;e<r.length;e++)r[e]=this.convertFunction2IdEx(r[e]);let a=this.generateId();const l={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:r,rid:a};this.retCallbackMap[a]=Object.assign({callback:o},l),this.sendAdapter(e,l)}while(0)})}internalAttachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(r)||(this.eventCallbackMaps[r]={}),this.eventCallbackMaps[r].hasOwnProperty(t)||(this.eventCallbackMaps[r][t]={}),a.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let i=this.generateId();return this.eventCallbackMaps[r][t][i]=n,i}internalDetachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;delete this.eventCallbackMaps[r][t][n],a.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;let e=this.eventCallbackMaps[r][t];for(let t in e){let r=e[t];r&&r.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...r){let i=void 0,o=!1;do{const a=this.contextCallbackMap[t];if(!a){s.error("the context function has been freeer",t),i={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let l=void 0,c=void 0;try{l=a.apply(null,...r)}catch(e){c=e.message;break}if(void 0===n||null===n)break;if(i={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==c){i.error=c;break}if(l&&l.then){l.then(t=>{i.data=this.convertFunction2IdEx(t),i.success=!0,this.sendAdapter(e,i)}).catch(t=>{i.error=t instanceof Error?t.message:t,this.sendAdapter(e,i)}),o=!0;break}i.success=!0,i.data=this.convertFunction2IdEx(l)}while(0);!o&&i&&this.sendAdapter(e,i)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":r}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let r=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(r){n=((...e)=>{this.callRemoteContextById(t,r,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:r},l);break}for(let n in e){let r=e[n];e[n]=this.decodeParameter(r,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,r=!1;do{if(!t)break;let i=t.method;if(!i)break;let o=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(o):[].concat(o)},!this.apis||!this.apis[i]){n.error=`callRegisterFunction ${i} is undefined`;break}let a=void 0,s=this.decodeParameter(t.args,t.src);try{a=this.apis[i].apply(null,[t.src].concat(s))}catch(e){n.error=e.message;break}if(a&&a.then){a.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),r=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(a)}while(0);s.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(e,n)}}),c=global.__xdasIPCClienInstance,t.client=c},function(e,t,n){e.exports=n(9)(213)},function(e,t){e.exports=require("util")},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(1).default.getLogger("async-remote-call"),o=n(42),a=n(12),s=n(6);t.asyncRemoteCall=new class extends a.EventEmitter{constructor(){super(),this.mapObj=new Map,this.mapObjIniting=new Map,"renderer"!==process.type&&i.warning('can not import "renderer-process-call" module in non-renderer process',process.type)}getAppName(){return r(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}getAppVersion(){return r(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return r(this,void 0,void 0,function*(){return o.global.process.__resolve()})}getIpcMain(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getBrowserWindow(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getWebContents(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("webContents")})}getGlobalShortcut(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield o.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emit("OnInitCurrentWebContents",e),this.listeners("OnInitCurrentWebContents").forEach(e=>{this.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield o.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emit("OnInitCurrentWindow",e),this.listeners("OnInitCurrentWindow").forEach(e=>{this.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return r(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return s.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>r(this,void 0,void 0,function*(){this.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield o.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emit(e,t),this.listeners(e).forEach(t=>{this.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},function(e,t){e.exports=vendor_0aff229d1d3a2d2be355},function(e,t,n){"use strict";var r=n(66),i=n(116),o=Object.prototype.toString;function a(e){return"[object Array]"===o.call(e)}function s(e){return null!==e&&"object"==typeof e}function l(e){return"[object Function]"===o.call(e)}function c(e,t){if(null!==e&&void 0!==e)if("object"!=typeof e&&(e=[e]),a(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:a,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:i,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:l,isStream:function(e){return s(e)&&l(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:c,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,i=arguments.length;r<i;r++)c(arguments[r],n);return t},extend:function(e,t,n){return c(t,function(t,i){e[i]=n&&"function"==typeof t?r(t,n):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(8),a=n(1).default.getLogger("XLStat");let s=o.default(i.join(__rootDir,"../bin/ThunderHelper.node"));function l(e=""){let t;if("string"==typeof e)t=e;else if(c(e)&&"object"==typeof e){let n=[];for(let t in e)c(e[t])&&n.push(t+"="+encodeURIComponent(e[t]));t=n.join(",")}return t}function c(e){return void 0!==e&&null!==e}!function(e){let t=null;function n(){return t||(t=s.xlstat4),t}function i(e,t="",i="",o=0,s=0,c=0,u=0,d="",f=0){return r(this,void 0,void 0,function*(){let r=0;do{if(void 0===e){r=1;break}let h=l(d);r="browser"===process.type?yield new Promise(a=>{r=n().asyncTrackEvent(e,t,i,o,s,c,u,h,f,e=>{a(e)})}):n().trackEvent(e,t,i,o,s,c,u,h,f),a.information(e,t,i,o,s,c,u,h,f)}while(0);return r})}function o(e,t=0){do{if(void 0===e)break;"browser"!==process.type&&n().trackClick(e,t)}while(0)}e.asyncTrackEvent=i,e.trackEvent=function(e,t="",n="",r=0,o=0,a=0,s=0,l="",c=0){i(e,t,n,r,o,a,s,l,c).catch()},e.trackEventEx=function(e,t="",n="",r=0){i(e,t,"",0,0,0,0,n,r).catch()},e.trackClick=o,e.trackShow=function(e,t=0){o(e,t)},e.setUserID=function(e=0,t=0){"browser"!==process.type&&n().setUserID(e,t)},e.initParam=function(e){return r(this,void 0,void 0,function*(){let t=-1;return t="browser"===process.type?yield new Promise(t=>{n().asyncInitParam(e,(e,n)=>{t(e?n:-1)})}):yield new Promise(t=>{n().initParamRemote(e,e=>{t(e)})})})},e.asyncUninit=function(e){return r(this,void 0,void 0,function*(){"browser"===process.type&&(yield new Promise(t=>{n().asyncUninit(e,()=>{t()})}))})},e.uninit=function(){"browser"===process.type&&n().waitFinish()}}(t.XLStatNS||(t.XLStatNS={}))},function(e,t){e.exports=require("events")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const r=n(2);let i,o=console;function a(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,i=(new Error).stack.split("\n").slice(e+1);i.shift();let o=[];return i.forEach((e,i)=>{let a=t.exec(e)||n.exec(e),s={};a&&5===a.length&&(s.method=a[1],s.path=a[2],s.line=a[3],s.pos=a[4],s.file=r.basename(s.path),o.push(s))}),o}if(i="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return a(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){o.time(...e)},t.timeEnd=function(...e){o.timeEnd(...e)},t.trace=function(...e){let t=a(),n="";t[0]&&t[0].method&&(n=n),o.trace(i,...e)},t.info=function(...e){let t=a(),n="anonymous";t[0]&&t[0].method&&(n=n),o.info(i,"["+n+"]",e.join(","))},t.warn=function(...e){let t=a(),n="";t[0]&&t[0].method&&(n=n),o.warn("<WARN>"+i,"["+n+"]",e.join(","))},t.error=function(...e){let t=a(),n="";t[0]&&t[0].method&&(n=n),o.error("<ERROR>"+i,"["+n+"]",e.join(","))},t.log=function(...e){o.log(i,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},function(e,t){e.exports=require("os")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n,r,i,o,a,s,l,c,u,d,f,h,p,g,m,v,_,y,w,b,C;!function(e){e[e.Unkown=0]="Unkown",e[e.Create=1]="Create",e[e.InvaldParam=2]="InvaldParam",e[e.InvaldLink=3]="InvaldLink",e[e.InvaldConfig=4]="InvaldConfig",e[e.Timeout=5]="Timeout",e[e.VerifyData=6]="VerifyData",e[e.Forbidden=7]="Forbidden",e[e.RangeDispatch=8]="RangeDispatch",e[e.FilePathOverRanging=9]="FilePathOverRanging",e[e.FileCreate=201]="FileCreate",e[e.FileWrite=202]="FileWrite",e[e.FileRead=203]="FileRead",e[e.FileRename=204]="FileRename",e[e.FileFull=205]="FileFull",e[e.FileOccupied=211]="FileOccupied",e[e.FileAccessDenied=212]="FileAccessDenied",e[e.BtUploadExist=601]="BtUploadExist",e[e.ForbinddenResource=701]="ForbinddenResource",e[e.ForbinddenAccount=702]="ForbinddenAccount",e[e.ForbinddenArea=703]="ForbinddenArea",e[e.ForbinddenCopyright=704]="ForbinddenCopyright",e[e.ForbinddenReaction=705]="ForbinddenReaction",e[e.ForbinddenPorn=706]="ForbinddenPorn",e[e.DownloadSDKCrash=10001]="DownloadSDKCrash",e[e.torrentFileNotExist=10002]="torrentFileNotExist"}(t=e.TaskError||(e.TaskError={})),function(e){e[e.Unkown=-1]="Unkown",e[e.Success=0]="Success",e[e.QueryFailed=1]="QueryFailed",e[e.ServerError=2]="ServerError",e[e.ResourceNotFound=3]="ResourceNotFound",e[e.AuthorizingFailed=4]="AuthorizingFailed",e[e.ForbidByCopyright=5]="ForbidByCopyright",e[e.ForbidByPorNoGraphy=6]="ForbidByPorNoGraphy",e[e.ForbidByReactionary=7]="ForbidByReactionary",e[e.ForbidByOtherFilter=8]="ForbidByOtherFilter"}(n=e.DcdnStatusCode||(e.DcdnStatusCode={})),function(e){e[e.Begin=-1]="Begin",e[e.Unkown=0]="Unkown",e[e.StandBy=1]="StandBy",e[e.PreDownloading=2]="PreDownloading",e[e.StartWaiting=3]="StartWaiting",e[e.StartPending=4]="StartPending",e[e.Started=5]="Started",e[e.StopPending=6]="StopPending",e[e.Stopped=7]="Stopped",e[e.Succeeded=8]="Succeeded",e[e.Failed=9]="Failed",e[e.Seeding=10]="Seeding",e[e.DestroyPending=11]="DestroyPending",e[e.End=12]="End"}(r=e.TaskStatus||(e.TaskStatus={})),function(e){e[e.Begin=-1]="Begin",e[e.StandBy=0]="StandBy",e[e.Stopped=1]="Stopped",e[e.Started=2]="Started",e[e.Complete=3]="Complete",e[e.Forbidden=4]="Forbidden",e[e.Error=5]="Error",e[e.End=6]="End"}(i=e.BtFileStatus||(e.BtFileStatus={})),function(e){e[e.DispatchStrategyNone=0]="DispatchStrategyNone",e[e.DispatchStrategyOrigin=1]="DispatchStrategyOrigin",e[e.DispatchStrategyP2s=2]="DispatchStrategyP2s",e[e.DispatchStrategyP2p=4]="DispatchStrategyP2p",e[e.DispatchStrategyAll=-1]="DispatchStrategyAll"}(o=e.DispatchStrategy||(e.DispatchStrategy={})),function(e){e[e.Unkown=0]="Unkown",e[e.P2sp=1]="P2sp",e[e.Bt=2]="Bt",e[e.Emule=3]="Emule",e[e.Group=4]="Group",e[e.Magnet=5]="Magnet"}(a=e.TaskType||(e.TaskType={})),function(e){e[e.Invalid=0]="Invalid",e[e.P2sp=1]="P2sp",e[e.Emule=2]="Emule"}(s=e.TaskCfgType||(e.TaskCfgType={})),function(e){e.Unkown="Unkown",e.Downloading="Downloading",e.Completed="Completed",e.Recycle="Recycle"}(l=e.CategroyViewID||(e.CategroyViewID={})),function(e){e[e.Unknow=0]="Unknow",e[e.TaskCreated=1]="TaskCreated",e[e.InsertToCategoryView=2]="InsertToCategoryView",e[e.RemoveFromCategoryView=3]="RemoveFromCategoryView",e[e.StatusChanged=4]="StatusChanged",e[e.DetailChanged=5]="DetailChanged",e[e.ChannelInfoChanged=6]="ChannelInfoChanged",e[e.DcdnStatusChanged=7]="DcdnStatusChanged",e[e.TaskDescriptionChanged=8]="TaskDescriptionChanged",e[e.TaskUserRead=9]="TaskUserRead",e[e.TaskRenamed=10]="TaskRenamed",e[e.TaskMovedEnd=11]="TaskMovedEnd",e[e.TaskMovingStateChange=12]="TaskMovingStateChange",e[e.BtSubFileDetailChanged=13]="BtSubFileDetailChanged",e[e.BtSubFileLoaded=14]="BtSubFileLoaded",e[e.BtSubFileForbidden=15]="BtSubFileForbidden",e[e.BtSubFileDcdnStatusChanged=16]="BtSubFileDcdnStatusChanged",e[e.TaskCategoryMovedEnd=17]="TaskCategoryMovedEnd",e[e.GroupTaskSubFileDetailChanged=18]="GroupTaskSubFileDetailChanged",e[e.TaskDestroying=19]="TaskDestroying",e[e.TaskDestroyed=20]="TaskDestroyed"}(c=e.TaskEventType||(e.TaskEventType={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.VirtualId=2]="VirtualId",e[e.SrcTotal=3]="SrcTotal",e[e.SrcUsing=4]="SrcUsing",e[e.FileSize=5]="FileSize",e[e.ReceivedSize=6]="ReceivedSize",e[e.DownloadSize=7]="DownloadSize",e[e.TotalDownloadSize=8]="TotalDownloadSize",e[e.CreateTime=9]="CreateTime",e[e.CompletionTime=10]="CompletionTime",e[e.DownloadingPeriod=11]="DownloadingPeriod",e[e.Progress=12]="Progress",e[e.RecycleTime=13]="RecycleTime",e[e.FileCreated=14]="FileCreated",e[e.Forbidden=15]="Forbidden",e[e.CategoryId=16]="CategoryId",e[e.UserRead=17]="UserRead",e[e.OpenOnComplete=18]="OpenOnComplete",e[e.GroupTaskId=19]="GroupTaskId",e[e.DownloadSubTask=20]="DownloadSubTask",e[e.NameType=21]="NameType",e[e.OwnerProduct=22]="OwnerProduct",e[e.FileIndex=23]="FileIndex",e[e.NameFixed=24]="NameFixed",e[e.ValidDownloadSize=25]="ValidDownloadSize",e[e.RealDownloadSize=26]="RealDownloadSize",e[e.ResourceLegal=27]="ResourceLegal",e[e.TaskType=28]="TaskType",e[e.ErrorCode=29]="ErrorCode",e[e.PlayPosition=30]="PlayPosition",e[e.Duration=31]="Duration",e[e.NumberEnd=32]="NumberEnd",e[e.BooleanStart=4096]="BooleanStart",e[e.Destroyed=4097]="Destroyed",e[e.Background=4098]="Background",e[e.Moving=4099]="Moving",e[e.BooleanEnd=4100]="BooleanEnd",e[e.StringStart=8192]="StringStart",e[e.TaskName=8193]="TaskName",e[e.SavePath=8194]="SavePath",e[e.RelativePath=8195]="RelativePath",e[e.TaskUrl=8196]="TaskUrl",e[e.RefUrl=8197]="RefUrl",e[e.Cid=8198]="Cid",e[e.Gcid=8199]="Gcid",e[e.Cookie=8200]="Cookie",e[e.ProductInfo=8201]="ProductInfo",e[e.Origin=8202]="Origin",e[e.Description=8203]="Description",e[e.UserData=8204]="UserData",e[e.OriginName=8205]="OriginName",e[e.HTTPContentType=8206]="HTTPContentType",e[e.CategoryViewId=8207]="CategoryViewId",e[e.YunTaskId=8208]="YunTaskId",e[e.StringEnd=8209]="StringEnd",e[e.ObjectStart=12288]="ObjectStart",e[e.ObjectEnd=12289]="ObjectEnd"}(u=e.TaskAttribute||(e.TaskAttribute={})),function(e){e[e.UnKnown=0]="UnKnown",e[e.SrcTotal=1]="SrcTotal",e[e.SrcUsing=2]="SrcUsing",e[e.FileSize=4]="FileSize",e[e.ReceivedSize=8]="ReceivedSize",e[e.DownloadSize=16]="DownloadSize",e[e.CompletionTime=32]="CompletionTime",e[e.DownloadingPeriod=64]="DownloadingPeriod",e[e.Progress=128]="Progress",e[e.RecycleTime=256]="RecycleTime",e[e.FileCreated=512]="FileCreated",e[e.Forbidden=1024]="Forbidden",e[e.UserRead=2048]="UserRead",e[e.OpenOnComplete=4096]="OpenOnComplete",e[e.DownloadSubTask=8192]="DownloadSubTask",e[e.TaskName=16384]="TaskName",e[e.SavePath=32768]="SavePath",e[e.Cid=65536]="Cid",e[e.Gcid=131072]="Gcid",e[e.UserData=262144]="UserData",e[e.CategoryViewId=524288]="CategoryViewId",e[e.ErrorCode=1048576]="ErrorCode",e[e.TaskSpeed=2097152]="TaskSpeed",e[e.ChannelInfo=4194304]="ChannelInfo",e[e.ValidDownloadSize=8388608]="ValidDownloadSize",e[e.OriginName=16777216]="OriginName",e[e.HTTPContentType=33554432]="HTTPContentType",e[e.PlayPosition=67108864]="PlayPosition",e[e.Duration=134217728]="Duration",e[e.YunTaskId=268435456]="YunTaskId"}(d=e.TaskDetailChangedFlags||(e.TaskDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(f=e.BtSubFileDetailChangedFlags||(e.BtSubFileDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(h=e.GroupTaskSubFileDetailChangedFlags||(e.GroupTaskSubFileDetailChangedFlags={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.FileStatus=2]="FileStatus",e[e.DownloadSize=3]="DownloadSize",e[e.FileSize=4]="FileSize",e[e.EnableDcdn=5]="EnableDcdn",e[e.ErrorCode=6]="ErrorCode",e[e.DcdnStatus=7]="DcdnStatus",e[e.RealIndex=8]="RealIndex",e[e.FileOffset=9]="FileOffset",e[e.Visible=10]="Visible",e[e.Download=11]="Download",e[e.UserRead=12]="UserRead",e[e.PlayPosition=13]="PlayPosition",e[e.Duration=14]="Duration",e[e.NumberEnd=15]="NumberEnd",e[e.StringStart=4096]="StringStart",e[e.FinalName=4097]="FinalName",e[e.RelativePath=4098]="RelativePath",e[e.FileName=4099]="FileName",e[e.FilePath=4100]="FilePath",e[e.Cid=4101]="Cid",e[e.Gcid=4102]="Gcid",e[e.StringEnd=4103]="StringEnd"}(p=e.BtFileAttribute||(e.BtFileAttribute={})),function(e){e[e.P2spUrl=0]="P2spUrl",e[e.BtInfoId=1]="BtInfoId",e[e.EmuleFileHash=2]="EmuleFileHash",e[e.MagnetUrl=3]="MagnetUrl",e[e.GroupTaskName=4]="GroupTaskName"}(g=e.KeyType||(e.KeyType={})),function(e){e[e.NameInclude=1]="NameInclude",e[e.BtDisplayNameInclude=2]="BtDisplayNameInclude"}(m=e.SearchKeyType||(e.SearchKeyType={})),function(e){e[e.ExtEqual=1]="ExtEqual",e[e.NameLikeAndExtEqual=2]="NameLikeAndExtEqual",e[e.TaskTypeEqual=4]="TaskTypeEqual"}(v=e.FilterKeyType||(e.FilterKeyType={})),function(e){e[e.All=0]="All",e[e.CommonForeground=1]="CommonForeground",e[e.CommonBackground=2]="CommonBackground",e[e.Temporary=3]="Temporary",e[e.PreDownload=4]="PreDownload",e[e.PrivateForeground=5]="PrivateForeground"}(_=e.KeyFilter||(e.KeyFilter={})),function(e){e[e.Unknown=-1]="Unknown",e[e.LoadTaskBasic=0]="LoadTaskBasic",e[e.Create=1]="Create",e[e.Complete=2]="Complete",e[e.Recycle=3]="Recycle",e[e.Recover=4]="Recover",e[e.ReDownload=5]="ReDownload",e[e.MoveThoughCategory=6]="MoveThoughCategory"}(y=e.TaskInsertReason||(e.TaskInsertReason={})),function(e){e[e.Unknown=-1]="Unknown",e[e.Manual=0]="Manual",e[e.PauseAll=1]="PauseAll",e[e.DeleteTask=2]="DeleteTask",e[e.TaskJammed=3]="TaskJammed",e[e.LowSpeed=4]="LowSpeed",e[e.MaxDownloadReduce=5]="MaxDownloadReduce",e[e.MoveTask=6]="MoveTask",e[e.SelectDownloadLists=7]="SelectDownloadLists",e[e.PrivateLoginOut=8]="PrivateLoginOut",e[e.FreeDownload=9]="FreeDownload",e[e.Exit=10]="Exit"}(w=e.TaskStopReason||(e.TaskStopReason={})),function(e){e[e.RESOURCE_FROM_MEMBER=1]="RESOURCE_FROM_MEMBER",e[e.RESOURCE_FROM_OFFLINE=2]="RESOURCE_FROM_OFFLINE",e[e.RESOURCE_FROM_CRYSTAL_LARGE=4]="RESOURCE_FROM_CRYSTAL_LARGE",e[e.RESOURCE_FROM_CRYSTAL_SMALL=8]="RESOURCE_FROM_CRYSTAL_SMALL",e[e.RESOURCE_FROM_DCDN=16]="RESOURCE_FROM_DCDN",e[e.RESOURCE_FROM_FREEDCDN=32]="RESOURCE_FROM_FREEDCDN"}(b=e.XLResourceFrom||(e.XLResourceFrom={})),function(e){e[e.XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD=0]="XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD",e[e.XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING=1]="XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING",e[e.XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING=2]="XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING"}(C=e.XLDownloadStrategy||(e.XLDownloadStrategy={}))}(t.DownloadKernel||(t.DownloadKernel={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(2),a=n(1),s=n(18),l=n(25),c=a.default.getLogger("Thunder.Util"),u="Thunder Network\\Thunder7.9\\";function d(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(){let e=l.ThunderHelper.getSystemTempPath(),t=l.ThunderHelper.getLogicalDriveStrings(),n=0;for(let r=0;r<t.length;r++){if(l.ThunderHelper.getDriveType(t[r])===l.ThunderHelper.DriverType.DRIVE_FIXED){let i=l.ThunderHelper.getDriveInfo(t[r]);n<i.freeBytes&&t[r]!==e&&(n=i.freeBytes,e=t[r])}}return e.substring(0,1)+":\\迅雷下载"}function a(e){let t=(e.style.webkitTransform||getComputedStyle(e,"").getPropertyValue("-webkit-transform")||e.style.transform||getComputedStyle(e,"").getPropertyValue("transform")).match(/\-?[0-9]+\.?[0-9]*/g);return{x:parseInt(t&&(t[12]||t[4])||"0",10),y:parseInt(t&&(t[13]||t[5])||"0",10)}}function f(e){let t=!1;do{let n="",r="";if(/^[a-zA-Z]:\\/.test(e))n=e.slice(3);else{if(0!==e.indexOf("\\\\"))break;{let t=e.indexOf("\\",2);if(-1===t||t===e.length-1)break;if(""===(r=(n=e.slice(2)).substr(0,t-2)))break}}if(/[\*\"<>\?:\|]/i.test(n))break;if(e.length>256)break;if(""===r){t=!0;break}let i=r.indexOf(".ipv6-literal.net");-1!==i?(-1!==(i=(r=r.substr(0,i)).indexOf("%"))&&(r=r.substr(0,i)),r=r.replace(/\-/g,":"),/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/.test(r)&&(t=!0)):/(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))/.test(r)&&(t=!0)}while(0);return t}e.formatSize=function(e,t){0===t||(t=t||2);let n="0B";if("number"==typeof e&&e>0){let r=["B","KB","MB","GB","TB"],i=0,o=e;for(;o>=1e3&&!(i>=4);)o/=1024,i+=1;n=-1===String(o).indexOf(".")?o+r[i]:o.toFixed(t)+r[i]}return n},e.formatSizeCustom=function(e,t=2,n=5){let r="0B";if("number"==typeof e&&e>0){let i=["B","KB","MB","GB","TB"],o=0,a=e;for(;a>=1e3&&!(o>=4);)a/=1024,o+=1;if(-1===String(a).indexOf("."))r=a+i[o];else{let e=a.toFixed(t);e.length<=n?r="KB"!==i[o]&&"MB"!==i[o]||1===t?e+i[o]:a.toFixed(1)+i[o]:("."===(e=e.substr(0,n))[n-1]&&(e=e.substr(0,n-1)),r=e+i[o])}}return r},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.getDefaultDownloadDir=t,e.getMaxFreeDriver=function(){return t().substring(0,1)},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){c.warning(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",r=!1,i=[],o=e.length;for(let a=0;a<o;a++){let s=e[a];if('"'!==s&&"'"!==s||(""===n?(r=!0,n=s):n===s&&(r=!1,n=""))," "!==s||r||a===o-1){if(a===o-1){let n=e.substring(t);""!==n.trim()&&i.push(d(n))}}else{let n=e.substring(t,a);""!==n.trim()&&i.push(d(n)),t=a+1}}return i},e.getThunderTempPath=function(e,t){return r(this,void 0,void 0,function*(){const r=yield Promise.resolve().then(()=>n(14));let i=o.join(r.tmpdir(),u);return t&&(i=o.join(i,t)),void 0!==e&&e&&(yield s.FileSystemAWNS.mkdirsAW(i)),i})},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,r)=>{e+=0===r?"?":"&",e+=`${n}=${t[n]}`}),e},e.setQueryStringEx=function(e,t){for(let n in t)e+=-1===e.indexOf("?")?"?":"&",e+=`${n}=${t[n]}`;return e},e.getQueryString=function(e,t){return e&&t&&e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:""},e.isClipboardTextFormatAvailable=function(){let e=!1,t=i.clipboard.availableFormats();for(let n of t)if("text/plain"===n){e=!0;break}return e},e.keywordsHighLight=function(e,t,n){if(!e)return"";if(!t)return e;if(0===e.length)return e;if(0===t.length)return e;let r=/\\/,i=t.split(" ");if(0===(i=i.filter(e=>e.trim().length>0)).length)return e;for(let t=0;t<i.length;t++)if(i[t].search(r)>0)return e;n=void 0===n||null===n?"#FF0000":n;let o="",a=["\\[","\\^","\\*","\\(","\\)","\\|","\\?","\\$","\\.","\\+"],s="",l="|";for(let e=0;e<i.length;e++){for(let t=0;t<a.length;t++){let n=new RegExp(a[t],"g");i[e]=i[e].replace(n,a[t])}e===i.length-1&&(l=""),s=s.concat(i[e],l)}let c=new RegExp(s,"gi");return o=e.replace(c,e=>'<span style= "color:'+n+'">'+e+"</span>")},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setStyle=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style[t]=n})},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),r=t.split("."),i=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(r[e])>0){i=1;break}if(Number(n[e])-Number(r[e])<0){i=-1;break}}return i},e.throttle=function(e,t){let n,r=0;return(...i)=>{const o=Date.now();clearTimeout(n),o-r>t?(e(...i),r=o):n=setTimeout(()=>{e(...i),r=o},t)}},e.debounce=function(e,t){let n=null;return(...r)=>{n&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}},e.getElementFixed=function(e){let t=e.offsetLeft,n=e.offsetTop,r=e.offsetParent;for(;null!==r;){let e=a(r);t+=r.offsetLeft+e.x,n+=r.offsetTop+e.y,r=r.offsetParent}return{x:t,y:n}},e.escapeHTML=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")},e.unescapeHTML=function(e){return e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")},e.isValidPath=f,e.isValidDownloadPath=function(e){return r(this,void 0,void 0,function*(){let t=!1;do{if(e.length<3)break;if("私人空间"===e){t=!0;break}if(l.ThunderHelper.getDriveType(e)===l.ThunderHelper.DriverType.DRIVE_REMOTE){t=!0;break}if(!f(e))break;if(!(yield s.FileSystemAWNS.dirExistsAW(e))&&!(yield s.FileSystemAWNS.mkdirsAW(e)))break;t=!0}while(0);return t})};let h=void 0;function p(e,t="normal 12px sans-serif"){h||(h=document.createElement("canvas"));let n=h.getContext("2d");return n.font=t,n.measureText(e).width}function g(e,t,n="normal 12px sans-serif",r=1){function i(e,t,r,o,a){let s=-1;do{if(e>t){s=t;break}let l=Math.round((e+t)/2),c=p(`${r.substr(0,l)}...${o}`,n);if(a===c){s=l;break}if(a>c){if(Math.round(a)===Math.round(c)){s=l;break}s=i(l+1,t,r,o,a)}else if(c>a){if(Math.round(a)===Math.round(c)){s=l-1;break}s=i(e,l-1,r,o,a)}}while(0);return s}let a=e;do{if(!t)break;if(!e)break;let s=t.offsetWidth*r;if(s>p(e,n))break;let l=o.extname(e);""!==l&&(l=l.substring(1));let c=e.substr(0,e.length-l.length-1);if(!c)break;let u=i(0,c.length,c,l,s);if(-1===u)break;a=`${c.substr(0,u-2*(r-1))}...${l}`}while(0);return a}e.getTextWidth=p,e.getOmitName=g,e.getOmitNameMultiLine=function(e,t,n){return g(e,t,"normal 12px microsoft yahei",2)},e.setTimeoutAw=function(e,t){return new Promise((n,r)=>{setTimeout(()=>{t&&t(),n()},e)})}}(t.ThunderUtil||(t.ThunderUtil={}))},function(e,t,n){"use strict";var r=n(10),i=n(67),o=n(69),a=n(44),s=n(40),l=n(70).http,c=n(70).https,u=n(34),d=n(48),f=n(127),h=n(46),p=n(68);e.exports=function(e){return new Promise(function(t,n){var g,m=e.data,v=e.headers;if(v["User-Agent"]||v["user-agent"]||(v["User-Agent"]="axios/"+f.version),m&&!r.isStream(m)){if(Buffer.isBuffer(m));else if(r.isArrayBuffer(m))m=new Buffer(new Uint8Array(m));else{if(!r.isString(m))return n(h("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",e));m=new Buffer(m,"utf-8")}v["Content-Length"]=m.length}var _=void 0;e.auth&&(_=(e.auth.username||"")+":"+(e.auth.password||""));var y=u.parse(e.url),w=y.protocol||"http:";if(!_&&y.auth){var b=y.auth.split(":");_=(b[0]||"")+":"+(b[1]||"")}_&&delete v.Authorization;var C="https:"===w,E=C?e.httpsAgent:e.httpAgent,T={path:o(y.path,e.params,e.paramsSerializer).replace(/^\?/,""),method:e.method,headers:v,agent:E,auth:_};e.socketPath?T.socketPath=e.socketPath:(T.hostname=y.hostname,T.port=y.port);var k,S=e.proxy;if(!S&&!1!==S){var R=w.slice(0,-1)+"_proxy",x=process.env[R]||process.env[R.toUpperCase()];if(x){var P=u.parse(x);if(S={host:P.hostname,port:P.port},P.auth){var N=P.auth.split(":");S.auth={username:N[0],password:N[1]}}}}if(S&&(T.hostname=S.host,T.host=S.host,T.headers.host=y.hostname+(y.port?":"+y.port:""),T.port=S.port,T.path=w+"//"+y.hostname+(y.port?":"+y.port:"")+T.path,S.auth)){var I=new Buffer(S.auth.username+":"+S.auth.password,"utf8").toString("base64");T.headers["Proxy-Authorization"]="Basic "+I}e.transport?k=e.transport:0===e.maxRedirects?k=C?s:a:(e.maxRedirects&&(T.maxRedirects=e.maxRedirects),k=C?c:l),e.maxContentLength&&e.maxContentLength>-1&&(T.maxBodyLength=e.maxContentLength);var D=k.request(T,function(r){if(!D.aborted){clearTimeout(g),g=null;var o=r;switch(r.headers["content-encoding"]){case"gzip":case"compress":case"deflate":o=o.pipe(d.createUnzip()),delete r.headers["content-encoding"]}var a=r.req||D,s={status:r.statusCode,statusText:r.statusMessage,headers:r.headers,config:e,request:a};if("stream"===e.responseType)s.data=o,i(t,n,s);else{var l=[];o.on("data",function(t){l.push(t),e.maxContentLength>-1&&Buffer.concat(l).length>e.maxContentLength&&n(h("maxContentLength size of "+e.maxContentLength+" exceeded",e,null,a))}),o.on("error",function(t){D.aborted||n(p(t,e,null,a))}),o.on("end",function(){var r=Buffer.concat(l);"arraybuffer"!==e.responseType&&(r=r.toString("utf8")),s.data=r,i(t,n,s)})}}});D.on("error",function(t){D.aborted||n(p(t,e,null,D))}),e.timeout&&!g&&(g=setTimeout(function(){D.abort(),n(h("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",D))},e.timeout)),e.cancelToken&&e.cancelToken.promise.then(function(e){D.aborted||(D.abort(),n(e))}),r.isStream(m)?m.pipe(D):D.end(m)})}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(21),o=n(2),a=n(6),s=n(60),l=a.promisify,c=n(1).default.getLogger("Thunder.base.fs-utilities");!function(e){function t(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.access);try{yield n(e),t=!0}catch(e){c.information(e)}}return t})}function a(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.mkdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function u(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.rmdir);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function d(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(i.unlink);try{yield n(e),t=!0}catch(e){c.warning(e)}}return t})}function f(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.readdir);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function h(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.lstat);try{t=yield n(e)}catch(e){c.warning(e)}}return t})}function p(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let r=o.join(e,t),i=yield h(r);n=null!==i&&i.isDirectory()?yield g(r):yield d(r)}return n})}function g(e){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e){if(yield t(e)){n=!0;let t=(yield f(e))||[];for(let r=0;r<t.length;r++)n=(yield p(e,t[r]))&&n;(n||0===t.length)&&(n=(yield u(e))&&n)}}return n})}function m(e){return r(this,void 0,void 0,function*(){let n=!1;return c.information("mkdirsAW",e),void 0!==e&&((yield t(e))?n=!0:o.dirname(e)===e?n=!1:(yield m(o.dirname(e)))&&(n=yield a(e))),n})}function v(e,n){return r(this,void 0,void 0,function*(){let r;if(e.toLowerCase()!==n.toLowerCase()&&(yield t(e))){let t=i.createReadStream(e),o=i.createWriteStream(n);r=new Promise(e=>{t.pipe(o).on("finish",()=>{e(!0)})})}else r=new Promise(e=>{e(!1)});return r})}e.readFileAW=function(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(i.readFile);try{t=yield n(e)}catch(e){c.warning(e)}}return t})},e.readLineAw=function(e){return r(this,void 0,void 0,function*(){let n=null;do{if(!e)break;if(!t(e))break;n=yield new Promise(t=>{let n=[];const r=i.createReadStream(e),o=s.createInterface({input:r});o.on("line",e=>{n.push(e)}),o.on("close",()=>{t(n)})})}while(0);return n})},e.writeFileAW=function(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const r=l(i.writeFile);try{yield r(e,t),n=!0}catch(e){c.warning(e)}}return n})},e.existsAW=t,e.dirExistsAW=function(e){return r(this,void 0,void 0,function*(){let n=!1;do{if(!(n=yield t(e)))break;let r=yield h(e);if(!r)break;n=r.isDirectory()}while(0);return n})},e.mkdirAW=a,e.rmdirAW=u,e.unlinkAW=d,e.readdirAW=f,e.lstatAW=h,e.rmdirsAW=g,e.mkdirsAW=m,e.renameAW=function(e,t){return r(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=l(i.rename);try{yield n(e,t)}catch(e){c.warning(e)}}})},e.copyFileAW=v,e.copyDirsAW=function e(n,i){return r(this,void 0,void 0,function*(){let r=!1,a=yield h(n);if(a.isDirectory()){r=yield m(i);let s=(yield f(n))||[];for(let l=0;l<s.length;l++){let c=o.join(n,s[l]),u=o.join(i,s[l]);(r=yield t(c))&&(r=(a=yield h(c)).isDirectory()?yield e(c,u):yield v(c,u))}}return r})},e.mkdtempAW=function(){return r(this,void 0,void 0,function*(){let e=!1;const t=l(i.mkdtemp),r=(yield Promise.resolve().then(()=>n(14))).tmpdir();try{e=yield t(`${r}${o.sep}`)}catch(e){c.warning(e)}return e})},e.deleteEmptySubDirs=function(e,n){return r(this,void 0,void 0,function*(){let r=!0;e=o.normalize(e),n=o.normalize(n),e.length>3&&"\\"===e[e.length-1]&&(e=e.slice(0,e.length-1)),n.length>3&&"\\"===n[n.length-1]&&(n=n.slice(0,n.length-1));do{if(0!==e.indexOf(n)){r=!1;break}let i=e;for(;i!==n;){if((yield t(i))&&!(yield u(i))){r=!1;break}i=o.dirname(i)}}while(0);return r})},e.getFileSize=function e(n){return r(this,void 0,void 0,function*(){let r=0;do{if(!n)break;if(!(yield t(n)))break;let i=yield h(n);if(i)if(i.isDirectory()){let t=yield f(n);for(let i=0;i<t.length;i++)r+=(yield e(o.join(n,t[i])))}else r=i.size}while(0);return r})},e.isDirectoryEmptyAW=function(e,n=!0){return r(this,void 0,void 0,function*(){let r=!0;do{if(!e){r=!1;break}if(!(yield t(e))){r=n;break}let i=yield h(e);if(!i){r=!1;break}if(!i.isDirectory()){r=!1;break}if((yield f(e)).length>0){r=!1;break}}while(0);return r})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},function(e,t,n){e.exports=n(115)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.channelRMNewTaskReadyForSetTaskData="RM_NEWTASK_READYRORSETTASKDATA",e.channelRMNewTaskSetTaskData="RM_NEWTASK_SETTASKDATA",e.channelRMPreNewTaskSetTaskData="RM_PRENEWTASK_SETTASKDATA",e.channelRMNewTaskCreateNewTask="RM_NEWTASK_CREATENEWTASK",e.channelRMNewTaskClose="RM_NEWTASK_CLOSE",e.channelRMPreNewTaskClose="RM_PRENEWTASK_CLOSE",e.channelRMNewTaskSetBTInfo="RM_NEWTASK_SETBTINFO",e.channelRMNewTaskDownloadTorrent="RM_NEW_TASK_DOWNLOAD_TORRENT",e.channelRMNewTaskCreateBtTask="RM_NEWTASK_CRATEBTTASK",e.channelRMNewTaskCancleMagnet="RM_NEWTASK_CANCLE_MAGNET",e.channelRMImportTorrent="RM_NEWTASK_IMPORT_TORRENT",e.channelRMGetConfigValueResolve="RM_GET_CONFIG_VALUE_RESOLVE",e.channelRMGetConfigValueReject="RM_GET_CONFIG_VALUE_REJECT",e.channelRMSetConfigValueReject="RM_SET_CONFIG_VALUE_REJECT",e.channelMRTrayMenuClick="MR_TRAY_MENU_CLICK",e.channelMRNewTaskMagnetTaskCreated="MR_NEW_TASK_MAGNET_TASK_CREATED",e.channelMRNewTaskDownloadTorrentResult="MR_NEW_TASK_DOWNLOAD_TORRENT_RESULT",e.channelMRNewTaskCreateNewTaskResult="MR_NEWTASK_CREATENEWTASK_RESULT",e.channelMRNewTaskCreateBtTaskResult="RM_NEWTASK_CRATEBTTASK_RESULT",e.channelMRGetConfigValue="MR_GET_CONFIG_VALUE",e.channelMRSetConfigValue="MR_SET_CONFIG_VALUE",e.channelRMCommitPlanTask="RM_PLANTASK_COMMIT",e.channelRMPerformePlanTask="RM_PLANTASK_PERFORME",e.channelRMProcessSend="RM_RENDER_PROCESS_INFO",e.channelRMGetPrivateSpaceInfo="RM_RENDER_GET_PRIVATE_SPACE_INFO",e.channelMRGetPrivateSpaceInfoResult="MR_RENDER_GET_PRIVATE_SPACE_INFO_RESULT",e.channelRMFileCopy="RM_FILE_COPY",e.channelRMFileMove="RM_FILE_MOVE",e.channelMRFileCopyResult="MR_FILE_COPY_RESULT",e.channelMRFileMoveResult="MR_FILE_MOVE_RESULT",e.channelRMGetSutitleByCid="RM_RENDER_GET_SUBTITLE_BY_CID",e.channelMRGetSutitleByCidResult="MR_RENDER_GET_SUBTITLE_BY_CID_RESULT",e.channelRMGetSutitleByName="RM_RENDER_GET_SUBTITLE_BY_NAME",e.channelMRGetSutitleByNameResult="MR_RENDER_GET_SUBTITLE_BY_NAME_RESULT",e.channelRMDownloadSutitle="RM_RENDER_DOWNLOAD_SUBTITLE",e.channelMRDownloadSutitleSuc="MR_RENDER_DOWNLOAD_SUBTITLE_SUCCESS",e.channelMRDownloadSutitleFail="MR_RENDER_DOWNLOAD_SUBTITLE_FAIL",e.channelRMGetDisplayName="RM_RENDER_GET_SUBTITLE_DISPLAYNAME",e.channelMRGetDisplayNameResult="MR_RENDER_GET_SUBTITLE_DISPLAYNAME_RESULT",e.channelMRBringWindowToTop="MR_RENDER_BRING_WINDOW_TO_TOP",e.channelRMDownloadXmp="RM_RENDER_DOWNLOAD_XMP",e.channelRMXmpFixBoxCreated="RM_RENDER_XMPFIXBOX_CREATED",e.channelMRFixXmpSuc="MR_RENDER_FIX_XMP_SUC",e.channelMRFixXMPFail="MR_RENDER_FIX_XMP_FAIL",e.channelRMDownloadXmpEx="RM_RENDER_DOWNLOAD_XMP_EX",e.channelRMSetPosition="RM_SET_POSITION",e.channelRMSetFoucs="RM_SET_FOCUS",e.channelRMCreateAddressDropWnd="RM_CREATE_ADDRESS_DROPWND",e.channelRMRefreshAddressDropWnd="RM_REFRESH_ADDRESS_DROPWND",e.channelRMSelectAddressDropItem="RM_SELECT_ADDRESS_DROPITEM",e.channelRMCreateSearchWindow="RM_CREATE_SEARCH_WINDOW",e.channelRMShowSearchWindow="RM_SHOW_SEARCH_WINDOW",e.channelRMAddressKeyDown="RM_ADDRESS_BAR_KEYDOWN",e.channelMRFWAddressKeyDown="MR_ADDRESS_FW_BAR_KEYDOWN",e.channelMRFWSelectAddressDropItem="MR_FW_SELECT_ADDRESS_DROPITEM",e.channelRMAddressDropWndKeyDown="RM_ADDRESS_DROPWND_KEYDOWN",e.channelRMClickMouse="RM_CLICK_MOUSE",e.channelMRSearchBarFocusChange="MR_SEARCHBAR_FOCUS_CHANGE",e.channelMRFWAddressDropWndKeyDown="MR_FW_ADDRESS_DROPWND_KEYDOWN",e.channelMRClickAddressDropItem="MR_CLICK_ADDRESS_DROPITEM",e.channelMRMainWndMax="MR_MAINWINDOW_MAX",e.channelMRMainWndMin="MR_MAINWINDOW_MIN",e.channelMRMainWndRestore="MR_MAINWINDOW_RESTORE",e.channelRMGetBrowserStartType="RM_GET_BROWSER_START_TYPE",e.channelMRGetBrowserStartTypeResult="MR_GET_BROWSER_START_TYPE_RESULT",e.channelRMExecute="RM_SHELL_EXECUTE",e.channelMRExecuteResult="MR_SHELL_EXECUTE_RESULT",e.channelMRAdTipsClick="MR_AD_TIPS_CLICK",e.channelMRNotificationMsg="MR_NOTIFICATION_MSG",e.channelRMSetProgressBar="RM_SET_PROGRESS_BAR",e.channelRMRoundWindow="RM_ROUND_WINDOW",e.channelMRShowOrHideWindow="MR_SHOW_OR_HIDE_WINDOW",e.channelMRSuspensionWindowShowOrHide="MR_SUSPENSION_WINDOW_SHOW_OR_HIDE",e.channelRMConfigInitFinished="RM_CONFIG_INIT_FINISHED",e.channelRMConfigValueChanged="RM_CONFIG_VALUE_CHANGED",e.channelRMIndividuationBrowserMsg="RM_INDIVIDUATION_BROWSER_MSG",e.channelMRIndividuationBrowserMsg="MR_INDIVIDUATION_BROWSER_MSG",e.channelRMSetEnvironmentVariable="RM_SET_ENVIRONMENT_VARIABLE",e.channelMREmbedPlayerPos="MR_EMBED_PLAYER_POSITION",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},function(e,t){e.exports=require("fs")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=function(...e){},t.error=function(...e){},t.warning=function(...e){},t.critical=function(...e){},t.verbose=function(...e){},"development"===process.env.LOGGER_ENV&&(t.information=function(...e){console.log("information",e)},t.error=function(...e){console.log("error",e)},t.warning=function(...e){console.log("warning",e)},t.critical=function(...e){console.log("critical",e)},t.verbose=function(...e){console.log("verbose",e)})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.msgIPCCommunicatorForward="ipc_communicator_forward",e.msgIPCSendToMain="ipc_send_to_main",e.msgIPCSendToRenderer="ipc_send_to_renderer",e.msgIPCRendererConnect="ipc_renderer_connect",e.msgIPCRendererDisconnect="ipc_renderer_disconnect",e.msgNCCallNativeFunction="nc_call_native_function",e.msgNCCheckNativeFunction="nc_check_native_function",e.msgNCCallJsFunctionById="nc_call_js_function_by_id",e.msgNCCallJsFunctionByName="nc_call_js_function_by_name",e.msgNCNativeFireEvent="nc_native_fire_event",e.msgNCNativeCallReady="nc_native_call_ready"}(t.CommonIPCMessage||(t.CommonIPCMessage={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t;!function(e){e[e.OK=0]="OK",e[e.Cancel=1]="Cancel",e[e.Close=2]="Close",e[e.Unknow=3]="Unknow"}(t=e.Action||(e.Action={}))}(t.MessageBoxNS||(t.MessageBoxNS={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(33),a=n(14),s=n(15),l=n(8).default(i.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){let t,n,i,c;function u(e){let t=e;return/^[a-zA-Z]:\\/.test(e)?t=e.slice(0,3):e&&"\\"!==e[e.length-1]&&(t=e+"\\"),t}!function(e){e[e.DRIVE_UNKNOWN=0]="DRIVE_UNKNOWN",e[e.DRIVE_NO_ROOT_DIR=1]="DRIVE_NO_ROOT_DIR",e[e.DRIVE_REMOVABLE=2]="DRIVE_REMOVABLE",e[e.DRIVE_FIXED=3]="DRIVE_FIXED",e[e.DRIVE_REMOTE=4]="DRIVE_REMOTE",e[e.DRIVE_CDROM=5]="DRIVE_CDROM",e[e.DRIVE_RAMDISK=6]="DRIVE_RAMDISK"}(t=e.DriverType||(e.DriverType={})),function(e){e[e.Unspecified=0]="Unspecified",e[e.HDD=3]="HDD",e[e.SSD=4]="SSD",e[e.SCM=5]="SCM"}(n=e.MediaType||(e.MediaType={})),function(e){e.HKEY_CLASSES_ROOT="HKEY_CLASSES_ROOT",e.HKEY_CURRENT_USER="HKEY_CURRENT_USER",e.HKEY_LOCAL_MACHINE="HKEY_LOCAL_MACHINE",e.HKEY_USERS="HKEY_USERS"}(i=e.RegistryHKey||(e.RegistryHKey={})),function(e){e[e.REG_NONE=0]="REG_NONE",e[e.REG_SZ=1]="REG_SZ",e[e.REG_EXPAND_SZ=2]="REG_EXPAND_SZ",e[e.REG_BINARY=3]="REG_BINARY",e[e.REG_DWORD=4]="REG_DWORD",e[e.REG_DWORD_LITTLE_ENDIAN=4]="REG_DWORD_LITTLE_ENDIAN",e[e.REG_DWORD_BIG_ENDIAN=5]="REG_DWORD_BIG_ENDIAN",e[e.REG_LINK=6]="REG_LINK",e[e.REG_MULTI_SZ=7]="REG_MULTI_SZ",e[e.REG_RESOURCE_LIST=8]="REG_RESOURCE_LIST",e[e.REG_FULL_RESOURCE_DESCRIPTOR=9]="REG_FULL_RESOURCE_DESCRIPTOR",e[e.REG_RESOURCE_REQUIREMENTS_LIST=10]="REG_RESOURCE_REQUIREMENTS_LIST",e[e.REG_QWORD=11]="REG_QWORD",e[e.REG_QWORD_LITTLE_ENDIAN=11]="REG_QWORD_LITTLE_ENDIAN"}(c=e.RegistryDataType||(e.RegistryDataType={})),e.getDriveType=function(e){return e=u(e),l.getDriveType(e)},e.getDriveInfo=function(e){return e=u(e),l.getDriveInfo(e)},e.getFreePartitionSpace=function(e){return e=u(e),l.getFreePartitionSpace(e)},e.getLogicalDriveStrings=function(){return l.getLogicalDriveStrings()},e.getPartitionSpace=function(e){return e=u(e),l.getPartitionSpace(e)},e.getSystemTempPath=function(){return l.getSystemTempPath()},e.getTaskTypeFromUrl=function(e){let t=l.getTaskTypeFromUrl(e);if(t===s.DownloadKernel.TaskType.Unkown&&function(e){e=e.toLowerCase();let t=!1;do{if("http://"===e.substr(0,"http://".length)){t=!0;break}if("https://"===e.substr(0,"https://".length)){t=!0;break}if("ftp://"===e.substr(0,"ftp://".length)){t=!0;break}}while(0);return t}(e)){let n=/:\/\/\[(.+?)\].*/.exec(e);n||(n=/.+?:\/\/.*?\[(.+?)\].*/.exec(e)),n&&n[1]&&o.isIPv6(n[1])&&(t=s.DownloadKernel.TaskType.P2sp)}return t},e.extractIcon=function(e,t){return l.extractIcon(e,t)},e.isWindow7=function(){return 1===l.isWin7()},e.isWindow8OrLater=function(){let e=!1;do{let t=a.release();if(!t)break;let n=t.indexOf(".",0);if(n<0)break;let r=t.substring(0,n);if(!r)break;let i=parseInt(r,10);i&&i>=8&&(e=!0)}while(0);return e},e.isWindows10=function(){let e=!1;do{let t=a.release();if(!t)break;if(0===t.indexOf("10.0.")){e=!0;break}}while(0);return e},e.compareStr=function(e,t){return l.compareStr(e,t)},e.getTickCount=function(){return l.getTickCount()},e.setScreenSaveActive=function(e,t){return l.setScreenSaveActive(e,t)},e.isSparseDriver=function(e){return e=u(e),l.isSparseDriver(e)},e.getAppList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getAppList(t=>{e(t)})})})},e.isSSD=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.isSSD((t,n)=>{e(n)})})})},e.getMemoryInfo=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getMemoryInfo((t,n)=>{e({totalPhy:t,totalVir:n})})})})},e.getHardDiskSpaceList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getHardDiskSpaceList(t=>{e(t)})})})},e.getCpuList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{l.getCpuList(t=>{e(t)})})})},e.getFixedDriveMediaType=function(e){return r(this,void 0,void 0,function*(){return new Promise(t=>{e.length>1&&(e=e.slice(0,1)),l.getDriveMediaType(e.toUpperCase(),(e,n)=>{t(n)})})})},e.sleep=function(e){return r(this,void 0,void 0,function*(){yield new Promise((t,n)=>{setTimeout(t,e)})})},e.getTextScale=function(){let e=100,t=l.readRegString(i.HKEY_CURRENT_USER,"SOFTWARE\\Microsoft\\Accessibility","TextScaleFactor");return t&&(e=Number(t)),isNaN(e)&&(e=100),e},e.getWindowRect=function(e){return e?l.getWindowRect(e):{x:0,y:0,width:0,height:0}},e.moveWindow=function(e,t){e&&l.moveWindow(e,t.x,t.y,t.width,t.height,!0)},e.getSystemDirectory=function(){return l.getSystemDirectory()},e.getVersionBlockString=function(e,t){return l.getVersionBlockString(e,t)},e.getOwnerName=function(e){return l.getOwnerName(e)},e.createRegKey=function(e,t){return l.createRegKey(e,t)},e.deleteRegKey=function(e,t){return l.deleteRegKey(e,t)},e.readRegString=function(e,t,n){return l.readRegString(e,t,n)},e.queryRegValue=function(e,t,n){return l.queryRegValue(e,t,n)},e.writeRegValue=function(e,t,n,r,i){return l.writeRegValue(e,t,n,r,i)},e.deleteRegValue=function(e,t,n){return l.deleteRegValue(e,t,n)}}(t.ThunderHelper||(t.ThunderHelper={}))},,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),i=n(6),o=n(22),a=n(23);!function(e){e.mainProcessContext="main-process",e.mainRendererContext="main-renderer",e.mainPageWebviewRendererContext="main-page-webview-renderer",e.newTaskRendererContext="new-task-renderer",e.preNewTaskRendererContext="pre-new-task-renderer",e.loginRendererContext="login-renderer";class t{constructor(){this.isConnected=!1,this.isOnIPCEvent=!1,this.rendererInfos=[],this.listeners=new Map,t.intervalIPCModuleMsgs=[a.CommonIPCMessage.msgIPCRendererConnect,a.CommonIPCMessage.msgIPCRendererDisconnect]}onMessage(e,t){do{if(!i.isString(e)||0===e.length){o.error("msgName is null");break}if(i.isNullOrUndefined(t)){o.error("listener is null");break}this.listeners.has(e)?this.listeners.get(e).push(t):this.listeners.set(e,[t])}while(0)}getCommunicatorInfo(){return this.currInfo}getAllRenderer(){return this.rendererInfos}getCommunicatorInfoById(e){for(let t of this.rendererInfos)if(t.id===e)return t;return null}getCommunicatorInfoByContext(e){for(let t of this.rendererInfos)if(t.context===e)return t;return null}startListenIPCMessage(e){this.isOnIPCEvent||(this.isOnIPCEvent=!0,e&&this.ListenSendToMainMsg(),this.ListenSendToRendererMsg(e))}ListenSendToMainMsg(){r.ipcMain.on(a.CommonIPCMessage.msgIPCSendToMain,(e,t)=>{let n=void 0;do{if(i.isNullOrUndefined(t)){o.error("msgInfo is empty");break}if(!this.isConnected){o.warning("hasnot been connected yet");break}let r=t.msg.name;if(this.isIPCModuleIntervalMsg(r)){o.information(`IPC module interval msg : ${r}`);let i=this.handleIPCModuleIntervalMsg(e.sender,t);if(n=i[1],!i[0])break;o.information("need to dispatch msg:"+r)}i.isNullOrUndefined(n)?n=this.NotifyListener(t):this.NotifyListener(t)}while(0);i.isNullOrUndefined(n)||(e.returnValue=n),t=null})}ListenSendToRendererMsg(e){(e?r.ipcMain:r.ipcRenderer).on(a.CommonIPCMessage.msgIPCSendToRenderer,(t,n)=>{let r=void 0;do{if(i.isNullOrUndefined(n)){o.error("msgInfo is empty");break}if(!this.isConnected){o.warning("hasnot been connected yet");break}let a=n.msg.name;if(this.isIPCModuleIntervalMsg(a)){o.information(`IPC module interval msg : ${a}`);let e=this.handleIPCModuleIntervalMsg(t.sender,n);if(r=e[1],!e[0])break;o.information("need to dispatch msg:"+a)}e?(o.information("is main, handle forward msg"),this.handleForwardRendererToRendererMsg(n)):(o.information("is renderer, handle business msg"),i.isNullOrUndefined(r)?r=this.NotifyListener(n):this.NotifyListener(n))}while(0);i.isNullOrUndefined(r)||(t.returnValue=r),n=null})}isIPCModuleIntervalMsg(e){for(let n of t.intervalIPCModuleMsgs)if(e===n)return!0;return!1}handleIPCModuleIntervalMsg(e,t){let n=[!1,void 0];do{let r=t.msg.name;if(r===a.CommonIPCMessage.msgIPCRendererConnect){n=[!0,this.handleRendererConnectMsg(e,t)];break}if(r===a.CommonIPCMessage.msgIPCRendererDisconnect){n=[!0,this.handleRendererDisconnectMsg(e,t)];break}}while(0);return n}handleRendererConnectMsg(e,t){o.verbose(e),o.verbose(t)}handleRendererDisconnectMsg(e,t){o.verbose(e),o.verbose(t)}handleForwardRendererToRendererMsg(e){this.sendForwardRendererToRendererMsg(e)}sendForwardRendererToRendererMsg(e){o.verbose(e)}NotifyListener(e){let t=void 0,n=e.msg.name;if(this.listeners.has(n)){let r=this.listeners.get(n),i=!0;for(let n of r)i?(i=!1,t=n(e)):n(e)}return t}}e.Communicator=t}(t.CommonIPCBase||(t.CommonIPCBase={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(14),i=n(2);t.getDefaultPrex=function(){return i.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=i.basename(process.execPath,".exe"));let o=i.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(o="\\\\.\\pipe\\"+(o=(o=o.replace(/^\//,"")).replace(/\//g,"-"))),o},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},function(e,t,n){e.exports=n(9)(45)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(8).default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(){let e=!0;{0;let t=r.resolve("C:\\ETW_LOG\\log.ini");e="1"===i.readINI(t,"Log","enable")}return e}e.isDevToolsEnable=function(){return t()},e.isLogEnable=t,e.getLogOutput=function(){let e=process.env.TL_OUTPUT;do{if(e&&""!==e)break;let t=r.resolve("C:\\ETW_LOG\\log.ini");e=i.readINI(t,"Log","output")}while(0);return e}}(t.DevEnvHelperNS||(t.DevEnvHelperNS={}))},function(e,t){e.exports=require("net")},function(e,t){e.exports=require("url")},function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(r||(r={})),t.default=r},function(e,t,n){"use strict";var r;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(r||(r={})),e.exports=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(15);let o=["apk","pic","video","mp4","rmvb","wmv","mpg","mkv","mov","rm","avi","flv","doc","link","ppt","word","magnetic","music","pdf","rar","xls","txt","unknow","gif","ipa","ipsw","dll","chm","text","install","iso"];const a=".xv;.xlmv;.3gp;.3gp2;.3gpp;.3gpp2;.3mm;.3p2;.60d;.787;.aaf;.aep;.aepx;.aet;.aetx;.ajp;.ale;.amv;.amx;.arf;\n  .asf;.asx;.avb;.avd;.avi;.avp;.avs;.avs;.axm;.bdm;.bdmv;.bik;.bix;.bmk;.bnp;.box;.bs4;.bsf;.byu;.camproj;.camrec;.clpi;.cmmp;\n  .cmmtpl;.cmproj;.cmrec;.cpi;.cst;.cvc;.d2v;.d3v;.dat;.dav;.dce;.dck;.ddat;.dif;.dir;.divx;.dlx; .dmb;.dmsm;.dmsm3d;.dmss;.dnc;.dpg;\n  .dream;.dsy;.dv;.dv-avi;.dv4;.dvdmedia;.dvr-ms;.dvx;.dxr;.dzm;.dzp;.dzt;.edl;.evo;.eye;.f4p;.f4v;.fbr;.fbr;.fbz;.fcp;.flc;.flh;\n  .fli;.flv;.flx;.gfp;.gl;.grasp;.gts;.gvi;.gvp;.hdmov;.hkm;.ifo;.imovieproj;.imovieproject;.iva;.ivf;.ivr;.ivs;.izz;.izzy;.jts;.jtv;\n  .k3g;.lrec;.lsf;.lsx;.m15;.m1pg;.m1v;.m21;.m21;.m2a;.m2p;.m2t;.m2ts;.m2v;.m4e;.m4u;.m4v;.m75;.meta;.mgv;.mj2;.mjp;.mjpg;.mkv;.mmv;\n  .mnv;.mod;.modd;.moff;.moi;.moov;.mov;.movie;.mp21;.mp2v;.mp4;.mp4v;.mpe;.mpeg;.mpeg4;.mpf;.mpg;.mpg2;.mpgindex;.mpl;.mpls;\n  .mpsub;.mpv;.mpv2;.mqv;.msdvd;.msh;.mswmm;.mts;.mtv;.mvb;.mvc;.mvd;.mve;.mvp;.mvy;.mxf;.mys;.ncor;.nsv;.nuv;.nvc;.ogm;.ogv;.ogx;.osp;\n  .par;.pds;.pgi;.piv;.pjs;.pmf;.pns;.ppj;.prel;.pro;.prproj;.prtl;.psh;.pssd;.pva;.pvr;.pxv;.qt;.qtch;.qtl;.qtm;.qtz;\n  .r3d;.rcproject;.rdb;.rec;.rm;.rmd;.rmp;.rms;.rmvb;.roq;.rp;.rts;.rts;.rum;.rv;.sbk;.sbt;.scc;.scm;.scn;.screenflow;.sec;.seq;.sfd;\n  .sfvidcap;.smk;.sml;.smv;.spl;.ssm;.stl;.str;.stx;.svi;.swf;.swi;.swt;.tda3mt;.tivo;.tix;.tod;.tp;.tp0;.tpd;\n  .tpr;.trp;.ts;.tts;.tvs;.vc1;.vcpf;.vcr;.vcv;.vdo;.vdr;.veg;.vem;.vf;.vfw;.vfz;.vgz;.vid;.viewlet;.viv;.vivo;.vlab;.vob;.vp3;.vp6;.vp7;\n  .vro;.vs4;.vse;.vsp;.w32;.wcp;.webm;.wlmp;.wm;.wmd;.wmmp;.wmv;.wmx;.wp3;.wpl;.wtv;.wvx;.xfl;.xvid;.yuv;.zm1;.zm2;.zm3;.zmv;",s=".exe;.com;.bat;.msi;.apk;.ipa;.iso;.mds;.bin;.img;.ipsw;",l=".txt;.html;.htm;.shtml;.xhtml;.chm;.hlp;.inf;.rtf;.tex;.ltx;.doc;.docx;.wps;.ppt;.pptx;.odf;.pdf;.xls;.xlsx;.docm;.\n  dot;.dotm;.dotx;.email;.rp;.pps;.et;.ett;.xlt;.dbf;.prn;.csv;.mht;.mhtml;.xml;.wpt;.dps;.dpt;.pot;.ppsx;.epub;.mobi;.lit;.wdl;.ceb;.abm;\n  .pdg;.umb;.ibooks;",c=".aiff;.cue;.m3u;.au;.cdda;.raw;.wav;.flac;.tak;.mp3;.aac;.wma;.m4a;.mid;.mka;.mp2;.mpa;.mpc;.ape;.ofr;\n  .ogg;.ra;.wv;.tta;.ac3;.dts;.nsf;.mod;.s3m;.xm;.it;.vst;",u=".psd;.tga;.gif;.jpeg;.jpg;.jp2;.bmp;.ico;.pcx;.png;.pbm;.pgm;.ppm;.pnm;.pgf;.arw;.cr2;.crw;.dcr;.dng;.erf;.kdc;.mef;\n  .mos;.mrw;.nef;.nrw;.orf;.pef;.ptx;.r3d;.raf;.raw;.rw2;.srf;.srw;.x3f;.ras;.tiff;.tif;.wbmp;.ilbm;.lbm;.iff;.ico;",d=".zip;.zipx;.rar;.7z;.isz;.cab;.arj;.ace;.alz;.uue;.tar;.gz; .gzip;.tgz;.tpz;.bzip2;.bz2;.bz;.tbz;.tbz2;.xz;.txz;\n  .lzh;.lha;.zt;.az; .xpi;.jar;.wim;.swm;.rpm;.xar;.deb;.dmg;.hfs;.cpio;.lzma;.lzma86;.split;",f=".torrent;",h=".idx;.smi;.sub;.psb;.ssa;.ass;.usf;.ssf;.srt;.sup",p=".3gp;.asf;.avi;.divx;.f4v;.flv;.mkv;.mov;.movie;.mp4;.mpeg;.mpeg4;.mpg;.mpg2;.rm;.rmvb;.rp;.swf;.tp;.tp0;.ts;.wmv",g=".exe;.com;.bat;.msi",m=".wav;.flac;.mp3;.aac;.wma;.m4a;.mid;.ape;.ogg;.ra;.mod",v=".psd;.tga;.gif;.jpeg;.jpg;.jp2;.bmp;.ico;.pcx;.pdf;.png;.pbm;.pgm;.ppm;.pnm;.pgf;.arw;.cr2;.crw;.dcr;.dng;.erf;.kdc;\n  .mef;.mos;.mrw;.nef;.nrw;.orf;.pef;.ptx;.r3d;.raf;.raw;.rw2;.srf;.srw;.x3f;.ras;.tiff;.tif;.wbmp;.ilbm;.lbm;.iff;.ico",_=".txt;.html;.htm;.shtml;.xhtml;.chm;.hlp;.inf;.rtf;.tex;.ltx;.doc;.docx;.wps;.ppt;.pptx;.odf;.pdf;.xls;.xlsx;.docm;.dot;.dotm;.dotx;.email;.rp;.pps",y=".rar;.tar;.zip;.gzip;.cab;.uue;.arj;.bz2;.lzh;.jar;.ace;.iso;.7-zip;.7z",w=".asf;.mpg;.rmvb;.rm;.wmv;.avi;.mp4;.mpeg;.mkv;.mov;.ts;.flv;.3gp;.m2ts;",b=".exe;.com;.bat;.scr;.lnk;.pif;.wsh;",C=".iso;";!function(e){let t;function n(e){let n=t.Unkown,i=r.extname(e);return null!==i&&void 0!==i&&i.length>=2&&(i=i.toLowerCase()),void 0===i||""===i||i.length<2?n=t.Unkown:p.indexOf(i)>-1?n=t.Video:g.indexOf(i)>-1?n=t.Software:_.indexOf(i)>-1?n=t.Doc:m.indexOf(i)>-1?n=t.Music:v.indexOf(i)>-1?n=t.Pic:y.indexOf(i)>-1&&(n=t.Zip),i.length>1&&".z"===i.slice(0,2)&&/[0-9]+/.test(i.substring(2))&&(n=t.Zip),n}e.getTaskIcon=function(e,t,n){n=n||"xly-type-";let p="";do{if(t===i.DownloadKernel.TaskType.Bt){p="bt-file";break}if(t===i.DownloadKernel.TaskType.Group){p="group";break}p="unknown";let n=r.extname(e);if(""===n||n.length<2)break;let g=(n=n.toLowerCase()).substring(1);if(o.indexOf(g)>-1){p="doc"===n?"word":g;break}if(n+=";",a.indexOf(n)>-1){p="video";break}if(s.indexOf(n)>-1){p="install",[".mds;",".bin;",".img;"].indexOf(n)>-1&&(p="iso");break}if(l.indexOf(n)>-1){if(p="doc",[".htm;",".html;",".mht;"].indexOf(n)>-1){p="link";break}if(".docx;"===n){p="word";break}if(".xlsx;"===n){p="xls";break}if(".pptx;"===n){p="ppt";break}break}if(c.indexOf(n)>-1){p="music";break}if(u.indexOf(n)>-1){p="pic";break}if(d.indexOf(n)>-1){p="rar";break}if(f.indexOf(n)>-1){p="bt-link";break}if(h.indexOf(n)>-1){p="text";break}}while(0);return`${n}${p}`},function(e){e[e.Unkown=0]="Unkown",e[e.Video=1]="Video",e[e.Software=2]="Software",e[e.Doc=3]="Doc",e[e.Music=4]="Music",e[e.Pic=5]="Pic",e[e.Zip=6]="Zip",e[e.Bt=7]="Bt"}(t=e.FileExtType||(e.FileExtType={})),e.getTaskFileType=function(e,n){let i=t.Unkown;return void 0===n&&void 0!==e&&(n=r.extname(e)),null!==n&&void 0!==n&&n.length>=2&&(n=n.toLowerCase(),n+=";"),void 0===n||""===n||n.length<3?i=t.Unkown:a.indexOf(n)>-1?i=t.Video:s.indexOf(n)>-1?i=t.Software:l.indexOf(n)>-1?i=t.Doc:c.indexOf(n)>-1?i=t.Music:u.indexOf(n)>-1?i=t.Pic:d.indexOf(n)>-1?i=t.Zip:f.indexOf(n)>-1&&(i=t.Bt),n.length>1&&".z"===n.slice(0,2)&&/[0-9]+/.test(n.substring(2))&&(i=t.Zip),i},e.isVideoFileExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=r.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",w.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isSubtitleExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=r.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",h.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isExecutableExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=r.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",b.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isIsoExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=r.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",C.indexOf(n)>-1&&(t=!0)}while(0);return t},e.getGroupFileType=n,e.getDefaultGroupPrefix=function(e){let r="任务组";do{if(void 0===e||null===e||0===e.length)break;let i=[];for(let e=0;e<7;e++)i[e]=0;for(let t of e){let e=n(t);i[e]=i[e]+1}let o=t.Unkown;for(let e=1;e<i.length;e++)i[e]>i[o]&&(o=e);o===t.Video?r="视频任务组":o===t.Software?r="程序任务组":o===t.Music?r="音乐任务组":o===t.Pic?r="图片任务组":o===t.Doc?r="文档任务组":o===t.Zip&&(r="压缩包任务组")}while(0);return r},e.compareVersion=function(e,t){let n=-2;do{if(null===e||void 0===e||""===e||null===t||void 0===t||""===t)break;let r=0,i=0,o=0,a=0,s=0,l=0,c=0,u=0,d=e.split(/\./);if(null===d||void 0===d||d.length<3)break;if(r=Number(d[0]),i=Number(d[1]),o=Number(d[2]),null!==d[3]&&void 0!==d[3]&&""!==d[3]&&(a=Number(d[3])),null===(d=t.split(/\./))||void 0===d||d.length<3)break;if(s=Number(d[0]),l=Number(d[1]),c=Number(d[2]),null!==d[3]&&void 0!==d[3]&&""!==d[3]&&(u=Number(d[3])),s>r){n=1;break}if(s<r){n=-1;break}if(l>i){n=1;break}if(l<i){n=-1;break}if(c>o){n=1;break}if(c<o){n=-1;break}if(0!==a){if(u>a){n=1;break}if(u<a){n=-1;break}}n=0}while(0);return n}}(t.TaskUtilHelper||(t.TaskUtilHelper={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),i=n(6),o=n(22),a=n(23),s=n(28);!function(e){class t extends s.CommonIPCBase.Communicator{constructor(){super()}initialize(e){this.currInfo={id:void 0,context:e,isMainCommunicator:!1}}connect(){this.isConnected?o.warning("has been connected"):(this.sendConnectMsgToMain(),this.isConnected=!0,this.startListenIPCMessage(!1))}disconnect(){this.isConnected?(this.isConnected=!1,this.sendDisconnectMsgToMain()):o.warning("hasnot been connected yet")}sendMessageToMain(e){this.sendIPCMsgToMain(e)}sendMessageToMainSync(e){return this.sendIPCMsgToMain(e,!0)}sendMessageToRenderer(e,t){this.sendIPCMsgToRenderer(e,t)}handleRendererConnectMsg(e,t){do{if(i.isNullOrUndefined(t)){o.error("msgInfo is null");break}let e=t.msg.args[0];if(i.isNullOrUndefined(e)){o.error("connectRendererInfo is null");break}o.information(`Renderer: new renderer will connect, id = ${e.id}, context = ${e.context}`),this.rendererInfos.push(e)}while(0)}handleRendererDisconnectMsg(e,t){do{if(i.isNullOrUndefined(t)){o.error("msgInfo is null");break}let e=t.msg.args[0];if(i.isNullOrUndefined(e)){o.error("disconnectRendererInfo is null");break}o.information(`renderer will disconnect, id = ${e.id}, context = ${e.context}`);for(let t=0;t<this.rendererInfos.length;++t)if(this.rendererInfos[t]===e){this.rendererInfos.splice(t,1);break}}while(0)}sendConnectMsgToMain(){let e=this.sendMessageToMainSync({name:a.CommonIPCMessage.msgIPCRendererConnect,args:[]});this.currInfo.id=e[0],this.rendererInfos=e[1]}sendDisconnectMsgToMain(){this.sendMessageToMain({name:a.CommonIPCMessage.msgIPCRendererDisconnect,args:[]})}sendIPCMsgToMain(e,t=!1){let n=void 0;do{if(i.isNullOrUndefined(e)){o.error("msg is null");break}n=(t?r.ipcRenderer.sendSync:r.ipcRenderer.send)(a.CommonIPCMessage.msgIPCSendToMain,{msg:e,senderInfo:this.currInfo})}while(0);return n}sendIPCMsgToRenderer(e,t){do{if(i.isNullOrUndefined(e)){o.error("rendererId is null");break}if(i.isNullOrUndefined(t)){o.error("msg is null");break}let n=[e].concat(t.args);t.args=n,r.ipcRenderer.send(a.CommonIPCMessage.msgIPCSendToRenderer,{msg:t,senderInfo:this.currInfo})}while(0)}}e.RendererCommunicator=t,e.rendererCommunicator=new t}(t.CommonIPCRenderer||(t.CommonIPCRenderer={}))},function(e,t){e.exports=require("crypto")},function(e,t){e.exports=require("https")},function(e,t){e.exports=require("buffer")},function(e,t,n){"use strict";const r=n(13);if("renderer"===process.type){if(r.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(53);e.require=t.remoteRequire,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,r)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,r)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(r.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(57);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(6),i=n(22),o=n(23),a=n(28),s=n(38);!function(e){let t;!function(e){e[e.Unknown=-1]="Unknown",e[e.Success=0]="Success",e[e.FunctionNotExist=1]="FunctionNotExist",e[e.ParamaterError=2]="ParamaterError",e[e.CallFailed=3]="CallFailed"}(t=e.NativeCallErrorCode||(e.NativeCallErrorCode={}));class n{constructor(e,t,n){this.maxId=e,this.minId=t,this.invalidId=n}generateId(){return this.minId===this.maxId?this.invalidId:this.minId++}isInvalidId(e){return e===this.invalidId}}e.IdGenerator=n;const l=0;e.idGenerator=new n(1e7,1,l);class c{constructor(){this.jsCallbacks=new Map,this.jsReturnCallbacks=new Map,this.eventJsCallbakcs=new Map,this.jsRegisterFunctions=new Map,this.targetCommunitorInfo=s.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfoByContext(a.CommonIPCBase.mainRendererContext),this.bindMsgListeners(),this.notifyNativeCallReady()}CallNativeFunction(t,...n){do{if(r.isNullOrUndefined(t)||0===t.length){i.error("funcName is empty");break}if(!this.targetCommunitorInfo){i.error("CallNativeFunction but targetCommunitorInfo null");break}i.information("funcName = ",t),this.printArgs(n);let a=l;for(let t=0;t<n.length;++t)if(r.isFunction(n[t])){let r=e.idGenerator.generateId(),i=n[t];t===n.length-1?(this.jsReturnCallbacks.set(r,i),a=r,n.pop()):(this.jsCallbacks.set(r,i),n[t]=r)}s.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:o.CommonIPCMessage.msgNCCallNativeFunction,args:[t,a].concat(n)})}while(0)}AttachNativeEvent(t,n){let o=void 0;do{if(r.isNullOrUndefined(t)||0===t.length){i.error("eventName is empty");break}if(r.isNullOrUndefined(n)){i.error("callback is empty");break}let a=e.idGenerator.generateId();if(e.idGenerator.isInvalidId(a)){i.error("id error");break}if(this.eventJsCallbakcs.has(t))this.eventJsCallbakcs.get(t).set(a,n);else{let e=new Map;e.set(a,n),this.eventJsCallbakcs.set(t,e)}o=a}while(0);return o}DetachNativeEvent(e,t){do{if(r.isNullOrUndefined(e)||0===e.length){i.error("eventName is empty");break}if(r.isNullOrUndefined(t)){i.error("callback is empty");break}if(!this.eventJsCallbakcs.has(e)){i.error(`event: ${e} doesnot have listener`);break}if(!this.eventJsCallbakcs.get(e).has(t)){i.error(`event: ${e} doesnot have the listener of id=${t}`);break}this.eventJsCallbakcs.get(e).delete(t)}while(0)}CheckNativeFunction(t,n){do{if(r.isNullOrUndefined(t)||0===t.length){i.error("funcName is empty");break}if(r.isNullOrUndefined(n)){i.error("callback is empty");break}if(!this.targetCommunitorInfo){i.error("CheckNativeFunction but targetCommunitorInfo null");break}i.information("funcName = ",t);let a=e.idGenerator.generateId();this.jsReturnCallbacks.set(a,n),s.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:o.CommonIPCMessage.msgNCCheckNativeFunction,args:[t,a]})}while(0)}RegisterJSFunction(e,n){let o=t.ParamaterError;do{if(r.isNullOrUndefined(e)||0===e.length){i.error("funcName is empty");break}if(r.isNullOrUndefined(n)){i.error("jsFunc is empty");break}this.jsRegisterFunctions.set(e,n),o=t.Success}while(0);return o}bindMsgListeners(){s.CommonIPCRenderer.rendererCommunicator.onMessage(o.CommonIPCMessage.msgNCCallJsFunctionById,e=>{this.handleCallJsFunctionById(e.msg.args)}),s.CommonIPCRenderer.rendererCommunicator.onMessage(o.CommonIPCMessage.msgNCCallJsFunctionByName,e=>{this.handleCallJsFunctionByName(e.msg.args)}),s.CommonIPCRenderer.rendererCommunicator.onMessage(o.CommonIPCMessage.msgNCNativeFireEvent,e=>{this.handleNativeFireEvent(e.msg.args)})}handleCallJsFunctionById(t){do{let n=t[0];if(!r.isNumber(n)){i.error(`id error id = ${n}`);break}if(e.idGenerator.isInvalidId(n)){i.error(`id = ${n} invalid`);break}let o=null,a=0;if(this.jsCallbacks.has(n)&&(a=1,o=this.jsCallbacks.get(n)),this.jsReturnCallbacks.has(n)&&(a=2,o=this.jsReturnCallbacks.get(n)),0===a){i.error(`callbacks[${n}] is null`);break}t.splice(0,1),o.apply(null,t),2===a&&this.jsReturnCallbacks.delete(n)}while(0)}handleCallJsFunctionByName(e){do{let t=e[0];if(!r.isString(t)){i.error(`funcName error funcName = ${t}`);break}if(!this.jsRegisterFunctions.has(t)){i.error(`jsRegisterFunctions[${t}] is null`);break}e.splice(0,1),this.jsRegisterFunctions.get(t).apply(null,e)}while(0)}handleNativeFireEvent(e){do{let t=e[0];if(!r.isString(t)){i.warning(`eventName error eventName = ${t}`);break}if(!this.eventJsCallbakcs.has(t)){i.warning(`eventJsCallbakcs[${t}] is null`);break}e.shift(),this.eventJsCallbakcs.get(t).forEach((t,n,o)=>{i.information(`value = ${t}, key = ${n}, map = ${o}`),r.isNullOrUndefined(t)||t.apply(null,e)})}while(0)}notifyNativeCallReady(){do{if(!this.targetCommunitorInfo){i.error("notifyNativeCallReady but targetCommunitorInfo null");break}s.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:o.CommonIPCMessage.msgNCNativeCallReady,args:[s.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfo()]})}while(0)}printArgs(e){for(let t in e)i.information(`index ${t} = `,e[t])}}e.NativeCallImpl=c,e.nativeCall=new c}(t.NativeCallModule||(t.NativeCallModule={}))},function(e,t){e.exports=require("http")},function(e,t,n){"use strict";var r=n(10),i=n(118),o={"Content-Type":"application/x-www-form-urlencoded"};function a(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var s,l={adapter:("undefined"!=typeof XMLHttpRequest?s=n(119):"undefined"!=typeof process&&(s=n(17)),s),transformRequest:[function(e,t){return i(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(a(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(a(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};l.headers={common:{Accept:"application/json, text/plain, */*"}},r.forEach(["delete","get","head"],function(e){l.headers[e]={}}),r.forEach(["post","put","patch"],function(e){l.headers[e]=r.merge(o)}),e.exports=l},function(e,t,n){"use strict";var r=n(68);e.exports=function(e,t,n,i,o){var a=new Error(e);return r(a,t,n,i,o)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(51),i=n(12);t.Parser=class extends i.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),i=JSON.parse(e);this.emit("message",i),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},function(e,t){e.exports=require("zlib")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(6),o=n(1),a=n(8),s=n(32),l=n(3),c=n(20),u=a.default(r.join(__rootDir,"../bin/ThunderHelper.node"));function d(){"console"===process.env.TL_OUTPUT?o.default.outputLogger=o.outputLoggerConsole:o.default.outputLogger=function(){function e(e){return function(...t){u.printEtwLog(e,function(...e){return e.map(e=>i.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[o.LogLevel.Critical]:e(o.LogLevel.Critical),[o.LogLevel.Error]:e(o.LogLevel.Error),[o.LogLevel.Warning]:e(o.LogLevel.Warning),[o.LogLevel.Information]:e(o.LogLevel.Information),[o.LogLevel.Verbose]:e(o.LogLevel.Verbose)}}()}function f(){let e=s.DevEnvHelperNS.isLogEnable();"1"===process.env.TL_ENABLE!==e&&(process.env.TL_ENABLE=e?"1":"0",o.default.enableLogger=e,u.enableETWLogger(e));let t=s.DevEnvHelperNS.getLogOutput();t&&t!==process.env.TL_OUTPUT&&(process.env.TL_OUTPUT=t,d())}process.env.TL_ENABLE="0",o.default.enableLogger="1"===process.env.TL_ENABLE,d(),f(),"browser"===process.type?l.ipcMain.on(c.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{l.BrowserWindow.getAllWindows().forEach(e=>{e.isDestroyed()||e.webContents.send(c.ThunderChannelList.channelMRUpdateLogEnviroment)}),f()}):"renderer"===process.type&&l.ipcRenderer.on(c.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{f()})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(33),i=n(12),o=n(30),a=n(47),s=n(29);t.Client=class extends i.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=s.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new a.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{o.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){o.error(e.message)}else o.information("This socket has been ended by the other party",this.context&&this.context.name)}}},function(e,t){e.exports=require("string_decoder")},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(14),o=(n(21),n(2)),a=n(6);let s=null;const l=n(11),c=n(3),u=n(1),d=n(8),f=n(20),h=n(7),p="xdas_profile_stat";let g="",m=void 0,v=null,_=void 0,y=null,w=d.default(o.join(__rootDir,"../bin/ThunderHelper.node")),b=new Set;function C(){return r(this,void 0,void 0,function*(){return new Promise(e=>r(this,void 0,void 0,function*(){void 0===_&&(null===y&&(y=new Promise(e=>{e(_=function(e){let t="";if(0===e.length&&"renderer"===process.type){let e=o.normalize(decodeURIComponent(window.location.href)),n=e.indexOf(process.resourcesPath);n=n>-1?n+process.resourcesPath.length+1:n;let r=e.length-1,i=e.indexOf("?"),a=e.indexOf("#");r=i>-1?Math.min(i-1,r):r,r=a>-1?Math.min(a-1,r):r,n>-1&&r>=n&&(t=e.substr(n,r-n+1))}return 0===t.length&&(t=0!==e.length?e:process.type),t=t.replace(/\||,|;/g,"_")}(""))})),_=yield y),e(_)}))})}function E(e){let t="";do{if(null===e||void 0===e)break;switch(typeof e){case"string":t=e;break;case"object":t=a.inspect(e)||"";break;case"number":case"boolean":t=e.toString()||""}}while(0);return t}function T(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function k(e){return r(this,void 0,void 0,function*(){return new Promise(t=>r(this,void 0,void 0,function*(){let r=void 0;null===s&&(s=yield Promise.resolve().then(()=>n(39)));let i=s.createHash("md5");null!==i&&(r=i.update(e).digest("hex")),t(r)}))})}function S(){return new Promise(e=>r(this,void 0,void 0,function*(){let t="";t=void 0===m?"browser"===process.type?function(){if(void 0===m){let e=process.argv.length,t=process.argv;for(let n=0;n<e;n++){let e=t[n];if(e.startsWith("-StartType:")){m=e.substring("-StartType:".length);break}}void 0===m&&(m="")}return m}():yield function(){return r(this,void 0,void 0,function*(){return null===v&&(v=new Promise(e=>{c.ipcRenderer.send(f.ThunderChannelList.channelRMGetBrowserStartType),c.ipcRenderer.once(f.ThunderChannelList.channelMRGetBrowserStartTypeResult,(t,n)=>{m=n,e(n),v=null})})),v})}():m,e(t)}))}function R(e,t,n,i){return r(this,void 0,void 0,function*(){let o=E(t),a=E(n),s=yield k(a),c=function(e){let t=new RegExp(T("file:///"),"g"),n=new RegExp(T(process.resourcesPath+"\\"),"g"),r=new RegExp(T(encodeURI(process.resourcesPath.replace(/\\/g,"/")+"/")),"g");return e.replace(t,"").replace(n,"").replace(r,"")}(E(i)),u=yield k(c),d=`${e}:${s}:${u}`;b.has(d)||(b.add(d),l.XLStatNS.trackEvent(p,"uncaught_exception","",0,0,0,0,`type=${e},business_name=${yield C()},code=${o},message_hash=${s},message=${encodeURI(a)},stack_hash=${u},stack=${encodeURI(c)}`)),function(e,t,n,i){return r(this,void 0,void 0,function*(){})}().catch()})}function x(e){console.error(e);let t=e||{};R("unhandledRejection",t.code,t instanceof Error?t.message:t,t.stack).catch()}!function(e){e.init=function(e){g=e},e.trackColdStartUpEvent=function(e){return r(this,void 0,void 0,function*(){let t=w.iSColdStartUp()?1:0,n=i.release(),r=w.performanceMonitorReporter.getProcessUptime(),o=yield S(),a=`key=${e},start_type=${o},cold_start_up=${t},os_version=${n},cost_time=${r}`;l.XLStatNS.trackEvent(p,"cold_start_up","",0,0,0,0,a)})}}(t.PerformanceMonitorStatNS||(t.PerformanceMonitorStatNS={})),function(){return r(this,void 0,void 0,function*(){if(process.on("uncaughtException",e=>{console.error(e);let t=e||{};R("uncaughtException",t.code,t.message,t.stack).catch()}),"browser"===process.type)process.on("unhandledRejection",(e,t)=>{x(e)}),c.ipcMain.on(f.ThunderChannelList.channelRMGetBrowserStartType,function(e){return r(this,void 0,void 0,function*(){let t=yield S();e.sender.send(f.ThunderChannelList.channelMRGetBrowserStartTypeResult,t)})});else if("browser"!==process.type){window.addEventListener("unhandledrejection",e=>{x(e&&e.reason||{})});let e=yield h.asyncRemoteCall.getCurrentWebContents();null!==e&&void 0!==e&&e.once("did-finish-load",()=>{(function(){return r(this,void 0,void 0,function*(){do{if("browser"===process.type)break;if(null===window.performance.timing||void 0===window.performance.timing)break;let e=w.iSColdStartUp()?1:0,t=i.release(),n=window.performance.timing,r=n.loadEventEnd-n.navigationStart,o=n.fetchStart-n.navigationStart,a=n.domainLookupEnd-n.domainLookupStart,s=n.connectEnd-n.connectStart,c=n.responseStart-n.requestStart,u=n.responseEnd-n.responseStart,d=n.domComplete-n.domLoading,f=yield S();l.XLStatNS.trackEvent(p,"page_load_time","",0,0,0,0,`start_type=${f},cold_start_up=${e},os_version=${t},load_time=${r},before_fetch_time=${o},domin_lookup_time=${a},connect_time=${s},first_response_time=${c},responseTime=${u},domTime=${d},process=${g}`)}while(0)})})().catch()})}u.default.hook("beforeLog",(e,t,...n)=>{e===u.LogLevel.Critical&&l.XLStatNS.trackEvent(p,"critical_error","",0,0,0,0,`module_name=${t},messages=${n}`)})})}().catch()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const r=n(3),i=n(41),o=n(54),a=n(55),s=n(35),l=n(56),c=n(13),u=n(36),d=r.ipcRenderer,f=process.electronBinding("v8_util"),h=new o.default,p=f.createIDWeakMap(),g=f.getHiddenValue(global,"contextId");class m{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function v(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let r=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:v(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(r))return{type:"buffer",value:i.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(u.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(f.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:f.getHiddenValue(e,"__remote_id__")};let r={type:e instanceof m?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)r.members.push({name:t,value:n(e[t])});return t.delete(e),r}if("function"==typeof e){return{type:"function",id:h.add(e),location:f.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function _(e,t,n){u.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function y(e,t,n,r=!1){const i=t=>{if(e.hasOwnProperty(t.name)&&!r)return;let n,i={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(p.has(t.value.refId)&&(n=p.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=E(t.value,e,t.name);i.get=(()=>n),i.set=(e=>n=e)}else"get"===t.type&&(i.get=(()=>n),t.writable&&(i.set=(e=>{n=e})),n=E(t.value));Object.defineProperty(e,t.name,i)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)i(n[t])}}function w(e,t,n){if(n){let t=E(n);Object.setPrototypeOf(e,t)}}function b(e,t){f.setHiddenValue(e,"__remote_id__",t)}function C(e){return f.getHiddenValue(e,"__remote_id__")}function E(e,t,n){const r={value:()=>e.value,array:()=>e.members.map(e=>E(e)),buffer:()=>i.Buffer.from(e.value),promise:()=>Promise.resolve({then:E(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:r,value:i}=e.members[n];t[r]=i}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let o;return e.type in r?o=r[e.type]():e.refId?p.has(e.refId)?(f.addRemoteObjectRef(g,e.refId),o=p.get(e.refId)):(c.warn("[metaToValue] refId point to null"+e.refId),o="function"===e.type?()=>{}:{}):e.id?p.has(e.id)?(f.addRemoteObjectRef(g,e.id),y(o=p.get(e.id),e.id,e.members,!0),w(o,e.id,e.proto)):(o="function"===e.type?t?function(e,t,n){if(p.has(n.id))return p.get(n.id);let r=C(e),i=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(i,"name",{value:t,writable:!1,enumerable:!0});let o=new Proxy(i,{apply:(e,n,i)=>new Promise((e,o)=>{let c=C(n);if(c||(c=C(n.__remoteObj_)),!c)throw Error("is this function was a bound function?");let u=s.default.browser.memberCall,f=l.default(u),h=v(i);d.send(u,g,f,c,r,t,h),a.default.add(f,t=>{try{_(E(t),e,o)}catch(e){o(e)}})}),construct:(e,n,i)=>new Promise((e,i)=>{let o=s.default.browser.memberConstruct,c=l.default(o);d.send(o,g,c,r,t,v(n)),a.default.add(c,t=>{try{let n=E(t);e(n)}catch(e){i(e)}})})});return f.setHiddenValue(i,"__remote_id__",n.id),o}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return b(n,t),new Proxy(n,{apply:(e,n,r)=>new Promise((e,i)=>{let o=s.default.browser.functionCall,c=l.default(o),u=C(n);d.send(o,g,c,u,t,v(r)),a.default.add(c,t=>{try{_(E(t),e,i)}catch(e){i(e)}})}),construct:(e,n,r)=>new Promise((e,r)=>{let i=s.default.browser.construct,o=l.default(i);d.send(i,g,o,t,v(n)),a.default.add(o,t=>{try{let n=E(t);e(n)}catch(e){r(e)}})})})}(e):{},f.setRemoteObjectFreer(o,g,e.id),p.set(e.id,o),f.setHiddenValue(o,"__remote_id__",e.id),f.addRemoteObjectRef(g,e.id),function(e){let t=C(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,r){if("function"==typeof r)throw new Error("set a function to a remote member is dangerous");return new Promise((i,o)=>{let c=s.default.browser.memberSet,u=l.default(c),f=v([r]);d.send(c,g,u,t,n,f),a.default.add(u,t=>{try{let a=E(t);e[n]=r,i(a)}catch(e){o(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((r,i)=>{let o=s.default.browser.memberGet,c=l.default(o);d.send(o,g,c,t,n),a.default.add(c,t=>{try{const o=E(t);e[n]=o,r(o)}catch(e){i(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let r=s.default.browser.sync,i=l.default(r);d.send(r,g,i,t),a.default.add(i,r=>{try{if(r.id!==t)throw Error("SYNC_RETURN: remote id not match");let i=E(r);e(i)}catch(e){n(e)}})})}}})}(o),y(o,e.id,e.members),w(o,e.id,e.proto),Object.defineProperty(o.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=o[e],r=o.__proto__;for(;r;){if(Object.prototype.hasOwnProperty.call(r,e)){delete r[e];break}r=r.__proto__}Object.defineProperty(o,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):c.error("no id of meta:",e),o}t.Interest=m;class T{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,r)=>{let i=this.getChannel(e),o=l.default(i);d.send(i,g,o,t),a.default.add(o,e=>{try{let t=E(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){r(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=s.default.browser.require;break;case"builtin":t=s.default.browser.builtIn;break;case"global":t=s.default.browser.global;break;case"current_window":t=s.default.browser.currentWindow;break;case"current_web_contents":t=s.default.browser.currentWebContents;break;case"client_web_contents":t=s.default.browser.clientWebContents;break;case"web_contents":t=s.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function k(e,t,n){try{a.default.invoke(t,n).remove(t)}finally{a.default.remove(t)}}d.on(s.default.renderer.requireReturn,k),d.on(s.default.renderer.getBuiltInReturn,k),d.on(s.default.renderer.getGlobalReturn,k),d.on(s.default.renderer.currentWindowReturn,k),d.on(s.default.renderer.currentWebContentsReturn,k),d.on(s.default.renderer.functionCallReturn,k),d.on(s.default.renderer.constructReturn,k),d.on(s.default.renderer.memberCallReturn,k),d.on(s.default.renderer.memberSetReturn,k),d.on(s.default.renderer.memberGetReturn,k),d.on(s.default.renderer.memberConstructReturn,k),d.on(s.default.renderer.callback,(e,t,n)=>{h.apply(t,E(n))}),d.on(s.default.renderer.syncReturn,k),d.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{c.info("[RELEASE_CALLBACK]: callbackId:",t),h.remove(t)}),process.on("exit",()=>{d.send(s.default.browser.contextRelease)});const S=["__resolve","__isResolved"],R=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],x=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function P(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(x(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(x(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(x(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(x(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(x(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(x(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,r)=>(x(e),Reflect.defineProperty(e.__remoteObj_,n,r)),get:(t,n,r)=>{if("string"==typeof n){if(String.prototype.includes.call(R,n)){return e[n]}if(String.prototype.includes.call(S,n)){return e[n]}}return x(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,r,i)=>(x(e),Reflect.set(e.__remoteObj_,n,r,i)),ownKeys:t=>(x(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,r)=>{x(e),Reflect.apply(e.__remoteObj_,n,r)},construct:(t,n,r)=>{if(x(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,r)=>{let i=s.default.browser.construct,o=l.default(i),c=f.getHiddenValue(e.__remoteObj_,"__remote_id__");d.send(i,g,o,c,v(n)),a.default.add(o,e=>{try{t(E(e))}catch(e){r(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=C(e);t&&b(n,t)}}),n}t.remoteRequire=function(e){return P(new T("module",e))},t.getBuiltin=function(e){return P(new T("builtin",e))},t.getGlobal=function(e){return P(new T("global",e))},t.getCurrentWindow=function(){return P(new T("current_window"))},t.getCurrentWebContents=function(){return P(new T("current_web_contents"))},t.getWebContents=function(){return P(new T("web_contents"))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=r.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,i=(new Error).stack;let o,a=n.exec(i);for(;null!==a;){const e=a[1];if(!e.includes("native")&&!e.includes("electron.asar")){o=/([^/^)]*)\)?$/gi.exec(e)[1];break}a=n.exec(i)}return this.callbacks[t]=e,r.setHiddenValue(e,"__remote_callback_id__",t),r.setHiddenValue(e,"__remote_call_location__",o),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(r.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13);var i;!function(e){let t={};e.add=function(e,n,r){t[e]={func:n,thisArg:r}},e.invoke=function(n,...i){let o=t[n];return o?o.thisArg?o.func.apply(o.thisArg,...i):o.func(...i):r.error(`Cannot invoke function by unrecognize id. ${n}`),e},e.remove=function(e){delete t[e]}}(i||(i={})),t.default=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=0;t.default=function(e){return e?e.concat(".").concat(String(++r)):String(++r)}},function(e,t,n){"use strict";const r=n(3),i=n(58),o=n(35),a=n(59),s=n(13),l=n(36),c=r.ipcMain,u=process.electronBinding("v8_util");let d=u.createDoubleIDWeakMap();const f=new i.default;function h(e,t,n,r,i,o){let a,l=!1,c=null,u=!1;do{try{a=t[r]}catch(e){l=!0}if(l)try{a=n.value[r],l=!1,n.meta.extendedMemberNames.push(r),u=!0}catch(e){s.error(`property ${r} untouchable, even try root[name]`)}if(l)break;let i=Object.getOwnPropertyDescriptor(t,r);if(void 0===i){s.warn(`descriptor of property ${r} is undefined`);break}c={name:r,enumerable:i.enumerable,writable:!1,type:"get"},void 0===i.get&&"function"==typeof a?c.type="method":((i.set||i.writable)&&(c.writable=!0),c.type="get"),u?(c.configurable=!0,c.value=m(e,a,o,!1,null)):c.value=m(e,a,o,!1,n)}while(0);return c}function p(e,t,n,r=null){let i=Object.getOwnPropertyNames(t);"function"==typeof t&&(i=i.filter(function(e){return!String.prototype.includes.call(a.propertiesOfFunction,e)}));let o=[];do{if(0===i.length)break;let a=i.length;for(let s=0;s<a;s++){let a=h(e,t,n,i[s],0,r);a&&o.push(a)}}while(0);return o}function g(e,t,n,r=null){let i=null,o=Object.getPrototypeOf(t);return i=null===o||o===Object.prototype||o===Function.prototype?null:m(e,o,r,!1,n)}function m(e,t,n=null,r=!1,i=null){n=null===n?{}:n;const o={type:typeof t};"object"===o.type&&(o.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":l.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&u.getHiddenValue(e,"simple")&&(n="value"),n}(t,r));do{if("object"===o.type||"function"===o.type){let r=f.getIdOfObject(t);if(r&&n[r]){o.refId=r,f.add(e,t);break}}"array"===o.type?o.members=t.map(t=>m(e,t,n)):"object"===o.type||"function"===o.type?(null==i&&(o.extendedMemberNames=[],i={value:t,meta:o}),o.name=t.constructor?t.constructor.name:"",o.id=f.add(e,t),n[o.id]=!0,o.members=p(e,t,i,n),o.proto=g(e,t,i,n)):"buffer"===o.type?o.value=Buffer.from(t):"promise"===o.type?(t.then(function(){},function(){}),o.then=m(e,function(e,n){t.then(e,n)})):"error"===o.type?(o.members=v(t),o.members.push({name:"name",value:t.name})):"date"===o.type?o.value=t.getTime():(o.type="value",o.value=t)}while(0);return o}function v(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function _(e,t,n,i){const a=function(i){let l,c,h=0,p=0;switch(i.type){case"value":return i.value;case"remote-object":return f.get(i.id);case"array":return _(e,t,n,i.value);case"buffer":return Buffer.from(i.value);case"date":return new Date(i.value);case"promise":return Promise.resolve({then:a(i.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:i.name}),h=0,p=(c=i.members).length;h<p;h++)e[(l=c[h]).name]=a(l.value);return e}case"function":{const a=e.id,l=[n,i.id];if(s.info("renderer function id:"+l),d.has(l))return d.get(l);let c=function(...t){s.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||a!==e.id?function(e,t,n){let i="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof r.webContents.constructor){const{sender:e}=t[0],r=e.eventNames().filter(t=>{let r=e.listeners(t),i=!1;return r.forEach(e=>{e===n&&(i=!0)}),i});r.length>0&&(i+=`\nRemote event names: ${r.join(", ")}`,r.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}s.warn(i)}(i,n,c):e.send(o.default.renderer.callback,i.id,m(e,n))};return Object.defineProperty(c,"length",{value:i.length}),u.setRemoteCallbackFreer(c,t,n,i.id,e),d.set(l,c),c}default:throw new TypeError(`Unknown type: ${i.type}`)}};return i.map(a)}function y(e,t,n,r){let i,o;try{return t.apply(n,r)}catch(e){return o=t.name,new Error(`Could not call remote function '${i=null!=o?o:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function w(e){return{type:"exception",message:e.message,stack:e.stack||e}}function b(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var C;!function(e){const t=(e,t,...n)=>{const r=e.sender;r.isDestroyed()?s.warn("webcontext is destroyed."):r.send(t,...n)};e.startServer=function(){c.on(o.default.browser.require,(e,n,r,i)=>{s.info(`[REQUIRE] module=${i} `);let a=process.mainModule.require(i),l=m(e.sender,a);t(e,o.default.renderer.requireReturn,r,l)}),c.on(o.default.browser.builtIn,(e,n,i,a)=>{s.info(`[BUILTIN]: property=${a} contextId=${n}`);let l=r[a],c=m(e.sender,l);s.info(`[BUILTIN]: returns remoteId:${c.id}, type: ${typeof l}`),t(e,o.default.renderer.getBuiltInReturn,i,c)}),c.on(o.default.browser.global,(e,n,r,i)=>{s.info(`[GLOBAL]: proerty:${i} contextId=${n}`);let a,l=global[i];a=m(e.sender,l),s.info(`[GLOBAL]: returns remoteid=${a.id}, obj=`+typeof l),t(e,o.default.renderer.getGlobalReturn,r,a)}),c.on(o.default.browser.currentWindow,(e,n,r,i)=>{s.info(`[CURRENT_WINDOW]: property=${i} contextId=${n}`);let a=e.sender.getOwnerBrowserWindow.call(e.sender),l=m(e.sender,a);s.info(`[CURRENT_WINDOW]: returns remoteid=${l.id}, obj=`+a),t(e,o.default.renderer.currentWindowReturn,r,l)}),c.on(o.default.browser.currentWebContents,(e,n,r,i)=>{t(e,o.default.renderer.currentWebContentsReturn,r,m(e.sender,e.sender))}),c.on(o.default.browser.webContents,(e,n,i,a)=>{s.info(`[WebContents]: proerty:${a} contextId=${n}`);let l,c=r.webContents;l=m(e.sender,c),s.info(`[WebContents]: returns remoteid=${l.id}, obj=`+typeof c),t(e,o.default.renderer.webContentsReturn,i,l)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(r=>{n?e.once(r,t[r]):e.on(r,t[r])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};c.on(o.default.browser.construct,(n,r,i,a,l)=>{let c,u=null;try{s.info(`[CONSTRUCTOR]: remoteId=${a} `);let d=l.length>0?l[l.length-1]:null;l=_(n.sender,n.frameId,r,l);let h=f.get(a);null==h&&b(`Cannot call constructor on missing remote object ${a}`),d&&"interest"===d.type&&(u=l.pop());let p=new(Function.prototype.bind.apply(h,[null,...l]));p&&u&&e(p,u),c=m(n.sender,p,null,!1),s.info(`[CONSTRUCTOR]: returns remoteId =${c.id} name=${h.name} `)}catch(e){c=w(e)}finally{t(n,o.default.renderer.constructReturn,i,c)}}),c.on(o.default.browser.functionCall,function(e,n,r,i,a,l){let c;try{s.info(`[FUNCTION_CALL]: remoteId=${a}`),l=_(e.sender,e.frameId,n,l);let u=f.get(a);if(null==u)s.error(`Cannot call function on missing remote object ${a}`),c=m(e.sender,void 0);else{let t=i?f.get(i):global;if(t){let n=y(0,u,t,l);c=m(e.sender,n)}else s.error(`Cannot call function(${a}) on missing context(${i})`),c=m(e.sender,void 0)}s.info(`[FUNCTION_CALL]: name=${u.name}`)}catch(e){c=w(e)}finally{t(e,o.default.renderer.functionCallReturn,r,c)}}),c.on(o.default.browser.memberCall,function(e,n,r,i,a,l,c){let u;s.info(`[MEMBER_CALL]: thisArg=${i}, remoteId=${a}, method=${l}, args count=${c.length}`);try{c=_(e.sender,e.frameId,n,c);let d=f.get(a);null==d&&b(`Cannot call function '${l}' on missing remote object ${a}`);let h=i?f.get(i):d;if(h){let t=y(0,d[l],h,c);u=m(e.sender,t),s.info("[MEMBER_CALL]: return="+t)}else u=m(e.sender,void 0)}catch(e){u=w(e)}finally{t(e,o.default.renderer.memberCallReturn,r,u)}}),c.on(o.default.browser.memberGet,function(e,n,r,i,a){let l;try{s.info(`[MEMBER_GET]: remoteId=${i}, property=`,a);let n=f.get(i);null==n&&b(`Cannot get property '${Object.toString.call(a)}' on missing remote object ${i}`);let c=n[a];l=m(e.sender,c)}catch(e){l=w(e)}finally{t(e,o.default.renderer.memberGetReturn,r,l)}}),c.on(o.default.browser.memberSet,function(e,n,r,i,a,l){try{s.info(`[MEMBER_SET]: remoteId=${i}, property=`+a),l=_(e.sender,e.frameId,n,l);let c=f.get(i);null==c&&b(`Cannot set property '${Object.toString.call(a)}' on missing remote object ${i}`),c[a]=l[0],t(e,o.default.renderer.memberSetReturn,r,{type:"value",value:!0})}catch(n){t(e,o.default.renderer.memberSetReturn,r,w(n))}}),c.on(o.default.browser.memberConstruct,function(n,r,i,a,l,c){let u,d=null;try{s.info(`[MEMBER_CONSTRUCTOR]: regId=${a}, method=${l}`);let h=c.length>0?c[c.length-1]:null;c=_(n.sender,n.frameId,r,c);let p=f.get(a);null==p&&b(`Cannot call constructor '${l}' on missing remote object ${a}`),h&&"interest"===h.type&&(d=c.pop());let g=p[l],v=new(Function.prototype.bind.apply(g,[null,...c]));v&&d&&e(v,d),u=m(n.sender,v)}catch(e){u=w(e)}finally{t(n,o.default.renderer.memberConstructReturn,i,u)}}),c.on(o.default.browser.sync,function(e,n,r,i){let a=f.get(i);t(e,o.default.renderer.syncReturn,r,m(e.sender,a))}),c.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=f.get(t);if(r.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let r=n.name;r||(r=n.constructor?n.constructor.name:""),f.remove(e.sender.id,t)}else t<0&&s.warn("remote id reference to nothing:",t)}),c.on(o.default.browser.contextRelease,e=>{f.clear(e.sender.id)})},e.getObjectRegistry=function(){return f}}(C||(C={})),e.exports=C},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13),i=-1*Math.pow(2,31),o=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),r=e.id;let i=this.owners.get(r);return i||(i=new Set,this.owners.set(r,i),this.registerDeleteListener(e,r)),i.has(n)||(i.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return o.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=o.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=i)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),o.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(o.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),i=(o,a)=>{a===n&&(r.info("render-view-deleted: processid="+n),(()=>{r.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",i),this.clear(t))};e.on("render-view-deleted",i)}}},function(e,t,n){"use strict";var r;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(r||(r={})),e.exports=r},function(e,t){e.exports=require("readline")},function(e,t,n){e.exports=n(9)(216)},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(7),o=n(18),a=n(4),s=n(32);!function(e){function t(e,t){return r(this,void 0,void 0,function*(){if(null!==e){let n=e.webContents;(yield n.isDevToolsOpened())?yield n.closeDevTools():yield n.openDevTools(t)}})}e.openDevTool=t,e.enableDevTools=function(e){return r(this,void 0,void 0,function*(){window.addEventListener("keyup",n=>r(this,void 0,void 0,function*(){if("F12"===n.key&&n.ctrlKey)s.DevEnvHelperNS.isLogEnable()&&(yield t(yield i.asyncRemoteCall.getCurrentWindow(),e));else if(("t"===n.key||"T"===n.key)&&n.altKey&&s.DevEnvHelperNS.isLogEnable()){let e=document.getElementById("DevProcessPid");if(e)document.body.removeChild(e);else{(e=document.createElement("p")).id="DevProcessPid",e.style.position="absolute",e.style.left="0px",e.style.top="0px",e.style.width="100%",e.style.zIndex="10000",e.style.color="rgb(255,0,0)",document.body.appendChild(e);let t="process.pid:"+process.pid;t+="\r\nlocation.href:"+location.href,t+="\r\nprocess.argv:"+process.argv,e.innerText=t}}}),!0)})},e.enableDragOpenFile=function(e){void 0===e&&(e=!1),document.addEventListener("dragover",e=>(e.preventDefault(),e.stopPropagation(),!1),!0),document.addEventListener("drop",e=>r(this,void 0,void 0,function*(){e.preventDefault(),e.stopPropagation();let t=e.dataTransfer,n=t.files,r=t.items;if(void 0!==r&&null!==r&&r.length>0)for(let e=0;e<r.length;e++){let t=r[e];"string"===t.kind&&"text/uri-list"===t.type?t.getAsString(e=>{a.client.callServerFunction("DropOpenUrl",e).catch()}):t.kind}if(void 0!==n&&null!==n&&n.length>0)for(let e=0;e<n.length;e++){let t=n[e].path;void 0!==t&&null!==t&&""!==t&&(yield o.FileSystemAWNS.existsAW(t))&&a.client.callServerFunction("DropOpenFile",t).catch()}return!1}),!0)}}(t.ThunderToolsNS||(t.ThunderToolsNS={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(7),o=n(1),a=n(16),s=o.default.getLogger("MenuSkinNS");!function(e){e.setStyle=function(e,t){return r(this,void 0,void 0,function*(){if(s.information("setStyle",e,t),null!==e){const{WindowPreferenceNS:e}=yield Promise.resolve().then(()=>n(73));let t={windowPreference:e.getWindowPreference()};s.information("skinOpts",t)}})},e.popEditableDefaultContextMenu=function(e,t,n){return r(this,void 0,void 0,function*(){let n=yield i.asyncRemoteCall.getCurrentWebContents();n.once("context-menu",(o,l)=>r(this,void 0,void 0,function*(){if(s.verbose(o),l.isEditable){let r=[{label:"撤销",enabled:l.editFlags.canUndo,click:()=>{n.undo()}},{type:"separator"},{label:"剪切",enabled:l.editFlags.canCut,click:()=>{n.cut()}},{label:"复制",enabled:l.editFlags.canCopy,click:()=>{n.copy()}},{label:"粘贴",enabled:l.editFlags.canPaste&&a.ThunderUtil.isClipboardTextFormatAvailable(),click:()=>{n.paste()}},{label:"删除",enabled:l.editFlags.canDelete,click:()=>{n.delete()}},{type:"separator"},{label:"全选",enabled:l.editFlags.canSelectAll,click:()=>{n.selectAll()}}];if(void 0!==e&&"function"==typeof e){let n=e(l);void 0!==n&&n.length>0&&(void 0===t?t=r.length:(t<0&&(t=r.length+1+t)<0&&(t=0),t>r.length&&(t=r.length)),r.splice(t,0,...n))}let o=yield(yield i.asyncRemoteCall.getMenu()).buildFromTemplate(r),s=yield i.asyncRemoteCall.getCurrentWindow();yield o.popup({window:s})}}))})}}(t.MenuSkinNS||(t.MenuSkinNS={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.getTextScale=function(){return 1},e.fixWindowSize=function(e,t){return r(this,void 0,void 0,function*(){window.resizeTo(e,t)})},e.autoFixWindowSize=function(){},e.fixZoomFactory=function(){}}(t.FixTextScale||(t.FixTextScale={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(4),i=n(1).default.getLogger("common/skin"),o=!0;let a=null;function s(e){if(!o||null===e||void 0===e)return;let t=localStorage.getItem("skin_body_classes");a&&a.classes===e.classes||(localStorage.removeItem("skin_body_classes"),e&&e.classes&&(document.body.classList.add(e.classes),localStorage.setItem("skin_body_classes",e.classes)),a&&a.classes?document.body.classList.remove(a.classes):t!==e.classes&&document.body.classList.remove(t),a=Object.freeze(Object.assign({},e)))}r.client.callServerFunction("GetSkinInfo").then(s).catch(e=>{i.warning(e)}),r.client.attachServerEvent("OnChangeSkin",(e,...t)=>{s(t[0])})},function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},function(e,t,n){"use strict";var r=n(46);e.exports=function(e,t,n){var i=n.config.validateStatus;n.status&&i&&!i(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},function(e,t,n){"use strict";e.exports=function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e}},function(e,t,n){"use strict";var r=n(10);function i(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var o;if(n)o=n(t);else if(r.isURLSearchParams(t))o=t.toString();else{var a=[];r.forEach(t,function(e,t){null!==e&&void 0!==e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),a.push(i(t)+"="+i(e))}))}),o=a.join("&")}return o&&(e+=(-1===e.indexOf("?")?"?":"&")+o),e}},function(e,t,n){var r=n(34),i=n(44),o=n(40),a=n(83),s=n(75).Writable,l=n(124)("follow-redirects"),c={GET:!0,HEAD:!0,OPTIONS:!0,TRACE:!0},u=Object.create(null);function d(e,t){s.call(this),e.headers=e.headers||{},this._options=e,this._redirectCount=0,this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var n=this;if(this._onNativeResponse=function(e){n._processResponse(e)},!e.pathname&&e.path){var r=e.path.indexOf("?");r<0?e.pathname=e.path:(e.pathname=e.path.substring(0,r),e.search=e.path.substring(r))}this._performRequest()}function f(e){var t={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(e).forEach(function(i){var o=i+":",s=n[o]=e[i],c=t[i]=Object.create(s);c.request=function(e,i){return"string"==typeof e?(e=r.parse(e)).maxRedirects=t.maxRedirects:e=Object.assign({protocol:o,maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},e),e.nativeProtocols=n,a.equal(e.protocol,o,"protocol mismatch"),l("options",e),new d(e,i)},c.get=function(e,t){var n=c.request(e,t);return n.end(),n}}),t}["abort","aborted","error","socket","timeout"].forEach(function(e){u[e]=function(t){this._redirectable.emit(e,t)}}),d.prototype=Object.create(s.prototype),d.prototype.write=function(e,t,n){this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,n)):(this.emit("error",new Error("Request body larger than maxBodyLength limit")),this.abort())},d.prototype.end=function(e,t,n){var r=this._currentRequest;e?this.write(e,t,function(){r.end(null,null,n)}):r.end(null,null,n)},d.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)},d.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},["abort","flushHeaders","getHeader","setNoDelay","setSocketKeepAlive","setTimeout"].forEach(function(e){d.prototype[e]=function(t,n){return this._currentRequest[e](t,n)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(d.prototype,e,{get:function(){return this._currentRequest[e]}})}),d.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(this._options.agents){var n=e.substr(0,e.length-1);this._options.agent=this._options.agents[n]}var i=this._currentRequest=t.request(this._options,this._onNativeResponse);for(var o in this._currentUrl=r.format(this._options),i._redirectable=this,u)o&&i.on(o,u[o]);if(this._isRedirect){var a=this._requestBodyBuffers;!function e(){if(0!==a.length){var t=a.pop();i.write(t.data,t.encoding,e)}else i.end()}()}},d.prototype._processResponse=function(e){var t=e.headers.location;if(t&&!1!==this._options.followRedirects&&e.statusCode>=300&&e.statusCode<400){if(++this._redirectCount>this._options.maxRedirects)return void this.emit("error",new Error("Max redirects exceeded."));var n,i=this._options.headers;if(307!==e.statusCode&&!(this._options.method in c))for(n in this._options.method="GET",this._requestBodyBuffers=[],i)/^content-/i.test(n)&&delete i[n];if(!this._isRedirect)for(n in i)/^host$/i.test(n)&&delete i[n];var o=r.resolve(this._currentUrl,t);l("redirecting to",o),Object.assign(this._options,r.parse(o)),this._isRedirect=!0,this._performRequest()}else e.responseUrl=this._currentUrl,this.emit("response",e),this._requestBodyBuffers=[]},e.exports=f({http:i,https:o}),e.exports.wrap=f},function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(80),i=n(74);!function(e){e.getWindowPreference=function(e=!1){let t=i.default(),n={};return t&&t.colors&&"string"==typeof t.colors.colorPrimaryControl1&&(n.hoverBackgroundColor=e?parseInt(r.ColorUtilNS.rgbaStringToHexWith0xBegin(t.colors.colorPrimaryControl1),16):r.ColorUtilNS.rgbaStringToHexWith0xBegin(t.colors.colorPrimaryControl1)),n}}(t.WindowPreferenceNS||(t.WindowPreferenceNS={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(1),a=n(4),s=o.default.getLogger("GetSkinInfo");let l;(function(){return r(this,void 0,void 0,function*(){"renderer"===process.type?(s.information("renderer process"),a.client.callServerFunction("GetSkinInfo").then(e=>{l=e,s.information("send OnChangeSkin",e)}).catch(e=>{s.warning(e)}),a.client.attachServerEvent("OnChangeSkin",(e,t)=>{l=t,s.information("send OnChangeSkin",t)})):"browser"===process.type&&(s.information("main process"),i.ipcMain.on("OnChangeSkin",(e,t)=>{s.information("OnChangeSkin",t),l=t}))})})().catch(e=>{s.information(e)}),t.default=function(){return l}},function(e,t){e.exports=require("stream")},function(e,t,n){"use strict";n.r(t);var r=n(77),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5);let a=class extends o.Vue{onOptionsChanged(){return i(this,void 0,void 0,function*(){})}get filterOptions(){return this.options.filter(e=>"separator"!==e)}onSelect(e){this.$emit("input",e.srcElement.value,!0)}mounted(){let e=this.$refs.selectNative;if(e)for(let t in this.filterOptions)if(String(this.filterOptions[t])===this.value){e.selectedIndex=t;break}}focusEdit(){if(this.$refs.select){let e=this.$refs.select.$el.querySelector("input");null!==e&&void 0!==e&&e.focus()}}};r([o.Prop()],a.prototype,"value",void 0),r([o.Prop({default:()=>[]})],a.prototype,"options",void 0),r([o.Prop({default:!1})],a.prototype,"useNative",void 0),r([o.Prop({default:"bottom"})],a.prototype,"position",void 0),r([o.Watch("options")],a.prototype,"onOptionsChanged",null),a=r([o.Component],a),t.default=a},function(e,t,n){"use strict";n.r(t);var r=n(79),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(3),a=n(7),s=n(5),l=n(11),c=n(135),u=n(4);let d=class extends s.Vue{constructor(){super(...arguments),this.isMouseup=!0}created(){return i(this,void 0,void 0,function*(){let e=yield a.asyncRemoteCall.getCurrentWindow();(yield e.isVisible())?o.ipcRenderer.send("dropdown-file-window-create",e.id,this.panel):setTimeout(()=>{o.ipcRenderer.send("dropdown-file-window-create",e.id,this.panel)},20),o.ipcRenderer.on("dropdown-file-window-blur",()=>{this.isMouseup&&this.hideMenu()})})}handleMenuShow(){return i(this,void 0,void 0,function*(){l.XLStatNS.trackEvent("core_event","create_task_panel_downloadpath_show");let e=this.$el.getBoundingClientRect(),t=yield a.asyncRemoteCall.getCurrentWindow(),n=[];if("cloud"===this.dropOwner){let e=(yield u.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcGetRecentFolder"))[0];if(e){for(let t of e)""!==t.id&&n.push({dir:t.name,alias:t.name,canDelete:!0,id:t.id});n=n.slice(0,4)}n.splice(0,0,{dir:"我的云盘",alias:"我的云盘",canDelete:!1,id:""})}else n=yield c.HistoryPathsNS.getLogicHistoryPaths(this.enablePrivateSpace,this.enableDelete,this.appendDirs);o.ipcRenderer.send("dropdown-file-window-show",t.id,!0,JSON.stringify({bottom:e.bottom,height:e.height,left:e.left,right:e.right,top:e.top,width:e.width}),n,this.dropOwner,this.enableDelete)})}hideMenu(){this.$refs.select&&(this.$refs.select.menuVisible=!1)}focusEdit(){if(this.$refs.select){let e=this.$refs.select.$el.querySelector("input");null!==e&&void 0!==e&&e.focus()}}};r([s.Prop()],d.prototype,"value",void 0),r([s.Prop()],d.prototype,"panel",void 0),r([s.Prop()],d.prototype,"dropOwner",void 0),r([s.Prop()],d.prototype,"checked",void 0),r([s.Prop({default:()=>[]})],d.prototype,"appendDirs",void 0),r([s.Prop({default:!0})],d.prototype,"enablePrivateSpace",void 0),r([s.Prop({default:!0})],d.prototype,"enableDelete",void 0),d=r([s.Component],d),t.default=d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){function t(e){let t=e.toString(16).toUpperCase();return t.length<2&&(t="0"+t),t}function n(e,n,r,i){return"0x"+t(i)+t(e)+t(n)+t(r)}e.rgbaStringToHexWith0xBegin=function(e){if(void 0!==e){let t=e.split(",");return n(parseInt(t[0]||"0",10),parseInt(t[1]||"0",10),parseInt(t[2]||"0",10),parseInt(t[3]||"255",10))}},e.colorNumberToHex=t,e.rgbaToHexWith0xBegin=n}(t.ColorUtilNS||(t.ColorUtilNS={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(39),o=n(21),a=n(18),s=n(1).default.getLogger("Thunder.base.tools-utilities"),l=n(2),c=n(8).default(l.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(e){return r(this,void 0,void 0,function*(){let t;return t=e&&(yield a.FileSystemAWNS.existsAW(e))?new Promise(t=>{c.asyncCalcuteFileMD5(e,(e,n)=>{e?(n=n.toUpperCase(),t(n)):t(void 0)})}):new Promise(e=>{e(void 0)})})}e.genarateMd5=function(e){let t=void 0,n=i.createHash("md5");return null!==n&&(t=n.update(e).digest("hex")),t},e.matchFileMd5=function(e,n){return r(this,void 0,void 0,function*(){let r=!1,i=yield t(e);return void 0!==i&&i===n.toUpperCase()&&(s.information("check full md5 sucessful"),r=!0),r})},e.calculateFileMd5Ex=function(e){return r(this,void 0,void 0,function*(){let t;if(e&&(yield a.FileSystemAWNS.existsAW(e))){let n=o.createReadStream(e),r=i.createHash("md5");n.on("data",e=>{r.update(e)}),t=new Promise(e=>{n.on("end",()=>{let t=r.digest("hex");t=t.toUpperCase(),e(t)})})}else t=new Promise(e=>{e(void 0)});return t})},e.calculateFileMd5=t,e.encryptBuffer=function(e,t){let n=null;try{let r=i.createCipheriv("aes-128-ecb",t,""),o=r.update(e),a=r.final();n=Buffer.concat([o,a])}catch(e){s.warning("encryptBuffer",e)}return n},e.decryptBuffer=function(e,t){let n=null;try{let r=i.createDecipheriv("aes-128-ecb",t,""),o=r.update(e),a=r.final();n=Buffer.concat([o,a])}catch(e){s.warning("decryptBuffer",e)}return n},e.encryptSha1Buffer=function(e){let t=null;try{t=i.createHash("sha1").update(e).digest("hex")}catch(e){s.warning("encryptSha1Buffer",e)}return t},e.encryptHmacBuffer=function(e,t,n,r="hex"){let o=null;try{o=i.createHmac(e,t).update(n,"utf8").digest(r)}catch(e){s.warning("encryptSha1Buffer",e)}return o},e.setForegroundWindow=function(e,t){return r(this,void 0,void 0,function*(){if(null!==e&&null!==t){let n=void 0,i=[];"renderer"===process.type?(n=yield t.getNativeWindowHandle(),i=yield t.getChildWindows()):(n=t.getNativeWindowHandle(),i=t.getChildWindows());let o=[];for(let e=0;e<i.length;++e){let t=i[e],n=!1;"renderer"===process.type?(yield t.isDestroyed())||(n=yield t.isAlwaysOnTop()):t.isDestroyed()||(n=t.isAlwaysOnTop()),n&&o.push(t)}let a=n.readUIntLE(0,4);e.setForegroundWindow(a)?s.information("setForegroundWindow sucessful"):s.information("setForegroundWindow failed"),o.forEach(e=>r(this,void 0,void 0,function*(){"renderer"===process.type?(yield e.isDestroyed())||e.setAlwaysOnTop(!0).catch():e.isDestroyed()||e.setAlwaysOnTop(!0)}))}})}}(t.ToolsUtilitiesAWNS||(t.ToolsUtilitiesAWNS={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(7),a=n(20),s=n(4),l=n(28);!function(e){e.resizeToFitContent=function(e=0,t=0,n){return r(this,void 0,void 0,function*(){let r=yield o.asyncRemoteCall.getCurrentWindow();if(e>0&&t>0)window.resizeTo(e,t);else{let e=document.querySelector(".td-dialog");null===e&&(e=document.querySelector(".msg-pop-message")),null!==e&&window.resizeTo(e.offsetWidth,e.offsetHeight)}n&&"function"==typeof n&&(yield n()),window.requestIdleCallback(()=>{setTimeout(()=>{r.show()},50)})})},e.autoAdaptScrollIntoView=function(e,t,n){let r=t.scrollTop,i=t.getBoundingClientRect(),o=e.getBoundingClientRect(),a=i.bottom;void 0!==n&&"number"==typeof n&&n>0&&(a=i.top+n),o.top<i.top?t.scrollTop=r-(i.top-o.top):o.bottom>a&&(t.scrollTop=r+(o.bottom-a))},e.fitWindowPosInParent=function(e,t){return r(this,void 0,void 0,function*(){if(e||(e=yield o.asyncRemoteCall.getCurrentWindow()),t||(t=yield e.getParentWindow()),!t)return;let n=(yield e.getNativeWindowHandle()).readUIntLE(0,4),r=(yield t.getNativeWindowHandle()).readUIntLE(0,4);yield s.client.callRemoteClientFunction(l.CommonIPCBase.mainProcessContext,"SetPosition",n,r)})},e.getWindowsInParentCenterPos=function(e,t,n){return r(this,void 0,void 0,function*(){n||"browser"===process.type||(n=yield o.asyncRemoteCall.getCurrentWindow());let r="browser"!==process.type?yield n.getPosition():n.getPosition(),a="browser"!==process.type?yield n.getSize():n.getSize(),s="browser"!==process.type?yield o.asyncRemoteCall.getScreen():i.screen,l=yield s.getCursorScreenPoint(),c=yield s.getDisplayNearestPoint(l),u=c.size.width,d=c.size.height,f=a[0],h=a[1];r[0]+f>u&&(f=u-r[0]),r[1]+h>d&&(h=d-r[1]);let p=r[0]+(f-e)/2,g=r[1]+(h-t)/2;return p<0?p=0:p>u-e&&(p=u-e),g<0?g=0:g>d-t&&(g=d-t),[Math.round(p),Math.round(g)]})},e.centerWnd=function(e,t,n){do{if("browser"!==process.type)break;if(!e||!t)break;let r=e.getNativeWindowHandle().readUIntLE(0,4);if(!r)break;let o=t.getPosition(),a=t.getSize(),s=e.getSize(),l=i.screen.getCursorScreenPoint(),c=i.screen.getDisplayNearestPoint(l),u=c.scaleFactor,d=c.size.width,f=c.size.height,h=a[0],p=a[1];o[0]+h>d&&(h=d-o[0]),o[1]+p>f&&(p=f-o[1]);let g=o[0]+(h-s[0])/2,m=o[1]+(p-s[1])/2;g<0?g=0:g>d-s[0]&&(g=d-s[0]),m<0?m=0:m>f-s[1]&&(m=f-s[1]),n.setWindowPos(r,0,g*u,m*u,0,0,5)}while(0)},e.bringWindowToTop=function(e){return r(this,void 0,void 0,function*(){if(!e){let t=yield(yield o.asyncRemoteCall.getCurrentWindow()).getNativeWindowHandle();e=t.readUIntLE(0,4)}i.ipcRenderer.send(a.ThunderChannelList.channelMRBringWindowToTop,e)})}}(t.ThunderWindowNS||(t.ThunderWindowNS={}))},function(e,t){e.exports=require("assert")},function(e,t,n){e.exports=n(9)(215)},function(e,t,n){e.exports=n(9)(194)},function(e,t){!function(e){var t,n='<svg><symbol id="td-icon-svg-file" viewBox="0 0 1204 1024"><path d="M180.705882 1024c-102.4 0-180.705882-78.305882-180.705882-180.705882V180.705882c0-102.4 78.305882-180.705882 180.705882-180.705882h240.941177c102.4 0 180.705882 78.305882 180.705882 180.705882h421.647059c102.4 0 180.705882 78.305882 180.705882 180.705883v481.882353c0 102.4-78.305882 180.705882-180.705882 180.705882H180.705882z" fill="#FFC25A" ></path><path d="M301.176471 361.411765h602.352941c66.258824 0 120.470588 54.211765 120.470588 120.470588v361.411765c0 66.258824-54.211765 120.470588-120.470588 120.470588H301.176471c-66.258824 0-120.470588-54.211765-120.470589-120.470588V481.882353c0-66.258824 54.211765-120.470588 120.470589-120.470588z" fill="#FFFFFF" ></path><path d="M180.705882 542.117647h843.294118c102.4 0 180.705882 78.305882 180.705882 180.705882v120.470589c0 102.4-78.305882 180.705882-180.705882 180.705882H180.705882c-102.4 0-180.705882-78.305882-180.705882-180.705882v-120.470589c0-102.4 78.305882-180.705882 180.705882-180.705882z" fill="#FFD68F" ></path></symbol></svg>',r=function(e,t){t.firstChild?function(e,t){t.parentNode.insertBefore(e,t)}(e,t.firstChild):t.appendChild(e)};if((t=document.getElementsByTagName("script"))[t.length-1].getAttribute("data-injectcss")&&!e.__iconfont__svg__cssinject__){e.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}!function(t){if(document.addEventListener)if(~["complete","loaded","interactive"].indexOf(document.readyState))setTimeout(t,0);else{var n=function(){document.removeEventListener("DOMContentLoaded",n,!1),t()};document.addEventListener("DOMContentLoaded",n,!1)}else document.attachEvent&&function(e,t){var n=e.document,r=!1,i=function(){r||(r=!0,t())},o=function(){try{n.documentElement.doScroll("left")}catch(e){return void setTimeout(o,50)}i()};o(),n.onreadystatechange=function(){"complete"==n.readyState&&(n.onreadystatechange=null,i())}}(e,t)}(function(){var e,t;(e=document.createElement("div")).innerHTML=n,n=null,(t=e.getElementsByTagName("svg")[0])&&(t.setAttribute("aria-hidden","true"),t.style.position="absolute",t.style.width=0,t.style.height=0,t.style.overflow="hidden",r(t,document.body))})}(window)},function(e,t){var n,r=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];t.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return 4*e+17},t.getSymbolTotalCodewords=function(e){return r[e]},t.getBCHDigit=function(e){for(var t=0;0!==e;)t++,e>>>=1;return t},t.setToSJISFunction=function(e){if("function"!=typeof e)throw new Error('"toSJISFunc" is not a valid function.');n=e},t.isKanjiModeEnabled=function(){return void 0!==n},t.toSJIS=function(e){return n(e)}},function(e,t,n){var r=n(212),i=n(213);t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(e,t){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!r.isValid(t))throw new Error("Invalid version: "+t);return t>=1&&t<10?e.ccBits[0]:t<27?e.ccBits[1]:e.ccBits[2]},t.getBestModeForData=function(e){return i.testNumeric(e)?t.NUMERIC:i.testAlphanumeric(e)?t.ALPHANUMERIC:i.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},t.isValid=function(e){return e&&e.bit&&e.ccBits},t.from=function(e,n){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+e)}}(e)}catch(e){return n}}},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.useNative?n("select",{ref:"selectNative",staticClass:"xly-select",on:{change:e.onSelect}},e._l(e.filterOptions,function(t){return n("option",{key:t,domProps:{value:t}},[e._v(e._s(t))])})):n("td-select",e._g(e._b({ref:"select",attrs:{value:e.value,options:e.filterOptions,position:e.position}},"td-select",e.$attrs,!1),e.$listeners),[e._t("suffix",null,{slot:"suffix"}),e._v(" "),e._t("append",null,{slot:"append"})],2)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement;return(e._self._c||t)("td-select",e._g(e._b({ref:"select",class:{"is-checked":e.checked},attrs:{value:e.value,"custom-menu-enabled":""},on:{"menu-show":e.handleMenuShow},nativeOn:{mousedown:function(t){e.isMouseup=!1},mouseup:function(t){e.isMouseup=!0}}},"td-select",e.$attrs,!1),e.$listeners),[e._t("prefix",null,{slot:"prefix"}),e._v(" "),e._t("suffix",null,{slot:"suffix"}),e._v(" "),e._t("append",null,{slot:"append"})],2)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){function t(e){const t=new Date(+e);return"Invalid Date"===t.toString()?"":`${t.getFullYear()}-${i(t.getMonth()+1,2)}-${i(t.getDate(),2)} ${i(t.getHours(),2)}:${i(t.getMinutes(),2)}`}function n(e){let t=new Date(+e);return"Invalid Date"===t.toString()?"":i(t.getHours(),2)+":"+i(t.getMinutes(),2)}function r(e,t){return((e=new Date(e).setHours(0,0,0,0))-(t=new Date(t).setHours(0,0,0,0)))/1e3/60/60/24}function i(e,t){let n=e.toString().length,r=e.toString();for(;n<t;)r="0"+r,n++;return r}e.formatDate=function(e,t){/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length)));let n={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds()};for(let e in n){let r=n[e]+"";new RegExp(`(${e})`).test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?r:("00"+(r=r)).substr(r.length)))}var r;if(/(f+)/.test(t)){let n=e.getMilliseconds()+"";t=t.replace(RegExp.$1,("000"+n).substr(n.length))}return t},e.getPubTime=function(e){const o=new Date(+e),a=(new Date).getTime(),s=a-1*e,l=r(a,o.getTime());let c="";if(s<36e5){let e=Math.floor(s/6e4);c=e>=1?e+"分钟前":"刚刚"}else c=s<864e5?Math.floor(s/36e5)+"小时前":1===l?"昨天"+n(e):2===l?"前天"+n(e):o.getFullYear()===(new Date).getFullYear()?`${i(o.getMonth()+1,2)}-${i(o.getDate(),2)} ${i(o.getHours(),2)}:${i(o.getMinutes(),2)}`:t(e);return c},e.createTimeFormat=t,e.timeFormat=n,e.dateDiff=r,e.pad=i,e.formatSeconds=function(e){let t="";do{if(e<=0){t="00:00:00";break}let n=Math.floor(e/3600),r=Math.floor(e/60)%60,i=Math.floor(e%60);t=n<10?"0"+n+":":n+":",t+=r<10?"0"+r+":":r+":",t+=i<10?"0"+i:""+i}while(0);return t},e.formatSecondsCustom=function(e){let t="";do{if(e<=0){t="00:00:00";break}let n=Math.floor(e/3600),r=Math.floor(e/60)%60,i=Math.floor(e%60);if(n<=99)t=n<10?"0"+n+":":n+":",t+=r<10?"0"+r+":":r+":",t+=i<10?"0"+i:""+i;else{let n=Math.floor(e/86400);t=`剩${n=n>999?999:n}天`}}while(0);return t},e.convertTimeToMinutes=function(e,t,n){return 3600*e+60*t+n},e.convertMinuteToTime=function(e){return[Math.floor(e/3600),Math.floor(e/60%60),Math.floor(e%60)]}}(t.TimeHelperNS||(t.TimeHelperNS={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(15),a=n(25),s=n(1);let l=null;const c=n(4),u=s.default.getLogger("url.helper");!function(e){let t=!1,s=["txt","url","nfo","lnk"];function d(e){let t=!0;if("string"==typeof e)if(40===e.length||32===e.length){for(let n=0;n<e.length;n++)if(e.charCodeAt(n)>127){t=!1;break}}else t=!1;else t=!1;return t}function f(e,t=!0){t=void 0===t||t;let n=!1;do{if("string"!=typeof e||""===e)break;let r=e.trim();if(""===r)break;if(a.ThunderHelper.getTaskTypeFromUrl(r)===o.DownloadKernel.TaskType.Unkown){if(!t)break;d(r)&&(n=!0)}else n=!0;n||r.match(/^file:\/\/\//)&&(n=!0)}while(0);return n}e.isMagnetCode=d,e.fixMagnetUrl=function(e){let t="";do{if("string"!=typeof e||""===e)break;if(""===(t=e.trim()))break;a.ThunderHelper.getTaskTypeFromUrl(t)===o.DownloadKernel.TaskType.Unkown&&d(t)&&(t="magnet:?xt=urn:btih:"+t)}while(0);return t},e.isUrlValid=f,e.isBirdKey=function(e){return e?e.match(/#[Xx]([0-9a-zA-Z])+#/g):null},e.isYunFetchBackTask=function(e){return e?e.match(/#\$ThunderPanFetchBack:([\s\S]+)\$#/):null},e.isCustomProtocol=function(e){return r(this,void 0,void 0,function*(){let t=!1;null===l&&(l=yield Promise.resolve().then(()=>n(34)));let r=l.parse(e);do{if(null===r||void 0===r)break;let e=r.protocol;if("magnet:"===e){t=!0;break}if("thunder:"===e){t=!0;break}if("ed2k:"===e){t=!0;break}}while(0);return t})},e.isP2spOrEmuleUrl=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=e.trim();if(""===n)break;let r=a.ThunderHelper.getTaskTypeFromUrl(n);if(r===o.DownloadKernel.TaskType.P2sp||r===o.DownloadKernel.TaskType.Emule){t=!0;break}}while(0);return t},e.isSupportUrl=function(e){e=e.toLowerCase();let t=!1;do{if("http://"===e.substr(0,"http://".length)){t=!0;break}if("https://"===e.substr(0,"https://".length)){t=!0;break}if("ftp://"===e.substr(0,"ftp://".length)){t=!0;break}if("ed2k://"===e.substr(0,"ed2k://".length)){t=!0;break}if("thunder://"===e.substr(0,"thunder://".length)){t=!0;break}if("magnet:?"===e.substr(0,"magnet:?".length)){t=!0;break}}while(0);return t};let h=".edu;.gov;.mil;.hk;.tw;.com.tw;.com.tw;.aero;.biz;.coop;.info;.museum;.name;.pro;";h+=".com;.cn;.xin;.net;.top;.xyz;.wang;.shop;.site;.club;.cc;.fun;.online;.biz;.red;.link;.ltd;.mobi;.info;.org;",h+=".com.cn;.net.cn;.org.cn;.gov.cn;.name;.vip;.work;.tv;.co;.kim;.group;.tech;.store;.ren;.ink;.pub;.live;.wiki;.design;";const p=(h+=".me").split(";");function g(e){let t=!1;do{if(void 0===e||null===e||""===e)break;if(-1!==p.indexOf(e)){t=!0;break}}while(0);return t}function m(e){let t=null;do{if(void 0===e||null===e)break;if(""===(e=e.trim()))break;let n=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(e);if(null===n)break;u.information("url parse result ",n),t={};let r=["url","scheme","slash","host","port","path","query","hash"];for(let e=0;e<r.length;e++)t[r[e]]=n[e];u.information("url parse",t)}while(0);return t}e.isUsualDomainSuffix=g,e.urlParse=m,e.isUrl=function(e){let t=!1,n=e;do{if(void 0===e||null===e)break;if(f(e,!1)){t=!0;break}if(e.trim().match(/^file:\/\/\//)){t=!0;break}let r=m(e);if(null===r){u.information("url parse failed");break}if(void 0===r.host||null===r.host)break;if(void 0!==r.scheme){t=!0;break}let o=r.host.match(/^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/);if(o){if(u.information("url parse is ip",o),Number(o[1])<=0||Number(o[1])>255){t=!1;break}if(Number(o[2])<0||Number(o[2])>255){t=!1;break}if(Number(o[4])<0||Number(o[4])>255){t=!1;break}if(Number(o[4])<0||Number(o[4])>255){t=!1;break}t=!0,void 0===r.scheme&&void 0!==r.port&&(n=`http://${e}`);break}if(r.host.match(/\.{2,}/)){u.information("url parse has multi dot"),t=!1;break}let a=i.extname(r.host);if(""===a)break;if(g(a)){t=!0;break}}while(0);return u.information("url parse isUrl",t),{ret:t,suggest:n}},e.isFileNameValid=function(e){let t=!1;do{if(void 0===e)break;if(null===e)break;if(""===(e=e.trim()))break;if("."===e[0])break;if(e.match(/[\/\\"<>\?\*|]/))break;t=!0}while(0);return t},e.initBlackList=function(){return r(this,void 0,void 0,function*(){if(!t){let e=yield c.client.callServerFunction("GetConfigModules","SuffixBlackList",s);e&&(s=e,t=!0)}})},e.isSuffixNeedDownload=function(e){let t=!0;do{if(void 0===e)break;if(""===e||"."===e)break;if(e=e.toLowerCase(),-1!==s.indexOf(e)){t=!1;break}}while(0);return t}}(t.URLHelperNS||(t.URLHelperNS={}))},function(e,t,n){"use strict";n.r(t);var r=n(94),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(t,"__esModule",{value:!0});const i=n(5),o=n(186),a=n(4);let s=class extends i.Vue{get menu(){let e="立即下载";do{if(this.buttonTipText&&""!==this.buttonTipText){e=this.buttonTipText;break}}while(0);return e}handleClick(e){this.$emit("download",e),a.client.callServerFunction("SetPerformanceItemStart","ClickDownload")}};r([i.Prop({})],s.prototype,"dropEnable",void 0),r([i.Prop({})],s.prototype,"mode",void 0),r([i.Prop({})],s.prototype,"disabled",void 0),r([i.Prop({default:""})],s.prototype,"buttonTipText",void 0),s=r([i.Component({components:{DropdownNative:o.default}})],s),t.default=s},function(e,t,n){"use strict";n.r(t);var r=n(96),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(42),a=n(7),s=n(5);let l=class extends s.Vue{onMenusChanged(){return i(this,void 0,void 0,function*(){yield this.appendContent()})}appendContent(){return i(this,void 0,void 0,function*(){if(!this.dropdown){let e=yield o.electron.DropdownWindow.__resolve();this.dropdown=yield new e}yield this.dropdown.clear();let e=yield o.electron.DropdownWindowContent.__resolve();for(let t of this.menus)if("separator"===t){let t=yield new e({type:"separator"});yield this.dropdown.append(t)}else{let n=yield new e({label:t.toString(),click:()=>{this.$emit("input",t,!0)}});yield this.dropdown.append(n)}})}handleDropClick(){return i(this,void 0,void 0,function*(){this.dropdown||(yield this.appendContent());let e=this.$el.getBoundingClientRect();const{DropDownWindowSkinNS:t}=yield Promise.resolve().then(()=>n(187));let r=Math.round(((e.width>800?800:e.width)-100)/2);t.setStyle(this.dropdown,{windowPreference:{marginLeft:r,marginRight:0,fontHeight:-16,cornerRadius:3,stringWidth:Math.round(e.width)-39-r}});let i=yield a.asyncRemoteCall.getCurrentWindow();this.dropdown.popup({window:i,x:Math.round(e.left),y:Math.round(e.top+e.height+4)})})}};r([s.Prop()],l.prototype,"menus",void 0),r([s.Watch("menus")],l.prototype,"onMenusChanged",null),l=r([s.Component],l),t.default=l},function(e,t,n){"use strict";n.r(t);var r=n(98),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5),a=n(162),s=n(190),l=n(11),c=n(16),u=n(4);let d=class extends o.Vue{constructor(){super(...arguments),this.lackSpaceTip=!1,this.lackSpaceShowed=!1}onCloudChoosedChange(){this.cloudChoosed||(this.lackSpaceTip=!1)}get title(){let e="下载到：";do{if(this.logicChoosed)break;this.cloudChoosed&&(e="添加到：")}while(0);return e}handleSpaceDanger(e){return i(this,void 0,void 0,function*(){let t=this.logicChoosed,n=this.cloudChoosed;if(e){if(!this.lackSpaceShowed&&(this.lackSpaceShowed=!0,window.__xadsConfigCloudeChoosedGet||(t=yield u.client.callServerFunction("GetConfigValue","TaskDefaultSettings","LogicChoosed",this.logicChoosed),n=yield u.client.callServerFunction("SetConfigValue","TaskDefaultSettings","CloudChoosed",this.cloudChoosed)),t&&!n&&(this.$emit("check-change",!0,"cloud"),!this.lackSpaceTip))){this.lackSpaceTip=!0;let e=yield u.client.callServerFunction("IsLogined");l.XLStatNS.trackEvent("core_event","lack_space_bubble_show","",0,0,0,0,`is_login=${e?1:0}`)}}else this.lackSpaceTip=!1})}handleChangePath(e,t){this.$emit("logic-input",e,t)}handleChangeCloudPath(e,t){t&&!this.cloudChoosed&&e&&e.name&&this.$emit("check-change",!0,"cloud"),this.$emit("cloud-input",e,t)}verify(){return i(this,void 0,void 0,function*(){let e=!0;do{if(this.logicChoosed&&(e=yield this.$refs.logicPathSelector.verify()),!e)break;this.cloudChoosed&&(e=yield this.$refs.cloudPathSelector.verify())}while(0);if(e){"yunpan"!==c.ThunderUtil.getQueryString(location.href,"from")&&(yield u.client.callServerFunction("SetConfigValue","TaskDefaultSettings","LogicChoosed",this.logicChoosed),yield u.client.callServerFunction("SetConfigValue","TaskDefaultSettings","CloudChoosed",this.cloudChoosed))}return e})}};r([o.Prop({})],d.prototype,"logicChoosed",void 0),r([o.Prop({})],d.prototype,"cloudChoosed",void 0),r([o.Prop({})],d.prototype,"panel",void 0),r([o.Prop({})],d.prototype,"selectSize",void 0),r([o.Prop({})],d.prototype,"selectCount",void 0),r([o.Prop({})],d.prototype,"savePath",void 0),r([o.Prop({})],d.prototype,"cloudPath",void 0),r([o.Watch("cloudChoosed")],d.prototype,"onCloudChoosedChange",null),d=r([o.Component({components:{PathSelectorWindow:a.default,CloudPathSelector:s.default}})],d),t.default=d},function(e,t,n){"use strict";n.r(t);var r=n(100),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(3),a=n(7),s=n(5),l=n(4),c=n(152),u=n(25),d=n(16),f=n(18),h=n(1),p=n(153),g=h.default.getLogger("ThunderNewTask.path-selector");let m=class extends s.Vue{constructor(){super(...arguments),this.timerId=-1,this.freeSpace=0,this.totalSpace=0,this.driverDescription="",this.diskIcon="",this.tooltipVisible=!1,this.tooltipTimerId=void 0,this.valid=!0,this.showPathTips=!1,this.pathTips="",this.isDanger=!1,this.isWarn=!1}onPathChange(e,t){return i(this,void 0,void 0,function*(){do{if(!e)break;let n=e;if("私人空间"===e){let e=yield l.client.callServerFunction("GetUserID");if(null===e||void 0===e||""===e)break;n=yield l.client.callServerFunction("GetPrivateSavePath",e)}if(this.totalSpace=u.ThunderHelper.getPartitionSpace(n),this.freeSpace=u.ThunderHelper.getFreePartitionSpace(n),void 0===t)break;if(!u.ThunderHelper.isWindows10())break;let r=u.ThunderHelper.getDriveType(n);if(!r)break;if(r===u.ThunderHelper.DriverType.DRIVE_REMOTE){this.driverDescription="共享目录",this.diskIcon="xlx-icon-file-share";break}if(r===u.ThunderHelper.DriverType.DRIVE_REMOVABLE){this.driverDescription="U盘",this.diskIcon="xlx-icon-u-disk";break}if(r===u.ThunderHelper.DriverType.DRIVE_FIXED){g.information("getDriveMediaType start");let e=yield u.ThunderHelper.getFixedDriveMediaType(n);e===u.ThunderHelper.MediaType.HDD?(this.driverDescription="机械硬盘",this.diskIcon="xlx-icon-hdd"):e===u.ThunderHelper.MediaType.SSD?(this.driverDescription="固态硬盘",this.diskIcon="xlx-icon-ssd"):(this.driverDescription="",this.diskIcon=""),g.information("getDriveMediaType end",e,this.driverDescription)}}while(0)})}onDanger(e){this.$emit("danger",e)}created(){return i(this,void 0,void 0,function*(){this.timerId=setInterval(()=>i(this,void 0,void 0,function*(){void 0!==this.value&&null!==this.value&&("私人空间"===this.value?this.valid=!0:this.value.length>=3?this.valid=d.ThunderUtil.isValidPath(this.value):this.valid=!1,this.valid&&this.onPathChange(this.value,void 0))}),1e3),yield p.default(),l.client.callServerFunction("GetPrivateSpaceInfo").then(e=>i(this,void 0,void 0,function*(){if(e&&-1!==e.currentCategoryId)this.$emit("logic-input","私人空间");else{let e=yield l.client.callServerFunction("GetDownloadDir");g.information(e),this.$emit("logic-input",e)}}))})}get spaceColor(){let e="";return this.isDanger?e="is-danger":this.isWarn&&(e="is-warn"),e}get freeSpaceFormat(){let e="";do{if("string"!=typeof this.value)break;if(-1===this.freeSpace)break;if(""===this.value){this.isDanger=!1;break}if(this.value.length<3){this.isDanger=!1;break}if(!this.valid){this.isDanger=!1;break}if(this.isDanger=!1,this.isWarn=!1,this.needSpace>=this.freeSpace){this.isDanger=!0,e="磁盘空间不足";break}this.freeSpace/this.totalSpace<=.3&&(this.isWarn=!0),e="剩余:"+d.ThunderUtil.formatSize(this.freeSpace)}while(0);return e}handleInput(e){this.$emit("logic-input",e,!0)}handleContextMenu(){return i(this,void 0,void 0,function*(){const{MenuSkinNS:e}=yield Promise.resolve().then(()=>n(63));e.popEditableDefaultContextMenu()})}handleChangePath(){return i(this,void 0,void 0,function*(){const e=yield a.asyncRemoteCall.getDialog();let t=yield a.asyncRemoteCall.getCurrentWindow();t&&!(yield t.isDestroyed())&&e.showOpenDialog(t,{defaultPath:this.value,properties:["openDirectory"]}).then(e=>{e.canceled||this.$emit("logic-input",e.filePaths[0],!0),this.$refs.root.focusEdit()})})}handleCheck(e){this.$emit("check-change",e)}mounted(){return i(this,void 0,void 0,function*(){if(o.ipcRenderer.on("dropdown-file-window-select",(e,t,n,r,i)=>{g.information("dropdown select",t,r),"logic"===i&&this.$emit("logic-input",t,r)}),u.ThunderHelper.isWindows10()){let e="path_selector_disk_tooltip_show",t=localStorage.getItem(e);"1"!==t&&null!==t||(this.tooltipVisible=!0,localStorage.setItem(e,"0"),this.tooltipTimerId=setTimeout(()=>{this.tooltipVisible=!1},5e3))}document.addEventListener("click",()=>{this.showPathTips=!1}),(yield a.asyncRemoteCall.getCurrentWindow()).hookWindowMessage(528,()=>{this.showPathTips=!1})})}verify(){return i(this,void 0,void 0,function*(){let e=!1;do{if(!this.value){e=!0;break}if(this.value.length<3){this.pathTips="不合法的存储路径",this.showPathTips=!0;break}if("私人空间"===this.value){e=!0;break}if(u.ThunderHelper.getDriveType(this.value)===u.ThunderHelper.DriverType.DRIVE_REMOTE){e=!0;break}if(!d.ThunderUtil.isValidPath(this.value)){this.pathTips="不合法的存储路径",this.showPathTips=!0;break}if(!(yield f.FileSystemAWNS.dirExistsAW(this.value))){if(!(yield f.FileSystemAWNS.mkdirsAW(this.value))){this.pathTips="不合法的存储路径",this.showPathTips=!0;break}}e=!0}while(0);return e})}destroyed(){-1!==this.timerId&&(clearTimeout(this.timerId),this.timerId=-1),this.tooltipTimerId&&(clearTimeout(this.tooltipTimerId),this.tooltipTimerId=void 0)}};r([s.Prop({})],m.prototype,"choosed",void 0),r([s.Prop({})],m.prototype,"panel",void 0),r([s.Prop({default:""})],m.prototype,"value",void 0),r([s.Prop({default:0})],m.prototype,"needSpace",void 0),r([s.Watch("value",{immediate:!0})],m.prototype,"onPathChange",null),r([s.Watch("isDanger",{immediate:!0})],m.prototype,"onDanger",null),m=r([s.Component({components:{SelectWindow:c.default}})],m),t.default=m},function(e,t,n){"use strict";n.r(t);var r=n(102),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(3),a=n(5),s=n(11),l=n(16),c=n(91),u=n(153),d=n(7),f=n(152),h=n(4),p=n(1).default.getLogger("ThunderNewTask.path-selector");let g=class extends a.Vue{constructor(){super(...arguments),this.guideOverDays=!0,this.guideConfig={date:"",days:0,type:"a",vType:"b",vCount:0},this.isDanger=!1,this.isWarn=!1,this.isLogined=!1,this.freeSpace=0,this.showPathTips=!1,this.pathTips="",this.cacheData=null}onPathChange(e,t){}onDanger(e){this.$emit("danger",e)}onLoginChange(e){this.$nextTick(()=>i(this,void 0,void 0,function*(){if(e){let e=[[{name:"我的云盘",id:""}],void 0];if("yunpan"===l.ThunderUtil.getQueryString(location.href,"from")&&(e=yield h.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcGetCurrentRoute")),e&&e[0]&&e[0].length>1){let t=e[0],n=t.map(e=>e.name),r=t.map(e=>e.id),i={name:n.join("\\"),id:r.join("\\")};this.$emit("cloud-input",i)}else{let e=yield h.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcDefaultSavePath");if(e&&e[0]){let t=e[0];this.$emit("cloud-input",t)}else this.$emit("cloud-input",{name:"我的云盘",id:""})}let t=(yield h.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcGetDriveInfo"))[0];t&&t.limit&&t.usage&&(this.freeSpace=t.limit-t.usage)}}))}get guideShow(){let e=!1;do{if(this.guideOverDays)break;if(this.isLogined&&this.choosed)break;e=!0}while(0);return e}get componentName(){let e="";switch(this.panel){case"PreNewTaskDlg":case"NewTaskDlg":case"BtTaskDlg":e=`${this.panel}CloudGuideContainer`}return e}get spaceColor(){let e="";return this.isDanger?e="is-danger":this.isWarn&&(e="is-warn"),e}get freeSpaceFormat(){let e="";return this.freeSpace&&(e="剩余:"+l.ThunderUtil.formatSize(this.freeSpace)),p.information("freeSpaceFormat",e),e}handleInput(e){this.$emit("input",e,!0)}handleChangePath(){return i(this,void 0,void 0,function*(){let e=yield d.asyncRemoteCall.getCurrentWindow(),t=yield h.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcTreePath",{pid:e.id});if((yield e.isAlwaysOnTop())||(yield e.setAlwaysOnTop(!0),yield e.show(!0),setTimeout(()=>i(this,void 0,void 0,function*(){(yield e.isDestroyed())||(yield e.setAlwaysOnTop(!1))}),60)),t&&t[0]){let e=t[0];if(e.error)p.information("IpcTreePath error",e.error);else{let t=void 0;do{if(0===e.path.length)break;t={name:e.path.map(e=>e.title).join("\\"),id:e.path.map(e=>e.id).join("\\")}}while(0);t&&this.$emit("cloud-input",t,!0)}}})}handleCheck(e){this.$emit("check-change",e)}handleGuideClick(){s.XLStatNS.trackEvent("core_event","create_task_panel_yunpan_guid_click","",0,0,0,0,`is_login=${this.isLogined?1:0}`),this.handleCheck(!0)}handleUpdateConfig(e){return i(this,void 0,void 0,function*(){do{if(!e)break;if(this.cacheData=e,!window.__xadsConfigCloudeChoosedGet){yield u.default(),yield h.client.callServerFunction("IsConfigInitFinish");let t=yield h.client.callServerFunction("GetConfigValue","TaskDefaultSettings","CloudChoosed",!0);if((yield h.client.callServerFunction("IsLogined"))&&t)break;if(e!==this.cacheData){p.information("handleUpdateConfig await change break",e,this.cacheData);break}}this.guideShow&&s.XLStatNS.trackEvent("core_event","create_task_panel_yunpan_guid_show","",0,0,0,0,`is_login=${this.isLogined?1:0}`),this.guideConfig=Object.assign({},this.guideConfig,e),localStorage.setItem("newtask_cloudguide",JSON.stringify(this.guideConfig))}while(0)})}initCloudGuideConfig(){do{let e=localStorage.getItem("newtask_cloudguide"),t=null;if(e)try{t=JSON.parse(e)}catch(e){t=null}if(t){if(this.guideConfig.date=t.date||"",t.days&&!isNaN(t.days)&&(this.guideConfig.days=Number(t.days)),this.guideConfig.days>21){this.guideOverDays=!0;break}this.guideConfig.vType=["b","c"].includes(t.vType)?t.vType:"b",this.guideConfig.vCount=t.vCount>=0&&t.vCount<=3?t.vCount:0,this.guideOverDays=!1}else this.guideOverDays=!1;let n=c.TimeHelperNS.formatDate(new Date,"yyyy-MM-dd");this.guideConfig.date!==n&&(this.guideConfig.days=this.guideConfig.days+1,this.guideConfig.date=n,localStorage.setItem("newtask_cloudguide",JSON.stringify(this.guideConfig)))}while(0)}mounted(){return i(this,void 0,void 0,function*(){this.initCloudGuideConfig(),o.ipcRenderer.on("dropdown-file-window-select",(e,t,n,r,i)=>{p.information("dropdown select",t,r),"cloud"===i&&this.$emit("cloud-input",{name:t,id:n},r)}),document.addEventListener("click",()=>{this.showPathTips=!1}),(yield d.asyncRemoteCall.getCurrentWindow()).hookWindowMessage(528,()=>{this.showPathTips=!1}),yield u.default(),h.client.attachServerEvent("OnLoginSuc",(e,...t)=>{this.isLogined=!0}),h.client.attachServerEvent("OnLogout",(e,...t)=>{this.isLogined=!1}),this.isLogined=yield h.client.callServerFunction("IsLogined")})}destroyed(){}verify(){return i(this,void 0,void 0,function*(){let e=!1;do{if(this.selectCount){let e=yield h.client.callServerFunction("GetConfigModules","cloud","limitadd");if(e&&this.selectCount>e){this.pathTips=`最多支持同时添加${e}个链接`,this.showPathTips=!0;break}}if(!this.isLogined){e=!0;break}if(!this.value){this.pathTips="不合法的存储路径",this.showPathTips=!0;break}e=!0}while(0);return e})}onClickLogin(){if(!this.guideShow){let e="download_newtask_pannel",t=!0;h.client.callServerFunction("ShowLoginDlg",e,t)}}};r([a.Prop({})],g.prototype,"choosed",void 0),r([a.Prop({})],g.prototype,"panel",void 0),r([a.Prop({default:""})],g.prototype,"value",void 0),r([a.Prop({default:0})],g.prototype,"needSpace",void 0),r([a.Prop({})],g.prototype,"selectCount",void 0),r([a.Watch("value",{immediate:!0})],g.prototype,"onPathChange",null),r([a.Watch("isDanger",{immediate:!0})],g.prototype,"onDanger",null),r([a.Watch("isLogined",{immediate:!0})],g.prototype,"onLoginChange",null),g=r([a.Component({components:{SelectWindow:f.default,PreNewTaskDlgCloudGuideContainer:()=>Promise.resolve().then(()=>n(191)),NewTaskDlgCloudGuideContainer:()=>Promise.resolve().then(()=>n(198)),BtTaskDlgCloudGuideContainer:()=>Promise.resolve().then(()=>n(201))}})],g),t.default=g},function(e,t,n){"use strict";n.r(t);var r=n(104),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(140),i=n(163);t.default=i.connector.connect({mapGettersToProps:{hasVideo:e=>e.hasVideo}})(r.default)},function(e,t,n){"use strict";n.r(t);var r=n(106),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(t,"__esModule",{value:!0});const i=n(5),o={a:"存云盘，节省电脑空间",b:"存云盘，可立即播放",c:"同步手机/网页，随时随地看"};let a=class extends i.Vue{onHasVideo(){do{if(this.hasVideo){let e=Object.assign({},this.options);e.vCount>=3&&(e.vCount=0,e.vType="b"===e.vType?"c":"b"),e.type=e.vType,e.vCount=e.vCount+1,this.$emit("update",e);break}this.$emit("update",{type:"a"})}while(0)}get guideText(){return o[this.options.type]}};r([i.Prop({})],a.prototype,"hasVideo",void 0),r([i.Prop({})],a.prototype,"isLogined",void 0),r([i.Prop({})],a.prototype,"options",void 0),r([i.Watch("hasVideo",{immediate:!0})],a.prototype,"onHasVideo",null),a=r([i.Component({})],a),t.default=a},function(e,t,n){"use strict";n.r(t);var r=n(108),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(140),i=n(155);t.default=i.connector.connect({mapGettersToProps:{hasVideo:e=>e.hasVideo}})(r.default)},function(e,t,n){"use strict";n.r(t);var r=n(110),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(140),i=n(168);t.default=i.connector.connect({mapStateToProps:{hasVideo:e=>e.BtTask.hasVideo}})(r.default)},function(e,t,n){"use strict";n.r(t);var r=n(112),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(t,"__esModule",{value:!0});const i=n(5),o=n(16);let a=class extends i.Vue{constructor(){super(...arguments),this.tipsText="",this.isShowTips=!1,this.timerId=null}handleDocClick(){this.tipsText="",this.isShowTips=!1}mounted(){document.addEventListener("mousedown",this.handleDocClick)}destroyed(){document.removeEventListener("mousedown",this.handleDocClick)}debounce(e,t){this.timerId&&clearTimeout(this.timerId),this.timerId=setTimeout(()=>{this.timerId=null,e()},t)}handleInputChangeDebounce(e){this.debounce(()=>{this.handleInputChange(e)},500)}handleInputChange(e){let t=!0;do{if(this.isAlpha){if(e.length>1){this.tipsText="最大输入字符个数为1",this.isShowTips=!0,t=!1;break}if(!o.ThunderUtil.isAlpha(e)){this.tipsText="只能为字母",this.isShowTips=!0,t=!1;break}}else if(this.isNumber){if(!o.ThunderUtil.isDigital(e)){this.tipsText="输入值必须为数字",this.isShowTips=!0,t=!1;break}let n=Number(e);if(void 0!==this.min&&null!==this.min&&n<this.min){this.tipsText="输入值必须大于等于"+this.min,this.isShowTips=!0,t=!1;break}if(void 0!==this.max&&null!==this.max&&n>this.max){this.tipsText="输入值必须小于等于"+this.max,this.isShowTips=!0,t=!1;break}}}while(0);t?this.$emit("input",e):this.$emit("input",this.value,e)}};r([i.Prop({default:"top"})],a.prototype,"placement",void 0),r([i.Prop({})],a.prototype,"value",void 0),r([i.Prop({default:!1})],a.prototype,"isNumber",void 0),r([i.Prop({})],a.prototype,"max",void 0),r([i.Prop({})],a.prototype,"min",void 0),r([i.Prop({default:!1})],a.prototype,"isAlpha",void 0),a=r([i.Component],a),t.default=a},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(8),a=n(1),s=n(3),l=n(20),c=n(15),u=n(25),d=n(114),f=a.default.getLogger("ThunderNewTask"),h=o.default(i.join(__rootDir,"../bin/ThunderHelper.node"));let p=5;!function(e){let t;function o(e,t,n,r){void 0===n&&(n=u.ThunderHelper.getTaskTypeFromUrl(e));let o=void 0,a=-1,s="",l="";do{if(n!==c.DownloadKernel.TaskType.Emule&&n!==c.DownloadKernel.TaskType.P2sp)break;let u=d.ParseUrlFileNameNS.getNameFromUrl(e),f=i.extname(u);if(""!==f&&(s=f.substring(1)),n===c.DownloadKernel.TaskType.Emule){let t=d.ParseUrlFileNameNS.parseEd2kUrl(e);a=t.fileSize,l=t.fileHash}o={url:e,cookie:"",referer:"",fileName:u,browser:"",statClick:t=void 0===t?"":t,fileSize:a,fileHash:l,fileType:s,birdkeyChars:r};break}while(0);return o}e.taskOptFileNameFixed=2,function(e){e.PreNewTask="PreNewTaskCtrl",e.NewTask="NewTaskCtrl",e.UrlFilter="UrlFilterCtrl",e.Magnet="BtCtrl"}(t=e.TaskCtrlType||(e.TaskCtrlType={})),e.updateThreadCount=function(e){do{if(isNaN(e))break;if(e<=0||e>10)break;p=e}while(0)},e.contructTaskByUrl=function(e,t,n,r){let i=o(e,t,n,r);f.verbose(e);let a=5;return{taskType:n,data:i,setting:{loginFtp:!1,ftpInfo:{userName:"",password:""},onlyOrigin:!1,thread:a=null!==i.thread&&void 0!==i.thread?i.thread:p,note:"",openAfterDownload:!1},selected:!0}},e.contructTaskByUrlData=function(e,t){let n=void 0,r=u.ThunderHelper.getTaskTypeFromUrl(e.url);if(r===c.DownloadKernel.TaskType.Emule&&(void 0===e.fileHash||""===e.fileHash)){let t=d.ParseUrlFileNameNS.parseEd2kUrl(e.url);e.fileSize=t.fileSize,e.fileHash=t.fileHash}if(void 0!==e.fileSize&&null!==e.fileSize&&0!==e.fileSize||(e.fileSize=-1),e.fileName||(e.fileName=d.ParseUrlFileNameNS.getNameFromUrl(e.url)),e.fileName=e.fileName.replace(/[*?/\\:|<>\"]/g,"_"),!e.fileType){let t=i.extname(e.fileName);""!==t&&(e.fileType=t.substring(1))}if(void 0===t||null===t){let n=5;t={loginFtp:!1,ftpInfo:{userName:"",password:""},onlyOrigin:!1,thread:n=null!==e.thread&&void 0!==e.thread?e.thread:p,note:"",openAfterDownload:!1}}return n={taskType:r,data:e,setting:t,selected:!0}},e.getDesktopPath=function(){return h.getDesktopPath()},e.getDocumentPath=function(){return h.getDocumentPath()},e.queryFileExists=function(e){return h.queryFileExists(e)},e.isFilePathValid=function(e){return h.isFilePathValid(e)},e.getNewTaskDataByUrl=o,e.getDownloadResult=function(e,...t){return r(this,void 0,void 0,function*(){let n={result:!1};return s.ipcRenderer.send(l.ThunderChannelList.channelRMNewTaskCreateNewTask,e,...t),n=yield new Promise(e=>{s.ipcRenderer.once(l.ThunderChannelList.channelMRNewTaskCreateNewTaskResult,(t,n,r)=>{e({result:n,message:r})})})})},e.readThunderUnionConfig=function(e){return r(this,void 0,void 0,function*(){let t=null,r=null;try{const r=yield n(19);r.defaults.adapter=n(17),t=yield r.get(e)}catch(e){f.information(e)}return null!==t&&void 0!==t.status&&200===t.status&&void 0!==t.data&&null!==t.data&&(r=t.data),r})}}(t.ThunderNewtaskHelperNS||(t.ThunderNewtaskHelperNS={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(1),o=n(15),a=n(37),s=n(25),l=n(8),c=i.default.getLogger("ThunderNewTask"),u=l.default(r.join(__rootDir,"../bin/ThunderHelper.node")),d=".asf;.avi;.exe;.iso;.mp3;.mpeg;.mpg;.mpga;.ra;.rar;.rm;.rmvb;.tar;.wma;.wmp;.wmv;\n  .mov;.zip;.3gp;.chm;.mdf;.torrent;.jar;.msi;.arj;.bin;.dll;.psd;.hqx;.sit;.lzh;.gz;.tgz;.xlsx;.xls;.doc;.docx;.ppt;\n  .pptx;.flv;.swf;.mkv;.tp;.ts;.flac;.ape;.wav;.aac;.txt;.dat;.7z;.ttf;.bat;.xv;.xvx;.pdf;.mp4;.apk;.ipa;.epub;.mobi;\n  .deb;.sisx;.cab;.pxl;.dmg;.msu;",f=".a;.a3m;.a3w;.a4m;.a4p;.a4w;.a5w;.aam;.aas;.abf;.abk;.abs;.ace;.acm;.acp;.act;.ad;\n  .ada;.adb;.adf;.adi;.adm;.adp;.adr;.ads;.af2;.af3;.afm;.ai;.aif;.aifc;.aiff;.aim;.ais;.akw;.alb;.all;.ams;.anc;.ani;\n  .ans;.ant;.api;.aps;.ari;.arj;.art;.asa;.asc;.asd;.ase;.asf;.asm;.aso;.asp;.asv;.asx;.atw;.au;.avb;.avi;.avr;.avs;\n  .awd;.awr;.axx;.bak;.bas;.bat;.bdf;.bgl;.bi;.bif;.biff;.bin;.bk;.bk$;.bks;.bmk;.bmp;.book;.brx;.bsp;.btm;.bud;.bun;\n  .bw;.bwv;.c;.cab;.cad;.cal;.cap;.cas;.cb;.cc;.ccb;.cch;.cco;.cct;.cda;.cdf;.cdi;.cdm;.cdr;.cdt;.cdx;.cfg;.cgi;.cgm;\n  .chk;.chm;.chr;.cif;.cil;.class;.cll;.clp;.cls;.cmf;.cmv;.cmx;.cnf;.cnm;.cnt;.cod;.com;.cpl;.cpo;.cpp;.cpr;.cpt;.cpx;\n  .crd;.crp;.crt;.csc;.csp;.cst;.csv;.ctl;.cur;.cv;.cxx;.dat;.db;.dbc;.dbf;.dbx;.dcm;.dcs;.dct;.dcu;.dcx;.dem;.der;.dewf;\n  .dib;.dic;.dif;.dig;.dir;.diz;.dlg;.dll;.dls;.dmd;.dmf;.doc;.dot;.draw;.drv;.drw;.dsf;.dsg;.dsm;.dsp;.dsq;.dsw;.dtd;\n  .dun;.dv;.dxf;.dxr;.eda;.edd;.emd;.emf;.eml;.ephtml;.eps;.exe;.fav;.fax;.fcd;.fdf;.ffa;.ffk;.ffl;.ffo;.fif;.fla;.flc;\n  .fm;.fml;.fng;.fnk;.fon;.fot;.frt;.frx;.ftg;.fts;.gal;.gdb;.gdm;.gem;.gen;.getright;.gfi;.gfx;.gho;.gif;.gim;.gix;.gkh;\n  .gks;.gl;.gna;.gnt;.gnx;.gra;.grf;.grp;.hcom;.hgl;.hlp;.hpj;.hpp;.hst;.ht;.htm;.html;.htt;.htx;.icb;.icc;.icl;.icm;.ico;\n  .idd;.idf;.idq;.idx;.iff;.iges;.igf;.ilbm;.ima;.inf;.ini;.inrs;.ins;.int;.iqy;.iso;.ist;.isu;.iwc;.j62;.jar;.java;.jbf;\n  .jff;.jfif;.jif;.jmp;.jpe;.jpeg;.jpg;.js;.jsp;.jtf;.k25;.kar;.kdc;.key;.kfx;.kiz;.kkw;.kmp;.kqp;.lab;.lbm;.lbt;.lbx;.ldb;\n  .ldl;.leg;.lft;.lgo;.lha;.lib;.lin;.lis;.llx;.lnk;.log;.lst;.lu;.lyr;.lzh;.lzs;.m1v;.m3u;.mad;.maf;.mam;.map;.maq;.mar;.mat;\n  .mb1;.mbx;.mcr;.mdb;.mde;.mdl;.mdn;.mdw;.mdz;.mic;.mid;.mim;.mime;.mli;.mme;.mng;.mnu;.mod;.mov;.mp2;.mp3;.mpa;.mpe;.mpeg;\n  .mpg;.mpp;.mpr;.msg;.msi;.msn;.msp;.mst;.mtm;.nan;.nap;.ncb;.ncd;.ncf;.nff;.nft;.nil;.nist;.nls;.nlu;.ntx;.nwc;.nws;.obj;\n  .ocx;.ods;.ofn;.oft;.olb;.ole;.oogl;.opo;.p65;.pab;.part;.pas;.pbd;.pbl;.pbm;.pbr;.pcd;.pcl;.pcm;.pdd;.pdf;.pfm;.pgl;.pgm;\n  .ph;.php;.php3;.phtml;.pic;.pjt;.pjx;.pkg;.pli;.png;.pot;.ppa;.ppf;.ppm;.pps;.ppt;.prf;.prg;.prj;.prn;.prt;.psd;.psp;.pst;\n  .pwz;.qic;.qif;.qlb;.qry;.qtp;.qtx;.qw;.ra;.ram;.rar;.rdf;.reg;.rep;.res;.rft;.rgb;.rm;.rmd;.rpt;.rtf;.rul;.rvp;.s;.sav;.sbl;\n  .scc;.scf;.scp;.scr;.sct;.scx;.sdt;.sdv;.sdx;.sep;.sfd;.sfi;.sfr;.sfx;.sgi;.sgml;.shg;.shtml;.shw;.sig;.ska;.skl;.sl;.spl;\n  .sqc;.sqr;.str;.swa;.swf;.sys;.syw;.taz;.tga;.theme;.thn;.tif;.tiff;.tig;.tlb;.tmp;.tol;.tpl;.trm;.trn;.ttf;.txt;.txw;.udf;\n  .ult;.url;.use;.uwf;.vbp;.vbp;.vbw;.vbw;.vbx;.vbx;.vct;.vcx;.vda;.vda;.vir;.vir;.viv;.vqf;.vsd;.vsd;.vsl;.vsl;.vss;.vss;.vst;\n  .vst;.vsw;.vsw;.vxd;.vxd;.w3l;.wab;.wad;.wav;.wbk;.wcm;.wdb;.wfm;.wfn;.wil;.wiz;.wll;.wmf;.wow;.wp;.wpd;.wpf;.wpg;.wps;.wpt;.wr1;\n  .wrk;.wrl;.wrz;.x;.x16;.x32;.xar;.xbm;.xi;.xla;.xlb;.xlc;.xld;.xlk;.xll;.xlm;.xls;.xlt;.xlv;.xlw;.xnk;.xpm;.xwd;.xwf;.yal;.z;.zap;.zip;";!function(e){function t(e){let t=!1;do{if(void 0===e||null===e)break;if(""===e||"."===e)break;if(d.indexOf(e)>-1){t=!0;break}}while(0);return t}function n(e){let t=!1;do{if(void 0===e||null===e)break;if(""===e)break;if(e.match(/[\/\\"<>\?\*|]/))break;t=!0}while(0);return t}function i(e){let n=!1;do{if(void 0===e||null===e)break;if(""===e||"."===e)break;let r=a.TaskUtilHelper.getTaskFileType(void 0,e);if(r===a.TaskUtilHelper.FileExtType.Video||r===a.TaskUtilHelper.FileExtType.Music||r===a.TaskUtilHelper.FileExtType.Pic){n=!0;break}n=t(e)}while(0);return n}function l(e){let t=!1,o=r.parse(e);return t=n(o.name)&&i(o.ext)}function h(e){let t={};do{if(void 0===e||null===e)break;c.information("parseDynamicUrlArgs");let n=/([^&=?]+)=([^&]*)/g;for(;n.exec(e);)t[RegExp.$1]=RegExp.$2;c.information("parseDynamicUrlArgs ret ",t)}while(0);return t}function p(e){let t={pageFileName:void 0,args:void 0};do{if(void 0===e||null===e)break;c.information("parseDynamicUrlPath"),e.match(/[\/]?([^?]*)\?([^\s]*)/)?(t.pageFileName=RegExp.$1,t.args=RegExp.$2):(t.pageFileName=e,t.args=""),c.information("parseDynamicUrlPath ret",t)}while(0);return t}function g(e){let t="";do{if(void 0===e||null===e)break;let n=p(e);if(void 0!==n.args){let e=h(n.args);for(let n in e){let r=e[n];if(l(r)){t=r;break}}}void 0!==n.pageFileName&&l(n.pageFileName)&&(t=n.pageFileName)}while(0);return t}function m(e){let t=[];do{if(void 0===e||null===e)break;let n=g(e);if(""!==n&&!t.includes(n)){let e=v(n);t.push(e)}let r=v(e);t.includes(r)||t.push(r)}while(0);return t}function v(e){return u.parseFileNameFromP2spUrlPath(e)}function _(e){return u.isThunderPrivateUrl(e)}function y(e){return u.parseEd2kUrl(e)}function w(e){return u.parseUrl(e)}e.isDownloadFileExtName=t,e.isIllegalFileName=n,e.isGoodFileExtName=i,e.isUsualFileExtName=function(e){let t=!1;do{if(void 0===e||null===e)break;if(""===e||"."===e)break;if(i(e)){t=!0;break}if(f.indexOf(e)>-1){t=!0;break}}while(0);return t},e.isGoodFileName=l,e.parseDynamicUrlArgs=h,e.parseDynamicUrlPath=p,e.parseFileNameFromDynamicUrlPath=g,e.getFileNameListFromUrlPath=m,e.getNameFromUrl=function(e){let t="index.html",n=s.ThunderHelper.getTaskTypeFromUrl(e);if(n===o.DownloadKernel.TaskType.P2sp){let n=w(e);if(n){let e=m(n.fullPath);e.length>0&&(t=e[0])}else{let n=/:\/\/.*?\[.+?\].*(\/.+)/.exec(e);if(n&&n[1]){let e=m(n[1]);e.length>0&&(t=e[0])}}}else n===o.DownloadKernel.TaskType.Emule&&(t=y(e).fileName);return t.replace(/[*?/\\:|<>\"]/g,"_")},e.parseFileNameFromP2spUrlPath=v,e.isThunderPrivateUrl=_,e.parseThunderPrivateUrl=function(e){let t={url:e,codePage:-1};return _(e)&&(t=u.parseThunderPrivateUrl(e)),t},e.parseEd2kUrl=y,e.parseUrl=w,e.parseMagnetUrl=function(e){return u.parseMagnetUrl(e)},e.makeUrl=function(e){return u.makeUrl(e)}}(t.ParseUrlFileNameNS||(t.ParseUrlFileNameNS={}))},function(e,t,n){"use strict";var r=n(10),i=n(66),o=n(117),a=n(45);function s(e){var t=new o(e),n=i(o.prototype.request,t);return r.extend(n,o.prototype,t),r.extend(n,t),n}var l=s(a);l.Axios=o,l.create=function(e){return s(r.merge(a,e))},l.Cancel=n(72),l.CancelToken=n(133),l.isCancel=n(71),l.all=function(e){return Promise.all(e)},l.spread=n(134),e.exports=l,e.exports.default=l},function(e,t){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},function(e,t,n){"use strict";var r=n(45),i=n(10),o=n(128),a=n(129);function s(e){this.defaults=e,this.interceptors={request:new o,response:new o}}s.prototype.request=function(e){"string"==typeof e&&(e=i.merge({url:arguments[0]},arguments[1])),(e=i.merge(r,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var t=[a,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},i.forEach(["delete","get","head","options"],function(e){s.prototype[e]=function(t,n){return this.request(i.merge(n||{},{method:e,url:t}))}}),i.forEach(["post","put","patch"],function(e){s.prototype[e]=function(t,n,r){return this.request(i.merge(r||{},{method:e,url:t,data:n}))}}),e.exports=s},function(e,t,n){"use strict";var r=n(10);e.exports=function(e,t){r.forEach(e,function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])})}},function(e,t,n){"use strict";var r=n(10),i=n(67),o=n(69),a=n(120),s=n(121),l=n(46),c="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(122);e.exports=function(e){return new Promise(function(t,u){var d=e.data,f=e.headers;r.isFormData(d)&&delete f["Content-Type"];var h=new XMLHttpRequest,p="onreadystatechange",g=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in h||s(e.url)||(h=new window.XDomainRequest,p="onload",g=!0,h.onprogress=function(){},h.ontimeout=function(){}),e.auth){var m=e.auth.username||"",v=e.auth.password||"";f.Authorization="Basic "+c(m+":"+v)}if(h.open(e.method.toUpperCase(),o(e.url,e.params,e.paramsSerializer),!0),h.timeout=e.timeout,h[p]=function(){if(h&&(4===h.readyState||g)&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in h?a(h.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?h.response:h.responseText,status:1223===h.status?204:h.status,statusText:1223===h.status?"No Content":h.statusText,headers:n,config:e,request:h};i(t,u,r),h=null}},h.onerror=function(){u(l("Network Error",e,null,h)),h=null},h.ontimeout=function(){u(l("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",h)),h=null},r.isStandardBrowserEnv()){var _=n(123),y=(e.withCredentials||s(e.url))&&e.xsrfCookieName?_.read(e.xsrfCookieName):void 0;y&&(f[e.xsrfHeaderName]=y)}if("setRequestHeader"in h&&r.forEach(f,function(e,t){void 0===d&&"content-type"===t.toLowerCase()?delete f[t]:h.setRequestHeader(t,e)}),e.withCredentials&&(h.withCredentials=!0),e.responseType)try{h.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&h.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&h.upload&&h.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){h&&(h.abort(),u(e),h=null)}),void 0===d&&(d=null),h.send(d)})}},function(e,t,n){"use strict";var r=n(10),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,o,a={};return e?(r.forEach(e.split("\n"),function(e){if(o=e.indexOf(":"),t=r.trim(e.substr(0,o)).toLowerCase(),n=r.trim(e.substr(o+1)),t){if(a[t]&&i.indexOf(t)>=0)return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([n]):a[t]?a[t]+", "+n:n}}),a):a}},function(e,t,n){"use strict";var r=n(10);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function i(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=i(window.location.href),function(t){var n=r.isString(t)?i(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},function(e,t,n){"use strict";var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function i(){this.message="String contains an invalid character"}i.prototype=new Error,i.prototype.code=5,i.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,n,o=String(e),a="",s=0,l=r;o.charAt(0|s)||(l="=",s%1);a+=l.charAt(63&t>>8-s%1*8)){if((n=o.charCodeAt(s+=.75))>255)throw new i;t=t<<8|n}return a}},function(e,t,n){"use strict";var r=n(10);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,i,o,a){var s=[];s.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),r.isString(i)&&s.push("path="+i),r.isString(o)&&s.push("domain="+o),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t,n){function r(){var e;try{e=t.storage.debug}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e}(t=e.exports=n(125)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n=this.useColors;if(e[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+e[0]+(n?"%c ":" ")+"+"+t.humanize(this.diff),!n)return;var r="color: "+this.color;e.splice(1,0,r,"color: inherit");var i=0,o=0;e[0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&"%c"===e&&(o=++i)}),e.splice(o,0,r)},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=r,t.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(r())},function(e,t,n){function r(e){var n;function r(){if(r.enabled){var e=r,i=+new Date,o=i-(n||i);e.diff=o,e.prev=n,e.curr=i,n=i;for(var a=new Array(arguments.length),s=0;s<a.length;s++)a[s]=arguments[s];a[0]=t.coerce(a[0]),"string"!=typeof a[0]&&a.unshift("%O");var l=0;a[0]=a[0].replace(/%([a-zA-Z%])/g,function(n,r){if("%%"===n)return n;l++;var i=t.formatters[r];if("function"==typeof i){var o=a[l];n=i.call(e,o),a.splice(l,1),l--}return n}),t.formatArgs.call(e,a),(r.log||t.log||console.log.bind(console)).apply(e,a)}}return r.namespace=e,r.enabled=t.enabled(e),r.useColors=t.useColors(),r.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),r.destroy=i,"function"==typeof t.init&&t.init(r),t.instances.push(r),r}function i(){var e=t.instances.indexOf(this);return-1!==e&&(t.instances.splice(e,1),!0)}(t=e.exports=r.debug=r.default=r).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){var n;t.save(e),t.names=[],t.skips=[];var r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(n=0;n<i;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")));for(n=0;n<t.instances.length;n++){var o=t.instances[n];o.enabled=t.enabled(o.namespace)}},t.enabled=function(e){if("*"===e[e.length-1])return!0;var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(126),t.instances=[],t.names=[],t.skips=[],t.formatters={}},function(e,t){var n=1e3,r=60*n,i=60*r,o=24*i,a=365.25*o;function s(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var l,c=typeof e;if("string"===c&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return s*a;case"days":case"day":case"d":return s*o;case"hours":case"hour":case"hrs":case"hr":case"h":return s*i;case"minutes":case"minute":case"mins":case"min":case"m":return s*r;case"seconds":case"second":case"secs":case"sec":case"s":return s*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}(e);if("number"===c&&!1===isNaN(e))return t.long?s(l=e,o,"day")||s(l,i,"hour")||s(l,r,"minute")||s(l,n,"second")||l+" ms":function(e){if(e>=o)return Math.round(e/o)+"d";if(e>=i)return Math.round(e/i)+"h";if(e>=r)return Math.round(e/r)+"m";if(e>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},function(e){e.exports={_args:[["axios@0.18.0","D:\\jenkins\\workspace\\ThunderPC_AP_Build_Pack\\channel\\trunk\\build\\app"]],_from:"axios@0.18.0",_id:"axios@0.18.0",_inBundle:!1,_integrity:"sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=",_location:"/axios",_phantomChildren:{},_requested:{type:"version",registry:!0,raw:"axios@0.18.0",name:"axios",escapedName:"axios",rawSpec:"0.18.0",saveSpec:null,fetchSpec:"0.18.0"},_requiredBy:["/","/@types/axios","/@xunlei/thunderx-login-main"],_resolved:"http://xnpm.repo.xunlei.cn/axios/-/axios-0.18.0.tgz",_spec:"0.18.0",_where:"D:\\jenkins\\workspace\\ThunderPC_AP_Build_Pack\\channel\\trunk\\build\\app",author:{name:"Matt Zabriskie"},browser:{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},bugs:{url:"https://github.com/axios/axios/issues"},bundlesize:[{path:"./dist/axios.min.js",threshold:"5kB"}],dependencies:{"follow-redirects":"^1.3.0","is-buffer":"^1.1.5"},description:"Promise based HTTP client for the browser and node.js",devDependencies:{bundlesize:"^0.5.7",coveralls:"^2.11.9","es6-promise":"^4.0.5",grunt:"^1.0.1","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.0.0","grunt-contrib-nodeunit":"^1.0.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^19.0.0","grunt-karma":"^2.0.0","grunt-ts":"^6.0.0-beta.3","grunt-webpack":"^1.0.18","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1",karma:"^1.3.0","karma-chrome-launcher":"^2.0.0","karma-coverage":"^1.0.0","karma-firefox-launcher":"^1.0.0","karma-jasmine":"^1.0.2","karma-jasmine-ajax":"^0.1.13","karma-opera-launcher":"^1.0.0","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^1.1.0","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.7","karma-webpack":"^1.7.0","load-grunt-tasks":"^3.5.2",minimist:"^1.2.0",sinon:"^1.17.4",typescript:"^2.0.3","url-search-params":"^0.6.1",webpack:"^1.13.1","webpack-dev-server":"^1.14.1"},homepage:"https://github.com/axios/axios",keywords:["xhr","http","ajax","promise","node"],license:"MIT",main:"index.js",name:"axios",repository:{type:"git",url:"git+https://github.com/axios/axios.git"},scripts:{build:"NODE_ENV=production grunt build",coveralls:"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",examples:"node ./examples/server.js",postversion:"git push && git push --tags",preversion:"npm test",start:"node ./sandbox/server.js",test:"grunt test && bundlesize",version:"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json"},typings:"./index.d.ts",version:"0.18.0"}},function(e,t,n){"use strict";var r=n(10);function i(){this.handlers=[]}i.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},i.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},i.prototype.forEach=function(e){r.forEach(this.handlers,function(t){null!==t&&e(t)})},e.exports=i},function(e,t,n){"use strict";var r=n(10),i=n(130),o=n(71),a=n(45),s=n(131),l=n(132);function c(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return c(e),e.baseURL&&!s(e.url)&&(e.url=l(e.baseURL,e.url)),e.headers=e.headers||{},e.data=i(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||a.adapter)(e).then(function(t){return c(e),t.data=i(t.data,t.headers,e.transformResponse),t},function(t){return o(t)||(c(e),t&&t.response&&(t.response.data=i(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},function(e,t,n){"use strict";var r=n(10);e.exports=function(e,t,n){return r.forEach(n,function(n){e=n(e,t)}),e}},function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,n){"use strict";var r=n(72);function i(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new r(e),t(n.reason))})}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var e;return{token:new i(function(t){e=t}),cancel:e}},e.exports=i},function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(4),o=n(7),a=n(1).default.getLogger("path-selector"),s="PathAndCategory",l="historyDownloadPaths",c=8;function u(e){return r(this,void 0,void 0,function*(){a.information("SetConfigValue",e),yield i.client.callServerFunction("SetConfigValue",s,l,e),yield i.client.callServerFunction("SaveConfig")})}!function(e){function t(){return r(this,void 0,void 0,function*(){return yield i.client.callServerFunction("GetConfigValue",s,l,[])})}e.getMaxHistoryPathsLen=function(){return c},e.getHistoryPaths=t,e.addHistoryPaths=function(e){return r(this,void 0,void 0,function*(){let n=yield t();do{if(void 0===e||null===e||""===e)break;if("\\"===e[e.length-1]&&(e=e.slice(0,e.length-1)),n.includes(e))break;n.length>=c&&n.splice(0,1),n.push(e),u(n).catch()}while(0)})},e.deleteHistoryPath=function(e){return r(this,void 0,void 0,function*(){let n=yield t();do{if(void 0===e||null===e||""===e)break;"\\"===e[e.length-1]&&(e=e.slice(0,e.length-1));let t=n.indexOf(e);if(-1===t)break;n.splice(t,1),u(n).catch()}while(0)})},e.clearHistoryPaths=function(){return r(this,void 0,void 0,function*(){u([]).catch()})},e.getLogicHistoryPaths=function(e=!0,n=!0,i){return r(this,void 0,void 0,function*(){let r=[],a=yield o.asyncRemoteCall.getApp(),s=yield a.getPath("desktop"),l=yield a.getPath("documents"),c=[s,l];r.push({dir:s,alias:"桌面",canDelete:!1},{dir:l,alias:"我的文档",canDelete:!1});let u=yield t();if(void 0!==u){let e=u.indexOf(s);-1!==e&&u.splice(e,1);let t=u.indexOf(l);if(-1!==t&&u.splice(t,1),u.length>0)for(let e of u)"\\"!==e[e.length-1]&&(e+="\\"),-1===c.indexOf(e)&&(c.push(e),r.push({dir:e,alias:e,canDelete:n}))}if(e){let e="私人空间";c.includes(e)||r.push({dir:e,alias:e,canDelete:!1})}if(i)for(let e=i.length-1;e>=0;e--){let t=i[e];"\\"!==t[t.length-1]&&(t+="\\");let o=c.indexOf(t);-1!==o?r.unshift(r.splice(o,1)[0]):r.unshift({dir:t,alias:t,canDelete:n})}return r})},e.getCloudHistoryPath=function(){}}(t.HistoryPathsNS||(t.HistoryPathsNS={}))},function(e,t,n){e.exports=n(41).Buffer},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(40),o=n(4),a=n(1),s=n(81),l=n(6),c=a.default.getLogger("FetchRes"),u={mock:"http://easy-mock.com/mock/59f0652c1bd72e7a888988ab/sl",test:"http://test.api-shoulei-ssl.xunlei.com",prod:"http://api-shoulei-ssl.xunlei.com"},d={"server error":"服务器异常","params error":"参数错误","login plz":"未登录","shield review":"风险账号","shield reject":"异常账号","not allowed":"无权限做此操作","not found":"未找到资源",exists:"已存在","too many":"超过操作上限"};function f(e){return(e=(e=encodeURIComponent(e)).replace(/\(/g,"%29")).replace(/\)/g,"%28")}function h({url:e,data:t,method:i="get"},a="prod"){return r(this,arguments,void 0,function*(){c.information("fetchFromApiProxy",arguments);try{const r=yield Promise.all([o.client.callServerFunction("GetUserID"),o.client.callServerFunction("GetSessionID"),o.client.callServerFunction("GetPeerID")]),[u,d,h]=r;c.information(r);let p={_h:Object.assign({"Account-Id":u,"Peer-Id":h,"User-Id":u,"Session-Id":d,"App-Type":"pc_xunlei"},t&&t._h||{})};t&&t._h&&delete t._h,t&&(t.peer_id=h);let g={accesskey:"pc.xunlei",nonce:Math.floor(*********Math.random()).toString(),timestamp:Math.floor((new Date).getTime()/1e3).toString()},m={};"get"===i&&t&&(m=t,t=null);for(let e in m)l.isObject(m[e])||(g[e]=m[e]);let v=[];for(let e in g)v.push(f(e)+"%3D"+f(g[e]));v.sort();let _=f(e),y=i.toUpperCase()+"&"+_+"&",w="";for(let e=0;e<v.length-1;++e)w+=v[e]+"%26";let b,C=y+(w+=v[v.length-1]);!t||"post"!==i&&"put"!==i||(C+="%26"+JSON.stringify(t)),c.information("message: ",C),b="test"===a?"c9879c94a55474304cca0abafb867653":"89917368930f3fea5bafebe704d6b623";let E=s.ToolsUtilitiesAWNS.encryptHmacBuffer("sha1",b,C,"base64");E=(E=E.replace(/\+/g,"-")).replace(/\//g,"_");let T=e+"?";for(let e in g)T+=f(e)+"="+f(g[e])+"&";T+="sig="+E,c.information("fetchFromApiProxy method",i,", uri",T,", data",t,", headers",p._h);const k=yield n(19);return k.defaults.adapter=n(17),k.defaults.headers["Content-Type"]="post"===i?"application/json":"application/x-www-form-urlencoded",k.request({method:i,url:T,data:t,headers:p._h}).then(e=>(c.information("fetchFromApiProxy success",e),e)).catch(e=>(c.information("fetchFromApiProxy failed",e),{error:e}))}catch(e){return{error:e}}})}function p({url:e,data:t,method:i="get"},a="prod"){return r(this,arguments,void 0,function*(){c.information("fetchFromApiProxy",arguments);try{const r=yield Promise.all([o.client.callServerFunction("GetUserID"),o.client.callServerFunction("GetSessionID"),o.client.callServerFunction("GetPeerID")]),[a,s,u]=r;c.information("fetchFromApiProxyNoSign",r);let d={_h:Object.assign({"Account-Id":a,"Peer-Id":u,"User-Id":a,"Session-Id":s,"App-Type":"pc_xunlei"},t&&t._h||{})};t&&t._h&&delete t._h,t&&(t.peer_id=u);let h={accesskey:"pc.xunlei",nonce:Math.floor(*********Math.random()).toString(),timestamp:Math.floor((new Date).getTime()/1e3).toString()},p={};"get"===i&&t&&(p=t,t=null);for(let e in p)l.isObject(p[e])||(h[e]=p[e]);let g=e;g.includes("?")?g+="&":g+="?";for(let e in h)g+=f(e)+"="+f(h[e])+"&";c.information("fetchFromApiProxy method",i,", uri",g,", data",t,", headers",d._h);const m=yield n(19);return m.defaults.adapter=n(17),m.defaults.headers["Content-Type"]="post"===i?"application/json":"application/x-www-form-urlencoded",m.request({method:i,url:g,data:t,headers:d._h}).then(e=>(c.information("fetchFromApiProxy success",e),e)).catch(e=>(c.information("fetchFromApiProxy failed",e),{error:e}))}catch(e){return{error:e}}})}function g({url:e,data:t,method:n="get"},i="prod",a=!0){return r(this,void 0,void 0,function*(){let r=u[i],s=yield o.client.callServerFunction("GetValue","ConfigFetchInterface","TestServer",0);return 1===s&&(r=u[i="test"]),c.information("当前fetchSlRes 是否测试服:",s," 远程地址:",r),a?h({url:`${r}${e}`,data:t,method:n},i):p({url:`${r}${e}`,data:t,method:n},i)})}function m(e,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"){return Array.apply(null,new Array(e)).map(function(){return t.charAt(Math.floor(Math.random()*t.length))}).join("")}function v(e,t=document.cookie){return null==t.match(new RegExp("(^"+e+"| "+e+")=([^;]*)"))?"":RegExp.$2}t.fetchFromApiProxy=h,t.fetchFromApiProxyNoSign=p,t.fetchSlRes=g,t.fetchPCRes=function({url:e,data:t,method:n="get"},i="prod"){return r(this,void 0,void 0,function*(){return g({url:e,data:t,method:n},i)})},t.fetchPCRequest=function({url:e,data:t,method:i="get"},a="prod"){return r(this,void 0,void 0,function*(){let r=u[a],s=yield o.client.callServerFunction("GetValue","ConfigFetchInterface","TestServer",0);1===s&&(r=u[a="test"]),c.information("当前fetchPCRequest 是否测试服:",s," 远程地址:",r);let l={_h:Object.assign({},t&&t._h||{})};t&&t._h&&delete t._h;const d=yield n(19);d.defaults.adapter=n(17),d.defaults.headers["Content-Type"]="post"===i?"application/json":"application/x-www-form-urlencoded";try{return d.request({method:i,url:`${r}${e}`,params:"get"===i&&t?t:{},data:"post"===i&&t?t:{},headers:l._h}).then(e=>(c.information("fetchRequest success",e),e)).catch(e=>(c.information("fetchRequest fail",e),{error:e}))}catch(e){return{error:e}}})},t.fetchRequest=function({url:e,data:t,method:i="get"}){return r(this,arguments,void 0,function*(){c.information("fetchRequest",arguments);try{const r=yield n(19);return r.defaults.adapter=n(17),r.defaults.headers["Content-Type"]="post"===i?"application/json":"application/x-www-form-urlencoded",r.request({method:i,url:e,params:"get"===i&&t?t:{},data:"post"===i&&t?t:{}}).then(e=>(c.information("fetchRequest success",e),e)).catch(e=>(c.information("fetchRequest fail",e),{error:e}))}catch(e){return{error:e}}})},t.fetchClientRes=function(e,...t){return r(this,void 0,void 0,function*(){return o.client.callServerFunction(e,...t)})},t.fetch=function(e,t){return r(this,void 0,void 0,function*(){const r=n(19);if(!t){let e=new i.Agent({rejectUnauthorized:!1});t={timeout:1e4,httpsAgent:e}}return r(e,t).then(e=>e.data)})},t.fetchCmtRes=function(e,t,i,a="prod"){return r(this,void 0,void 0,function*(){let r=u[a];t=`${r}${t}`;const s=yield Promise.all([o.client.callServerFunction("GetUserID"),o.client.callServerFunction("GetSessionID")]),[l,d]=s;let f=i&&i._h||[];delete i._h;let h={};"get"===e?(h=Object.assign({},i,{app_id:14,userid:l,session_id:d,v:"1.0",jsL2S:1,_:(new Date).getTime()}),i=null):i=Object.assign({},i,{app_id:14,userid:l,session_id:d,v:"1.0",jsL2S:1,_:(new Date).getTime()});const p=n(19).create();return p.defaults.timeout=1e4,p.defaults.adapter=n(17),l&&""!==l&&(p.defaults.headers["User-Id"]=l),d&&""!==d&&(p.defaults.headers["Session-Id"]=d),p.defaults.headers["Content-Type"]="post"===e?"application/json":"application/x-www-form-urlencoded",p.request({method:e,url:t,params:h,data:i,headers:f}).then(e=>{if(200===e.status&&e.data)return e.data;throw new Error(`请求评论接口 ${t} 失败`)}).catch(e=>{c.warning("fetchCmtRes error, catch axios res",e)})})},t.random=m,t.getCookie=v,t.fetchDataWithShoulei=function(e,t={},i={}){return r(this,void 0,void 0,function*(){e=`https://api-shoulei-ssl.xunlei.com${e}`;let a=yield function(e={},t={}){return r(this,void 0,void 0,function*(){const n=(t=Object.assign({},{method:e&&e._m||"get"},t)).method,r=!process.server&&document.cookie||"";let{userID:i=0,version:a,sessionID:l}=yield o.client.callServerFunction("GetUserInfoAw"),c=yield o.client.callServerFunction("GetPeerID");c=c||v("peerid",r)||v("deviceid",r);const u={userID:i,sessionID:l,version:a,guid:s.ToolsUtilitiesAWNS.genarateMd5(c||m(32)),peerId:c},d=[...e&&e._h||[]];let f;return delete e._h,delete e._m,"get"===n&&(f=e,e=null),{method:n,params:f,headerStrings:d,userInfo:u,data:e}})}(t,i),{method:l,params:u,userInfo:f}=a;const h=!process.server&&document.cookie||"",p=Object.assign({},{"Peer-Id":f.peerId,Guid:f.guid,"User-Id":f&&"0"!==f.userID?"":f.userID,"Session-Id":f.sessionID,"Product-Id":14,"Version-Code":"4.8.1","Account-Id":165,"Credit-Key":v("creditkey",h)},i.headers),g=n(19).create();return g.defaults.adapter=n(17),g.defaults.timeout=3e4,g.defaults.headers["Content-Type"]="post"===l?"application/json":"application/x-www-form-urlencoded",g({method:l,url:e,data:t,params:u,headers:p}).then(e=>("ok"!==e.data.result&&(e.data.errorText=d(e.data)),"shield reject"===e.data.result?Promise.resolve({response:e}):(e.data.result,e.data))).catch(e=>(c.warning("error",e),Promise.resolve(Object.assign({},e.response,{message:e.message}))))})}},,function(e,t,n){"use strict";n.r(t);var r=n(89),i=n(76);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\components\\select-native.vue",t.default=s.exports},function(e,t,n){"use strict";n.r(t);var r=n(151),i=n(105);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\views\\cloud-guide.vue",t.default=s.exports},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(1),o=n(4),a=n(43),s=i.default.getLogger("Thunder.ShouldLogin");t.default=function(e=!1,t=!1,n){return r(this,void 0,void 0,function*(){let r,i=null;try{let l=yield o.client.callServerFunction("IsLogined"),c=yield o.client.callServerFunction("GetUserID");l?(s.information("is logined"),r=Promise.resolve(c)):(r=new Promise((n,r)=>{let o=-1;if(o=a.NativeCallModule.nativeCall.AttachNativeEvent("OnUserLogin",e=>{let t=Date.now();s.information("On UserLogin",e,t,i),i&&Math.floor(t-i)>3e5?n(""):n(e),-1!==o&&(a.NativeCallModule.nativeCall.DetachNativeEvent("OnUserLogin",o),o=-1)}),!e){let e=a.NativeCallModule.nativeCall.AttachNativeEvent("OnLoginWndClose",r=>{do{if("close"===r){if(n(""),!t&&-1!==o){a.NativeCallModule.nativeCall.DetachNativeEvent("OnUserLogin",o),o=-1;break}}else if("suc"===r&&-1!==o)break;t&&-1!==o&&(a.NativeCallModule.nativeCall.DetachNativeEvent("OnUserLogin",o),o=-1)}while(0);a.NativeCallModule.nativeCall.DetachNativeEvent("OnLoginWndClose",e)})}}),e||(i=Date.now(),o.client.callServerFunction("ShowLoginDlg",n).catch(e=>{s.warning(e)})))}catch(e){s.warning(e),r=Promise.resolve("")}return r})}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(4);!function(e){e.getValue=function(e,t,n){return r(this,void 0,void 0,function*(){return yield i.client.callServerFunction("GetConfigValue",e,t,n)})},e.setValue=function(e,t,n){return r(this,void 0,void 0,function*(){yield i.client.callServerFunction("SetConfigValue",e,t,n)})}}(t.ConfigHelperNS||(t.ConfigHelperNS={}))},function(e,t,n){"use strict";e.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(7);!function(e){let t;!function(e){e.Info="info",e.Warning="warning",e.Error="error",e.Success="success"}(t=e.ConfirmType||(e.ConfirmType={})),e.confirm=function(e,t={}){return r(this,void 0,void 0,function*(){let n=String(Math.random()).replace(/\D/,"");i.ipcRenderer.send("message-box-confirm-create",{options:e,dialogConf:t,boxId:n});let r=yield o.asyncRemoteCall.getIpcMain();return r.once(`message-box-init-${n}`,t=>{t.sender.send("message-box-init-reply",{popType:"confirm",options:e})}),new Promise(e=>{r.once(`message-box-resolve-${n}`,(t,n,r)=>{e({action:n,checkboxChecked:r})})})})},e.pop=function(e,t={}){return r(this,void 0,void 0,function*(){let n=String(Math.random()).replace(/\D/,""),r=e.parentId;i.ipcRenderer.send("message-box-pop-create",{dialogConf:t,boxId:n,parentId:r});let a=yield o.asyncRemoteCall.getIpcMain();return a.once(`message-box-init-${n}`,t=>{t.sender.send("message-box-init-reply",{popType:"message-box",options:e})}),new Promise(e=>{a.once(`message-box-resolve-${n}`,(t,n,r)=>{e({action:n,formData:r})})})})},e.custom=function(e,t={},n={}){return r(this,void 0,void 0,function*(){let r=String(Math.random()).replace(/\D/,"");i.ipcRenderer.send("message-box-custom-create",JSON.parse(JSON.stringify({popType:e,dialogConf:n,boxId:r,options:t})));let a=yield o.asyncRemoteCall.getIpcMain();return a.once(`message-box-init-${r}`,r=>{r.sender.send("message-box-init-reply",JSON.parse(JSON.stringify({popType:e,options:t,dialogConf:n})))}),new Promise(e=>{a.once(`message-box-resolve-${r}`,(t,n,r)=>{e({action:n,args:r})})})})},e.customIndividuation=function(e,t={},n={}){return r(this,void 0,void 0,function*(){let r=String(Math.random()).replace(/\D/,"");return i.ipcRenderer.send("message-box-individuation-create",{popType:e,dialogConf:n,boxId:r,options:t}),(yield o.asyncRemoteCall.getIpcMain()).once(`message-box-init-${r}`,r=>{r.sender.send("message-box-init-reply",{popType:e,options:t,dialogConf:n})}),new Promise(e=>{i.ipcRenderer.once(`message-box-resolve-${r}`,(t,n,r)=>{e({action:n,args:r})})})})}}(t.MessageBox||(t.MessageBox={}))},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-tooltip",{attrs:{content:e.tipsText,visible:e.isShowTips,placement:e.placement}},[e._t("default"),e._v(" "),n("td-input",e._b({attrs:{value:e.value,isNumber:e.isNumber,max:e.max,min:e.min,isAlpha:e.isAlpha},on:{input:e.handleInputChangeDebounce}},"td-input",e.$attrs,!1))],2)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.dropEnable?n("div",{staticClass:"xly-dialog-task__button",class:{"is-disabled":e.disabled},attrs:{slot:"footer"},slot:"footer"},[n("td-button",{staticClass:"xly-button-down",attrs:{size:"large"},on:{click:function(t){e.handleClick(1)}}},[e._v(e._s("立即下载"))]),e._v(" "),n("i",{staticClass:"xly-line"}),e._v(" "),n("td-button",{staticClass:"xly-button-wait",attrs:{size:"large",title:"稍后下载"},on:{click:function(t){e.handleClick(0)}}},[n("i",{staticClass:"xly-icon-wait"})])],1):n("td-button",{attrs:{size:"large",disabled:e.disabled},on:{click:function(t){e.handleClick(1)}}},[e._v(e._s("立即添加"))])},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"xly-dialog-site"},[n("p",{staticClass:"xly-dialog-site__title"},[e._v(e._s(e.title))]),e._v(" "),n("path-selector-window",{ref:"logicPathSelector",attrs:{panel:e.panel,choosed:e.logicChoosed,needSpace:e.selectSize,value:e.savePath},on:{"logic-input":e.handleChangePath,danger:e.handleSpaceDanger,"check-change":function(t){return e.$emit("check-change",t,"logic")}}}),e._v(" "),n("cloud-path-selector",{ref:"cloudPathSelector",attrs:{panel:e.panel,choosed:e.cloudChoosed,value:e.cloudPath.name,selectCount:e.selectCount},on:{"cloud-input":e.handleChangeCloudPath,"check-change":function(t){return e.$emit("check-change",t,"cloud")}}}),e._v(" "),e.lackSpaceTip?n("div",{staticClass:"xly-guide-tips"},[n("i",{staticClass:"xly-icon-netdisk"}),e._v(" "),n("p",[e._v("电脑空间不足，试试存云盘吧")]),e._v(" "),n("i",{staticClass:"td-icon-close",attrs:{title:"关闭"},on:{click:function(t){e.lackSpaceTip=!1}}})]):e._e()],1)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";var r=function(){var e=this.$createElement;return(this._self._c||e)("td-dropdown",this._g(this._b({attrs:{menus:this.menus,"custom-menu-enabled":""},on:{"drop-click":this.handleDropClick}},"td-dropdown",this.$attrs,!1),this.$listeners),[this._t("default")],2)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-tooltip",{staticStyle:{display:"block"},attrs:{content:e.pathTips,visible:e.showPathTips,offset:{y:3},placement:"top"}},[n("select-window",{ref:"root",staticClass:"xlx-select--type",attrs:{editable:"",value:e.value,panel:e.panel,checked:e.choosed,placeholder:"请选择下载目录",dropOwner:"logic"},on:{input:e.handleInput},nativeOn:{contextmenu:function(t){return e.handleContextMenu(t)}}},[n("td-checkbox",{attrs:{slot:"prefix",value:e.choosed},on:{input:e.handleCheck},slot:"prefix"},[e._v("电脑")]),e._v(" "),n("a",{staticClass:"xly-select__file",attrs:{slot:"suffix",href:"javascript:;"},on:{click:e.handleChangePath},slot:"suffix"},[n("i",{staticClass:"xly-icon-file"})]),e._v(" "),n("span",{staticClass:"xly-select__size",class:e.spaceColor,attrs:{slot:"suffix"},slot:"suffix"},[e._v(e._s(e.freeSpaceFormat))])],1)],1)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-tooltip",{staticStyle:{display:"block"},attrs:{content:e.pathTips,visible:e.showPathTips,offset:{y:3},placement:"top"}},[n("select-window",{ref:"root",staticClass:"xlx-select--type",attrs:{value:e.value,panel:e.panel,checked:e.choosed,placeholder:"请选择下载目录",dropOwner:"cloud"},on:{input:e.handleInput}},[n("td-checkbox",{attrs:{slot:"prefix",value:e.choosed},on:{input:e.handleCheck},slot:"prefix"},[e._v("云盘")]),e._v(" "),e.isLogined?[n("span",{staticClass:"xly-select__size",attrs:{slot:"suffix"},slot:"suffix"},[e._v(e._s(e.freeSpaceFormat))]),e._v(" "),n("a",{staticClass:"xly-select__file",attrs:{slot:"suffix",href:"javascript:;"},on:{click:e.handleChangePath},slot:"suffix"},[n("i",{staticClass:"xly-icon-file"})])]:[n("a",{staticClass:"xly-select__link",attrs:{slot:"suffix",href:"javascript:;"},on:{click:e.onClickLogin},slot:"suffix"},[e._v(e._s(e.guideShow?" ":"登录迅雷帐号，免费获得云盘空间"))])]],2),e._v(" "),e.guideShow?n(e.componentName,{tag:"component",attrs:{options:e.guideConfig,isLogined:e.isLogined},on:{click:e.handleGuideClick,update:e.handleUpdateConfig}}):e._e()],1)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"xly-guide-tag",class:{"is-login":e.isLogined}},[n("span",{on:{click:function(t){e.$emit("click")}}},[e._v(e._s(e.guideText))])])},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},function(e,t,n){"use strict";n.r(t);var r=n(90),i=n(78);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\components\\select-window.vue",t.default=s.exports},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(4);function o(e,t,n){return r(this,void 0,void 0,function*(){return new Promise(r=>{i.client.start(e,t,n,(e,...t)=>{if("connect"===e)r(!0);else if("error"===e){"ENOENT"===t[0].code&&r(!1)}})})})}t.default=function(e,t,n){return r(this,void 0,void 0,function*(){return new Promise(i=>r(this,void 0,void 0,function*(){do{if(yield o(e,t,n)){i();break}let a=null;a=setInterval(()=>r(this,void 0,void 0,function*(){(yield o(e,t,n))&&(clearInterval(a),a=null,i())}),200)}while(0)}))})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(34),i=n(44),o=n(1).default.getLogger("Thunder.shub-http.http-session"),a=n(40),s=n(16),{isDef:l}=s.ThunderUtil;var c;!function(e){e.HTTP="HTTP",e.HTTPS="HTTPS"}(c=t.Protocol||(t.Protocol={}));t.HttpSession=class{constructor(){this.mRetries=0,this.mHost=void 0,this.mPort=void 0,this.mPath=void 0,this.mAuth=void 0,this.mAccept=void 0,this.mBody=null,this.mUrl=void 0,this.mCookie=void 0,this.mProtocol=c.HTTP,this.mTimeout=void 0,this.mCurRetries=0}set host(e){this.mHost=e}get host(){return this.mHost}set port(e){this.mPort=e}get port(){let e=void 0;return e=l(this.mPort)?this.mPort:this.protocol===c.HTTPS?443:80}set path(e){this.mPath=e}get path(){return this.mPath}set url(e){this.mUrl=e}get protocol(){return this.mProtocol}set protocol(e){this.mProtocol=e}get url(){return this.mUrl}get cookie(){return this.mCookie}set cookie(e){this.mCookie=e}set auth(e){this.mAuth=e}get auth(){return this.mAuth}set accept(e){this.mAccept=e}get accept(){return this.mAccept}set body(e){this.mBody=e}get body(){return this.mBody}set retries(e){this.mRetries=e}get retries(){return this.mRetries}set timeout(e){this.mTimeout=e}get timeout(){return this.mTimeout}post(e,t){do{let n=this.body;if(!n){o.information("body is empty"),t(null);break}let r=this.auth,i=this.accept,a={hostname:this.host,port:this.port,path:this.path||"/",method:"POST",auth:r||void 0,headers:{"Content-Length":n?n.length:0,Cookie:this.cookie||"",Accept:i||"*/*"}};o.verbose("option",a);try{this.postImpl(n,a,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,o.information("mCurRetries",this.mCurRetries),this.post(e,t)):t(n)})}catch(e){o.warning("error ",e),t(null)}}while(0)}get(e,t){let n=null;if(this.url){let e=r.parse(this.url,!0);e&&"https:"===e.protocol?this.protocol=c.HTTPS:this.protocol=c.HTTP,n=this.url}else{let e=this.auth,t=this.accept;n={hostname:this.host,port:this.port,path:this.path||"/",method:"GET",auth:e||void 0,headers:{Cookie:this.cookie||"",Accept:t||"*/*"}}}o.verbose("option",n);try{this.getImpl(n,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,o.information("mCurRetries",this.mCurRetries),this.get(e,t)):t(n)})}catch(e){o.warning("error ",e),t(null)}}postImpl(e,t,n,r){let s=(this.protocol===c.HTTPS?a:i).request(t,e=>{let t=null;e.on("data",e=>{t=t?Buffer.concat([t,e]):e}),e.on("end",()=>{o.information("statusCode",e.statusCode),o.information("headers",e.headers),n({statusCode:e.statusCode,headers:e.headers,body:t})})});s.on("error",e=>{r(e)}),s.on("timeout",()=>{s.abort()}),this.timeout&&s.setTimeout(this.timeout),s.write(e),s.end()}getImpl(e,t,n){(this.protocol===c.HTTPS?a:i).get(e,e=>{let n=null;e.on("data",e=>{n=n?Buffer.concat([n,e]):e}),e.on("end",()=>{o.information("statusCode",e.statusCode),o.information("headers",e.headers),t({statusCode:e.statusCode,headers:e.headers,body:n})})}).on("error",e=>{n(e)})}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(31),i=n(84),o=n(85),a=n(199),s=n(200);r.default.use(i.default);const l=new i.default.Store({modules:{Counter:a.default,NewTask:s.default},strict:!1});t.connector=new o.default(l)},function(e,t){function n(e){if("string"!=typeof e)throw new Error("Color should be defined as hex string");var t=e.slice().replace("#","").split("");if(t.length<3||5===t.length||t.length>8)throw new Error("Invalid hex color: "+e);3!==t.length&&4!==t.length||(t=Array.prototype.concat.apply([],t.map(function(e){return[e,e]}))),6===t.length&&t.push("F","F");var n=parseInt(t.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+t.slice(0,6).join("")}}t.getOptions=function(e){e||(e={}),e.color||(e.color={});var t=void 0===e.margin||null===e.margin||e.margin<0?4:e.margin,r=e.width&&e.width>=21?e.width:void 0,i=e.scale||4;return{width:r,scale:r?4:i,margin:t,color:{dark:n(e.color.dark||"#000000ff"),light:n(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},t.getScale=function(e,t){return t.width&&t.width>=e+2*t.margin?t.width/(e+2*t.margin):t.scale},t.getImageWidth=function(e,n){var r=t.getScale(e,n);return Math.floor((e+2*n.margin)*r)},t.qrToImageData=function(e,n,r){for(var i=n.modules.size,o=n.modules.data,a=t.getScale(i,r),s=Math.floor((i+2*r.margin)*a),l=r.margin*a,c=[r.color.light,r.color.dark],u=0;u<s;u++)for(var d=0;d<s;d++){var f=4*(u*s+d),h=r.color.light;if(u>=l&&d>=l&&u<s-l&&d<s-l)h=c[o[Math.floor((u-l)/a)*i+Math.floor((d-l)/a)]?1:0];e[f++]=h.r,e[f++]=h.g,e[f++]=h.b,e[f]=h.a}}},,,,,,function(e,t,n){"use strict";n.r(t);var r=n(149),i=n(99);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);n(189);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\views\\path-selector-window.vue",t.default=s.exports},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(31),i=n(84),o=n(85),a=n(192);r.default.use(i.default);const s=new i.default.Store({modules:{PreNewTask:a.default},strict:!1});t.connector=new o.default(s)},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(154),o=n(193),a=n(1),s=n(196),l=n(137),c=a.default.getLogger("Thunder.QueryTaskInfo");const u=new class extends i.HttpSession{constructor(){super(),this.lastActiveTaskId=-1,this.curBatchQueryId=void 0,this.timerId=null}queryP2spTaskInfo(e){return r(this,void 0,void 0,function*(){return s.Common.isThunderPrivateUrl(e)&&(e=s.Common.parseThunderPrivateUrl(e)),0===e.indexOf("ed2k://")&&-1===e.indexOf("|")&&(e=e.replace(/%7C/g,"|")),new Promise(t=>{this.queryResInfo("url",e).then(e=>{let n={result:0,gcid:"",fileSize:0,errCode:0};e&&200===e.status&&null!==e.data&&null!==e.data.result&&"ok"===e.data.result&&""!==e.data.gcid?(n.result=e.data.code,n.fileSize=e.data.file_size,n.gcid=e.data.gcid,t(n)):t(null)}).catch(()=>{t(null)})})})}asyncBatchQueryP2spTaskInfoByUrl(e,t,n){return r(this,void 0,void 0,function*(){if(!e||0===e.length)return 0;void 0===this.curBatchQueryId?this.curBatchQueryId=1:this.curBatchQueryId+=1;let r=0;for(;r<e.length;++r){let n=yield this.queryP2spTaskInfo(e[r]);n&&t(e[r],n,r),yield this.delay(200)}return r>=e.length&&n&&"function"==typeof n&&n(this.curBatchQueryId),this.curBatchQueryId})}postRequestEd2kTaskInfoByEd2kLink(e){return s.Common.isThunderPrivateUrl(e)&&(e=s.Common.parseThunderPrivateUrl(e)),new Promise(t=>{this.host="hub5idx.shub.sandai.net",this.port=80,this.retries=1,this.timeout=15e3,this.body=o.QueryTaskInfoPackageNS.createQueryEd2kTaskInfoBuffer(e),this.post(e=>{let n=null;200===e.statusCode&&e.body&&e.body.length>12&&(n=o.QueryTaskInfoPackageNS.praseEd2kTaskInfoBuffer(e.body)),c.information("postRequestEd2kTaskInfoByEd2kLink",n),t(n)},e=>{c.warning("postRequestEd2kTaskInfoByEd2kLink",e),t(null)})})}delay(e){return r(this,void 0,void 0,function*(){return new Promise(t=>{setTimeout(t,e)})})}queryResInfo(e,t){return r(this,void 0,void 0,function*(){return l.fetchPCRes({url:"/xlppc.resinfo.api/v1/queryresinfo",data:{type:e,res:t}},"prod")})}};!function(e){e.postRequestEd2kTaskInfoByEd2kLink=function(e){return r(this,void 0,void 0,function*(){return u.postRequestEd2kTaskInfoByEd2kLink(e)})},e.asyncBatchQueryP2spTaskInfoByUrl=function(e,t,n){return r(this,void 0,void 0,function*(){return u.asyncBatchQueryP2spTaskInfoByUrl(e,t,n)})},e.queryResInfo=function(e,t){return r(this,void 0,void 0,function*(){return u.queryResInfo(e,t)})}}(t.QueryTaskInfoHelperNS||(t.QueryTaskInfoHelperNS={}))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12);t.default=class{constructor(){this.emitter=null,this.emitter=new r.EventEmitter}attachOnceListener(e,t){this.emitter.once(e,t)}attachListener(e,t){this.emitter.addListener(e,t)}prependListener(e,t){this.emitter.prependListener(e,t)}prependOnceListener(e,t){this.emitter.prependOnceListener(e,t)}detachListener(e,t){this.emitter.removeListener(e,t)}fireEvent(e,...t){return this.emitter.emit(e,...t)}setMaxListeners(e){this.emitter.setMaxListeners(e)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.eventPluginLoderPluginReady="event_plugin_loader_plugin_ready",e.eventDownloadItemActive="event_download_item_active",e.eventDownloadItemChosen="event_download_item_chosen",e.eventDetailSwitch2Index="event_detail_switch_to_index",e.eventDownloadSwitchCategory="event_download_switch_category",e.evetDownloadMove2Tail="event_download_move_to_tail",e.eventBringMainWindowToTop="event_bring_main_window_to_top",e.eventDownloadContextMenuClick="event_download_context_menu_click",e.eventOnCommand="event_on_command",e.eventOnCommandline="event_on_command_line",e.eventDownloadKernelInitEnd="event_dk_init_end",e.eventDownloadSDKRecover="event_sdk_crash_recover",e.eventTaskDataBaseLoadEnd="event_dk_task_data_base_load_end",e.eventGlobalDownloadSpeedChanged="event_dk_global_download_speed_changed",e.eventGlobalUploadSpeedChanged="event_dk_global_upload_speed_changed",e.eventConfigInitFinished="event_config_init_finished",e.eventConfigValueChanged="event_config_value_changed",e.eventBHOConfigInitFinished="event_bho_config_init_finished",e.eventBHOConfigValueChanged="event_bho_config_value_changed",e.eventMemoryConfigValueChanged="event_memory_config_value_changed",e.eventShowHomePage="event_show_home_page",e.eventShowSearchTaskPage="event_show_searchtask_page",e.eventConfigBrowserInitFinished="event_config_browser_init_finished",e.eventDownloadContextMenuPopup="event_download_context_menu_popup",e.eventDownloadContextMenuEnd="event_download_context_menu_end",e.eventEmbeddedBrowserWndClick="event_embedded_browser_wnd_click",e.eventLoginWndCreate="event_login_wnd_create",e.eventLoginWndCreated="event_login_wnd_created",e.eventLoginWndShow="event_login_wnd_show",e.eventLoginWndShowResult="event_login_wnd_show_result",e.eventLoginWndClose="event_login_wnd_close",e.eventRetryLoginWndCreate="event_retry_login_wnd_create",e.eventModifierUserinfoWndCreate="event_modifier-userinfo-wnd-create",e.eventBrowserNumberChange="event_browser_number_change",e.eventUploadConfigInitFinished="event_upload_config_init_finished",e.eventUploadConfigValueChanged="event_upload_config_value_changed"}(t.NodeEventMesssageNS||(t.NodeEventMesssageNS={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(31),i=n(84),o=n(85),a=n(202),s=n(203);r.default.use(i.default);const l=new i.default.Store({modules:{Counter:a.default,BtTask:s.default},strict:!1});t.connector=new o.default(l)},function(e,t,n){"use strict";n.r(t);var r=n(145),i=n(111);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\views\\limit-input.vue",t.default=s.exports},function(e,t,n){e.exports=n(9)(51)},function(e,t,n){e.exports=n(9)(24)},function(e,t,n){e.exports=n(9)(63)},function(e,t,n){e.exports=n(9)(8)},function(e,t,n){e.exports=n(9)(60)},function(e,t){t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2},t.isValid=function(e){return e&&void 0!==e.bit&&e.bit>=0&&e.bit<4},t.from=function(e,n){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw new Error("Unknown EC Level: "+e)}}(e)}catch(e){return n}}},,,,,,function(e,t,n){"use strict";n.r(t);var r=n(146),i=n(93);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\views\\down-mode.vue",t.default=s.exports},function(e,t,n){"use strict";n.r(t);var r=n(147),i=n(97);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\views\\download-to.vue",t.default=s.exports},function(e,t,n){e.exports=n(515)},,,function(e,t,n){"use strict";n.r(t);var r=n(148),i=n(95);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\components\\dropdown-native.vue",t.default=s.exports},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(188);!function(e){e.setStyle=function(e,t){return r(this,void 0,void 0,function*(){if(null!==e){const{WindowPreferenceNS:r}=yield Promise.resolve().then(()=>n(73));let o={windowPreference:r.getWindowPreference()};e.setStyle(i(o,t))}})}}(t.DropDownWindowSkinNS||(t.DropDownWindowSkinNS={}))},function(e,t,n){(function(e){var n=200,r="__lodash_hash_undefined__",i=800,o=16,a=9007199254740991,s="[object Arguments]",l="[object AsyncFunction]",c="[object Function]",u="[object GeneratorFunction]",d="[object Null]",f="[object Object]",h="[object Proxy]",p="[object Undefined]",g=/^\[object .+?Constructor\]$/,m=/^(?:0|[1-9]\d*)$/,v={};v["[object Float32Array]"]=v["[object Float64Array]"]=v["[object Int8Array]"]=v["[object Int16Array]"]=v["[object Int32Array]"]=v["[object Uint8Array]"]=v["[object Uint8ClampedArray]"]=v["[object Uint16Array]"]=v["[object Uint32Array]"]=!0,v[s]=v["[object Array]"]=v["[object ArrayBuffer]"]=v["[object Boolean]"]=v["[object DataView]"]=v["[object Date]"]=v["[object Error]"]=v[c]=v["[object Map]"]=v["[object Number]"]=v[f]=v["[object RegExp]"]=v["[object Set]"]=v["[object String]"]=v["[object WeakMap]"]=!1;var _="object"==typeof global&&global&&global.Object===Object&&global,y="object"==typeof self&&self&&self.Object===Object&&self,w=_||y||Function("return this")(),b="object"==typeof t&&t&&!t.nodeType&&t,C=b&&"object"==typeof e&&e&&!e.nodeType&&e,E=C&&C.exports===b,T=E&&_.process,k=function(){try{return T&&T.binding&&T.binding("util")}catch(e){}}(),S=k&&k.isTypedArray;function R(e,t){return"__proto__"==t?void 0:e[t]}var x,P,N,I=Array.prototype,D=Function.prototype,O=Object.prototype,L=w["__core-js_shared__"],A=D.toString,M=O.hasOwnProperty,F=(x=/[^.]+$/.exec(L&&L.keys&&L.keys.IE_PROTO||""))?"Symbol(src)_1."+x:"",B=O.toString,U=A.call(Object),j=RegExp("^"+A.call(M).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),H=E?w.Buffer:void 0,W=w.Symbol,G=w.Uint8Array,$=H?H.allocUnsafe:void 0,V=(P=Object.getPrototypeOf,N=Object,function(e){return P(N(e))}),z=Object.create,K=O.propertyIsEnumerable,q=I.splice,Y=W?W.toStringTag:void 0,Q=function(){try{var e=be(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),X=H?H.isBuffer:void 0,J=Math.max,Z=Date.now,ee=be(w,"Map"),te=be(Object,"create"),ne=function(){function e(){}return function(t){if(!De(t))return{};if(z)return z(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();function re(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function ie(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function oe(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function ae(e){var t=this.__data__=new ie(e);this.size=t.size}function se(e,t){var n=Re(e),r=!n&&Se(e),i=!n&&!r&&Pe(e),o=!n&&!r&&!i&&Le(e),a=n||r||i||o,s=a?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],l=s.length;for(var c in e)!t&&!M.call(e,c)||a&&("length"==c||i&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Ce(c,l))||s.push(c);return s}function le(e,t,n){(void 0===n||ke(e[t],n))&&(void 0!==n||t in e)||de(e,t,n)}function ce(e,t,n){var r=e[t];M.call(e,t)&&ke(r,n)&&(void 0!==n||t in e)||de(e,t,n)}function ue(e,t){for(var n=e.length;n--;)if(ke(e[n][0],t))return n;return-1}function de(e,t,n){"__proto__"==t&&Q?Q(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}re.prototype.clear=function(){this.__data__=te?te(null):{},this.size=0},re.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},re.prototype.get=function(e){var t=this.__data__;if(te){var n=t[e];return n===r?void 0:n}return M.call(t,e)?t[e]:void 0},re.prototype.has=function(e){var t=this.__data__;return te?void 0!==t[e]:M.call(t,e)},re.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=te&&void 0===t?r:t,this},ie.prototype.clear=function(){this.__data__=[],this.size=0},ie.prototype.delete=function(e){var t=this.__data__,n=ue(t,e);return!(n<0||(n==t.length-1?t.pop():q.call(t,n,1),--this.size,0))},ie.prototype.get=function(e){var t=this.__data__,n=ue(t,e);return n<0?void 0:t[n][1]},ie.prototype.has=function(e){return ue(this.__data__,e)>-1},ie.prototype.set=function(e,t){var n=this.__data__,r=ue(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},oe.prototype.clear=function(){this.size=0,this.__data__={hash:new re,map:new(ee||ie),string:new re}},oe.prototype.delete=function(e){var t=we(this,e).delete(e);return this.size-=t?1:0,t},oe.prototype.get=function(e){return we(this,e).get(e)},oe.prototype.has=function(e){return we(this,e).has(e)},oe.prototype.set=function(e,t){var n=we(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},ae.prototype.clear=function(){this.__data__=new ie,this.size=0},ae.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},ae.prototype.get=function(e){return this.__data__.get(e)},ae.prototype.has=function(e){return this.__data__.has(e)},ae.prototype.set=function(e,t){var r=this.__data__;if(r instanceof ie){var i=r.__data__;if(!ee||i.length<n-1)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new oe(i)}return r.set(e,t),this.size=r.size,this};var fe,he=function(e,t,n){for(var r=-1,i=Object(e),o=n(e),a=o.length;a--;){var s=o[fe?a:++r];if(!1===t(i[s],s,i))break}return e};function pe(e){return null==e?void 0===e?p:d:Y&&Y in Object(e)?function(e){var t=M.call(e,Y),n=e[Y];try{e[Y]=void 0;var r=!0}catch(e){}var i=B.call(e);r&&(t?e[Y]=n:delete e[Y]);return i}(e):function(e){return B.call(e)}(e)}function ge(e){return Oe(e)&&pe(e)==s}function me(e){return!(!De(e)||F&&F in e)&&(Ne(e)?j:g).test(function(e){if(null!=e){try{return A.call(e)}catch(e){}try{return e+""}catch(e){}}return""}(e))}function ve(e){if(!De(e))return function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}(e);var t=Ee(e),n=[];for(var r in e)("constructor"!=r||!t&&M.call(e,r))&&n.push(r);return n}function _e(e,t,n,r,i){e!==t&&he(t,function(o,a){if(De(o))i||(i=new ae),function(e,t,n,r,i,o,a){var s=R(e,n),l=R(t,n),c=a.get(l);if(c)return void le(e,n,c);var u=o?o(s,l,n+"",e,t,a):void 0,d=void 0===u;if(d){var h=Re(l),p=!h&&Pe(l),g=!h&&!p&&Le(l);u=l,h||p||g?Re(s)?u=s:Oe(w=s)&&xe(w)?u=function(e,t){var n=-1,r=e.length;t||(t=Array(r));for(;++n<r;)t[n]=e[n];return t}(s):p?(d=!1,u=function(e,t){if(t)return e.slice();var n=e.length,r=$?$(n):new e.constructor(n);return e.copy(r),r}(l,!0)):g?(d=!1,m=l,v=!0?(_=m.buffer,y=new _.constructor(_.byteLength),new G(y).set(new G(_)),y):m.buffer,u=new m.constructor(v,m.byteOffset,m.length)):u=[]:function(e){if(!Oe(e)||pe(e)!=f)return!1;var t=V(e);if(null===t)return!0;var n=M.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&A.call(n)==U}(l)||Se(l)?(u=s,Se(s)?u=function(e){return function(e,t,n,r){var i=!n;n||(n={});var o=-1,a=t.length;for(;++o<a;){var s=t[o],l=r?r(n[s],e[s],s,n,e):void 0;void 0===l&&(l=e[s]),i?de(n,s,l):ce(n,s,l)}return n}(e,Ae(e))}(s):(!De(s)||r&&Ne(s))&&(u=function(e){return"function"!=typeof e.constructor||Ee(e)?{}:ne(V(e))}(l))):d=!1}var m,v,_,y;var w;d&&(a.set(l,u),i(u,l,r,o,a),a.delete(l));le(e,n,u)}(e,t,a,n,_e,r,i);else{var s=r?r(R(e,a),o,a+"",e,t,i):void 0;void 0===s&&(s=o),le(e,a,s)}},Ae)}function ye(e,t){return Te(function(e,t,n){return t=J(void 0===t?e.length-1:t,0),function(){for(var r=arguments,i=-1,o=J(r.length-t,0),a=Array(o);++i<o;)a[i]=r[t+i];i=-1;for(var s=Array(t+1);++i<t;)s[i]=r[i];return s[t]=n(a),function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}(e,this,s)}}(e,t,Be),e+"")}function we(e,t){var n,r,i=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof t?"string":"hash"]:i.map}function be(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return me(n)?n:void 0}function Ce(e,t){var n=typeof e;return!!(t=null==t?a:t)&&("number"==n||"symbol"!=n&&m.test(e))&&e>-1&&e%1==0&&e<t}function Ee(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||O)}var Te=function(e){var t=0,n=0;return function(){var r=Z(),a=o-(r-n);if(n=r,a>0){if(++t>=i)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(Q?function(e,t){return Q(e,"toString",{configurable:!0,enumerable:!1,value:(n=t,function(){return n}),writable:!0});var n}:Be);function ke(e,t){return e===t||e!=e&&t!=t}var Se=ge(function(){return arguments}())?ge:function(e){return Oe(e)&&M.call(e,"callee")&&!K.call(e,"callee")},Re=Array.isArray;function xe(e){return null!=e&&Ie(e.length)&&!Ne(e)}var Pe=X||function(){return!1};function Ne(e){if(!De(e))return!1;var t=pe(e);return t==c||t==u||t==l||t==h}function Ie(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=a}function De(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Oe(e){return null!=e&&"object"==typeof e}var Le=S?function(e){return function(t){return e(t)}}(S):function(e){return Oe(e)&&Ie(e.length)&&!!v[pe(e)]};function Ae(e){return xe(e)?se(e,!0):ve(e)}var Me,Fe=(Me=function(e,t,n){_e(e,t,n)},ye(function(e,t){var n=-1,r=t.length,i=r>1?t[r-1]:void 0,o=r>2?t[2]:void 0;for(i=Me.length>3&&"function"==typeof i?(r--,i):void 0,o&&function(e,t,n){if(!De(n))return!1;var r=typeof t;return!!("number"==r?xe(n)&&Ce(t,n.length):"string"==r&&t in n)&&ke(n[t],e)}(t[0],t[1],o)&&(i=r<3?void 0:i,r=1),e=Object(e);++n<r;){var a=t[n];a&&Me(e,a,n,i)}return e}));function Be(e){return e}e.exports=Fe}).call(this,n(165)(e))},function(e,t,n){"use strict";var r=n(1025);n.n(r).a},function(e,t,n){"use strict";n.r(t);var r=n(150),i=n(101);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\views\\cloud-path-selector.vue",t.default=s.exports},function(e,t,n){"use strict";n.r(t);var r=n(103);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);var o=n(0),a=Object(o.a)(r.default,void 0,void 0,!1,null,null,null);a.options.__file="src\\pre-new-task-renderer\\views\\cloud-guide-container.vue",t.default=a.exports},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(1),o=n(113),a=n(37),s=i.default.getLogger("ThunderNewTask"),l={savePath:"",cloudPath:{name:"",id:""},groupChecked:!1,groupName:"",downMode:1,magnetList:[],dataMap:{},urlList:[]},c={HANDLE_URLS_CHANGE(e,t){let n=Object.assign({},e.dataMap);for(let r=0;r<t.length;r++)if(n.hasOwnProperty(t[r].url));else{let i=o.ThunderNewtaskHelperNS.contructTaskByUrl(t[r].url,"newwindow_url",t[r].type,t[r].birdKey);n[t[r].url]=i,e.urlList.push(t[r].url)}e.dataMap=Object.assign({},n)},HANDLE_URLS_INSERT(e,t){let n={},r=[];for(let i of t)if(e.dataMap.hasOwnProperty(i.url));else{let e=o.ThunderNewtaskHelperNS.contructTaskByUrlData(i),t=e.data.fileName;e.data.fileName=t.replace(/[*?/\\:|<>\"]/g,"_"),n[i.url]=e,r.push(i.url)}e.dataMap=Object.assign({},e.dataMap,n);let i=r.filter((e,t,n)=>n.indexOf(e)===t);i.length>0&&e.urlList.push(...i)},HANDLE_TASKDATA_INSERT(e,t){let n={},r=[];for(let i of t)e.dataMap.hasOwnProperty(i.data.url)||(n[i.data.url]=i,r.push(i.data.url));e.dataMap=Object.assign({},e.dataMap,n);let i=r.filter((e,t,n)=>n.indexOf(e)===t);i.length>0&&e.urlList.push(...i)},HANDLE_MAGNETS_CHANGE(e,t){e.magnetList=t},HANDLE_SAVE_PATH_CHANGE(e,t){e.savePath=t},HANDLE_CLOUD_PATH_CHANGE(e,t){e.cloudPath=t},HANDLE_GROUP_CHECK_CHANGE(e,t){if(e.groupChecked=t.checked,t.checked&&void 0!==name){let n=t.name.replace(/[*?/\\:|<>\"]/g,"_");e.groupName=n}},HANDLE_GROUP_NAME_CHANGE(e,t){if(e.groupChecked){let n=t.replace(/[*?/\\:|<>\"]/g,"_");e.groupName=n}},HANDLE_DOWN_MODE_CHANGE(e,t){e.downMode=t},HADNLE_SETTING_CHANGE(e,t){if("all"===t.type){for(let n in e.dataMap)e.dataMap[n].setting=Object.assign({},t.setting);if(t.url!==t.newUrl){let n=e.dataMap[t.url],r=e.urlList.indexOf(t.url);-1!==r&&(e.dataMap[t.newUrl]=n,delete e.dataMap[t.url],e.urlList.splice(r,1,t.newUrl))}}else if(e.dataMap.hasOwnProperty(t.url)&&(e.dataMap[t.url].setting=Object.assign({},t.setting),t.url!==t.newUrl)){let n=e.dataMap[t.url],r=e.urlList.indexOf(t.url);if(-1!==r){n.data.url=t.newUrl,e.dataMap[t.newUrl]=n,delete e.dataMap[t.url];let i=e.urlList.indexOf(t.newUrl);-1!==i&&e.urlList.splice(i,1),e.urlList.splice(r,1,t.newUrl)}}},HANDLE_RENAME(e,t){e.dataMap.hasOwnProperty(t.url)&&(e.dataMap[t.url].data.fileName=t.value,e.dataMap[t.url].data.nameFixedFlag=o.ThunderNewtaskHelperNS.taskOptFileNameFixed)},HANDLE_SELECT(e,t){e.dataMap.hasOwnProperty(t.url)&&(e.dataMap[t.url].selected=t.value)},HANDLE_FETCH_SIZE(e,t){e.dataMap.hasOwnProperty(t.url)&&(e.dataMap[t.url].data.fileSize=t.fileSize,e.dataMap[t.url].data.gcid=t.gcid)}},u={fetchTaskSize({commit:e,state:t,rootState:n},r){s.verbose(e),t.dataMap.hasOwnProperty(r)&&t.dataMap[r].data.fileSize},batchFetchSize({commit:e,state:t,rootState:i}){return r(this,void 0,void 0,function*(){let r=[];for(let e of t.urlList){let n=t.dataMap[e];!n||-1!==n.data.fileSize&&n.data.gcid&&""!==n.data.gcid||r.push(e)}s.information("batchFetchSize",r.length);const{QueryTaskInfoHelperNS:i}=yield Promise.resolve().then(()=>n(164));i.asyncBatchQueryP2spTaskInfoByUrl(r,(t,n)=>{n&&e("HANDLE_FETCH_SIZE",{result:n.result,gcid:n.gcid,fileSize:n.fileSize,errCode:n.errCode,url:t})}).catch()})}},d={selectedDownlist:e=>{let t=[];for(let n of e.urlList)e.dataMap[n].selected&&t.push(e.dataMap[n]);return t},fileNameList:e=>{return Object.values(e.dataMap).map(e=>e.data.fileName)},totalSize:e=>{let t=0;for(let n in e.dataMap)e.dataMap[n].selected&&(t+=e.dataMap[n].data.fileSize);return t},hasVideo:e=>{let t=!1;for(let n in e.dataMap)if(e.dataMap[n].selected&&e.dataMap[n].data.fileName&&a.TaskUtilHelper.isVideoFileExt(e.dataMap[n].data.fileName)){t=!0;break}return t}};t.default={state:l,mutations:c,actions:u,getters:d}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(8),o=n(194),a=n(195),s=n(1).default.getLogger("Thunder.QueryTaskInfoPackage"),l=i.default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){let t=0;function n(){let e=0,t=r.join(__rootDir,"../../Thunder.exe"),n=l.getFileVersion(t).split(".");if(n&&4===n.length){e=26*Number(n[0]).valueOf()+18*Number(n[1]).valueOf()+Number(n[3]).valueOf()}return e}function i(){return l.getPeerID()}e.createQueryP2spTaskInfoBuffer=function(e){let r=0,l=Buffer.alloc(10240);return s.information("createQueryP2spTaskInfoBuffer protocolVersion",61,r),r=l.writeUInt32LE(61,r),s.information("createQueryP2spTaskInfoBuffer sequence",t,r),r=l.writeUInt32LE(++t,r),s.information("createQueryP2spTaskInfoBuffer command length","0",r),r=l.writeUInt32LE(0,r),s.information("createQueryP2spTaskInfoBuffer thunder ver",n(),r),r=l.writeUInt32LE(n(),r),s.information("createQueryP2spTaskInfoBuffer compress flag","0",r),r=l.writeUInt16LE(0,r),s.information("createQueryP2spTaskInfoBuffer reserved","0",r),r=a.BufferUtilitiesNS.writeString(l,"",r,"utf-8"),s.information("createQueryP2spTaskInfoBuffer command","2021",r),r=l.writeUInt16LE(2021,r),s.information("createQueryP2spTaskInfoBuffer by what","2",r),r=l.writeUInt8(2,r),s.information("createQueryP2spTaskInfoBuffer body length","0",r),r=l.writeUInt32LE(0,r),s.information("createQueryP2spTaskInfoBuffer url",e,r),r=a.BufferUtilitiesNS.writeString(l,e,r,"utf-8"),s.information("createQueryP2spTaskInfoBuffer code","-1",r),r=l.writeInt32LE(-1,r),s.information("createQueryP2spTaskInfoBuffer","origin url",r),r=a.BufferUtilitiesNS.writeString(l,"",r,"utf-8"),s.information("createQueryP2spTaskInfoBuffer code","-1",r),r=l.writeInt32LE(-1,r),s.information("createQueryP2spTaskInfoBuffer","refurl",r),r=a.BufferUtilitiesNS.writeString(l,"",r,"utf-8"),s.information("createQueryP2spTaskInfoBuffer code","-1",r),r=l.writeInt32LE(-1,r),s.information("createQueryP2spTaskInfoBuffer",i(),r),r=a.BufferUtilitiesNS.writeString(l,i(),r,"utf-8"),s.information("createQueryP2spTaskInfoBuffer","ip",r),r=l.writeInt32LE(0,r),r=l.writeInt32LE(0,r),r=l.writeInt32LE(-1,r),r=l.writeInt32LE(-1,r),s.information("createQueryP2spTaskInfoBuffer","length",r),l=l.slice(0,r),s.information("createQueryP2spTaskInfoBuffer",l),l=o.ShubCryptoNS.encryptHttpBuffer(l)},e.praseP2spTaskInfoBuffer=function(e){let t={result:0,gcid:"",fileSize:0,errCode:0};try{e=o.ShubCryptoNS.decryptHttpBuffer(e);let n=0,r=e.readUInt32LE(n);s.information("protocolVersion:",r),n+=4;let i=e.readUInt32LE(n);s.information("sequence:",i),n+=4;let l=e.readUInt32LE(n);s.information("commandLegth:",l),n+=4;let c=e.readUInt32LE(n);s.information("thunderVersion:",c),n+=4;let u=e.readUInt16LE(n);s.information("compressFlag:",u),n+=2,n=a.BufferUtilitiesNS.readString(e,n,"utf-8")[0];let d=e.readUInt16LE(n);if(s.information("commandType:",d),n+=2,t.result=e.readUInt8(n),n+=1,1===t.result){n=a.BufferUtilitiesNS.readString(e,n,"hex")[0];let r=a.BufferUtilitiesNS.readUInt64LE(e,n);n=r[0],t.fileSize=r[1];let i=a.BufferUtilitiesNS.readString(e,n,"hex");n=i[0],t.gcid=i[1].toUpperCase(),s.information("gcid",i[1])}else t.errCode=e.readUInt32LE(n);s.information("praseTaskInfoBuffer",t)}catch(e){s.warning("decryptHttpBuffer",e)}return t},e.createQueryEd2kTaskInfoBuffer=function(e){let r=0,c=Buffer.alloc(4096);s.information("createQueryEd2kTaskInfoBuffer protocolVersion",60,r),r=c.writeUInt32LE(60,r),s.information("createQueryEd2kTaskInfoBuffer sequence",t,r),r=c.writeUInt32LE(++t,r),s.information("createQueryEd2kTaskInfoBuffer command length","0",r),r=c.writeUInt32LE(0,r),s.information("createQueryEd2kTaskInfoBuffer thunder ver",n(),r),r=c.writeUInt32LE(n(),r),s.information("createQueryEd2kTaskInfoBuffer compress flag","0",r),r=c.writeUInt16LE(0,r),s.information("createQueryEd2kTaskInfoBuffer reserved","0",r),r=a.BufferUtilitiesNS.writeString(c,"",r,"utf-8"),s.information("createQueryEd2kTaskInfoBuffer command","5001",r),r=c.writeUInt16LE(5001,r),s.information("createQueryEd2kTaskInfoBuffer",i(),r),r=a.BufferUtilitiesNS.writeString(c,i(),r,"utf-8");let u=l.parseEd2kUrl(e),d=u.fileHash;r=c.writeInt32LE(d.length/2,r);let f=0,h=0;for(let e=0;e<d.length;e+=2)f=parseInt(d.charAt(e),16),h=parseInt(d.charAt(e+1),16),r=c.writeUInt8(f<<4|h,r);return r=a.BufferUtilitiesNS.writeUInt64LE(c,u.fileSize,r),r=c.writeInt32LE(0,r),r=a.BufferUtilitiesNS.writeString(c,"",r,"utf-8"),r=c.writeInt32LE(0,r),r=a.BufferUtilitiesNS.writeString(c,e,r,"utf-8"),c=c.slice(0,r),s.information("createQueryEd2kTaskInfoBuffer before encrypt",c,r),c=o.ShubCryptoNS.encryptHttpBuffer(c)},e.praseEd2kTaskInfoBuffer=function(e){let t={result:0,hasRecord:0,gcid:""};try{e=o.ShubCryptoNS.decryptHttpBuffer(e),s.information("praseEd2kTaskInfoBuffer buffer",e);let n=0,r=e.readUInt32LE(n);s.information("protocolVersion:",r),n+=4;let i=e.readUInt32LE(n);s.information("sequence:",i),n+=4;let l=e.readUInt32LE(n);s.information("commandLegth:",l),n+=4;let c=e.readUInt32LE(n);s.information("thunderVersion:",c),n+=4;let u=e.readUInt16LE(n);s.information("compressFlag:",u),n+=2,n=a.BufferUtilitiesNS.readString(e,n,"utf-8")[0];let d=e.readUInt16LE(n);if(s.information("commandType:",d),n+=2,t.result=e.readUInt8(n),n+=1,1===t.result&&(t.hasRecord=e.readInt32LE(n),n+=4,1===t.hasRecord)){n=a.BufferUtilitiesNS.readString(e,n,"utf-8")[0],n=a.BufferUtilitiesNS.readString(e,n,"utf-8")[0],n=a.BufferUtilitiesNS.readString(e,n,"hex")[0];let r=a.BufferUtilitiesNS.readString(e,n,"hex");n=r[0],t.gcid=r[1].toUpperCase()}s.information("praseTaskInfoBuffer",t)}catch(e){s.warning("decryptHttpBuffer",e)}return s.information("praseEd2kTaskInfoBuffer response",t),t}}(t.QueryTaskInfoPackageNS||(t.QueryTaskInfoPackageNS={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(41),i=n(48),o=n(39),a=n(81),s=n(1).default.getLogger("Thunder.shub-http.shub-crypto");!function(e){function t(e){let t=o.createHash("md5");return t.update(e),t.digest()}e.encryptHttpBuffer=function(e){if(0!==e.readUInt16LE(16)){let t=r.Buffer.alloc(20);e.copy(t,0,0,20);let n=r.Buffer.alloc(e.length-20);e.copy(n,0,20);let o=i.deflateSync(n);e=r.Buffer.concat([t,o])}let n=r.Buffer.alloc(8);e.copy(n,0,0,7),n=t(n);let o=r.Buffer.alloc(12);e.copy(o,0,0,11);let s=r.Buffer.alloc(e.length-12);e.copy(s,0,12),s=a.ToolsUtilitiesAWNS.encryptBuffer(s,n);let l=r.Buffer.concat([o,s]);return l.writeUInt32LE(s.length,8),l},e.decryptHttpBuffer=function(e){let n=null;try{let o=r.Buffer.alloc(8);e.copy(o,0,0,7),o=t(o);let l=r.Buffer.alloc(12);e.copy(l,0,0,11);let c=r.Buffer.alloc(e.length-12);if(e.copy(c,0,12),c=a.ToolsUtilitiesAWNS.decryptBuffer(c,o),0!==(n=r.Buffer.concat([l,c])).readUInt16LE(16)){let e=r.Buffer.alloc(20);n.copy(e,0,0,20);let t=r.Buffer.alloc(n.length-20);n.copy(t,0,20);let o=i.inflateSync(t);n=r.Buffer.concat([e,o])}}catch(e){s.warning("decryptHttpBuffer",e)}return n}}(t.ShubCryptoNS||(t.ShubCryptoNS={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(8).default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){e.writeUInt64LE=function(e,t,n){const r=~~(t/4294967295),i=t%4294967295-r;return n=e.writeUInt32LE(i,n),n=e.writeUInt32LE(r,n)},e.writeString=function(e,t,n,r){let o=null;if("gbk"===r){let e=936,n=i.utf8StringEncodeToBinary(t,e);o=Buffer.from(n,"binary")}else o=Buffer.from(t,r);return(n=e.writeUInt32LE(o.byteLength,n))+o.copy(e,n,0)},e.readUInt64LE=function(e,t){let n=e.readUInt32LE(t);t+=4;let r=e.readUInt32LE(t);return[t+=4,r*Math.pow(2,32)+n]},e.readString=function(e,t,n){let r=e.readUInt32LE(t);t+=4;let o=void 0;if("gbk"===n){let n=936,a=e.toString("binary",t,t+r);o=i.utf8StringDecodeFromBinary(a,n)}else o=e.toString(n,t,t+r);return[t+=r,o]}}(t.BufferUtilitiesNS||(t.BufferUtilitiesNS={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(15),o=n(8),a=n(197),s=Math.pow(2,38),l=o.default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(e){return l.isThunderPrivateUrl(e)}function n(e){let t=!0;for(let n=0;n<e.length;n++)if(e.charCodeAt(n)>127){t=!1;break}return t}e.formatSize=function(e){let t=0,n=e,r=["B","KB","MB","GB","TB","PB"];for(;n>=1024&&!(t>r.length-1);)n/=1024,t++;let i=r[t];return n.toFixed(2)+i},e.getSavePathFromImportFile=function(e){let t="";return e&&""!==e&&(t=r.dirname(e)),t},e.getTaskNameFromImportFile=function(e){let t="";do{if(!e||""===e)break;let n=r.basename(e),i=r.extname(e);if(!n||""===n||!i||""===i)break;let o=r.parse(n).name;if(!o||""===o)break;let a=r.extname(o);if(!a||""===a)break;".td"===i||".xltd"===i?t=".bt"===a||".emule"===a?r.parse(o).name:o:".cfg"===i&&(".td"===a||".xltd"===a?t=r.parse(o).name:".emule"!==a&&".xlemule"!==a&&".bt"!==a&&".xlbt"!==a||(o=r.parse(o).name,t=r.parse(o).name))}while(0);return t},e.getTaskTypeFromImportingFile=function(e){let t=i.DownloadKernel.TaskType.Unkown;do{if(!e||""===e)break;let n=r.basename(e),o=r.extname(e);if(!n||""===n||!o||""===o)break;let a=r.parse(n).name;if(!a||""===a)break;let s=r.extname(a).toLowerCase();".td"===o||".xltd"===o?t=".bt"===s?i.DownloadKernel.TaskType.Bt:".emule"===s?i.DownloadKernel.TaskType.Emule:i.DownloadKernel.TaskType.P2sp:".cfg"===o&&(".td"===s||".xltd"===s?t=i.DownloadKernel.TaskType.P2sp:".emule"===s||".xlemule"===s||".part"===s?t=i.DownloadKernel.TaskType.Emule:".bt"!==s&&".xlbt"!==s||(t=i.DownloadKernel.TaskType.Bt))}while(0);return t},e.isThunderPrivateUrl=t,e.parseThunderPrivateUrl=function(e){let n=e;if(t(e)){let t=l.parseThunderPrivateUrl(e);n=null!==t&&void 0!==t?t.url:void 0}return n},e.isAllAsciiChar=n,e.parseEd2kUrl=function(e){let t=null;do{-1===e.indexOf("|")&&(e=e.replace("%7C","|"));let r=[];if((r=e.split("|")).length<5)break;let i="ed2k://";if(r[0].length<i.length||0!==r[0].indexOf(i))break;let o="file";if(r[1].length<o.length||0!==r[1].indexOf(o))break;let a=r[2],l=parseInt(r[3],10);if(l<=0||l>s)break;if(32!==r[4].length)break;let c=r[4];if(-1!==a.indexOf("%")&&n(a))try{a=decodeURI(a)}catch(e){}t={fileName:a=(a=(a=(a=(a=(a=(a=(a=(a=a.replace("<","_")).replace(">","_")).replace(":","_")).replace('"',"_")).replace("/","_")).replace("\\","_")).replace("|","_")).replace("?","_")).replace("*","_"),fileHash:c,fileSize:l}}while(0);return t};let o=void 0;function c(e){let t=0,n=-1;for(let r=0;r<e.length;++r)t+=(n=e.charCodeAt(r))>=0&&n<=128?1:2;return t}function u(e,t){let n="",r=0,i=new String;for(let o=0;o<e.length;o++){let a=e.charAt(o);if(r++,escape(a).length>4&&r++,i=i.concat(a),r>=t){n=i.toString();break}}return r<t&&(n=e),n}e.getXmpPath=function(){if(!o){let e="HKEY_LOCAL_MACHINE",t="SOFTWARE\\Thunder Network\\Xmp",n="Path";o=l.readRegString(e,t,n)}return o},e.getLastAssociateExts=function(){return a.ConfigNS.getValue("DownloadAndPlay","AssociateExts",".asf;.avi;.wm;.wmp;.wmv;.ram;.rm;.rmvb;.rp;.rpm;.rt;.smil;.scm;.dat;.m1v;.m2v;.m2p;.m2ts;.mp2v;.mpe;.mpeg;.mpeg1;.mpeg2;.mpg;.mpv2;.pss;.pva;.tp;.tpr;.ts;.m4b;.m4r;.m4p;.m4v;.mp4;.mpeg4;.3g2;.3gp;.3gp2;.3gpp;.mov;.qt;.flv;.f4v;.swf;.hlv;.ifo;.vob;.amv;.csf;.divx;.evo;.mkv;.mod;.pmp;.vp6;.bik;.mts;.xlmv;.ogm;.ogv;.ogx;.dvd;.srt;.ass;.ssa;.smi;.idx;.sub;.sup;.psb;.usf;.ssf")},e.getStringLength=c,e.cutString=u,e.getMaxFilePath=function(e){let t="",n=c(e);if(n<260)t=e;else{let i=r.dirname(e),o=r.basename(e),a=r.extname(e),s=260-(n=c(i)+2)-a.length-2;s>0&&(o=u(o,s),o+=a,t=r.join(i,o))}return t}}(t.Common||(t.Common={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(21),o=n(2),a=n(1),s=n(166),l=n(18),c=n(167),u=n(16),d=a.default.getLogger("Config");class f{constructor(){this.isInit=!1,this.configData={},this.configPath=void 0,this.configBakPath=void 0,this.eventContainer=new s.default,this.changed=!1,this.timer=null,this.eventContainer.setMaxListeners(0)}init(e){return r(this,void 0,void 0,function*(){if(!this.isInit){this.configPath=e,this.configBakPath=e+".bak";let t=null,n=yield l.FileSystemAWNS.existsAW(e);if(n){let n=yield l.FileSystemAWNS.readFileAW(e);if(n&&n.length>0)try{t=JSON.parse(n.toString())}catch(e){d.warning(e)}}if(!t&&(n=yield l.FileSystemAWNS.existsAW(this.configBakPath))){let e=yield l.FileSystemAWNS.readFileAW(this.configBakPath);if(e&&e.length>0)try{t=JSON.parse(e.toString())}catch(e){d.warning(e)}}t&&this.mergeConfigData(t),this.isInit=!0,this.eventContainer.fireEvent(c.NodeEventMesssageNS.eventConfigInitFinished)}this.timer||(this.timer=setInterval(()=>{this.changed&&(this.changed=!1,this.save().catch())},5e3))})}getValue(e,t,n){let r=n;do{if(!e||""===e)break;if(!t||""===t)break;let n=this.configData[e];if(!n)break;t in n&&(r=n[t])}while(0);return r}setValue(e,t,n){do{if(!e||""===e)break;if(!t||""===t)break;let r=void 0,i=this.configData[e];if(i)r=i[t],i[t]=n;else{let r={};r[t]=n,this.configData[e]=r}r!==n&&(this.changed=!0,this.fireValueChangedEvent(e,t,r,n))}while(0)}removeValue(e,t){do{if(!e||""===e)break;if(!t||""===t)break;let n=void 0,r=this.configData[e];r&&(n=r[t],r[t]=void 0),void 0!==n&&this.fireValueChangedEvent(e,t,n,void 0)}while(0)}isInitConfigFinished(){return this.isInit}getNetDiscVODCachePath(){return r(this,void 0,void 0,function*(){yield this.ensureConfigFisihed();let e=this.getValue("ThunderPanPlugin","defaultDownloadPath","");if(!e){let t=this.getValue("ThunderPanPlugin","downloadPaths",[]);t&&t.length>0&&(e=t[0])}if(e)e=o.join(e,"云盘缓存");else{let t=yield u.ThunderUtil.getThunderTempPath(!0);e=o.join(t,"云盘下载","云盘缓存")}return e})}save(){return r(this,void 0,void 0,function*(){if(this.isInit){let e=JSON.stringify(this.configData,void 0,2);yield l.FileSystemAWNS.writeFileAW(this.configPath,e),yield l.FileSystemAWNS.writeFileAW(this.configBakPath,e)}})}saveSync(){if(this.isInit){let e=JSON.stringify(this.configData,void 0,2);i.writeFileSync(this.configPath,e),i.writeFileSync(this.configBakPath,e)}}attachListener(e,t){do{if(e===c.NodeEventMesssageNS.eventConfigInitFinished){if(this.isInit){t(this.isInit);break}this.eventContainer.attachOnceListener(e,t);break}this.eventContainer.attachListener(e,t)}while(0)}detachListener(e,t){this.eventContainer.detachListener(e,t)}prependListener(e,t){do{if(e===c.NodeEventMesssageNS.eventConfigInitFinished){if(this.isInit){t(this.isInit);break}this.eventContainer.prependOnceListener(e,t);break}this.eventContainer.prependListener(e,t)}while(0)}mergeConfigData(e){if(e)if(null===this.configData)this.configData=e;else for(let t in e){let n=e[t];if(!n)break;for(let e in n){let r=n[e],i=this.configData[t];if(i)i[e]=r;else{let n={};n[e]=r,this.configData[t]=n}}}}fireValueChangedEvent(e,t,n,r){this.eventContainer.fireEvent(c.NodeEventMesssageNS.eventConfigValueChanged,e,t,n,r)}ensureConfigFisihed(){return new Promise((e,t)=>{this.attachListener(c.NodeEventMesssageNS.eventConfigInitFinished,()=>{e()})})}}t.Config=f;const h=new f;!function(e){e.init=function(e){return r(this,void 0,void 0,function*(){yield h.init(e)})},e.getNetDiscVODCachePath=function(){return r(this,void 0,void 0,function*(){return yield h.getNetDiscVODCachePath()})},e.isInitConfigFinished=function(){return h.isInitConfigFinished()},e.isInitAw=function(){return r(this,void 0,void 0,function*(){return!!h.isInitConfigFinished()||new Promise(e=>{h.attachListener(c.NodeEventMesssageNS.eventConfigInitFinished,()=>{e(!0)})})})},e.save=function(){return r(this,void 0,void 0,function*(){yield h.save()})},e.saveSync=function(){h.saveSync()},e.getValue=function(e,t,n){return h.getValue(e,t,n)},e.setValue=function(e,t,n){h.setValue(e,t,n)},e.attachListener=function(e,t){h.attachListener(e,t)},e.prependListener=function(e,t){h.prependListener(e,t)},e.detachListener=function(e,t){h.detachListener(e,t)}}(t.ConfigNS||(t.ConfigNS={}))},function(e,t,n){"use strict";n.r(t);var r=n(107);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);var o=n(0),a=Object(o.a)(r.default,void 0,void 0,!1,null,null,null);a.options.__file="src\\new-task-renderer\\views\\cloud-guide-container.vue",t.default=a.exports},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={decrementMainCounter(e){e.main--},incrementMainCounter(e){e.main++}},i={someAsyncTask({commit:e},t){setTimeout(()=>{e("decrementMainCounter")},t)}};t.default={state:{main:0},mutations:r,actions:i}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(1),a=n(113),s=n(15),l=n(114),c=n(25),u=n(37),d=o.default.getLogger("ThunderNewTask"),f={savePath:"",cloudPath:{name:"",id:""},groupChecked:!1,groupName:"",downMode:1,dataList:[],excludeFilters:[]},h={HANDLE_URLS_INSERT(e,t){let n=e.dataList.length;for(let r=0;r<t.length;r++){let o=t[r],a=c.ThunderHelper.getTaskTypeFromUrl(o.url);if(a===s.DownloadKernel.TaskType.Unkown)continue;if(a===s.DownloadKernel.TaskType.Emule){let e=l.ParseUrlFileNameNS.parseEd2kUrl(o.url);o.fileSize=e.fileSize,o.fileHash=e.fileHash}""===o.fileName&&(o.fileName=l.ParseUrlFileNameNS.getNameFromUrl(o.url));let u=i.extname(o.fileName);""!==u&&(o.fileType=u.substring(1).toLowerCase()),"number"!=typeof o.fileSize&&(o.fileSize=-1);let d=5;o.thread&&"number"==typeof o.thread&&o.thread>0&&o.thread<=10&&(d=o.thread);let f={taskType:a,data:o,setting:{loginFtp:!1,onlyOrigin:!1,thread:d,ftpInfo:{userName:"",password:""},note:"",openAfterDownload:!1},selected:!0,index:n+r};e.dataList.push(f)}},HANDLE_FILTER_CHANGE(e,t){e.excludeFilters=t},HANDLE_FETCH_SIZE(e,t){t.index>=0&&t.index<e.dataList.length&&(e.dataList[t.index].data.fileSize=t.fileSize,e.dataList[t.index].data.gcid=t.gcid)},HANDLE_SAVE_PATH_CHANGE(e,t){e.savePath=t},HANDLE_CLOUD_PATH_CHANGE(e,t){e.cloudPath=t},HANDLE_GROUP_CHECK_CHANGE(e,t){e.groupChecked=t},HANDLE_GROUP_NAME_CHANGE(e,t){if(e.groupChecked){let n=t.replace(/[*?/\\:|<>\"]/g,"_");e.groupName=n}},HANDLE_DOWN_MODE_CHANGE(e,t){e.downMode=t},HANDLE_RENAME(e,t){if(t.index>=0||t.index<e.dataList.length){let n=t.value.replace(/[*?/\\:|<>\"]/g,"_");e.dataList[t.index].data.fileName=n,e.dataList[t.index].data.nameFixedFlag=a.ThunderNewtaskHelperNS.taskOptFileNameFixed}},HANDLE_ORIGIN_ONLY(e,t){(t.index>=0||t.index<e.dataList.length)&&(e.dataList[t.index].setting.onlyOrigin=t.value)},HADNLE_THREAD_COUNT(e,t){(t.index>=0||t.index<e.dataList.length)&&(e.dataList[t.index].setting.thread=t.count)},HANDLE_LOGIN_FTP(e,t){(t.index>=0||t.index<e.dataList.length)&&(e.dataList[t.index].setting.loginFtp=t.value)},HANDLE_CHECK_CHANGE(e,t){(t.index>=0||t.index<e.dataList.length)&&(e.dataList[t.index].selected=t.check)},HANDLE_SELECT_BY_EXT(e,t){for(let n of e.dataList)n.selected||"all"!==t&&String(n.data.fileType)!==t||(n.selected=!0)},HANDLE_UNSELECT_BY_EXT(e,t){for(let n of e.dataList)n.selected&&("all"!==t&&String(n.data.fileType)!==t||(n.selected=!1))},HADNLE_SETTING_CHANGE(e,t){if(-1===t.index)for(let n=0;n<e.dataList.length;n++)e.dataList[n].setting=t.setting;else(t.index>=0||t.index<e.dataList.length)&&(e.dataList[t.index].setting=t.setting)}},p={batchFetchSize({commit:e,state:t,rootState:i}){return r(this,void 0,void 0,function*(){let r=[],i={};for(let e=0;e<t.dataList.length;e++){let n=t.dataList[e];if(-1===n.data.fileSize||!n.data.gcid||""===n.data.gcid){let t=n.data.url;r.push(t),i[t]=e}}if(r.length>0){const{QueryTaskInfoHelperNS:t}=yield Promise.resolve().then(()=>n(164));t.asyncBatchQueryP2spTaskInfoByUrl(r,(t,n,r)=>{n&&e("HANDLE_FETCH_SIZE",{index:i[t],result:n.result,gcid:n.gcid,fileSize:n.fileSize,errCode:n.errCode,url:t})}).catch()}})}},g={taskDataList:e=>{d.information("getters taskDataList in");let t=[];return t=e.dataList.map(e=>e.data)},taskDataFilterList:e=>{let t=e.excludeFilters.join(";");return t+=";",e.dataList.filter(e=>{let n="";return n=void 0===e.data.fileType?"undefined;":e.data.fileType+";",-1===t.indexOf(n)})},taskSettingList:e=>{d.information("getters taskSettingList in");let t=[];return t=e.dataList.map(e=>e.setting)},taskUrls:e=>{d.information("getters taskDataList in");let t=[];return t=e.dataList.map(e=>e.data.url)},taskTypeFilter:e=>{let t=new Set;t.add("all");for(let n of e.dataList)void 0!==n.data.fileType?t.add(n.data.fileType.toLowerCase()):t.add("undefined");return t},hasVideo:e=>{let t=!1;for(let n of e.dataList)if(n.selected&&n.data.fileName&&u.TaskUtilHelper.isVideoFileExt(n.data.fileName)){t=!0;break}return t}};t.default={state:f,mutations:h,actions:p,getters:g}},function(e,t,n){"use strict";n.r(t);var r=n(109);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);var o=n(0),a=Object(o.a)(r.default,void 0,void 0,!1,null,null,null);a.options.__file="src\\bt-task-renderer\\views\\cloud-guide-container.vue",t.default=a.exports},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={decrementMainCounter(e){e.main--},incrementMainCounter(e){e.main++}},i={someAsyncTask({commit:e},t){setTimeout(()=>{e("decrementMainCounter")},t)}};t.default={state:{main:0},mutations:r,actions:i}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),i=n(1),o=n(37),a=n(92);i.default.getLogger("ThunderNewTask").verbose("bt store");const s={hasVideo:!1,savePath:"",cloudPath:{name:"",id:""},downMode:1,seedFile:"",displayName:"",taskInfo:{title:"",infoId:"",trackerUrls:[],fileLists:[]},productInfo:{browser:"",source:"Unknown",referer:"",cookie:"",searchFrom:"",clickFrom:""},selected:[],birdkeyChars:""},l={UPDATE_TASKINFO(e,t){Object.freeze(t.data.fileLists),e.seedFile=t.seedFile,e.displayName=t.data.title,e.taskInfo=Object.assign({},t.data),e.productInfo=Object.assign({},t.productInfo),e.originUrl=t.originUrl,e.birdkeyChars=t.birdkeyChars;{let n=!1;for(let e of t.data.fileLists)if(-1!==e.filePath.toUpperCase().indexOf("BDMV\\")){n=!0;break}let i=!1;e.selected=n?t.data.fileLists.map(e=>(i||(i=o.TaskUtilHelper.isVideoFileExt(e.fileName)),e.realIndex)):t.data.fileLists.filter(e=>{let t=!1;do{if(e.fileSize<1024)break;let n=r.extname(e.fileName);""!==n&&(n=n.substring(1).toLowerCase()),(t=a.URLHelperNS.isSuffixNeedDownload(n))&&!i&&(i=o.TaskUtilHelper.isVideoFileExt(e.fileName))}while(0);return t}).map(e=>e.realIndex),e.hasVideo=i}},UPDATE_PRODUCT_INFO(e,t){e.productInfo=Object.assign({},t)},HANDLE_SAVE_PATH_CHANGE(e,t){e.savePath=t},HANDLE_CLOUD_PATH_CHANGE(e,t){e.cloudPath=t},HANDLE_RENAME(e,t){e.taskInfo.title=t.replace(/[*?/\\:|<>\"]/g,"_")},HANDLE_SELECT_CHANGE(e,t){e.selected=[...t.newSelects];let n=!1;for(let e of t.checkedFiles)if(o.TaskUtilHelper.isVideoFileExt(e.fileName)){n=!0;break}e.hasVideo=n},HANDLE_DOWN_MODE_CHANGE(e,t){e.downMode=t}},c={extFilters:e=>{let t=new Set;t.add("default"),t.add("all");for(let n of e.taskInfo.fileLists){let e=r.extname(n.fileName);""!==e&&(e=e.substring(1)),t.add(e.toLowerCase())}return t}};t.default={state:s,mutations:l,actions:{},getters:c}},function(e,t,n){"use strict";var r=n(1034);n.n(r).a},function(e,t,n){"use strict";var r=n(1038);n.n(r).a},function(e,t,n){"use strict";var r=n(1048);n.n(r).a},function(e,t,n){"use strict";var r=n(1051);n.n(r).a},function(e,t,n){e.exports=n(9)(15)},function(e,t,n){e.exports=n(9)(52)},function(e,t,n){var r=n(136),i=n(87),o=n(175),a=n(518),s=n(519),l=n(520),c=n(521),u=n(522),d=n(211),f=n(523),h=n(526),p=n(527),g=n(88),m=n(528),v=n(214);function _(e,t,n){var r,i,o=e.size,a=p.getEncodedBits(t,n);for(r=0;r<15;r++)i=1==(a>>r&1),r<6?e.set(r,8,i,!0):r<8?e.set(r+1,8,i,!0):e.set(o-15+r,8,i,!0),r<8?e.set(8,o-r-1,i,!0):r<9?e.set(8,15-r-1+1,i,!0):e.set(8,15-r-1,i,!0);e.set(o-8,8,1,!0)}function y(e,t,n){var o=new a;n.forEach(function(t){o.put(t.mode.bit,4),o.put(t.getLength(),g.getCharCountIndicator(t.mode,e)),t.write(o)});var s=8*(i.getSymbolTotalCodewords(e)-d.getTotalCodewordsCount(e,t));for(o.getLengthInBits()+4<=s&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(0);for(var l=(s-o.getLengthInBits())/8,c=0;c<l;c++)o.put(c%2?17:236,8);return function(e,t,n){for(var o=i.getSymbolTotalCodewords(t),a=d.getTotalCodewordsCount(t,n),s=o-a,l=d.getBlocksCount(t,n),c=l-o%l,u=Math.floor(o/l),h=Math.floor(s/l),p=h+1,g=u-h,m=new f(g),v=0,_=new Array(l),y=new Array(l),w=0,b=new r(e.buffer),C=0;C<l;C++){var E=C<c?h:p;_[C]=b.slice(v,v+E),y[C]=m.encode(_[C]),v+=E,w=Math.max(w,E)}var T,k,S=new r(o),R=0;for(T=0;T<w;T++)for(k=0;k<l;k++)T<_[k].length&&(S[R++]=_[k][T]);for(T=0;T<g;T++)for(k=0;k<l;k++)S[R++]=y[k][T];return S}(o,e,t)}function w(e,t,n,r){var o;if(v(e))o=m.fromArray(e);else{if("string"!=typeof e)throw new Error("Invalid data");var a=t;if(!a){var d=m.rawSplit(e);a=h.getBestVersionForData(d,n)}o=m.fromString(e,a||40)}var f=h.getBestVersionForData(o,n);if(!f)throw new Error("The amount of data is too big to be stored in a QR Code");if(t){if(t<f)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+f+".\n")}else t=f;var p=y(t,n,o),g=i.getSymbolSize(t),w=new s(g);return function(e,t){for(var n=e.size,r=c.getPositions(t),i=0;i<r.length;i++)for(var o=r[i][0],a=r[i][1],s=-1;s<=7;s++)if(!(o+s<=-1||n<=o+s))for(var l=-1;l<=7;l++)a+l<=-1||n<=a+l||(s>=0&&s<=6&&(0===l||6===l)||l>=0&&l<=6&&(0===s||6===s)||s>=2&&s<=4&&l>=2&&l<=4?e.set(o+s,a+l,!0,!0):e.set(o+s,a+l,!1,!0))}(w,t),function(e){for(var t=e.size,n=8;n<t-8;n++){var r=n%2==0;e.set(n,6,r,!0),e.set(6,n,r,!0)}}(w),function(e,t){for(var n=l.getPositions(t),r=0;r<n.length;r++)for(var i=n[r][0],o=n[r][1],a=-2;a<=2;a++)for(var s=-2;s<=2;s++)-2===a||2===a||-2===s||2===s||0===a&&0===s?e.set(i+a,o+s,!0,!0):e.set(i+a,o+s,!1,!0)}(w,t),_(w,n,0),t>=7&&function(e,t){for(var n,r,i,o=e.size,a=h.getEncodedBits(t),s=0;s<18;s++)n=Math.floor(s/3),r=s%3+o-8-3,i=1==(a>>s&1),e.set(n,r,i,!0),e.set(r,n,i,!0)}(w,t),function(e,t){for(var n=e.size,r=-1,i=n-1,o=7,a=0,s=n-1;s>0;s-=2)for(6===s&&s--;;){for(var l=0;l<2;l++)if(!e.isReserved(i,s-l)){var c=!1;a<t.length&&(c=1==(t[a]>>>o&1)),e.set(i,s-l,c),-1==--o&&(a++,o=7)}if((i+=r)<0||n<=i){i-=r,r=-r;break}}}(w,p),isNaN(r)&&(r=u.getBestMask(w,_.bind(null,w,n))),u.applyMask(r,w),_(w,n,r),{modules:w,version:t,errorCorrectionLevel:n,maskPattern:r,segments:o}}t.create=function(e,t){if(void 0===e||""===e)throw new Error("No input text");var n,r,a=o.M;return void 0!==t&&(a=o.from(t.errorCorrectionLevel,o.M),n=h.from(t.version),r=u.from(t.maskPattern),t.toSJISFunc&&i.setToSJISFunction(t.toSJISFunc)),w(e,n,a,r)}},function(e,t,n){var r=n(175),i=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],o=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];t.getBlocksCount=function(e,t){switch(t){case r.L:return i[4*(e-1)+0];case r.M:return i[4*(e-1)+1];case r.Q:return i[4*(e-1)+2];case r.H:return i[4*(e-1)+3];default:return}},t.getTotalCodewordsCount=function(e,t){switch(t){case r.L:return o[4*(e-1)+0];case r.M:return o[4*(e-1)+1];case r.Q:return o[4*(e-1)+2];case r.H:return o[4*(e-1)+3];default:return}}},function(e,t){t.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40}},function(e,t){var n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",r="(?:(?![A-Z0-9 $%*+\\-./:]|"+(n=n.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";t.KANJI=new RegExp(n,"g"),t.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),t.BYTE=new RegExp(r,"g"),t.NUMERIC=new RegExp("[0-9]+","g"),t.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");var i=new RegExp("^"+n+"$"),o=new RegExp("^[0-9]+$"),a=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");t.testKanji=function(e){return i.test(e)},t.testNumeric=function(e){return o.test(e)},t.testAlphanumeric=function(e){return a.test(e)}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";var r=n(6),i=n(75),o=e.exports=function(){i.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};r.inherits(o,i),o.prototype.read=function(e,t){this._reads.push({length:Math.abs(e),allowLess:e<0,func:t}),process.nextTick(function(){this._process(),this._paused&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))},o.prototype.write=function(e,t){return this.writable?(n=Buffer.isBuffer(e)?e:new Buffer(e,t||this._encoding),this._buffers.push(n),this._buffered+=n.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",new Error("Stream not writable")),!1);var n},o.prototype.end=function(e,t){e&&this.write(e,t),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},o.prototype.destroySoon=o.prototype.end,o.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("There are some read requests waiting on finished stream")),this.destroy()},o.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},o.prototype._processReadAllowingLess=function(e){this._reads.shift();var t=this._buffers[0];t.length>e.length?(this._buffered-=e.length,this._buffers[0]=t.slice(e.length),e.func.call(this,t.slice(0,e.length))):(this._buffered-=t.length,this._buffers.shift(),e.func.call(this,t))},o.prototype._processRead=function(e){this._reads.shift();for(var t=0,n=0,r=new Buffer(e.length);t<e.length;){var i=this._buffers[n++],o=Math.min(i.length,e.length-t);i.copy(r,t,0,o),t+=o,o!==i.length&&(this._buffers[--n]=i.slice(o))}n>0&&this._buffers.splice(0,n),this._buffered-=e.length,e.func.call(this,r)},o.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){var e=this._reads[0];if(e.allowLess)this._processReadAllowingLess(e);else{if(!(this._buffered>=e.length))break;this._processRead(e)}}this._buffers&&this._buffers.length>0&&null===this._buffers[0]&&this._end()}catch(e){this.emit("error",e)}}},function(e,t,n){"use strict";var r=n(217),i=n(218);function o(e,t,n){var r=e*t;return 8!==n&&(r=Math.ceil(r/(8/n))),r}var a=e.exports=function(e,t){var n=e.width,i=e.height,a=e.interlace,s=e.bpp,l=e.depth;if(this.read=t.read,this.write=t.write,this.complete=t.complete,this._imageIndex=0,this._images=[],a)for(var c=r.getImagePasses(n,i),u=0;u<c.length;u++)this._images.push({byteWidth:o(c[u].width,s,l),height:c[u].height,lineIndex:0});else this._images.push({byteWidth:o(n,s,l),height:i,lineIndex:0});this._xComparison=8===l?s:16===l?2*s:1};a.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},a.prototype._unFilterType1=function(e,t,n){for(var r=this._xComparison,i=r-1,o=0;o<n;o++){var a=e[1+o],s=o>i?t[o-r]:0;t[o]=a+s}},a.prototype._unFilterType2=function(e,t,n){for(var r=this._lastLine,i=0;i<n;i++){var o=e[1+i],a=r?r[i]:0;t[i]=o+a}},a.prototype._unFilterType3=function(e,t,n){for(var r=this._xComparison,i=r-1,o=this._lastLine,a=0;a<n;a++){var s=e[1+a],l=o?o[a]:0,c=a>i?t[a-r]:0,u=Math.floor((c+l)/2);t[a]=s+u}},a.prototype._unFilterType4=function(e,t,n){for(var r=this._xComparison,o=r-1,a=this._lastLine,s=0;s<n;s++){var l=e[1+s],c=a?a[s]:0,u=s>o?t[s-r]:0,d=s>o&&a?a[s-r]:0,f=i(u,c,d);t[s]=l+f}},a.prototype._reverseFilterLine=function(e){var t,n=e[0],r=this._images[this._imageIndex],i=r.byteWidth;if(0===n)t=e.slice(1,i+1);else switch(t=new Buffer(i),n){case 1:this._unFilterType1(e,t,i);break;case 2:this._unFilterType2(e,t,i);break;case 3:this._unFilterType3(e,t,i);break;case 4:this._unFilterType4(e,t,i);break;default:throw new Error("Unrecognised filter type - "+n)}this.write(t),r.lineIndex++,r.lineIndex>=r.height?(this._lastLine=null,this._imageIndex++,r=this._images[this._imageIndex]):this._lastLine=t,r?this.read(r.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}},function(e,t,n){"use strict";var r=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];t.getImagePasses=function(e,t){for(var n=[],i=e%8,o=t%8,a=(e-i)/8,s=(t-o)/8,l=0;l<r.length;l++){for(var c=r[l],u=a*c.x.length,d=s*c.y.length,f=0;f<c.x.length&&c.x[f]<i;f++)u++;for(f=0;f<c.y.length&&c.y[f]<o;f++)d++;u>0&&d>0&&n.push({width:u,height:d,index:l})}return n},t.getInterlaceIterator=function(e){return function(t,n,i){var o=t%r[i].x.length,a=(t-o)/r[i].x.length*8+r[i].x[o],s=n%r[i].y.length;return 4*a+((n-s)/r[i].y.length*8+r[i].y[s])*e*4}}},function(e,t,n){"use strict";e.exports=function(e,t,n){var r=e+t-n,i=Math.abs(r-e),o=Math.abs(r-t),a=Math.abs(r-n);return i<=o&&i<=a?e:o<=a?t:n}},function(e,t,n){"use strict";var r=n(143),i=n(220),o=e.exports=function(e,t){this._options=e,e.checkCRC=!1!==e.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[r.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[r.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[r.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[r.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[r.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[r.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=t.read,this.error=t.error,this.metadata=t.metadata,this.gamma=t.gamma,this.transColor=t.transColor,this.palette=t.palette,this.parsed=t.parsed,this.inflateData=t.inflateData,this.finished=t.finished};o.prototype.start=function(){this.read(r.PNG_SIGNATURE.length,this._parseSignature.bind(this))},o.prototype._parseSignature=function(e){for(var t=r.PNG_SIGNATURE,n=0;n<t.length;n++)if(e[n]!==t[n])return void this.error(new Error("Invalid file signature"));this.read(8,this._parseChunkBegin.bind(this))},o.prototype._parseChunkBegin=function(e){for(var t=e.readUInt32BE(0),n=e.readUInt32BE(4),o="",a=4;a<8;a++)o+=String.fromCharCode(e[a]);var s=Boolean(32&e[4]);if(this._hasIHDR||n===r.TYPE_IHDR){if(this._crc=new i,this._crc.write(new Buffer(o)),this._chunks[n])return this._chunks[n](t);s?this.read(t+4,this._skipChunk.bind(this)):this.error(new Error("Unsupported critical chunk type "+o))}else this.error(new Error("Expected IHDR on beggining"))},o.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},o.prototype._parseChunkEnd=function(e){var t=e.readInt32BE(0),n=this._crc.crc32();this._options.checkCRC&&n!==t?this.error(new Error("Crc error - "+t+" - "+n)):this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleIHDR=function(e){this.read(e,this._parseIHDR.bind(this))},o.prototype._parseIHDR=function(e){this._crc.write(e);var t=e.readUInt32BE(0),n=e.readUInt32BE(4),i=e[8],o=e[9],a=e[10],s=e[11],l=e[12];if(8===i||4===i||2===i||1===i||16===i)if(o in r.COLORTYPE_TO_BPP_MAP)if(0===a)if(0===s)if(0===l||1===l){this._colorType=o;var c=r.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:t,height:n,depth:i,interlace:Boolean(l),palette:Boolean(o&r.COLORTYPE_PALETTE),color:Boolean(o&r.COLORTYPE_COLOR),alpha:Boolean(o&r.COLORTYPE_ALPHA),bpp:c,colorType:o}),this._handleChunkEnd()}else this.error(new Error("Unsupported interlace method"));else this.error(new Error("Unsupported filter method"));else this.error(new Error("Unsupported compression method"));else this.error(new Error("Unsupported color type"));else this.error(new Error("Unsupported bit depth "+i))},o.prototype._handlePLTE=function(e){this.read(e,this._parsePLTE.bind(this))},o.prototype._parsePLTE=function(e){this._crc.write(e);for(var t=Math.floor(e.length/3),n=0;n<t;n++)this._palette.push([e[3*n],e[3*n+1],e[3*n+2],255]);this.palette(this._palette),this._handleChunkEnd()},o.prototype._handleTRNS=function(e){this.read(e,this._parseTRNS.bind(this))},o.prototype._parseTRNS=function(e){if(this._crc.write(e),this._colorType===r.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length)return void this.error(new Error("Transparency chunk must be after palette"));if(e.length>this._palette.length)return void this.error(new Error("More transparent colors than palette size"));for(var t=0;t<e.length;t++)this._palette[t][3]=e[t];this.palette(this._palette)}this._colorType===r.COLORTYPE_GRAYSCALE&&this.transColor([e.readUInt16BE(0)]),this._colorType===r.COLORTYPE_COLOR&&this.transColor([e.readUInt16BE(0),e.readUInt16BE(2),e.readUInt16BE(4)]),this._handleChunkEnd()},o.prototype._handleGAMA=function(e){this.read(e,this._parseGAMA.bind(this))},o.prototype._parseGAMA=function(e){this._crc.write(e),this.gamma(e.readUInt32BE(0)/r.GAMMA_DIVISION),this._handleChunkEnd()},o.prototype._handleIDAT=function(e){this.read(-e,this._parseIDAT.bind(this,e))},o.prototype._parseIDAT=function(e,t){if(this._crc.write(t),this._colorType===r.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw new Error("Expected palette not found");this.inflateData(t);var n=e-t.length;n>0?this._handleIDAT(n):this._handleChunkEnd()},o.prototype._handleIEND=function(e){this.read(e,this._parseIEND.bind(this))},o.prototype._parseIEND=function(e){this._crc.write(e),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}},function(e,t,n){"use strict";var r=[];!function(){for(var e=0;e<256;e++){for(var t=e,n=0;n<8;n++)1&t?t=3988292384^t>>>1:t>>>=1;r[e]=t}}();var i=e.exports=function(){this._crc=-1};i.prototype.write=function(e){for(var t=0;t<e.length;t++)this._crc=r[255&(this._crc^e[t])]^this._crc>>>8;return!0},i.prototype.crc32=function(){return-1^this._crc},i.crc32=function(e){for(var t=-1,n=0;n<e.length;n++)t=r[255&(t^e[n])]^t>>>8;return-1^t}},function(e,t,n){"use strict";var r=n(217),i={1:{0:0,1:0,2:0,3:255},2:{0:0,1:0,2:0,3:1},3:{0:0,1:1,2:2,3:255},4:{0:0,1:1,2:2,3:3}};function o(e,t,n,r,o,a){for(var s=e.width,l=e.height,c=e.index,u=0;u<l;u++)for(var d=0;d<s;d++){for(var f=n(d,u,c),h=0;h<4;h++){var p=i[r][h];if(255===p)t[f+h]=255;else{var g=p+a;if(g===o.length)throw new Error("Ran out of data");t[f+h]=o[g]}}a+=r}return a}function a(e,t,n,r,o,a){for(var s=e.width,l=e.height,c=e.index,u=0;u<l;u++){for(var d=0;d<s;d++)for(var f=o.get(r),h=n(d,u,c),p=0;p<4;p++){var g=i[r][p];t[h+p]=255!==g?f[g]:a}o.resetAfterLine()}}t.dataToBitMap=function(e,t){var n,i=t.width,s=t.height,l=t.depth,c=t.bpp,u=t.interlace;if(8!==l)var d=function(e,t){var n=[],r=0;function i(){if(r===e.length)throw new Error("Ran out of data");var i,o,a,s,l,c,u,d,f=e[r];switch(r++,t){default:throw new Error("unrecognised depth");case 16:u=e[r],r++,n.push((f<<8)+u);break;case 4:u=15&f,d=f>>4,n.push(d,u);break;case 2:l=3&f,c=f>>2&3,u=f>>4&3,d=f>>6&3,n.push(d,u,c,l);break;case 1:i=1&f,o=f>>1&1,a=f>>2&1,s=f>>3&1,l=f>>4&1,c=f>>5&1,u=f>>6&1,d=f>>7&1,n.push(d,u,c,l,s,a,o,i)}}return{get:function(e){for(;n.length<e;)i();var t=n.slice(0,e);return n=n.slice(e),t},resetAfterLine:function(){n.length=0},end:function(){if(r!==e.length)throw new Error("extra data found")}}}(e,l);n=l<=8?new Buffer(i*s*4):new Uint16Array(i*s*4);var f,h,p=Math.pow(2,l)-1,g=0;if(u)f=r.getImagePasses(i,s),h=r.getInterlaceIterator(i,s);else{var m=0;h=function(){var e=m;return m+=4,e},f=[{width:i,height:s}]}for(var v=0;v<f.length;v++)8===l?g=o(f[v],n,h,c,e,g):a(f[v],n,h,c,d,p);if(8===l){if(g!==e.length)throw new Error("extra data found")}else d.end();return n}},function(e,t,n){"use strict";e.exports=function(e,t){var n=t.depth,r=t.width,i=t.height,o=t.colorType,a=t.transColor,s=t.palette,l=e;return 3===o?function(e,t,n,r,i){for(var o=0,a=0;a<r;a++)for(var s=0;s<n;s++){var l=i[e[o]];if(!l)throw new Error("index "+e[o]+" not in palette");for(var c=0;c<4;c++)t[o+c]=l[c];o+=4}}(e,l,r,i,s):(a&&function(e,t,n,r,i){for(var o=0,a=0;a<r;a++)for(var s=0;s<n;s++){var l=!1;if(1===i.length?i[0]===e[o]&&(l=!0):i[0]===e[o]&&i[1]===e[o+1]&&i[2]===e[o+2]&&(l=!0),l)for(var c=0;c<4;c++)t[o+c]=0;o+=4}}(e,l,r,i,a),8!==n&&(16===n&&(l=new Buffer(r*i*4)),function(e,t,n,r,i){for(var o=Math.pow(2,i)-1,a=0,s=0;s<r;s++)for(var l=0;l<n;l++){for(var c=0;c<4;c++)t[a+c]=Math.floor(255*e[a+c]/o+.5);a+=4}}(e,l,r,i,n))),l}},function(e,t,n){"use strict";var r=n(143),i=n(220),o=n(539),a=n(540),s=n(48),l=e.exports=function(e){if(this._options=e,e.deflateChunkSize=e.deflateChunkSize||32768,e.deflateLevel=null!=e.deflateLevel?e.deflateLevel:9,e.deflateStrategy=null!=e.deflateStrategy?e.deflateStrategy:3,e.inputHasAlpha=null==e.inputHasAlpha||e.inputHasAlpha,e.deflateFactory=e.deflateFactory||s.createDeflate,e.bitDepth=e.bitDepth||8,e.colorType="number"==typeof e.colorType?e.colorType:r.COLORTYPE_COLOR_ALPHA,e.inputColorType="number"==typeof e.inputColorType?e.inputColorType:r.COLORTYPE_COLOR_ALPHA,-1===[r.COLORTYPE_GRAYSCALE,r.COLORTYPE_COLOR,r.COLORTYPE_COLOR_ALPHA,r.COLORTYPE_ALPHA].indexOf(e.colorType))throw new Error("option color type:"+e.colorType+" is not supported at present");if(-1===[r.COLORTYPE_GRAYSCALE,r.COLORTYPE_COLOR,r.COLORTYPE_COLOR_ALPHA,r.COLORTYPE_ALPHA].indexOf(e.inputColorType))throw new Error("option input color type:"+e.inputColorType+" is not supported at present");if(8!==e.bitDepth&&16!==e.bitDepth)throw new Error("option bit depth:"+e.bitDepth+" is not supported at present")};l.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},l.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},l.prototype.filterData=function(e,t,n){var i=o(e,t,n,this._options),s=r.COLORTYPE_TO_BPP_MAP[this._options.colorType];return a(i,t,n,this._options,s)},l.prototype._packChunk=function(e,t){var n=t?t.length:0,r=new Buffer(n+12);return r.writeUInt32BE(n,0),r.writeUInt32BE(e,4),t&&t.copy(r,8),r.writeInt32BE(i.crc32(r.slice(4,r.length-4)),r.length-4),r},l.prototype.packGAMA=function(e){var t=new Buffer(4);return t.writeUInt32BE(Math.floor(e*r.GAMMA_DIVISION),0),this._packChunk(r.TYPE_gAMA,t)},l.prototype.packIHDR=function(e,t){var n=new Buffer(13);return n.writeUInt32BE(e,0),n.writeUInt32BE(t,4),n[8]=this._options.bitDepth,n[9]=this._options.colorType,n[10]=0,n[11]=0,n[12]=0,this._packChunk(r.TYPE_IHDR,n)},l.prototype.packIDAT=function(e){return this._packChunk(r.TYPE_IDAT,e)},l.prototype.packIEND=function(){return this._packChunk(r.TYPE_IEND,null)}},function(e,t,n){"use strict";var r=e.exports=function(e){this._buffer=e,this._reads=[]};r.prototype.read=function(e,t){this._reads.push({length:Math.abs(e),allowLess:e<0,func:t})},r.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){var e=this._reads[0];if(!this._buffer.length||!(this._buffer.length>=e.length||e.allowLess))break;this._reads.shift();var t=this._buffer;this._buffer=t.slice(e.length),e.func.call(this,t.slice(0,e.length))}return this._reads.length>0?new Error("There are some read requests waitng on finished stream"):this._buffer.length>0?new Error("unrecognised content at end of stream"):void 0}},function(e,t,n){var r=n(156);function i(e,t){var n=e.a/255,r=t+'="'+e.hex+'"';return n<1?r+" "+t+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function o(e,t,n){var r=e+t;return void 0!==n&&(r+=" "+n),r}t.render=function(e,t,n){var a=r.getOptions(t),s=e.modules.size,l=e.modules.data,c=s+2*a.margin,u=a.color.light.a?"<path "+i(a.color.light,"fill")+' d="M0 0h'+c+"v"+c+'H0z"/>':"",d="<path "+i(a.color.dark,"stroke")+' d="'+function(e,t,n){for(var r="",i=0,a=!1,s=0,l=0;l<e.length;l++){var c=Math.floor(l%t),u=Math.floor(l/t);c||a||(a=!0),e[l]?(s++,l>0&&c>0&&e[l-1]||(r+=a?o("M",c+n,.5+u+n):o("m",i,0),i=0,a=!1),c+1<t&&e[l+1]||(r+=o("h",s),s=0)):i++}return r}(l,s,a.margin)+'"/>',f='viewBox="0 0 '+c+" "+c+'"',h='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+f+' shape-rendering="crispEdges">'+u+d+"</svg>\n";return"function"==typeof n&&n(null,h),h}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(137),o=n(144),a=n(4),s=n(141),l=n(1).default.getLogger("ThunderBirdKey");var c;!function(e){e[e.ShowUI_Decoding=0]="ShowUI_Decoding",e[e.ShowUI_Creating=1]="ShowUI_Creating",e[e.ShowUI_DecodeFailed=2]="ShowUI_DecodeFailed",e[e.ShowUI_CreateFailed=3]="ShowUI_CreateFailed",e[e.ShowUI_CreateSucc=4]="ShowUI_CreateSucc",e[e.ShowUI_CreateSuccDis=5]="ShowUI_CreateSuccDis"}(c=t.ShowType||(t.ShowType={}));class u{constructor(){this.lastBirdKey=[]}static GetInstance(){return u.instance||(u.instance=new u),u.instance}setLastBirdKey(e){if(e&&0!==e.length){this.lastBirdKey=[];for(let t=0;t<e.length;t++)this.lastBirdKey.push(e[t])}}getLastBirdKey(){return this.lastBirdKey}isSameBirdKey(e){if(!this.lastBirdKey)return!1;if(e.length!==this.lastBirdKey.length)return!1;let t=0;for(let n=0;n<e.length;n++)e[n]===this.lastBirdKey[n]&&t++;return t===e.length}}function d(e,t,n,r,o){let a={url:"/dlj_create",data:Object.assign({url:e,"Chat-Version":2},o),method:"post"};return"string"==typeof n&&(a.data.type=n),t&&(a.data.uid=t),"string"==typeof r&&(a.data.passwd=r),i.fetchSlRes(a)}function f(e,t,n,r){return d(e,t,n,void 0,r)}u.instance=null,t.encodeBirdKey=d,t.setBirdKeyPriority=function(e,t,n=!1){return i.fetchSlRes({url:"/dlj/option",data:{url:e,priority:n,"Chat-Version":2},method:"put"})},t.queryBirdKeyInfo=f,t.getUserNameByID=function(e){return i.fetchSlRes({url:"/user_info/pc_info",data:{uid:e}})},t.decodeBirdKey=function(e){return r(this,void 0,void 0,function*(){let t=yield a.client.callServerFunction("GetThunderVersion");return i.fetchSlRes({url:"/dlj/bird_key",data:{_h:{"Chat-Version":2,"Version-Name":t},key:e.toLowerCase()},method:"get"})})},t.openBirdKeyEncodeDialog=function(e,t){return r(this,void 0,void 0,function*(){let n=function(e,t){l.information("弹出生成迅雷口令面板,begin,url=",e),o.MessageBox.custom("BirdKeyShow",{showType:c.ShowUI_Creating,url:e,statData:t},{modal:!1}).catch(e=>{l.information("弹出生成迅雷口令面板,error=",e)}),l.information("弹出生成迅雷口令面板,end")};l.information("调用promise IsLogined 函数 Begin");let i=yield a.client.callServerFunction("IsLogined");l.information("调用promise IsLogined 函数 End, isLogined=",i),i?n(e,t):f(e).then(i=>r(this,void 0,void 0,function*(){if("object"!=typeof i)return Promise.resolve();if(!i.data)return Promise.resolve();if("encrypted"===i.data.type){let r=yield s.default(!1,!0,"birdkey");if(!r||""===r)return;n(e,t)}else n(e,t);return Promise.resolve()})).catch(e=>{l.information("调用queryBirdKeyInfo 接口异常 error=",e)})})},t.openBirdKeyDecodeDialog=function(e,t){o.MessageBox.custom("BirdKeyShow",{showType:c.ShowUI_Decoding,birdkeyChars:e,statData:t},{modal:!1}).catch()},t.saveVestUrlToConfig=function(e,t){let n=t?"vestURL":"commonUrl";a.client.callServerFunction("GetValue","BirdKeyVest",n,[]).then(t=>{let r=t||[];""!==e&&-1===r.join().indexOf(e)&&(r.push(e),a.client.callServerFunction("SetValue","BirdKeyVest",n,r).catch())}).catch()},t.isVestUrl=function(e,t){return r(this,void 0,void 0,function*(){let n=yield a.client.callServerFunction("GetValue","BirdKeyVest",t,[]);return""!==e&&-1!==(n||[]).join().indexOf(e)})},t.getBirdKeyMgr=function(){return u.GetInstance()}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.r(t);var r=n(266),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(7),a=n(5),s=n(713),l=n(16),c=n(4),u=n(113),d=n(144),f=n(24);n(64).FixTextScale.fixZoomFactory();let h=class extends a.Vue{handleViewChange(e){return i(this,void 0,void 0,function*(){let e=yield o.asyncRemoteCall.getCurrentWindow();yield e.hide(),d.MessageBox.custom("batch-new-task-ctrl",{windowWidth:500,windowHeight:557}).then(t=>{if(t.action===f.MessageBoxNS.Action.OK){e.show();let n=t.args.picked,r=t.args.urlExample,i=t.args.from,o=t.args.to,a=t.args.wildcardLen;this.handleBatchInfoChange(n,r,i,o,a)}else e.show()})})}padLeftZero(e,t){if(t.length>e)return t;return("000000".substring(0,e)+t).substr(t.length)}getBatchUrls(e,t,n,r,i){let o=[];if("number"===e){let e=n,a=r;if(e>a)for(let n=e;n>=a;n--){let e=this.padLeftZero(i,n+""),r=t.replace(/\(\*\)/,e);o.push(r)}else for(let n=e;n<=a;n++){let e=this.padLeftZero(i,n+""),r=t.replace(/\(\*\)/,e);o.push(r)}}else if("alpha"===e){let e=n,i=r;if(l.ThunderUtil.isUpperCase(e)&&l.ThunderUtil.isLowerCase(i)){for(let n=e.charCodeAt(0);n>="A".charCodeAt(0);n--){let e=t.replace(/\(\*\)/,String.fromCharCode(n));o.push(e)}for(let e="z".charCodeAt(0);e>=i.charCodeAt(0);e--){let n=t.replace(/\(\*\)/,String.fromCharCode(e));o.push(n)}}else if(l.ThunderUtil.isLowerCase(e)&&l.ThunderUtil.isUpperCase(i)){for(let n=e.charCodeAt(0);n<="z".charCodeAt(0);n++){let e=t.replace(/\(\*\)/,String.fromCharCode(n));o.push(e)}for(let e="A".charCodeAt(0);e<=i.charCodeAt(0);e++){let n=t.replace(/\(\*\)/,String.fromCharCode(e));o.push(n)}}else if(e>i)for(let n=e.charCodeAt(0);n>=i.charCodeAt(0);n--){let e=t.replace(/\(\*\)/,String.fromCharCode(n));o.push(e)}else for(let n=e.charCodeAt(0);n<=i.charCodeAt(0);n++){let e=t.replace(/\(\*\)/,String.fromCharCode(n));o.push(e)}}return o}handleBatchInfoChange(e,t,n,r,i){let o=this.getBatchUrls(e,t,n,r,i),a=[];for(let e of o){let t=u.ThunderNewtaskHelperNS.getNewTaskDataByUrl(e,"newwindow_batch");void 0!==t&&a.push(t)}this.$refs.ctrl.handleIpcMessage(null,JSON.stringify(a))}created(){return i(this,void 0,void 0,function*(){let e=yield c.client.callServerFunction("GetConfigValue","TaskDefaultSettings","OrignHostThreads",5);e&&u.ThunderNewtaskHelperNS.updateThreadCount(e)})}};h=r([a.Component({components:{PreNewTaskCtrlContainer:s.default}})],h),t.default=h},function(e,t,n){"use strict";n.r(t);var r=n(268),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(714),i=n(163);t.default=i.connector.connect({mapStateToProps:{savePath:e=>e.PreNewTask.savePath,cloudPath:e=>e.PreNewTask.cloudPath,downMode:e=>e.PreNewTask.downMode,urlList:e=>e.PreNewTask.urlList,magnetList:e=>e.PreNewTask.magnetList,groupChecked:e=>e.PreNewTask.groupChecked,groupName:e=>e.PreNewTask.groupName},mapCommitToProps:{HANDLE_URLS_CHANGE:"HANDLE_URLS_CHANGE",HANDLE_URLS_INSERT:"HANDLE_URLS_INSERT",HANDLE_DOWN_MODE_CHANGE:"HANDLE_DOWN_MODE_CHANGE",HADNLE_SETTING_CHANGE:"HADNLE_SETTING_CHANGE",HANDLE_RENAME:"HANDLE_RENAME",HANDLE_SELECT:"HANDLE_SELECT",HANDLE_SAVE_PATH_CHANGE:"HANDLE_SAVE_PATH_CHANGE",HANDLE_CLOUD_PATH_CHANGE:"HANDLE_CLOUD_PATH_CHANGE",HANDLE_GROUP_CHECK_CHANGE:"HANDLE_GROUP_CHECK_CHANGE",HANDLE_GROUP_NAME_CHANGE:"HANDLE_GROUP_NAME_CHANGE",HANDLE_TASKDATA_INSERT:"HANDLE_TASKDATA_INSERT"},mapDispatchToProps:{},mapGettersToProps:{fileNameList:e=>e.fileNameList,selectedDownlist:e=>e.selectedDownlist}})(r.default)},function(e,t,n){"use strict";n.r(t);var r=n(270),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(3),a=n(7),s=n(2),l=n(5),c=n(4),u=n(11),d=n(181),f=n(715),h=n(182),p=n(15),g=n(113),m=n(92),v=n(16),_=n(91),y=n(20),w=n(37),b=n(135),C=n(141),E=n(1),T=n(25),k=n(226),S=n(24),R=n(144),x=n(6),P=E.default.getLogger("ThunderNewTask.PreNewTask");let N=class extends l.Vue{constructor(){super(...arguments),this.showContent=!0,this.downloadTipText="",this.context="",this.tipsText="",this.pathTips="",this.showPathTips=!1,this.isShowTips=!1,this.preGroupName="",this.extendData=null,this.isSelectInstallCheck=!1,this.newPaths=[],this.privateSpace=!1,this.hasChangePath=!1,this.logicChoosed=!0,this.cloudChoosed=!1,this.groupBirdKey=[],this.clickFrom=""}onOperateVisibleChange(e){this.adjustHeight()}onListShow(){return i(this,void 0,void 0,function*(){let e=yield a.asyncRemoteCall.getCurrentWindow();(yield e.isVisible())||this.$nextTick(()=>{e.show()})})}get visibleCount(){return 0===this.urlList.length?0:4}get disabled(){let e=!0;do{if(this.downloadTipText)break;if(0===this.selectCount)break;e=!this.logicChoosed&&!this.cloudChoosed}while(0);return e}get isShowInstall(){let e=!1;return this.groupChecked&&null!==this.extendData&&void 0!==this.extendData&&null!==this.extendData.installFile&&void 0!==this.extendData.installFile&&""!==this.extendData.installFile&&(e=!0),e}get taskCount(){return this.urlList.length}get selectCount(){return this.selectedDownlist.length}get selectSize(){let e=0;for(let t of this.selectedDownlist)e+=t.data.fileSize;return e}get defaultGroupNamePrefix(){return w.TaskUtilHelper.getDefaultGroupPrefix(this.fileNameList)}get dropEnable(){return this.logicChoosed||!this.logicChoosed&&!this.cloudChoosed}adjustHeight(){return i(this,void 0,void 0,function*(){let e=yield a.asyncRemoteCall.getCurrentWindow(),t=320;this.visibleCount>0&&(t=525);let n=yield e.getSize();yield e.setMinimumSize(500,t);do{if(n[0]>=500&&n[1]>=t)break;e.setSize(n[0]>500?n[0]:500,n[1]>t?n[1]:t,!0)}while(0)})}handleBirdKey(){}handleShowContent(){this.showContent=!0}handleInputFinish(){return i(this,void 0,void 0,function*(){let e=yield this.handleInputChange(this.context);return e&&(this.showContent=!1,this.context=""),e})}handleInputUnfold(){this.showContent=!1}getUrlFromBirdKey(e){return i(this,void 0,void 0,function*(){let t=void 0;do{let n=m.URLHelperNS.isBirdKey(e);if(!n||0===n.length)break;let r=n[0].length,i=yield k.decodeBirdKey(n[0].substring(2,r-1));i.data&&"ok"===i.data.result&&(t=x.isString(i.data.url)&&-1===i.data.url.indexOf("%")?i.data.url:decodeURIComponent(i.data.url))}while(0);return t})}handleInputChange(e){return i(this,void 0,void 0,function*(){let t=!0,n=e.trim(),r=[];""!==n&&(r=n.split("\n")),1===r.length&&(r=n.split(/[\n\r\n]/));let i=[],o=[],a=[],s=[],l=[],u=0,d=[],f=0,h=!1;for(let e=0;e<r.length;e++){let t=r[e],n=void 0;if(h=!1,(d=m.URLHelperNS.isBirdKey(t))&&d.length>0){n=t,f=d[0].length;let e=yield k.decodeBirdKey(d[0].substring(2,f-1));if(P.information("ctrl+v输入迅雷口令并解析,url:",t,d," data：",e),e.data&&"ok"===e.data.result){if(t=x.isString(e.data.url)&&-1===e.data.url.indexOf("%")?e.data.url:decodeURIComponent(e.data.url),"encrypted"===e.data.type){let r,i={url:t,stat:"prenewtaskctrl",type:e.data.type,password:e.data.passwd,userid:e.data.uid,birdkeyChars:n},o=!1;if(o=(r=yield R.MessageBox.custom("BridKeyPwdcheck",{taskUrlItem:i})).action!==S.MessageBoxNS.Action.OK||(!r.args||!r.args.success))continue}if(h=!0,t.includes("taskGroupName")){if(this.urlList.length>0)continue;-1===s.indexOf(t)&&(s.push(t),l.push({url:t,birdkeyChars:n}));continue}k.saveVestUrlToConfig(t,"vest"===e.data.type)}}if(h||(t=m.URLHelperNS.fixMagnetUrl(t)),!1===m.URLHelperNS.isUrlValid(t))continue;if(u++,this.urlList.indexOf(t)>-1||o.indexOf(t)>-1)continue;let c=T.ThunderHelper.getTaskTypeFromUrl(t);if(c===p.DownloadKernel.TaskType.P2sp||c===p.DownloadKernel.TaskType.Emule){if(this.urlList.length>=1e3)break;i.push({url:t,type:c,birdKey:n})}else o.push(t),a.push({url:t,birdkeyChars:n})}return this.groupBirdKey=l,i.length>0&&this.HANDLE_URLS_CHANGE(i),a&&a.length>0&&(c.client.callServerFunction("CreateBtTaskWindow",a,this.clickFrom),0===i.length&&0===this.groupBirdKey.length&&0===this.urlList.length&&(yield this.close(S.MessageBoxNS.Action.OK))),0===u&&0===a.length&&0===this.groupBirdKey.length&&(this.tipsText="请输入有效的下载地址",this.isShowTips=!0,t=!1),t})}handleChangeLogicPath(e,t){do{if(""===e&&!t)break;if(this.hasChangePath&&!t)break;t&&(this.hasChangePath=!0),this.HANDLE_SAVE_PATH_CHANGE(e)}while(0)}handleChangeCloudPath(e){this.HANDLE_CLOUD_PATH_CHANGE(e)}handlePathCheckChange(e,t){"cloud"===t?this.cloudChoosed=e:"logic"===t&&(this.logicChoosed=e)}handleSpaceDanger(e){e&&1===this.downMode&&this.HANDLE_DOWN_MODE_CHANGE(0)}close(e){return new Promise(t=>{setTimeout(()=>{o.ipcRenderer.send(y.ThunderChannelList.channelRMPreNewTaskClose,e),window.close(),t()},10)})}handleClose(){return i(this,void 0,void 0,function*(){u.XLStatNS.trackEvent("core_event","create_task_panel_click","",0,0,0,0,"source=Thunder,panel=PreNewTaskDlg,button=close"),yield this.close(S.MessageBoxNS.Action.Close)})}handleImportTorrent(){return i(this,void 0,void 0,function*(){let e=yield a.asyncRemoteCall.getCurrentWindow();e.hide(),void 0===(yield c.client.callServerFunction("ImportTorrent",{source:"newwindow_bt",clickFrom:this.clickFrom}))?e.show():e.close()})}handleAddBatchTask(){this.$emit("view-change","BatchNewTaskCtrlContainer")}onOperatorDrop(){return i(this,void 0,void 0,function*(){let e=[{label:"添加BT任务",click:()=>{u.XLStatNS.trackEvent("core_event","create_task_panel_click","",0,0,0,0,"source=Thunder,panel=PreNewTaskDlg,button=addbt"),this.handleImportTorrent().catch()}},{label:"添加批量任务",click:()=>{u.XLStatNS.trackEvent("core_event","create_task_panel_click","",0,0,0,0,"source=Thunder,panel=PreNewTaskDlg,button=addbatch"),this.handleAddBatchTask()}}];const t=yield a.asyncRemoteCall.getCurrentWindow();const r=yield(yield a.asyncRemoteCall.getMenu()).buildFromTemplate(e),{MenuSkinNS:i}=yield Promise.resolve().then(()=>n(63));yield i.setStyle(r,{}),r.popup({window:t,async:!1})})}downloadImmediately(e,t,n){return i(this,void 0,void 0,function*(){let r=void 0;P.information("开始下载",t),this.selectedDownlist.length>1&&this.groupChecked&&""===(r=this.groupName)&&(r="-"),this.isShowInstall&&(this.extendData.isSelectInstallCheck=this.isSelectInstallCheck);let i=JSON.stringify(Object.assign({},this.selectedDownlist)),o=yield g.ThunderNewtaskHelperNS.getDownloadResult(t,e,i,this.privateSpace,this.magnetList,r,this.extendData,this.groupBirdKey,this.cloudChoosed?this.cloudPath:void 0,n);if(o.result){let e=!0;null!==this.extendData&&void 0!==this.extendData&&null!==this.extendData.isNotSavePath&&void 0!==this.extendData.isNotSavePath&&!0===this.extendData.isNotSavePath&&(e=!1),this.privateSpace&&(e=!1),e&&b.HistoryPathsNS.addHistoryPaths(t),yield this.close(S.MessageBoxNS.Action.OK)}else o.message&&(o.pathError?(this.pathTips=o.message,this.showPathTips=!0):(this.tipsText=o.message,this.isShowTips=!0))})}handleInputFinishAndDownload(e){return i(this,void 0,void 0,function*(){do{if(this.showContent){if(this.context){yield this.handleInputFinish();break}yield this.handleInputUnfold();break}yield this.handleDownload(e);break}while(0)})}concatChannle(e){let t=`other/${e}`;do{if(!this.clickFrom||!this.cloudChoosed)break;if("yunpan"!==this.clickFrom&&"download"!==this.clickFrom&&"clipboard"!==this.clickFrom)break;t=`${this.clickFrom}/${e}`}while(0);return t}handleDownload(e){return i(this,void 0,void 0,function*(){let t=1===e?"download":"manul",n="";this.logicChoosed&&this.cloudChoosed?n="yunpan_and_computer":this.logicChoosed?n="computer":this.cloudChoosed&&(n="yunpan");let r=this.$refs.downloadTo,i=`source=Thunder,panel=PreNewTaskDlg,button=${t},box_state=${n},lack_space_bubble_show=${r.lackSpaceShowed?1:0}`;u.XLStatNS.trackEvent("core_event","create_task_panel_click","",0,0,0,0,i);do{if(this.tipsText="",this.isShowTips=!1,0===this.taskCount&&0===this.groupBirdKey.length){this.tipsText="请输入有效的下载地址",this.isShowTips=!0;break}if(0===this.selectedDownlist.length&&0===this.groupBirdKey.length){this.tipsText="未选择下载任务",this.isShowTips=!0;break}if(!(yield r.verify()))break;if(this.logicChoosed){let t=this.concatChannle("add_url/yunpan_and_download"),n=this.savePath;if("私人空间"===this.savePath){let e=yield C.default(!1,!0,"privatespace");if(!e||""===e)return;let t=yield c.client.callServerFunction("GetPrivateSavePath",e);t=s.join(t,e),n=t,this.privateSpace=!0}else this.privateSpace=!1;this.HANDLE_DOWN_MODE_CHANGE(e),this.$nextTick(()=>{this.downloadImmediately(e,n,t).catch()})}else if(this.cloudChoosed){let e=this.concatChannle("add_url/only_yunpan"),t=void 0;this.selectedDownlist.length>1&&this.groupChecked&&""===(t=this.groupName)&&(t="-"),yield c.client.callServerFunction("AddUrls2Cloud",[...this.selectedDownlist],this.cloudPath,t,void 0,void 0,e),yield this.close(S.MessageBoxNS.Action.OK)}}while(0)})}handlePopInputMenu(e){return i(this,void 0,void 0,function*(){const{MenuSkinNS:e}=yield Promise.resolve().then(()=>n(63));e.popEditableDefaultContextMenu(e=>{let t=[];if(e.isEditable){let n=v.ThunderUtil.isClipboardTextFormatAvailable();t.push({label:"粘贴并立即下载",click:()=>i(this,void 0,void 0,function*(){(yield a.asyncRemoteCall.getCurrentWebContents()).paste(),setTimeout(()=>i(this,void 0,void 0,function*(){this.handleInputFinishAndDownload(1)}),20)}),enabled:e.editFlags.canPaste&&n})}return t},-4)})}handleSetting(e,t,n,r){this.HADNLE_SETTING_CHANGE({url:e,setting:t,newUrl:n,type:r})}handleGroupCheck(e){let t=void 0;if(e){let e="source=Thunder,panel=PreNewTaskDlg,button=grouptask";if(u.XLStatNS.trackEvent("core_event","create_task_panel_click","",0,0,0,0,e),null!==this.preGroupName&&void 0!==this.preGroupName&&""!==this.preGroupName)t=this.preGroupName;else{t=this.defaultGroupNamePrefix+_.TimeHelperNS.formatDate(new Date,"_yyyyMMdd_hhmm")}}this.HANDLE_GROUP_CHECK_CHANGE({checked:e,name:t})}handleGroupNameChange(e){e=e.trim(),this.groupChecked&&(this.preGroupName=e,this.HANDLE_GROUP_NAME_CHANGE(e))}created(){return i(this,void 0,void 0,function*(){"yunpan"===v.ThunderUtil.getQueryString(location.href,"from")?(this.logicChoosed=!1,this.cloudChoosed=!0):(this.logicChoosed=yield c.client.callServerFunction("GetConfigValue","TaskDefaultSettings","LogicChoosed",!0),this.cloudChoosed=yield c.client.callServerFunction("GetConfigValue","TaskDefaultSettings","CloudChoosed",!0),window.__xadsConfigCloudeChoosedGet=!0)})}handleIpcMessage(e,t,n,r,s,l){return i(this,void 0,void 0,function*(){P.information("xxxx ipc msg",l),this.downloadTipText=l?"正在解析下载链接...":"",P.verbose(e);do{if(!t){let e=o.clipboard.readText();if(e&&e.length>0&&-1===e.indexOf("(*)")){const t=["http://","https://","ftp://","magnet:?","thunder://","ed2k://"];for(let n=0;n<t.length;++n)if(e.indexOf(t[n])>=0){this.context=e;break}}let t=yield a.asyncRemoteCall.getCurrentWindow();(yield t.isVisible())||(yield t.center(),yield t.show());break}let e=null;try{e=JSON.parse(t)}catch(e){P.warning(e)}if(void 0===e||null===e||0===e.length||void 0===e[0])break;let i=this.urlList,u=[],d=[];for(let t of e){let e=yield this.getUrlFromBirdKey(t.url);e&&(t.url=e);let n=-1===i.indexOf(t.url),r=T.ThunderHelper.getTaskTypeFromUrl(t.url),o=r===p.DownloadKernel.TaskType.P2sp||r===p.DownloadKernel.TaskType.Emule;if(n&&o){u.push(t);continue}let a=-1===[...this.magnetList,...d].map(e=>e.url).indexOf(t.url),s=r===p.DownloadKernel.TaskType.Bt;a&&s&&d.push(t)}if(0===u.length){if(e.length>0){d.length>0&&c.client.callServerFunction("CreateBtTaskWindow",d,this.clickFrom);let e=yield a.asyncRemoteCall.getCurrentWindow();(yield e.isVisible())||(0!==this.urlList.length||l?(yield e.center(),yield e.show()):yield e.close())}return}let f=void 0;null!==s&&void 0!==s&&void 0!==s.threadCount&&"number"==typeof s.threadCount&&s.threadCount>0&&(f={loginFtp:!1,ftpInfo:{userName:"",password:""},onlyOrigin:!1,thread:s.threadCount,note:"",openAfterDownload:!1});let h=u.map(e=>g.ThunderNewtaskHelperNS.contructTaskByUrlData(e,f));setTimeout(()=>{this.handleInputUnfold(),this.HANDLE_TASKDATA_INSERT(h)},0),void 0!==n&&""!==n&&null!==n&&(this.hasChangePath||(this.hasChangePath=!0,this.HANDLE_SAVE_PATH_CHANGE(n))),void 0!==r&&""!==r&&null!==r&&(this.preGroupName=r,this.HANDLE_GROUP_CHECK_CHANGE({checked:!0,name:r})),null!==s&&void 0!==s&&(this.extendData=s)}while(0)})}handleDocClick(){this.isShowTips=!1,this.showPathTips=!1}handleKeyDown(e){e.ctrlKey&&13===e.keyCode&&this.handleInputFinishAndDownload(this.downMode)}mounted(){return i(this,void 0,void 0,function*(){"1"===v.ThunderUtil.getQueryString(location.href,"hasTask")&&(this.showContent=!1);let e=v.ThunderUtil.getQueryString(location.href,"from");e&&(this.clickFrom=e),o.ipcRenderer.on(y.ThunderChannelList.channelRMPreNewTaskSetTaskData,this.handleIpcMessage),document.addEventListener("click",this.handleDocClick),document.addEventListener("keydown",this.handleKeyDown),(yield a.asyncRemoteCall.getCurrentWindow()).hookWindowMessage(528,()=>{this.isShowTips=!1,this.showPathTips=!1})})}destroyed(){o.ipcRenderer.removeListener(y.ThunderChannelList.channelRMPreNewTaskSetTaskData,this.handleIpcMessage),document.removeEventListener("click",this.handleDocClick),document.removeEventListener("keydown",this.handleKeyDown)}};r([l.Prop({})],N.prototype,"savePath",void 0),r([l.Prop({})],N.prototype,"cloudPath",void 0),r([l.Prop({})],N.prototype,"groupChecked",void 0),r([l.Prop({})],N.prototype,"groupName",void 0),r([l.Prop({})],N.prototype,"downMode",void 0),r([l.Prop({})],N.prototype,"urlList",void 0),r([l.Prop({})],N.prototype,"fileNameList",void 0),r([l.Prop({})],N.prototype,"magnetList",void 0),r([l.Prop({})],N.prototype,"selectedDownlist",void 0),r([l.Prop({})],N.prototype,"HANDLE_URLS_CHANGE",void 0),r([l.Prop({})],N.prototype,"HANDLE_URLS_INSERT",void 0),r([l.Prop({})],N.prototype,"HANDLE_TASKDATA_INSERT",void 0),r([l.Prop({})],N.prototype,"HANDLE_DOWN_MODE_CHANGE",void 0),r([l.Prop({})],N.prototype,"HANDLE_SAVE_PATH_CHANGE",void 0),r([l.Prop({})],N.prototype,"HANDLE_CLOUD_PATH_CHANGE",void 0),r([l.Prop({})],N.prototype,"HANDLE_GROUP_CHECK_CHANGE",void 0),r([l.Prop({})],N.prototype,"HANDLE_GROUP_NAME_CHANGE",void 0),r([l.Prop({})],N.prototype,"HADNLE_SETTING_CHANGE",void 0),r([l.Watch("visibleCount")],N.prototype,"onOperateVisibleChange",null),r([l.Watch("urlList")],N.prototype,"onListShow",null),N=r([l.Component({components:{DownMode:d.default,NewtaskQrcode:f.default,DownloadTo:h.default,DownInfoContainer:()=>Promise.resolve().then(()=>n(717))}})],N),t.default=N},function(e,t,n){"use strict";n.r(t);var r=n(272),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5),a=n(15),s=n(4),l=n(11),c=n(142),u=n(1),d=n(183),f={margin:0,width:108},h=u.default.getLogger("newtask-qrcode");let p=class extends o.Vue{constructor(){super(...arguments),this.showGuideTip=!1,this.tipVisible=!1,this.lastTaskUrl="",this.shareUrl=""}onTipVisibleChanged(e){return i(this,void 0,void 0,function*(){if(e){let e=document.getElementById("qrcodecanvas"),t=yield this.generalShareUrl(),n="";!t||t.length<=0?(t="https://mobile.xunlei.com/m/channel.html?from=task_panel",n="download_sl"):n="source",h.information("tipVisible url",t),d.toCanvas(e,t,f,e=>{h.information("QRCode.toCanvas error = ",e)});let r=`type=${n},fileurl=${encodeURIComponent(t)}`;l.XLStatNS.trackEvent("core_event","create_task_panel_qr_hover","",0,0,0,0,r)}})}isSupportTaskType(e){return e===a.DownloadKernel.TaskType.P2sp||e===a.DownloadKernel.TaskType.Emule}getBtTaskVideoIndex(){return i(this,void 0,void 0,function*(){let e=-1;{let t=this.btTaskInfo.fileLists;const{TaskUtilHelper:r}=yield Promise.resolve().then(()=>n(37));for(let n=0;n<t.length;++n){let i=t[n];if(i&&r.isVideoFileExt(i.fileName)){e=n;break}}}return e})}getBtTaskUrl(){let e="";return(e=this.originUrl)&&""!==e||(e="magnet:?xt=urn:btih:"+this.btTaskInfo.infoId),e}generalShareUrl(){return i(this,void 0,void 0,function*(){let e="";do{if(this.taskType===a.DownloadKernel.TaskType.Bt){if(this.shareUrl&&this.shareUrl.length>0){e=this.shareUrl;break}let t=this.getBtTaskUrl(),n=this.btTaskInfo.infoId;if(!t||t.length<=0||!n||n.length<=0){h.information("taskUrl",t,"gcid",n);break}let r=this.btTaskInfo.title,i="",o=0;this.btTaskInfo.fileLists.forEach(e=>{o+=e.fileSize});let a=yield this.getBtTaskVideoIndex();this.shareUrl=yield s.client.callServerFunction("GetShouleiShareLinkDlj2",t,r,i,o,n,a,"pic","task_panel"),h.information("GetShouleiShareLinkDlj2",this.shareUrl)}else{let t=null,n=0;for(let e=0;e<this.downList.length&&!(this.downList[e].selected&&(++n,t||(t=this.downList[e]),n>=2));++e);if(1!==n||!t){h.information("this.downList",this.downList);break}if(!this.isSupportTaskType(t.taskType)){h.information("selectItem",t);break}let r=this.downList[0].data.url,i=this.downList[0].data.gcid;if(!r||r.length<=0||!i||i.length<=0){h.information("taskUrl",r,"gcid",i);break}if(this.lastTaskUrl===r){e=this.shareUrl,h.information("last shareUrl",this.shareUrl);break}let o=this.downList[0].data.fileName,a=this.downList[0].data.referer,l=this.downList[0].data.fileSize,c=-1;this.shareUrl=yield s.client.callServerFunction("GetShouleiShareLinkDlj2",r,o,a,l,i,c,"pic","task_panel"),h.information("GetShouleiShareLinkDlj2",this.shareUrl),this.shareUrl&&this.shareUrl.length>0&&(e=this.shareUrl,this.lastTaskUrl=r)}}while(0);return e})}mounted(){return i(this,void 0,void 0,function*(){this.generalShareUrl().catch(),this.showGuideTip=yield c.ConfigHelperNS.getValue("Share","TaskPanelGuideTip",!0),this.showGuideTip&&c.ConfigHelperNS.setValue("Share","TaskPanelGuideTip",!1).catch()})}};r([o.Prop({})],p.prototype,"downList",void 0),r([o.Prop({})],p.prototype,"taskType",void 0),r([o.Prop({})],p.prototype,"btTaskInfo",void 0),r([o.Prop({})],p.prototype,"originUrl",void 0),r([o.Watch("tipVisible")],p.prototype,"onTipVisibleChanged",null),p=r([o.Component({})],p),t.default=p},function(e,t,n){"use strict";n.r(t);var r=n(274),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(718),i=n(163);t.default=i.connector.connect({mapStateToProps:{urlList:e=>e.PreNewTask.urlList,groupChecked:e=>e.PreNewTask.groupChecked,groupName:e=>e.PreNewTask.groupName},mapCommitToProps:{HADNLE_SETTING_CHANGE:"HADNLE_SETTING_CHANGE"},mapDispatchToProps:{fetchTaskSize:"fetchTaskSize",batchFetchSize:"batchFetchSize"},mapGettersToProps:{totalSize:e=>e.totalSize,selectedDownlist:e=>e.selectedDownlist}})(r.default)},function(e,t,n){"use strict";n.r(t);var r=n(276),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(t,"__esModule",{value:!0});const i=n(5),o=n(16),a=n(719),s=n(169),l=n(25),c=n(15);let u=class extends i.Vue{constructor(){super(...arguments),this.groupEditShow=!1,this.current="",this.currentTaskType=c.DownloadKernel.TaskType.Unkown,this.settingVisible=!1,this.tempSettingInfo={loginFtp:!1,ftpInfo:{userName:"",password:""},onlyOrigin:!1,thread:5,note:"",openAfterDownload:!1},this.tempUrl="",this.tipsText="",this.isShowTips=!1,this.loadedIndex=0}onGroupChecked(e){e&&(this.groupEditShow=!0)}onTaskListChanged(){this.batchFetchSize()}get currentIsP2sp(){return this.currentTaskType===c.DownloadKernel.TaskType.P2sp}get taskCount(){return this.urlList.length}get totalSizeFormat(){return o.ThunderUtil.formatSize(this.totalSize)}get loadedList(){let e=this.loadedIndex+20;return e>this.taskCount&&(e=this.taskCount),this.urlList.slice(0,e)}get selectFileCount(){return this.selectedDownlist.length}get groupVisible(){let e=!1;return this.selectFileCount>1&&(e=!0),e}get groupLabel(){let e="";return this.groupChecked||(e="合并为任务组"),e}get curThread(){let e="";return this.currentIsP2sp&&(e=""+this.tempSettingInfo.thread),e}get groupNameDisplay(){let e=this.groupName;return""!==this.groupName&&void 0!==this.groupName||(e="-"),e}loadMore(){this.loadedIndex+20>this.taskCount?this.loadedIndex=this.taskCount:this.loadedIndex+=20}mounted(){document.addEventListener("click",this.handleDocClick)}destroyed(){document.removeEventListener("click",this.handleDocClick)}handleSetting(e){this.tempSettingInfo=o.ThunderUtil.deepCopy(e.setting),this.tempUrl=e.data.url,this.current=e.data.url,this.currentTaskType=e.taskType,this.settingVisible=!0}handleGroupCheckChange(e){this.$emit("group",e);let t=this.$refs.groupedit.$el.querySelector("input");null!==t&&void 0!==t&&this.$nextTick(()=>{t.select()})}handleGroupNameChange(e){this.$emit("groupname-change",e)}checkP2spUrl(e){let t=!1,n=l.ThunderHelper.getTaskTypeFromUrl(e);return n!==c.DownloadKernel.TaskType.P2sp&&n!==c.DownloadKernel.TaskType.Emule||(t=!0),t}showTip(){this.tipsText="请输入有效的下载地址",this.isShowTips=!0}handleApplyAll(){this.checkP2spUrl(this.tempUrl)?(this.settingVisible=!1,this.$emit("setted",this.current,this.tempSettingInfo,this.tempUrl,"all")):this.showTip()}handleApply(){this.checkP2spUrl(this.tempUrl)?(this.settingVisible=!1,this.$emit("setted",this.current,this.tempSettingInfo,this.tempUrl,"single")):this.showTip()}handleDocClick(){this.settingVisible=!1,this.groupEditShow=!1,this.isShowTips=!1}stopSettingClickPropagation(e){this.isShowTips=!1,e.stopPropagation()}};r([i.Prop({})],u.prototype,"groupChecked",void 0),r([i.Prop({})],u.prototype,"groupName",void 0),r([i.Prop({})],u.prototype,"urlList",void 0),r([i.Prop({})],u.prototype,"totalSize",void 0),r([i.Prop({})],u.prototype,"selectedDownlist",void 0),r([i.Prop({})],u.prototype,"fetchTaskSize",void 0),r([i.Prop({})],u.prototype,"batchFetchSize",void 0),r([i.Watch("groupChecked",{immediate:!0})],u.prototype,"onGroupChecked",null),r([i.Watch("urlList",{immediate:!0})],u.prototype,"onTaskListChanged",null),u=r([i.Component({components:{DownItemContainer:a.default,LimitInput:s.default}})],u),t.default=u},function(e,t,n){"use strict";n.r(t);var r=n(278),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(720),i=n(163);t.default=i.connector.connect({mapStateToProps:{urlTaskData:(e,t)=>{let n=t.url,r=null;return e.PreNewTask.dataMap.hasOwnProperty(n)&&(r=e.PreNewTask.dataMap[n]),r}},mapCommitToProps:{HADNLE_SETTING_CHANGE:"HADNLE_SETTING_CHANGE",HANDLE_RENAME:"HANDLE_RENAME",HANDLE_SELECT:"HANDLE_SELECT"},mapDispatchToProps:{},mapGettersToProps:{}})(r.default)},function(e,t,n){"use strict";n.r(t);var r=n(280),i=n.n(r);for(var o in r)"default"!==o&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=i.a},function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(5),a=n(16),s=n(37),l=n(139);let c=class extends o.Vue{constructor(){super(...arguments),this.tempName="",this.editing=!1,this.titleOverFlow="",this.checkboxSlot="",this.optionsWithDefault=[]}get name(){return this.urlTaskData.data.fileName}get type(){let e="",t=i.extname(this.name);return""!==t&&(e=t.substring(1)),e||"未知"}get size(){return this.urlTaskData.data.fileSize<0?0:this.urlTaskData.data.fileSize}get selected(){return this.urlTaskData.selected}get foramtSize(){return a.ThunderUtil.formatSize(this.size)}get taskIcon(){return s.TaskUtilHelper.getTaskIcon(this.name,void 0,"xly-type-")}onNameChange(e){this.$nextTick(()=>{this.checkboxSlot=a.ThunderUtil.getOmitName(this.name,this.$refs.nameSpan)})}handleTaskNameMouseOver(e){do{if(!0===this.editing){this.titleOverFlow="";break}this.checkboxSlot===this.name?this.titleOverFlow="":this.titleOverFlow=this.name}while(0)}handleTaskNameClick(e){e.stopPropagation(),e.preventDefault(),this.renameClick()}handleDocClick(){this.editing=!1}mounted(){this.checkboxSlot=a.ThunderUtil.getOmitName(this.name,this.$refs.nameSpan),document.addEventListener("click",this.handleDocClick),this.optionsWithDefault.push(this.name)}destroyed(){document.removeEventListener("click",this.handleDocClick)}renameClick(){this.editing=!this.editing,this.$nextTick(()=>{let e=this.$refs.renameSelect.$el.querySelector("input");if(e&&e.value)do{let t=i.extname(e.value);if(""===t){e.select();break}if(s.TaskUtilHelper.getTaskFileType(void 0,t)===s.TaskUtilHelper.FileExtType.Unkown){e.select();break}e.focus(),e.setSelectionRange(0,e.value.length-t.length)}while(0)})}settingClick(e){this.$emit("setting",this.urlTaskData),e.stopPropagation()}handleRenameTemp(e,t){this.tempName=e,t&&(this.editing=!1,void 0!==e&&""!==e&&e!==this.name&&this.HANDLE_RENAME({url:this.url,value:e}))}handleRenameBlur(){this.editing&&(this.editing=!1,void 0!==this.tempName&&""!==this.tempName&&this.tempName!==this.name&&(this.tempName.match(/[\/\\"<>\?\*:|]/)||this.HANDLE_RENAME({url:this.url,value:this.tempName})))}handleSelect(e){this.HANDLE_SELECT({url:this.url,value:e})}};r([o.Prop({default:""})],c.prototype,"url",void 0),r([o.Prop({})],c.prototype,"urlTaskData",void 0),r([o.Prop({})],c.prototype,"HADNLE_SETTING_CHANGE",void 0),r([o.Prop({})],c.prototype,"HANDLE_RENAME",void 0),r([o.Prop({})],c.prototype,"HANDLE_SELECT",void 0),r([o.Watch("name")],c.prototype,"onNameChange",null),c=r([o.Component({components:{SelectNative:l.default}})],c),t.default=c},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=n(1054);n.n(r).a},function(e,t,n){"use strict";var r=n(1056);n.n(r).a},function(e,t,n){"use strict";var r=n(1060);n.n(r).a},function(e,t,n){e.exports=n(9)(25)},function(e,t,n){var r=n(516),i=n(210),o=n(534),a=n(546),s=n(547),l=n(548);function c(e,t,n){if(void 0===e)throw new Error("String required as first argument");if(void 0===n&&(n=t,t={}),"function"!=typeof n){if(!r())throw new Error("Callback required as last argument");t=n||{},n=null}return{opts:t,cb:n}}function u(e){switch(e){case"svg":return l;case"txt":case"utf8":return a;case"png":case"image/png":default:return o}}function d(e,t,n){if(!n.cb)return new Promise(function(r,o){try{var a=i.create(t,n.opts);return e(a,n.opts,function(e,t){return e?o(e):r(t)})}catch(e){o(e)}});try{var r=i.create(t,n.opts);return e(r,n.opts,n.cb)}catch(e){n.cb(e)}}t.create=i.create,t.toCanvas=n(549).toCanvas,t.toString=function(e,t,n){var r=c(e,t,n);return d(function(e){switch(e){case"svg":return l;case"terminal":return s;case"utf8":default:return a}}(r.opts.type).render,e,r)},t.toDataURL=function(e,t,n){var r=c(e,t,n);return d(u(r.opts.type).renderToDataURL,e,r)},t.toBuffer=function(e,t,n){var r=c(e,t,n);return d(u(r.opts.type).renderToBuffer,e,r)},t.toFile=function(e,t,n,i){if("string"!=typeof e||"string"!=typeof t&&"object"!=typeof t)throw new Error("Invalid argument");if(arguments.length<3&&!r())throw new Error("Too few arguments provided");var o=c(t,n,i);return d(u(o.opts.type||function(e){return e.slice(2+(e.lastIndexOf(".")-1>>>0)).toLowerCase()}(e)).renderToFile.bind(null,e),t,o)},t.toFileStream=function(e,t,n){if(arguments.length<2)throw new Error("Too few arguments provided");var r=c(t,n,e.emit.bind(e,"error"));d(u("png").renderToFileStream.bind(null,e),t,r)}},function(e,t,n){"use strict";var r=n(517);e.exports=function(){return"function"==typeof r.Promise&&"function"==typeof r.Promise.prototype.then}},function(e,t,n){"use strict";e.exports="object"==typeof self&&self.self===self&&self||"object"==typeof global&&global.global===global&&global||this},function(e,t){function n(){this.buffer=[],this.length=0}n.prototype={get:function(e){var t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(var n=0;n<t;n++)this.putBit(1==(e>>>t-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},e.exports=n},function(e,t,n){var r=n(136);function i(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new r(e*e),this.data.fill(0),this.reservedBit=new r(e*e),this.reservedBit.fill(0)}i.prototype.set=function(e,t,n,r){var i=e*this.size+t;this.data[i]=n,r&&(this.reservedBit[i]=!0)},i.prototype.get=function(e,t){return this.data[e*this.size+t]},i.prototype.xor=function(e,t,n){this.data[e*this.size+t]^=n},i.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]},e.exports=i},function(e,t,n){var r=n(87).getSymbolSize;t.getRowColCoords=function(e){if(1===e)return[];for(var t=Math.floor(e/7)+2,n=r(e),i=145===n?26:2*Math.ceil((n-13)/(2*t-2)),o=[n-7],a=1;a<t-1;a++)o[a]=o[a-1]-i;return o.push(6),o.reverse()},t.getPositions=function(e){for(var n=[],r=t.getRowColCoords(e),i=r.length,o=0;o<i;o++)for(var a=0;a<i;a++)0===o&&0===a||0===o&&a===i-1||o===i-1&&0===a||n.push([r[o],r[a]]);return n}},function(e,t,n){var r=n(87).getSymbolSize;t.getPositions=function(e){var t=r(e);return[[0,0],[t-7,0],[0,t-7]]}},function(e,t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var n=3,r=3,i=40,o=10;function a(e,n,r){switch(e){case t.Patterns.PATTERN000:return(n+r)%2==0;case t.Patterns.PATTERN001:return n%2==0;case t.Patterns.PATTERN010:return r%3==0;case t.Patterns.PATTERN011:return(n+r)%3==0;case t.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2==0;case t.Patterns.PATTERN101:return n*r%2+n*r%3==0;case t.Patterns.PATTERN110:return(n*r%2+n*r%3)%2==0;case t.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}}t.isValid=function(e){return null!=e&&""!==e&&!isNaN(e)&&e>=0&&e<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(e){for(var t=e.size,r=0,i=0,o=0,a=null,s=null,l=0;l<t;l++){i=o=0,a=s=null;for(var c=0;c<t;c++){var u=e.get(l,c);u===a?i++:(i>=5&&(r+=n+(i-5)),a=u,i=1),(u=e.get(c,l))===s?o++:(o>=5&&(r+=n+(o-5)),s=u,o=1)}i>=5&&(r+=n+(i-5)),o>=5&&(r+=n+(o-5))}return r},t.getPenaltyN2=function(e){for(var t=e.size,n=0,i=0;i<t-1;i++)for(var o=0;o<t-1;o++){var a=e.get(i,o)+e.get(i,o+1)+e.get(i+1,o)+e.get(i+1,o+1);4!==a&&0!==a||n++}return n*r},t.getPenaltyN3=function(e){for(var t=e.size,n=0,r=0,o=0,a=0;a<t;a++){r=o=0;for(var s=0;s<t;s++)r=r<<1&2047|e.get(a,s),s>=10&&(1488===r||93===r)&&n++,o=o<<1&2047|e.get(s,a),s>=10&&(1488===o||93===o)&&n++}return n*i},t.getPenaltyN4=function(e){for(var t=0,n=e.data.length,r=0;r<n;r++)t+=e.data[r];return Math.abs(Math.ceil(100*t/n/5)-10)*o},t.applyMask=function(e,t){for(var n=t.size,r=0;r<n;r++)for(var i=0;i<n;i++)t.isReserved(i,r)||t.xor(i,r,a(e,i,r))},t.getBestMask=function(e,n){for(var r=Object.keys(t.Patterns).length,i=0,o=1/0,a=0;a<r;a++){n(a),t.applyMask(a,e);var s=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(a,e),s<o&&(o=s,i=a)}return i}},function(e,t,n){var r=n(136),i=n(524);function o(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}o.prototype.initialize=function(e){this.degree=e,this.genPoly=i.generateECPolynomial(this.degree)},o.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");var t=new r(this.degree);t.fill(0);var n=r.concat([e,t],e.length+this.degree),o=i.mod(n,this.genPoly),a=this.degree-o.length;if(a>0){var s=new r(this.degree);return s.fill(0),o.copy(s,a),s}return o},e.exports=o},function(e,t,n){var r=n(136),i=n(525);t.mul=function(e,t){var n=new r(e.length+t.length-1);n.fill(0);for(var o=0;o<e.length;o++)for(var a=0;a<t.length;a++)n[o+a]^=i.mul(e[o],t[a]);return n},t.mod=function(e,t){for(var n=new r(e);n.length-t.length>=0;){for(var o=n[0],a=0;a<t.length;a++)n[a]^=i.mul(t[a],o);for(var s=0;s<n.length&&0===n[s];)s++;n=n.slice(s)}return n},t.generateECPolynomial=function(e){for(var n=new r([1]),o=0;o<e;o++)n=t.mul(n,[1,i.exp(o)]);return n}},function(e,t,n){var r=n(136);if(r.alloc)var i=r.alloc(512),o=r.alloc(256);else i=new r(512),o=new r(256);!function(){for(var e=1,t=0;t<255;t++)i[t]=e,o[e]=t,256&(e<<=1)&&(e^=285);for(t=255;t<512;t++)i[t]=i[t-255]}(),t.log=function(e){if(e<1)throw new Error("log("+e+")");return o[e]},t.exp=function(e){return i[e]},t.mul=function(e,t){return 0===e||0===t?0:i[o[e]+o[t]]}},function(e,t,n){var r=n(87),i=n(211),o=n(175),a=n(88),s=n(212),l=n(214),c=r.getBCHDigit(7973);function u(e,t){return a.getCharCountIndicator(e,t)+4}function d(e,t){var n=0;return e.forEach(function(e){var r=u(e.mode,t);n+=r+e.getBitsLength()}),n}t.from=function(e,t){return s.isValid(e)?parseInt(e,10):t},t.getCapacity=function(e,t,n){if(!s.isValid(e))throw new Error("Invalid QR Code version");void 0===n&&(n=a.BYTE);var o=8*(r.getSymbolTotalCodewords(e)-i.getTotalCodewordsCount(e,t));if(n===a.MIXED)return o;var l=o-u(n,e);switch(n){case a.NUMERIC:return Math.floor(l/10*3);case a.ALPHANUMERIC:return Math.floor(l/11*2);case a.KANJI:return Math.floor(l/13);case a.BYTE:default:return Math.floor(l/8)}},t.getBestVersionForData=function(e,n){var r,i=o.from(n,o.M);if(l(e)){if(e.length>1)return function(e,n){for(var r=1;r<=40;r++)if(d(e,r)<=t.getCapacity(r,n,a.MIXED))return r}(e,i);if(0===e.length)return 1;r=e[0]}else r=e;return function(e,n,r){for(var i=1;i<=40;i++)if(n<=t.getCapacity(i,r,e))return i}(r.mode,r.getLength(),i)},t.getEncodedBits=function(e){if(!s.isValid(e)||e<7)throw new Error("Invalid QR Code version");for(var t=e<<12;r.getBCHDigit(t)-c>=0;)t^=7973<<r.getBCHDigit(t)-c;return e<<12|t}},function(e,t,n){var r=n(87),i=r.getBCHDigit(1335);t.getEncodedBits=function(e,t){for(var n=e.bit<<3|t,o=n<<10;r.getBCHDigit(o)-i>=0;)o^=1335<<r.getBCHDigit(o)-i;return 21522^(n<<10|o)}},function(e,t,n){var r=n(88),i=n(529),o=n(530),a=n(531),s=n(532),l=n(213),c=n(87),u=n(533);function d(e){return unescape(encodeURIComponent(e)).length}function f(e,t,n){for(var r,i=[];null!==(r=e.exec(n));)i.push({data:r[0],index:r.index,mode:t,length:r[0].length});return i}function h(e){var t,n,i=f(l.NUMERIC,r.NUMERIC,e),o=f(l.ALPHANUMERIC,r.ALPHANUMERIC,e);return c.isKanjiModeEnabled()?(t=f(l.BYTE,r.BYTE,e),n=f(l.KANJI,r.KANJI,e)):(t=f(l.BYTE_KANJI,r.BYTE,e),n=[]),i.concat(o,t,n).sort(function(e,t){return e.index-t.index}).map(function(e){return{data:e.data,mode:e.mode,length:e.length}})}function p(e,t){switch(t){case r.NUMERIC:return i.getBitsLength(e);case r.ALPHANUMERIC:return o.getBitsLength(e);case r.KANJI:return s.getBitsLength(e);case r.BYTE:return a.getBitsLength(e)}}function g(e,t){var n,l=r.getBestModeForData(e);if((n=r.from(t,l))!==r.BYTE&&n.bit<l.bit)throw new Error('"'+e+'" cannot be encoded with mode '+r.toString(n)+".\n Suggested mode is: "+r.toString(l));switch(n!==r.KANJI||c.isKanjiModeEnabled()||(n=r.BYTE),n){case r.NUMERIC:return new i(e);case r.ALPHANUMERIC:return new o(e);case r.KANJI:return new s(e);case r.BYTE:return new a(e)}}t.fromArray=function(e){return e.reduce(function(e,t){return"string"==typeof t?e.push(g(t,null)):t.data&&e.push(g(t.data,t.mode)),e},[])},t.fromString=function(e,n){for(var i=function(e,t){for(var n={},i={start:{}},o=["start"],a=0;a<e.length;a++){for(var s=e[a],l=[],c=0;c<s.length;c++){var u=s[c],d=""+a+c;l.push(d),n[d]={node:u,lastCount:0},i[d]={};for(var f=0;f<o.length;f++){var h=o[f];n[h]&&n[h].node.mode===u.mode?(i[h][d]=p(n[h].lastCount+u.length,u.mode)-p(n[h].lastCount,u.mode),n[h].lastCount+=u.length):(n[h]&&(n[h].lastCount=u.length),i[h][d]=p(u.length,u.mode)+4+r.getCharCountIndicator(u.mode,t))}}o=l}for(f=0;f<o.length;f++)i[o[f]].end=0;return{map:i,table:n}}(function(e){for(var t=[],n=0;n<e.length;n++){var i=e[n];switch(i.mode){case r.NUMERIC:t.push([i,{data:i.data,mode:r.ALPHANUMERIC,length:i.length},{data:i.data,mode:r.BYTE,length:i.length}]);break;case r.ALPHANUMERIC:t.push([i,{data:i.data,mode:r.BYTE,length:i.length}]);break;case r.KANJI:t.push([i,{data:i.data,mode:r.BYTE,length:d(i.data)}]);break;case r.BYTE:t.push([{data:i.data,mode:r.BYTE,length:d(i.data)}])}}return t}(h(e,c.isKanjiModeEnabled())),n),o=u.find_path(i.map,"start","end"),a=[],s=1;s<o.length-1;s++)a.push(i.table[o[s]].node);return t.fromArray(function(e){return e.reduce(function(e,t){var n=e.length-1>=0?e[e.length-1]:null;return n&&n.mode===t.mode?(e[e.length-1].data+=t.data,e):(e.push(t),e)},[])}(a))},t.rawSplit=function(e){return t.fromArray(h(e,c.isKanjiModeEnabled()))}},function(e,t,n){var r=n(88);function i(e){this.mode=r.NUMERIC,this.data=e.toString()}i.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(e){var t,n,r;for(t=0;t+3<=this.data.length;t+=3)n=this.data.substr(t,3),r=parseInt(n,10),e.put(r,10);var i=this.data.length-t;i>0&&(n=this.data.substr(t),r=parseInt(n,10),e.put(r,3*i+1))},e.exports=i},function(e,t,n){var r=n(88),i=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function o(e){this.mode=r.ALPHANUMERIC,this.data=e}o.getBitsLength=function(e){return 11*Math.floor(e/2)+e%2*6},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(e){var t;for(t=0;t+2<=this.data.length;t+=2){var n=45*i.indexOf(this.data[t]);n+=i.indexOf(this.data[t+1]),e.put(n,11)}this.data.length%2&&e.put(i.indexOf(this.data[t]),6)},e.exports=o},function(e,t,n){var r=n(136),i=n(88);function o(e){this.mode=i.BYTE,this.data=new r(e)}o.getBitsLength=function(e){return 8*e},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(e){for(var t=0,n=this.data.length;t<n;t++)e.put(this.data[t],8)},e.exports=o},function(e,t,n){var r=n(88),i=n(87);function o(e){this.mode=r.KANJI,this.data=e}o.getBitsLength=function(e){return 13*e},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(e){var t;for(t=0;t<this.data.length;t++){var n=i.toSJIS(this.data[t]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[t]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),e.put(n,13)}},e.exports=o},function(e,t,n){"use strict";var r={single_source_shortest_paths:function(e,t,n){var i={},o={};o[t]=0;var a,s,l,c,u,d,f,h=r.PriorityQueue.make();for(h.push(t,0);!h.empty();)for(l in s=(a=h.pop()).value,c=a.cost,u=e[s]||{})u.hasOwnProperty(l)&&(d=c+u[l],f=o[l],(void 0===o[l]||f>d)&&(o[l]=d,h.push(l,d),i[l]=s));if(void 0!==n&&void 0===o[n]){var p=["Could not find a path from ",t," to ",n,"."].join("");throw new Error(p)}return i},extract_shortest_path_from_predecessor_list:function(e,t){for(var n=[],r=t;r;)n.push(r),e[r],r=e[r];return n.reverse(),n},find_path:function(e,t,n){var i=r.single_source_shortest_paths(e,t,n);return r.extract_shortest_path_from_predecessor_list(i,n)},PriorityQueue:{make:function(e){var t,n=r.PriorityQueue,i={};for(t in e=e||{},n)n.hasOwnProperty(t)&&(i[t]=n[t]);return i.queue=[],i.sorter=e.sorter||n.default_sorter,i},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){var n={value:e,cost:t};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};e.exports=r},function(e,t,n){var r=n(21),i=n(535).PNG,o=n(156);t.render=function(e,t){var n=o.getOptions(t),r=n.rendererOpts,a=o.getImageWidth(e.modules.size,n);r.width=a,r.height=a;var s=new i(r);return o.qrToImageData(s.data,e,n),s},t.renderToDataURL=function(e,n,r){void 0===r&&(r=n,n=void 0),t.renderToBuffer(e,n,function(e,t){e&&r(e);var n="data:image/png;base64,";n+=t.toString("base64"),r(null,n)})},t.renderToBuffer=function(e,n,r){void 0===r&&(r=n,n=void 0);var i=t.render(e,n),o=[];i.on("error",r),i.on("data",function(e){o.push(e)}),i.on("end",function(){r(null,Buffer.concat(o))}),i.pack()},t.renderToFile=function(e,n,i,o){void 0===o&&(o=i,i=void 0);var a=r.createWriteStream(e);a.on("error",o),a.on("close",o),t.renderToFileStream(a,n,i)},t.renderToFileStream=function(e,n,r){t.render(n,r).pack().pipe(e)}},function(e,t,n){"use strict";var r=n(6),i=n(75),o=n(536),a=n(538),s=n(541),l=t.PNG=function(e){i.call(this),e=e||{},this.width=0|e.width,this.height=0|e.height,this.data=this.width>0&&this.height>0?new Buffer(4*this.width*this.height):null,e.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new o(e),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(e){this.data=e,this.emit("parsed",e)}.bind(this)),this._packer=new a(e),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};r.inherits(l,i),l.sync=s,l.prototype.pack=function(){return this.data&&this.data.length?(process.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this):(this.emit("error","No data provided"),this)},l.prototype.parse=function(e,t){var n,r;t&&(n=function(e){this.removeListener("error",r),this.data=e,t(null,this)}.bind(this),r=function(e){this.removeListener("parsed",n),t(e,null)}.bind(this),this.once("parsed",n),this.once("error",r));return this.end(e),this},l.prototype.write=function(e){return this._parser.write(e),!0},l.prototype.end=function(e){this._parser.end(e)},l.prototype._metadata=function(e){this.width=e.width,this.height=e.height,this.emit("metadata",e)},l.prototype._gamma=function(e){this.gamma=e},l.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},l.bitblt=function(e,t,n,r,i,o,a,s){if(r|=0,i|=0,o|=0,a|=0,s|=0,(n|=0)>e.width||r>e.height||n+i>e.width||r+o>e.height)throw new Error("bitblt reading outside image");if(a>t.width||s>t.height||a+i>t.width||s+o>t.height)throw new Error("bitblt writing outside image");for(var l=0;l<o;l++)e.data.copy(t.data,(s+l)*t.width+a<<2,(r+l)*e.width+n<<2,(r+l)*e.width+n+i<<2)},l.prototype.bitblt=function(e,t,n,r,i,o,a){return l.bitblt(this,e,t,n,r,i,o,a),this},l.adjustGamma=function(e){if(e.gamma){for(var t=0;t<e.height;t++)for(var n=0;n<e.width;n++)for(var r=e.width*t+n<<2,i=0;i<3;i++){var o=e.data[r+i]/255;o=Math.pow(o,1/2.2/e.gamma),e.data[r+i]=Math.round(255*o)}e.gamma=0}},l.prototype.adjustGamma=function(){l.adjustGamma(this)}},function(e,t,n){"use strict";var r=n(6),i=n(48),o=n(215),a=n(537),s=n(219),l=n(221),c=n(222),u=e.exports=function(e){o.call(this),this._parser=new s(e,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this)}),this._options=e,this.writable=!0,this._parser.start()};r.inherits(u,o),u.prototype._handleError=function(e){this.emit("error",e),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0},u.prototype._inflateData=function(e){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=i.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{var t=(1+(this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3))*this._bitmapInfo.height,n=Math.max(t,i.Z_MIN_CHUNK);this._inflate=i.createInflate({chunkSize:n});var r=t,o=this.emit.bind(this,"error");this._inflate.on("error",function(e){r&&o(e)}),this._filter.on("complete",this._complete.bind(this));var a=this._filter.write.bind(this._filter);this._inflate.on("data",function(e){r&&(e.length>r&&(e=e.slice(0,r)),r-=e.length,a(e))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(e)},u.prototype._handleMetaData=function(e){this.emit("metadata",e),this._bitmapInfo=Object.create(e),this._filter=new a(this._bitmapInfo)},u.prototype._handleTransColor=function(e){this._bitmapInfo.transColor=e},u.prototype._handlePalette=function(e){this._bitmapInfo.palette=e},u.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"),this.destroySoon())},u.prototype._complete=function(e){if(!this.errord){try{var t=l.dataToBitMap(e,this._bitmapInfo),n=c(t,this._bitmapInfo);t=null}catch(e){return void this._handleError(e)}this.emit("parsed",n)}}},function(e,t,n){"use strict";var r=n(6),i=n(215),o=n(216),a=e.exports=function(e){i.call(this);var t=[],n=this;this._filter=new o(e,{read:this.read.bind(this),write:function(e){t.push(e)},complete:function(){n.emit("complete",Buffer.concat(t))}}),this._filter.start()};r.inherits(a,i)},function(e,t,n){"use strict";var r=n(6),i=n(75),o=n(143),a=n(223),s=e.exports=function(e){i.call(this);var t=e||{};this._packer=new a(t),this._deflate=this._packer.createDeflate(),this.readable=!0};r.inherits(s,i),s.prototype.pack=function(e,t,n,r){this.emit("data",new Buffer(o.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(t,n)),r&&this.emit("data",this._packer.packGAMA(r));var i=this._packer.filterData(e,t,n);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(e){this.emit("data",this._packer.packIDAT(e))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(i)}},function(e,t,n){"use strict";var r=n(143);e.exports=function(e,t,n,i){var o,a=-1!==[r.COLORTYPE_COLOR_ALPHA,r.COLORTYPE_ALPHA].indexOf(i.colorType);if(i.colorType===i.inputColorType){var s=(o=new ArrayBuffer(2),new DataView(o).setInt16(0,256,!0),256!==new Int16Array(o)[0]);if(8===i.bitDepth||16===i.bitDepth&&s)return e}var l=16!==i.bitDepth?e:new Uint16Array(e.buffer),c=255,u=r.COLORTYPE_TO_BPP_MAP[i.inputColorType];4!=u||i.inputHasAlpha||(u=3);var d=r.COLORTYPE_TO_BPP_MAP[i.colorType];16===i.bitDepth&&(c=65535,d*=2);var f=new Buffer(t*n*d),h=0,p=0,g=i.bgColor||{};function m(e,t){var n,o,s,l=c;switch(i.inputColorType){case r.COLORTYPE_COLOR_ALPHA:l=e[t+3],n=e[t],o=e[t+1],s=e[t+2];break;case r.COLORTYPE_COLOR:n=e[t],o=e[t+1],s=e[t+2];break;case r.COLORTYPE_ALPHA:l=e[t+1],o=n=e[t],s=n;break;case r.COLORTYPE_GRAYSCALE:o=n=e[t],s=n;break;default:throw new Error("input color type:"+i.inputColorType+" is not supported at present")}return i.inputHasAlpha&&(a||(l/=c,n=Math.min(Math.max(Math.round((1-l)*g.red+l*n),0),c),o=Math.min(Math.max(Math.round((1-l)*g.green+l*o),0),c),s=Math.min(Math.max(Math.round((1-l)*g.blue+l*s),0),c))),{red:n,green:o,blue:s,alpha:l}}void 0===g.red&&(g.red=c),void 0===g.green&&(g.green=c),void 0===g.blue&&(g.blue=c);for(var v=0;v<n;v++)for(var _=0;_<t;_++){var y=m(l,h);switch(i.colorType){case r.COLORTYPE_COLOR_ALPHA:case r.COLORTYPE_COLOR:8===i.bitDepth?(f[p]=y.red,f[p+1]=y.green,f[p+2]=y.blue,a&&(f[p+3]=y.alpha)):(f.writeUInt16BE(y.red,p),f.writeUInt16BE(y.green,p+2),f.writeUInt16BE(y.blue,p+4),a&&f.writeUInt16BE(y.alpha,p+6));break;case r.COLORTYPE_ALPHA:case r.COLORTYPE_GRAYSCALE:var w=(y.red+y.green+y.blue)/3;8===i.bitDepth?(f[p]=w,a&&(f[p+1]=y.alpha)):(f.writeUInt16BE(w,p),a&&f.writeUInt16BE(y.alpha,p+2))}h+=u,p+=d}return f}},function(e,t,n){"use strict";var r=n(218);var i={0:function(e,t,n,r,i){for(var o=0;o<n;o++)r[i+o]=e[t+o]},1:function(e,t,n,r,i,o){for(var a=0;a<n;a++){var s=a>=o?e[t+a-o]:0,l=e[t+a]-s;r[i+a]=l}},2:function(e,t,n,r,i){for(var o=0;o<n;o++){var a=t>0?e[t+o-n]:0,s=e[t+o]-a;r[i+o]=s}},3:function(e,t,n,r,i,o){for(var a=0;a<n;a++){var s=a>=o?e[t+a-o]:0,l=t>0?e[t+a-n]:0,c=e[t+a]-(s+l>>1);r[i+a]=c}},4:function(e,t,n,i,o,a){for(var s=0;s<n;s++){var l=s>=a?e[t+s-a]:0,c=t>0?e[t+s-n]:0,u=t>0&&s>=a?e[t+s-(n+a)]:0,d=e[t+s]-r(l,c,u);i[o+s]=d}}},o={0:function(e,t,n){for(var r=0,i=t+n,o=t;o<i;o++)r+=Math.abs(e[o]);return r},1:function(e,t,n,r){for(var i=0,o=0;o<n;o++){var a=o>=r?e[t+o-r]:0,s=e[t+o]-a;i+=Math.abs(s)}return i},2:function(e,t,n){for(var r=0,i=t+n,o=t;o<i;o++){var a=t>0?e[o-n]:0,s=e[o]-a;r+=Math.abs(s)}return r},3:function(e,t,n,r){for(var i=0,o=0;o<n;o++){var a=o>=r?e[t+o-r]:0,s=t>0?e[t+o-n]:0,l=e[t+o]-(a+s>>1);i+=Math.abs(l)}return i},4:function(e,t,n,i){for(var o=0,a=0;a<n;a++){var s=a>=i?e[t+a-i]:0,l=t>0?e[t+a-n]:0,c=t>0&&a>=i?e[t+a-(n+i)]:0,u=e[t+a]-r(s,l,c);o+=Math.abs(u)}return o}};e.exports=function(e,t,n,r,a){var s;if("filterType"in r&&-1!==r.filterType){if("number"!=typeof r.filterType)throw new Error("unrecognised filter types");s=[r.filterType]}else s=[0,1,2,3,4];16===r.bitDepth&&(a*=2);for(var l=t*a,c=0,u=0,d=new Buffer((l+1)*n),f=s[0],h=0;h<n;h++){if(s.length>1)for(var p=1/0,g=0;g<s.length;g++){var m=o[s[g]](e,u,l,a);m<p&&(f=s[g],p=m)}d[c]=f,c++,i[f](e,u,l,d,c,a),c+=l,u+=l}return d}},function(e,t,n){"use strict";var r=n(542),i=n(545);t.read=function(e,t){return r(e,t||{})},t.write=function(e,t){return i(e,t)}},function(e,t,n){"use strict";var r=!0,i=n(48),o=n(543);i.deflateSync||(r=!1);var a=n(224),s=n(544),l=n(219),c=n(221),u=n(222);e.exports=function(e,t){if(!r)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");var n,d,f;var h=[];var p=new a(e);if(new l(t,{read:p.read.bind(p),error:function(e){n=e},metadata:function(e){d=e},gamma:function(e){f=e},palette:function(e){d.palette=e},transColor:function(e){d.transColor=e},inflateData:function(e){h.push(e)}}).start(),p.process(),n)throw n;var g,m=Buffer.concat(h);if(h.length=0,d.interlace)g=i.inflateSync(m);else{var v=(1+(d.width*d.bpp*d.depth+7>>3))*d.height;g=o(m,{chunkSize:v,maxLength:v})}if(m=null,!g||!g.length)throw new Error("bad png - invalid inflate data response");var _=s.process(g,d);m=null;var y=c.dataToBitMap(_,d);_=null;var w=u(y,d);return d.data=w,d.gamma=f||0,d}},function(e,t,n){"use strict";var r=n(83).ok,i=n(48),o=n(6),a=n(41).kMaxLength;function s(e){if(!(this instanceof s))return new s(e);e&&e.chunkSize<i.Z_MIN_CHUNK&&(e.chunkSize=i.Z_MIN_CHUNK),i.Inflate.call(this,e),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,e&&null!=e.maxLength&&(this._maxLength=e.maxLength)}function l(e,t){t&&process.nextTick(t),e._handle&&(e._handle.close(),e._handle=null)}function c(e,t){return function(e,t){if("string"==typeof t&&(t=Buffer.from(t)),!(t instanceof Buffer))throw new TypeError("Not a string or buffer");var n=e._finishFlushFlag;return null==n&&(n=i.Z_FINISH),e._processChunk(t,n)}(new s(t),e)}s.prototype._processChunk=function(e,t,n){if("function"==typeof n)return i.Inflate._processChunk.call(this,e,t,n);var o,s=this,c=e&&e.length,u=this._chunkSize-this._offset,d=this._maxLength,f=0,h=[],p=0;function g(e,t){if(!s._hadError){var n=u-t;if(r(n>=0,"have should not go down"),n>0){var i=s._buffer.slice(s._offset,s._offset+n);if(s._offset+=n,i.length>d&&(i=i.slice(0,d)),h.push(i),p+=i.length,0===(d-=i.length))return!1}return(0===t||s._offset>=s._chunkSize)&&(u=s._chunkSize,s._offset=0,s._buffer=Buffer.allocUnsafe(s._chunkSize)),0===t&&(f+=c-e,c=e,!0)}}this.on("error",function(e){o=e}),r(this._handle,"zlib binding closed");do{var m=this._handle.writeSync(t,e,f,c,this._buffer,this._offset,u);m=m||this._writeState}while(!this._hadError&&g(m[0],m[1]));if(this._hadError)throw o;if(p>=a)throw l(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+a.toString(16)+" bytes");var v=Buffer.concat(h,p);return l(this),v},o.inherits(s,i.Inflate),e.exports=t=c,t.Inflate=s,t.createInflate=function(e){return new s(e)},t.inflateSync=c},function(e,t,n){"use strict";var r=n(224),i=n(216);t.process=function(e,t){var n=[],o=new r(e);return new i(t,{read:o.read.bind(o),write:function(e){n.push(e)},complete:function(){}}).start(),o.process(),Buffer.concat(n)}},function(e,t,n){"use strict";var r=!0,i=n(48);i.deflateSync||(r=!1);var o=n(143),a=n(223);e.exports=function(e,t){if(!r)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");var n=new a(t||{}),s=[];s.push(new Buffer(o.PNG_SIGNATURE)),s.push(n.packIHDR(e.width,e.height)),e.gamma&&s.push(n.packGAMA(e.gamma));var l=n.filterData(e.data,e.width,e.height),c=i.deflateSync(l,n.getDeflateOptions());if(l=null,!c||!c.length)throw new Error("bad png - invalid compressed data response");return s.push(n.packIDAT(c)),s.push(n.packIEND()),Buffer.concat(s)}},function(e,t,n){var r=n(156),i={WW:" ",WB:"▄",BB:"█",BW:"▀"};t.render=function(e,t,n){var o=e.modules.size,a=e.modules.data,s=r.getOptions(t),l="",c=Array(o+2*s.margin+1).join(i.WW);c=Array(s.margin/2+1).join(c+"\n");var u,d,f=Array(s.margin+1).join(i.WW);l+=c;for(var h=0;h<o;h+=2){l+=f;for(var p=0;p<o;p++){var g=a[h*o+p],m=a[(h+1)*o+p];l+=(d=m,(u=g)&&d?i.BB:u&&!d?i.BW:!u&&d?i.WB:i.WW)}l+=f+"\n"}return l+=c.slice(0,-1),"function"==typeof n&&n(null,l),l},t.renderToFile=function(e,r,i,o){void 0===o&&(o=i,i=void 0);var a=n(21),s=t.render(r,i);a.writeFile(e,s,o)}},function(e,t){t.render=function(e,t,n){var r=e.modules.size,i=e.modules.data,o="[47m  [0m",a="",s=Array(r+3).join(o),l=Array(2).join(o);a+=s+"\n";for(var c=0;c<r;++c){a+=o;for(var u=0;u<r;u++)a+=i[c*r+u]?"[40m  [0m":o;a+=l+"\n"}return a+=s+"\n","function"==typeof n&&n(null,a),a}},function(e,t,n){var r=n(225);t.render=r.render,t.renderToFile=function(e,r,i,o){void 0===o&&(o=i,i=void 0);var a=n(21),s='<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+t.render(r,i);a.writeFile(e,s,o)}},function(e,t,n){var r=n(550),i=n(210),o=n(551),a=n(225);function s(e,t,n,o,a){var s=[].slice.call(arguments,1),l=s.length,c="function"==typeof s[l-1];if(!c&&!r())throw new Error("Callback required as last argument");if(!c){if(l<1)throw new Error("Too few arguments provided");return 1===l?(n=t,t=o=void 0):2!==l||t.getContext||(o=n,n=t,t=void 0),new Promise(function(r,a){try{var s=i.create(n,o);r(e(s,t,o))}catch(e){a(e)}})}if(l<2)throw new Error("Too few arguments provided");2===l?(a=n,n=t,t=o=void 0):3===l&&(t.getContext&&void 0===a?(a=o,o=void 0):(a=o,o=n,n=t,t=void 0));try{var u=i.create(n,o);a(null,e(u,t,o))}catch(e){a(e)}}t.create=i.create,t.toCanvas=s.bind(null,o.render),t.toDataURL=s.bind(null,o.renderToDataURL),t.toString=s.bind(null,function(e,t,n){return a.render(e,n)})},function(e,t){e.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},function(e,t,n){var r=n(156);t.render=function(e,t,n){var i=n,o=t;void 0!==i||t&&t.getContext||(i=t,t=void 0),t||(o=function(){try{return document.createElement("canvas")}catch(e){throw new Error("You need to specify a canvas element")}}()),i=r.getOptions(i);var a=r.getImageWidth(e.modules.size,i),s=o.getContext("2d"),l=s.createImageData(a,a);return r.qrToImageData(l.data,e,i),function(e,t,n){e.clearRect(0,0,t.width,t.height),t.style||(t.style={}),t.height=n,t.width=n,t.style.height=n+"px",t.style.width=n+"px"}(s,o,a),s.putImageData(l,0,0),o},t.renderToDataURL=function(e,n,r){var i=r;void 0!==i||n&&n.getContext||(i=n,n=void 0),i||(i={});var o=t.render(e,n,i),a=i.type||"image/png",s=i.rendererOpts||{};return o.toDataURL(a,s.quality)}},function(e,t,n){e.exports=function(e){function t(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=4)}([function(e,t){e.exports={inserted:function(e,t,n){var r=n.context.$refs[t.arg];r&&r.addItem({el:e,value:t.value})},unbind:function(e,t,n){var r=n.context.$refs[t.arg];r&&r.removeItem({el:e,value:t.value})}}},function(e,t,n){var r=n(5)(n(2),n(6),null,null,null);e.exports=r.exports},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(3);t.default={name:"Popup",props:{appendToBody:{type:Boolean,default:!0},arrowClass:String,direction:{type:String,default:"bottom"},display:{type:Boolean,default:!1},padding:{type:Number,default:0},trigger:{type:String,default:"hover"},scrollShow:{type:Boolean,default:!0}},data:function(){return{arrowStyle:{},delay:100,directionMap:{top:"height",left:"width"},secondDirection:"",left:0,top:0,currentElement:null,willHide:!1}},computed:{directionClass:function(){var e=/(top)|(bottom)|(left)|(right)/i,t=e.exec(this.direction)||[],n=this.secondDirection?e.exec(this.secondDirection)[0]:"",r=t[0]||"";return""===n?"popup-"+r:"popup-"+n},triggerEvent:function(){switch(this.trigger){case"hover":return"mouseenter";case"focus":return"focus";case"click":return"click"}},unTriggerEl:function(){return"click"===this.trigger?document:this.$el},unTriggerEvent:function(){switch(this.trigger){case"hover":return"mouseleave";case"focus":return"blur";case"click":return"click"}}},mounted:function(){this.appendToBody&&document.body.appendChild(this.$el),this.$el.addEventListener(this.triggerEvent,this.handlePopupVisible),this.unTriggerEl.addEventListener(this.unTriggerEvent,this.handlePopupInvisible),this.handleResizeThrottle=n.i(r.a)(this.handleResize,16),window.addEventListener("resize",this.handleResizeThrottle)},beforeDestroy:function(){this.$el.removeEventListener(this.triggerEvent,this.handlePopupVisible),this.unTriggerEl.removeEventListener(this.unTriggerEvent,this.handlePopupInvisible),window.removeEventListener("resize",this.handleResizeThrottle)},destroyed:function(){this.appendToBody&&this.$el&&document.body.hasChildNodes(this.$el)&&document.body.removeChild(this.$el)},methods:{addItem:function(e){e.el.handleVisible=this.handleVisible.bind(this,e.value,e.el),e.el.addEventListener(this.triggerEvent,e.el.handleVisible),"click"!==this.trigger&&(e.el.handleInvisible=this.handleInvisible.bind(this,e.value),e.el.addEventListener(this.unTriggerEvent,e.el.handleInvisible)),this.bindScroll(e.el)},removeItem:function(e){e.el.removeEventListener(this.triggerEvent,e.el.handleVisible),"click"!==this.trigger&&e.el.removeEventListener(this.unTriggerEvent,e.el.handleInvisible),this.unbindScroll(e.el)},bindScroll:function(e){for(e=e.parentNode;e;)e.addEventListener("scroll",this.handleScroll),e=e.parentNode},unbindScroll:function(e){for(e=e.parentNode;e;)e.removeEventListener("scroll",this.handleScroll),e=e.parentNode},computePosition:function(e,t){e=e.getBoundingClientRect();var n=0,r=0,i=(t=t.getBoundingClientRect()).top-e.height-this.padding,o=t.left+t.width+this.padding,a=t.top+t.height+this.padding,s=t.left-e.width-this.padding;/(top)|(bottom)/i.test(this.direction)?(n=t.left+(t.width-e.width)/2,"bottomStart"!==this.direction&&"topStart"!==this.direction||(n=t.left),"bottomEnd"!==this.direction&&"topEnd"!==this.direction||(n=t.left-e.width+t.width),n<0?(n=0,this.arrowStyle={left:(t.left+t.right)/2+"px"}):n+e.width>window.innerWidth?(n=window.innerWidth-e.width,this.arrowStyle={left:(t.left+t.right)/2-n+"px"}):this.arrowStyle={left:(t.left+t.right)/2-n+"px"},"top"===this.direction||"topStart"===this.direction||"topEnd"===this.direction?i<0&&a+e.height<=window.innerHeight?(r=a,this.secondDirection="bottom","topStart"===this.direction&&(this.secondDirection="bottomStart",this.arrowStyle={left:(t.left+t.right)/2-n+"px"}),"topEnd"===this.direction&&(this.secondDirection="bottomEnd",this.arrowStyle={left:(t.left+t.right)/2-n+"px"})):(r=i,this.secondDirection="",this.arrowStyle={left:(t.left+t.right)/2-n+"px"}):a+e.height>window.innerHeight&&i>=0?(r=i,this.secondDirection="top","bottomStart"===this.direction&&(this.secondDirection="topStart",this.arrowStyle={left:(t.left+t.right)/2-n+"px"}),"bottomEnd"===this.direction&&(this.secondDirection="topEnd",this.arrowStyle={left:(t.left+t.right)/2-n+"px"})):(r=a,this.secondDirection="",this.arrowStyle={left:(t.left+t.right)/2-n+"px"})):/(left)|(right)/i.test(this.direction)&&(r=t.top+(t.height-e.height)/2,"leftStart"!==this.direction&&"rightStart"!==this.direction||(r=t.top),"leftEnd"!==this.direction&&"rightEnd"!==this.direction||(r=t.top-e.height+t.height),r<0?(r=0,this.arrowStyle={top:(t.top+t.bottom)/2+"px"}):r+e.height>window.innerHeight?(r=window.innerHeight-e.height,this.arrowStyle={top:(t.top+t.bottom)/2-r+"px"}):this.arrowStyle={top:(t.top+t.bottom)/2-r+"px"},"left"===this.direction||"leftStart"===this.direction||"leftEnd"===this.direction?s<0&&o+e.width<=window.innerWidth?(n=o,this.secondDirection="right","leftStart"===this.direction&&(this.secondDirection="rightStart",this.arrowStyle={top:(t.top+t.bottom)/2-r+"px"}),"leftEnd"===this.direction&&(this.secondDirection="rightEnd",this.arrowStyle={top:(t.top+t.bottom)/2-r+"px"})):(n=s,this.secondDirection="",this.arrowStyle={top:(t.top+t.bottom)/2-r+"px"}):o+e.width>window.innerWidth&&s>=0?(n=s,this.secondDirection="left","rightStart"===this.direction&&(this.secondDirection="leftStart",this.arrowStyle={top:(t.top+t.bottom)/2-r+"px"}),"rightEnd"===this.direction&&(this.secondDirection="leftEnd",this.arrowStyle={top:(t.top+t.bottom)/2-r+"px"})):(n=o,this.secondDirection="",this.arrowStyle={top:(t.top+t.bottom)/2-r+"px"})),this.top=r,this.left=n},handleVisible:function(e,t,n){var r=this;if("click"===this.trigger&&this.display&&!0!==n)return this.handleInvisible(e);this.willHide=!1,this.currentElement=t,this.currentValue=e,this.$emit("update:display",!0),this.$emit("show",e),this.$nextTick(function(){setTimeout(function(){r.computePosition(r.$el,r.currentElement)})})},handleInvisible:function(e){var t=this;"click"===this.trigger?(this.$emit("update:display",!1),this.$emit("hide",e)):(this.willHide=!0,setTimeout(function(){t.willHide&&(t.$emit("update:display",!1),t.$emit("hide",e))},this.delay))},handleScroll:function(){this.scrollShow?this.display&&this.computePosition(this.$el,this.currentElement):(this.$emit("update:display",!1),this.$emit("hide"))},handlePopupVisible:function(e){e.stopPropagation(),this.willHide=!1,this.$emit("update:display",!0),this.$emit("show",this.value)},handlePopupInvisible:function(e){var t=this;"click"===this.trigger&&this.isClosest(e.target,this.currentElement)||("click"===this.trigger?(this.$emit("update:display",!1),this.$emit("hide",this.value)):(this.willHide=!0,setTimeout(function(){t.willHide&&(t.$emit("update:display",!1),t.$emit("hide",t.value))},this.delay)))},handleResize:function(){this.display&&this.handleVisible(this.value,this.currentElement,!0)},show:function(e,t){this.handleVisible(t,e)},hide:function(){this.handleInvisible()},isDirectionLine:function(e){return"top"===e||"bottom"===e?"top"===this.direction||"bottom"===this.direction:"left"===this.direction||"right"===this.direction},isClosest:function(e,t){return e!==document&&(e===t||this.isClosest(e.parentNode,t))}}}},function(e,t,n){"use strict";t.a=function(e,t){var n=this,r=0;return function(){for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];var s=+new Date;s-r>t&&(e.apply(n,o),r=s)}}},function(e,t,n){var r=n(0),i=n(1);i.install=function(e){e.directive("popup",r),e.component(i.name,i)},e.exports=i},function(e,t){e.exports=function(e,t,n,r,i){var o,a=e=e||{},s=typeof e.default;"object"!==s&&"function"!==s||(o=e,a=e.default);var l,c="function"==typeof a?a.options:a;if(t&&(c.render=t.render,c.staticRenderFns=t.staticRenderFns),r&&(c._scopeId=r),i?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),n&&n.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(i)},c._ssrRegister=l):n&&(l=n),l){var u=c.functional,d=u?c.render:c.beforeCreate;u?c.render=function(e,t){return l.call(t),d(e,t)}:c.beforeCreate=d?[].concat(d,l):[l]}return{esModule:o,exports:a,options:c}}},function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:e.directionClass,style:{display:e.display?"block":"none",left:e.left+"px",top:e.top+"px"},attrs:{tabindex:"999"}},[e._t("default"),e._v(" "),n("span",{staticClass:"popup-arrow",class:e.arrowClass,style:e.arrowStyle})],2)},staticRenderFns:[]}}])},,,,,,,,,,,,,function(e,t,n){"use strict";var r=function(){var e=this.$createElement;return(this._self._c||e)("pre-new-task-ctrl-container",{ref:"ctrl",on:{"view-change":this.handleViewChange},nativeOn:{dragstart:function(e){e.preventDefault()}}})},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("td-dialog",{attrs:{visible:"","custom-class":"xly-dialog-task","before-close":e.handleClose}},[n("h2",{attrs:{slot:"header"},slot:"header"},[n("span",{on:{click:e.onOperatorDrop}},[e._v("添加链接或口令")]),e._v(" "),n("i",{staticClass:"xly-icon-arrow-right",on:{click:e.onOperatorDrop}})]),e._v(" "),n("div",{staticClass:"xly-dialog-link"},[n("td-tooltip",{staticStyle:{display:"block"},attrs:{content:e.tipsText,visible:e.isShowTips,offset:{y:3},placement:"top"}},[n("div",{staticClass:"xly-dialog-link__add",on:{click:e.handleShowContent}},[n("i",{staticClass:"xly-icon-add"}),e._v(" 添加链接\n      ")])]),e._v(" "),n("div",{staticClass:"xly-dialog-link__content",class:{"is-show":e.showContent}},[n("td-input",{attrs:{type:"textarea",placeholder:"添加多个下载链接时，请确保每行只有一个链接。",autofocus:""},nativeOn:{contextmenu:function(t){return e.handlePopInputMenu(t)}},model:{value:e.context,callback:function(t){e.context=t},expression:"context"}}),e._v(" "),n("div",{staticClass:"xly-dialog-link__operate"},[n("td-button",{staticClass:"td-button--text",on:{click:e.handleInputUnfold}},[e._v("收起")]),e._v(" "),n("td-button",{staticClass:"td-button--text",attrs:{disabled:""===e.context},on:{click:function(t){e.handleInputFinish()}},nativeOn:{click:function(e){e.stopPropagation()}}},[e._v("确定")])],1)],1)],1),e._v(" "),n("down-info-container",{directives:[{name:"show",rawName:"v-show",value:e.visibleCount>0,expression:"visibleCount > 0"}],on:{setted:e.handleSetting,group:e.handleGroupCheck,"groupname-change":e.handleGroupNameChange}}),e._v(" "),n("download-to",{ref:"downloadTo",attrs:{panel:"PreNewTaskDlg","logic-choosed":e.logicChoosed,"cloud-choosed":e.cloudChoosed,selectSize:e.selectSize,selectCount:e.selectCount,savePath:e.savePath,cloudPath:e.cloudPath},on:{"logic-input":e.handleChangeLogicPath,"cloud-input":e.handleChangeCloudPath,"check-change":e.handlePathCheckChange}}),e._v(" "),n("i",{staticClass:"xly-icon-resize"}),e._v(" "),n("template",{slot:"footer"},[n("down-mode",{attrs:{disabled:e.disabled,dropEnable:e.dropEnable,buttonTipText:e.downloadTipText,mode:e.downMode},on:{download:e.handleInputFinishAndDownload},nativeOn:{click:function(e){e.stopPropagation()}}})],1)],2)},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},,,,,,,,,,,,function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"xlx-dialog-code",on:{mouseleave:function(t){e.tipVisible=!1}}},[n("div",{staticClass:"td-tooltip-wrapper"},[n("div",{staticClass:"xlx-mobile-code",on:{mouseenter:function(t){e.tipVisible=!0,e.showGuideTip=!1}}},[n("i",{staticClass:"xlx-icon-code"}),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.tipVisible,expression:"tipVisible"}],staticClass:"xlx-mobile-code__drop"},[e._m(0),e._v(" "),n("p",[e._v("手机迅雷、微信、QQ扫码，下载到手机")])])]),e._v(" "),e.showGuideTip?n("div",{staticClass:"td-tooltip is-top"},[n("div",{staticClass:"td-tooltip__inner"},[e._v("手机下载更快更便捷")]),e._v(" "),n("span",{staticClass:"td-poper__arrow"}),e._v(" "),n("a",{staticClass:"td-tooltip__close",attrs:{href:"javascript:;"},on:{click:function(t){e.showGuideTip=!1}}},[n("i",{staticClass:"td-icon-close"})])]):e._e()]),e._v(" "),n("p",{on:{mouseenter:function(t){e.tipVisible=!0,e.showGuideTip=!1}}},[e._v("下载到手机，随时随地观看")])])},i=[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"code-picture"},[t("canvas",{attrs:{id:"qrcodecanvas"}})])}];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"xly-dialog-normal"},[e._m(0),e._v(" "),n("div",{staticClass:"xly-dialog-normal__content"},[n("ul",{staticClass:"xly-dialog__down-unordered"},[e._l(e.loadedList,function(t){return n("down-item-container",{key:t,attrs:{url:t},on:{setting:e.handleSetting}})}),e._v(" "),n("li",{directives:[{name:"load",rawName:"v-load",value:{handler:e.loadMore,distance:"50px"},expression:"{ handler: loadMore, distance: `50px` }"}]})],2)]),e._v(" "),n("div",{staticClass:"xly-dialog-normal__group"},[n("td-checkbox",{directives:[{name:"show",rawName:"v-show",value:e.groupVisible,expression:"groupVisible"}],attrs:{value:e.groupChecked},on:{input:e.handleGroupCheckChange}},[e._v(e._s(e.groupLabel))]),e._v(" "),n("td-input",{directives:[{name:"show",rawName:"v-show",value:e.groupEditShow&&e.groupVisible,expression:"groupEditShow && groupVisible"}],ref:"groupedit",attrs:{placeholder:"请输入任务组名称",value:e.groupName},on:{input:e.handleGroupNameChange},nativeOn:{click:function(e){e.stopPropagation()}}}),e._v(" "),n("span",{directives:[{name:"show",rawName:"v-show",value:!e.groupEditShow&&e.groupChecked&&e.groupVisible,expression:"!groupEditShow && groupChecked && groupVisible"}],staticClass:"xly-dialog-normal__label",on:{click:function(t){t.stopPropagation(),e.groupEditShow=!0}}},[e._v(e._s(e.groupNameDisplay))]),e._v(" "),n("p",{staticClass:"xly-dialog-normal__text"},[e._v("总：\n      "),n("span",{staticClass:"xly-dialog-normal__number"},[e._v(e._s(e.selectFileCount))]),e._v("个文件("),n("span",{staticClass:"xly-dialog-normal__number"},[e._v(e._s(e.totalSizeFormat))]),e._v(")")])],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.settingVisible,expression:"settingVisible"}],staticClass:"xly-down-setting",on:{click:e.stopSettingClickPropagation}},[n("ul",[n("li",{staticClass:"xly-down-setting__item xly-down-setting__item--link"},[n("td-tooltip",{staticStyle:{display:"block"},attrs:{content:e.tipsText,visible:e.isShowTips,offset:{y:30},placement:"top"}},[n("td-input",{attrs:{label:"下载链接："},model:{value:e.tempUrl,callback:function(t){e.tempUrl=t},expression:"tempUrl"}})],1)],1),e._v(" "),n("li",{staticClass:"xly-down-setting__item"},[n("td-checkbox",{model:{value:e.tempSettingInfo.openAfterDownload,callback:function(t){e.$set(e.tempSettingInfo,"openAfterDownload",t)},expression:"tempSettingInfo.openAfterDownload"}},[e._v("下载完成后打开")])],1),e._v(" "),n("li",{staticClass:"xly-down-setting__item"},[n("td-checkbox",{attrs:{disabled:!e.currentIsP2sp},model:{value:e.tempSettingInfo.onlyOrigin,callback:function(t){e.$set(e.tempSettingInfo,"onlyOrigin",t)},expression:"tempSettingInfo.onlyOrigin"}},[e._v("只从原始地址下载")]),e._v(" "),n("p",{staticClass:"xly-down-setting__number"},[n("limit-input",{attrs:{isNumber:!0,readonly:!e.currentIsP2sp,min:1,max:10,placement:"left",value:e.curThread},on:{input:function(t){return e.tempSettingInfo.thread=Number(t)}}},[n("span",{staticClass:"td-input__label"},[e._v("原始地址线程数(1-10)")])])],1)],1),e._v(" "),n("li",{staticClass:"xly-down-setting__item"},[n("td-checkbox",{attrs:{disabled:!e.currentIsP2sp},model:{value:e.tempSettingInfo.loginFtp,callback:function(t){e.$set(e.tempSettingInfo,"loginFtp",t)},expression:"tempSettingInfo.loginFtp"}},[e._v("登录FTP服务器")]),e._v(" "),n("div",{staticClass:"xly-form"},[n("td-input",{attrs:{label:"账号：",readonly:!e.tempSettingInfo.loginFtp},model:{value:e.tempSettingInfo.ftpInfo.userName,callback:function(t){e.$set(e.tempSettingInfo.ftpInfo,"userName",t)},expression:"tempSettingInfo.ftpInfo.userName"}}),e._v(" "),n("td-input",{attrs:{label:"密码：",readonly:!e.tempSettingInfo.loginFtp},model:{value:e.tempSettingInfo.ftpInfo.password,callback:function(t){e.$set(e.tempSettingInfo.ftpInfo,"password",t)},expression:"tempSettingInfo.ftpInfo.password"}})],1)],1),e._v(" "),n("li",{staticClass:"xly-down-setting__item xly-down-setting__item--link"},[n("td-input",{attrs:{placeholder:"请输入注释"},model:{value:e.tempSettingInfo.note,callback:function(t){e.$set(e.tempSettingInfo,"note",t)},expression:"tempSettingInfo.note"}})],1)]),e._v(" "),n("div",{staticClass:"xly-down-setting__operate"},[n("a",{staticClass:"xly-operate__button",attrs:{href:"javascript:;"},on:{click:function(t){return t.stopPropagation(),e.handleApplyAll(t)}}},[e._v("全部应用此设置")]),e._v(" "),n("a",{staticClass:"xly-operate__button",attrs:{href:"javascript:;"},on:{click:function(t){return t.stopPropagation(),e.handleApply(t)}}},[e._v("确定")])])])])},i=[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"xly-dialog-normal__title"},[t("span",{staticClass:"xly-dialog-normal__name"},[this._v("文件名")]),this._v(" "),t("span",{staticClass:"xly-dialog-normal__type"},[this._v("类型")]),this._v(" "),t("span",{staticClass:"xly-dialog-normal__size"},[this._v("大小")])])}];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},,,,,,,,,,,,function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"xly-dialog-normal__item"},[n("div",{staticClass:"xly-dialog-normal__name"},[n("td-checkbox",{attrs:{value:this.selected},on:{input:e.handleSelect}}),e._v(" "),n("i",{staticClass:"xly-icon-type is-small",class:e.taskIcon}),e._v(" "),n("span",{ref:"nameSpan",staticClass:"xly-dialog-normal__label",attrs:{title:e.titleOverFlow},on:{mouseover:function(t){return t.stopPropagation(),e.handleTaskNameMouseOver(t)},click:function(t){return t.stopPropagation(),e.handleTaskNameClick(t)}}},[e._v(e._s(e.editing?"":e.checkboxSlot))]),e._v(" "),n("a",{directives:[{name:"show",rawName:"v-show",value:!e.editing,expression:"!editing"}],staticClass:"xly-dialog-resource__rename",attrs:{href:"javascript:;",title:"重命名"},on:{click:function(t){return t.stopPropagation(),e.renameClick(t)}}},[n("i",{staticClass:"xly-icon-rename"})]),e._v(" "),n("select-native",{directives:[{name:"show",rawName:"v-show",value:e.editing,expression:"editing"}],ref:"renameSelect",attrs:{editable:"",value:e.name,options:e.optionsWithDefault},on:{input:e.handleRenameTemp,blur:e.handleRenameBlur},nativeOn:{click:function(e){e.stopPropagation()}}})],1),e._v(" "),n("span",{staticClass:"xly-dialog-normal__type",attrs:{title:e.type}},[e._v(e._s(e.type))]),e._v(" "),n("span",{staticClass:"xly-dialog-normal__size"},[e._v(e._s(e.foramtSize)+"\n    "),n("a",{staticClass:"xly-dialog-normal__choose",attrs:{href:"javascript:;"}},[n("i",{staticClass:"td-icon-arrow-drop",on:{click:e.settingClick}})])])])},i=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return i})},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){n(49),e.exports=n(711)},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(a,s)}l((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(38);i.CommonIPCRenderer.rendererCommunicator.initialize("preNewTaskRendererContext"),i.CommonIPCRenderer.rendererCommunicator.connect(),n(4).client.start({name:"preNewTaskRendererContext"});const o=n(52),a=n(7),s=n(31);n(65);const l=n(712),c=n(1);n(722);const u=c.default.getLogger("PreNewTaskRenderer");o.PerformanceMonitorStatNS.init("pre-new-task-renderer"),function(){return r(this,void 0,void 0,function*(){let e=yield a.asyncRemoteCall.getCurrentWindow(),t=!1;function i(){return r(this,void 0,void 0,function*(){do{if(t)break;t=!0;const{ThunderWindowNS:e}=yield Promise.resolve().then(()=>n(82));yield e.bringWindowToTop();const{ThunderToolsNS:r}=yield Promise.resolve().then(()=>n(62));r.enableDevTools().catch(e=>{u.warning(e)}),r.enableDragOpenFile(),yield Promise.resolve().then(()=>n(74))}while(0)})}e.once("show",i),(yield e.isVisible())&&(yield i())})}().catch();const d=n(552);s.default.use(d),s.default.config.ignoredElements=["webview"],new s.default({components:{App:l.default},render:e=>e("app")}).$mount("#app")},function(e,t,n){"use strict";n.r(t);var r=n(565),i=n(265);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);n(204),n(205),n(206),n(207),n(511),n(512),n(513);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\pre-new-task-renderer\\app.vue",t.default=s.exports},function(e,t,n){"use strict";n.r(t);var r=n(267);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);var o=n(0),a=Object(o.a)(r.default,void 0,void 0,!1,null,null,null);a.options.__file="src\\pre-new-task-renderer\\views\\pre-new-task-ctrl-container.vue",t.default=a.exports},function(e,t,n){"use strict";n.r(t);var r=n(638),i=n(269);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);n(721);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\pre-new-task-renderer\\views\\pre-new-task-ctrl.vue",t.default=s.exports},function(e,t,n){"use strict";n.r(t);var r=n(650),i=n(271);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);n(716);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\common\\views\\newtask-qrcode.vue",t.default=s.exports},function(e,t,n){"use strict";var r=n(1062);n.n(r).a},function(e,t,n){"use strict";n.r(t);var r=n(273);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);var o=n(0),a=Object(o.a)(r.default,void 0,void 0,!1,null,null,null);a.options.__file="src\\pre-new-task-renderer\\views\\pre-new-task\\down-info-container.vue",t.default=a.exports},function(e,t,n){"use strict";n.r(t);var r=n(665),i=n(275);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\pre-new-task-renderer\\views\\pre-new-task\\down-info.vue",t.default=s.exports},function(e,t,n){"use strict";n.r(t);var r=n(277);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);var o=n(0),a=Object(o.a)(r.default,void 0,void 0,!1,null,null,null);a.options.__file="src\\pre-new-task-renderer\\views\\pre-new-task\\down-item-container.vue",t.default=a.exports},function(e,t,n){"use strict";n.r(t);var r=n(677),i=n(279);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var a=n(0),s=Object(a.a)(i.default,r.a,r.b,!1,null,null,null);s.options.__file="src\\pre-new-task-renderer\\views\\pre-new-task\\down-item.vue",t.default=s.exports},function(e,t,n){"use strict";var r=n(1064);n.n(r).a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(31);n(61),n(86);const i=n(173),o=n(208),a=n(170),s=n(209),l=n(171),c=n(174),u=n(172),d=n(514);r.default.use(i.default),r.default.use(o.default),r.default.use(a.default),r.default.use(s.default),r.default.use(l.default),r.default.use(c.default),r.default.use(u.default),r.default.use(d.default)},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t){},,,,,,,,,function(e,t){},,,,function(e,t){},,,,,,,,,,function(e,t){},,,function(e,t){},,,function(e,t){},,function(e,t){},,,,function(e,t){},,function(e,t){},,function(e,t){}]);
//# sourceMappingURL=renderer.js.map