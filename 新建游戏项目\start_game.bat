@echo off
echo 启动斗地主游戏...
echo.
echo 请确保已安装Godot 4.4.1或更高版本
echo.

REM 尝试找到Godot可执行文件
set GODOT_PATH=""

REM 检查常见的Godot安装路径
if exist "C:\Program Files\Godot\godot.exe" (
    set GODOT_PATH="C:\Program Files\Godot\godot.exe"
) else if exist "C:\Program Files (x86)\Godot\godot.exe" (
    set GODOT_PATH="C:\Program Files (x86)\Godot\godot.exe"
) else if exist "%USERPROFILE%\Desktop\Godot\godot.exe" (
    set GODOT_PATH="%USERPROFILE%\Desktop\Godot\godot.exe"
) else (
    echo 未找到Godot可执行文件
    echo 请手动用Godot编辑器打开project.godot文件
    echo 或者将godot.exe添加到系统PATH中
    pause
    exit /b 1
)

echo 找到Godot: %GODOT_PATH%
echo 启动游戏...

REM 启动游戏
%GODOT_PATH% --main-pack project.godot

pause
