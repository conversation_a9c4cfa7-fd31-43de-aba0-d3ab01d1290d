module.exports=function(e){var t={};function n(i){if(t[i])return t[i].exports;var o=t[i]={i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(i,o,function(t){return e[t]}.bind(null,o));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=738)}({0:function(e,t,n){"use strict";function i(e,t,n,i,o,r,s,a){var l,d="function"==typeof e?e.options:e;if(t&&(d.render=t,d.staticRenderFns=n,d._compiled=!0),i&&(d.functional=!0),r&&(d._scopeId="data-v-"+r),s?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},d._ssrRegister=l):o&&(l=a?function(){o.call(this,this.$root.$options.shadowRoot)}:o),l)if(d.functional){d._injectStyles=l;var c=d.render;d.render=function(e,t){return l.call(t),c(e,t)}}else{var u=d.beforeCreate;d.beforeCreate=u?[].concat(u,l):[l]}return{exports:e,options:d}}n.d(t,"a",function(){return i})},1:function(e,t,n){e.exports=n(9)(137)},1078:function(e,t){},1080:function(e,t){},1082:function(e,t){},1084:function(e,t){},11:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(2),r=n(8),s=n(1).default.getLogger("XLStat");let a=r.default(o.join(__rootDir,"../bin/ThunderHelper.node"));function l(e=""){let t;if("string"==typeof e)t=e;else if(d(e)&&"object"==typeof e){let n=[];for(let t in e)d(e[t])&&n.push(t+"="+encodeURIComponent(e[t]));t=n.join(",")}return t}function d(e){return void 0!==e&&null!==e}!function(e){let t=null;function n(){return t||(t=a.xlstat4),t}function o(e,t="",o="",r=0,a=0,d=0,c=0,u="",h=0){return i(this,void 0,void 0,function*(){let i=0;do{if(void 0===e){i=1;break}let f=l(u);i="browser"===process.type?yield new Promise(s=>{i=n().asyncTrackEvent(e,t,o,r,a,d,c,f,h,e=>{s(e)})}):n().trackEvent(e,t,o,r,a,d,c,f,h),s.information(e,t,o,r,a,d,c,f,h)}while(0);return i})}function r(e,t=0){do{if(void 0===e)break;"browser"!==process.type&&n().trackClick(e,t)}while(0)}e.asyncTrackEvent=o,e.trackEvent=function(e,t="",n="",i=0,r=0,s=0,a=0,l="",d=0){o(e,t,n,i,r,s,a,l,d).catch()},e.trackEventEx=function(e,t="",n="",i=0){o(e,t,"",0,0,0,0,n,i).catch()},e.trackClick=r,e.trackShow=function(e,t=0){r(e,t)},e.setUserID=function(e=0,t=0){"browser"!==process.type&&n().setUserID(e,t)},e.initParam=function(e){return i(this,void 0,void 0,function*(){let t=-1;return t="browser"===process.type?yield new Promise(t=>{n().asyncInitParam(e,(e,n)=>{t(e?n:-1)})}):yield new Promise(t=>{n().initParamRemote(e,e=>{t(e)})})})},e.asyncUninit=function(e){return i(this,void 0,void 0,function*(){"browser"===process.type&&(yield new Promise(t=>{n().asyncUninit(e,()=>{t()})}))})},e.uninit=function(){"browser"===process.type&&n().waitFinish()}}(t.XLStatNS||(t.XLStatNS={}))},12:function(e,t){e.exports=require("events")},13:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const i=n(2);let o,r=console;function s(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,o=(new Error).stack.split("\n").slice(e+1);o.shift();let r=[];return o.forEach((e,o)=>{let s=t.exec(e)||n.exec(e),a={};s&&5===s.length&&(a.method=s[1],a.path=s[2],a.line=s[3],a.pos=s[4],a.file=i.basename(a.path),r.push(a))}),r}if(o="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return s(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){r.time(...e)},t.timeEnd=function(...e){r.timeEnd(...e)},t.trace=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),r.trace(o,...e)},t.info=function(...e){let t=s(),n="anonymous";t[0]&&t[0].method&&(n=n),r.info(o,"["+n+"]",e.join(","))},t.warn=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),r.warn("<WARN>"+o,"["+n+"]",e.join(","))},t.error=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),r.error("<ERROR>"+o,"["+n+"]",e.join(","))},t.log=function(...e){r.log(o,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},14:function(e,t){e.exports=require("os")},142:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(4);!function(e){e.getValue=function(e,t,n){return i(this,void 0,void 0,function*(){return yield o.client.callServerFunction("GetConfigValue",e,t,n)})},e.setValue=function(e,t,n){return i(this,void 0,void 0,function*(){yield o.client.callServerFunction("SetConfigValue",e,t,n)})}}(t.ConfigHelperNS||(t.ConfigHelperNS={}))},15:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n,i,o,r,s,a,l,d,c,u,h,f,p,m,g,v,w,S,_,T,b;!function(e){e[e.Unkown=0]="Unkown",e[e.Create=1]="Create",e[e.InvaldParam=2]="InvaldParam",e[e.InvaldLink=3]="InvaldLink",e[e.InvaldConfig=4]="InvaldConfig",e[e.Timeout=5]="Timeout",e[e.VerifyData=6]="VerifyData",e[e.Forbidden=7]="Forbidden",e[e.RangeDispatch=8]="RangeDispatch",e[e.FilePathOverRanging=9]="FilePathOverRanging",e[e.FileCreate=201]="FileCreate",e[e.FileWrite=202]="FileWrite",e[e.FileRead=203]="FileRead",e[e.FileRename=204]="FileRename",e[e.FileFull=205]="FileFull",e[e.FileOccupied=211]="FileOccupied",e[e.FileAccessDenied=212]="FileAccessDenied",e[e.BtUploadExist=601]="BtUploadExist",e[e.ForbinddenResource=701]="ForbinddenResource",e[e.ForbinddenAccount=702]="ForbinddenAccount",e[e.ForbinddenArea=703]="ForbinddenArea",e[e.ForbinddenCopyright=704]="ForbinddenCopyright",e[e.ForbinddenReaction=705]="ForbinddenReaction",e[e.ForbinddenPorn=706]="ForbinddenPorn",e[e.DownloadSDKCrash=10001]="DownloadSDKCrash",e[e.torrentFileNotExist=10002]="torrentFileNotExist"}(t=e.TaskError||(e.TaskError={})),function(e){e[e.Unkown=-1]="Unkown",e[e.Success=0]="Success",e[e.QueryFailed=1]="QueryFailed",e[e.ServerError=2]="ServerError",e[e.ResourceNotFound=3]="ResourceNotFound",e[e.AuthorizingFailed=4]="AuthorizingFailed",e[e.ForbidByCopyright=5]="ForbidByCopyright",e[e.ForbidByPorNoGraphy=6]="ForbidByPorNoGraphy",e[e.ForbidByReactionary=7]="ForbidByReactionary",e[e.ForbidByOtherFilter=8]="ForbidByOtherFilter"}(n=e.DcdnStatusCode||(e.DcdnStatusCode={})),function(e){e[e.Begin=-1]="Begin",e[e.Unkown=0]="Unkown",e[e.StandBy=1]="StandBy",e[e.PreDownloading=2]="PreDownloading",e[e.StartWaiting=3]="StartWaiting",e[e.StartPending=4]="StartPending",e[e.Started=5]="Started",e[e.StopPending=6]="StopPending",e[e.Stopped=7]="Stopped",e[e.Succeeded=8]="Succeeded",e[e.Failed=9]="Failed",e[e.Seeding=10]="Seeding",e[e.DestroyPending=11]="DestroyPending",e[e.End=12]="End"}(i=e.TaskStatus||(e.TaskStatus={})),function(e){e[e.Begin=-1]="Begin",e[e.StandBy=0]="StandBy",e[e.Stopped=1]="Stopped",e[e.Started=2]="Started",e[e.Complete=3]="Complete",e[e.Forbidden=4]="Forbidden",e[e.Error=5]="Error",e[e.End=6]="End"}(o=e.BtFileStatus||(e.BtFileStatus={})),function(e){e[e.DispatchStrategyNone=0]="DispatchStrategyNone",e[e.DispatchStrategyOrigin=1]="DispatchStrategyOrigin",e[e.DispatchStrategyP2s=2]="DispatchStrategyP2s",e[e.DispatchStrategyP2p=4]="DispatchStrategyP2p",e[e.DispatchStrategyAll=-1]="DispatchStrategyAll"}(r=e.DispatchStrategy||(e.DispatchStrategy={})),function(e){e[e.Unkown=0]="Unkown",e[e.P2sp=1]="P2sp",e[e.Bt=2]="Bt",e[e.Emule=3]="Emule",e[e.Group=4]="Group",e[e.Magnet=5]="Magnet"}(s=e.TaskType||(e.TaskType={})),function(e){e[e.Invalid=0]="Invalid",e[e.P2sp=1]="P2sp",e[e.Emule=2]="Emule"}(a=e.TaskCfgType||(e.TaskCfgType={})),function(e){e.Unkown="Unkown",e.Downloading="Downloading",e.Completed="Completed",e.Recycle="Recycle"}(l=e.CategroyViewID||(e.CategroyViewID={})),function(e){e[e.Unknow=0]="Unknow",e[e.TaskCreated=1]="TaskCreated",e[e.InsertToCategoryView=2]="InsertToCategoryView",e[e.RemoveFromCategoryView=3]="RemoveFromCategoryView",e[e.StatusChanged=4]="StatusChanged",e[e.DetailChanged=5]="DetailChanged",e[e.ChannelInfoChanged=6]="ChannelInfoChanged",e[e.DcdnStatusChanged=7]="DcdnStatusChanged",e[e.TaskDescriptionChanged=8]="TaskDescriptionChanged",e[e.TaskUserRead=9]="TaskUserRead",e[e.TaskRenamed=10]="TaskRenamed",e[e.TaskMovedEnd=11]="TaskMovedEnd",e[e.TaskMovingStateChange=12]="TaskMovingStateChange",e[e.BtSubFileDetailChanged=13]="BtSubFileDetailChanged",e[e.BtSubFileLoaded=14]="BtSubFileLoaded",e[e.BtSubFileForbidden=15]="BtSubFileForbidden",e[e.BtSubFileDcdnStatusChanged=16]="BtSubFileDcdnStatusChanged",e[e.TaskCategoryMovedEnd=17]="TaskCategoryMovedEnd",e[e.GroupTaskSubFileDetailChanged=18]="GroupTaskSubFileDetailChanged",e[e.TaskDestroying=19]="TaskDestroying",e[e.TaskDestroyed=20]="TaskDestroyed"}(d=e.TaskEventType||(e.TaskEventType={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.VirtualId=2]="VirtualId",e[e.SrcTotal=3]="SrcTotal",e[e.SrcUsing=4]="SrcUsing",e[e.FileSize=5]="FileSize",e[e.ReceivedSize=6]="ReceivedSize",e[e.DownloadSize=7]="DownloadSize",e[e.TotalDownloadSize=8]="TotalDownloadSize",e[e.CreateTime=9]="CreateTime",e[e.CompletionTime=10]="CompletionTime",e[e.DownloadingPeriod=11]="DownloadingPeriod",e[e.Progress=12]="Progress",e[e.RecycleTime=13]="RecycleTime",e[e.FileCreated=14]="FileCreated",e[e.Forbidden=15]="Forbidden",e[e.CategoryId=16]="CategoryId",e[e.UserRead=17]="UserRead",e[e.OpenOnComplete=18]="OpenOnComplete",e[e.GroupTaskId=19]="GroupTaskId",e[e.DownloadSubTask=20]="DownloadSubTask",e[e.NameType=21]="NameType",e[e.OwnerProduct=22]="OwnerProduct",e[e.FileIndex=23]="FileIndex",e[e.NameFixed=24]="NameFixed",e[e.ValidDownloadSize=25]="ValidDownloadSize",e[e.RealDownloadSize=26]="RealDownloadSize",e[e.ResourceLegal=27]="ResourceLegal",e[e.TaskType=28]="TaskType",e[e.ErrorCode=29]="ErrorCode",e[e.PlayPosition=30]="PlayPosition",e[e.Duration=31]="Duration",e[e.NumberEnd=32]="NumberEnd",e[e.BooleanStart=4096]="BooleanStart",e[e.Destroyed=4097]="Destroyed",e[e.Background=4098]="Background",e[e.Moving=4099]="Moving",e[e.BooleanEnd=4100]="BooleanEnd",e[e.StringStart=8192]="StringStart",e[e.TaskName=8193]="TaskName",e[e.SavePath=8194]="SavePath",e[e.RelativePath=8195]="RelativePath",e[e.TaskUrl=8196]="TaskUrl",e[e.RefUrl=8197]="RefUrl",e[e.Cid=8198]="Cid",e[e.Gcid=8199]="Gcid",e[e.Cookie=8200]="Cookie",e[e.ProductInfo=8201]="ProductInfo",e[e.Origin=8202]="Origin",e[e.Description=8203]="Description",e[e.UserData=8204]="UserData",e[e.OriginName=8205]="OriginName",e[e.HTTPContentType=8206]="HTTPContentType",e[e.CategoryViewId=8207]="CategoryViewId",e[e.YunTaskId=8208]="YunTaskId",e[e.StringEnd=8209]="StringEnd",e[e.ObjectStart=12288]="ObjectStart",e[e.ObjectEnd=12289]="ObjectEnd"}(c=e.TaskAttribute||(e.TaskAttribute={})),function(e){e[e.UnKnown=0]="UnKnown",e[e.SrcTotal=1]="SrcTotal",e[e.SrcUsing=2]="SrcUsing",e[e.FileSize=4]="FileSize",e[e.ReceivedSize=8]="ReceivedSize",e[e.DownloadSize=16]="DownloadSize",e[e.CompletionTime=32]="CompletionTime",e[e.DownloadingPeriod=64]="DownloadingPeriod",e[e.Progress=128]="Progress",e[e.RecycleTime=256]="RecycleTime",e[e.FileCreated=512]="FileCreated",e[e.Forbidden=1024]="Forbidden",e[e.UserRead=2048]="UserRead",e[e.OpenOnComplete=4096]="OpenOnComplete",e[e.DownloadSubTask=8192]="DownloadSubTask",e[e.TaskName=16384]="TaskName",e[e.SavePath=32768]="SavePath",e[e.Cid=65536]="Cid",e[e.Gcid=131072]="Gcid",e[e.UserData=262144]="UserData",e[e.CategoryViewId=524288]="CategoryViewId",e[e.ErrorCode=1048576]="ErrorCode",e[e.TaskSpeed=2097152]="TaskSpeed",e[e.ChannelInfo=4194304]="ChannelInfo",e[e.ValidDownloadSize=8388608]="ValidDownloadSize",e[e.OriginName=16777216]="OriginName",e[e.HTTPContentType=33554432]="HTTPContentType",e[e.PlayPosition=67108864]="PlayPosition",e[e.Duration=134217728]="Duration",e[e.YunTaskId=268435456]="YunTaskId"}(u=e.TaskDetailChangedFlags||(e.TaskDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(h=e.BtSubFileDetailChangedFlags||(e.BtSubFileDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(f=e.GroupTaskSubFileDetailChangedFlags||(e.GroupTaskSubFileDetailChangedFlags={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.FileStatus=2]="FileStatus",e[e.DownloadSize=3]="DownloadSize",e[e.FileSize=4]="FileSize",e[e.EnableDcdn=5]="EnableDcdn",e[e.ErrorCode=6]="ErrorCode",e[e.DcdnStatus=7]="DcdnStatus",e[e.RealIndex=8]="RealIndex",e[e.FileOffset=9]="FileOffset",e[e.Visible=10]="Visible",e[e.Download=11]="Download",e[e.UserRead=12]="UserRead",e[e.PlayPosition=13]="PlayPosition",e[e.Duration=14]="Duration",e[e.NumberEnd=15]="NumberEnd",e[e.StringStart=4096]="StringStart",e[e.FinalName=4097]="FinalName",e[e.RelativePath=4098]="RelativePath",e[e.FileName=4099]="FileName",e[e.FilePath=4100]="FilePath",e[e.Cid=4101]="Cid",e[e.Gcid=4102]="Gcid",e[e.StringEnd=4103]="StringEnd"}(p=e.BtFileAttribute||(e.BtFileAttribute={})),function(e){e[e.P2spUrl=0]="P2spUrl",e[e.BtInfoId=1]="BtInfoId",e[e.EmuleFileHash=2]="EmuleFileHash",e[e.MagnetUrl=3]="MagnetUrl",e[e.GroupTaskName=4]="GroupTaskName"}(m=e.KeyType||(e.KeyType={})),function(e){e[e.NameInclude=1]="NameInclude",e[e.BtDisplayNameInclude=2]="BtDisplayNameInclude"}(g=e.SearchKeyType||(e.SearchKeyType={})),function(e){e[e.ExtEqual=1]="ExtEqual",e[e.NameLikeAndExtEqual=2]="NameLikeAndExtEqual",e[e.TaskTypeEqual=4]="TaskTypeEqual"}(v=e.FilterKeyType||(e.FilterKeyType={})),function(e){e[e.All=0]="All",e[e.CommonForeground=1]="CommonForeground",e[e.CommonBackground=2]="CommonBackground",e[e.Temporary=3]="Temporary",e[e.PreDownload=4]="PreDownload",e[e.PrivateForeground=5]="PrivateForeground"}(w=e.KeyFilter||(e.KeyFilter={})),function(e){e[e.Unknown=-1]="Unknown",e[e.LoadTaskBasic=0]="LoadTaskBasic",e[e.Create=1]="Create",e[e.Complete=2]="Complete",e[e.Recycle=3]="Recycle",e[e.Recover=4]="Recover",e[e.ReDownload=5]="ReDownload",e[e.MoveThoughCategory=6]="MoveThoughCategory"}(S=e.TaskInsertReason||(e.TaskInsertReason={})),function(e){e[e.Unknown=-1]="Unknown",e[e.Manual=0]="Manual",e[e.PauseAll=1]="PauseAll",e[e.DeleteTask=2]="DeleteTask",e[e.TaskJammed=3]="TaskJammed",e[e.LowSpeed=4]="LowSpeed",e[e.MaxDownloadReduce=5]="MaxDownloadReduce",e[e.MoveTask=6]="MoveTask",e[e.SelectDownloadLists=7]="SelectDownloadLists",e[e.PrivateLoginOut=8]="PrivateLoginOut",e[e.FreeDownload=9]="FreeDownload",e[e.Exit=10]="Exit"}(_=e.TaskStopReason||(e.TaskStopReason={})),function(e){e[e.RESOURCE_FROM_MEMBER=1]="RESOURCE_FROM_MEMBER",e[e.RESOURCE_FROM_OFFLINE=2]="RESOURCE_FROM_OFFLINE",e[e.RESOURCE_FROM_CRYSTAL_LARGE=4]="RESOURCE_FROM_CRYSTAL_LARGE",e[e.RESOURCE_FROM_CRYSTAL_SMALL=8]="RESOURCE_FROM_CRYSTAL_SMALL",e[e.RESOURCE_FROM_DCDN=16]="RESOURCE_FROM_DCDN",e[e.RESOURCE_FROM_FREEDCDN=32]="RESOURCE_FROM_FREEDCDN"}(T=e.XLResourceFrom||(e.XLResourceFrom={})),function(e){e[e.XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD=0]="XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD",e[e.XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING=1]="XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING",e[e.XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING=2]="XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING"}(b=e.XLDownloadStrategy||(e.XLDownloadStrategy={}))}(t.DownloadKernel||(t.DownloadKernel={}))},154:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(34),o=n(44),r=n(1).default.getLogger("Thunder.shub-http.http-session"),s=n(40),a=n(16),{isDef:l}=a.ThunderUtil;var d;!function(e){e.HTTP="HTTP",e.HTTPS="HTTPS"}(d=t.Protocol||(t.Protocol={}));t.HttpSession=class{constructor(){this.mRetries=0,this.mHost=void 0,this.mPort=void 0,this.mPath=void 0,this.mAuth=void 0,this.mAccept=void 0,this.mBody=null,this.mUrl=void 0,this.mCookie=void 0,this.mProtocol=d.HTTP,this.mTimeout=void 0,this.mCurRetries=0}set host(e){this.mHost=e}get host(){return this.mHost}set port(e){this.mPort=e}get port(){let e=void 0;return e=l(this.mPort)?this.mPort:this.protocol===d.HTTPS?443:80}set path(e){this.mPath=e}get path(){return this.mPath}set url(e){this.mUrl=e}get protocol(){return this.mProtocol}set protocol(e){this.mProtocol=e}get url(){return this.mUrl}get cookie(){return this.mCookie}set cookie(e){this.mCookie=e}set auth(e){this.mAuth=e}get auth(){return this.mAuth}set accept(e){this.mAccept=e}get accept(){return this.mAccept}set body(e){this.mBody=e}get body(){return this.mBody}set retries(e){this.mRetries=e}get retries(){return this.mRetries}set timeout(e){this.mTimeout=e}get timeout(){return this.mTimeout}post(e,t){do{let n=this.body;if(!n){r.information("body is empty"),t(null);break}let i=this.auth,o=this.accept,s={hostname:this.host,port:this.port,path:this.path||"/",method:"POST",auth:i||void 0,headers:{"Content-Length":n?n.length:0,Cookie:this.cookie||"",Accept:o||"*/*"}};r.verbose("option",s);try{this.postImpl(n,s,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,r.information("mCurRetries",this.mCurRetries),this.post(e,t)):t(n)})}catch(e){r.warning("error ",e),t(null)}}while(0)}get(e,t){let n=null;if(this.url){let e=i.parse(this.url,!0);e&&"https:"===e.protocol?this.protocol=d.HTTPS:this.protocol=d.HTTP,n=this.url}else{let e=this.auth,t=this.accept;n={hostname:this.host,port:this.port,path:this.path||"/",method:"GET",auth:e||void 0,headers:{Cookie:this.cookie||"",Accept:t||"*/*"}}}r.verbose("option",n);try{this.getImpl(n,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,r.information("mCurRetries",this.mCurRetries),this.get(e,t)):t(n)})}catch(e){r.warning("error ",e),t(null)}}postImpl(e,t,n,i){let a=(this.protocol===d.HTTPS?s:o).request(t,e=>{let t=null;e.on("data",e=>{t=t?Buffer.concat([t,e]):e}),e.on("end",()=>{r.information("statusCode",e.statusCode),r.information("headers",e.headers),n({statusCode:e.statusCode,headers:e.headers,body:t})})});a.on("error",e=>{i(e)}),a.on("timeout",()=>{a.abort()}),this.timeout&&a.setTimeout(this.timeout),a.write(e),a.end()}getImpl(e,t,n){(this.protocol===d.HTTPS?s:o).get(e,e=>{let n=null;e.on("data",e=>{n=n?Buffer.concat([n,e]):e}),e.on("end",()=>{r.information("statusCode",e.statusCode),r.information("headers",e.headers),t({statusCode:e.statusCode,headers:e.headers,body:n})})}).on("error",e=>{n(e)})}}},157:function(e,t,n){"use strict";var i=n(1078);n.n(i).a},16:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(3),r=n(2),s=n(1),a=n(18),l=n(25),d=s.default.getLogger("Thunder.Util"),c="Thunder Network\\Thunder7.9\\";function u(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(){let e=l.ThunderHelper.getSystemTempPath(),t=l.ThunderHelper.getLogicalDriveStrings(),n=0;for(let i=0;i<t.length;i++){if(l.ThunderHelper.getDriveType(t[i])===l.ThunderHelper.DriverType.DRIVE_FIXED){let o=l.ThunderHelper.getDriveInfo(t[i]);n<o.freeBytes&&t[i]!==e&&(n=o.freeBytes,e=t[i])}}return e.substring(0,1)+":\\迅雷下载"}function s(e){let t=(e.style.webkitTransform||getComputedStyle(e,"").getPropertyValue("-webkit-transform")||e.style.transform||getComputedStyle(e,"").getPropertyValue("transform")).match(/\-?[0-9]+\.?[0-9]*/g);return{x:parseInt(t&&(t[12]||t[4])||"0",10),y:parseInt(t&&(t[13]||t[5])||"0",10)}}function h(e){let t=!1;do{let n="",i="";if(/^[a-zA-Z]:\\/.test(e))n=e.slice(3);else{if(0!==e.indexOf("\\\\"))break;{let t=e.indexOf("\\",2);if(-1===t||t===e.length-1)break;if(""===(i=(n=e.slice(2)).substr(0,t-2)))break}}if(/[\*\"<>\?:\|]/i.test(n))break;if(e.length>256)break;if(""===i){t=!0;break}let o=i.indexOf(".ipv6-literal.net");-1!==o?(-1!==(o=(i=i.substr(0,o)).indexOf("%"))&&(i=i.substr(0,o)),i=i.replace(/\-/g,":"),/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/.test(i)&&(t=!0)):/(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))/.test(i)&&(t=!0)}while(0);return t}e.formatSize=function(e,t){0===t||(t=t||2);let n="0B";if("number"==typeof e&&e>0){let i=["B","KB","MB","GB","TB"],o=0,r=e;for(;r>=1e3&&!(o>=4);)r/=1024,o+=1;n=-1===String(r).indexOf(".")?r+i[o]:r.toFixed(t)+i[o]}return n},e.formatSizeCustom=function(e,t=2,n=5){let i="0B";if("number"==typeof e&&e>0){let o=["B","KB","MB","GB","TB"],r=0,s=e;for(;s>=1e3&&!(r>=4);)s/=1024,r+=1;if(-1===String(s).indexOf("."))i=s+o[r];else{let e=s.toFixed(t);e.length<=n?i="KB"!==o[r]&&"MB"!==o[r]||1===t?e+o[r]:s.toFixed(1)+o[r]:("."===(e=e.substr(0,n))[n-1]&&(e=e.substr(0,n-1)),i=e+o[r])}}return i},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.getDefaultDownloadDir=t,e.getMaxFreeDriver=function(){return t().substring(0,1)},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){d.warning(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",i=!1,o=[],r=e.length;for(let s=0;s<r;s++){let a=e[s];if('"'!==a&&"'"!==a||(""===n?(i=!0,n=a):n===a&&(i=!1,n=""))," "!==a||i||s===r-1){if(s===r-1){let n=e.substring(t);""!==n.trim()&&o.push(u(n))}}else{let n=e.substring(t,s);""!==n.trim()&&o.push(u(n)),t=s+1}}return o},e.getThunderTempPath=function(e,t){return i(this,void 0,void 0,function*(){const i=yield Promise.resolve().then(()=>n(14));let o=r.join(i.tmpdir(),c);return t&&(o=r.join(o,t)),void 0!==e&&e&&(yield a.FileSystemAWNS.mkdirsAW(o)),o})},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,i)=>{e+=0===i?"?":"&",e+=`${n}=${t[n]}`}),e},e.setQueryStringEx=function(e,t){for(let n in t)e+=-1===e.indexOf("?")?"?":"&",e+=`${n}=${t[n]}`;return e},e.getQueryString=function(e,t){return e&&t&&e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:""},e.isClipboardTextFormatAvailable=function(){let e=!1,t=o.clipboard.availableFormats();for(let n of t)if("text/plain"===n){e=!0;break}return e},e.keywordsHighLight=function(e,t,n){if(!e)return"";if(!t)return e;if(0===e.length)return e;if(0===t.length)return e;let i=/\\/,o=t.split(" ");if(0===(o=o.filter(e=>e.trim().length>0)).length)return e;for(let t=0;t<o.length;t++)if(o[t].search(i)>0)return e;n=void 0===n||null===n?"#FF0000":n;let r="",s=["\\[","\\^","\\*","\\(","\\)","\\|","\\?","\\$","\\.","\\+"],a="",l="|";for(let e=0;e<o.length;e++){for(let t=0;t<s.length;t++){let n=new RegExp(s[t],"g");o[e]=o[e].replace(n,s[t])}e===o.length-1&&(l=""),a=a.concat(o[e],l)}let d=new RegExp(a,"gi");return r=e.replace(d,e=>'<span style= "color:'+n+'">'+e+"</span>")},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setStyle=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style[t]=n})},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),i=t.split("."),o=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(i[e])>0){o=1;break}if(Number(n[e])-Number(i[e])<0){o=-1;break}}return o},e.throttle=function(e,t){let n,i=0;return(...o)=>{const r=Date.now();clearTimeout(n),r-i>t?(e(...o),i=r):n=setTimeout(()=>{e(...o),i=r},t)}},e.debounce=function(e,t){let n=null;return(...i)=>{n&&clearTimeout(n),n=setTimeout(()=>{e(...i)},t)}},e.getElementFixed=function(e){let t=e.offsetLeft,n=e.offsetTop,i=e.offsetParent;for(;null!==i;){let e=s(i);t+=i.offsetLeft+e.x,n+=i.offsetTop+e.y,i=i.offsetParent}return{x:t,y:n}},e.escapeHTML=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")},e.unescapeHTML=function(e){return e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")},e.isValidPath=h,e.isValidDownloadPath=function(e){return i(this,void 0,void 0,function*(){let t=!1;do{if(e.length<3)break;if("私人空间"===e){t=!0;break}if(l.ThunderHelper.getDriveType(e)===l.ThunderHelper.DriverType.DRIVE_REMOTE){t=!0;break}if(!h(e))break;if(!(yield a.FileSystemAWNS.dirExistsAW(e))&&!(yield a.FileSystemAWNS.mkdirsAW(e)))break;t=!0}while(0);return t})};let f=void 0;function p(e,t="normal 12px sans-serif"){f||(f=document.createElement("canvas"));let n=f.getContext("2d");return n.font=t,n.measureText(e).width}function m(e,t,n="normal 12px sans-serif",i=1){function o(e,t,i,r,s){let a=-1;do{if(e>t){a=t;break}let l=Math.round((e+t)/2),d=p(`${i.substr(0,l)}...${r}`,n);if(s===d){a=l;break}if(s>d){if(Math.round(s)===Math.round(d)){a=l;break}a=o(l+1,t,i,r,s)}else if(d>s){if(Math.round(s)===Math.round(d)){a=l-1;break}a=o(e,l-1,i,r,s)}}while(0);return a}let s=e;do{if(!t)break;if(!e)break;let a=t.offsetWidth*i;if(a>p(e,n))break;let l=r.extname(e);""!==l&&(l=l.substring(1));let d=e.substr(0,e.length-l.length-1);if(!d)break;let c=o(0,d.length,d,l,a);if(-1===c)break;s=`${d.substr(0,c-2*(i-1))}...${l}`}while(0);return s}e.getTextWidth=p,e.getOmitName=m,e.getOmitNameMultiLine=function(e,t,n){return m(e,t,"normal 12px microsoft yahei",2)},e.setTimeoutAw=function(e,t){return new Promise((n,i)=>{setTimeout(()=>{t&&t(),n()},e)})}}(t.ThunderUtil||(t.ThunderUtil={}))},176:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(42),r=n(7),s=n(2),a=n(8),l=n(15),d=n(16),c=n(11),u=n(1).default.getLogger("Thunder.FloatPanelHelper"),h=a.default(s.join(__rootDir,"../bin/ThunderHelper.node"));let f=null,p=null,m=0,g=0;!function(e){e[e.LeftBottom=0]="LeftBottom",e[e.LeftTop=1]="LeftTop",e[e.RightTop=2]="RightTop",e[e.RightBottom=3]="RightBottom"}(t.FloatPanelDirection||(t.FloatPanelDirection={})),function(e){function t(){return i(this,void 0,void 0,function*(){return null===f&&(f=yield o.global.mainRendererWindow.__resolve()),f})}function n(){return i(this,void 0,void 0,function*(){return null===p&&(p=yield r.asyncRemoteCall.getCurrentWindow()),p})}function s(){return i(this,void 0,void 0,function*(){if(!m){let e=yield(yield t()).getNativeWindowHandle();m=e.readUIntLE(0,4)}return m})}let a,v,w;function S(e){let t=null,n=e.receiveSize,i=e.srcUsing;do{if(e.taskType===l.DownloadKernel.TaskType.P2sp){if(n>0){t=a.Stared;break}if(e.taskStatus===l.DownloadKernel.TaskStatus.StartPending){t=a.Starting;break}if(e.taskStatus===l.DownloadKernel.TaskStatus.Started&&0===n){t=a.Connect;break}}else if(e.taskType===l.DownloadKernel.TaskType.Bt){if(n>0){t=v.Stared;break}if(e.taskStatus===l.DownloadKernel.TaskStatus.StartPending){t=v.Analy;break}if(e.taskStatus===l.DownloadKernel.TaskStatus.Started){t=i>0?v.Connect:v.Search;break}}else if(e.taskType===l.DownloadKernel.TaskType.Emule){if(n>0){t=w.Stared;break}if(e.taskStatus===l.DownloadKernel.TaskStatus.StartPending){t=w.Starting;break}if(e.taskStatus===l.DownloadKernel.TaskStatus.Started){t=i>0?w.Connect:w.Search;break}}}while(0);return t}e.pageSize=20,e.noneReactiveTaskBaseInfos={},e.cloudNoneReactiveTasksMap={},e.getMainWindow=t,e.getFloatPanelWindow=n,e.getMainWindowHandle=s,e.getFloatPanelWindowHandle=function(){return i(this,void 0,void 0,function*(){if(!g){let e=yield(yield n()).getNativeWindowHandle();g=e.readUIntLE(0,4)}return g})},e.getDpiFactor=function(){let e=1;return e=h.getDPIAwareSupport()?h.getMonitorDPIFactor(s()):h.getSysDPIFactor()},e.traceFloatPanelEvent=function(e,t){let n="";null!==t&&void 0!==t&&""!==t&&(n=t),u.information(e,n),c.XLStatNS.trackEvent("xlx_vip_event",e,"",0,0,0,0,n)},e.trackEvent=function(e,t){let n="";null!==t&&void 0!==t&&""!==t&&(n=t),u.information(e,n),c.XLStatNS.trackEvent("client_quick",e,"",0,0,0,0,n)},e.isDownloadStatus=function(e){let t=!1;return e!==l.DownloadKernel.TaskStatus.StartPending&&e!==l.DownloadKernel.TaskStatus.StartWaiting&&e!==l.DownloadKernel.TaskStatus.Started||(t=!0),t},e.formatSpeed=function(e){let t={speed:"0",unit:"B/s"};if("number"==typeof e&&e>0){let n=["B/s","KB/s","MB/s","GB/s","TB/s"],i=0,o=e;for(;o>=1e3&&!(i>=4);)o/=1024,i+=1;t=-1===String(o).indexOf(".")?{speed:String(o),unit:n[i]}:{speed:o.toFixed(1),unit:n[i]}}return t},e.isThunderMainWndForeground=function(){let e=h.getForegroundProcessName();u.information("foreProcName:",e);let t=!1;return(e.toLowerCase().indexOf("thunder.exe")>-1||e.toLowerCase().indexOf("xlbrowser.exe")>-1)&&(t=!0),t},function(e){e[e.Unkown=0]="Unkown",e[e.Starting=1]="Starting",e[e.Connect=2]="Connect",e[e.Stared=3]="Stared"}(a||(a={})),function(e){e[e.Unkown=0]="Unkown",e[e.Analy=1]="Analy",e[e.Search=2]="Search",e[e.Connect=3]="Connect",e[e.Stared=4]="Stared"}(v||(v={})),function(e){e[e.Unkown=0]="Unkown",e[e.Starting=1]="Starting",e[e.Search=2]="Search",e[e.Connect=3]="Connect",e[e.Stared=4]="Stared"}(w||(w={})),e.getTaskStartPrompt=function(e){let t=void 0;do{if(null===e)break;if(e.taskStatus===l.DownloadKernel.TaskStatus.StartPending||e.taskStatus===l.DownloadKernel.TaskStatus.Started){if(e.taskType===l.DownloadKernel.TaskType.P2sp){let n=S(e);if(null===n)break;n===a.Connect&&(t="连接资源");break}if(e.taskType===l.DownloadKernel.TaskType.Bt){let n=S(e);if(null===n)break;n===v.Analy?t="解析种子":n===v.Search?t="寻找资源":n===v.Connect&&(t="连接资源");break}if(e.taskType===l.DownloadKernel.TaskType.Emule){let n=S(e);if(null===n)break;n===w.Search?t="寻找资源":n===w.Connect&&(t="连接资源")}}}while(0);return t},e.getTaskStatusPrompt=function(e){let t="";if(e)switch(e.taskStatus){case l.DownloadKernel.TaskStatus.Unkown:t="未知错误";break;case l.DownloadKernel.TaskStatus.StandBy:t="准备开始";break;case l.DownloadKernel.TaskStatus.PreDownloading:t="等待中";break;case l.DownloadKernel.TaskStatus.StartWaiting:t="排队等待";break;case l.DownloadKernel.TaskStatus.StartPending:t="正在开始";break;case l.DownloadKernel.TaskStatus.Started:e.downloadSpeed>=0&&(t=d.ThunderUtil.formatSize(e.downloadSpeed,1),t+="/S");break;case l.DownloadKernel.TaskStatus.StopPending:t="正在停止";break;case l.DownloadKernel.TaskStatus.Stopped:t="暂停";break;case l.DownloadKernel.TaskStatus.Succeeded:t="完成";break;case l.DownloadKernel.TaskStatus.Failed:t="任务出错";break;case l.DownloadKernel.TaskStatus.Seeding:t="完成"}return t},e.getProgress=function(e){let t=0;if(e){let n=e.downloadSize,i=e.taskStatus;if(i===l.DownloadKernel.TaskStatus.Succeeded||i===l.DownloadKernel.TaskStatus.Seeding)t=100;else{let i=e.fileSize;0!==i&&(t=n/i*100,(t=parseInt(t.toString(),10))>100?t=100:t<0&&(t=0))}}return t}}(t.FloatPanelHelper||(t.FloatPanelHelper={}))},177:function(e,t,n){e.exports=n(9)(138)},18:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(21),r=n(2),s=n(6),a=n(60),l=s.promisify,d=n(1).default.getLogger("Thunder.base.fs-utilities");!function(e){function t(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(o.access);try{yield n(e),t=!0}catch(e){d.information(e)}}return t})}function s(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(o.mkdir);try{yield n(e),t=!0}catch(e){d.warning(e)}}return t})}function c(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(o.rmdir);try{yield n(e),t=!0}catch(e){d.warning(e)}}return t})}function u(e){return i(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=l(o.unlink);try{yield n(e),t=!0}catch(e){d.warning(e)}}return t})}function h(e){return i(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(o.readdir);try{t=yield n(e)}catch(e){d.warning(e)}}return t})}function f(e){return i(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(o.lstat);try{t=yield n(e)}catch(e){d.warning(e)}}return t})}function p(e,t){return i(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let i=r.join(e,t),o=yield f(i);n=null!==o&&o.isDirectory()?yield m(i):yield u(i)}return n})}function m(e){return i(this,void 0,void 0,function*(){let n=!1;if(void 0!==e){if(yield t(e)){n=!0;let t=(yield h(e))||[];for(let i=0;i<t.length;i++)n=(yield p(e,t[i]))&&n;(n||0===t.length)&&(n=(yield c(e))&&n)}}return n})}function g(e){return i(this,void 0,void 0,function*(){let n=!1;return d.information("mkdirsAW",e),void 0!==e&&((yield t(e))?n=!0:r.dirname(e)===e?n=!1:(yield g(r.dirname(e)))&&(n=yield s(e))),n})}function v(e,n){return i(this,void 0,void 0,function*(){let i;if(e.toLowerCase()!==n.toLowerCase()&&(yield t(e))){let t=o.createReadStream(e),r=o.createWriteStream(n);i=new Promise(e=>{t.pipe(r).on("finish",()=>{e(!0)})})}else i=new Promise(e=>{e(!1)});return i})}e.readFileAW=function(e){return i(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=l(o.readFile);try{t=yield n(e)}catch(e){d.warning(e)}}return t})},e.readLineAw=function(e){return i(this,void 0,void 0,function*(){let n=null;do{if(!e)break;if(!t(e))break;n=yield new Promise(t=>{let n=[];const i=o.createReadStream(e),r=a.createInterface({input:i});r.on("line",e=>{n.push(e)}),r.on("close",()=>{t(n)})})}while(0);return n})},e.writeFileAW=function(e,t){return i(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const i=l(o.writeFile);try{yield i(e,t),n=!0}catch(e){d.warning(e)}}return n})},e.existsAW=t,e.dirExistsAW=function(e){return i(this,void 0,void 0,function*(){let n=!1;do{if(!(n=yield t(e)))break;let i=yield f(e);if(!i)break;n=i.isDirectory()}while(0);return n})},e.mkdirAW=s,e.rmdirAW=c,e.unlinkAW=u,e.readdirAW=h,e.lstatAW=f,e.rmdirsAW=m,e.mkdirsAW=g,e.renameAW=function(e,t){return i(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=l(o.rename);try{yield n(e,t)}catch(e){d.warning(e)}}})},e.copyFileAW=v,e.copyDirsAW=function e(n,o){return i(this,void 0,void 0,function*(){let i=!1,s=yield f(n);if(s.isDirectory()){i=yield g(o);let a=(yield h(n))||[];for(let l=0;l<a.length;l++){let d=r.join(n,a[l]),c=r.join(o,a[l]);(i=yield t(d))&&(i=(s=yield f(d)).isDirectory()?yield e(d,c):yield v(d,c))}}return i})},e.mkdtempAW=function(){return i(this,void 0,void 0,function*(){let e=!1;const t=l(o.mkdtemp),i=(yield Promise.resolve().then(()=>n(14))).tmpdir();try{e=yield t(`${i}${r.sep}`)}catch(e){d.warning(e)}return e})},e.deleteEmptySubDirs=function(e,n){return i(this,void 0,void 0,function*(){let i=!0;e=r.normalize(e),n=r.normalize(n),e.length>3&&"\\"===e[e.length-1]&&(e=e.slice(0,e.length-1)),n.length>3&&"\\"===n[n.length-1]&&(n=n.slice(0,n.length-1));do{if(0!==e.indexOf(n)){i=!1;break}let o=e;for(;o!==n;){if((yield t(o))&&!(yield c(o))){i=!1;break}o=r.dirname(o)}}while(0);return i})},e.getFileSize=function e(n){return i(this,void 0,void 0,function*(){let i=0;do{if(!n)break;if(!(yield t(n)))break;let o=yield f(n);if(o)if(o.isDirectory()){let t=yield h(n);for(let o=0;o<t.length;o++)i+=(yield e(r.join(n,t[o])))}else i=o.size}while(0);return i})},e.isDirectoryEmptyAW=function(e,n=!0){return i(this,void 0,void 0,function*(){let i=!0;do{if(!e){i=!1;break}if(!(yield t(e))){i=n;break}let o=yield f(e);if(!o){i=!1;break}if(!o.isDirectory()){i=!1;break}if((yield h(e)).length>0){i=!1;break}}while(0);return i})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},2:function(e,t){e.exports=require("path")},20:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.channelRMNewTaskReadyForSetTaskData="RM_NEWTASK_READYRORSETTASKDATA",e.channelRMNewTaskSetTaskData="RM_NEWTASK_SETTASKDATA",e.channelRMPreNewTaskSetTaskData="RM_PRENEWTASK_SETTASKDATA",e.channelRMNewTaskCreateNewTask="RM_NEWTASK_CREATENEWTASK",e.channelRMNewTaskClose="RM_NEWTASK_CLOSE",e.channelRMPreNewTaskClose="RM_PRENEWTASK_CLOSE",e.channelRMNewTaskSetBTInfo="RM_NEWTASK_SETBTINFO",e.channelRMNewTaskDownloadTorrent="RM_NEW_TASK_DOWNLOAD_TORRENT",e.channelRMNewTaskCreateBtTask="RM_NEWTASK_CRATEBTTASK",e.channelRMNewTaskCancleMagnet="RM_NEWTASK_CANCLE_MAGNET",e.channelRMImportTorrent="RM_NEWTASK_IMPORT_TORRENT",e.channelRMGetConfigValueResolve="RM_GET_CONFIG_VALUE_RESOLVE",e.channelRMGetConfigValueReject="RM_GET_CONFIG_VALUE_REJECT",e.channelRMSetConfigValueReject="RM_SET_CONFIG_VALUE_REJECT",e.channelMRTrayMenuClick="MR_TRAY_MENU_CLICK",e.channelMRNewTaskMagnetTaskCreated="MR_NEW_TASK_MAGNET_TASK_CREATED",e.channelMRNewTaskDownloadTorrentResult="MR_NEW_TASK_DOWNLOAD_TORRENT_RESULT",e.channelMRNewTaskCreateNewTaskResult="MR_NEWTASK_CREATENEWTASK_RESULT",e.channelMRNewTaskCreateBtTaskResult="RM_NEWTASK_CRATEBTTASK_RESULT",e.channelMRGetConfigValue="MR_GET_CONFIG_VALUE",e.channelMRSetConfigValue="MR_SET_CONFIG_VALUE",e.channelRMCommitPlanTask="RM_PLANTASK_COMMIT",e.channelRMPerformePlanTask="RM_PLANTASK_PERFORME",e.channelRMProcessSend="RM_RENDER_PROCESS_INFO",e.channelRMGetPrivateSpaceInfo="RM_RENDER_GET_PRIVATE_SPACE_INFO",e.channelMRGetPrivateSpaceInfoResult="MR_RENDER_GET_PRIVATE_SPACE_INFO_RESULT",e.channelRMFileCopy="RM_FILE_COPY",e.channelRMFileMove="RM_FILE_MOVE",e.channelMRFileCopyResult="MR_FILE_COPY_RESULT",e.channelMRFileMoveResult="MR_FILE_MOVE_RESULT",e.channelRMGetSutitleByCid="RM_RENDER_GET_SUBTITLE_BY_CID",e.channelMRGetSutitleByCidResult="MR_RENDER_GET_SUBTITLE_BY_CID_RESULT",e.channelRMGetSutitleByName="RM_RENDER_GET_SUBTITLE_BY_NAME",e.channelMRGetSutitleByNameResult="MR_RENDER_GET_SUBTITLE_BY_NAME_RESULT",e.channelRMDownloadSutitle="RM_RENDER_DOWNLOAD_SUBTITLE",e.channelMRDownloadSutitleSuc="MR_RENDER_DOWNLOAD_SUBTITLE_SUCCESS",e.channelMRDownloadSutitleFail="MR_RENDER_DOWNLOAD_SUBTITLE_FAIL",e.channelRMGetDisplayName="RM_RENDER_GET_SUBTITLE_DISPLAYNAME",e.channelMRGetDisplayNameResult="MR_RENDER_GET_SUBTITLE_DISPLAYNAME_RESULT",e.channelMRBringWindowToTop="MR_RENDER_BRING_WINDOW_TO_TOP",e.channelRMDownloadXmp="RM_RENDER_DOWNLOAD_XMP",e.channelRMXmpFixBoxCreated="RM_RENDER_XMPFIXBOX_CREATED",e.channelMRFixXmpSuc="MR_RENDER_FIX_XMP_SUC",e.channelMRFixXMPFail="MR_RENDER_FIX_XMP_FAIL",e.channelRMDownloadXmpEx="RM_RENDER_DOWNLOAD_XMP_EX",e.channelRMSetPosition="RM_SET_POSITION",e.channelRMSetFoucs="RM_SET_FOCUS",e.channelRMCreateAddressDropWnd="RM_CREATE_ADDRESS_DROPWND",e.channelRMRefreshAddressDropWnd="RM_REFRESH_ADDRESS_DROPWND",e.channelRMSelectAddressDropItem="RM_SELECT_ADDRESS_DROPITEM",e.channelRMCreateSearchWindow="RM_CREATE_SEARCH_WINDOW",e.channelRMShowSearchWindow="RM_SHOW_SEARCH_WINDOW",e.channelRMAddressKeyDown="RM_ADDRESS_BAR_KEYDOWN",e.channelMRFWAddressKeyDown="MR_ADDRESS_FW_BAR_KEYDOWN",e.channelMRFWSelectAddressDropItem="MR_FW_SELECT_ADDRESS_DROPITEM",e.channelRMAddressDropWndKeyDown="RM_ADDRESS_DROPWND_KEYDOWN",e.channelRMClickMouse="RM_CLICK_MOUSE",e.channelMRSearchBarFocusChange="MR_SEARCHBAR_FOCUS_CHANGE",e.channelMRFWAddressDropWndKeyDown="MR_FW_ADDRESS_DROPWND_KEYDOWN",e.channelMRClickAddressDropItem="MR_CLICK_ADDRESS_DROPITEM",e.channelMRMainWndMax="MR_MAINWINDOW_MAX",e.channelMRMainWndMin="MR_MAINWINDOW_MIN",e.channelMRMainWndRestore="MR_MAINWINDOW_RESTORE",e.channelRMGetBrowserStartType="RM_GET_BROWSER_START_TYPE",e.channelMRGetBrowserStartTypeResult="MR_GET_BROWSER_START_TYPE_RESULT",e.channelRMExecute="RM_SHELL_EXECUTE",e.channelMRExecuteResult="MR_SHELL_EXECUTE_RESULT",e.channelMRAdTipsClick="MR_AD_TIPS_CLICK",e.channelMRNotificationMsg="MR_NOTIFICATION_MSG",e.channelRMSetProgressBar="RM_SET_PROGRESS_BAR",e.channelRMRoundWindow="RM_ROUND_WINDOW",e.channelMRShowOrHideWindow="MR_SHOW_OR_HIDE_WINDOW",e.channelMRSuspensionWindowShowOrHide="MR_SUSPENSION_WINDOW_SHOW_OR_HIDE",e.channelRMConfigInitFinished="RM_CONFIG_INIT_FINISHED",e.channelRMConfigValueChanged="RM_CONFIG_VALUE_CHANGED",e.channelRMIndividuationBrowserMsg="RM_INDIVIDUATION_BROWSER_MSG",e.channelMRIndividuationBrowserMsg="MR_INDIVIDUATION_BROWSER_MSG",e.channelRMSetEnvironmentVariable="RM_SET_ENVIRONMENT_VARIABLE",e.channelMREmbedPlayerPos="MR_EMBED_PLAYER_POSITION",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},21:function(e,t){e.exports=require("fs")},22:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=function(...e){},t.error=function(...e){},t.warning=function(...e){},t.critical=function(...e){},t.verbose=function(...e){},"development"===process.env.LOGGER_ENV&&(t.information=function(...e){console.log("information",e)},t.error=function(...e){console.log("error",e)},t.warning=function(...e){console.log("warning",e)},t.critical=function(...e){console.log("critical",e)},t.verbose=function(...e){console.log("verbose",e)})},227:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(31),o=n(84),r=n(85),s=n(745),a=n(746);i.default.use(o.default);const l=new o.default.Store({modules:{Suspension:s.default,CloudDownload:a.default},strict:!1});t.connector=new r.default(l)},228:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.Success=0]="Success",e[e.FunctionUnExist=1]="FunctionUnExist",e[e.ParamError=2]="ParamError",e[e.CallFailed=3]="CallFailed",e[e.NotAllowed=4]="NotAllowed"}(t.NativeFunctionErrorCode||(t.NativeFunctionErrorCode={}))},229:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(4);class r{constructor(){this.mPeerId=void 0,this.mUserId="0",this.mSessionId=void 0,this.mIsLogin=!1,this.mIsVip=!1,this.mVipLevel=0,this.mVasType=0,this.mExpireDate="",this.mThunderVersionStr=void 0,this.mThunderVersionNumber=void 0}get expireDate(){return this.mExpireDate}get thunderVersionNumber(){return this.mThunderVersionNumber}get thunderVersionString(){return this.mThunderVersionStr}get vasType(){return this.mVasType}get vipLevel(){return this.mVipLevel}get userId(){return this.mUserId}get isLogin(){return this.mIsLogin}get isVip(){return this.mIsVip}get isPlatinumVip(){return this.isVip&&this.mVasType>2}get isSuperVip(){return this.isVip&&5===this.mVasType}getSessionId(){return i(this,void 0,void 0,function*(){return new Promise(e=>{this.mSessionId?e(this.mSessionId):o.client.callServerFunction("GetSessionID").then(t=>{this.mSessionId=t,e(this.mSessionId||"0")}).catch()})})}getPeerId(){return i(this,void 0,void 0,function*(){return new Promise(e=>{this.mPeerId?e(this.mPeerId):o.client.callServerFunction("GetPeerID").then(t=>{this.mPeerId=t,e(this.mPeerId||"")}).catch()})})}getUserinfo(){return i(this,void 0,void 0,function*(){return yield o.client.callServerFunction("GetAllUserInfo")})}parseUserInfo(){return i(this,void 0,void 0,function*(){let e=yield this.getUserinfo();if(e)if(this.mIsLogin=!0,this.mUserId=e.userID||"0",e.vipList&&e.vipList[0]){if(e.vipList[0].isVip){let t=Number(e.vipList[0].isVip).valueOf();this.mIsVip=t>0}else this.mIsVip=!1;e.vipList[0].vipLevel&&(this.mVipLevel=Number(e.vipList[0].vipLevel).valueOf()),e.vipList[0].vasType&&(this.mVasType=Number(e.vipList[0].vasType).valueOf()),e.vipList[0].expireDate&&(this.mExpireDate=e.vipList[0].expireDate)}else this.mIsVip=!1})}loadThunderVersion(){o.client.callServerFunction("GetThunderVersion").then(e=>{this.mThunderVersionStr=e,this.mThunderVersionNumber=0;let t=this.mThunderVersionStr.split(".");if(t&&4===t.length){let e=Number(t[0]).valueOf(),n=Number(t[1]).valueOf(),i=Number(t[2]).valueOf(),o=128;this.mThunderVersionNumber=o*Math.pow(2,24)+e*Math.pow(2,16)+n*Math.pow(2,8)+i}}).catch()}onLogin(){return i(this,void 0,void 0,function*(){this.clear(),yield this.parseUserInfo()})}onLogout(){this.clear()}clear(){this.mUserId="0",this.mSessionId=void 0,this.mIsVip=!1,this.mIsLogin=!1,this.mVasType=0,this.mVipLevel=0}}t.UserHelper=r,function(e){const t=new r;e.getUserHelper=function(){return t}}(t.UserHelperNS||(t.UserHelperNS={}))},23:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.msgIPCCommunicatorForward="ipc_communicator_forward",e.msgIPCSendToMain="ipc_send_to_main",e.msgIPCSendToRenderer="ipc_send_to_renderer",e.msgIPCRendererConnect="ipc_renderer_connect",e.msgIPCRendererDisconnect="ipc_renderer_disconnect",e.msgNCCallNativeFunction="nc_call_native_function",e.msgNCCheckNativeFunction="nc_check_native_function",e.msgNCCallJsFunctionById="nc_call_js_function_by_id",e.msgNCCallJsFunctionByName="nc_call_js_function_by_name",e.msgNCNativeFireEvent="nc_native_fire_event",e.msgNCNativeCallReady="nc_native_call_ready"}(t.CommonIPCMessage||(t.CommonIPCMessage={}))},230:function(e,t,n){"use strict";var i=n(1080);n.n(i).a},237:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t;!function(e){e[e.Default=0]="Default",e[e.Vip=1]="Vip"}(t=e.SkinType||(e.SkinType={})),e.defaultSkinInfo={type:t.Default,id:1,name:"默认皮肤"},e.vipSkinInfo={type:t.Vip,id:2,name:"会员皮肤"},e.cacheDir=`${__profilesDir}/suspension-skin`}(t.SuspensionSkinHelperNS||(t.SuspensionSkinHelperNS={}))},25:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(2),r=n(33),s=n(14),a=n(15),l=n(8).default(o.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){let t,n,o,d;function c(e){let t=e;return/^[a-zA-Z]:\\/.test(e)?t=e.slice(0,3):e&&"\\"!==e[e.length-1]&&(t=e+"\\"),t}!function(e){e[e.DRIVE_UNKNOWN=0]="DRIVE_UNKNOWN",e[e.DRIVE_NO_ROOT_DIR=1]="DRIVE_NO_ROOT_DIR",e[e.DRIVE_REMOVABLE=2]="DRIVE_REMOVABLE",e[e.DRIVE_FIXED=3]="DRIVE_FIXED",e[e.DRIVE_REMOTE=4]="DRIVE_REMOTE",e[e.DRIVE_CDROM=5]="DRIVE_CDROM",e[e.DRIVE_RAMDISK=6]="DRIVE_RAMDISK"}(t=e.DriverType||(e.DriverType={})),function(e){e[e.Unspecified=0]="Unspecified",e[e.HDD=3]="HDD",e[e.SSD=4]="SSD",e[e.SCM=5]="SCM"}(n=e.MediaType||(e.MediaType={})),function(e){e.HKEY_CLASSES_ROOT="HKEY_CLASSES_ROOT",e.HKEY_CURRENT_USER="HKEY_CURRENT_USER",e.HKEY_LOCAL_MACHINE="HKEY_LOCAL_MACHINE",e.HKEY_USERS="HKEY_USERS"}(o=e.RegistryHKey||(e.RegistryHKey={})),function(e){e[e.REG_NONE=0]="REG_NONE",e[e.REG_SZ=1]="REG_SZ",e[e.REG_EXPAND_SZ=2]="REG_EXPAND_SZ",e[e.REG_BINARY=3]="REG_BINARY",e[e.REG_DWORD=4]="REG_DWORD",e[e.REG_DWORD_LITTLE_ENDIAN=4]="REG_DWORD_LITTLE_ENDIAN",e[e.REG_DWORD_BIG_ENDIAN=5]="REG_DWORD_BIG_ENDIAN",e[e.REG_LINK=6]="REG_LINK",e[e.REG_MULTI_SZ=7]="REG_MULTI_SZ",e[e.REG_RESOURCE_LIST=8]="REG_RESOURCE_LIST",e[e.REG_FULL_RESOURCE_DESCRIPTOR=9]="REG_FULL_RESOURCE_DESCRIPTOR",e[e.REG_RESOURCE_REQUIREMENTS_LIST=10]="REG_RESOURCE_REQUIREMENTS_LIST",e[e.REG_QWORD=11]="REG_QWORD",e[e.REG_QWORD_LITTLE_ENDIAN=11]="REG_QWORD_LITTLE_ENDIAN"}(d=e.RegistryDataType||(e.RegistryDataType={})),e.getDriveType=function(e){return e=c(e),l.getDriveType(e)},e.getDriveInfo=function(e){return e=c(e),l.getDriveInfo(e)},e.getFreePartitionSpace=function(e){return e=c(e),l.getFreePartitionSpace(e)},e.getLogicalDriveStrings=function(){return l.getLogicalDriveStrings()},e.getPartitionSpace=function(e){return e=c(e),l.getPartitionSpace(e)},e.getSystemTempPath=function(){return l.getSystemTempPath()},e.getTaskTypeFromUrl=function(e){let t=l.getTaskTypeFromUrl(e);if(t===a.DownloadKernel.TaskType.Unkown&&function(e){e=e.toLowerCase();let t=!1;do{if("http://"===e.substr(0,"http://".length)){t=!0;break}if("https://"===e.substr(0,"https://".length)){t=!0;break}if("ftp://"===e.substr(0,"ftp://".length)){t=!0;break}}while(0);return t}(e)){let n=/:\/\/\[(.+?)\].*/.exec(e);n||(n=/.+?:\/\/.*?\[(.+?)\].*/.exec(e)),n&&n[1]&&r.isIPv6(n[1])&&(t=a.DownloadKernel.TaskType.P2sp)}return t},e.extractIcon=function(e,t){return l.extractIcon(e,t)},e.isWindow7=function(){return 1===l.isWin7()},e.isWindow8OrLater=function(){let e=!1;do{let t=s.release();if(!t)break;let n=t.indexOf(".",0);if(n<0)break;let i=t.substring(0,n);if(!i)break;let o=parseInt(i,10);o&&o>=8&&(e=!0)}while(0);return e},e.isWindows10=function(){let e=!1;do{let t=s.release();if(!t)break;if(0===t.indexOf("10.0.")){e=!0;break}}while(0);return e},e.compareStr=function(e,t){return l.compareStr(e,t)},e.getTickCount=function(){return l.getTickCount()},e.setScreenSaveActive=function(e,t){return l.setScreenSaveActive(e,t)},e.isSparseDriver=function(e){return e=c(e),l.isSparseDriver(e)},e.getAppList=function(){return i(this,void 0,void 0,function*(){return new Promise(e=>{l.getAppList(t=>{e(t)})})})},e.isSSD=function(){return i(this,void 0,void 0,function*(){return new Promise(e=>{l.isSSD((t,n)=>{e(n)})})})},e.getMemoryInfo=function(){return i(this,void 0,void 0,function*(){return new Promise(e=>{l.getMemoryInfo((t,n)=>{e({totalPhy:t,totalVir:n})})})})},e.getHardDiskSpaceList=function(){return i(this,void 0,void 0,function*(){return new Promise(e=>{l.getHardDiskSpaceList(t=>{e(t)})})})},e.getCpuList=function(){return i(this,void 0,void 0,function*(){return new Promise(e=>{l.getCpuList(t=>{e(t)})})})},e.getFixedDriveMediaType=function(e){return i(this,void 0,void 0,function*(){return new Promise(t=>{e.length>1&&(e=e.slice(0,1)),l.getDriveMediaType(e.toUpperCase(),(e,n)=>{t(n)})})})},e.sleep=function(e){return i(this,void 0,void 0,function*(){yield new Promise((t,n)=>{setTimeout(t,e)})})},e.getTextScale=function(){let e=100,t=l.readRegString(o.HKEY_CURRENT_USER,"SOFTWARE\\Microsoft\\Accessibility","TextScaleFactor");return t&&(e=Number(t)),isNaN(e)&&(e=100),e},e.getWindowRect=function(e){return e?l.getWindowRect(e):{x:0,y:0,width:0,height:0}},e.moveWindow=function(e,t){e&&l.moveWindow(e,t.x,t.y,t.width,t.height,!0)},e.getSystemDirectory=function(){return l.getSystemDirectory()},e.getVersionBlockString=function(e,t){return l.getVersionBlockString(e,t)},e.getOwnerName=function(e){return l.getOwnerName(e)},e.createRegKey=function(e,t){return l.createRegKey(e,t)},e.deleteRegKey=function(e,t){return l.deleteRegKey(e,t)},e.readRegString=function(e,t,n){return l.readRegString(e,t,n)},e.queryRegValue=function(e,t,n){return l.queryRegValue(e,t,n)},e.writeRegValue=function(e,t,n,i,o){return l.writeRegValue(e,t,n,i,o)},e.deleteRegValue=function(e,t,n){return l.deleteRegValue(e,t,n)}}(t.ThunderHelper||(t.ThunderHelper={}))},28:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(6),r=n(22),s=n(23);!function(e){e.mainProcessContext="main-process",e.mainRendererContext="main-renderer",e.mainPageWebviewRendererContext="main-page-webview-renderer",e.newTaskRendererContext="new-task-renderer",e.preNewTaskRendererContext="pre-new-task-renderer",e.loginRendererContext="login-renderer";class t{constructor(){this.isConnected=!1,this.isOnIPCEvent=!1,this.rendererInfos=[],this.listeners=new Map,t.intervalIPCModuleMsgs=[s.CommonIPCMessage.msgIPCRendererConnect,s.CommonIPCMessage.msgIPCRendererDisconnect]}onMessage(e,t){do{if(!o.isString(e)||0===e.length){r.error("msgName is null");break}if(o.isNullOrUndefined(t)){r.error("listener is null");break}this.listeners.has(e)?this.listeners.get(e).push(t):this.listeners.set(e,[t])}while(0)}getCommunicatorInfo(){return this.currInfo}getAllRenderer(){return this.rendererInfos}getCommunicatorInfoById(e){for(let t of this.rendererInfos)if(t.id===e)return t;return null}getCommunicatorInfoByContext(e){for(let t of this.rendererInfos)if(t.context===e)return t;return null}startListenIPCMessage(e){this.isOnIPCEvent||(this.isOnIPCEvent=!0,e&&this.ListenSendToMainMsg(),this.ListenSendToRendererMsg(e))}ListenSendToMainMsg(){i.ipcMain.on(s.CommonIPCMessage.msgIPCSendToMain,(e,t)=>{let n=void 0;do{if(o.isNullOrUndefined(t)){r.error("msgInfo is empty");break}if(!this.isConnected){r.warning("hasnot been connected yet");break}let i=t.msg.name;if(this.isIPCModuleIntervalMsg(i)){r.information(`IPC module interval msg : ${i}`);let o=this.handleIPCModuleIntervalMsg(e.sender,t);if(n=o[1],!o[0])break;r.information("need to dispatch msg:"+i)}o.isNullOrUndefined(n)?n=this.NotifyListener(t):this.NotifyListener(t)}while(0);o.isNullOrUndefined(n)||(e.returnValue=n),t=null})}ListenSendToRendererMsg(e){(e?i.ipcMain:i.ipcRenderer).on(s.CommonIPCMessage.msgIPCSendToRenderer,(t,n)=>{let i=void 0;do{if(o.isNullOrUndefined(n)){r.error("msgInfo is empty");break}if(!this.isConnected){r.warning("hasnot been connected yet");break}let s=n.msg.name;if(this.isIPCModuleIntervalMsg(s)){r.information(`IPC module interval msg : ${s}`);let e=this.handleIPCModuleIntervalMsg(t.sender,n);if(i=e[1],!e[0])break;r.information("need to dispatch msg:"+s)}e?(r.information("is main, handle forward msg"),this.handleForwardRendererToRendererMsg(n)):(r.information("is renderer, handle business msg"),o.isNullOrUndefined(i)?i=this.NotifyListener(n):this.NotifyListener(n))}while(0);o.isNullOrUndefined(i)||(t.returnValue=i),n=null})}isIPCModuleIntervalMsg(e){for(let n of t.intervalIPCModuleMsgs)if(e===n)return!0;return!1}handleIPCModuleIntervalMsg(e,t){let n=[!1,void 0];do{let i=t.msg.name;if(i===s.CommonIPCMessage.msgIPCRendererConnect){n=[!0,this.handleRendererConnectMsg(e,t)];break}if(i===s.CommonIPCMessage.msgIPCRendererDisconnect){n=[!0,this.handleRendererDisconnectMsg(e,t)];break}}while(0);return n}handleRendererConnectMsg(e,t){r.verbose(e),r.verbose(t)}handleRendererDisconnectMsg(e,t){r.verbose(e),r.verbose(t)}handleForwardRendererToRendererMsg(e){this.sendForwardRendererToRendererMsg(e)}sendForwardRendererToRendererMsg(e){r.verbose(e)}NotifyListener(e){let t=void 0,n=e.msg.name;if(this.listeners.has(n)){let i=this.listeners.get(n),o=!0;for(let n of i)o?(o=!1,t=n(e)):n(e)}return t}}e.Communicator=t}(t.CommonIPCBase||(t.CommonIPCBase={}))},29:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(14),o=n(2);t.getDefaultPrex=function(){return o.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=i.tmpdir();let n=e;e||(n=o.basename(process.execPath,".exe"));let r=o.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(r="\\\\.\\pipe\\"+(r=(r=r.replace(/^\//,"")).replace(/\//g,"-"))),r},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},293:function(e,t,n){"use strict";n.r(t);var i=n(294),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},294:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(741),o=n(227);t.default=o.connector.connect({mapStateToProps:{downloadStatus:e=>e.Suspension.downloadStatus,totalDownloadSpeed:e=>e.Suspension.totalDownloadSpeed,totalVipSpeed:e=>e.Suspension.totalVipSpeed,showStatus:e=>e.Suspension.showStatus,statusText:e=>e.Suspension.statusText,taskIdLists:e=>e.Suspension.taskIdLists,taskBaseInfos:e=>e.Suspension.taskBaseInfos,cloudTaskIds:e=>e.CloudDownload.cloudTaskIds,cloudTasksMap:e=>e.CloudDownload.cloudTasksMap},mapCommitToProps:{INIT_TASKLIST:"INIT_TASKLIST",INSERT_TASK:"INSERT_TASK",REMOVE_TASK:"REMOVE_TASK",UPDATE_TASKSTATUS:"UPDATE_TASKSTATUS",UPDATE_TASKDETAIL:"UPDATE_TASKDETAIL",UPDATE_TASKBASE:"UPDATE_TASKBASE",UPDATE_SUSPENSION:"UPDATE_SUSPENSION"},mapDispatchToProps:{},mapGettersToProps:{}})(i.default)},295:function(e,t,n){"use strict";n.r(t);var i=n(296),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},296:function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var o,r=arguments.length,s=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(r<3?o(s):r>3?o(t,n,s):o(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},o=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const r=n(6),s=n(3),a=n(7),l=n(28),d=n(5),c=n(742),u=n(43),h=n(4),f=n(176),p=n(750),m=n(2),g=n(8),v=n(15),w=n(751),S=n(20),_=n(679),T=n(1),b=n(556),y=n(752),k=n(237);n(64).FixTextScale.fixZoomFactory();const C=T.default.getLogger("Thunder.SuspensionRender"),R=g.default(m.join(__rootDir,"../bin/ThunderHelper.node")),E=g.default(m.join(__rootDir,"../bin/ThunderSuspensionWindow.node")),I=_.HoverTextManagerNS.getHoverTextManager(),P=y.BubbleManagerNS.getBubbleManager();!function(e){e.Comments="comments",e.FileList="files",e.Attribute="detail"}(t.DetailIndex||(t.DetailIndex={}));let x=class extends d.Vue{constructor(){super(...arguments),this.isLogined=!1,this.isVip=!1,this.isInVipAcc=!1,this.isStopAnimation=!1,this.isDowning=!1,this.isClick=!1,this.isStatus=!1,this.totalSpeed={speed:"0",unit:"B/S"},this.vipSpeed="",this.hoverWindowWidth=404,this.hoverWindowHeight=270,this.isThunderForeground=!1,this.isDowningStatus=!1,this.downloadProgress=0,this.startShowTime=0,this.suspensionWindowPos={x:void 0,y:void 0},this.isFullScreen=!1,this.isStartShowWindow=!0,this.dpiFactor=1,this.showFloatPanel=!1,this.selectedTaskId=0,this.canTryTaskId=1,this.tryingTaskId=2,this.skinInfo=void 0,this.silent=!1}onDownloadingChanged(e){return o(this,void 0,void 0,function*(){1===p.FloatPanelMenuHelper.getShowFloatPanelType()&&(e?(yield this.canHideWindow())||E.showSuspensionWindow():E.hideSuspensionWindow())})}updateSuspensionState(){return o(this,void 0,void 0,function*(){if(this.isDowning=this.downloadStatus,r.isNullOrUndefined(this.statusText)?this.isStatus=!1:(this.statusText=this.statusText,this.isStatus=this.showStatus),this.isStatus&&(this.isDowning=!1),this.isStatus){if(!r.isNullOrUndefined(this.statusText)&&!(yield this.canHideWindow())){E.showConnectingTextWindow(this.statusText),(yield(yield f.FloatPanelHelper.getMainWindow()).isMaximized())&&E.hideSuspensionWindow()}}else this.isDowning||E.updateDownloadState(!1);this.isDowningStatus=this.isDowning,I.isDonwloading=this.isDowningStatus,r.isNullOrUndefined(this.statusText)||(I.isDonwloading=!0)})}showOrHideFloatPanel(e){return o(this,void 0,void 0,function*(){C.information("showOrHideFloatPanel this.showFloatPanel:",this.showFloatPanel,", bShow:",e),this.showFloatPanel!==e&&(this.showFloatPanel=e,e?(f.FloatPanelHelper.trackEvent("float_monitor_hover_show"),(yield f.FloatPanelHelper.getFloatPanelWindow()).showInactive()):(yield f.FloatPanelHelper.getFloatPanelWindow()).hide(),P.isFloatPanelShowed=e)})}setFloatPanelDirection(e,t){let n={x:0,y:0};switch(e){case f.FloatPanelDirection.LeftBottom:n.x-=t.width,n.y-=0;break;case f.FloatPanelDirection.LeftTop:n.x-=t.width,n.y-=t.height;break;case f.FloatPanelDirection.RightTop:n.x+=0,n.y-=t.height;break;case f.FloatPanelDirection.RightBottom:n.x-=0,n.y-=0}return n}leave(){this.isClick||this.showOrHideFloatPanel(!1).catch()}getDisplayNearestPoint(e){return o(this,void 0,void 0,function*(){do{let t=yield a.asyncRemoteCall.getCurrentWindow();if(!t){C.warning("getCurrentWindow failed");break}let n=yield t.getBounds();if(C.information("getDisplayNearestPoint browserBounds:",n),!n){C.warning("getCurrentWindow failed");break}let i=yield(yield a.asyncRemoteCall.getScreen()).getAllDisplays();for(let t=0;t<i.length;++t){let n=i[t],o=n.bounds;if(C.information("1111getDisplayNearestPoint displayBounds:",o,", point:",e),e.x>=o.x&&e.x<o.x+o.width*n.scaleFactor&&e.y>=o.y&&e.y<o.y+o.height*n.scaleFactor)return C.information("2222getDisplayNearestPoint displayBounds:",o,", point:",e),n}}while(0);return yield(yield a.asyncRemoteCall.getScreen()).getPrimaryDisplay()})}getFloatPanelDirection(e,t,n,i){return o(this,void 0,void 0,function*(){C.information("getFloatPanelDirection x: ",e,"y:",t);let o=E.getHandle(),r=R.getMonitorDPIFactor(o),s=yield this.getDisplayNearestPoint({x:Math.round(e),y:Math.round(t)}),a=s.workArea,l=a.width*s.scaleFactor-40,d=a.height*s.scaleFactor-60,c=f.FloatPanelDirection.RightBottom;return c=e+n<a.x+l?t+i<a.y+d?f.FloatPanelDirection.RightBottom:f.FloatPanelDirection.RightTop:t+i<a.y+d?f.FloatPanelDirection.LeftBottom:f.FloatPanelDirection.LeftTop,C.information("getFloatPanelDirection x:",e,", y:",t,", width:",n,", height:",i,", dpiFactor:",r,", display.scaleFactor:",s.scaleFactor,", screenWidth:",l,", screenHeight:",d,", rect:",a,", ret:",c),c})}canHideWindow(){return o(this,void 0,void 0,function*(){let e=!0;do{if(this.silent&&(this.silent=(yield h.client.callRemoteClientFunction(l.CommonIPCBase.mainProcessContext,"IsSilentSupsWnd"))[0],this.silent)){e=!0;break}let t=p.FloatPanelMenuHelper.getShowFloatPanelType();if(0===t){e=!1;break}if(2===t){e=!0;break}let n=yield h.client.callServerFunction("GetMainWindowStates");if(n.maximized&&f.FloatPanelHelper.isThunderMainWndForeground()&&n.visible){e=!0;break}(this.isDowning||this.isStatus)&&(e=!1)}while(0);return e})}setSuspensionConfig(e){h.client.callServerFunction("SetConfigValue","ConfigSuspension","SuspensionX",e.x).catch(),h.client.callServerFunction("SetConfigValue","ConfigSuspension","SuspensionY",e.y).catch()}handleRightClick(){this.showOrHideFloatPanel(!1).catch(),p.FloatPanelMenuHelper.popupMenu()}showOrHideMainWindow(e){return o(this,void 0,void 0,function*(){let t="",n=yield f.FloatPanelHelper.getMainWindow(),i=yield h.client.callServerFunction("GetMainWindowStates");i.minimized?(yield h.client.callServerFunction("BringMainWndToTop"),this.isThunderForeground=!0,t="showmain"):i.visible&&!e&&this.isThunderForeground?i.minimized?yield n.restore():(yield n.minimize(),yield n.hide(),this.isThunderForeground=!1,t="hidemain"):(yield h.client.callServerFunction("BringMainWndToTop"),this.isThunderForeground=!0,t="showmain"),f.FloatPanelHelper.trackEvent("float_monitor_dbclick_main_status_get",`status=${t}`)})}showMainWindow(){return o(this,void 0,void 0,function*(){let e=yield f.FloatPanelHelper.getMainWindow(),t=yield h.client.callServerFunction("GetMainWindowStates");t.minimized?yield h.client.callServerFunction("BringMainWndToTop"):t.visible?this.isThunderForeground?t.minimized&&(yield e.restore()):yield e.show():yield h.client.callServerFunction("BringMainWndToTop")})}onGetUserInfo(){h.client.callServerFunction("GetAllUserInfo").then(e=>{do{if(C.information("onGetUserInfo allUserinfo",e),!e)break;I.isLogined=!0,this.isLogined=!0;let t=e.vipList[0],n=Number(t.isVip);I.vipType=Number(t.vasType),C.information("onGetUserInfo vipInfo",t),0!==n?(this.isVip=!0,I.isVip=!0,E.setIsVipUser(!0),E.updateSuspensionState(!1),E.updateTrialState(!1)):(this.isVip=!1,I.isVip=!1,E.setIsVipUser(!1))}while(0)}).catch()}onTotalVipAcclerateStatusChanged(e){e&&(this.isInVipAcc=e.acclerating)}listenLoginEvent(){u.NativeCallModule.nativeCall.AttachNativeEvent("onLoginSuc",()=>{I.isLogined=!0,this.isLogined=!0,I.isVip=!1,this.isVip=!1,this.onGetUserInfo()}),u.NativeCallModule.nativeCall.AttachNativeEvent("onLoginFailed",()=>{I.isLogined=!1,I.isVip=!1,I.vipType=0,this.isLogined=!1,this.isVip=!1,E.setIsVipUser(!1)}),u.NativeCallModule.nativeCall.AttachNativeEvent("onUserInfoChange",(e,t)=>{"vipinfo"===e&&this.onGetUserInfo()}),u.NativeCallModule.nativeCall.AttachNativeEvent("onLogout",()=>{this.isVip=!1,this.isLogined=!1,I.isLogined=!1,I.isVip=!1,I.vipType=0,E.setIsVipUser(!1)})}getBooskeyState(){return o(this,void 0,void 0,function*(){return yield h.client.callServerFunction("GetConfigValue","BossKey","BossKeyState",!1)})}initSuspensionPosition(){return o(this,void 0,void 0,function*(){let e=(yield(yield a.asyncRemoteCall.getScreen()).getPrimaryDisplay()).bounds,t=R.getDisplayWindowRect();C.warning("cursorPos:",t),C.information("screenBounds:",e,", this.suspensionWindowPos:",this.suspensionWindowPos.x,this.suspensionWindowPos.y),(this.suspensionWindowPos.x<e.x||this.suspensionWindowPos.x>e.x+t.width||this.suspensionWindowPos.y<e.y||this.suspensionWindowPos.y>e.y+t.height)&&(this.suspensionWindowPos.x=e.x+t.width-210*this.dpiFactor,this.suspensionWindowPos.y=e.y+90),C.warning("this.suspensionWindowPos.x:",this.suspensionWindowPos.x,", this.suspensionWindowPos.y:",this.suspensionWindowPos.y),this.silent=(yield h.client.callRemoteClientFunction(l.CommonIPCBase.mainProcessContext,"IsSilentSupsWnd"))[0];let n=yield this.canHideWindow();E.setSuspensionWindowPos(this.suspensionWindowPos.x,this.suspensionWindowPos.y,this.silent||n)})}initSuspensionPositionConfig(){h.client.callServerFunction("GetConfigValue","ConfigSuspension","SuspensionX",null).then(e=>{null!==e&&e>=0?(this.suspensionWindowPos.x=e,void 0!==this.suspensionWindowPos.y&&this.initSuspensionPosition().catch()):(this.suspensionWindowPos.x=-1,this.initSuspensionPosition().catch())}).catch(),h.client.callServerFunction("GetConfigValue","ConfigSuspension","SuspensionY",null).then(e=>{null!==e&&e>=0?(this.suspensionWindowPos.y=e,void 0!==this.suspensionWindowPos.x&&this.initSuspensionPosition().catch()):(this.suspensionWindowPos.y=-1,this.initSuspensionPosition().catch())}).catch()}getConfigValue(){h.client.callServerFunction("GetConfigValue","ConfigFloatPanel","ConfigFloatPanelShowSpeedType","0").then(e=>{null!==e&&p.FloatPanelMenuHelper.setShowSpeedType(Number(e))}).catch(),h.client.callServerFunction("GetConfigValue","ConfigFloatPanel","FloatPanelValue","0").then(e=>o(this,void 0,void 0,function*(){if(null!==e){this.isStartShowWindow=!1;let t=Number(e);p.FloatPanelMenuHelper.setShowFloatPanelType(t);let n=yield h.client.callServerFunction("GetMainWindowStates");n.maximized&&n.visible?E.hideSuspensionWindow():2===t?E.hideSuspensionWindow():0===t?(this.silent=(yield h.client.callRemoteClientFunction(l.CommonIPCBase.mainProcessContext,"IsSilentSupsWnd"))[0],this.silent||E.showSuspensionWindow()):(yield this.canHideWindow())?E.hideSuspensionWindow():(this.silent=(yield h.client.callRemoteClientFunction(l.CommonIPCBase.mainProcessContext,"IsSilentSupsWnd"))[0],this.silent||E.showSuspensionWindow())}})).catch()}getAnimationLevel(){return o(this,void 0,void 0,function*(){let e=yield h.client.callServerFunction("GetConfigValue","GenericSettings","AnimationLevel","0");this.isStopAnimation="0"===e})}listenFullScreen(){setInterval(()=>o(this,void 0,void 0,function*(){R.IsInFullScreen()?(E.hideSuspensionWindow(),this.isFullScreen=!0):this.isFullScreen&&((yield this.canHideWindow())||(E.showSuspensionWindow(),this.isFullScreen=!1))}),1e3)}listenConfigEvent(){h.client.callServerFunction("IsConfigInitFinish").then(()=>{this.getConfigValue(),this.getAnimationLevel().catch()}).catch(),h.client.attachServerEvent("OnConfigValueChanged",(e,t,n,i,o)=>{"ConfigFloatPanel"===t?i!==o&&this.getConfigValue():"GenericSettings"===t&&"AnimationLevel"===n&&i!==o&&this.getAnimationLevel().catch()})}getCommonExtData(){let e="";return e=(e=(e=e+",is_login="+(I.isLogined?"1":"0"))+",is_vip="+(I.isVip?"1":"0"))+",vip_type="+I.vipType}listenMainEvent(){return o(this,void 0,void 0,function*(){let e=yield f.FloatPanelHelper.getMainWindow();e.on("maximize",()=>{E.hideSuspensionWindow()}),e.on("blur",()=>o(this,void 0,void 0,function*(){(yield this.canHideWindow())?E.hideSuspensionWindow():f.FloatPanelHelper.isThunderMainWndForeground()||E.showSuspensionWindow()})),e.on("focus",()=>o(this,void 0,void 0,function*(){(yield e.isMaximized())?E.hideSuspensionWindow():(yield this.canHideWindow())?E.hideSuspensionWindow():this.isStartShowWindow||E.showSuspensionWindow()})),e.on("minimize",()=>o(this,void 0,void 0,function*(){(yield this.getBooskeyState())?E.hideSuspensionWindow():(yield e.isVisible())&&((yield this.canHideWindow())?E.hideSuspensionWindow():E.showSuspensionWindow())})),e.on("unmaximize",()=>o(this,void 0,void 0,function*(){(yield this.canHideWindow())?E.hideSuspensionWindow():E.showSuspensionWindow()})),e.on("hide",()=>o(this,void 0,void 0,function*(){(yield this.getBooskeyState())?E.hideSuspensionWindow():(yield this.canHideWindow())?E.hideSuspensionWindow():(this.silent=(yield h.client.callRemoteClientFunction(l.CommonIPCBase.mainProcessContext,"IsSilentSupsWnd"))[0],this.silent||E.showSuspensionWindow())}))})}getCloudDownloadingDetail(){let e=0,t=0,n=[],i=f.FloatPanelHelper.cloudNoneReactiveTasksMap;for(let o in i){let r=i[o],s=r.taskStatus;s!==v.DownloadKernel.TaskStatus.PreDownloading&&s!==v.DownloadKernel.TaskStatus.StartWaiting&&s!==v.DownloadKernel.TaskStatus.StartPending&&s!==v.DownloadKernel.TaskStatus.Started||(t+=r.fileSize,e+=r.downloadSize,n.push(Number(o)))}return{downSize:e,totalSize:t,downloading:n}}listenGlobalSpeedChangeEvent(){h.client.attachServerEvent("OnTotalVipAcclerateStatusChanged",(e,t)=>{e&&"vip-download-webview"===e.name&&this.onTotalVipAcclerateStatusChanged(t)}),u.NativeCallModule.nativeCall.AttachNativeEvent("OnGlobalDownloadSpeedChanged",(e,t)=>o(this,void 0,void 0,function*(){this.totalSpeed=f.FloatPanelHelper.formatSpeed(e),this.isVip||this.isInVipAcc||(t=0);let n=f.FloatPanelHelper.formatSpeed(t);this.vipSpeed="+"+n.speed+n.unit;let i=yield h.client.callServerFunction("GetDownloadDetails");if(i){let n=this.getCloudDownloadingDetail(),o=i.totalSize+n.totalSize,r=i.downSize+n.downSize,s=0;o>0&&(s=r/o),s=(s=s>1?1:s)<0?0:s,this.downloadProgress=s,this.isDowningStatus&&E.showDownloadWindow(e,t,this.downloadProgress)}}))}refreshSuspension(){let e=!1,t=0,n=0,i=!1,o=void 0,r=[],s=void 0;for(let i=0;i<this.taskIdLists.length;i++)s=this.taskIdLists[i],this.taskBaseInfos[s]&&f.FloatPanelHelper.isDownloadStatus(this.taskBaseInfos[s].taskStatus)&&(e=!0,t+=this.taskBaseInfos[s].downloadSpeed,n+=this.taskBaseInfos[s].vipSpeed,r.push(s));for(let i=0;i<this.cloudTaskIds.length;i++){let o=this.cloudTaskIds[i];f.FloatPanelHelper.isDownloadStatus(this.cloudTasksMap[o].taskStatus)&&(e=!0,t+=this.cloudTasksMap[o].downloadSpeed,n+=this.cloudTasksMap[o].vipSpeed,r.push(o))}if(i=1===r.length){this.cloudTasksMap.hasOwnProperty(String(r[0]))?(o=f.FloatPanelHelper.getTaskStartPrompt(this.cloudTasksMap[r[0]]),this.cloudTasksMap[r[0]].taskStatus===v.DownloadKernel.TaskStatus.StartPending&&void 0===o&&(o="连接资源")):(o=f.FloatPanelHelper.getTaskStartPrompt(this.taskBaseInfos[r[0]]),this.taskBaseInfos[r[0]].taskStatus===v.DownloadKernel.TaskStatus.StartPending&&void 0===o&&(o="连接资源"))}this.UPDATE_SUSPENSION({downloadStatus:e,totalDownloadSpeed:t,totalVipSpeed:n,showStatus:i,statusText:o})}updateSuspensionBall(){this.refreshSuspension(),this.$nextTick(()=>{this.updateSuspensionState().catch()})}onGetTaskList(e){C.information("onGetTaskList, taskList:",e),this.INIT_TASKLIST(e),this.updateSuspensionBall()}onTaskRemoved(e){C.information("onTaskRemoved, taskIdList:",e),this.REMOVE_TASK(e),this.updateSuspensionBall()}onTaskInserted(e){C.information("onTaskInserted, taskBases:",e),this.INSERT_TASK(e),this.updateSuspensionBall()}onTaskStatusChanged(e){C.information("onTaskStatusChanged, statusMap:",e),this.UPDATE_TASKSTATUS(e),this.updateSuspensionBall()}onTaskDetailChanged(e){C.information("onTaskDetailChanged, taskBasesMap:",e),this.UPDATE_TASKDETAIL(e),this.updateSuspensionBall()}onBtSubFileLoaded(e,t){C.information("onBtSubFileLoaded, taskBase:",t),this.UPDATE_TASKBASE(t),this.updateSuspensionBall()}listenTrialEvent(){C.information("listenTrialEvent-----------------------------"),u.NativeCallModule.nativeCall.AttachNativeEvent("OnCurTaskChange",(e,t,n)=>o(this,void 0,void 0,function*(){this.selectedTaskId=e,C.information("OnCurTaskChange taskId:",e);let t=yield b.VipPluginFunctionNS.getVipTaskInfo(e);t?(C.information("OnCurTaskChange taskInfo.taskType,taskInfo.taskStatus:",t.taskType,t.taskStatus),1===t.taskType&&0===t.taskStatus?(E.updateTrialState(!0),this.canTryTaskId=e,C.information("OnCurTaskChange updateTrialState true, taskId:",e)):(C.information("OnCurTaskChange updateTrialState false, taskId:",e),E.updateTrialState(!1))):E.updateTrialState(!1)})),u.NativeCallModule.nativeCall.AttachNativeEvent("OnTaskRemoved",(e,t,n)=>{let i=[];try{i=JSON.parse(n),C.information("OnTaskRemoved taskIds,taskIdList = ",n,i);for(let e of i)C.information("OnTaskRemoved this.isVip,this.canTryTaskId,taskId = ",this.isVip,this.canTryTaskId,e),this.isVip||this.canTryTaskId!==e&&this.tryingTaskId!==e||(E.updateTrialState(!1),E.updateSuspensionState(!1),E.showNormalWindow(),this.canTryTaskId=1,this.tryingTaskId=2)}catch(e){C.warning(e)}}),h.client.attachServerEvent("OnVipTaskInfoChanged",(e,t,n)=>{e&&"vip-download-webview"===e.name&&n&&(1!==n.taskType&&2!==n.taskType||this.isVip||(-1===n.taskStatus||0===n.taskStatus||3===n.taskStatus||7===n.taskStatus||10===n.taskStatus?(E.updateSuspensionState(!1),E.showNormalWindow()):this.canTryTaskId===t&&(E.updateSuspensionState(!0),E.showVipWindow())),C.information("onVipTaskInfoChanged taskId,taskInfo:",t,n),1===n.taskType&&0===n.taskStatus?(this.canTryTaskId=t,this.canTryTaskId===this.selectedTaskId?(C.information("onVipTaskInfoChanged updateTrialState true, taskId:",t),E.updateTrialState(!0)):C.information("onVipTaskInfoChanged updateTrialState false, taskId:",t)):1===n.taskType&&2===n.taskStatus&&t===this.selectedTaskId&&E.updateTrialState(!1))})}onChangeSkin(e){do{if(this.skinInfo&&e&&this.skinInfo.type===e.type)break;this.skinInfo=e,e&&e.type===k.SuspensionSkinHelperNS.SkinType.Vip?E.showVipChangeWindow():E.showNormalWindow()}while(0)}initTaskManager(){let e=w.TaskManagerNS.getTaskManager();e.addListener(w.TaskManagerNS.eventIdGetTaskList,this.onGetTaskList.bind(this)),e.addListener(w.TaskManagerNS.eventIdTaskRemoved,this.onTaskRemoved.bind(this)),e.addListener(w.TaskManagerNS.eventIdTaskInserted,this.onTaskInserted.bind(this)),e.addListener(w.TaskManagerNS.eventIdTaskStatusChanged,this.onTaskStatusChanged.bind(this)),e.addListener(w.TaskManagerNS.eventIdTaskDetailChanged,this.onTaskDetailChanged.bind(this)),e.addListener(w.TaskManagerNS.eventIdBtSubFileLoaded,this.onBtSubFileLoaded.bind(this)),e.init()}setFloatPanelWindowPos(){return o(this,void 0,void 0,function*(){do{let e=E.getHandle();if(!e){C.warning("thunderSuspensionWindow getHandle failed");break}let t=R.getWindowRect(e),n=yield f.FloatPanelHelper.getFloatPanelWindowHandle();if(!n){C.warning("FloatPanelHelper getFloatPanelWindowHandle failed");break}let i=R.getWindowRect(n);if(!i){C.warning("getWindowRect failed");break}let o=yield this.getFloatPanelDirection(t.x+170*this.dpiFactor,t.y,i.width,i.height),r=this.setFloatPanelDirection(o,i);r.x+=210*this.dpiFactor,r.y+=60*this.dpiFactor,r.x=Math.round(t.x+r.x),r.y=Math.round(t.y+r.y),R.setWindowPos(n,0,r.x,r.y,394*this.dpiFactor,260*this.dpiFactor,4)}while(0)})}isMainWndShow(){return o(this,void 0,void 0,function*(){let e=yield h.client.callServerFunction("GetMainWindowStates");C.information("getMainWndState");let t=!0;return!e.minimized&&e.visible||(t=!1),t})}mounted(){return o(this,void 0,void 0,function*(){this.dpiFactor=f.FloatPanelHelper.getDpiFactor(),E.setDpiFactor(this.dpiFactor);let e=yield f.FloatPanelHelper.getFloatPanelWindowHandle();E.startSuspensionWindow(e),P.init(),s.ipcRenderer.on("suspension-window-restore",()=>{p.FloatPanelMenuHelper.restoreSuspension()}),s.ipcRenderer.on(S.ThunderChannelList.channelMRSuspensionWindowShowOrHide,(e,t)=>{if(t)this.startShowTime=(new Date).getTime(),f.FloatPanelHelper.trackEvent("float_monitor_show");else{let e=(((new Date).getTime()-this.startShowTime)/1e3).toFixed(2);f.FloatPanelHelper.trackEvent("float_monitor_online_time",`time=${e}`),this.startShowTime=0}}),this.listenMainEvent(),this.onGetUserInfo(),this.listenLoginEvent(),this.listenConfigEvent(),this.listenFullScreen(),this.listenGlobalSpeedChangeEvent(),this.initTaskManager(),this.listenTrialEvent(),h.client.attachServerEvent("OnChangeSuspensionSkin",(e,...t)=>{this.onChangeSkin(t[0])}),h.client.callServerFunction("GetSuspensionSkinInfo").then(e=>{e&&this.onChangeSkin(e)}).catch(),I.init(),(yield f.FloatPanelHelper.getFloatPanelWindow()).hookWindowMessage(736,(e,t)=>{let n=this.dpiFactor;this.dpiFactor=e[0]/96,C.information("OnDpiChanged this.dpiFactor:",this.dpiFactor,", old dpiFactor:",n),E.setDpiFactor(this.dpiFactor),this.setFloatPanelWindowPos().catch()}),E.setCreateCallback(()=>{this.initSuspensionPositionConfig()}),E.setRightClickDownCallback(()=>{C.information("ThunderFloat_RightDown")}),E.setRightClickUpCallback(()=>{C.information("ThunderFloat_RightUp"),this.handleRightClick()}),E.setLeftClickDownCallback(()=>{this.showOrHideFloatPanel(!1).catch(),C.information("ThunderFloat_LeftDown")}),E.setLeftDBClickCallback(()=>{C.information("ThunderFloat_LeftDb"),this.showOrHideMainWindow(!1).catch(),P.isBubbleVisible&&P.clickBubble(4),C.information("setLeftDBClickCallback ClickTryAcclerateBtn canTryTaskId,selectedTaskId,isDowning,isVip:",this.canTryTaskId,this.selectedTaskId,this.isDowning,this.isVip),this.canTryTaskId===this.selectedTaskId&&(C.information("setLeftDBClickCallback ClickTryAcclerateBtn taskId:",this.canTryTaskId),this.isDowning&&(this.isVip||(E.updateTrialState(!1),b.VipPluginFunctionNS.clickTryAcclerateBtn(this.selectedTaskId,"xfq").catch(),C.information("setLeftDBClickCallback ClickTryAcclerateBtn success:",this.canTryTaskId),this.tryingTaskId=this.canTryTaskId,this.canTryTaskId=1,this.selectedTaskId=0)))}),E.setHoverCallback((e,t,n,i)=>{this.isThunderForeground=f.FloatPanelHelper.isThunderMainWndForeground(),C.information("ThunderFloat_Hover"),C.information("hover isThunderForeground",this.isThunderForeground),this.setFloatPanelWindowPos(),R.getKeyState(2)>=0&&(P.isBubbleVisible||this.showOrHideFloatPanel(!0).catch()),E.showFloatArrowInAni()}),E.setTrialTextShowCallback((e,t,n,i)=>{C.information("setTrialTextShowCallback left.right:",e,t);let o=this.getCommonExtData();f.FloatPanelHelper.traceFloatPanelEvent("xfq_wzl_show",o)}),E.setTrialHoverAniShowCallback((e,t,n,i)=>{C.information("setTrialHoverAniShowCallback left.right:",e,t);let o=this.getCommonExtData();f.FloatPanelHelper.traceFloatPanelEvent("xfq_hover_show",o)}),E.setLeaveCallback((e,t)=>o(this,void 0,void 0,function*(){C.information("ThunderFloat_Leave");let n=yield f.FloatPanelHelper.getFloatPanelWindowHandle(),i=R.getWindowRect(n);i&&i.x<=e&&i.y<=t&&i.x+i.width>=e&&i.y+i.height>=t||(yield this.showOrHideFloatPanel(!1),P.showBubble()),this.isDowning||(this.skinInfo&&this.skinInfo.type===k.SuspensionSkinHelperNS.SkinType.Vip?E.showVipWindow():E.showNormalWindow())})),E.setMoveCallback(()=>{this.showOrHideFloatPanel(!1).catch()}),E.setLeftClickUpCallback((e,t,n)=>o(this,void 0,void 0,function*(){C.information("ThunderFloat_LeftUp");let i=f.FloatPanelHelper.isThunderMainWndForeground();C.information("isThunderForeground",i);let o=yield this.isMainWndShow();if(C.information("isMainWndShow",o),n>0&&n<4||4===n&&o)return void P.clickBubble(n);this.setFloatPanelWindowPos();let r={x:e,y:t,height:0,width:0};if(this.setSuspensionConfig(r),C.information("setLeftClickUpCallback ClickTryAcclerateBtn canTryTaskId,selectedTaskId,isDowning,isVip:",this.canTryTaskId,this.selectedTaskId,this.isDowning,this.isVip),this.canTryTaskId===this.selectedTaskId){let e=this.getCommonExtData();e+=",float_type=wzl",f.FloatPanelHelper.traceFloatPanelEvent("xfq_click",e),C.information("setLeftClickUpCallback ClickTryAcclerateBtn taskId:",this.canTryTaskId),this.isDowning&&(this.isVip||(this.showMainWindow().catch(),E.updateTrialState(!1),b.VipPluginFunctionNS.clickTryAcclerateBtn(this.selectedTaskId,"xfq").catch(),C.information("setLeftClickUpCallback ClickTryAcclerateBtn success:",this.canTryTaskId),this.tryingTaskId=this.canTryTaskId,this.canTryTaskId=1,this.selectedTaskId=0))}}))})}};i([d.Prop({})],x.prototype,"downloadStatus",void 0),i([d.Prop({})],x.prototype,"totalDownloadSpeed",void 0),i([d.Prop({})],x.prototype,"totalVipSpeed",void 0),i([d.Prop({})],x.prototype,"showStatus",void 0),i([d.Prop({})],x.prototype,"statusText",void 0),i([d.Prop({})],x.prototype,"taskIdLists",void 0),i([d.Prop({})],x.prototype,"taskBaseInfos",void 0),i([d.Prop({})],x.prototype,"cloudTaskIds",void 0),i([d.Prop({})],x.prototype,"cloudTasksMap",void 0),i([d.Prop({})],x.prototype,"INIT_TASKLIST",void 0),i([d.Prop({})],x.prototype,"INSERT_TASK",void 0),i([d.Prop({})],x.prototype,"REMOVE_TASK",void 0),i([d.Prop({})],x.prototype,"UPDATE_TASKSTATUS",void 0),i([d.Prop({})],x.prototype,"UPDATE_TASKDETAIL",void 0),i([d.Prop({})],x.prototype,"UPDATE_TASKBASE",void 0),i([d.Prop({})],x.prototype,"UPDATE_SUSPENSION",void 0),i([d.Watch("isDowningStatus")],x.prototype,"onDownloadingChanged",null),x=i([d.Component({components:{FloatPanelContainer:c.default}})],x),t.default=x},297:function(e,t,n){"use strict";n.r(t);var i=n(298),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},298:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(743),o=n(227);t.default=o.connector.connect({mapStateToProps:{taskBaseInfos:e=>e.Suspension.taskBaseInfos,taskIdLists:e=>e.Suspension.taskIdLists,cloudTaskIds:e=>e.CloudDownload.cloudTaskIds},mapCommitToProps:{localLoadMore:"LOCAL_LOAD_MORE"},mapDispatchToProps:{},mapGettersToProps:{localLoadedList:e=>e.localLoadedList}})(i.default)},299:function(e,t,n){"use strict";n.r(t);var i=n(300),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},3:function(e,t){e.exports=require("electron")},30:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},300:function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var o,r=arguments.length,s=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(r<3?o(s):r>3?o(t,n,s):o(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5),r=n(4),s=n(744),a=n(747),l=n(15),d=n(176),c=n(679),u=n(8),h=n(2),f=u.default(h.join(__rootDir,"../bin/ThunderSuspensionWindow.node")),p=n(1).default.getLogger("Thunder.SuspensionRender.FloatPanelVue"),m=c.HoverTextManagerNS.getHoverTextManager();let g=class extends o.Vue{constructor(){super(...arguments),this.yellowhoverText="开通会员，享金色悬浮球>",this.bluehoverText="开通会员，有效提速164%>",this.yellowTextUrl="https://pay.xunlei.com/pay.html?referfrom=v_pc_xlx_ggong_pay_xfq",this.blueTextUrl="https://pay.xunlei.com/pay.html?referfrom=v_pc_xlx_ggong_pay_xfq",this.yellowTextShow=!1,this.blueTextShow=!0,this.textShowTimer=null,this.timerPause=!1,this.activeKey="download",this.manualChangeTab=!1,this.tabs=[{title:"正在下载",key:"download"},{title:"云盘下载",key:"cloud"}]}get downloadingTitle(){return 0===this.taskIdLists.length?"正在下载":`正在下载(${this.taskIdLists.length})`}get cloudTitle(){let e="云盘下载";do{if(!this.isLogined)break;if(0===this.cloudTaskIds.length)break;e=`云盘下载(${this.cloudTaskIds.length})`}while(0);return e}get haveDownloadTask(){return this.taskIdLists.length>0}get footerText(){return"cloud"===this.activeKey?"查看下载列表":"查看已完成"}get footerVisible(){return"download"===this.activeKey||this.isLogined}getDownloadingDetail(){let e=0,t=0,n=[],i=d.FloatPanelHelper.noneReactiveTaskBaseInfos;for(let o in i){let r=i[o],s=r.taskStatus;s!==l.DownloadKernel.TaskStatus.PreDownloading&&s!==l.DownloadKernel.TaskStatus.StartWaiting&&s!==l.DownloadKernel.TaskStatus.StartPending&&s!==l.DownloadKernel.TaskStatus.Started||(t+=r.fileSize,e+=r.downloadSize,n.push(Number(o)))}return{downSize:e,totalSize:t,downloading:n}}getCloudDownloadingDetail(){let e=0,t=0,n=[],i=d.FloatPanelHelper.cloudNoneReactiveTasksMap;for(let o in i){let r=i[o],s=r.taskStatus;s!==l.DownloadKernel.TaskStatus.PreDownloading&&s!==l.DownloadKernel.TaskStatus.StartWaiting&&s!==l.DownloadKernel.TaskStatus.StartPending&&s!==l.DownloadKernel.TaskStatus.Started||(t+=r.fileSize,e+=r.downloadSize,n.push(Number(o)))}return{downSize:e,totalSize:t,downloading:n}}onTabSelectChange(e){let t="download"===e?"downloading":"yunpan_getback";d.FloatPanelHelper.trackEvent("float_monitor_hover_tab_click",`tab_id=${t}`)}onFloatPanelHover(e){do{if(!e)break;if(this.manualChangeTab)break;if(this.activeKey="download",!this.isLogined)break;if(0===this.cloudTaskIds.length)break;if(0===this.taskIdLists.length){this.activeKey="cloud";break}if(0===this.getCloudDownloadingDetail().downloading.length)break;if(0===this.getDownloadingDetail().downloading.length){this.activeKey="cloud";break}}while(0)}onVipChange(e,t){p.information("is vip change ",e,t),this.init()}onClickShowFinishTask(){r.client.callServerFunction("SelectCategoryView",-1,l.DownloadKernel.CategroyViewID.Completed,void 0,!0).catch(),"download"===this.activeKey?d.FloatPanelHelper.trackEvent("float_monitor_hover_finished_click"):d.FloatPanelHelper.trackEvent("float_monitor_hover_getback_list_btn_click"),this.$emit("show-float-panel",!1)}clickItem(e){d.FloatPanelHelper.trackEvent("float_monitor_hover_downloading_click"),r.client.callServerFunction("SelectCategoryView",-1,l.DownloadKernel.CategroyViewID.Downloading,e,!0).catch(),this.$emit("show-float-panel",!1)}init(){if(p.information("float-panel mounted"),m.isVip)return this.blueTextShow=!1,this.yellowTextShow=!1,void(null!==this.textShowTimer&&(clearTimeout(this.textShowTimer),this.textShowTimer=null));this.yellowTextShow=!1,this.blueTextShow=!0;let e=m.getText();if(p.information("textData",e),e)for(let t=0;t<e.length;t++){const n=e[t];p.information("element",n),n&&n.color&&"yellow"===n.color&&(n.text&&""!==n.text&&(this.yellowhoverText=n.text),n.url&&""!==n.url&&(this.yellowTextUrl=this.checkUrlRefParam(n.url))),n&&n.color&&"blue"===n.color&&(n.text&&""!==n.text&&(this.bluehoverText=n.text),n.url&&""!==n.url&&(this.blueTextUrl=this.checkUrlRefParam(n.url)))}p.information("bluehoverText",this.bluehoverText),p.information("yellowhoverText",this.yellowhoverText),p.information("blueTextUrl",this.blueTextUrl),p.information("yellowTextUrl",this.yellowTextUrl),p.information("textShowTimer",this.textShowTimer),this.textShowTimer||(this.textShowTimer=setInterval(()=>{this.timerPause||(this.blueTextShow=!this.blueTextShow,this.yellowTextShow=!this.yellowTextShow,p.information("bluehoverText",this.bluehoverText),p.information("yellowhoverText",this.yellowhoverText),p.information("blueTextUrl",this.blueTextUrl),p.information("yellowTextUrl",this.yellowTextUrl),p.information("blueTextShow",this.blueTextShow),p.information("yellowTextShow",this.yellowTextShow),this.blueTextShow&&this.onTextShow("1"),this.yellowTextShow&&this.onTextShow("2"))},5e3)),this.bluehoverText=this.escape2Html(this.bluehoverText),this.yellowhoverText=this.escape2Html(this.yellowhoverText),p.information("bluehoverText",this.bluehoverText),p.information("yellowhoverText",this.yellowhoverText);let t=this.getCommonExtData();d.FloatPanelHelper.traceFloatPanelEvent("float_hover_show",t),this.onTextShow("1")}mounted(){this.init()}escape2Html(e){return e.replace("&gt;",">")}checkUrlRefParam(e){return-1===e.indexOf("?")&&(e+="?"),e+="referfrom=v_pc_xlx_ggong_pay_xfq"}getCommonExtData(){let e="";return e=(e=(e=e+",is_login="+(m.isLogined?"1":"0"))+",is_vip="+(m.isVip?"1":"0"))+",vip_type="+m.vipType}onTextShow(e){p.information("onTextShow",e);let t=this.getCommonExtData();t=t+",type="+e,d.FloatPanelHelper.traceFloatPanelEvent("float_hover_wzl_show",t)}onTextClick(e){p.information("onTextShow");let t=this.getCommonExtData();t=t+",type="+e,d.FloatPanelHelper.traceFloatPanelEvent("float_open_vip_click",t);let n=this.yellowTextUrl+"&aidfrom=xds_golden";"blue"===e&&(n=this.blueTextUrl+"&aidfrom=xds_common"),p.information("url",n),r.client.callServerFunction("BringMainWndToTop").catch(),r.client.callServerFunction("OpenNewTab",n).catch()}onMouseOver(e){p.information("onMouseOver",e),p.information("isDonwloading",m.isDonwloading),this.timerPause=!0,e&&(m.isDonwloading?f.showVipWindow():(p.information("showFloatChangeAni"),f.showFloatChangeAni()))}onMouseLeave(e){p.information("onMouseLeave",e),this.timerPause=!1,e&&(f.stopFloatChangeAni(),f.showNormalWindow())}destroyed(){null!==this.textShowTimer&&(clearTimeout(this.textShowTimer),this.textShowTimer=null)}};i([o.Prop({})],g.prototype,"taskBaseInfos",void 0),i([o.Prop({})],g.prototype,"taskIdLists",void 0),i([o.Prop({})],g.prototype,"localLoadedList",void 0),i([o.Prop({})],g.prototype,"cloudTaskIds",void 0),i([o.Prop({})],g.prototype,"isLogined",void 0),i([o.Prop({})],g.prototype,"isShow",void 0),i([o.Prop({})],g.prototype,"isVip",void 0),i([o.Prop({})],g.prototype,"localLoadMore",void 0),i([o.Watch("activeKey")],g.prototype,"onTabSelectChange",null),i([o.Watch("isShow")],g.prototype,"onFloatPanelHover",null),i([o.Watch("isVip")],g.prototype,"onVipChange",null),g=i([o.Component({components:{TaskItemContainer:s.default,CloudDownloadContainer:a.default}})],g),t.default=g},301:function(e,t,n){"use strict";n.r(t);var i=n(302),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},302:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(678),o=n(227);t.default=o.connector.connect({mapStateToProps:{taskBase:(e,t)=>{let n=t.taskId;return e.Suspension.taskBaseInfos[n]}}})(i.default)},303:function(e,t,n){"use strict";n.r(t);var i=n(304),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},304:function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var o,r=arguments.length,s=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(r<3?o(s):r>3?o(t,n,s):o(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s};Object.defineProperty(t,"__esModule",{value:!0});const o=n(6),r=n(5),s=n(37),a=n(176);let l=class extends r.Vue{get taskName(){return this.taskBase.taskName}get getTaskProgress(){let e="width: ",t=0;0!==this.taskBase.fileSize&&(t=this.taskBase.downloadSize/this.taskBase.fileSize),t>=1&&(t=.9999);let n=(100*t).toFixed(2)+"%";return"100.00%"===n&&(n="99.99%"),e+=n}get getTaskIcon(){return s.TaskUtilHelper.getTaskIcon(this.taskBase.taskName,this.taskBase.taskType)}get stateName(){let e="",t=a.FloatPanelHelper.getTaskStatusPrompt(this.taskBase),n=a.FloatPanelHelper.getTaskStartPrompt(this.taskBase);return o.isNullOrUndefined(n)?o.isNullOrUndefined(t)||(e=t):e=n,e}};i([r.Prop({})],l.prototype,"taskId",void 0),i([r.Prop({})],l.prototype,"taskBase",void 0),l=i([r.Component({})],l),t.default=l},305:function(e,t,n){"use strict";n.r(t);var i=n(306),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},306:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(748),o=n(227);t.default=o.connector.connect({mapStateToProps:{cloudTaskIds:e=>e.CloudDownload.cloudTaskIds},mapCommitToProps:{resetStore:"RESET_STORE",initTasks:"INIT_TASKS",removeTasks:"REMOVE_TASKS",insertTasks:"INSERT_TASKS",updateTasksDetail:"UPDATE_TASKS_DETAIL",updateTasksStatus:"UPDATE_TASKS_STATUS",cloudLoadMore:"CLOUD_LOAD_MORE"},mapDispatchToProps:{},mapGettersToProps:{cloudLoadedList:e=>e.cloudLoadedList}})(i.default)},307:function(e,t,n){"use strict";n.r(t);var i=n(308),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},308:function(e,t,n){"use strict";var i=this&&this.__decorate||function(e,t,n,i){var o,r=arguments.length,s=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(r<3?o(s):r>3?o(t,n,s):o(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},o=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const r=n(5),s=n(1),a=n(4),l=n(15),d=n(749),c=n(176),u=s.default.getLogger("Suspension cloud");let h=class extends r.Vue{constructor(){super(...arguments),this.currentCategoryId=null,this.statusChangedCookie=null,this.detailChangedCookie=null,this.insertedCookie=null,this.removedCookie=null}get empty(){return!this.isLogined||!this.cloudTaskIds.length}onLoginChange(e,t){return o(this,void 0,void 0,function*(){if(u.information("cloud login change",e,t),this.resetStore(),this.currentCategoryId=null,this.isLogined){yield a.client.callServerFunction("IsCategoryLoaded"),yield a.client.callServerFunction("IsLoadStorageTaskFinish");let e=yield a.client.callServerFunction("GetUserID"),t=yield a.client.callServerFunction("GetCategoryFromUserId","ThunderPanPlugin",e,"ThunderPan Download Category");t&&(this.currentCategoryId=t.categoryId);let n=yield a.client.callServerFunction("GetCategoryTaskList",this.currentCategoryId,l.DownloadKernel.CategroyViewID.Downloading);if(n&&n.length>0){let e=yield a.client.callServerFunction("GetTaskBaseInfos",n);e&&e.length>0&&this.initTasks(e)}}})}onTaskStatusChanged(e,t){u.information("onTaskStatusChanged"),this.updateTasksStatus(t)}onTaskDetailChanged(e,t){u.information("onTaskDetailChanged"),this.updateTasksDetail(t)}onTaskInserted(e,t,n,i){return o(this,void 0,void 0,function*(){u.information("onTaskInserted");do{if(0===i.length)break;if(this.currentCategoryId!==t)break;if(n!==l.DownloadKernel.CategroyViewID.Downloading)break;let e=yield a.client.callServerFunction("GetTaskBaseInfos",i);e&&e.length>0&&this.insertTasks(e)}while(0)})}onTaskRemoved(e,t,n,i){u.information("onTaskRemoved");do{if(0===i.length)break;if(this.currentCategoryId!==t)break;if(n!==l.DownloadKernel.CategroyViewID.Downloading)break;this.removeTasks(i)}while(0)}created(){return o(this,void 0,void 0,function*(){u.information("cloud created"),this.statusChangedCookie=a.client.attachServerEvent("OnTaskStatusChanged",(e,...t)=>{let n=null;try{n=JSON.parse(t[0])}catch(e){}n&&(this.onTaskStatusChanged(e,n),this.$nextTick(()=>{this.$emit("refresh-event")}))}),this.detailChangedCookie=a.client.attachServerEvent("OnTaskDetailChanged",(e,...t)=>{let n=null;try{n=JSON.parse(t[0])}catch(e){}n&&(this.onTaskDetailChanged(e,n),this.$nextTick(()=>{this.$emit("refresh-event")}))}),this.insertedCookie=a.client.attachServerEvent("OnTaskInserted",(e,t,n,i)=>{let o=[];try{o=JSON.parse(i)}catch(e){}o&&this.onTaskInserted(e,t,n,o)}),this.removedCookie=a.client.attachServerEvent("OnTaskRemoved",(e,t,n,i)=>{let o=[];try{o=JSON.parse(i)}catch(e){}o&&this.onTaskRemoved(e,t,n,o)})})}mounted(){u.information("cloud panel mounted")}destroyed(){this.statusChangedCookie&&(a.client.detachServerEvent("OnTaskStatusChanged",this.statusChangedCookie),this.statusChangedCookie=null),this.detailChangedCookie&&(a.client.detachServerEvent("OnTaskDetailChanged",this.detailChangedCookie),this.detailChangedCookie=null),this.insertedCookie&&(a.client.detachServerEvent("OnTaskInserted",this.insertedCookie),this.insertedCookie=null),this.removedCookie&&(a.client.detachServerEvent("OnTaskRemoved",this.removedCookie),this.removedCookie=null)}clickItem(e){c.FloatPanelHelper.trackEvent("float_monitor_hover_getback_item_click");a.client.callServerFunction("SelectNav","pan-plugin-view").catch(),a.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcShowTransferWindow","download",[e],"float_monitor_hover").catch(),this.$emit("show-float-panel",!1)}};i([r.Prop({})],h.prototype,"isLogined",void 0),i([r.Prop({})],h.prototype,"cloudTaskIds",void 0),i([r.Prop({})],h.prototype,"cloudLoadedList",void 0),i([r.Prop({})],h.prototype,"resetStore",void 0),i([r.Prop({})],h.prototype,"initTasks",void 0),i([r.Prop({})],h.prototype,"removeTasks",void 0),i([r.Prop({})],h.prototype,"insertTasks",void 0),i([r.Prop({})],h.prototype,"updateTasksDetail",void 0),i([r.Prop({})],h.prototype,"updateTasksStatus",void 0),i([r.Prop({})],h.prototype,"cloudLoadMore",void 0),i([r.Watch("isLogined",{immediate:!0})],h.prototype,"onLoginChange",null),h=i([r.Component({components:{TaskItemContainer:d.default}})],h),t.default=h},309:function(e,t,n){"use strict";n.r(t);var i=n(310),o=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,function(){return i[e]})}(r);t.default=o.a},31:function(e,t,n){e.exports=n(9)(45)},310:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(678),o=n(227);t.default=o.connector.connect({mapStateToProps:{taskBase:(e,t)=>{let n=null,i=t.taskId;return n=e.CloudDownload.cloudTasksMap[i]}},mapCommitToProps:{},mapDispatchToProps:{},mapGettersToProps:{}})(i.default)},32:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(8).default(i.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(){let e=!0;{0;let t=i.resolve("C:\\ETW_LOG\\log.ini");e="1"===o.readINI(t,"Log","enable")}return e}e.isDevToolsEnable=function(){return t()},e.isLogEnable=t,e.getLogOutput=function(){let e=process.env.TL_OUTPUT;do{if(e&&""!==e)break;let t=i.resolve("C:\\ETW_LOG\\log.ini");e=o.readINI(t,"Log","output")}while(0);return e}}(t.DevEnvHelperNS||(t.DevEnvHelperNS={}))},33:function(e,t){e.exports=require("net")},34:function(e,t){e.exports=require("url")},35:function(e,t,n){"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(i||(i={})),t.default=i},36:function(e,t,n){"use strict";var i;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(i||(i={})),e.exports=i},37:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(15);let r=["apk","pic","video","mp4","rmvb","wmv","mpg","mkv","mov","rm","avi","flv","doc","link","ppt","word","magnetic","music","pdf","rar","xls","txt","unknow","gif","ipa","ipsw","dll","chm","text","install","iso"];const s=".xv;.xlmv;.3gp;.3gp2;.3gpp;.3gpp2;.3mm;.3p2;.60d;.787;.aaf;.aep;.aepx;.aet;.aetx;.ajp;.ale;.amv;.amx;.arf;\n  .asf;.asx;.avb;.avd;.avi;.avp;.avs;.avs;.axm;.bdm;.bdmv;.bik;.bix;.bmk;.bnp;.box;.bs4;.bsf;.byu;.camproj;.camrec;.clpi;.cmmp;\n  .cmmtpl;.cmproj;.cmrec;.cpi;.cst;.cvc;.d2v;.d3v;.dat;.dav;.dce;.dck;.ddat;.dif;.dir;.divx;.dlx; .dmb;.dmsm;.dmsm3d;.dmss;.dnc;.dpg;\n  .dream;.dsy;.dv;.dv-avi;.dv4;.dvdmedia;.dvr-ms;.dvx;.dxr;.dzm;.dzp;.dzt;.edl;.evo;.eye;.f4p;.f4v;.fbr;.fbr;.fbz;.fcp;.flc;.flh;\n  .fli;.flv;.flx;.gfp;.gl;.grasp;.gts;.gvi;.gvp;.hdmov;.hkm;.ifo;.imovieproj;.imovieproject;.iva;.ivf;.ivr;.ivs;.izz;.izzy;.jts;.jtv;\n  .k3g;.lrec;.lsf;.lsx;.m15;.m1pg;.m1v;.m21;.m21;.m2a;.m2p;.m2t;.m2ts;.m2v;.m4e;.m4u;.m4v;.m75;.meta;.mgv;.mj2;.mjp;.mjpg;.mkv;.mmv;\n  .mnv;.mod;.modd;.moff;.moi;.moov;.mov;.movie;.mp21;.mp2v;.mp4;.mp4v;.mpe;.mpeg;.mpeg4;.mpf;.mpg;.mpg2;.mpgindex;.mpl;.mpls;\n  .mpsub;.mpv;.mpv2;.mqv;.msdvd;.msh;.mswmm;.mts;.mtv;.mvb;.mvc;.mvd;.mve;.mvp;.mvy;.mxf;.mys;.ncor;.nsv;.nuv;.nvc;.ogm;.ogv;.ogx;.osp;\n  .par;.pds;.pgi;.piv;.pjs;.pmf;.pns;.ppj;.prel;.pro;.prproj;.prtl;.psh;.pssd;.pva;.pvr;.pxv;.qt;.qtch;.qtl;.qtm;.qtz;\n  .r3d;.rcproject;.rdb;.rec;.rm;.rmd;.rmp;.rms;.rmvb;.roq;.rp;.rts;.rts;.rum;.rv;.sbk;.sbt;.scc;.scm;.scn;.screenflow;.sec;.seq;.sfd;\n  .sfvidcap;.smk;.sml;.smv;.spl;.ssm;.stl;.str;.stx;.svi;.swf;.swi;.swt;.tda3mt;.tivo;.tix;.tod;.tp;.tp0;.tpd;\n  .tpr;.trp;.ts;.tts;.tvs;.vc1;.vcpf;.vcr;.vcv;.vdo;.vdr;.veg;.vem;.vf;.vfw;.vfz;.vgz;.vid;.viewlet;.viv;.vivo;.vlab;.vob;.vp3;.vp6;.vp7;\n  .vro;.vs4;.vse;.vsp;.w32;.wcp;.webm;.wlmp;.wm;.wmd;.wmmp;.wmv;.wmx;.wp3;.wpl;.wtv;.wvx;.xfl;.xvid;.yuv;.zm1;.zm2;.zm3;.zmv;",a=".exe;.com;.bat;.msi;.apk;.ipa;.iso;.mds;.bin;.img;.ipsw;",l=".txt;.html;.htm;.shtml;.xhtml;.chm;.hlp;.inf;.rtf;.tex;.ltx;.doc;.docx;.wps;.ppt;.pptx;.odf;.pdf;.xls;.xlsx;.docm;.\n  dot;.dotm;.dotx;.email;.rp;.pps;.et;.ett;.xlt;.dbf;.prn;.csv;.mht;.mhtml;.xml;.wpt;.dps;.dpt;.pot;.ppsx;.epub;.mobi;.lit;.wdl;.ceb;.abm;\n  .pdg;.umb;.ibooks;",d=".aiff;.cue;.m3u;.au;.cdda;.raw;.wav;.flac;.tak;.mp3;.aac;.wma;.m4a;.mid;.mka;.mp2;.mpa;.mpc;.ape;.ofr;\n  .ogg;.ra;.wv;.tta;.ac3;.dts;.nsf;.mod;.s3m;.xm;.it;.vst;",c=".psd;.tga;.gif;.jpeg;.jpg;.jp2;.bmp;.ico;.pcx;.png;.pbm;.pgm;.ppm;.pnm;.pgf;.arw;.cr2;.crw;.dcr;.dng;.erf;.kdc;.mef;\n  .mos;.mrw;.nef;.nrw;.orf;.pef;.ptx;.r3d;.raf;.raw;.rw2;.srf;.srw;.x3f;.ras;.tiff;.tif;.wbmp;.ilbm;.lbm;.iff;.ico;",u=".zip;.zipx;.rar;.7z;.isz;.cab;.arj;.ace;.alz;.uue;.tar;.gz; .gzip;.tgz;.tpz;.bzip2;.bz2;.bz;.tbz;.tbz2;.xz;.txz;\n  .lzh;.lha;.zt;.az; .xpi;.jar;.wim;.swm;.rpm;.xar;.deb;.dmg;.hfs;.cpio;.lzma;.lzma86;.split;",h=".torrent;",f=".idx;.smi;.sub;.psb;.ssa;.ass;.usf;.ssf;.srt;.sup",p=".3gp;.asf;.avi;.divx;.f4v;.flv;.mkv;.mov;.movie;.mp4;.mpeg;.mpeg4;.mpg;.mpg2;.rm;.rmvb;.rp;.swf;.tp;.tp0;.ts;.wmv",m=".exe;.com;.bat;.msi",g=".wav;.flac;.mp3;.aac;.wma;.m4a;.mid;.ape;.ogg;.ra;.mod",v=".psd;.tga;.gif;.jpeg;.jpg;.jp2;.bmp;.ico;.pcx;.pdf;.png;.pbm;.pgm;.ppm;.pnm;.pgf;.arw;.cr2;.crw;.dcr;.dng;.erf;.kdc;\n  .mef;.mos;.mrw;.nef;.nrw;.orf;.pef;.ptx;.r3d;.raf;.raw;.rw2;.srf;.srw;.x3f;.ras;.tiff;.tif;.wbmp;.ilbm;.lbm;.iff;.ico",w=".txt;.html;.htm;.shtml;.xhtml;.chm;.hlp;.inf;.rtf;.tex;.ltx;.doc;.docx;.wps;.ppt;.pptx;.odf;.pdf;.xls;.xlsx;.docm;.dot;.dotm;.dotx;.email;.rp;.pps",S=".rar;.tar;.zip;.gzip;.cab;.uue;.arj;.bz2;.lzh;.jar;.ace;.iso;.7-zip;.7z",_=".asf;.mpg;.rmvb;.rm;.wmv;.avi;.mp4;.mpeg;.mkv;.mov;.ts;.flv;.3gp;.m2ts;",T=".exe;.com;.bat;.scr;.lnk;.pif;.wsh;",b=".iso;";!function(e){let t;function n(e){let n=t.Unkown,o=i.extname(e);return null!==o&&void 0!==o&&o.length>=2&&(o=o.toLowerCase()),void 0===o||""===o||o.length<2?n=t.Unkown:p.indexOf(o)>-1?n=t.Video:m.indexOf(o)>-1?n=t.Software:w.indexOf(o)>-1?n=t.Doc:g.indexOf(o)>-1?n=t.Music:v.indexOf(o)>-1?n=t.Pic:S.indexOf(o)>-1&&(n=t.Zip),o.length>1&&".z"===o.slice(0,2)&&/[0-9]+/.test(o.substring(2))&&(n=t.Zip),n}e.getTaskIcon=function(e,t,n){n=n||"xly-type-";let p="";do{if(t===o.DownloadKernel.TaskType.Bt){p="bt-file";break}if(t===o.DownloadKernel.TaskType.Group){p="group";break}p="unknown";let n=i.extname(e);if(""===n||n.length<2)break;let m=(n=n.toLowerCase()).substring(1);if(r.indexOf(m)>-1){p="doc"===n?"word":m;break}if(n+=";",s.indexOf(n)>-1){p="video";break}if(a.indexOf(n)>-1){p="install",[".mds;",".bin;",".img;"].indexOf(n)>-1&&(p="iso");break}if(l.indexOf(n)>-1){if(p="doc",[".htm;",".html;",".mht;"].indexOf(n)>-1){p="link";break}if(".docx;"===n){p="word";break}if(".xlsx;"===n){p="xls";break}if(".pptx;"===n){p="ppt";break}break}if(d.indexOf(n)>-1){p="music";break}if(c.indexOf(n)>-1){p="pic";break}if(u.indexOf(n)>-1){p="rar";break}if(h.indexOf(n)>-1){p="bt-link";break}if(f.indexOf(n)>-1){p="text";break}}while(0);return`${n}${p}`},function(e){e[e.Unkown=0]="Unkown",e[e.Video=1]="Video",e[e.Software=2]="Software",e[e.Doc=3]="Doc",e[e.Music=4]="Music",e[e.Pic=5]="Pic",e[e.Zip=6]="Zip",e[e.Bt=7]="Bt"}(t=e.FileExtType||(e.FileExtType={})),e.getTaskFileType=function(e,n){let o=t.Unkown;return void 0===n&&void 0!==e&&(n=i.extname(e)),null!==n&&void 0!==n&&n.length>=2&&(n=n.toLowerCase(),n+=";"),void 0===n||""===n||n.length<3?o=t.Unkown:s.indexOf(n)>-1?o=t.Video:a.indexOf(n)>-1?o=t.Software:l.indexOf(n)>-1?o=t.Doc:d.indexOf(n)>-1?o=t.Music:c.indexOf(n)>-1?o=t.Pic:u.indexOf(n)>-1?o=t.Zip:h.indexOf(n)>-1&&(o=t.Bt),n.length>1&&".z"===n.slice(0,2)&&/[0-9]+/.test(n.substring(2))&&(o=t.Zip),o},e.isVideoFileExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=i.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",_.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isSubtitleExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=i.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",f.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isExecutableExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=i.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",T.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isIsoExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=i.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",b.indexOf(n)>-1&&(t=!0)}while(0);return t},e.getGroupFileType=n,e.getDefaultGroupPrefix=function(e){let i="任务组";do{if(void 0===e||null===e||0===e.length)break;let o=[];for(let e=0;e<7;e++)o[e]=0;for(let t of e){let e=n(t);o[e]=o[e]+1}let r=t.Unkown;for(let e=1;e<o.length;e++)o[e]>o[r]&&(r=e);r===t.Video?i="视频任务组":r===t.Software?i="程序任务组":r===t.Music?i="音乐任务组":r===t.Pic?i="图片任务组":r===t.Doc?i="文档任务组":r===t.Zip&&(i="压缩包任务组")}while(0);return i},e.compareVersion=function(e,t){let n=-2;do{if(null===e||void 0===e||""===e||null===t||void 0===t||""===t)break;let i=0,o=0,r=0,s=0,a=0,l=0,d=0,c=0,u=e.split(/\./);if(null===u||void 0===u||u.length<3)break;if(i=Number(u[0]),o=Number(u[1]),r=Number(u[2]),null!==u[3]&&void 0!==u[3]&&""!==u[3]&&(s=Number(u[3])),null===(u=t.split(/\./))||void 0===u||u.length<3)break;if(a=Number(u[0]),l=Number(u[1]),d=Number(u[2]),null!==u[3]&&void 0!==u[3]&&""!==u[3]&&(c=Number(u[3])),a>i){n=1;break}if(a<i){n=-1;break}if(l>o){n=1;break}if(l<o){n=-1;break}if(d>r){n=1;break}if(d<r){n=-1;break}if(0!==s){if(c>s){n=1;break}if(c<s){n=-1;break}}n=0}while(0);return n}}(t.TaskUtilHelper||(t.TaskUtilHelper={}))},38:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(3),o=n(6),r=n(22),s=n(23),a=n(28);!function(e){class t extends a.CommonIPCBase.Communicator{constructor(){super()}initialize(e){this.currInfo={id:void 0,context:e,isMainCommunicator:!1}}connect(){this.isConnected?r.warning("has been connected"):(this.sendConnectMsgToMain(),this.isConnected=!0,this.startListenIPCMessage(!1))}disconnect(){this.isConnected?(this.isConnected=!1,this.sendDisconnectMsgToMain()):r.warning("hasnot been connected yet")}sendMessageToMain(e){this.sendIPCMsgToMain(e)}sendMessageToMainSync(e){return this.sendIPCMsgToMain(e,!0)}sendMessageToRenderer(e,t){this.sendIPCMsgToRenderer(e,t)}handleRendererConnectMsg(e,t){do{if(o.isNullOrUndefined(t)){r.error("msgInfo is null");break}let e=t.msg.args[0];if(o.isNullOrUndefined(e)){r.error("connectRendererInfo is null");break}r.information(`Renderer: new renderer will connect, id = ${e.id}, context = ${e.context}`),this.rendererInfos.push(e)}while(0)}handleRendererDisconnectMsg(e,t){do{if(o.isNullOrUndefined(t)){r.error("msgInfo is null");break}let e=t.msg.args[0];if(o.isNullOrUndefined(e)){r.error("disconnectRendererInfo is null");break}r.information(`renderer will disconnect, id = ${e.id}, context = ${e.context}`);for(let t=0;t<this.rendererInfos.length;++t)if(this.rendererInfos[t]===e){this.rendererInfos.splice(t,1);break}}while(0)}sendConnectMsgToMain(){let e=this.sendMessageToMainSync({name:s.CommonIPCMessage.msgIPCRendererConnect,args:[]});this.currInfo.id=e[0],this.rendererInfos=e[1]}sendDisconnectMsgToMain(){this.sendMessageToMain({name:s.CommonIPCMessage.msgIPCRendererDisconnect,args:[]})}sendIPCMsgToMain(e,t=!1){let n=void 0;do{if(o.isNullOrUndefined(e)){r.error("msg is null");break}n=(t?i.ipcRenderer.sendSync:i.ipcRenderer.send)(s.CommonIPCMessage.msgIPCSendToMain,{msg:e,senderInfo:this.currInfo})}while(0);return n}sendIPCMsgToRenderer(e,t){do{if(o.isNullOrUndefined(e)){r.error("rendererId is null");break}if(o.isNullOrUndefined(t)){r.error("msg is null");break}let n=[e].concat(t.args);t.args=n,i.ipcRenderer.send(s.CommonIPCMessage.msgIPCSendToRenderer,{msg:t,senderInfo:this.currInfo})}while(0)}}e.RendererCommunicator=t,e.rendererCommunicator=new t}(t.CommonIPCRenderer||(t.CommonIPCRenderer={}))},39:function(e,t){e.exports=require("crypto")},4:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(12),r=n(50),s=n(29),a=n(30);function l(e){a.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let d=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends o.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,i){do{if(t||(t=s.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(i)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))i("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{i("error",e)}),e.on("connect",()=>{i("connect")}),e.on("end",()=>{let t=e.isInprocess();i("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===s.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let o=new r.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=o,n||(this.client=o),o.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(o,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(o,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(o,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(o,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===o&&this.emit("stat_call_function_back",o.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let i=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,i));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),o.on("error",e=>{i&&i("error",e),this.emit("socket-error",e,o.getContext(),n,o.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),o.isInprocess()?(this.connectedMap[t.toLowerCase()]=o,i&&i("connect"),this.emit("connect",o.getContext(),n,!0)):o.on("connect",()=>{this.connectedMap[t.toLowerCase()]=o,i&&i("connect"),this.emit("connect",o.getContext(),n,!1)}),o.on("end",()=>{let e=o.isInprocess();a.information("server is ended, and this client emit end",t,n,e),i&&i("end",o.getContext(),n,e),this.emit("end",o.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(o)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return i(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return i(this,void 0,void 0,function*(){let n=null,i=yield this.callServerFunctionEx(e,...t);return i&&(n=i[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return i(this,void 0,void 0,function*(){let i=null,o=yield this.crossCallServerFunctionEx(e,t,...n);return o&&(i=o[0]),i})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(i,t,...n)}}crossIsRemoteClientExist(e,t){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return i(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(i,t,n)}})}crossCallRemoteClientFunction(e,t,n,...i){{if(!e)throw new Error("An argument for 'productId' was not provided");let o=this.singletonMap[e.toLowerCase()];if(!o)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(o,t,n,...i)}}crossAttachServerEvent(e,t,n){let i=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let o=this.singletonMap[e.toLowerCase()];if(!o)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");i=this.internalAttachServerEvent(o,t,n)}return i}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(i,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let i=this.singletonMap[e.toLowerCase()];if(!i)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(i,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,i)=>{do{if(!t){n(!1);break}let i=this.generateId();const o={alias:t,action:"register",rid:i};let r=(e,i)=>{e?(a.error("register error",e.message),n(i)):n(t)};this.retCallbackMap[i]=Object.assign({callback:r},o),this.sendAdapter(e,o)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(a.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===s.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,i)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let i=this.generateId();const o={action:"check_server_function_exist",method:t,rid:i};let r=(e,t)=>{n(!e&&t)};this.retCallbackMap[i]=Object.assign({callback:r},o),this.sendAdapter(e,o)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((i,o)=>{do{if(!e){i([null,"client doesn't ready"]);break}if(!t){i([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let o=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const r={rid:o,method:t,args:n};let s=(t,n)=>{t?(a.error("callServerFunction error",t,e.getContext()),i([null,t])):i([n,void 0])};this.retCallbackMap[o]=Object.assign({callback:s},r),this.sendAdapter(e,r)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,i)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let i=this.generateId();const o={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:i};let r=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[i]=Object.assign({callback:r},o),this.sendAdapter(e,o)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((i,o)=>{do{if(!e){i(!1);break}if(!t){i(!1);break}if(!n){i(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){i(!(!this.apis||!this.apis[n]));break}let o=this.generateId();const r={action:"check_client_function_exist",method:n,rid:o,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let s=(e,t)=>{i(!e&&t)};this.retCallbackMap[o]=Object.assign({callback:s},r),this.sendAdapter(e,r)}while(0)})}internalCallRemoteClientFunction(e,t,n,...i){return new Promise((o,r)=>{do{if(!e){o([null,"client doesn't ready"]);break}if(!t){o([null,"remote client alias is not specifed"]);break}if(!n){o([null,"funcName is not specifed"]);break}let r=(e,t)=>{e?(a.information("callRemoteClientFunction",e.message),o([null,e])):o([t,void 0])};if(i)for(let e=0;e<i.length;e++)i[e]=this.convertFunction2IdEx(i[e]);let s=this.generateId();const l={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:i,rid:s};this.retCallbackMap[s]=Object.assign({callback:r},l),this.sendAdapter(e,l)}while(0)})}internalAttachServerEvent(e,t,n){let i=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(i)||(this.eventCallbackMaps[i]={}),this.eventCallbackMaps[i].hasOwnProperty(t)||(this.eventCallbackMaps[i][t]={}),s.isObjectEmpty(this.eventCallbackMaps[i][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let o=this.generateId();return this.eventCallbackMaps[i][t][o]=n,o}internalDetachServerEvent(e,t,n){let i=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(i))break;if(!this.eventCallbackMaps[i].hasOwnProperty(t))break;delete this.eventCallbackMaps[i][t][n],s.isObjectEmpty(this.eventCallbackMaps[i][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let i=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(i))break;if(!this.eventCallbackMaps[i].hasOwnProperty(t))break;let e=this.eventCallbackMaps[i][t];for(let t in e){let i=e[t];i&&i.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...i){let o=void 0,r=!1;do{const s=this.contextCallbackMap[t];if(!s){a.error("the context function has been freeer",t),o={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let l=void 0,d=void 0;try{l=s.apply(null,...i)}catch(e){d=e.message;break}if(void 0===n||null===n)break;if(o={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==d){o.error=d;break}if(l&&l.then){l.then(t=>{o.data=this.convertFunction2IdEx(t),o.success=!0,this.sendAdapter(e,o)}).catch(t=>{o.error=t instanceof Error?t.message:t,this.sendAdapter(e,o)}),r=!0;break}o.success=!0,o.data=this.convertFunction2IdEx(l)}while(0);!r&&o&&this.sendAdapter(e,o)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let i=this.generateId();this.contextCallbackMap[i]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":i}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let i=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(i){n=((...e)=>{this.callRemoteContextById(t,i,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:i},l);break}for(let n in e){let i=e[n];e[n]=this.decodeParameter(i,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,i=!1;do{if(!t)break;let o=t.method;if(!o)break;let r=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(r):[].concat(r)},!this.apis||!this.apis[o]){n.error=`callRegisterFunction ${o} is undefined`;break}let s=void 0,a=this.decodeParameter(t.args,t.src);try{s=this.apis[o].apply(null,[t.src].concat(a))}catch(e){n.error=e.message;break}if(s&&s.then){s.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),i=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(s)}while(0);a.information("callRegisterFunction",n),!i&&n&&this.sendAdapter(e,n)}}),d=global.__xdasIPCClienInstance,t.client=d},40:function(e,t){e.exports=require("https")},41:function(e,t){e.exports=require("buffer")},42:function(e,t,n){"use strict";const i=n(13);if("renderer"===process.type){if(i.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(53);e.require=t.remoteRequire,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,i)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,i)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(i.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(57);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},43:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(6),o=n(22),r=n(23),s=n(28),a=n(38);!function(e){let t;!function(e){e[e.Unknown=-1]="Unknown",e[e.Success=0]="Success",e[e.FunctionNotExist=1]="FunctionNotExist",e[e.ParamaterError=2]="ParamaterError",e[e.CallFailed=3]="CallFailed"}(t=e.NativeCallErrorCode||(e.NativeCallErrorCode={}));class n{constructor(e,t,n){this.maxId=e,this.minId=t,this.invalidId=n}generateId(){return this.minId===this.maxId?this.invalidId:this.minId++}isInvalidId(e){return e===this.invalidId}}e.IdGenerator=n;const l=0;e.idGenerator=new n(1e7,1,l);class d{constructor(){this.jsCallbacks=new Map,this.jsReturnCallbacks=new Map,this.eventJsCallbakcs=new Map,this.jsRegisterFunctions=new Map,this.targetCommunitorInfo=a.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfoByContext(s.CommonIPCBase.mainRendererContext),this.bindMsgListeners(),this.notifyNativeCallReady()}CallNativeFunction(t,...n){do{if(i.isNullOrUndefined(t)||0===t.length){o.error("funcName is empty");break}if(!this.targetCommunitorInfo){o.error("CallNativeFunction but targetCommunitorInfo null");break}o.information("funcName = ",t),this.printArgs(n);let s=l;for(let t=0;t<n.length;++t)if(i.isFunction(n[t])){let i=e.idGenerator.generateId(),o=n[t];t===n.length-1?(this.jsReturnCallbacks.set(i,o),s=i,n.pop()):(this.jsCallbacks.set(i,o),n[t]=i)}a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:r.CommonIPCMessage.msgNCCallNativeFunction,args:[t,s].concat(n)})}while(0)}AttachNativeEvent(t,n){let r=void 0;do{if(i.isNullOrUndefined(t)||0===t.length){o.error("eventName is empty");break}if(i.isNullOrUndefined(n)){o.error("callback is empty");break}let s=e.idGenerator.generateId();if(e.idGenerator.isInvalidId(s)){o.error("id error");break}if(this.eventJsCallbakcs.has(t))this.eventJsCallbakcs.get(t).set(s,n);else{let e=new Map;e.set(s,n),this.eventJsCallbakcs.set(t,e)}r=s}while(0);return r}DetachNativeEvent(e,t){do{if(i.isNullOrUndefined(e)||0===e.length){o.error("eventName is empty");break}if(i.isNullOrUndefined(t)){o.error("callback is empty");break}if(!this.eventJsCallbakcs.has(e)){o.error(`event: ${e} doesnot have listener`);break}if(!this.eventJsCallbakcs.get(e).has(t)){o.error(`event: ${e} doesnot have the listener of id=${t}`);break}this.eventJsCallbakcs.get(e).delete(t)}while(0)}CheckNativeFunction(t,n){do{if(i.isNullOrUndefined(t)||0===t.length){o.error("funcName is empty");break}if(i.isNullOrUndefined(n)){o.error("callback is empty");break}if(!this.targetCommunitorInfo){o.error("CheckNativeFunction but targetCommunitorInfo null");break}o.information("funcName = ",t);let s=e.idGenerator.generateId();this.jsReturnCallbacks.set(s,n),a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:r.CommonIPCMessage.msgNCCheckNativeFunction,args:[t,s]})}while(0)}RegisterJSFunction(e,n){let r=t.ParamaterError;do{if(i.isNullOrUndefined(e)||0===e.length){o.error("funcName is empty");break}if(i.isNullOrUndefined(n)){o.error("jsFunc is empty");break}this.jsRegisterFunctions.set(e,n),r=t.Success}while(0);return r}bindMsgListeners(){a.CommonIPCRenderer.rendererCommunicator.onMessage(r.CommonIPCMessage.msgNCCallJsFunctionById,e=>{this.handleCallJsFunctionById(e.msg.args)}),a.CommonIPCRenderer.rendererCommunicator.onMessage(r.CommonIPCMessage.msgNCCallJsFunctionByName,e=>{this.handleCallJsFunctionByName(e.msg.args)}),a.CommonIPCRenderer.rendererCommunicator.onMessage(r.CommonIPCMessage.msgNCNativeFireEvent,e=>{this.handleNativeFireEvent(e.msg.args)})}handleCallJsFunctionById(t){do{let n=t[0];if(!i.isNumber(n)){o.error(`id error id = ${n}`);break}if(e.idGenerator.isInvalidId(n)){o.error(`id = ${n} invalid`);break}let r=null,s=0;if(this.jsCallbacks.has(n)&&(s=1,r=this.jsCallbacks.get(n)),this.jsReturnCallbacks.has(n)&&(s=2,r=this.jsReturnCallbacks.get(n)),0===s){o.error(`callbacks[${n}] is null`);break}t.splice(0,1),r.apply(null,t),2===s&&this.jsReturnCallbacks.delete(n)}while(0)}handleCallJsFunctionByName(e){do{let t=e[0];if(!i.isString(t)){o.error(`funcName error funcName = ${t}`);break}if(!this.jsRegisterFunctions.has(t)){o.error(`jsRegisterFunctions[${t}] is null`);break}e.splice(0,1),this.jsRegisterFunctions.get(t).apply(null,e)}while(0)}handleNativeFireEvent(e){do{let t=e[0];if(!i.isString(t)){o.warning(`eventName error eventName = ${t}`);break}if(!this.eventJsCallbakcs.has(t)){o.warning(`eventJsCallbakcs[${t}] is null`);break}e.shift(),this.eventJsCallbakcs.get(t).forEach((t,n,r)=>{o.information(`value = ${t}, key = ${n}, map = ${r}`),i.isNullOrUndefined(t)||t.apply(null,e)})}while(0)}notifyNativeCallReady(){do{if(!this.targetCommunitorInfo){o.error("notifyNativeCallReady but targetCommunitorInfo null");break}a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:r.CommonIPCMessage.msgNCNativeCallReady,args:[a.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfo()]})}while(0)}printArgs(e){for(let t in e)o.information(`index ${t} = `,e[t])}}e.NativeCallImpl=d,e.nativeCall=new d}(t.NativeCallModule||(t.NativeCallModule={}))},44:function(e,t){e.exports=require("http")},47:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(51),o=n(12);t.Parser=class extends o.EventEmitter{constructor(){super(),this.decoder=new i.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,i=(t+=this.decoder.write(e)).indexOf("\n",n);for(;i>=0;){const e=t.slice(n,i),o=JSON.parse(e);this.emit("message",o),n=i+1,i=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},49:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(2),o=n(6),r=n(1),s=n(8),a=n(32),l=n(3),d=n(20),c=s.default(i.join(__rootDir,"../bin/ThunderHelper.node"));function u(){"console"===process.env.TL_OUTPUT?r.default.outputLogger=r.outputLoggerConsole:r.default.outputLogger=function(){function e(e){return function(...t){c.printEtwLog(e,function(...e){return e.map(e=>o.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[r.LogLevel.Critical]:e(r.LogLevel.Critical),[r.LogLevel.Error]:e(r.LogLevel.Error),[r.LogLevel.Warning]:e(r.LogLevel.Warning),[r.LogLevel.Information]:e(r.LogLevel.Information),[r.LogLevel.Verbose]:e(r.LogLevel.Verbose)}}()}function h(){let e=a.DevEnvHelperNS.isLogEnable();"1"===process.env.TL_ENABLE!==e&&(process.env.TL_ENABLE=e?"1":"0",r.default.enableLogger=e,c.enableETWLogger(e));let t=a.DevEnvHelperNS.getLogOutput();t&&t!==process.env.TL_OUTPUT&&(process.env.TL_OUTPUT=t,u())}process.env.TL_ENABLE="0",r.default.enableLogger="1"===process.env.TL_ENABLE,u(),h(),"browser"===process.type?l.ipcMain.on(d.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{l.BrowserWindow.getAllWindows().forEach(e=>{e.isDestroyed()||e.webContents.send(d.ThunderChannelList.channelMRUpdateLogEnviroment)}),h()}):"renderer"===process.type&&l.ipcRenderer.on(d.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{h()})},5:function(e,t,n){e.exports=n(9)(213)},50:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(33),o=n(12),r=n(30),s=n(47),a=n(29);t.Client=class extends o.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=a.getSockPath(e.socketPrex);this.socket=i.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new s.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{r.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){r.error(e.message)}else r.information("This socket has been ended by the other party",this.context&&this.context.name)}}},51:function(e,t){e.exports=require("string_decoder")},52:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(14),r=(n(21),n(2)),s=n(6);let a=null;const l=n(11),d=n(3),c=n(1),u=n(8),h=n(20),f=n(7),p="xdas_profile_stat";let m="",g=void 0,v=null,w=void 0,S=null,_=u.default(r.join(__rootDir,"../bin/ThunderHelper.node")),T=new Set;function b(){return i(this,void 0,void 0,function*(){return new Promise(e=>i(this,void 0,void 0,function*(){void 0===w&&(null===S&&(S=new Promise(e=>{e(w=function(e){let t="";if(0===e.length&&"renderer"===process.type){let e=r.normalize(decodeURIComponent(window.location.href)),n=e.indexOf(process.resourcesPath);n=n>-1?n+process.resourcesPath.length+1:n;let i=e.length-1,o=e.indexOf("?"),s=e.indexOf("#");i=o>-1?Math.min(o-1,i):i,i=s>-1?Math.min(s-1,i):i,n>-1&&i>=n&&(t=e.substr(n,i-n+1))}return 0===t.length&&(t=0!==e.length?e:process.type),t=t.replace(/\||,|;/g,"_")}(""))})),w=yield S),e(w)}))})}function y(e){let t="";do{if(null===e||void 0===e)break;switch(typeof e){case"string":t=e;break;case"object":t=s.inspect(e)||"";break;case"number":case"boolean":t=e.toString()||""}}while(0);return t}function k(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function C(e){return i(this,void 0,void 0,function*(){return new Promise(t=>i(this,void 0,void 0,function*(){let i=void 0;null===a&&(a=yield Promise.resolve().then(()=>n(39)));let o=a.createHash("md5");null!==o&&(i=o.update(e).digest("hex")),t(i)}))})}function R(){return new Promise(e=>i(this,void 0,void 0,function*(){let t="";t=void 0===g?"browser"===process.type?function(){if(void 0===g){let e=process.argv.length,t=process.argv;for(let n=0;n<e;n++){let e=t[n];if(e.startsWith("-StartType:")){g=e.substring("-StartType:".length);break}}void 0===g&&(g="")}return g}():yield function(){return i(this,void 0,void 0,function*(){return null===v&&(v=new Promise(e=>{d.ipcRenderer.send(h.ThunderChannelList.channelRMGetBrowserStartType),d.ipcRenderer.once(h.ThunderChannelList.channelMRGetBrowserStartTypeResult,(t,n)=>{g=n,e(n),v=null})})),v})}():g,e(t)}))}function E(e,t,n,o){return i(this,void 0,void 0,function*(){let r=y(t),s=y(n),a=yield C(s),d=function(e){let t=new RegExp(k("file:///"),"g"),n=new RegExp(k(process.resourcesPath+"\\"),"g"),i=new RegExp(k(encodeURI(process.resourcesPath.replace(/\\/g,"/")+"/")),"g");return e.replace(t,"").replace(n,"").replace(i,"")}(y(o)),c=yield C(d),u=`${e}:${a}:${c}`;T.has(u)||(T.add(u),l.XLStatNS.trackEvent(p,"uncaught_exception","",0,0,0,0,`type=${e},business_name=${yield b()},code=${r},message_hash=${a},message=${encodeURI(s)},stack_hash=${c},stack=${encodeURI(d)}`)),function(e,t,n,o){return i(this,void 0,void 0,function*(){})}().catch()})}function I(e){console.error(e);let t=e||{};E("unhandledRejection",t.code,t instanceof Error?t.message:t,t.stack).catch()}!function(e){e.init=function(e){m=e},e.trackColdStartUpEvent=function(e){return i(this,void 0,void 0,function*(){let t=_.iSColdStartUp()?1:0,n=o.release(),i=_.performanceMonitorReporter.getProcessUptime(),r=yield R(),s=`key=${e},start_type=${r},cold_start_up=${t},os_version=${n},cost_time=${i}`;l.XLStatNS.trackEvent(p,"cold_start_up","",0,0,0,0,s)})}}(t.PerformanceMonitorStatNS||(t.PerformanceMonitorStatNS={})),function(){return i(this,void 0,void 0,function*(){if(process.on("uncaughtException",e=>{console.error(e);let t=e||{};E("uncaughtException",t.code,t.message,t.stack).catch()}),"browser"===process.type)process.on("unhandledRejection",(e,t)=>{I(e)}),d.ipcMain.on(h.ThunderChannelList.channelRMGetBrowserStartType,function(e){return i(this,void 0,void 0,function*(){let t=yield R();e.sender.send(h.ThunderChannelList.channelMRGetBrowserStartTypeResult,t)})});else if("browser"!==process.type){window.addEventListener("unhandledrejection",e=>{I(e&&e.reason||{})});let e=yield f.asyncRemoteCall.getCurrentWebContents();null!==e&&void 0!==e&&e.once("did-finish-load",()=>{(function(){return i(this,void 0,void 0,function*(){do{if("browser"===process.type)break;if(null===window.performance.timing||void 0===window.performance.timing)break;let e=_.iSColdStartUp()?1:0,t=o.release(),n=window.performance.timing,i=n.loadEventEnd-n.navigationStart,r=n.fetchStart-n.navigationStart,s=n.domainLookupEnd-n.domainLookupStart,a=n.connectEnd-n.connectStart,d=n.responseStart-n.requestStart,c=n.responseEnd-n.responseStart,u=n.domComplete-n.domLoading,h=yield R();l.XLStatNS.trackEvent(p,"page_load_time","",0,0,0,0,`start_type=${h},cold_start_up=${e},os_version=${t},load_time=${i},before_fetch_time=${r},domin_lookup_time=${s},connect_time=${a},first_response_time=${d},responseTime=${c},domTime=${u},process=${m}`)}while(0)})})().catch()})}c.default.hook("beforeLog",(e,t,...n)=>{e===c.LogLevel.Critical&&l.XLStatNS.trackEvent(p,"critical_error","",0,0,0,0,`module_name=${t},messages=${n}`)})})}().catch()},53:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const i=n(3),o=n(41),r=n(54),s=n(55),a=n(35),l=n(56),d=n(13),c=n(36),u=i.ipcRenderer,h=process.electronBinding("v8_util"),f=new r.default,p=h.createIDWeakMap(),m=h.getHiddenValue(global,"contextId");class g{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function v(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let i=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:v(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(i))return{type:"buffer",value:o.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(c.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(h.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:h.getHiddenValue(e,"__remote_id__")};let i={type:e instanceof g?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)i.members.push({name:t,value:n(e[t])});return t.delete(e),i}if("function"==typeof e){return{type:"function",id:f.add(e),location:h.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function w(e,t,n){c.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function S(e,t,n,i=!1){const o=t=>{if(e.hasOwnProperty(t.name)&&!i)return;let n,o={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(p.has(t.value.refId)&&(n=p.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=y(t.value,e,t.name);o.get=(()=>n),o.set=(e=>n=e)}else"get"===t.type&&(o.get=(()=>n),t.writable&&(o.set=(e=>{n=e})),n=y(t.value));Object.defineProperty(e,t.name,o)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)o(n[t])}}function _(e,t,n){if(n){let t=y(n);Object.setPrototypeOf(e,t)}}function T(e,t){h.setHiddenValue(e,"__remote_id__",t)}function b(e){return h.getHiddenValue(e,"__remote_id__")}function y(e,t,n){const i={value:()=>e.value,array:()=>e.members.map(e=>y(e)),buffer:()=>o.Buffer.from(e.value),promise:()=>Promise.resolve({then:y(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:i,value:o}=e.members[n];t[i]=o}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let r;return e.type in i?r=i[e.type]():e.refId?p.has(e.refId)?(h.addRemoteObjectRef(m,e.refId),r=p.get(e.refId)):(d.warn("[metaToValue] refId point to null"+e.refId),r="function"===e.type?()=>{}:{}):e.id?p.has(e.id)?(h.addRemoteObjectRef(m,e.id),S(r=p.get(e.id),e.id,e.members,!0),_(r,e.id,e.proto)):(r="function"===e.type?t?function(e,t,n){if(p.has(n.id))return p.get(n.id);let i=b(e),o=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(o,"name",{value:t,writable:!1,enumerable:!0});let r=new Proxy(o,{apply:(e,n,o)=>new Promise((e,r)=>{let d=b(n);if(d||(d=b(n.__remoteObj_)),!d)throw Error("is this function was a bound function?");let c=a.default.browser.memberCall,h=l.default(c),f=v(o);u.send(c,m,h,d,i,t,f),s.default.add(h,t=>{try{w(y(t),e,r)}catch(e){r(e)}})}),construct:(e,n,o)=>new Promise((e,o)=>{let r=a.default.browser.memberConstruct,d=l.default(r);u.send(r,m,d,i,t,v(n)),s.default.add(d,t=>{try{let n=y(t);e(n)}catch(e){o(e)}})})});return h.setHiddenValue(o,"__remote_id__",n.id),r}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return T(n,t),new Proxy(n,{apply:(e,n,i)=>new Promise((e,o)=>{let r=a.default.browser.functionCall,d=l.default(r),c=b(n);u.send(r,m,d,c,t,v(i)),s.default.add(d,t=>{try{w(y(t),e,o)}catch(e){o(e)}})}),construct:(e,n,i)=>new Promise((e,i)=>{let o=a.default.browser.construct,r=l.default(o);u.send(o,m,r,t,v(n)),s.default.add(r,t=>{try{let n=y(t);e(n)}catch(e){i(e)}})})})}(e):{},h.setRemoteObjectFreer(r,m,e.id),p.set(e.id,r),h.setHiddenValue(r,"__remote_id__",e.id),h.addRemoteObjectRef(m,e.id),function(e){let t=b(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,i){if("function"==typeof i)throw new Error("set a function to a remote member is dangerous");return new Promise((o,r)=>{let d=a.default.browser.memberSet,c=l.default(d),h=v([i]);u.send(d,m,c,t,n,h),s.default.add(c,t=>{try{let s=y(t);e[n]=i,o(s)}catch(e){r(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((i,o)=>{let r=a.default.browser.memberGet,d=l.default(r);u.send(r,m,d,t,n),s.default.add(d,t=>{try{const r=y(t);e[n]=r,i(r)}catch(e){o(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let i=a.default.browser.sync,o=l.default(i);u.send(i,m,o,t),s.default.add(o,i=>{try{if(i.id!==t)throw Error("SYNC_RETURN: remote id not match");let o=y(i);e(o)}catch(e){n(e)}})})}}})}(r),S(r,e.id,e.members),_(r,e.id,e.proto),Object.defineProperty(r.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=r[e],i=r.__proto__;for(;i;){if(Object.prototype.hasOwnProperty.call(i,e)){delete i[e];break}i=i.__proto__}Object.defineProperty(r,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):d.error("no id of meta:",e),r}t.Interest=g;class k{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,i)=>{let o=this.getChannel(e),r=l.default(o);u.send(o,m,r,t),s.default.add(r,e=>{try{let t=y(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){i(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=a.default.browser.require;break;case"builtin":t=a.default.browser.builtIn;break;case"global":t=a.default.browser.global;break;case"current_window":t=a.default.browser.currentWindow;break;case"current_web_contents":t=a.default.browser.currentWebContents;break;case"client_web_contents":t=a.default.browser.clientWebContents;break;case"web_contents":t=a.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function C(e,t,n){try{s.default.invoke(t,n).remove(t)}finally{s.default.remove(t)}}u.on(a.default.renderer.requireReturn,C),u.on(a.default.renderer.getBuiltInReturn,C),u.on(a.default.renderer.getGlobalReturn,C),u.on(a.default.renderer.currentWindowReturn,C),u.on(a.default.renderer.currentWebContentsReturn,C),u.on(a.default.renderer.functionCallReturn,C),u.on(a.default.renderer.constructReturn,C),u.on(a.default.renderer.memberCallReturn,C),u.on(a.default.renderer.memberSetReturn,C),u.on(a.default.renderer.memberGetReturn,C),u.on(a.default.renderer.memberConstructReturn,C),u.on(a.default.renderer.callback,(e,t,n)=>{f.apply(t,y(n))}),u.on(a.default.renderer.syncReturn,C),u.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{d.info("[RELEASE_CALLBACK]: callbackId:",t),f.remove(t)}),process.on("exit",()=>{u.send(a.default.browser.contextRelease)});const R=["__resolve","__isResolved"],E=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],I=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function P(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(I(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(I(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(I(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(I(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(I(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(I(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,i)=>(I(e),Reflect.defineProperty(e.__remoteObj_,n,i)),get:(t,n,i)=>{if("string"==typeof n){if(String.prototype.includes.call(E,n)){return e[n]}if(String.prototype.includes.call(R,n)){return e[n]}}return I(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,i,o)=>(I(e),Reflect.set(e.__remoteObj_,n,i,o)),ownKeys:t=>(I(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,i)=>{I(e),Reflect.apply(e.__remoteObj_,n,i)},construct:(t,n,i)=>{if(I(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,i)=>{let o=a.default.browser.construct,r=l.default(o),d=h.getHiddenValue(e.__remoteObj_,"__remote_id__");u.send(o,m,r,d,v(n)),s.default.add(r,e=>{try{t(y(e))}catch(e){i(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=b(e);t&&T(n,t)}}),n}t.remoteRequire=function(e){return P(new k("module",e))},t.getBuiltin=function(e){return P(new k("builtin",e))},t.getGlobal=function(e){return P(new k("global",e))},t.getCurrentWindow=function(){return P(new k("current_window"))},t.getCurrentWebContents=function(){return P(new k("current_web_contents"))},t.getWebContents=function(){return P(new k("web_contents"))}},54:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=i.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,o=(new Error).stack;let r,s=n.exec(o);for(;null!==s;){const e=s[1];if(!e.includes("native")&&!e.includes("electron.asar")){r=/([^/^)]*)\)?$/gi.exec(e)[1];break}s=n.exec(o)}return this.callbacks[t]=e,i.setHiddenValue(e,"__remote_callback_id__",t),i.setHiddenValue(e,"__remote_call_location__",r),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(i.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}},55:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(13);var o;!function(e){let t={};e.add=function(e,n,i){t[e]={func:n,thisArg:i}},e.invoke=function(n,...o){let r=t[n];return r?r.thisArg?r.func.apply(r.thisArg,...o):r.func(...o):i.error(`Cannot invoke function by unrecognize id. ${n}`),e},e.remove=function(e){delete t[e]}}(o||(o={})),t.default=o},554:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(154),r=n(555),s=n(1).default.getLogger("Thunder.SuspensionRender.hovertext");t.SettingHttpSession=class extends o.HttpSession{querySetting(){return i(this,void 0,void 0,function*(){return this.host="images.client.vip.xunlei.com",this.retries=2,this.timeout=2e4,this.path="/mini/xlx/tips_300x310/ssi_html/xds_common.js",s.information("uri:"+this.path),s.information("host:"+this.host),new Promise(e=>{this.get(t=>{let n=null;s.information("response",n),200===t.statusCode&&t.body&&(n=r.SettingHttpPackageNS.praseQueryBuffer(t.body)),e(n)},t=>{s.information("error",t),e(null)})})})}}},555:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(1).default.getLogger("Thunder.SuspensionRender.hovertext");!function(e){e.praseQueryBuffer=function(e){if(!e)return null;let t=e.toString(),n=null;try{n=JSON.parse(t)}catch(e){}if(i.information("resObj",n),!n)return null;let o=n.hangball?n.hangball:null;i.information("touches",o);let r=null;if(o&&o.unlogin){let e=o.unlogin?o.unlogin:null;i.information("unlogin",e),r={unlogin:e,notvip:null}}if(o&&o.notvip){let e=o.notvip?o.notvip:null;i.information("notvip",e),r.notvip=e}return r}}(t.SettingHttpPackageNS||(t.SettingHttpPackageNS={}))},556:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(4);!function(e){const t="vip-download-webview";let n=void 0;e.getVipTaskInfo=function(e){return i(this,void 0,void 0,function*(){let n=null;try{n=(yield o.client.callRemoteClientFunction(t,"GetVipTaskInfo",e))[0]}catch(e){}return n})},e.getVipSubTaskInfo=function(e,n){return i(this,void 0,void 0,function*(){let i=null;try{i=(yield o.client.callRemoteClientFunction(t,"GetVipSubTaskInfo",e,n))[0]}catch(e){}return i})},e.getVipSpeedColor=function(e){return i(this,void 0,void 0,function*(){let n=void 0;try{n=(yield o.client.callRemoteClientFunction(t,"GetVipSpeedColor",e))[0]}catch(e){}return n})},e.clickTryAcclerateBtn=function(e,n){return i(this,void 0,void 0,function*(){try{yield o.client.callRemoteClientFunction(t,"ClickTryAcclerateBtn",e,n)}catch(e){}})},e.getThunderVersionNumber=function(){return i(this,void 0,void 0,function*(){if(void 0===n){const e=yield o.client.callServerFunction("GetThunderVersion");n=0;let t=e.split(".");if(t&&4===t.length){let e=Number(t[0]).valueOf(),i=Number(t[1]).valueOf(),o=Number(t[2]).valueOf();n=128*Math.pow(2,24)+e*Math.pow(2,16)+i*Math.pow(2,8)+o}}return n})}}(t.VipPluginFunctionNS||(t.VipPluginFunctionNS={}))},557:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(558),r=n(559),s=n(1).default.getLogger("Thunder.Suspension.bubble"),a=n(229).UserHelperNS.getUserHelper();t.BubbleHttpSession=class extends o.HttpSession{queryTip(){return i(this,void 0,void 0,function*(){s.information("querytip");let e=a.userId,t=yield a.getSessionId();this.host="touch.vip.xunlei.com",this.retries=2,this.timeout=2e4;let n=a.thunderVersionString,i=a.thunderVersionNumber,o=yield a.getPeerId();return this.path="/xl11/touch?iver="+i+"&vfilter="+n+"&deviceid="+o+"&scene=xl11_suspendedball",this.cookie="userid="+e+";sessionid="+t+";",s.information("uri:"+this.path),s.information("host:"+this.host),s.information("cookie:"+this.cookie),new Promise(t=>{this.get(n=>{let i=null;200===n.statusCode&&n.body&&(i=r.BubbleHttpPackageNS.praseQueryBuffer(e,n.body)),t(i)},e=>{s.information("error",e),t(null)})})})}reportTip(e,t){return i(this,void 0,void 0,function*(){s.information("reportTip");let n=a.userId,i=yield a.getSessionId();return this.host="touch.vip.xunlei.com",this.retries=2,this.timeout=2e4,this.path="/xl11/setreaded?msgid="+e+"&reachtype="+t,this.cookie="userid="+n+";sessionid="+i+";",s.information("uri:"+this.path),s.information("host:"+this.host),s.information("cookie:"+this.cookie),new Promise(e=>{this.get(t=>{let i=null;200===t.statusCode&&t.body&&(i=r.BubbleHttpPackageNS.praseReportBuffer(n,t.body)),e(i)},t=>{s.information("error",t),e(null)})})})}}},558:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(44);t.HttpSession=class{constructor(){this.mRetries=0,this.mHost=void 0,this.mPort=80,this.mPath=void 0,this.mAuth=void 0,this.mAccept=void 0,this.mBody=null,this.mUrl=void 0,this.mTimeout=void 0,this.mCurRetries=0,this.mCookie=void 0}get cookie(){return this.mCookie}set cookie(e){this.mCookie=e}set host(e){this.mHost=e}get host(){return this.mHost}set port(e){this.mPort=e}get port(){return this.mPort}set path(e){this.mPath=e}get path(){return this.mPath}set url(e){this.mUrl=e}get url(){return this.mUrl}set auth(e){this.mAuth=e}get auth(){return this.mAuth}set accept(e){this.mAccept=e}get accept(){return this.mAccept}set body(e){this.mBody=e}get body(){return this.mBody}set retries(e){this.mRetries=e}get retries(){return this.mRetries}set timeout(e){this.mTimeout=e}get timeout(){return this.mTimeout}post(e,t){let n=this.body;if(!n)return void t(null);let i=this.auth,o=this.accept,r={hostname:this.host,port:this.port,path:this.path||"/",method:"POST",auth:i||void 0,headers:{"Content-Length":n?n.length:0,Accept:o||"*/*"}};this.postImpl(n,r,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,this.post(e,t)):t(n)})}get(e,t){let n={hostname:this.host,port:this.port,path:this.path||"/",method:"GET",headers:{Cookie:this.cookie}};this.getImpl(n,e,n=>{this.mCurRetries<this.retries?(this.mCurRetries++,this.get(e,t)):t(n)})}postImpl(e,t,n,o){let r=i.request(t,e=>{let t=null;e.on("data",e=>{t=t?Buffer.concat([t,e]):e}),e.on("end",()=>{n({statusCode:e.statusCode,headers:e.headers,body:t})})});r.on("error",e=>{o(e)}),r.on("timeout",()=>{r.abort()}),this.timeout&&r.setTimeout(this.timeout),r.write(e),r.end()}getImpl(e,t,n){i.get(e,e=>{let n=null;e.on("data",e=>{n=n?Buffer.concat([n,e]):e}),e.on("end",()=>{t({statusCode:e.statusCode,headers:e.headers,body:n})})}).on("error",e=>{n(e)})}}},559:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(1).default.getLogger("Thunder.Suspension.bubble");!function(e){e.praseQueryBuffer=function(e,t){if(!t)return null;let n=t.toString(),o=null;try{o=JSON.parse(n)}catch(e){return null}if(i.information("jsonstr",n),i.information("resObj",o),!o)return null;let r=o.data?o.data.touches:null,s=null;if(r&&r.length>0){let e=r[0];s={result:o.result,msg:o.msg,data:e}}return s},e.praseReportBuffer=function(e,t){if(!t)return null;let n=t.toString();try{let e=JSON.parse(n);return{result:e.result,msg:e.msg}}catch(e){return null}}}(t.BubbleHttpPackageNS||(t.BubbleHttpPackageNS={}))},56:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let i=0;t.default=function(e){return e?e.concat(".").concat(String(++i)):String(++i)}},57:function(e,t,n){"use strict";const i=n(3),o=n(58),r=n(35),s=n(59),a=n(13),l=n(36),d=i.ipcMain,c=process.electronBinding("v8_util");let u=c.createDoubleIDWeakMap();const h=new o.default;function f(e,t,n,i,o,r){let s,l=!1,d=null,c=!1;do{try{s=t[i]}catch(e){l=!0}if(l)try{s=n.value[i],l=!1,n.meta.extendedMemberNames.push(i),c=!0}catch(e){a.error(`property ${i} untouchable, even try root[name]`)}if(l)break;let o=Object.getOwnPropertyDescriptor(t,i);if(void 0===o){a.warn(`descriptor of property ${i} is undefined`);break}d={name:i,enumerable:o.enumerable,writable:!1,type:"get"},void 0===o.get&&"function"==typeof s?d.type="method":((o.set||o.writable)&&(d.writable=!0),d.type="get"),c?(d.configurable=!0,d.value=g(e,s,r,!1,null)):d.value=g(e,s,r,!1,n)}while(0);return d}function p(e,t,n,i=null){let o=Object.getOwnPropertyNames(t);"function"==typeof t&&(o=o.filter(function(e){return!String.prototype.includes.call(s.propertiesOfFunction,e)}));let r=[];do{if(0===o.length)break;let s=o.length;for(let a=0;a<s;a++){let s=f(e,t,n,o[a],0,i);s&&r.push(s)}}while(0);return r}function m(e,t,n,i=null){let o=null,r=Object.getPrototypeOf(t);return o=null===r||r===Object.prototype||r===Function.prototype?null:g(e,r,i,!1,n)}function g(e,t,n=null,i=!1,o=null){n=null===n?{}:n;const r={type:typeof t};"object"===r.type&&(r.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":l.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&c.getHiddenValue(e,"simple")&&(n="value"),n}(t,i));do{if("object"===r.type||"function"===r.type){let i=h.getIdOfObject(t);if(i&&n[i]){r.refId=i,h.add(e,t);break}}"array"===r.type?r.members=t.map(t=>g(e,t,n)):"object"===r.type||"function"===r.type?(null==o&&(r.extendedMemberNames=[],o={value:t,meta:r}),r.name=t.constructor?t.constructor.name:"",r.id=h.add(e,t),n[r.id]=!0,r.members=p(e,t,o,n),r.proto=m(e,t,o,n)):"buffer"===r.type?r.value=Buffer.from(t):"promise"===r.type?(t.then(function(){},function(){}),r.then=g(e,function(e,n){t.then(e,n)})):"error"===r.type?(r.members=v(t),r.members.push({name:"name",value:t.name})):"date"===r.type?r.value=t.getTime():(r.type="value",r.value=t)}while(0);return r}function v(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function w(e,t,n,o){const s=function(o){let l,d,f=0,p=0;switch(o.type){case"value":return o.value;case"remote-object":return h.get(o.id);case"array":return w(e,t,n,o.value);case"buffer":return Buffer.from(o.value);case"date":return new Date(o.value);case"promise":return Promise.resolve({then:s(o.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:o.name}),f=0,p=(d=o.members).length;f<p;f++)e[(l=d[f]).name]=s(l.value);return e}case"function":{const s=e.id,l=[n,o.id];if(a.info("renderer function id:"+l),u.has(l))return u.get(l);let d=function(...t){a.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||s!==e.id?function(e,t,n){let o="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof i.webContents.constructor){const{sender:e}=t[0],i=e.eventNames().filter(t=>{let i=e.listeners(t),o=!1;return i.forEach(e=>{e===n&&(o=!0)}),o});i.length>0&&(o+=`\nRemote event names: ${i.join(", ")}`,i.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}a.warn(o)}(o,n,d):e.send(r.default.renderer.callback,o.id,g(e,n))};return Object.defineProperty(d,"length",{value:o.length}),c.setRemoteCallbackFreer(d,t,n,o.id,e),u.set(l,d),d}default:throw new TypeError(`Unknown type: ${o.type}`)}};return o.map(s)}function S(e,t,n,i){let o,r;try{return t.apply(n,i)}catch(e){return r=t.name,new Error(`Could not call remote function '${o=null!=r?r:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function _(e){return{type:"exception",message:e.message,stack:e.stack||e}}function T(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var b;!function(e){const t=(e,t,...n)=>{const i=e.sender;i.isDestroyed()?a.warn("webcontext is destroyed."):i.send(t,...n)};e.startServer=function(){d.on(r.default.browser.require,(e,n,i,o)=>{a.info(`[REQUIRE] module=${o} `);let s=process.mainModule.require(o),l=g(e.sender,s);t(e,r.default.renderer.requireReturn,i,l)}),d.on(r.default.browser.builtIn,(e,n,o,s)=>{a.info(`[BUILTIN]: property=${s} contextId=${n}`);let l=i[s],d=g(e.sender,l);a.info(`[BUILTIN]: returns remoteId:${d.id}, type: ${typeof l}`),t(e,r.default.renderer.getBuiltInReturn,o,d)}),d.on(r.default.browser.global,(e,n,i,o)=>{a.info(`[GLOBAL]: proerty:${o} contextId=${n}`);let s,l=global[o];s=g(e.sender,l),a.info(`[GLOBAL]: returns remoteid=${s.id}, obj=`+typeof l),t(e,r.default.renderer.getGlobalReturn,i,s)}),d.on(r.default.browser.currentWindow,(e,n,i,o)=>{a.info(`[CURRENT_WINDOW]: property=${o} contextId=${n}`);let s=e.sender.getOwnerBrowserWindow.call(e.sender),l=g(e.sender,s);a.info(`[CURRENT_WINDOW]: returns remoteid=${l.id}, obj=`+s),t(e,r.default.renderer.currentWindowReturn,i,l)}),d.on(r.default.browser.currentWebContents,(e,n,i,o)=>{t(e,r.default.renderer.currentWebContentsReturn,i,g(e.sender,e.sender))}),d.on(r.default.browser.webContents,(e,n,o,s)=>{a.info(`[WebContents]: proerty:${s} contextId=${n}`);let l,d=i.webContents;l=g(e.sender,d),a.info(`[WebContents]: returns remoteid=${l.id}, obj=`+typeof d),t(e,r.default.renderer.webContentsReturn,o,l)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(i=>{n?e.once(i,t[i]):e.on(i,t[i])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};d.on(r.default.browser.construct,(n,i,o,s,l)=>{let d,c=null;try{a.info(`[CONSTRUCTOR]: remoteId=${s} `);let u=l.length>0?l[l.length-1]:null;l=w(n.sender,n.frameId,i,l);let f=h.get(s);null==f&&T(`Cannot call constructor on missing remote object ${s}`),u&&"interest"===u.type&&(c=l.pop());let p=new(Function.prototype.bind.apply(f,[null,...l]));p&&c&&e(p,c),d=g(n.sender,p,null,!1),a.info(`[CONSTRUCTOR]: returns remoteId =${d.id} name=${f.name} `)}catch(e){d=_(e)}finally{t(n,r.default.renderer.constructReturn,o,d)}}),d.on(r.default.browser.functionCall,function(e,n,i,o,s,l){let d;try{a.info(`[FUNCTION_CALL]: remoteId=${s}`),l=w(e.sender,e.frameId,n,l);let c=h.get(s);if(null==c)a.error(`Cannot call function on missing remote object ${s}`),d=g(e.sender,void 0);else{let t=o?h.get(o):global;if(t){let n=S(0,c,t,l);d=g(e.sender,n)}else a.error(`Cannot call function(${s}) on missing context(${o})`),d=g(e.sender,void 0)}a.info(`[FUNCTION_CALL]: name=${c.name}`)}catch(e){d=_(e)}finally{t(e,r.default.renderer.functionCallReturn,i,d)}}),d.on(r.default.browser.memberCall,function(e,n,i,o,s,l,d){let c;a.info(`[MEMBER_CALL]: thisArg=${o}, remoteId=${s}, method=${l}, args count=${d.length}`);try{d=w(e.sender,e.frameId,n,d);let u=h.get(s);null==u&&T(`Cannot call function '${l}' on missing remote object ${s}`);let f=o?h.get(o):u;if(f){let t=S(0,u[l],f,d);c=g(e.sender,t),a.info("[MEMBER_CALL]: return="+t)}else c=g(e.sender,void 0)}catch(e){c=_(e)}finally{t(e,r.default.renderer.memberCallReturn,i,c)}}),d.on(r.default.browser.memberGet,function(e,n,i,o,s){let l;try{a.info(`[MEMBER_GET]: remoteId=${o}, property=`,s);let n=h.get(o);null==n&&T(`Cannot get property '${Object.toString.call(s)}' on missing remote object ${o}`);let d=n[s];l=g(e.sender,d)}catch(e){l=_(e)}finally{t(e,r.default.renderer.memberGetReturn,i,l)}}),d.on(r.default.browser.memberSet,function(e,n,i,o,s,l){try{a.info(`[MEMBER_SET]: remoteId=${o}, property=`+s),l=w(e.sender,e.frameId,n,l);let d=h.get(o);null==d&&T(`Cannot set property '${Object.toString.call(s)}' on missing remote object ${o}`),d[s]=l[0],t(e,r.default.renderer.memberSetReturn,i,{type:"value",value:!0})}catch(n){t(e,r.default.renderer.memberSetReturn,i,_(n))}}),d.on(r.default.browser.memberConstruct,function(n,i,o,s,l,d){let c,u=null;try{a.info(`[MEMBER_CONSTRUCTOR]: regId=${s}, method=${l}`);let f=d.length>0?d[d.length-1]:null;d=w(n.sender,n.frameId,i,d);let p=h.get(s);null==p&&T(`Cannot call constructor '${l}' on missing remote object ${s}`),f&&"interest"===f.type&&(u=d.pop());let m=p[l],v=new(Function.prototype.bind.apply(m,[null,...d]));v&&u&&e(v,u),c=g(n.sender,v)}catch(e){c=_(e)}finally{t(n,r.default.renderer.memberConstructReturn,o,c)}}),d.on(r.default.browser.sync,function(e,n,i,o){let s=h.get(o);t(e,r.default.renderer.syncReturn,i,g(e.sender,s))}),d.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=h.get(t);if(i.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let i=n.name;i||(i=n.constructor?n.constructor.name:""),h.remove(e.sender.id,t)}else t<0&&a.warn("remote id reference to nothing:",t)}),d.on(r.default.browser.contextRelease,e=>{h.clear(e.sender.id)})},e.getObjectRegistry=function(){return h}}(b||(b={})),e.exports=b},576:function(e,t,n){"use strict";var i=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{on:{mouseleave:this.leave}},[t("float-panel-container",{directives:[{name:"show",rawName:"v-show",value:this.showFloatPanel,expression:"showFloatPanel"}],attrs:{isShow:this.showFloatPanel,isLogined:this.isLogined,isVip:this.isVip},on:{"show-float-panel":this.showOrHideFloatPanel}})],1)},o=[];i._withStripped=!0,n.d(t,"a",function(){return i}),n.d(t,"b",function(){return o})},58:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(13),o=-1*Math.pow(2,31),r=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),i=e.id;let o=this.owners.get(i);return o||(o=new Set,this.owners.set(i,o),this.registerDeleteListener(e,i)),o.has(n)||(o.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return r.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=r.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=o)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),r.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(r.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),o=(r,s)=>{s===n&&(i.info("render-view-deleted: processid="+n),(()=>{i.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",o),this.clear(t))};e.on("render-view-deleted",o)}}},59:function(e,t,n){"use strict";var i;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(i||(i={})),e.exports=i},6:function(e,t){e.exports=require("util")},60:function(e,t){e.exports=require("readline")},61:function(e,t,n){e.exports=n(9)(216)},62:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(7),r=n(18),s=n(4),a=n(32);!function(e){function t(e,t){return i(this,void 0,void 0,function*(){if(null!==e){let n=e.webContents;(yield n.isDevToolsOpened())?yield n.closeDevTools():yield n.openDevTools(t)}})}e.openDevTool=t,e.enableDevTools=function(e){return i(this,void 0,void 0,function*(){window.addEventListener("keyup",n=>i(this,void 0,void 0,function*(){if("F12"===n.key&&n.ctrlKey)a.DevEnvHelperNS.isLogEnable()&&(yield t(yield o.asyncRemoteCall.getCurrentWindow(),e));else if(("t"===n.key||"T"===n.key)&&n.altKey&&a.DevEnvHelperNS.isLogEnable()){let e=document.getElementById("DevProcessPid");if(e)document.body.removeChild(e);else{(e=document.createElement("p")).id="DevProcessPid",e.style.position="absolute",e.style.left="0px",e.style.top="0px",e.style.width="100%",e.style.zIndex="10000",e.style.color="rgb(255,0,0)",document.body.appendChild(e);let t="process.pid:"+process.pid;t+="\r\nlocation.href:"+location.href,t+="\r\nprocess.argv:"+process.argv,e.innerText=t}}}),!0)})},e.enableDragOpenFile=function(e){void 0===e&&(e=!1),document.addEventListener("dragover",e=>(e.preventDefault(),e.stopPropagation(),!1),!0),document.addEventListener("drop",e=>i(this,void 0,void 0,function*(){e.preventDefault(),e.stopPropagation();let t=e.dataTransfer,n=t.files,i=t.items;if(void 0!==i&&null!==i&&i.length>0)for(let e=0;e<i.length;e++){let t=i[e];"string"===t.kind&&"text/uri-list"===t.type?t.getAsString(e=>{s.client.callServerFunction("DropOpenUrl",e).catch()}):t.kind}if(void 0!==n&&null!==n&&n.length>0)for(let e=0;e<n.length;e++){let t=n[e].path;void 0!==t&&null!==t&&""!==t&&(yield r.FileSystemAWNS.existsAW(t))&&s.client.callServerFunction("DropOpenFile",t).catch()}return!1}),!0)}}(t.ThunderToolsNS||(t.ThunderToolsNS={}))},63:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(7),r=n(1),s=n(16),a=r.default.getLogger("MenuSkinNS");!function(e){e.setStyle=function(e,t){return i(this,void 0,void 0,function*(){if(a.information("setStyle",e,t),null!==e){const{WindowPreferenceNS:e}=yield Promise.resolve().then(()=>n(73));let t={windowPreference:e.getWindowPreference()};a.information("skinOpts",t)}})},e.popEditableDefaultContextMenu=function(e,t,n){return i(this,void 0,void 0,function*(){let n=yield o.asyncRemoteCall.getCurrentWebContents();n.once("context-menu",(r,l)=>i(this,void 0,void 0,function*(){if(a.verbose(r),l.isEditable){let i=[{label:"撤销",enabled:l.editFlags.canUndo,click:()=>{n.undo()}},{type:"separator"},{label:"剪切",enabled:l.editFlags.canCut,click:()=>{n.cut()}},{label:"复制",enabled:l.editFlags.canCopy,click:()=>{n.copy()}},{label:"粘贴",enabled:l.editFlags.canPaste&&s.ThunderUtil.isClipboardTextFormatAvailable(),click:()=>{n.paste()}},{label:"删除",enabled:l.editFlags.canDelete,click:()=>{n.delete()}},{type:"separator"},{label:"全选",enabled:l.editFlags.canSelectAll,click:()=>{n.selectAll()}}];if(void 0!==e&&"function"==typeof e){let n=e(l);void 0!==n&&n.length>0&&(void 0===t?t=i.length:(t<0&&(t=i.length+1+t)<0&&(t=0),t>i.length&&(t=i.length)),i.splice(t,0,...n))}let r=yield(yield o.asyncRemoteCall.getMenu()).buildFromTemplate(i),a=yield o.asyncRemoteCall.getCurrentWindow();yield r.popup({window:a})}}))})}}(t.MenuSkinNS||(t.MenuSkinNS={}))},64:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.getTextScale=function(){return 1},e.fixWindowSize=function(e,t){return i(this,void 0,void 0,function*(){window.resizeTo(e,t)})},e.autoFixWindowSize=function(){},e.fixZoomFactory=function(){}}(t.FixTextScale||(t.FixTextScale={}))},652:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"xly-suspension-list"},[n("td-tabs",{attrs:{tabs:e.tabs,"active-key":e.activeKey},on:{"update:activeKey":function(t){e.activeKey=t}},scopedSlots:e._u([{key:"title",fn:function(t){var i=t.tab;return[n("div",{on:{click:function(t){e.manualChangeTab=!0}}},[e._v(e._s("download"===i.key?e.downloadingTitle:e.cloudTitle))])]}},{key:"default",fn:function(t){var i=t.tab;return[n("div",{directives:[{name:"show",rawName:"v-show",value:"download"===i.key,expression:"tab.key === 'download'"}]},[e.haveDownloadTask?n("ul",[e._l(e.localLoadedList,function(t){return n("task-item-container",{key:t,attrs:{taskId:t},nativeOn:{click:function(n){e.clickItem(t)}}})}),e._v(" "),n("li",{directives:[{name:"load",rawName:"v-load",value:{handler:e.localLoadMore,distance:"50px"},expression:"{ handler: localLoadMore, distance: `50px` }"}]})],2):n("div",{staticClass:"xly-suspension-list__empty"},[n("p",[e._v("暂无下载任务")])])]),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:"cloud"===i.key,expression:"tab.key === 'cloud'"}]},[n("cloud-download-container",{attrs:{isLogined:e.isLogined}})],1)]}}])},[n("a",{directives:[{name:"show",rawName:"v-show",value:this.blueTextShow,expression:"this.blueTextShow"}],attrs:{slot:"extra",href:"javascript:;"},on:{mouseover:function(t){e.onMouseOver(!1)},mouseleave:function(t){e.onMouseLeave(!1)},click:function(t){e.onTextClick("blue")}},slot:"extra"},[e._v(e._s(this.bluehoverText))]),e._v(" "),n("a",{directives:[{name:"show",rawName:"v-show",value:this.yellowTextShow,expression:"this.yellowTextShow"}],staticClass:" xly-suspension-list__link--vip",attrs:{slot:"extra",href:"javascript:;"},on:{mouseover:function(t){e.onMouseOver(!0)},mouseleave:function(t){e.onMouseLeave(!0)},click:function(t){e.onTextClick("yellow")}},slot:"extra"},[e._v(e._s(this.yellowhoverText))])]),e._v(" "),n("div",{staticClass:"xly-suspension-list__footer"},[n("td-button",{directives:[{name:"show",rawName:"v-show",value:e.footerVisible,expression:"footerVisible"}],staticClass:"td-button--other",on:{click:e.onClickShowFinishTask}},[e._v(e._s(e.footerText))])],1)],1)},o=[];i._withStripped=!0,n.d(t,"a",function(){return i}),n.d(t,"b",function(){return o})},673:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"xly-suspension-list__item"},[n("span",{staticClass:"xly-suspension-list__background",style:e.getTaskProgress}),e._v(" "),n("i",{staticClass:"xly-icon-type is-middle",class:e.getTaskIcon}),e._v(" "),n("p",{staticClass:"xly-suspension-list__name",attrs:{title:e.taskName}},[e._v(e._s(e.taskName))]),e._v(" "),n("p",{staticClass:"xly-suspension-list__status"},[e._v(e._s(e.stateName))])])},o=[];i._withStripped=!0,n.d(t,"a",function(){return i}),n.d(t,"b",function(){return o})},674:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.empty?n("div",{staticClass:"xly-suspension-list__empty"},[n("p",[e._v("暂无云盘下载任务")])]):n("ul",[e._l(e.cloudLoadedList,function(t){return n("task-item-container",{key:t,attrs:{taskId:t},nativeOn:{click:function(n){e.clickItem(t)}}})}),e._v(" "),n("li",{directives:[{name:"load",rawName:"v-load",value:{handler:e.cloudLoadMore,distance:"50px"},expression:"{ handler: cloudLoadMore, distance: `50px` }"}]})],2)},o=[];i._withStripped=!0,n.d(t,"a",function(){return i}),n.d(t,"b",function(){return o})},678:function(e,t,n){"use strict";n.r(t);var i=n(673),o=n(303);for(var r in o)"default"!==r&&function(e){n.d(t,e,function(){return o[e]})}(r);var s=n(0),a=Object(s.a)(o.default,i.a,i.b,!1,null,null,null);a.options.__file="src\\suspension-renderer\\views\\task-item.vue",t.default=a.exports},679:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(1).default.getLogger("Thunder.SuspensionRender.hovertext.manager"),r=n(554);!function(e){class t{constructor(){this.mIsLogined=!1,this.mIsVip=!1,this.mVipType=0,this.mIsDonwloading=!1}get isDonwloading(){return this.mIsDonwloading}set isDonwloading(e){this.mIsDonwloading=e}get vipType(){return this.mVipType}set vipType(e){this.mVipType=e}get isVip(){return this.mIsVip}set isVip(e){this.mIsVip=e}get isLogined(){return this.mIsLogined}set isLogined(e){this.mIsLogined=e}init(){this.querySettingText().catch()}getText(){let e=null;return this.mResponse&&!this.mIsVip&&(e=this.mIsLogined?this.mResponse.notvip:this.mResponse.unlogin),o.information("data",e),e}querySettingText(){return i(this,void 0,void 0,function*(){let e=new r.SettingHttpSession;this.mResponse=yield e.querySetting(),o.information("mResponse",this.mResponse)})}}e.HoverTextManager=t;let n=null;e.getHoverTextManager=function(){return n||(n=new t),n}}(t.HoverTextManagerNS||(t.HoverTextManagerNS={}))},7:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(1).default.getLogger("async-remote-call"),r=n(42),s=n(12),a=n(6);t.asyncRemoteCall=new class extends s.EventEmitter{constructor(){super(),this.mapObj=new Map,this.mapObjIniting=new Map,"renderer"!==process.type&&o.warning('can not import "renderer-process-call" module in non-renderer process',process.type)}getAppName(){return i(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}getAppVersion(){return i(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return i(this,void 0,void 0,function*(){return r.global.process.__resolve()})}getIpcMain(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getBrowserWindow(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getWebContents(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("webContents")})}getGlobalShortcut(){return i(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return i(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>i(this,void 0,void 0,function*(){this.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield r.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emit("OnInitCurrentWebContents",e),this.listeners("OnInitCurrentWebContents").forEach(e=>{this.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return i(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>i(this,void 0,void 0,function*(){this.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield r.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emit("OnInitCurrentWindow",e),this.listeners("OnInitCurrentWindow").forEach(e=>{this.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return i(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return a.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>i(this,void 0,void 0,function*(){this.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield r.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emit(e,t),this.listeners(e).forEach(t=>{this.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}},73:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(80),o=n(74);!function(e){e.getWindowPreference=function(e=!1){let t=o.default(),n={};return t&&t.colors&&"string"==typeof t.colors.colorPrimaryControl1&&(n.hoverBackgroundColor=e?parseInt(i.ColorUtilNS.rgbaStringToHexWith0xBegin(t.colors.colorPrimaryControl1),16):i.ColorUtilNS.rgbaStringToHexWith0xBegin(t.colors.colorPrimaryControl1)),n}}(t.WindowPreferenceNS||(t.WindowPreferenceNS={}))},738:function(e,t,n){n(49),e.exports=n(739)},739:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(38);n(4).client.start({name:"suspensionRenderer"}),i.CommonIPCRenderer.rendererCommunicator.initialize("suspensionRenderer"),i.CommonIPCRenderer.rendererCommunicator.connect();const o=n(52),r=n(31),s=n(740),a=n(177);n(61);const l=n(62),d=n(1).default.getLogger("SuspensionRenderer");o.PerformanceMonitorStatNS.init("suspension-renderer"),l.ThunderToolsNS.enableDevTools().catch(e=>{d.warning(e)}),r.default.use(a.default),l.ThunderToolsNS.enableDragOpenFile(),new r.default({components:{AppContainer:s.default},render:e=>e("app-container")}).$mount("#app")},74:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(3),r=n(1),s=n(4),a=r.default.getLogger("GetSkinInfo");let l;(function(){return i(this,void 0,void 0,function*(){"renderer"===process.type?(a.information("renderer process"),s.client.callServerFunction("GetSkinInfo").then(e=>{l=e,a.information("send OnChangeSkin",e)}).catch(e=>{a.warning(e)}),s.client.attachServerEvent("OnChangeSkin",(e,t)=>{l=t,a.information("send OnChangeSkin",t)})):"browser"===process.type&&(a.information("main process"),o.ipcMain.on("OnChangeSkin",(e,t)=>{a.information("OnChangeSkin",t),l=t}))})})().catch(e=>{a.information(e)}),t.default=function(){return l}},740:function(e,t,n){"use strict";n.r(t);var i=n(293);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var r=n(0),s=Object(r.a)(i.default,void 0,void 0,!1,null,null,null);s.options.__file="src\\suspension-renderer\\app-container.vue",t.default=s.exports},741:function(e,t,n){"use strict";n.r(t);var i=n(576),o=n(295);for(var r in o)"default"!==r&&function(e){n.d(t,e,function(){return o[e]})}(r);n(157),n(230),n(753),n(754);var s=n(0),a=Object(s.a)(o.default,i.a,i.b,!1,null,null,null);a.options.__file="src\\suspension-renderer\\app.vue",t.default=a.exports},742:function(e,t,n){"use strict";n.r(t);var i=n(297);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var r=n(0),s=Object(r.a)(i.default,void 0,void 0,!1,null,null,null);s.options.__file="src\\suspension-renderer\\views\\float-panel-container.vue",t.default=s.exports},743:function(e,t,n){"use strict";n.r(t);var i=n(652),o=n(299);for(var r in o)"default"!==r&&function(e){n.d(t,e,function(){return o[e]})}(r);var s=n(0),a=Object(s.a)(o.default,i.a,i.b,!1,null,null,null);a.options.__file="src\\suspension-renderer\\views\\float-panel.vue",t.default=a.exports},744:function(e,t,n){"use strict";n.r(t);var i=n(301);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var r=n(0),s=Object(r.a)(i.default,void 0,void 0,!1,null,null,null);s.options.__file="src\\suspension-renderer\\views\\task-item-container.vue",t.default=s.exports},745:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(176);function o(e){let t=e.loadedIndex+i.FloatPanelHelper.pageSize;t>e.taskIdLists.length&&(t=e.taskIdLists.length);let n=e.taskIdLists.slice(0,t),o={};for(let e of n)o[e]=i.FloatPanelHelper.noneReactiveTaskBaseInfos[e];e.taskBaseInfos=Object.assign({},o)}const r={INIT_TASKLIST(e,t){t.sort((e,t)=>t.createTime-e.createTime);let n={};e.taskIdLists=t.map(e=>(n[e.taskId]=e,e.taskId)),i.FloatPanelHelper.noneReactiveTaskBaseInfos=n,o(e)},INSERT_TASK(e,t){let n={},r=[];for(let i=0;i<t.length;++i){let o=t[i],s=o.taskId;s&&void 0===e.taskBaseInfos[s]&&(n[s]=o,r.push(s))}i.FloatPanelHelper.noneReactiveTaskBaseInfos=Object.assign({},i.FloatPanelHelper.noneReactiveTaskBaseInfos,n),e.taskIdLists.splice(0,0,...r),o(e)},REMOVE_TASK(e,t){let n=Object.assign([],e.taskIdLists);for(let e=0;e<t.length;++e)for(let o=0;o<n.length;++o)if(n[o]===t[e]){n.splice(o,1),delete i.FloatPanelHelper.noneReactiveTaskBaseInfos[t[e]];break}e.taskIdLists=n,o(e)},UPDATE_TASKSTATUS(e,t){for(let n in t){let o=t[n];if(i.FloatPanelHelper.noneReactiveTaskBaseInfos.hasOwnProperty(String(n))){i.FloatPanelHelper.noneReactiveTaskBaseInfos[n].taskStatus=o;let t=e.taskBaseInfos[n];void 0!==t&&(t.taskStatus=o)}}},UPDATE_TASKDETAIL(e,t){let n={};for(let o in t){let r=t[o];i.FloatPanelHelper.noneReactiveTaskBaseInfos.hasOwnProperty(String(o))&&(i.FloatPanelHelper.noneReactiveTaskBaseInfos[o]=r,void 0!==e.taskBaseInfos[o]&&(n[o]=r))}e.taskBaseInfos=Object.assign({},e.taskBaseInfos,n)},UPDATE_TASKBASE(e,t){i.FloatPanelHelper.noneReactiveTaskBaseInfos.hasOwnProperty(String(t.taskId))&&(i.FloatPanelHelper.noneReactiveTaskBaseInfos[t.taskId]=t,null!==e.taskBaseInfos[t.taskId]&&(e.taskBaseInfos[t.taskId]=t))},UPDATE_SUSPENSION(e,t){e.downloadStatus=t.downloadStatus,e.totalDownloadSpeed=t.totalDownloadSpeed,e.totalVipSpeed=t.totalVipSpeed,e.showStatus=t.showStatus,e.statusText=t.statusText},LOCAL_LOAD_MORE(e){e.loadedIndex+i.FloatPanelHelper.pageSize>e.taskIdLists.length?e.loadedIndex=e.taskIdLists.length:e.loadedIndex+=i.FloatPanelHelper.pageSize,o(e)}},s={localLoadedList:e=>{let t=e.loadedIndex+i.FloatPanelHelper.pageSize;return t>e.taskIdLists.length&&(t=e.taskIdLists.length),e.taskIdLists.slice(0,t)}};t.default={state:{taskBaseInfos:{},loadedIndex:0,taskIdLists:[],downloadStatus:!1,totalDownloadSpeed:0,totalVipSpeed:0,showStatus:!1,statusText:""},mutations:r,actions:{},getters:s}},746:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(1),o=n(176),r=i.default.getLogger("store cloud");function s(e){let t=e.loadedIndex+o.FloatPanelHelper.pageSize;t>e.cloudTaskIds.length&&(t=e.cloudTaskIds.length);let n=e.cloudTaskIds.slice(0,t),i={};for(let e of n)i[e]=o.FloatPanelHelper.cloudNoneReactiveTasksMap[e];e.cloudTasksMap=Object.assign({},i)}const a={INIT_TASKS(e,t){let n={},i=[];for(let e of t)n[e.taskId]=e,i.unshift(e.taskId);o.FloatPanelHelper.cloudNoneReactiveTasksMap=n,e.cloudTaskIds=i,s(e)},INSERT_TASKS(e,t){let n=[];for(let e of t)o.FloatPanelHelper.cloudNoneReactiveTasksMap[e.taskId]=e,n.unshift(e.taskId);e.cloudTaskIds.splice(0,0,...n),s(e),r.information(JSON.stringify(e.cloudTasksMap),JSON.stringify(e.cloudTaskIds))},REMOVE_TASKS(e,t){let n=[...e.cloudTaskIds];for(let e of t){delete o.FloatPanelHelper.cloudNoneReactiveTasksMap[e];let t=n.indexOf(e);-1!==t&&n.splice(t,1)}e.cloudTaskIds=n,s(e)},UPDATE_TASKS_DETAIL(e,t){for(let n in t)o.FloatPanelHelper.cloudNoneReactiveTasksMap.hasOwnProperty(String(n))&&(o.FloatPanelHelper.cloudNoneReactiveTasksMap[n]=t[n],e.cloudTasksMap.hasOwnProperty(String(n))&&(e.cloudTasksMap[n]=Object.assign({},t[n])))},UPDATE_TASKS_STATUS(e,t){r.information("update tasks status");for(let n in t)o.FloatPanelHelper.cloudNoneReactiveTasksMap.hasOwnProperty(String(n))&&(o.FloatPanelHelper.cloudNoneReactiveTasksMap[n].taskStatus=t[n],e.cloudTasksMap.hasOwnProperty(String(n))&&(e.cloudTasksMap[n].taskStatus=t[n]))},CLOUD_LOAD_MORE(e){e.loadedIndex+o.FloatPanelHelper.pageSize>e.cloudTaskIds.length?e.loadedIndex=e.cloudTaskIds.length:e.loadedIndex+=o.FloatPanelHelper.pageSize,s(e)},RESET_STORE(e){e.cloudTaskIds.splice(0,e.cloudTaskIds.length),e.cloudTasksMap={},o.FloatPanelHelper.cloudNoneReactiveTasksMap={}}},l={cloudLoadedList:e=>{let t=e.loadedIndex+o.FloatPanelHelper.pageSize;return t>e.cloudTaskIds.length&&(t=e.cloudTaskIds.length),e.cloudTaskIds.slice(0,t)}};t.default={state:{cloudTaskIds:[],loadedIndex:0,cloudTasksMap:{}},mutations:a,actions:{},getters:l}},747:function(e,t,n){"use strict";n.r(t);var i=n(305);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var r=n(0),s=Object(r.a)(i.default,void 0,void 0,!1,null,null,null);s.options.__file="src\\suspension-renderer\\views\\cloud-download\\index-container.vue",t.default=s.exports},748:function(e,t,n){"use strict";n.r(t);var i=n(674),o=n(307);for(var r in o)"default"!==r&&function(e){n.d(t,e,function(){return o[e]})}(r);var s=n(0),a=Object(s.a)(o.default,i.a,i.b,!1,null,null,null);a.options.__file="src\\suspension-renderer\\views\\cloud-download\\index.vue",t.default=a.exports},749:function(e,t,n){"use strict";n.r(t);var i=n(309);for(var o in i)"default"!==o&&function(e){n.d(t,e,function(){return i[e]})}(o);var r=n(0),s=Object(r.a)(i.default,void 0,void 0,!1,null,null,null);s.options.__file="src\\suspension-renderer\\views\\cloud-download\\task-item-container.vue",t.default=s.exports},750:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(2),r=n(7),s=n(4),a=n(63),l=n(20),d=n(176),c=n(8).default(o.join(__rootDir,"../bin/ThunderSuspensionWindow.node"));let u=0,h=!0,f=0;var p;!function(e){e[e.start=1]="start",e[e.pause=2]="pause"}(p||(p={})),function(e){function t(e){return i(this,void 0,void 0,function*(){f=e,yield s.client.callServerFunction("SetConfigValue","ConfigFloatPanel","FloatPanelValue",e.toString()),yield s.client.callServerFunction("SaveConfig")})}e.popupMenu=function(){return i(this,void 0,void 0,function*(){let e=[{type:"normal",label:"新建任务(&N)",icon:`${__rootDir}/static/icon/newtask.png`,click:()=>i(this,void 0,void 0,function*(){d.FloatPanelHelper.trackEvent("float_monitor_rk_click","button=create"),(yield d.FloatPanelHelper.getMainWindow()).webContents.send(l.ThunderChannelList.channelMRTrayMenuClick,"-task:newtask","floatmonitor")})},{type:"separator"},{type:"normal",label:"开始全部任务",enabled:h,icon:`${__rootDir}/static/icon/startalltask.png`,click:()=>i(this,void 0,void 0,function*(){d.FloatPanelHelper.trackEvent("float_monitor_rk_click","button=start");let e=new Date;(yield d.FloatPanelHelper.getMainWindow()).webContents.send(l.ThunderChannelList.channelMRTrayMenuClick,"-task:startall"),yield s.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcAllRetrieveTaskControl",p.start),yield s.client.callServerFunction("SetConfigValue","Consumption","ShowTipsTime",e.getTime())})},{type:"normal",label:"暂停全部任务",icon:`${__rootDir}/static/icon/stoptask.png`,click:()=>i(this,void 0,void 0,function*(){d.FloatPanelHelper.trackEvent("float_monitor_rk_click","button=pause"),(yield d.FloatPanelHelper.getMainWindow()).webContents.send(l.ThunderChannelList.channelMRTrayMenuClick,"-task:pauseall"),yield s.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcAllRetrieveTaskControl",p.pause)})},{type:"submenu",label:"悬浮窗设置",submenu:[{type:"normal",label:"显示悬浮窗",icon:0===f?`${__rootDir}/static/icon/ok.png`:"",click:()=>{d.FloatPanelHelper.trackEvent("float_monitor_display_setting","result=show"),t(0).catch()}},{type:"normal",icon:1===f?`${__rootDir}/static/icon/ok.png`:"",label:"下载时显示悬浮窗",click:()=>{d.FloatPanelHelper.trackEvent("float_monitor_display_setting","result=undl_hide"),t(1).catch()}},{type:"normal",label:"隐藏悬浮窗",icon:2===f?`${__rootDir}/static/icon/ok.png`:"",click:()=>{d.FloatPanelHelper.trackEvent("float_monitor_display_setting","result=hide"),t(2).catch()}}]},{type:"normal",label:"关闭悬浮窗",click:()=>{f=2,c.hideSuspensionWindow(),d.FloatPanelHelper.trackEvent("float_monitor_rk_click","button=close")}},{type:"normal",icon:`${__rootDir}/static/icon/exit.png`,label:"退出迅雷(&X)",click:()=>i(this,void 0,void 0,function*(){d.FloatPanelHelper.trackEvent("float_monitor_rk_click","button=exit"),(yield d.FloatPanelHelper.getMainWindow()).webContents.send(l.ThunderChannelList.channelMRTrayMenuClick,"-task:quitprocess")})}],n=yield s.client.callServerFunction("GetMainWindowStates");n.visible?n.minimized?e.splice(0,0,{label:"打开迅雷",type:"normal",click:()=>{d.FloatPanelHelper.trackEvent("float_monitor_rk_click","button=showmain"),s.client.callServerFunction("BringMainWndToTop").catch()}}):e.splice(0,0,{label:"隐藏主界面",type:"normal",click:()=>i(this,void 0,void 0,function*(){d.FloatPanelHelper.trackEvent("float_monitor_rk_click","button=hidemain"),(yield d.FloatPanelHelper.getMainWindow()).minimize(),(yield d.FloatPanelHelper.getMainWindow()).hide()})}):e.splice(0,0,{label:"打开迅雷",type:"normal",click:()=>{d.FloatPanelHelper.trackEvent("float_monitor_rk_click","button=showmain"),s.client.callServerFunction("BringMainWndToTop").catch()}});let o=yield(yield r.asyncRemoteCall.getMenu()).buildFromTemplate(e);yield a.MenuSkinNS.setStyle(o,{});let u=yield d.FloatPanelHelper.getFloatPanelWindow();o.popup({window:u})})},e.restoreSuspension=function(){f=0,c.showSuspensionWindow()},e.getShowFloatPanelType=function(){return f},e.setShowFloatPanelType=function(e){f=e},e.setShowSpeedType=function(e){(u=e)>1&&(u=0)},e.setCanStartAll=function(e){h=e}}(t.FloatPanelMenuHelper||(t.FloatPanelMenuHelper={}))},751:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(4),o=n(43),r=n(228),s=n(15),a=n(12),l=n(1).default.getLogger("Thunder.SuspensionRender.TaskManager");!function(e){e.eventIdGetTaskList="OnGetTaskList",e.eventIdTaskRemoved="OnTaskRemoved",e.eventIdTaskInserted="OnTaskInserted",e.eventIdTaskStatusChanged="OnTaskStatusChanged",e.eventIdTaskDetailChanged="OnTaskDetailChanged",e.eventIdBtSubFileLoaded="OnBtSubFileLoaded";class t extends a.EventEmitter{constructor(){super()}init(){l.information("init"),this.listenTasksEvent()}listenTasksEvent(){o.NativeCallModule.nativeCall.CallNativeFunction("IsLoadStorageTaskFinish",e=>{e===r.NativeFunctionErrorCode.Success&&this.getTaskListAfterLoadTaskFinish()})}getTaskListAfterLoadTaskFinish(){o.NativeCallModule.nativeCall.CallNativeFunction("GetCategoryViewTaskListForSuspensionWnd",s.DownloadKernel.CategroyViewID.Downloading,this.onGetTaskListCallback.bind(this))}onGetTaskListCallback(t,n){if(t===r.NativeFunctionErrorCode.Success){let t=null;if(n)try{t=JSON.parse(n)}catch(e){l.warning(e)}t&&this.emit(e.eventIdGetTaskList,t),i.client.attachServerEvent("OnTaskInserted",this.onTaskInsertedCallback.bind(this)),i.client.attachServerEvent("OnTaskRemoved",this.onTaskRemovedCallback.bind(this)),i.client.attachServerEvent("OnTaskStatusChanged",this.onTaskStatusChangedCallback.bind(this)),i.client.attachServerEvent("OnTaskDetailChanged",this.onTaskDetailChangedCallback.bind(this)),o.NativeCallModule.nativeCall.AttachNativeEvent("OnBtSubFileLoaded",this.onBtSubFileLoadedCallback.bind(this))}}onGetTaskBaseInfosCallback(t,n){if(t===r.NativeFunctionErrorCode.Success){let t=null;try{t=JSON.parse(n)}catch(e){l.warning(e)}null!==t&&this.emit(e.eventIdTaskInserted,t)}}onTaskInsertedCallback(e,t,n,i){-1===t&&n===s.DownloadKernel.CategroyViewID.Downloading&&i&&i.length>0&&o.NativeCallModule.nativeCall.CallNativeFunction("GetTaskBaseInfos",i,this.onGetTaskBaseInfosCallback.bind(this))}onTaskRemovedCallback(t,n,i,o){if(-1===n&&i===s.DownloadKernel.CategroyViewID.Downloading){let t=null;try{t=JSON.parse(o)}catch(e){l.warning(e)}t&&this.emit(e.eventIdTaskRemoved,t)}}onTaskStatusChangedCallback(t,n){{let t=null;try{t=JSON.parse(n)}catch(e){l.warning(e)}null!==t&&this.emit(e.eventIdTaskStatusChanged,t)}}onTaskDetailChangedCallback(t,n){{let t=null;try{t=JSON.parse(n)}catch(e){l.warning(e)}null!==t&&this.emit(e.eventIdTaskDetailChanged,t)}}onBtSubFileLoadedCallback(t,n,i){{let n=null;try{n=JSON.parse(i)}catch(e){l.warning(e)}n&&this.emit(e.eventIdBtSubFileLoaded,t,n)}}}e.TaskManager=t;let n=null;e.getTaskManager=function(){return n=new t}}(t.TaskManagerNS||(t.TaskManagerNS={}))},752:function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))(function(o,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function a(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}l((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(557),r=n(1).default.getLogger("Thunder.Suspension.bubble"),s=n(8),a=n(43),l=n(2),d=n(11),c=n(142),u=s.default(l.join(__rootDir,"../bin/ThunderSuspensionWindow.node")),h=n(229),f=n(4),p=h.UserHelperNS.getUserHelper();class m{constructor(){this.mIsBubbleVisible=!1,this.mIsFloatPanelShowed=!1,this.mIsClosed=!1,this.mCloseTimer=null}get isFloatPanelShowed(){return this.mIsFloatPanelShowed}set isFloatPanelShowed(e){this.mIsFloatPanelShowed=e}get isBubbleVisible(){return r.information("mIsBubbleVisible",this.mIsBubbleVisible),this.mIsBubbleVisible}init(){return i(this,void 0,void 0,function*(){{this.attachUserEvents(),p.loadThunderVersion();let e=yield this.isLogin();r.information("islogin",e),e&&(yield p.onLogin(),r.information("start timer 30s"),setTimeout(()=>i(this,void 0,void 0,function*(){this.isCanShowBubble()&&(yield this.queryTip())}),3e4))}})}closeBubble(e){this.mIsBubbleVisible=!1,u.closeBubbleAni(e)}showBubble(){return i(this,void 0,void 0,function*(){do{let e=yield this.isCanShowBubble();if(r.information("isCanShow",e),this.calculateExpireDate(),!this.mResponse||0!==this.mResponse.result||!e||this.mIsClosed)break;try{this.mTipContent=JSON.parse(this.mResponse.data.content),r.information("mTipContent",this.mTipContent),this.mIsBubbleVisible=!0;let e=this.mTipContent.text.split("{{day}}");if(r.information("textArray",e),e.length>1){u.setBubbleText(e[0]),u.setBubbleEndText(e[1]);let t=this.calculateExpireDate();r.information("expireDate",t),u.setBubbleRedText(t.toString())}else u.setBubbleText(this.mTipContent.text);u.setBubbleBtnText(this.mTipContent.buttonText),u.showBubbleAni(1),yield this.reportTip();let t=this.getCurrentDateStr();yield c.ConfigHelperNS.setValue("SuspensionBubble",p.userId+"_ShowDate",t),r.information("SuspensionBubble",p.userId+"_ShowDate",t),this.killTimer(),this.mCloseTimer=setTimeout(()=>i(this,void 0,void 0,function*(){this.mIsClosed||this.closeBubble(1)}),6e4)}catch(e){}}while(0)})}queryTip(){return i(this,void 0,void 0,function*(){{let e=new o.BubbleHttpSession;this.mResponse=yield e.queryTip(),r.information("mResponse :",this.mResponse),yield this.showBubble()}})}traceEvent(e,t){let n="";null!==t&&void 0!==t&&""!==t&&(n=t),r.information(e,n),d.XLStatNS.trackEvent("xlx_vip_event",e,"",0,0,0,0,n)}getCommonExtData(){return i(this,void 0,void 0,function*(){let e="";return e=(e=(e=e+",is_login="+((yield this.isLogin())?"1":"0"))+",is_vip="+(p.isVip?"1":"0"))+",vip_type="+p.vasType})}showTip(){return i(this,void 0,void 0,function*(){let e=yield this.getCommonExtData();this.traceEvent("xfq_xfmodel_show",e)})}closeTip(e){return i(this,void 0,void 0,function*(){let t=yield this.getCommonExtData();t=t+",button="+e,this.traceEvent("xfq_xfmodel_click",t)})}isCanShowBubble(){return i(this,void 0,void 0,function*(){let e=!0;do{let t=yield c.ConfigHelperNS.getValue("SuspensionBubble",p.userId+"_ShowDate","0");r.information("bubbleShowDate",t);let n=this.getCurrentDateStr();if(r.information("curDate",n),t===n){e=!1;break}let i=yield c.ConfigHelperNS.getValue("CenterTip",p.userId+"_ShowDate","0");if(r.information("centerTipShowDate",i),i===n){e=!1;break}let o=yield this.isLogin().then(),s=Number(p.userId)%10;if(r.information("lastUidNumber",s),r.information("islogin",o),!o||s>4){e=!1;break}if(this.mIsFloatPanelShowed){e=!1;break}}while(0);return e})}killTimer(){null!==this.mCloseTimer&&clearInterval(this.mCloseTimer)}isLogin(){return i(this,void 0,void 0,function*(){return yield f.client.callServerFunction("IsLogined")})}attachUserEvents(){a.NativeCallModule.nativeCall.AttachNativeEvent("onLoginSuc",()=>i(this,void 0,void 0,function*(){r.information("onLoginSuc,start timer 30s"),yield p.onLogin(),setTimeout(()=>i(this,void 0,void 0,function*(){this.isCanShowBubble()&&(yield this.queryTip())}),3e4)})),a.NativeCallModule.nativeCall.AttachNativeEvent("onLogout",()=>{p.onLogout(),this.killTimer(),this.closeBubble(0),this.mIsBubbleVisible=!1,this.mIsClosed=!1,this.mResponse=null,this.mTipContent=null}),a.NativeCallModule.nativeCall.AttachNativeEvent("onUserInfoChange",(e,t)=>i(this,void 0,void 0,function*(){yield this.onUserInfoChange(e,t)})),a.NativeCallModule.nativeCall.AttachNativeEvent("onLoginStatusChange",(e,t)=>{this.onLoginStatusChange(t)})}calculateExpireDate(){let e,t,n;e=p.expireDate.substring(0,4),t=p.expireDate.substring(4,6),n=p.expireDate.substring(6,8);let i=new Date(e+"-"+t+"-"+n+" 00:00:00").getTime()/1e3,o=new Date,r=o.getFullYear(),s=o.getMonth(),a=o.getDate(),l=(o=new Date(r+"-"+(s<10?"0"+(s+1):s+1)+"-"+(a<10?"0"+a:a)+" 00:00:00")).getTime()/1e3;return Math.ceil(Math.abs(i-l)/86400)}getCurrentDateStr(){let e=new Date,t=e.getFullYear(),n=e.getMonth(),i=e.getDate();return t.toString()+(n<10?"0"+(n+1):n+1)+(i<10?"0"+i:i)}reportTip(){return i(this,void 0,void 0,function*(){let e=yield(new o.BubbleHttpSession).reportTip(this.mResponse.data.msgid,this.mResponse.data.reach_type);r.information("ret",e),yield this.showTip()})}clickBubble(e){this.mIsBubbleVisible=!1,r.information("clickType",e),this.mIsClosed=!0;let t=null;if(1===e?t="xf":2===e?t="x":3===e?t="other_area":4===e&&(t="xfq",this.closeBubble(1)),this.closeTip(t).catch(),2!==e&&this.mTipContent&&this.mTipContent.url){let e=this.mTipContent.url;this.mTipContent.aid&&(e=e+"?aidfrom="+this.mTipContent.aid),this.mTipContent.refer&&(e=e+"&referfrom="+this.mTipContent.refer),r.information("url",e),f.client.callServerFunction("BringMainWndToTop").catch(),f.client.callServerFunction("OpenNewTab",e).catch()}}onUserInfoChange(e,t){return i(this,void 0,void 0,function*(){"vipinfo"===e&&(yield p.parseUserInfo())})}onLoginStatusChange(e){"offline"===e&&p.onLogout()}}t.BubbleManager=m,function(e){let t=null;e.getBubbleManager=function(){return t||(t=new m),t}}(t.BubbleManagerNS||(t.BubbleManagerNS={}))},753:function(e,t,n){"use strict";var i=n(1082);n.n(i).a},754:function(e,t,n){"use strict";var i=n(1084);n.n(i).a},8:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},80:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){function t(e){let t=e.toString(16).toUpperCase();return t.length<2&&(t="0"+t),t}function n(e,n,i,o){return"0x"+t(o)+t(e)+t(n)+t(i)}e.rgbaStringToHexWith0xBegin=function(e){if(void 0!==e){let t=e.split(",");return n(parseInt(t[0]||"0",10),parseInt(t[1]||"0",10),parseInt(t[2]||"0",10),parseInt(t[3]||"255",10))}},e.colorNumberToHex=t,e.rgbaToHexWith0xBegin=n}(t.ColorUtilNS||(t.ColorUtilNS={}))},84:function(e,t,n){e.exports=n(9)(215)},85:function(e,t,n){e.exports=n(9)(194)},9:function(e,t){e.exports=vendor_0aff229d1d3a2d2be355}});
//# sourceMappingURL=renderer.js.map