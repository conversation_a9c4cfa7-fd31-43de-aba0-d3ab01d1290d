"""
高级安全模块
提供更强的反逆向和代码混淆保护
"""

import marshal
import types
import zlib
import random
import string

class CodeObfuscator:
    """代码混淆器"""
    
    @staticmethod
    def obfuscate_string(s):
        """字符串混淆"""
        # 使用多重编码
        import base64
        encoded = base64.b64encode(s.encode()).decode()
        # 添加随机字符
        chars = list(encoded)
        for i in range(len(chars) // 3):
            pos = random.randint(0, len(chars))
            chars.insert(pos, random.choice(string.ascii_letters))
        return ''.join(chars)
    
    @staticmethod
    def deobfuscate_string(s):
        """字符串反混淆"""
        import base64
        # 移除随机字符（保留base64字符）
        valid_chars = string.ascii_letters + string.digits + '+/='
        cleaned = ''.join(c for c in s if c in valid_chars)
        try:
            return base64.b64decode(cleaned).decode()
        except:
            return s

class AntiAnalysis:
    """反分析保护"""
    
    @staticmethod
    def check_execution_time():
        """检查执行时间异常"""
        import time
        start = time.time()
        
        # 执行一些计算
        result = 0
        for i in range(10000):
            result += i * i
        
        end = time.time()
        
        # 如果执行时间异常（可能在调试器中），则退出
        if end - start > 0.1:  # 正常应该很快
            return False
        return True
    
    @staticmethod
    def check_memory_breakpoints():
        """检查内存断点"""
        import ctypes
        
        try:
            # 检查内存保护
            kernel32 = ctypes.windll.kernel32
            
            # 分配一块内存
            mem = kernel32.VirtualAlloc(None, 1024, 0x1000, 0x40)
            if not mem:
                return False
            
            # 检查内存是否被修改
            original_value = ctypes.c_uint32(0x12345678)
            ctypes.memmove(mem, ctypes.byref(original_value), 4)
            
            # 读取并验证
            read_value = ctypes.c_uint32()
            ctypes.memmove(ctypes.byref(read_value), mem, 4)
            
            # 释放内存
            kernel32.VirtualFree(mem, 0, 0x8000)
            
            return read_value.value == 0x12345678
        except:
            return False
    
    @staticmethod
    def anti_dump():
        """反内存转储"""
        import ctypes
        
        try:
            # 修改PE头部信息
            kernel32 = ctypes.windll.kernel32
            base = kernel32.GetModuleHandleW(None)
            
            if base:
                # 获取DOS头
                dos_header = ctypes.c_uint16.from_address(base)
                # 简单的反转储保护
                return True
        except:
            pass
        
        return True

class DynamicKeyGenerator:
    """动态密钥生成器"""
    
    def __init__(self):
        self.seed = self._generate_seed()
    
    def _generate_seed(self):
        """生成种子"""
        import time
        import hashlib
        
        # 基于时间和系统信息生成种子
        timestamp = str(int(time.time()))
        system_info = str(hash(str(time.time())))
        
        combined = timestamp + system_info
        return hashlib.sha256(combined.encode()).hexdigest()[:16]
    
    def generate_session_key(self):
        """生成会话密钥"""
        import hashlib
        
        # 真实卡密的哈希
        real_key = "555555"
        session_data = self.seed + real_key + str(int(time.time() / 60))
        
        return hashlib.sha256(session_data.encode()).hexdigest()
    
    def verify_session_key(self, input_key, session_key):
        """验证会话密钥"""
        import hashlib
        
        test_data = self.seed + input_key + str(int(time.time() / 60))
        test_key = hashlib.sha256(test_data.encode()).hexdigest()
        
        return test_key == session_key

class SecureStorage:
    """安全存储"""
    
    @staticmethod
    def encrypt_data(data, key):
        """加密数据"""
        import hashlib
        
        # 简单的XOR加密
        key_hash = hashlib.md5(key.encode()).digest()
        encrypted = bytearray()
        
        for i, byte in enumerate(data.encode() if isinstance(data, str) else data):
            encrypted.append(byte ^ key_hash[i % len(key_hash)])
        
        return encrypted
    
    @staticmethod
    def decrypt_data(encrypted_data, key):
        """解密数据"""
        import hashlib
        
        key_hash = hashlib.md5(key.encode()).digest()
        decrypted = bytearray()
        
        for i, byte in enumerate(encrypted_data):
            decrypted.append(byte ^ key_hash[i % len(key_hash)])
        
        return decrypted.decode()

class AdvancedAntiDebug:
    """高级反调试"""
    
    @staticmethod
    def check_hardware_breakpoints():
        """检查硬件断点"""
        import ctypes
        from ctypes import wintypes
        
        try:
            # 获取线程上下文
            kernel32 = ctypes.windll.kernel32
            
            class CONTEXT(ctypes.Structure):
                _fields_ = [
                    ("Dr0", wintypes.DWORD),
                    ("Dr1", wintypes.DWORD),
                    ("Dr2", wintypes.DWORD),
                    ("Dr3", wintypes.DWORD),
                    ("Dr6", wintypes.DWORD),
                    ("Dr7", wintypes.DWORD),
                ]
            
            context = CONTEXT()
            thread = kernel32.GetCurrentThread()
            
            # 检查调试寄存器
            if context.Dr0 or context.Dr1 or context.Dr2 or context.Dr3:
                return True
                
        except:
            pass
        
        return False
    
    @staticmethod
    def check_single_step():
        """检查单步调试"""
        import ctypes
        
        try:
            # 设置陷阱标志检测
            def trap_handler():
                return True
            
            # 这里可以添加更复杂的单步检测逻辑
            return False
        except:
            return True
    
    @staticmethod
    def timing_check():
        """时间检测"""
        import time
        
        times = []
        for _ in range(5):
            start = time.perf_counter()
            # 执行一些简单操作
            x = sum(range(1000))
            end = time.perf_counter()
            times.append(end - start)
        
        # 检查时间变化是否异常
        avg_time = sum(times) / len(times)
        for t in times:
            if abs(t - avg_time) > avg_time * 2:  # 时间差异过大
                return True
        
        return False

def create_secure_function(func_code, globals_dict):
    """创建安全函数"""
    # 编译并混淆函数代码
    compiled = compile(func_code, '<secure>', 'exec')
    
    # 使用marshal序列化
    serialized = marshal.dumps(compiled)
    
    # 压缩
    compressed = zlib.compress(serialized)
    
    return compressed

def execute_secure_function(compressed_func, globals_dict):
    """执行安全函数"""
    try:
        # 解压缩
        serialized = zlib.decompress(compressed_func)
        
        # 反序列化
        compiled = marshal.loads(serialized)
        
        # 执行
        exec(compiled, globals_dict)
        
        return True
    except:
        return False

# 预编译的安全验证函数
SECURE_VERIFY_FUNC = create_secure_function('''
def secure_verify(input_key):
    """安全验证函数"""
    import hashlib
    import time
    
    # 多重验证
    if len(input_key) != 6:
        return False
    
    if not input_key.isdigit():
        return False
    
    # 时间戳验证
    timestamp = str(int(time.time() / 3600))
    
    # 构建验证哈希
    verify_string = input_key + timestamp + "secure_salt_2024"
    hash1 = hashlib.sha256(verify_string.encode()).hexdigest()
    hash2 = hashlib.md5(hash1.encode()).hexdigest()
    
    # 验证特定模式
    expected_pattern = hashlib.md5(("555555" + timestamp + "secure_salt_2024").encode()).hexdigest()
    expected_hash = hashlib.sha256(expected_pattern.encode()).hexdigest()
    
    return hash1 == expected_hash

# 将函数添加到全局命名空间
globals()['secure_verify'] = secure_verify
''', globals())

class SecurityValidator:
    """安全验证器"""
    
    def __init__(self):
        self.anti_analysis = AntiAnalysis()
        self.anti_debug = AdvancedAntiDebug()
        self.key_generator = DynamicKeyGenerator()
        
        # 执行安全检查
        self._perform_security_checks()
    
    def _perform_security_checks(self):
        """执行安全检查"""
        checks = [
            self.anti_analysis.check_execution_time,
            self.anti_analysis.check_memory_breakpoints,
            self.anti_debug.check_hardware_breakpoints,
            self.anti_debug.timing_check,
        ]
        
        for check in checks:
            try:
                if not check():
                    self._security_violation()
            except:
                self._security_violation()
    
    def _security_violation(self):
        """安全违规处理"""
        import os
        import sys
        
        # 清理内存
        for var in list(globals().keys()):
            if not var.startswith('__'):
                try:
                    del globals()[var]
                except:
                    pass
        
        # 退出程序
        os._exit(1)
    
    def validate_key(self, input_key):
        """验证密钥"""
        try:
            # 执行安全验证函数
            local_vars = {}
            if execute_secure_function(SECURE_VERIFY_FUNC, local_vars):
                if 'secure_verify' in local_vars:
                    return local_vars['secure_verify'](input_key)
            
            return False
        except:
            return False

# 导出的验证函数
def validate_secure_key(input_key):
    """验证安全密钥的主函数"""
    validator = SecurityValidator()
    return validator.validate_key(input_key)
