extends Node

# 牌堆管理类
var cards = []

func _ready():
	initialize_deck()

func initialize_deck():
	cards.clear()

	# 加载Card脚本以访问枚举
	var CardScript = load("res://scripts/Card.gd")

	# 创建普通牌 (3-A, 每种花色各一张)
	for suit in range(4):  # 0-3 对应四种花色
		for rank in range(CardScript.Rank.THREE, CardScript.Rank.ACE + 1):  # 3-14 对应 3-A
			var card = create_card(suit, rank)
			cards.append(card)

	# 添加2 (每种花色各一张)
	for suit in range(4):
		var card = create_card(suit, CardScript.Rank.TWO)
		cards.append(card)

	# 添加大小王
	var small_joker = create_card(0, CardScript.Rank.SMALL_JOKER)
	var big_joker = create_card(0, CardScript.Rank.BIG_JOKER)
	cards.append(small_joker)
	cards.append(big_joker)

	print("牌堆创建完成，共 ", cards.size(), " 张牌")

func create_card(suit, rank):
	# 实例化Card场景
	var CardScene = load("res://scenes/Card.tscn")
	var card = CardScene.instantiate()
	card.suit = suit
	card.rank = rank
	return card

func shuffle():
	# 洗牌
	for i in range(cards.size()):
		var j = randi() % cards.size()
		var temp = cards[i]
		cards[i] = cards[j]
		cards[j] = temp
	print("洗牌完成")

func deal_cards(num_players: int) -> Array:
	# 发牌，返回每个玩家的手牌
	if cards.size() < 54:
		print("错误：牌数不足")
		return []
	
	var player_hands: Array = []
	for i in range(num_players):
		player_hands.append([])
	
	# 每人发17张牌
	var cards_per_player = 17
	var card_index = 0

	for deal_round in range(cards_per_player):
		for player in range(num_players):
			if card_index < cards.size():
				player_hands[player].append(cards[card_index])
				card_index += 1
	
	# 剩余3张作为底牌
	var bottom_cards = []
	while card_index < cards.size():
		bottom_cards.append(cards[card_index])
		card_index += 1
	
	print("发牌完成，每人 ", cards_per_player, " 张，底牌 ", bottom_cards.size(), " 张")
	return [player_hands, bottom_cards]

func get_remaining_cards():
	return cards

func is_empty() -> bool:
	return cards.is_empty()

func draw_card():
	if not is_empty():
		return cards.pop_back()
	return null

func add_card(card):
	cards.append(card)

func get_card_count() -> int:
	return cards.size()
