.td-browser-tab__text, .td-cover--message, .td-dialog, .td-dropdown-menu, .td-input__inner, .td-select--system, .td-select-group, .td-select__dropdown, .td-textarea__inner {
    box-sizing: border-box
}

.td-checkbox__inner:focus, .td-input__inner, .td-radio__inner:focus, .td-select, .td-select .td-select__inner:focus, a:focus, button:focus, div:focus, p:focus, span:focus {
    outline: 0
}

.td-browser-tab__text, .td-table-tree .td-tree-node__label {
    -webkit-line-clamp: 1;
    word-break: break-all;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical
}

.td-icon-load, .td-icon-loading {
    animation: td-load 1.5s linear infinite both
}

.td-context-menu__text, .td-tooltip, .td-tooltip__inner {
    white-space: nowrap
}

:root {
    --font-size-base: 12px;
    --color-default: #1a1a1a;
    --color-link: grey;
    --color-primary: #69f;
    --color-primary-auxiliary: #9bf;
    --color-primary-hover: #4b87ff;
    --color-secondary: #f0f3fa;
    --color-secondary-hover: #d7e3fa;
    --color-danger-1: #ff1a1a;
    --color-danger-2: #ff4d4d;
    --color-danger-3: #ff8080;
    --color-warn-1: #ff9c1a;
    --color-warn-2: #ffb24d;
    --color-warn-3: #ffc880;
    --color-success-1: #00b36a;
    --color-success-2: #27cf8a;
    --color-success-3: #40efa8;
    --color-primary-gray-1: #b3b3b3;
    --color-primary-gray-2: #ccc;
    --color-primary-gray-3: #f5f5f5;
    --button-default-color: #fff;
    --button-default-fill: var(--color-primary);
    --button-default-fill-hover: var(--color-primary-hover);
    --button-secondary-color: #1a1a1a;
    --button-secondary-fill: #f0f3fa;
    --button-secondary-fill-hover: #d7e3fa;
    --button-disable-color: var(--color-primary-gray-1);
    --button-disable-fill: var(--color-primary-gray-3);
    --dialog-width: 400px;
    --dialog-background: #fff;
    --dialog-radius: 4px;
    --dialog-padding-x: 30px;
    --dialog-padding-y: 30px;
    --dialog-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    --input-radius: 4px;
    --input-border-color: var(--color-primary-gray-2);
    --input-border-color-hover: var(--color-primary-gray-1);
    --input-padding: 8px;
    --input-width: 306px;
    --dropdown-radius: 4px;
    --dropdown-border-color: var(--color-primary-gray-2);
    --collapse-border-color: var(--color-primary-gray-3);
    --collapse-height-title: 30px;
    --progress-height: 2px;
    --draglist-padding-y: 7px;
    --draglist-padding-x: 14px;
    --draglist-icon-size: 32px;
    --draglist-color-hover: #f1f1ff;
    --draglist-color-active: #cedaff;
    --context-menu-height: 28px;
    --context-menu-padding: 32px;
    --browser-height: 28px;
    --browser-background: hsla(0, 0%, 100%, 0.5);
    --browser-background-current: #fff;
    --table-color-line: #e2ebf1;
    --table-head-background: #f9faff;
    --tree-background: #f0f3fa;
    --slider-button-hover: #d7e3fa;
    --slider-button-active: #80abff;
    --drag-border: rgba(102, 153, 255, 0.4);
    --drag-background: rgba(102, 153, 255, 0.2)
}

body, dd, dl, form, h1, h2, h3, h4, h5, h6, p {
    margin: 0
}

button, input, ol, ul {
    margin: 0;
    padding: 0
}

ol, ul {
    list-style: none
}

body, button, input, textarea {
    font-family: microsoft yahei, -apple-system, PingFang SC, simsun, Arial, sans-serif;
    font-size: var(--font-size-base);
    color: var(--color-default);
    -webkit-font-smoothing: antialiased
}

body {
    word-wrap: break-word
}

a {
    color: var(--color-link);
    text-decoration: none
}

button {
    cursor: pointer
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 400
}

h1 {
    font-size: 18px
}

h2 {
    font-size: 16px
}

h3 {
    font-size: 14px
}

h4, h5, h6, p {
    font-size: inherit
}

small, sub, sup {
    font-size: 11px
}

hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee
}

table {
    border-collapse: separate;
    border-spacing: 0
}

i {
    font-style: normal
}

.td-browser-tab__item--new, .td-browser-tab__item--normal:after, .td-browser-tab__item--normal:before {
    -webkit-mask-image: url(data:image/png;base64,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);
    -webkit-mask-size: 250px auto;
    background: var(--browser-background)
}

.td-browser-tab__item.is-current:after, .td-browser-tab__item.is-current:before {
    background: var(--browser-background-current)
}

.td-browser-tab {
    position: relative;
    display: flex;
    height: var(--browser-height);
    padding-right: 40px;
    line-height: var(--browser-height);
    text-align: center
}

.td-browser-tab__item {
    position: relative;
    display: flex;
    align-items: center
}

.td-browser-tab__item:hover .td-browser-tab__text {
    color: var(--color-default)
}

.td-browser-tab__item.is-hover .td-browser-tab__close, .td-browser-tab__item:hover .td-browser-tab__close {
    display: block
}

.td-browser-tab__item--normal {
    min-width: 78px;
    flex-basis: 354px;
    margin-left: -10px;
    margin-right: -13px;
    clip-path: polygon(5px 0, calc(100% - 13px) 0, 100% 100%, 19px 100%, 8px 3px)
}

.td-browser-tab__item--normal:after, .td-browser-tab__item--normal:before {
    width: 22px;
    height: var(--browser-height);
    content: ""
}

.td-browser-tab__item--normal:before {
    -webkit-mask-position: -50px 0
}

.td-browser-tab__item--normal:after {
    -webkit-mask-position: -30px 0
}

.td-browser-tab__item--first {
    margin-left: 0;
    clip-path: polygon(13px 0, calc(100% - 12px) 0, 100% 100%, 0 100%)
}

.td-browser-tab__item--first:before {
    -webkit-mask-position: 0 0
}

.td-browser-tab__item--new {
    width: 40px;
    flex-shrink: 0;
    margin-left: 10px;
    -webkit-mask-position: -100px 0;
    opacity: .7;
    clip-path: none
}

.td-browser-tab__item--new:hover {
    opacity: 1
}

.td-browser-tab__item.is-hidden, .td-carousel__indicators--image ul .td-carousel__indicator.is-active:after, .td-carousel__indicators--image ul .td-carousel__indicator:hover:after, .td-draglist-item.is-hidden, .td-table__text .td-icon-sequence {
    opacity: 0
}

.td-browser-tab__item.is-current {
    z-index: 3;
    clip-path: polygon(13px 0, calc(100% - 12px) 0, 100% 100%, 0 100%)
}

.td-browser-tab__item.is-current .td-browser-tab__text {
    color: var(--color-primary);
    background: var(--browser-background-current)
}

.td-browser-tab__item.is-current:before {
    -webkit-mask-position: 0 0
}

.td-browser-tab__item.is-pinned {
    min-width: 220px
}

.td-browser-tab__item.drag-list-move.is-transition {
    transition: transform .15s
}

.td-browser-tab__item--more {
    position: absolute;
    right: 7px;
    width: 26px;
    height: 15px;
    margin-top: 5px;
    line-height: 15px;
    justify-content: center;
    color: #fff;
    background: rgba(0, 0, 0, .1);
    border-radius: 2px;
    cursor: pointer;
    transition: background .2s
}

.td-browser-tab__item--more.is-checked, .td-browser-tab__item--more:hover {
    background: rgba(0, 0, 0, .2)
}

.td-browser-tab__text {
    display: -webkit-box;
    width: calc(100% - 45px);
    height: 100%;
    padding-right: 4px;
    color: grey;
    background: var(--browser-background);
    overflow: hidden;
    cursor: default
}

.td-browser-tab__close {
    display: none;
    position: absolute;
    top: 50%;
    right: 14px;
    z-index: 2;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    line-height: 16px;
    border-radius: 50%;
    cursor: default
}

.td-browser-tab__close:hover {
    background: rgba(0, 0, 0, .1)
}

.td-browser-tab__close:hover .td-icon-close {
    color: var(--color-default) !important
}

.td-browser-tab-group {
    display: flex;
    flex-basis: 338px
}

.td-carousel {
    position: relative
}

.td-carousel__container {
    position: relative;
    height: 260px
}

.td-carousel__item {
    position: absolute;
    width: 100%;
    height: 100%;
    transition: transform .2s
}

.td-carousel__item.is-active {
    z-index: 2
}

.td-carousel__item img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.td-carousel__button {
    position: absolute;
    top: 50%;
    z-index: 3;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 27px;
    height: 60px;
    margin-top: -30px;
    color: #fff;
    background: rgba(0, 0, 0, .2);
    transition: background .2s;
    cursor: pointer
}

.td-carousel__button:hover {
    background: rgba(0, 0, 0, .4)
}

.td-carousel__button .td-icon-arrow-left, .td-carousel__button .td-icon-arrow-right {
    font-size: 30px
}

.td-carousel__button--left {
    left: 0;
    border-radius: 0 4px 4px 0
}

.td-carousel__button--right {
    right: 0;
    border-radius: 4px 0 0 4px
}

.td-carousel__indicators {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 3;
    width: 100%
}

.td-carousel__indicators ul {
    display: flex;
    justify-content: center;
    align-items: center
}

.td-carousel__indicators--image {
    top: 0;
    right: 12px;
    left: auto;
    width: 70px;
    height: 100%
}

.td-carousel__indicators--image ul {
    flex-direction: column;
    height: 100%
}

.td-carousel__indicators--image ul .td-carousel__indicator {
    position: relative;
    display: block;
    height: 42px;
    margin: 2px 0;
    border-radius: 2px;
    border: 1px solid #a7a7a7
}

.td-carousel__indicators--image ul .td-carousel__indicator a {
    display: block;
    height: 100%
}

.td-carousel__indicators--image ul .td-carousel__indicator a img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.td-carousel__indicators--image ul .td-carousel__indicator:after {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .5);
    pointer-events: none;
    transition: opacity .2s
}

.td-carousel__indicators--image ul .td-carousel__indicator.is-active {
    border-color: #fff
}

.td-carousel__indicator {
    height: 54px;
    display: flex;
    align-items: center;
    margin: 0 8px;
    cursor: pointer
}

.td-carousel__indicator:after {
    display: block;
    width: 38px;
    height: 2px;
    background: hsla(0, 0%, 100%, .5);
    border-radius: 1px;
    content: "";
    transition: background .2s
}

.td-carousel__indicator:hover:after {
    background: hsla(0, 0%, 100%, .8)
}

.td-carousel__indicator.is-active:after {
    background: #fff
}

.td-cover {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999;
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center
}

.td-dialog {
    position: relative;
    width: var(--dialog-width);
    padding: 0 var(--dialog-padding-x) var(--dialog-padding-y);
    background: var(--dialog-background);
    border-radius: var(--dialog-radius);
    box-shadow: var(--dialog-shadow)
}

.td-dialog__header h2 {
    padding: 20px 0;
    font-size: 15px;
    text-align: center
}

.td-dialog__close {
    position: absolute;
    display: flex;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    justify-content: center;
    align-items: center;
    border-top-right-radius: var(--dialog-radius)
}

.td-dialog__close:hover {
    background-color: var(--color-danger-3)
}

.td-dialog__close:hover .td-icon-close {
    color: #fff
}

.td-dialog__header {
    min-height: 30px
}

.td-dialog__footer {
    margin-top: 20px;
    position: relative
}

.td-dialog__footer .td-button {
    width: 100%
}

.td-dialog__more {
    margin-top: 30px
}

.td-dialog--fullscreen {
    width: 100%;
    height: 100%
}

.td-dialog-comfirm {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    min-height: 60px
}

.td-dialog-comfirm__icon .td-icon-warning {
    color: var(--color-warn-1);
    font-size: 32px
}

.td-dialog-comfirm__icon .td-icon-question {
    color: var(--color-primary);
    font-size: 32px
}

.td-dialog-comfirm__icon .td-icon-error {
    color: var(--color-danger-2);
    font-size: 32px
}

.td-dialog-comfirm__icon .td-icon-success {
    color: var(--color-success-2);
    font-size: 32px
}

.td-dialog-comfirm__content {
    flex: 1;
    margin-left: 15px;
    overflow: hidden
}

.td-dialog-comfirm__title {
    font-size: 16px;
    word-wrap: break-word
}

.td-dialog-comfirm__text {
    margin-top: 10px
}

.td-dialog-footer {
    display: flex;
    justify-content: space-between
}

.td-dialog-footer .td-button:nth-child(2) {
    margin-left: 12px
}

.td-more-arrow {
    position: absolute;
    bottom: -25px;
    width: 100%;
    text-align: center;
    color: var(--color-primary-gray-2)
}

.td-more-arrow--down .td-icon-arrow-drop {
    display: inline-block;
    transform: rotateX(180deg)
}

.td-dropdown {
    width: 100%;
    position: relative;
    display: inline-flex
}

.td-dropdown:hover .td-dropdown__text .td-icon-arrow-drop, .td-dropdown:hover .td-dropdown__text span {
    color: var(--color-primary)
}

.td-dropdown__text span {
    color: var(--color-default)
}

.td-dropdown__text .td-icon-arrow-drop {
    color: var(--color-primary-gray-1)
}

.td-dropdown.is-disabled .td-button {
    color: var(--button-disable-color) !important;
    background: var(--button-disable-fill) !important;
    cursor: not-allowed
}

.td-dropdown-group .td-button--large:last-child .td-icon, .td-dropdown-group .td-button--small:last-child .td-icon, .td-dropdown-group .td-button:last-child .td-icon {
    color: #fff
}

.td-dropdown-group {
    width: 100%;
    display: flex
}

.td-dropdown-group .td-button:first-child {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.td-dropdown-group .td-button:last-child {
    position: relative;
    width: 30px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin: 0
}

.td-dropdown-group .td-button:last-child:before {
    position: absolute;
    top: 30%;
    left: 0;
    width: 1px;
    height: 40%;
    background: #fff;
    content: ""
}

.td-dropdown-group .td-button--small:last-child {
    position: relative;
    width: 22px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin: 0
}

.td-dropdown-group .td-button--small:last-child .td-icon-arrow-drop {
    font-size: 13px
}

.td-dropdown-group .td-button--small:last-child:before {
    position: absolute;
    top: 30%;
    left: 0;
    width: 1px;
    height: 40%;
    background: #fff;
    content: ""
}

.td-dropdown-group .td-button--large:last-child {
    position: relative;
    width: 36px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin: 0
}

.td-dropdown-group .td-button--large:last-child:before {
    position: absolute;
    top: 30%;
    left: 0;
    width: 1px;
    height: 40%;
    background: #fff;
    content: ""
}

.td-dropdown-menu {
    position: absolute;
    top: 34px;
    z-index: 99;
    width: 100%;
    padding: 8px 0;
    background-color: #fff;
    border: 1px solid var(--dropdown-border-color);
    border-radius: var(--dropdown-radius)
}

.td-dropdown-menu ul {
    max-height: 150px;
    overflow-y: auto
}

.td-dropdown-menu ul::-webkit-scrollbar {
    width: 8px;
    background: 0 0
}

.td-dropdown-menu ul::-webkit-scrollbar:horizontal {
    height: 8px;
    border-bottom: 2px solid #fff
}

.td-dropdown-menu ul::-webkit-scrollbar-thumb {
    border-radius: 4px;
    width: 8px;
    background: rgba(0, 0, 0, .15);
    border-right: 2px solid #fff
}

.td-dropdown-menu ul::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, .3)
}

.td-dropdown-menu__item {
    display: block;
    padding: 7px 0 7px 8px;
    cursor: pointer
}

.td-dropdown-menu__item:hover {
    background-color: var(--button-secondary-fill)
}

.td-dropdown-menu__item.is-disabled {
    color: #ccc;
    cursor: default
}

.td-collapse-item.is-active .td-collapse-item__title, .td-collapse-item__title:hover {
    color: var(--color-primary)
}

.td-dropdown-menu__item.is-disabled:hover {
    background: unset
}

.td-dropdown-menu__group {
    position: relative;
    padding: 4px 0
}

.td-dropdown-menu__group:not(:last-of-type):after {
    position: absolute;
    bottom: 0;
    right: 0;
    width: calc(100% - 10px);
    height: 1px;
    background: #e6e6e6;
    content: ""
}

.td-dropdown-menu--large {
    top: 44px
}

.td-dropdown-menu--large ul {
    max-height: 178px
}

.td-dropdown-menu--large .td-dropdown-menu__item {
    padding-top: 10px;
    padding-bottom: 10px
}

.td-dropdown-menu--small {
    top: 26px
}

.td-dropdown-menu--small ul {
    max-height: 96px
}

.td-dropdown-menu--small .td-dropdown-menu__item {
    padding-top: 4px;
    padding-bottom: 4px
}

.td-drag-area {
    position: absolute;
    z-index: 998;
    background: var(--drag-background);
    border: 1px solid var(--drag-border)
}

.td-message, .td-tabs__title {
    position: relative;
    display: flex
}

.td-collapse-item {
    margin-top: -1px;
    border-top: 1px solid var(--collapse-border-color);
    border-bottom: 1px solid var(--collapse-border-color)
}

.td-collapse-item__title {
    font-size: 13px;
    line-height: var(--collapse-height-title);
    transition: .2s;
    cursor: pointer
}

.td-collapse-item__content {
    height: 0;
    overflow: hidden;
    transition: .2s
}

.td-collapse-item.is-active .td-collapse-item__content {
    padding: 12px 0;
    height: auto
}

.td-media {
    display: flex
}

.td-media__object {
    width: 32px;
    height: 32px;
    margin: 3px 0
}

.td-media__content {
    width: calc(100% - 46px);
    margin-left: 14px
}

.td-media.is-middle {
    align-items: center
}

.td-media.is-bottom {
    align-items: flex-end
}

.td-message {
    align-items: center;
    height: 40px;
    padding: 0 20px;
    margin: 5px 0;
    line-height: 40px;
    color: hsla(0, 0%, 100%, .7);
    border-radius: 4px;
    pointer-events: visible
}

.td-message a, .td-message i {
    color: #fff
}

.td-tabs__item.is-active, .td-tabs__item:hover {
    color: var(--color-primary)
}

.td-message a:hover {
    text-decoration: underline
}

.td-message--success {
    background-color: #00b36a
}

.td-message--error {
    background-color: #ff1a1a
}

.td-message--warning {
    background-color: #ff9c1a
}

.td-message--normal {
    background-color: rgba(102, 153, 255, .9)
}

.td-message__text {
    margin-left: 8px
}

.td-cover--message {
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 30px;
    pointer-events: none;
    z-index: 1000
}

.td-cover.is-middle {
    padding-top: 0;
    justify-content: center
}

.td-tabs__title {
    border-bottom: 1px solid #e4e9ef
}

.td-tabs__nav {
    display: flex
}

.td-tabs__item {
    width: 102px;
    height: 26px;
    line-height: 26px;
    cursor: pointer;
    text-align: center
}

.td-tabs__item.is-active {
    border-bottom: 2px solid
}

.td-tabs__extra {
    position: absolute;
    top: 0;
    right: 0;
    line-height: 26px
}

.td-tabs__content {
    position: relative;
    padding-top: 10px;
    overflow: hidden
}

.td-combo label {
    position: relative;
    display: block
}

.td-combo [class*=__inner] {
    margin-bottom: -1px;
    border-radius: 0
}

.td-combo [class*=is-] {
    z-index: 2
}

.td-combo > :first-child .td-select-group, .td-combo > :first-child [class*=__inner] {
    border-top-left-radius: var(--input-radius);
    border-top-right-radius: var(--input-radius)
}

.td-combo > :last-child .td-select-group, .td-combo > :last-child [class*=__inner] {
    border-bottom-left-radius: var(--input-radius);
    border-bottom-right-radius: var(--input-radius)
}

.td-combo .td-select {
    margin-bottom: -1px
}

.td-combo .td-select-group {
    border-radius: 0
}

.td-combo .td-input {
    display: block
}

.td-combo .td-input__inner {
    width: 100%;
    position: relative;
    border-radius: 0
}

.td-combo .td-input__inner:focus {
    z-index: 3
}

.td-radio {
    display: inline-flex;
    align-items: center
}

.td-radio__inner {
    position: relative;
    width: 12px;
    height: 12px;
    border: 1px solid #b3b3b3;
    border-radius: 50%;
    -webkit-appearance: none
}

.td-radio__inner:checked, .td-radio__inner:hover {
    border-color: var(--color-primary)
}

.td-radio__inner:checked:after {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    margin: -3px 0 0 -3px;
    background-color: var(--color-primary);
    border-radius: 50%;
    content: ""
}

.td-radio__label {
    margin-left: 6px
}

.td-radio.is-disabled .td-radio__inner {
    background-color: var(--color-primary-gray-3);
    border-color: var(--color-primary-gray-2)
}

.td-radio.is-disabled .td-radio__inner:checked:after {
    background-color: var(--color-primary-gray-2)
}

.td-radio.is-disabled .td-radio__label {
    color: var(--color-primary-gray-2)
}

.td-input {
    display: inline-block
}

.td-input__inner {
    height: 30px;
    padding: 6px var(--input-padding);
    border: 1px solid var(--input-border-color);
    border-radius: var(--input-radius);
    transition: border .2s
}

.td-input__inner:hover {
    border-color: var(--input-border-color-hover)
}

.td-input__inner:focus {
    border-color: var(--color-primary)
}

.td-input__inner::-webkit-input-placeholder {
    color: var(--color-primary-gray-2)
}

.td-input__inner:disabled {
    background: var(--color-primary-gray-3);
    cursor: not-allowed
}

.td-input--small .td-input__inner {
    height: 22px
}

.td-input--large .td-input__inner {
    height: 36px
}

.td-input__prefix, .td-input__suffix {
    width: 28px;
    height: 100%;
    color: var(--color-primary-gray-2);
    display: flex
}

.td-input--prefix {
    position: relative
}

.td-input--prefix .td-input__inner {
    padding: 0 10px 0 28px
}

.td-input__prefix {
    position: absolute;
    left: 1px;
    justify-content: center;
    align-items: center
}

.td-input--suffix {
    position: relative
}

.td-input--suffix .td-input__inner {
    padding: 0 28px 0 10px
}

.td-input__suffix {
    position: absolute;
    right: 1px;
    justify-content: center;
    align-items: center
}

.td-input.is-warn .td-input__inner {
    color: var(--color-default) !important;
    border-color: var(--color-danger-2) !important
}

.td-input.is-disabled {
    color: var(--color-primary-gray-3);
    cursor: not-allowed
}

.td-input.is-disabled .td-input__label {
    color: var(--color-primary-gray-2)
}

.td-input.is-disabled .td-input__inner::-webkit-input-placeholder {
    color: var(--color-primary-gray-1)
}

.td-input-group {
    display: flex;
    align-items: center;
    height: 30px;
    border: 1px solid var(--input-border-color);
    border-radius: var(--input-radius)
}

.td-input-group .td-input__button {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 7px
}

.td-input-group .td-input__button.is-disabled {
    cursor: default;
    color: var(--color-primary-gray-2)
}

.td-input-group__prepend {
    display: flex;
    align-items: center;
    height: 100%
}

.td-input-group .td-input {
    flex: 1;
    position: relative
}

.td-input-group .td-input__inner {
    width: 100%;
    border: 0;
    background: 0 0
}

.td-input-number__down:after, .td-input-number__up:after {
    background: var(--input-border-color);
    content: ""
}

.td-input-group .td-dropdown-menu {
    top: 32px
}

.td-input-number {
    position: relative;
    width: 110px
}

.td-input-number__down {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 28px;
    height: 100%;
    cursor: pointer
}

.td-input-number__down.is-disabled {
    color: var(--color-primary-gray-2)
}

.td-input-number__down:hover, .td-input-number__up:hover {
    color: var(--color-primary)
}

.td-input-number__down:after {
    position: absolute;
    right: 0;
    width: 1px;
    height: calc(100% - 12px)
}

.td-input-number__up {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    right: 0;
    width: 28px;
    height: 100%;
    cursor: pointer
}

.td-input-number__up:after {
    position: absolute;
    left: 0;
    width: 1px;
    height: calc(100% - 12px)
}

.td-input-number__up.is-disabled {
    color: var(--color-primary-gray-2);
    cursor: default
}

.td-input-number .td-input, .td-input-number .td-input__inner {
    width: 100%;
    text-align: center
}

.td-input-number.is-disabled {
    cursor: default
}

.td-input-number.is-disabled .td-input, .td-input-number.is-disabled .td-input-number__down, .td-input-number.is-disabled .td-input-number__up {
    color: var(--color-primary-gray-2);
    pointer-events: none
}

.td-input-number.is-disabled .td-input__inner {
    background: var(--color-primary-gray-3)
}

.td-input-button {
    position: relative;
    display: flex;
    width: 260px
}

.td-input-button--solid .td-input {
    width: calc(100% - 60px)
}

.td-input-button--solid .td-input__inner {
    width: 100%;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0
}

.td-input-button--solid .td-button {
    width: 60px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.td-input-button--ghost {
    position: relative;
    width: 260px
}

.td-input-button--ghost .td-input, .td-input-button--ghost .td-input__inner {
    width: 100%
}

.td-input-button--ghost .td-button {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px
}

.td-input-button--ghost .td-button:before {
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -8px;
    width: 1px;
    height: 16px;
    background: var(--input-border-color);
    content: ""
}

.td-textarea__label {
    line-height: 30px
}

.td-textarea__inner {
    display: block;
    width: 100%;
    padding: var(--input-padding);
    border: 1px solid var(--input-border-color);
    border-radius: var(--input-radius);
    resize: none;
    outline: 0
}

.td-textarea__inner::-webkit-scrollbar {
    width: 8px;
    background: 0 0
}

.td-textarea__inner::-webkit-scrollbar:horizontal {
    height: 8px;
    border-bottom: 2px solid #fff
}

.td-textarea__inner::-webkit-scrollbar-thumb {
    border-radius: 4px;
    width: 8px;
    background: rgba(0, 0, 0, .15);
    border-right: 2px solid #fff
}

.td-textarea__inner::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, .3)
}

.td-textarea__inner:focus {
    border-color: var(--color-primary)
}

.td-textarea__inner::-webkit-input-placeholder {
    color: var(--color-primary-gray-2)
}

.td-textarea.is-disabled .td-textarea__inner {
    background-color: var(--color-primary-gray-3);
    cursor: not-allowed
}

.td-textarea.is-warn .td-textarea__inner {
    border-color: var(--color-danger-2) !important
}

.td-select {
    position: relative;
    background: #fff
}

.td-select .td-dropdown-menu {
    top: 34px
}

.td-select .td-input {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1
}

.td-select .td-input__inner {
    height: 28px;
    flex: 1;
    background: 0 0;
    border: 0
}

.td-select__drop {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    color: var(--color-primary-gray-2)
}

.td-select__choose {
    position: relative;
    width: 44px;
    height: 17px;
    text-align: center
}

.td-select__choose:before {
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background: #ccc;
    content: ""
}

.td-select__dropdown {
    position: absolute;
    top: calc(100% + 4px);
    right: 0;
    width: 100%;
    padding: 4px 10px;
    background: #fff;
    border: 1px solid var(--input-border-color);
    border-radius: 4px
}

.td-select--system {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid var(--color-primary-gray-2);
    border-radius: 4px
}

.td-select--system:after {
    position: absolute;
    right: 8px;
    font-family: thunder-icon;
    font-size: 16px;
    color: var(--color-primary-gray-2);
    content: "\E622";
    pointer-events: none
}

.td-select.is-disabled {
    cursor: default;
    pointer-events: none;
    background: #f7f7f7
}

.td-select.is-disabled .td-select-group__label, .td-select.is-disabled .td-select__drop {
    color: var(--color-primary-gray-1)
}

.td-select.is-disabled .td-select__drop {
    pointer-events: none
}

.td-select.is-disabled .td-input__inner::-webkit-input-placeholder {
    color: var(--color-primary-gray-1)
}

.td-select.is-disabled .td-select-group {
    border-color: #e6e6e6
}

.td-select.is-chosen .td-select-group__label {
    color: var(--color-default)
}

.td-select.is-focus .td-select-group {
    border-color: var(--color-primary)
}

.td-select.is-focus .td-icon-arrow-drop {
    color: var(--color-primary)
}

.td-select.is-warn .td-select-group {
    border-color: var(--color-danger-2)
}

.td-select .td-select__inner {
    border: 0;
    -webkit-appearance: none;
    flex: 1;
    background: 0 0;
    text-indent: 8px;
    line-height: 36px
}

.td-select-group {
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--color-primary-gray-2);
    border-radius: 4px
}

.td-select-group__label {
    flex: 1;
    text-indent: 8px;
    max-width: calc(100% - 24px);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap
}

.td-select-group:hover {
    border-color: #a6a6a6
}

.td-progress {
    width: 100%;
    display: flex;
    align-items: center
}

.td-progress__text {
    margin-left: 10px
}

.td-progress-bar {
    width: 100%;
    height: var(--progress-height)
}

.td-progress-bar__outer {
    height: 100%;
    background: var(--color-primary-gray-3)
}

.td-progress-bar__inner {
    max-width: 100%;
    height: 100%;
    background: var(--color-primary)
}

.td-draglist {
    position: relative;
    overflow-x: hidden;
    overflow-y: auto
}

.td-draglist::-webkit-scrollbar {
    width: 8px;
    background: 0 0
}

.td-draglist::-webkit-scrollbar:horizontal {
    height: 8px;
    border-bottom: 2px solid #fff
}

.td-draglist::-webkit-scrollbar-thumb {
    border-radius: 4px;
    width: 8px;
    background: rgba(0, 0, 0, .15);
    border-right: 2px solid #fff
}

.td-draglist::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, .3)
}

.td-draglist-item {
    position: relative;
    padding: var(--draglist-padding-y) var(--draglist-padding-x)
}

.td-draglist-item:hover {
    background-color: var(--draglist-color-hover)
}

.td-draglist-item.drag-list-move.is-transition {
    transition: transform .15s
}

.td-draglist-item__icon {
    width: var(--draglist-icon-size);
    height: var(--draglist-icon-size)
}

.td-draglist-item__content {
    margin-left: 14px;
    flex: 1
}

.td-draglist-item__number {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 5;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: hsla(0, 0%, 100%, .7)
}

.td-draglist-item__number span {
    min-width: 20px;
    height: 30px;
    padding: 0 5px;
    font-size: 18px;
    color: #fff;
    text-align: center;
    line-height: 30px;
    background: var(--color-primary);
    border-radius: 15px
}

.td-draglist-item.is-active, .td-draglist-item.is-drag {
    background-color: var(--draglist-color-active)
}

.td-draglist-item.is-drag {
    user-select: none
}

.td-draglist-item.is-drag:after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3;
    width: 100%;
    height: 100%;
    background: hsla(0, 0%, 100%, .7);
    border: 1px solid var(--color-primary);
    box-sizing: border-box;
    content: ""
}

.td-draglist-item.is-drag-more:after, .td-draglist-item.is-drag-more:before {
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    position: absolute;
    content: "";
    box-sizing: border-box
}

.td-draglist-item.is-drag-more {
    background-color: var(--draglist-color-active);
    user-select: none
}

.td-draglist-item.is-drag-more:after {
    top: 0;
    left: 0;
    border: 1px solid var(--color-primary);
    z-index: 2
}

.td-context-menu, .td-context-menu__children {
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, .1)
}

.td-draglist-item.is-drag-more:before {
    top: 4px;
    left: 4px;
    border-right: 1px solid var(--color-primary);
    border-bottom: 1px solid var(--color-primary);
    z-index: 1
}

.td-draglist-item.is-chosen {
    background-color: var(--draglist-color-hover)
}

.td-context-menu {
    position: absolute;
    padding: 6px 0;
    background: #fff
}

.td-context-menu__main {
    position: relative;
    padding: 4px 0
}

.td-context-menu__main:after {
    position: absolute;
    left: var(--context-menu-padding);
    bottom: 0;
    width: calc(100% - var(--context-menu-padding));
    border-bottom: 1px solid #e6e6e6;
    content: ""
}

.td-context-menu__main:last-child:after {
    border-bottom: 0
}

.td-context-menu__item {
    position: relative;
    height: var(--context-menu-height);
    line-height: var(--context-menu-height);
    cursor: default
}

.td-context-menu__item .td-icon {
    position: absolute;
    top: 50%;
    left: 10px;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    line-height: 1
}

.td-context-menu__item .td-icon-more-right {
    position: absolute;
    top: 50%;
    right: 10px;
    margin-top: -8px
}

.td-context-menu__content {
    padding-right: var(--context-menu-padding);
    padding-left: var(--context-menu-padding)
}

.td-context-menu__content:hover {
    background-color: var(--color-primary);
    color: #fff
}

.td-context-menu__content.is-active {
    background: var(--color-primary);
    color: #fff
}

.td-context-menu__content.is-disabled {
    background: 0 0 !important;
    color: var(--color-primary-gray-1)
}

.td-context-menu__children {
    position: absolute;
    top: -10px;
    left: calc(100% + 4px);
    padding: 10px 0;
    background: #fff
}

.td-context-menu__text.is-disabled {
    color: var(--color-gray-2)
}

.td-button, .td-button:hover {
    color: var(--button-default-color)
}

.td-context-menu.is-left .td-context-menu__children {
    left: auto;
    right: calc(100% + 4px)
}

.td-button {
    display: inline-block;
    width: 100px;
    height: 30px;
    font-size: 14px;
    line-height: 30px;
    text-align: center;
    background: var(--button-default-fill);
    border: 0;
    border-radius: 4px;
    transition: .2s;
    cursor: pointer
}

.td-pager ul, .td-pagination, .td-rate, .td-slider, .td-switch, .td-switch__inner {
    display: flex
}

.td-button:hover {
    background: var(--button-default-fill-hover)
}

.td-button--other, .td-button--secondary, .td-button--secondary:hover {
    color: var(--button-secondary-color)
}

.td-button--secondary {
    background: var(--button-secondary-fill)
}

.td-button--secondary:hover {
    background: var(--button-secondary-fill-hover)
}

.td-button--other {
    line-height: 28px;
    background: 0 0;
    border: 1px solid var(--color-primary-gray-2);
    box-sizing: border-box
}

.td-button--other:hover {
    color: var(--color-primary);
    background: 0 0;
    border-color: currentColor
}

.td-button.is-disabled {
    color: var(--button-disable-color);
    background: var(--button-disable-fill);
    cursor: not-allowed
}

.td-button.is-status {
    background: #d7e3fa;
    cursor: default
}

.td-button.is-status .td-icon-loading {
    margin-right: 5px
}

.td-button--large {
    height: 40px;
    font-size: 16px;
    line-height: 40px
}

.td-button--small {
    width: 60px;
    height: 24px;
    font-size: 12px;
    line-height: 24px
}

.td-button--text {
    width: auto;
    color: var(--color-primary);
    background: 0 0
}

.td-button--text:hover {
    color: var(--color-primary-hover);
    background: 0 0
}

.td-button--text.is-disabled {
    color: var(--color-primary-gray-2);
    background: 0 0
}

.td-pagination {
    justify-content: center;
    align-items: center
}

.td-pagination__button {
    width: 16px;
    text-align: center;
    line-height: 16px
}

.td-pagination__button:hover {
    color: var(--color-primary)
}

.td-pagination__button.is-disabled {
    color: var(--button-disable-color);
    cursor: default
}

.td-pagination__jump {
    margin-left: 15px
}

.td-pagination__jump .td-input__inner {
    width: 40px;
    height: 22px;
    margin: 0 5px;
    line-height: 22px
}

.td-pagination__jump .td-select-group {
    width: 74px;
    height: 22px
}

.td-pagination__jump .td-select__drop {
    margin-right: 3px
}

.td-pagination__jump .td-dropdown-menu {
    top: 23px
}

.td-pagination__jump .td-dropdown-menu .td-dropdown-menu__item {
    position: relative;
    padding-left: 20px
}

.td-pagination__jump .td-dropdown-menu .td-icon {
    position: absolute;
    top: 50%;
    left: 4px;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    line-height: 1
}

.td-pagination--background .td-pager li {
    min-width: 30px;
    height: 30px;
    margin: 0 5px;
    padding: 0 3px;
    font-weight: 400;
    line-height: 30px;
    background: var(--button-secondary-fill);
    border-radius: 4px;
    box-sizing: border-box
}

.td-pagination--background .td-pager li:hover {
    color: var(--color-default);
    background: var(--button-secondary-fill-hover)
}

.td-pagination--background .td-pager li.is-active {
    color: #fff;
    background: var(--button-default-fill)
}

.td-pagination--background .td-pagination__button {
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    background: var(--button-secondary-fill);
    border-radius: 4px
}

.td-pagination--background .td-pagination__button:hover {
    color: var(--color-default);
    background: var(--button-secondary-fill-hover)
}

.td-pagination--background .td-pagination__button.is-disabled {
    color: var(--button-disable-color);
    background: var(--button-disable-fill)
}

.td-pager__number.is-active, .td-pager li.is-active, .td-pager li:hover {
    color: var(--color-primary)
}

.td-pagination--small .td-pager li, .td-pagination--small .td-pagination__button {
    min-width: 22px;
    height: 22px;
    font-size: 12px;
    line-height: 22px
}

.td-pagination--small .td-pagination__button {
    width: 22px
}

.td-pagination--large .td-pager li, .td-pagination--large .td-pagination__button {
    min-width: 36px;
    height: 36px;
    font-size: 12px;
    line-height: 36px
}

.td-pagination--large .td-pagination__button {
    width: 36px
}

.td-pager {
    padding: 0 10px
}

.td-pager li {
    min-width: 16px;
    padding: 0 11px;
    font-size: 14px;
    text-align: center;
    font-weight: 700;
    line-height: 16px;
    cursor: pointer
}

.td-pager li:hover .td-icon-more:before {
    content: "\E71A"
}

.td-popover {
    position: absolute;
    z-index: 99;
    min-width: 150px;
    padding: 12px;
    margin: 4px;
    background: #fff;
    border: 1px solid #e5e5e5
}

.td-popover .td-poper__arrow {
    width: 8px;
    height: 8px;
    border-left: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5
}

.td-popover:after {
    position: absolute;
    top: -6px;
    right: -6px;
    bottom: -6px;
    left: -6px;
    pointer-events: none;
    content: ""
}

.td-rate .td-icon-star-half:before, .td-rate__item.is-on .td-icon-star:before {
    content: "\E694"
}

.td-popover.is-top {
    margin-bottom: 8px
}

.td-popover.is-top .td-poper__arrow {
    bottom: -5px
}

.td-popover.is-left {
    margin-right: 8px
}

.td-popover.is-left .td-poper__arrow {
    right: -5px;
    transform: rotate(-135deg)
}

.td-popover.is-bottom {
    margin-top: 8px
}

.td-popover.is-bottom .td-poper__arrow {
    top: -5px;
    transform: rotate(135deg)
}

.td-popover.is-right {
    margin-left: 8px
}

.td-popover.is-right .td-poper__arrow {
    transform: rotate(45deg);
    left: -5px
}

.td-popover-comfirm {
    display: flex;
    flex-wrap: wrap;
    min-height: 20px;
    padding: 5px 14px
}

.td-popover-comfirm__icon {
    width: 16px;
    height: 16px;
    padding-top: 3px
}

.td-popover-comfirm__icon .td-icon-warning {
    color: var(--color-warn-2)
}

.td-popover-comfirm__content {
    flex: 1;
    margin-left: 8px;
    overflow: hidden
}

.td-popover-comfirm__title {
    font-size: 16px
}

.td-popover-comfirm__text {
    margin: 8px 0 3px;
    color: grey
}

.td-popover-comfirm__footer {
    position: relative;
    z-index: 2;
    width: 220px;
    text-align: center
}

.td-popover-comfirm__footer .td-button {
    height: 28px;
    color: var(--color-primary);
    line-height: 28px;
    background: 0 0;
    font-size: 12px
}

.td-popover-comfirm__footer .td-button--secondary {
    color: var(--color-default)
}

.td-rate__item {
    position: relative;
    cursor: pointer
}

.td-rate__item.is-on .td-icon-star:before {
    color: #ff9c1a
}

.td-rate__text {
    margin-left: 5px
}

.td-rate__item.is-half .td-icon-star-half {
    color: #ff9c1a
}

.td-rate--readonly .td-rate__item {
    cursor: default
}

.td-rate .td-icon-star-half {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    overflow: hidden;
    color: transparent
}

.td-slider {
    align-items: center;
    flex-wrap: wrap;
    min-height: 20px
}

.td-slider__button {
    position: absolute;
    top: 50%;
    margin: -7px 0 0 -7px;
    width: 14px;
    height: 14px;
    background: #fff;
    border: 3px solid var(--color-primary);
    border-radius: 50%;
    box-sizing: border-box;
    transition: transform .2s
}

.td-slider__button:hover {
    transform: scale(1.15)
}

.td-slider__button:active {
    background: var(--button-secondary-fill-hover)
}

.td-slider__bar {
    position: relative;
    width: 100%;
    height: 3px;
    background: #e6e6e6;
    border-radius: 1px
}

.td-slider__bar-inner {
    width: 0;
    height: 100%;
    background: var(--color-primary-auxiliary)
}

.td-slider__dot {
    position: absolute;
    top: 0;
    width: 3px;
    height: 3px;
    background: #fff;
    border-radius: 50%
}

.td-slider__dot.is-active {
    background: var(--color-primary-auxiliary)
}

.td-slider__mark {
    position: relative;
    width: 100%;
    height: 22px;
    margin-top: 10px
}

.td-slider__mark-text {
    position: absolute;
    width: 60px;
    margin-left: -30px;
    text-align: center
}

.td-slider--input-number > .td-input .td-input__inner, .td-slider--input > .td-input .td-input__inner {
    width: 50px
}

.td-slider__mark-text:first-child {
    text-align: left;
    margin-left: -5px
}

.td-slider__mark-text:last-child {
    text-align: right;
    margin-left: -55px
}

.td-slider__mark-text.is-chosen {
    margin-top: -3px;
    color: var(--color-primary)
}

.td-slider--input {
    position: relative;
    padding-right: 65px;
    min-height: 32px
}

.td-slider--input .td-input-number, .td-slider--input > .td-input {
    position: absolute;
    right: 0
}

.td-slider--input-number {
    position: relative;
    padding-right: 125px;
    min-height: 32px
}

.td-slider--input-number .td-input-number, .td-slider--input-number > .td-input {
    position: absolute;
    right: 0
}

.td-slider.is-vertical {
    height: auto
}

.td-slider.is-vertical .td-slider__bar {
    width: 2px;
    height: 100px
}

.td-slider.is-vertical .td-slider__bar-inner {
    position: absolute;
    bottom: 0;
    width: 2px
}

.td-slider.is-vertical .td-slider__button {
    left: 50%;
    margin: -7px 0 0 -7px
}

.td-checkbox__label, .td-switch__text {
    margin-left: 6px
}

.td-slider.is-disabled .td-slider__button {
    border-color: #ccc;
    cursor: default;
    transform: unset
}

.td-slider.is-disabled .td-slider__button:active, .td-slider.is-disabled .td-slider__button:hover {
    background-color: #fff
}

.td-slider.is-disabled .td-slider__bar-inner, .td-slider.is-disabled .td-slider__dot.is-active {
    background: #ccc
}

.td-slider.is-disabled .td-slider__mark-text.is-chosen {
    color: var(--color-default)
}

.td-switch__inner {
    width: 32px;
    height: 18px;
    padding: 3px;
    background: var(--color-primary-gray-1);
    border-radius: 9px;
    box-sizing: border-box;
    cursor: pointer
}

.td-switch__inner:after {
    width: 12px;
    height: 12px;
    background: #fff;
    border-radius: 50%;
    transition: transform .2s;
    content: ""
}

.td-switch.is-disabled .td-switch__inner {
    background: #e6e6e6;
    cursor: default
}

.td-switch.is-checeked .td-switch__inner {
    background: var(--color-primary)
}

.td-switch.is-checeked .td-switch__inner:after {
    transform: translate3d(14px, 0, 0)
}

.td-switch.is-checeked.is-disabled .td-switch__inner {
    background: #d7e3fa
}

.td-checkbox {
    display: inline-flex;
    align-items: center
}

.td-checkbox__inner {
    position: relative;
    flex: none;
    width: 14px;
    height: 14px;
    background: #fff;
    border: 1px solid var(--color-primary-gray-1);
    border-radius: 3px;
    -webkit-appearance: none
}

.td-checkbox__inner:before {
    position: absolute;
    top: 3px;
    left: 3px;
    width: 6px;
    height: 3px;
    border-left: 1px solid #fff;
    border-bottom: 1px solid #fff;
    transform: rotate(-45deg) scale(0);
    transition: .1s;
    content: ""
}

.td-checkbox__inner:checked, .td-checkbox__inner:hover {
    border-color: var(--color-primary)
}

.td-checkbox__inner:checked {
    background: var(--color-primary)
}

.td-checkbox__inner:checked:before {
    transform: rotate(-45deg) scale(1)
}

.td-checkbox.is-indeterminate .td-checkbox__inner {
    background-color: var(--color-primary);
    border-color: var(--color-primary)
}

.td-checkbox.is-indeterminate .td-checkbox__inner:before {
    position: absolute;
    left: 3px;
    width: 6px;
    height: 1px;
    background: #fff;
    content: "";
    border-left: 0;
    top: 5px;
    transform: rotate(0)
}

.td-checkbox.is-disabled {
    cursor: not-allowed
}

.td-checkbox.is-disabled .td-checkbox__inner {
    background-color: var(--color-primary-gray-3);
    border-color: var(--color-primary-gray-2)
}

.td-checkbox.is-disabled.is-indeterminate .td-checkbox__inner, .td-checkbox.is-disabled .td-checkbox__inner:checked {
    background-color: var(--color-primary-gray-2)
}

.td-checkbox.is-disabled .td-checkbox__label {
    color: var(--color-primary-gray-2)
}

.td-table {
    width: 100%;
    background: #f9faff;
    border: 1px solid var(--table-color-line);
    border-radius: 4px;
    box-sizing: border-box;
    table-layout: fixed
}

.td-table td, .td-table th {
    height: 34px;
    border-bottom: 1px solid var(--table-color-line)
}

.td-table .td-tree-node {
    margin-left: 10px;
    max-width: 100%
}

.td-table th {
    font-weight: 400;
    text-align: left
}

.td-table .is-striped {
    background-color: #f2f3fb
}

.td-table--hover tbody tr:hover {
    background: var(--tree-background)
}

.td-table__body-wrapper {
    overflow-y: auto
}

.td-table__body-wrapper::-webkit-scrollbar {
    width: 8px;
    background: 0 0
}

.td-table__body-wrapper::-webkit-scrollbar:horizontal {
    height: 8px;
    border-bottom: 2px solid #fff
}

.td-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 4px;
    width: 8px;
    background: rgba(0, 0, 0, .15);
    border-right: 2px solid #fff
}

.td-table-tree, .td-table__body, .td-table__header {
    width: 100%
}

.td-table__body-wrapper::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, .3)
}

.td-table__body tr:last-child td {
    border-bottom: 0
}

.td-table__footer-wrapper {
    position: relative;
    z-index: 2;
    margin-bottom: -1px;
    background: #f9faff;
    border-top: 1px solid var(--table-color-line)
}

.td-table__footer {
    width: 100%;
    height: 36px
}

.td-table__footer .td-checkbox {
    padding: 0 10px
}

.td-table__text {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 10px
}

.td-table__text .td-icon-sequence.is-show, .td-table__text:hover .td-icon-sequence {
    opacity: 1
}

.td-table--stripe tr:nth-child(2n) {
    background-color: #f2f3fb
}

.td-tree--hover .td-tree-node__content:hover, a.td-table__text:hover {
    background: var(--tree-background)
}

.td-table--border td, .td-table--border th {
    border-left: 1px solid var(--table-color-line)
}

.td-table--border .td-table__body-wrapper {
    margin-top: -1px
}

.td-table--border tr > td:first-child, .td-table--border tr > th:first-child {
    border-left: 0
}

a.td-table__text {
    color: var(--color-default)
}

.td-table-tree .td-tree-node__content {
    height: auto
}

.td-table-tree .td-table__footer .td-checkbox {
    margin-left: 18px
}

.td-table-tree .td-tree-node__label {
    display: -webkit-box;
    flex: 1;
    height: 34px;
    overflow: hidden;
    line-height: 34px
}

.td-table-tree__cell-inner {
    display: flex;
    align-items: center;
    margin: 0 10px
}

.td-tree-node__content {
    position: relative;
    display: flex;
    align-items: center;
    height: 36px
}

.td-tree-node__content.is-chosen {
    background: #eaedf5
}

.td-icon-sequence.is-ascending, .td-icon-sequence.is-descending {
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent
}

.td-tree-node__label {
    margin-left: 5px
}

.td-tree-node > .td-tree-node__children {
    position: relative;
    overflow: hidden;
    height: 0
}

.td-tree-node__image-icon {
    width: 16px;
    height: 16px;
    margin: 0 6px
}

.td-tree-node__expand-icon {
    margin-right: 2px;
    color: var(--color-primary-gray-1);
    transform: rotate(-90deg);
    transition: transform .2s
}

.td-tree-node__expand-icon.is-hidden {
    visibility: hidden
}

.td-tree-node__expand-icon.is-expanded {
    transform: rotate(0);
    color: var(--color-primary)
}

.td-tree-node__children {
    transition: .2s
}

.td-tree-node.is-expanded > .td-tree-node__children {
    height: auto;
    overflow: visible
}

.td-tree-node .td-checkbox__label {
    display: flex;
    align-items: center
}

@font-face {
    font-family: thunder-icon;
    src: url(data:application/font-woff;base64,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) format("truetype")
}

[class*=" td-icon-"], [class^=td-icon-] {
    font-family: thunder-icon !important;
    font-size: 16px;
    font-style: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.td-icon-load:before {
    content: "\E615"
}

.td-icon-close-browser:before {
    content: "\E61D"
}

.td-icon-arrow-line:before {
    content: "\E61E"
}

.td-icon-arrow-drop:before {
    content: "\E622"
}

.td-icon-rename:before {
    content: "\E623"
}

.td-icon-success:before {
    content: "\E638"
}

.td-icon-error:before {
    content: "\E637"
}

.td-icon-question:before {
    content: "\E63A"
}

.td-icon-warning:before {
    content: "\E639"
}

.td-icon-arrow-left:before {
    content: "\E651"
}

.td-icon-arrow-right:before {
    content: "\E652"
}

.td-icon-refresh:before {
    content: "\E653"
}

.td-icon-security:before {
    content: "\E67E"
}

.td-icon-sequence:before {
    content: "\E680"
}

.td-icon-info:before {
    content: "\E685"
}

.td-icon-choose:before {
    content: "\E686"
}

.td-icon-more-right:before {
    content: "\E687"
}

.td-icon-search:before {
    content: "\E696"
}

.td-icon-fav:before {
    content: "\E694"
}

.td-icon-fav1:before {
    content: "\E695"
}

.td-icon-setting:before {
    content: "\E6C4"
}

.td-icon-arrow-up:before {
    content: "\E6C5"
}

.td-icon-arrow-down:before {
    content: "\E6C6"
}

.td-icon-more:before {
    content: "\E71A"
}

.td-icon-more1:before {
    content: "\E71B"
}

.td-icon-svg {
    width: 20px
}

.td-icon-close:before {
    content: "\E61D"
}

.td-icon-sequence {
    color: var(--color-primary-gray-1)
}

.td-icon-sequence.is-ascending {
    background-image: linear-gradient(180deg, var(--color-default) 50%, var(--color-primary-gray-1) 51%)
}

.td-icon-sequence.is-descending {
    background-image: linear-gradient(180deg, var(--color-primary-gray-1) 50%, var(--color-default) 51%)
}

.td-icon-star {
    position: relative
}

.td-icon-star:before {
    content: "\E695";
    color: #b3b3b3
}

.td-icon-loading {
    display: inline-block
}

.td-icon-loading:before {
    content: "\E71D"
}

@keyframes td-load {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.td-icon-svg-file {
    height: 17px
}

.td-loading-bar {
    display: flex;
    justify-content: center;
    height: 1px;
    background: #e6e6e6
}

.td-loading-bar__inner {
    height: 100%;
    background: var(--color-primary);
    animation: td-loading 1.5s linear infinite both
}

@keyframes td-loading {
    0% {
        width: 0;
        opacity: 1
    }
    30% {
        width: 50%;
        opacity: 1
    }
    60% {
        width: 100%;
        opacity: 1
    }
    80%, to {
        width: 100%;
        opacity: 0
    }
}

.td-loading-mask {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: hsla(0, 0%, 100%, .8)
}

.td-loading-mask__icon {
    display: block
}

.td-loading-mask__icon i {
    font-size: 18px;
    display: block;
    color: var(--color-primary)
}

.td-loading-mask__text {
    margin-top: 10px;
    font-size: 16px;
    color: var(--color-primary)
}

.td-tooltip-wrapper {
    position: relative;
    display: inline-block
}

.td-tooltip {
    position: absolute;
    z-index: 9999;
    padding: 3px 8px;
    line-height: 20px;
    background: #fff;
    border-radius: 4px;
    filter: drop-shadow(0 0 3px rgba(0, 0, 0, .2))
}

.td-tooltip__inner {
    display: inline-flex;
    position: relative;
    z-index: 2
}

.td-poper__arrow, .td-tooltip:after {
    position: absolute;
    content: ""
}

.td-tooltip__inner [class^=td-icon-] {
    display: block;
    margin: 2px 6px 0 0
}

.td-tooltip__inner .td-icon-error {
    color: var(--color-danger-2)
}

.td-tooltip__inner .td-icon-warning {
    color: var(--color-warn-2)
}

.td-tooltip:after {
    top: -10px;
    right: -10px;
    bottom: -10px;
    left: -10px;
    pointer-events: none
}

.td-tooltip__close {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 16px;
    height: 16px;
    vertical-align: -1px
}

.td-tooltip__close:hover {
    color: var(--color-primary)
}

.td-tooltip.is-top {
    bottom: calc(100% + 6px);
    left: 0
}

.td-tooltip.is-right {
    top: 0;
    left: calc(100% + 6px)
}

.td-tooltip.is-bottom {
    top: calc(100% + 6px);
    left: 0
}

.td-tooltip.is-left {
    top: 0;
    right: calc(100% + 6px)
}

.td-poper__arrow {
    width: 6px;
    height: 6px;
    background: #fff;
    transform: rotate(-45deg)
}

.is-top .td-poper__arrow {
    bottom: -3px;
    left: 15px
}

.is-bottom .td-poper__arrow {
    top: -3px;
    left: 15px
}

.is-left .td-poper__arrow {
    top: 10px;
    right: -3px
}

.is-right .td-poper__arrow {
    top: 10px;
    left: -3px
}

body {
    --color-primary: #3f85ff;
    --color-default: #4d4d4d;
    --color-secondary: #b3b3b3;
    --color-auxiliary: #727272;
    --color-hover: #2670ea;
    --color-danger: #ff7b5f;
    --color-note: #ff9701;
    --color-title: #1a1a1a;
    --color-icon: #6a707c;
    --color-border: #e6e6e6;
    --color-disabled: #ccc;
    --color-success: #00b36a;
    --color-item-hover: #f6faff;
    --color-link: var(--color-default);
    --background: #f5f5f5;
    --background-secondary: #eff6fe;
    --input-border-color: #eee;
    --button-default-fill: #3f85ff;
    --button-default-fill-hover: #619bff;
    --button-secondary-fill: #f5f5f5;
    --button-secondary-color: #727272;
    --button-disable-color: #ccc;
    --button-disable-fill: #f5f5f5;
    --button-secondary-fill-hover: #e6e6e6;
    --input-border-color-hover: #eee;
    --table-color-line: #eee;
    --dropdown-border-color: var(--color-border);
    --badge-background: var(--color-danger)
}

pre {
    font-family: microsoft yahei, -apple-system, PingFang SC, simsun, Arial, sans-serif
}

@font-face {
    font-family: dincond;
    src: url(data:application/font-woff;base64,d09GRk9UVE8AAD+0AA0AAAAAXXwABAEtAAAAAAAAAAAAAAAAAAAAAAAAAABDRkYgAAAFAAAAL70AAEINLfVeEkZGVE0AAD1oAAAAGgAAABxd9dcuR0RFRgAANMAAAAAdAAAAHgAnAO5HUE9TAAA1AAAACGcAABEaFTw6HkdTVUIAADTgAAAAIAAAACBskXSPT1MvMgAAAYwAAABNAAAAYBLKKEVjbWFwAAADSAAAAaIAAAH6sFBa5mhlYWQAAAEwAAAAMwAAADbmCRjiaGhlYQAAAWQAAAAgAAAAJAdNA9xobXR4AAA9hAAAAjAAAAOgdQUkom1heHAAAAGEAAAABgAAAAYA6FAAbmFtZQAAAdwAAAFpAAACl1SqtyZwb3N0AAAE7AAAABMAAAAg/2sATXicY2BkYGAA4l0Mft/j+W2+MnAzvwCKMBzc+KoWRv+f9d+O+TXzUaA6DgYmkCgAjDwO4AB4nGNgZGBgPvrfjoGBRer/rP+zmV8zAEVQwAsAm2EHLwAAUAAA6AAAeJxjYGaczfiFgZWBgamLqYuBgWEahAaKGTE8ZEACDQwM+g4MDF7IYgwKDAq/mZhO/HdnMGU+ynBIgYFR0BEozMTPdAIkx8AEAMiSDj0AAAB4nI2RPU7DQBCF3+YPISFKSLkSbRLZMfmlSoIiJUUUhRPYssERq93IcYrUnIMWIQoOQMk5OAoFz8siRIEUj9bzzeyb2VkbwCleIfD93ODZscCJOHNcwpEIHJdxLu4dV6h5dFyl5s1xjfkPKkXlmNGTrSpYoI5PxyWcigvHZbTEleMK6uLBcZWaF8c15t/RhmdNYgSFCAky5GhijhCa2SUMTaFB3mBHhcIaW6RUxsxF2PM9pUazrsgb6iRmNi66aXbKWWMsKe7dkjP63Hb5rf7x0p4RURNy7dEC2p7nyZGKkixvzkMtl8aohtzsIrXepkkso72cGp1vU7ORM50nmQ7ztdGhkrcmk3ma2O1iSbWOsjDbs+k1p1xgYk+NeeexvWnMjdliYnTcHBvFaMUh73h1ZcfBKrnbqTD7v3wIn9al9RGggx794G/Toe93u/2g0+sPDh6j+EuX/BYBvc/Qu2wFnn9o+RcurHJ+AAAAeJxjYGBgZoBgGQZGBhD4AuQxgvksDDeAtBGDApAlxFDHsIbhP6MhoxNjMGMiYwVjHeMkpuNMdxVEFKQU5BSUFNQUDBSsFFwU1igqKQn9Zvr/H6hXgWEBwzqgHkfGIMYEoJ5aoJ5jTDcUhBUkFGQUFMB6LOF6GP/////w/4H/+//3/a//X/A/5b/ff6//5v9Y/n55sP/Bnge7H+x6sP3BugfLHsx9YHb/4L1LrE+gbiYRMLIxENTIyMTMwsrGzsHJxc3Dy8cvICgkLCIqJi4hKSUtIysnr6CopKyiqqauoamlraOrp29gaGRsYmpmbmFpZW1ja2fv4Ojk7OLq5u7h6eXt4+vnHxAYFBwSGhYeERkVHRMbF8/Q3NLWMWHq7AXzFy5etGTZiuUrV61ZvXbdhk0bN2/dsnPHrt238pOS0++UzsvNfFycwdA6naGAgSG1BOy0rEqGpdvrEnMYTjIwZFfdTahvmnLo8KXL129cubqNYd+xh4/uP3j67HbZtZv3Gjsbutp7evu6J01mmDhz1oz9py7knTl7rvzi+dMAqMmZzgAAeJxjYGYAg/8ZDL4MWAAAKA4BuAB4nL17d0BT1/74iZDca9BgiRdfF0PrqqOCq2qtdde9bbUoIiCbsEcCIWGI8gHZCWElgLKXgDLqrANrbR21rba0tdVapa3SVnou79L3fucmgNDne+/7/vl5QZJ7PveMz15XgMzNkUAgGLls1fqlMn+3aUtkvm5IMAwJkCNrg1hbAWs3jLU3Y/9mzm0ZYXZrhPl4bvvfY/6+Q/gS+snyJYRGvTSs7bmX0LSX5p23QhT/HI1GoL8hWxSElCjJf29wgIur+/LQIJk7+XUNDQpy93eNnDFj1vSZMxwcZ8yYYbfYd697UMi01S7+dhtlMt+pdgGhe329gj3d3ez2RtqtkPmHBHvKAuxW+Ye4B/m7hHjJ/F187fbJguxCPN2Nw/yvna/X3iCXoMjpg88x5EzknwANQ2bIHAmRCFHIEw1HYmRBdjsSSZAlGoWeQ1ZIikYjBlmjMeQMz6MX0IvoJfQysiHnsUP2aCwah15B49EENBFNQpPRq2gKmoqmoenoNTQDOSBHNBPNQrPRHDQXvY7mofloAXoDLURvokXoLRSFlqClaBlajlaglehttAqtRmvQWrQOrUcb0Ea0CW1GW9BWtA1tR++gd9EOtBO9h5zQLrQbOQsS0R7kgvYiD+SGDGgfikOVCJA30qIjSIWKUD46jCpQNMpBwQT3CUiDUlEEKkR5qAQpUC2qQtWoHtWgOpSEjqJjqAE1oibUio6jZtSCclEbOoXeRyfQaXQSlaMUdB6dRR+gc+gCKkDZ6CN0CX2IPkaX0RWUhj5BN9BVdA1dR5+jT9FN9BkqQ1+gr9AtdBt1oC+RHmWi79A36Ft0B32PitFdlI6SUQY6hLLQGfQ1uoja0T3kTjjkAFKjWBSPElEM2o9k6CBaTDgoFHmhcBSGAlAgCkGRSI58kC9yFRxAOlSK/JC/4CB6wDObOSHkVIK+RoGL4PCw+cM2Dysc9thsstkis3fNPM0izA6ZdZlLzF2ElsKlwhOiJSJvag11g3pEj6Zn05voYLpreOXwM+LXxWvEFeKT4u8tPC00FuUWd0f8bYTDCM8RyhG5I66N3DeyZuTFkT+M7JW8LNkkiZVoJCWSdsk3lsMsx1nOtHzLcp/lOcsvRs0atXlU5nNbntM+d9IKWc20crX60Oq+1T+lTtI26U+j0eg3Ru8YLR9dNbpj9M+je5gI5pD1SOvN1tusFWMEY5aO2TOmasyVvz14XvB80gsvvrDwhXdfuPjCZy+OfHHai3EvzXtpxUuHXjr50s2Xvn952MuKlyttzG1CbT1t79mZ2Y2zW2LnYpdi12DXbS+1d7DfYO9nn2ffbP+p/eOxz4+NH1sy9tLYX8ZZjZs7rnDcjVciXtG90vrK7fFzx28dnzZh/ITTEx5NfHni2omJEz+ZVDTpzKT7k0dOnj3Za3Lc5MOTr0zufjXj1ZZXb7/KTXl5yqIp+intU3dPjZ9aOfXaNPNpL0+rmnZ92vfTX5oe8trI1z567ZcZFjPmzFg3w21G0Yy6GSdndDkMc2AcpjjMc9jh4OUgd0h1qHA443DXcbjjVMctjuBY6Xh55sKZa2d6zYycqZn5zcxHs9bNaps9h3tdwq2T2AG2ZD/WW93GoziqW3oNb8NODJtl35tFSe9ginNi7MSSm2ymAc/SCz799V6XGV7CZjJ413huF4VbrPGuCeSDBB8AA377MZZ24bl6AXuBfYvpWkkdwWu4YRDArbGDUvLRDAK5NTMhecINiOSkcwEmXgV/bpcQD4NyvOsP/jM24z9+D/B4GRRgqZAbZh3BSe2gkHyW3OFEhr+b6QXN2LzgURE2N2On4B0M4BGhLbNr6bk1wh362AK4AJXpJTm5Gq0usxhKIC8uW5kbkRIEPjQ2F8FV5al9deGFgTne6fSDSdMoWUKwMjzSzTPiHVgD3LBSt0/96Duewg9CtRGwCNz3B0fJVUpFfCAEgTxTrVEUJJbAUZojM72lca7xKgqtiGpIoDmzR9icOppRkavX19Vmn4CbtIQ1jzMoDT0z9TF6q7bvOr7H1t9/9b1U37MYmzP24t7RGDNjxVIZVuCDzDzgkv90+hqEy1kvZpyYHd0bwbwiluAX4zpnGdjHnVY1j1Z2/tQp9WUTrKfjycKq80fuHvqKniE64KTeofCi5+AxwnMXas/Ct/Q3Th+/+sbabWNtYEP5e63raGnTyhXuq2E+cKLrK36GK3CstKGZloY0rBXOwK8w0qbAfQHhPrAbfBrgHDQWNDYdoz/C+Qy8Jw/08aSl1X6N8hqopOsLiitKDaFeNhJu+AA7DGYGyaRphp7HesH73WY4JIQJKY4ojD6sLkgsgzqa3UNBXWpZdoHucGFhcTG97IbQx0cmC/MI23dgL7jSvb4UuB7aa9in9yz3r/Kh++fC5mQyfzyCYQlAY2JTWH14nX+ZbxW9dJmwOEQfkROkiUyVgQfdSxbwSJSpIpVB4REhIfSny4SVVRXlhbWG+tQmaKQlZ/AjA/6KzPjWTTNWzAYzk0/9csr94+kbU7HVm5DKWU27uG/VL+9Ofrd+9cMLiZzVLRAmYqsHm4SSB78bsLALi8izK7rMWAccy4wXS7gXuVcNOFovOI8pM/zWagZv4iiulJKswLFdApxCoLq43RMoCWdNJExwDlua4ZXWE8SSVYsNPe1kruaeUmYicOv+7HoMQkl9XCdb2yk41v2EHJjDUxl2KgXn5TWyUlnp3lxncAaXKFmALEDuBZvpXjK2Od+rLKAs4FhUMzRDU25ZaVlpfg2cp+0Tmb0h4f7+wfomG3YW1VSiLy8vDt9rI6kHnhcFeDlZoM66x8mespsN3Ig/vobeO2R96GRLOwXN3b+T4V7rTs6JwrPxJWFxc147nIbzEY2yI/6l7po9sAecjTtR8Dtx6W41LVEUttcG3lHuVvjR3AruETjwRzL0LNVbtXb/3D2xW/oA/4mdGbh0oC6iJjY/SifX5mtydFn5tLQrrzKlBt6H69GtPlVBh/flOpHTukcb1/CArfSe7pPUUUNhqU0eaBIy1TnyZF/wgH3gqYiMi1XtjwLa07FWVAA5CVkxtPRBbniyB7wHzgddoiLi1Mo4OdA8Mmxb7V0I1vKMWDtuxNoxXVkp3dKcchK+oHn0/NDJygmCgrrN2JHYjOnciaX2nNSRwsvZQ68At6b3EEzFOwE4s24AbMafEL/DLukUtP1s5NN/4HgGvk/82K8+pNxf5wyRELk/Ki46TqlUK2m8Be/r5Np7p3flUg3FRRU2+ZAbnxWlCyWSt5FcWxVh6uiY/WS3fiGFx21P2HvyNPY00jjaSOO8srK2tuTL8JA2onaBXtDYjQVk3T+xLwNP1EcDq0MqvHXuEAXyAzGxNB6Om4j25Z7jOoTyXTGbiH6bnbm3zPWwx7Gw84SmbflljcXVmgbopO2TGdiT7CQPjY1RJijIBFHpsVp5cXIrHIfjSSfyi7K0uem5QOeCjiA5PzRpJ+wxoow9iDdP7bSq/lV6jRX0mDN3uTvc5l8paX0nHkVxG3r/7IZ+RhC0dPXtdhcD7Ym18kp1QbQuMidfm5OblZ9XeagSTsA1dZtHnazMLWcP0HvAXSmTeXionWAF7Dzkm+ebJc+Jyo+JVEZFx0bKfRM9YQMNC9OcjrmUyY5Hk53yFC07diytDW7T9mkM7IW9ivBYVXRClOlIGoUBGqEJWpIa8g1ZmqdHUuWHJbnBbno/vsaAZ4prRGRcnCpRCUpQpcZlRhQkN0It1EB9bkFmdk6qDmgdaBMz1bmRhA29jIgYTA6WsMF38U2yxqC6PYbN8A7skstcQ7xj3GAqjM12P+Jd7FcTfZRfOu+QNovmhnNNxALi53CHML9VewE+ou2TeHrs5OkxsHmtvCj5hJEebTw9dOl5A/QISdrF04M35XgzUTbElpvh3fhjYrqJVef/SOx4bZWONxEQq68xTez8TqPKoonKktbjEawrD1XfbmDnE/Nt1k6s6nDOGVOY/AKmue+47bCY6EI8vuurLgFRgl8yXb3CCRQr5FaZPj19GGcy8Bn3Hd4OHI2diVYkv8ANF0o+5H2NSXiLFZ5j9DayiVpgX2YNDFxXNnvWysr35m2HbeAeJgv09I55l5C3WwRlyRUFemLM0wugEHLiNTF5kanB4AluiUGh/vTGN4TSW5HBSj9i3ld0n6egJNOQq6cvfiksqcqugw+IQrDrNfBnwweUBgVvTS0e4jH3rdq67xBdJ13EnrJmWyi4EFnrXyYrc83dAUSBiXaAa5RM5i+Te8Imunc2JdXD23kex/bSt74QhunjCqCWZu2oi2UVZ22wtehsud9G29epOE1MtipdkRWZFk60pEwZLAsLjvWH9TS3kYL1Wf6GYFrqXCIrUx4nJ9Gn52blZBANqKWli1ypRDyJkTobNXcI0dx4PdVUbCCam6hVye/EtRrRxW4nuN1rPVG8A+9nJpHzIOjCQZ14W5egHZtjWzzMjF2APRhoPnDW86hao8yKStelazM1mqqqtEZev8a0+VT7VezRreKP+KcTXi76GJp1FZU1tdqT0EF8mmTm9WDZepttOOisqP2wzxu2B/E5ZmWQ/0ab7XjbGVF7SfBSW8k3cV1scZegiXe/HPAmZrJpK2yGaR8T+ftLMUsY6OXkru0X/av3HllJFlxFFlwlugpNR8qrz1xM6iLDxhWBG3VgeZi3Sh4REwD0KjbjY1EpFMZo5AbvQyvJIC2pIud07cK7jfMT42j9qlhyhKCkcdA9FhNre5osYY4v4dld3Gy8m5LcjyMQP+KoLiuy2dt4NWeOzaUs2fRmBu/gVr9GSZ9c58xEUnZnqMt7NgGg0KjzwmoSPwVMTLZ16qeGmuy8PE0p0NIn7ZC1JdmVdyAPXoo87VlHS1m/CpeCrbASXAP9vLZvSB4HnB35SRp3ZkOFV2PgVaC7v7iGGVsJtiB7bX6Mq/V9dCL4WUDsL2fOneFs8RnO3LjtK9iWu0K8hb+ZzLTxWG9ZTxFLDhN8/0TMUjB/5y5ez7DFnHlvMbXey2eXTSAE62Ly6YVvCWs83g+7AJehtaCmrrom7wRxBSRPns5FOIebx87G4/D7nBC417i7gO9y5gD84sSarTmLhxFvv1UoKSS0vKvvx3UXt4HF/BaJi68cmA5n8FPWklOMp7iXe8WcDSue0D8Xu6aTa/wQfsYt0LtRSHzYp4+9xj+2xnqqWPLLABfhGfzNeYRjpok5c+vpYkknQVh2F6Gb8Rkbfng+0al48rZPOMaGW2nEV+UnIvzi+5/hV2w58yTmNTJjiCHBwP5mJDY/pzSCfaeB2XhSWP4hkMMOB2yX/Pv2dr8aV8KPzyIbbaLb+1CbU2qgpS0XdgodWEsGpiRti3Wj/UveWVfvK4xPjE9Sg4lXQmsO3AA8hvwculHUxyulkK/KVtBFXinLgBtDS7CIGMnvB51kk7U9via8UltwGe7SeOI7lzlrG+5t45FaLosecC3CSDaJ2Ri7Y4PNCux/TdSetWOjreR2PHbCX+GdRoQZWWgSiXxmiCXlpumxHX+PNorB7xOwRS/G5iymJH+YmOds/1Oz8VLGgcRzJpKwy3ucGM4SXuutn8HWkw94BtkITUw2/s0E0bPTBLGit34SgRgFk3vrl5tAXySgwwmvHP219yj5IJRc7Zt0njX3CivEz+OfCNgyLnYZjuXhrbmfOPteIbaEr3HW11yWUHK4zyM14uVvZCFHsSTLpEnwQ+uZYsm8xQ97Zj20OtNtj52IxUpfzDwkQem0nlki6bWHDzmnP7tET93pxQy3rqdrImDiUQv7Hy3H79l3kzg2YjHT08W9N42SXnvwJ3n8zvRpIskjdoUB7yc7nsOuYDg7XDedcAJX9zt8g5d/wy0n1o6bdkvA7uKmMbd6d71JSdhvsa0AH2S/ZWaJJXEP5hhwD3awKu3GZt3SCPyb9WyxtGWOWBqxm0rArzHSlrliSTMfA7KxeuygtzrdjVG3NIql+egKiw7e9DweXC7T7oUIiNgfFatKiFHFqWgss//T6ddGKl1dINck0NITubGFCYXQABWakgq69njKZ+RZ2t76dbFEH4fnkuhojqCOuB0sWXLe0AXrTAtilnf3ORlvoDSq9KisiLQI4hvJYoL9PfccXEICQ/KTsrh2D1msxK9C1UDMUyExT5r0gvz0bFoaxZunV5n5YkkFWfD2bbxHT1Ykh+YjlWhmgRib3WbeEEumE15c2IEX6a1uEoqtJC5/GruZ6aCkXUsOi6QP3i8pPmHTSe0ElwTPAO/AcDdYR/tum3+2dzOB6GY3iyTN3GbswNbzAbNp67cwq2bYmvFefVsnUqkw7n43hKqCZd5useuBV2fCrA3VbiWyYlUzGIhTpcuis9M1GmJaHSjwToxQKWPVyngFKECZHputyknMBz09VgTOWtcqn3JZk7KZ+FXNeaWldHl5XiNcpe35tELo/ii1KlYdTSKMCFCTwIN4j6dI/HwFTuUZsnOyswp5Byxnf5aat/oQSmJMgoC5nQTzgtN8HMWaW7OjKLgUWx5WElzmT3zafyV0N2tJLH1fjNZrSQwAj8XreLhVKzYnrNuFHa3ZbPvebAqbsfcZ4pSRi4fi1hl65nXAAOAl7BjEsLX2veWUtHx7cOR7NlOpE9CYXltKVx82NMBlOo39J3meeHv8FCLJabKOXI/n8Zs1Y6dZczb4Bh6D3Tkz4J7nQoGdbs9rdD7UmdJy4x4XJJRwI4wSa3WCkHcFEchw604ijztDStps2eP2vScoWKt3rQ6iawOa9p8AovzyjQiR8az/O4lspR3sFyaUqI0oCcjZSWgSvF8RK1fLo5RRIdXuBpc+JNHKeGV0LAnoTiwUSzsGoWgAYyV9GDszeBHpLYL1N4kckqduLRJLquIMbJKRXfkIrZdI3ltie+vFRE4IAvEMXlJi+iWzY4hkVvhp3AYIFilPUNPSnxvGE4RgGZWp0qo0CUQw9xeScKJMW1I+VDCHTG5i5Z8xy61jiMF0oxKyI/N5Xu4wSaIb+KmC/YZKYol/WUwTL4ZpuVm0Jl2rydTQRIDxXXMihauNlDNum7jutewy5jfKF2RxIdGRKnmUSkG8+3Pje0u68UPq/NGakzaSfIIEAXYQ4P3EScRP8FRmiVjiYJJWyiStq4i0ZvDk7NoRVHTCFlff7FiCX7TnXiQCyjVQsI4QNpCukx1LPEkIe8aYlpLznEBQHsEKrJcSlC8jqs+VkuQbtydgR7IlDDccVnDccsyRD3hMbwnxD4hx8eqDuM5DWMBWjluFuRmwmuO2EkgxiTd6S57Ah7jzMtf5hDxQ2gdvZc1ZYeILYy0BWsvNWotn8fOKOS03ijMQI3QNv36Ne10oKeClY2bfHpaLJW7Qie8/FODT1ivEEv/FnbjpIW7qtMIicvJto+/wFzl/+2Km041gICDMUGmL36agUXVYkafURGeqD2UfyknT6YiJPwyNNBmrNBhKbaQPGjrXizzAKyY4OCQ0ygveo7nNlG9EaICN27QGUWlhqK8tuQHv5XoVh5YE18TUEdx59RksY2aLN0v2f3ZRgzZVjrfad/ftqQu34w+Zh25UQHgR2RKZ6v3ompCi4BJPrQfQG6Y2ELVaqg8jq5DdumqC8hTZ0fn7sw+oDyjiVCqFQhUErjQZ8w0PC7CRdrlNbRfVQa22pKS4SFcD79Nkxkp9UakNiesCDV0EXytwI7P6I2FIiFwdCIEQmRWmkeUGZXuAM3hGODnRV9YIi4vzs4/AESiMNajKokrUddAMtYVtbbQxm0wQz14hMeY5bGlKJ79JuN6kwPh0MvsdM0EsKSP0VOrxbL2gqTuvgxdLLUPUsfygnGhqlTJeTrSCIiNWF12cdAo+g8+ST+mKs3R5fMRbAFoSrmuiUwgM/UR08Liq1bsirDAkOzCVfjB/OuWV4B8V6u/tnPgmzIE5qW9WOxf5l0fVJNB2HX9Q5ekl2QUF5WW64ylkuz+RuCK4A3t3CTp5B2QR8XUDcPBXHQuwJXWhrvq4TR3kyzXR9K3PhV517xq2kCBGMG8RN9yWk6ydf5m7RTziF0iEMbbjqw48tkvwAxbiV/iJnElQtFLMezf45Y67X+GjhNj2RMWO4l0pXh5qV+Ja8uEBd2EDwII7oHsPXCcBzH8EwDl2gxA7PuqY1Ejlvv8twFcbCf97LTX0nOtnGZbE7Y/suUdUd29G3yfJFe5tYji/xNMELd1YStYajYsZ+DG+et/RaF2QLjyTXloplGdEaeM1+7MOaCAX8tJysrOzsnIITnXEhmWrNKpUBcj5gPtkRJN3qazYQ+tCqL4nyt9fJovkM2o+6cHaCE1EodygpFWJsQlxCXs8EtxhKSxO2dm6W6csUBXE0Td9hfnxuaosZYY6VQnREJkQHRurVsckRpAvUWlqjUq7PxcKaA6JwCnfo8KvTHZc0QIt0Kwtr6ioyK+D01CaUKosijEodGE5tOZQZnpGenN9WhPcJLqVxNXuIeGqWD5LpYDoNFVWiJ5o4CZoSa4vKtRkGbMieeRAGnVxGOzjsyLsw8eCDx9//tgM/8A+ZB5PnEi9Zc3/L2FDEq6w968I2m/gDR/hb2+Y9RwmQcjb4mXWq8Tc+lnMahFEHoyKUyfExe7nU0GxGQlZMYUHW+ESXEpp1RamZ2VmGFNBWQnpsRnqlGjCkR+JkhpUFRGFkQUhWX7gDqEKebi/J7wNa8j1drlnfniR4ijQFVBMGLGwUFuR3EBLvPFxg/w6DsWjrfCuu9IAfAy3MbiZgpaoqtCikGKvbE/wAM/osLCAgFhf8KTviiAb8jTajIzs9Bygj+giZLbb9hHPSqYKiwwIifYhNsUl068gnJbWFwWVxNZCAeSnEfBUTVZ2Di0N8CIOqhXjGR4W7BddXG0DeekFmmxNdt4hgr0ykeRkqYF1Ivy2rpRZLSZufAoxig7rfvzwB8CO3Cwumc8DjTXcI276xS6+BjOWwR4cOwFncsOGoHXHFXxzKFoXcjuZLXiJMCM1Iy0t3VCUWkoc22MJFeEGRV5QpjfQ43ptruBZtaIT3CxhLK5gICyJeGEJCXGJKqDdsKhBpIHMxHSiA1IUEE6o+8cNAf6c/YO5wR1cRkla2QkPsO2PguMPsM0DM9aMvcTcW3LT0XHJEkfHm0vu3bt5857NdGs3Xz93d9+KhobKyqNHK33diP7L4ZMJg3L815jxYvwCd64vt+WEj17E8fh5AX7vlhkux0eZi9wkClvgFcL8I9paqITaqNKw/ND8wAwv8Abf2LDI0Eh1KLjTsi8qqLLc3CMl+VGBNuAXF6oMp4l/5QxbhGTWamyF1+PnBE23bn1lhsvYyQyUHyxRGuJyYrKjM3IysjMztdrClCKib+vVlSH6yLyA7H1Ae4E/WSHMtEL4rRIKcg5pMsi/rENayIEsgiCNCsJ4NQrBSmV8QtyBGKCDNh8W5UJ2YkZsdkxyCIRC2MEAgtt4PtMZA6rU+Iy4jAPZRDj1b0ZS4KIN08sNkVXqaqiGymxDflVpSjkco/uioKs93swaseS8ydNRGr2Bk7yPd4I1s2ZL7Iln9rQOYcmHNHgdJe04lJiVmA1FxviiP/11gjgNJEYkJjuPsJSqu4sosFZiNv7O9Aqo17Zs4KZx085t+NH2dK9Pd09915/1IslrOBDPEpzDs8ywLw5kiB9ALuIWbzdgIaFhoiuzVswTzcBa85mHqWbYk9CMXbSIWrQTFn3eBtyvQoknbjKwY/SCqrv37prhkyTshuuJHwXUBdV4ZbuAC3iqg7wCPBLXwHJYnrqm1OOwV636GNDHoCb7cE1pXepHcJ2eaQ0uSZtiwuNVqgReScSkx2tiDEnn+ERt8jmtIUOj5UU0BzQJGSptePImcKH75Yt9Hpcz3CycTNT+JYf1rwHnIAzFKf23PnRYZ7wlYecB3sZZt/Nl2nY8ZdEX7OIvpE1sG7En68TSEHbzZabdi5I2XV9ESUOWr6S68fm3gBNw52EDngBCadPaL8jAZao3BZMQgQBVL+LvrhfzM1/sGdk/q9Mt6YO+Wdm11he5iRQWD2LwaCODB/wLg98iDK4bYPB4I4O/YGRw7gMj0iniiZMFH5gWNB3FC4+I0lvxbM+iW9LavmX9/6sA0NJCkwwMEQGp//9nIWBlXKcR6bXX36Sk/svfpp7g84uBGzaA9Nq1t8jAZUryIbcFb2UnEp+oujvF5BV1saJkxl4EsiS/yHBVTHR8JAk0lIQ/FAWJh6EWGlIPF5XTF28TnBWU5FRAFX3dfhMFwXFhUeH0xoXCYB+1B2yF5TnOtZ5l/k3yM3AWjhrKjtC11drT0E5Lu+zZibyjNZA5fp+I7KDkcSsO4FMYA8N4wZDcMqYwxwv3v45X9CQzG8TJvbl8BvrpsE3fcF3PB8xG8ezea0OGLzwW/Mwb4MEr2GD5YFv8dKrovqkSen5lNomde/9p3O5nzGaxBH8EXW1dF41pX/wjcbV6rPj8JHbty/jiDV29FMXp/nTClnAaT8KOxKGaJMQp5txzfHnlH0LJN7yGmPfXBDY3gYvkFcZAppk/70CyGe8kh571b0cNfx/D4+rfjLYZEfZ09MJjImzmU3768rH07yYIukfDPKakf0ycKMJlJmz0JYF5og3kgbG5aRfPHFts2kPf2MAOjGM7TDvoGyPrhz6OxOYRj0nAw49LnxTwGyDL9q+OnyOb9bqL3bsEt/oT+lufkdDnLr878xT3yf8lq4+9rt9djt3/JbXflxvm+WdIerjdyEd9SWJMArln5Ym92Ek8QkxAjQQfzwKqZxt5zPznmS6RmTYMAFk/G+gTdvfTPZ18zKeZ1xOu3oTNpb8Z4f5rgrmvLnAVGo9U1NBn2pN+J4/QxsmlP19gHQcLxINfjd0KhEHfZDgaG6+VVzmao8g3auVKTHPG6+pK0xhHE6fM2ZDLV1rz+g8gNGPnGzdmnfzHe+10/W+LIXyrMETrn+H51w2OSbJ7fwO9b/xnUPiBsDimPL4WeKpW1Axs054ILmfZKxQmlibVw+UhFZJiyIvTKOkybj+hvPWBpWFeaoVcFQAhoMhU59DYkjUXpgYQf3wd3ZeS5nn0L1lp7kX2a56gJgCewf8CsKbHk6flv53AlTy/oX+cMDoZfptQaIWRQjwIW8OZ9zZS7/oGubv7HD5lwzaS7zUUbCvYXelT49lKFOkZaMmvqaUrqwpa4Cwt/ZnzZ38cIh2HB0RscN66vQfxW+MrFxK+coEp/aDiBf6zv3jxNn6xr94z7q/1i9OmIvLpbjyc+D/PYR8GPj5wNLwmNi9ap9DwVfLMvLyq5BpiHz6Ib3Vt9Ktw0TgD7QwuClmAv0zhzVcqP+hmL1DHiw2VNoXGfghdRLIf+ENwsm9UeLxaFa8AOs4xU8QXUjNjconphN2wM3lvZKgqOvxAFNBTqWZoyayuamuBi3CDHkhyn8a2VoeNee4o3GXN0hScj2yQ8QVTjbE3JTIoyJsvcLvwAWRRWq5Gm5ml4WOx6oJQN9slTtR68FIGhvjI5C4kktye7VoURPy+I77lyma+ZJJBgplMjS6LxCZRfcnzE3uCA3w8Io602YAh80iONleXl1ICdK0oGf8xOP3Op5tL+3ZmzMCfmCN+Oslc8TVWzVPmv4HfYb8lvKPuXWZ86Cn42GeD3+9hiDJ4tVc+FPzDxwR6EeG5md3SSNMDbeSByL7ttM4VS4/fZdFgQR9YKd600gdbu6Xyp2vJd1PSEyd6VjIXnFo3bdq1a9OmVqcLNhfa2i7Ybu2tZuaKE4la4m1jbFznVDz39ufXscP0B6Y9k5/RVP8lbWY/wtFM0qPUP94961vtnuVM3Div2OCIhP37E8iVGZ8Rnx6S75PhzXde7BlCUH1qXjbx0+sys7Xp+UBX6sPcbBf/laYaV0NQqW8ZIWg1lGQW6vI0Ofk5eloTqYvKUx2Wl5MotBWOaUsraGn4FrG0Wa1Uxhn9nqw4rbLwQCvvt4TbLyRHwn7GcoLeaKv/pcLBCbl3eGPdX4ogrCn41liNIJHo02qEN/GtZw2qWPBgzyhafMjO5NljAIzwx1/AdhJ+2yBO4Mr5PQ3AEUrXdS8glJ7bLe0ZMqP0j29xw2AKGzP6cn072cAZMu1M663iLlMJ6Rkj+E0SVznAUs72MxD2A+CV+qcA28izk7Dt6zCJs+2A9q8vzhuAu/7YyudxVLeCt+08rPQJ3jw051EXZ2DP3F6Ix1nVd2MhX2D5B04kBkIQey6ynfbtagO4vZx4ry2hV+zIn1vFq98FWPg5VLwmVGiC0vmykb8qOMDT7cBKsIe56UuqXSrcjilPwSk4nlPRQFcfS78J39L2KQwsO7g7OjROKTfWXRQZcTnRRQdb4AbcSGnVFWXm5GUYMzX58ZlKXWjKLlhGDyTy8at9ufwOUy7fWAFYJP7JKHMSY0K/j+jmhOhTB3L63vgLo59mhOgj5NCs/yV2r9FX+/cQ35E5NgxAjH0GxH12JL8NE8QAG7zeLf3NCAT3Eju8W32P79G+C+/CnhjfPd67EueDIzimzq/eVbnnOEEWzWOr8jhd3ZraAfdoflbpz9/g74Y4AFXY8rOum9hSgBfgUbynGmnKRWDAS4yNPb0U39ZSwRt9shNi9OueYNETPulxlVkGYe8KQzWBqf5DNzHn0PzKXfTeKTfAcEpYpDqSWA6mvZi2AncOdPi20jOcmBlcqPBgdhJv2r9KPpt7OCuXJ5iO7zdS03puHgMLkrZFBcVGKeIUEA3R6bHZ9H0cKjykhkAIeVohIKxrKhJEmYoEJ5aJ+XpilEkA+qGIBDwL6oYpx/Bf5vrOqLoHoIgQnOieS0gyi+hgE2AbAQx1pab3yYK0rd+Y99UJeAHsKxV82VPDL9nWV8BJ4uuogjO/ElkxY8Wm6tABY3XI31gdCn9aHcIh4//BDC4O6Y3FoVJtSXnt8UN9tSFYeHBbdHBsdBSRCiVEp8ZmEdA8KKZxOAXF6XkaTVaWLi0H8iA3LitaF5yyHRbS/du8/ljwgTGOMm11cc/UIZ5JnxIY0BSSSmMnh9URPNPY+XKHpY3h0kHqwZfwZPpCwDlC6TXOnPtNJL3j5Ab25FtbI3DZQslrpjpfEXa077Ynj8/EK6TncdrTch9gf6H00pk2eCSU3tnuBDiSPGzPXSUzvbMLJpIvp9uAsxHCWoOpINhoKghWxnVN74uEpvd3aiBpGNv110COawogwXWE3jPXA9bDnmB/D6fN++1MvnSGfcumMo/m4EtQB9U6g4GWhgVQ8byja/I9aWmtyf389y09fW0aBq9DS8lDZGMpJvN5G1s/tDKKPPkZLeEv6Sn2c6PhPDTIcPqogiPi98eTn8SM/ekJacF6T40H0P9O4OugRltckJGenp6WSafFp8Vn7C+OqIypJxaxyWgRlcQinlIrY4wWMfqpRbSvZBaLsX8bwzecn01t1OZmabVZhVAI2tisGG1Uqitsowe6Q/rjm0ENInt7NjLbxP0VQLxQP6QIGMle40cP94eG5x9HYPNIYj763Frpk+lszBAmM/Vn8FVjY4sGPvN3hp/BVGDDy8ntM9YrxLieuOrkdjJfDvL9Cqs6Bbizx5zpdKaWwDIX+au0N7f1bYAFp8GxV/0E7rHqHwC+eguq8XphWkWJvgL4PKPhFzLfA5N4sx2GR/w308TsZzeXYAsr6Vv4Vs9aZplIqjrgFcyHmtK3SNShPVBDLxOlzjrn8xHQH8H5qtTvyXS/PRbgVx+Z4RT2N+bxJL4A8I0Bv0HmPE/cq03ijdbEjepPIDZw25n1ePpKOMNNF0rYHw2XyM1HJgPEdmB7AXbr8SJ22omzvw5CrML25Msuzv4a8LnxWGOHZGxf7hg/5m/0TDPe+AffEf4ie9+A5/S3jrP3me18PzmbZuDfnRi4icUk2NvDg88ZBG1tvO1MSc4+neQmpvm20/tEOzlz9BzgxMIl1mTOs0+nHAAhj9Nk1iXfDcx/ds7QeayNIIRSd5j+te4QzTLnrpHqSx+ZsZPZNcxdbgxeN4lbx42Zicf0rnnEriFgvNZ8/R7+bRCo9T0C+BseMwSe+40b40iRv+se4XUiydd4kYEvk7HW+HvmhyU3HRyWLHFwuLnkhx9u3vzBRoKP93XQf2/8MWN/sp4g5k6S//B3fEd9z5K4WysMWH+LRMyD37W48l3Hd9K6O0NfuOjZzGEGP099ualp5cqNe9+w4Z6n3ri49+rVC01f2kjD7/T6sD//9XWMni29EQyZaZyY+5Rj+BczuOcHl0iefnV9mtFtd9hgSt9ycX19TmHGHOd/rhpiPS4z4N+vCH6+ji2v4ynXzdjbuIxhV62lXudC5+HQtQC9q64DUd4fcG80wjm8CLg8IVfC0swVzmf1p730dRKe/ctbCI+M1f5P8HBVX3cO31RlLr3f36LTuaRUJP38VLH+5F9bdLz7WnQ6+RYd8kBU72aGuJYPsfmgfhT8cn83Scyg+VdIP++f/0vj/M9qAeqf/0vj/J/h82R+Ymc6h7SVDLUi735uyL6PV/6g1Qse38cr7puxMUQAt5wT5h7JriDhR7W6MuoIfe7c6o83bxEG5vpleROV7RcbEEVvObdq1fktQkVgrC+55Z3llxdIb97y8apz54SlURXGLG9F9pFcmsBsxh7MTlfXnTtdG0+caGwiv3t32kjYcaNJ/BaXyZZk4uWZIm5bJmUjnjNnw4jhMELcJe46OmJEt0V31oiRL6FpUjSXf21uOBqH3kCbkSeKQvGoAB0RjBZMFjgI1gniBe8LPhr2zrADw0qGnRzWbiYxG2u2wCzQ7EvzkebvmUeYq80LzavMvxE+JzwqbBF+JxKKbEVpogbRTdE90S+iPyhEWVAvUlupfVQEdZBKo/RUJbbkRnGWlAXP4RuHcrjN1YtNX9o+a4QfsLHwVUQHB8t1lTaYoSrzdSUledG+fXcVfXfz+u92cS/gFyZwL3AvTMDkvy78AlnSkrPkl+YW9zhxAuCW/umExVCG7fE8KOfshRacBC7hU+SvuSgpMDEwJlKtUKj/k5WmSyFXna3QRqYGJQcam28vh5/1rPapcM/b9jSBNbYvw/bKQIbtDBzNqyqvqzZ8kPIRbfFXz+L0n06UBd9/2/+3dwHFLWAXTBxoYF2A3+hdILT43xJ6/YkyiyEZsP94ON4H6XdBLNbGbudbPqP4ls/ta23J6iNCWub8T6+1+Ze55G4mDr+rwjfg377Rlmh8o03+zDfatM41XhV+rfIP4TK0FZaX9b3KZvF/Tpz15c0sjJ2ftctxLTcKsE3vcuCqjG3GVUJCjm0UCWJL4FvuFMAEvBq47b0lMFdosRTbOsL3nK3QYiDBFFC2V+MMf81H/PcEE5+MoC1M6Ygh+SWLf0kr5acU82klC1iY9D+55sm8a24xVpQUeTBSFR2nUsZH9retHE46A7fhdvIZXUmWLi/T2LbCJ9w00SkRyXzbyoEW1fveVb7lnpm7YQc4P/UZHYjPuLtyT7PyJNBtcFRbWlperms5dJJwVUiCQqWKjY1OVJJlovhlSpLOPl0ml18mB3T7s2I1fDtiCG0xONezB9zVPj67dyfOhCkwJXVmy+4qn2oV0aYmD7SiWtuc0sz3RCr2h8Wpn+GJWnBmz/RAD/EeqEUD32/GEuF/ZsNjXyufxeBGR4v/IVDui5MHSKToyyn8FQdGVOcR0XhKH6iLq4jWyw8H53vk0csrhJGZ0Zp4TULWwSwgpEzT8U0wuoEmmGxVWjTB3O8iOCOvl5UGHHHV8enNvXwTjH+kN9mnf1qwNiI3+EhYnYK+7issjNOpM2MyYw7F8G9d7VeoY2NUMYlyEhEr0tSaGG1CPhh4rO7I21fpUyprjuJbXY5qqqpKS/VH4TSRLGOl3GLgxQ/W0r53VN/bIiWycuOrIqZauY6vlVv8paGuOHafULnDM3FXEj2N+gk+1x7ppi06uekUno674UtOBzANEwU5neuGBSbN66TauYmoJblWnRtVlHgk6RO6u4MTfIUFHfai5E9Sj+iKsvMKiH66AJr3kp0Ga103o9Z1I2rQ2z1qy0FHetP8cwu2zL8gSnHUbalxr/BqINpwqO7FTtwcPIdzItcczviXfJtDohXyl1xONhbcm5yUXK+TS4ql+E1y8X9fJxd//00boggd1v94ydh7MptLFlo49mXHLFbCem76F+QDcdeSjO6axVsD1X2Lpzmyi1+3zxNaOLUJw8NjDiiNXcBRWaE58tQQ8IXQGE9PumWXUK/XHtIR4c4hkXuRMj+xGCqhWFNbS1sMzrzyiVcbi6CwsKCgcMPhw3ryawgLsrH4i/yY6FpCWcyHDfM2fj0ffuHsvgXeojhzlNE/t/h/0vZoJgAAAHicY2BkYGDgAWIxIGZiYATC50DMAuYxAAANiwEWAAAAAAEAAAAKABwAHgABbGF0bgAIAAQAAAAA//8AAAAAAAB4nLWXW0yb5xnHH5tDMMQ2zdy4aquszVjHlh4YC4GyXeVAWbdSSigQa5q6i16l6kVVVdM0CWNM1FsCiHXRFMIhQG4aTnKT9Ka4RVPqpDQLhMxLSLKVRo1JXJPYX2wn737fC0E5NKStNlk/vf5Oz/N/Dt/7vp9YRCRX1snTYnnjD2+/KTmSyRlRSswrlp2vv2Wek8Ujrln1mCOW/M36zirZLwflhPzH0m1JW6us72YUZ7yW4cuUTF/mh1lPZYWyzma3Zx9f9cecNTkNtkdthbZyW6Xt97aDtqO2ydxnc8ts5blVuf/KjeVJni8vvfrN1VH7GvtrsMZ+2H7Y8Yhj3dLvV446x+8c7zjaF3/Yuefn6IZR51rnG86j+YWro6idUjMyAzlSok5JqbosZWpBytW0eFVKmsEPLeoc0ZxUp7n/tJyCGXhO7OLk95Cs5/kClZZC2CAuKWIsho2Sgd0p2YSnUjUrZYzl6p+yWVbJVnJYAZVQDa+qhNSri9IAHtjF862wG9qgHTrgL9h4D/4Ke2A/z/bDAAzCARiCYRiBURiDcZ4NMk6hZwbWotqC6gSqEyhOoDiBwgwUXpRGrnmhCXzQDH5oURdQlkBZAmUJlCVQlkBZQjrVTbwkiH+9uo7lJJaTWE5iOSmNnPNCE/igGfywaDGJxSQWk1hMYjGJxaR0ig2LSXFoixvQVaKuSik6yiRfnlcRtC6saPlnYlfD4qCGTjWClaNSQM4KYYPaJUWMxbAZS1vVJepxiXpcoh6XZDvn6qCRZ7zQBD5oBj+0qD2yi2dbYTe0QTt0QCfsJd9dsA+6oQd6oQ+bA2R5GE0B/Ixzb5AxD3VxKSG2Uq6UcWcjx15oAh80gx/MuHKonpUqWamSlSpZqZKVKlmpklXf8UOinueubCqRohIpHalpfyu2K6ASqqFBRXXHNXKvF5rAB83ghxa83T/SVdLDDNELAWyN4yfI+ATeA3h3430e7/P0wRX6IEIFjxPh34nwPNU7JnV48KhJvLvx7sa7G+9uvLvx7sa7lf6I0B8R+iNCf0Tojwj9EZEPePYQHIYjMM65KfU+/f3+g3qG99kLTeCDZvCDmblH6Zm/oT9Jz3RhZQIrfnokTuYWyNwCmVsgc2bnTVChCSo0QYUmqNAEFZrQvdGFxx7ohT6e1TXn/xgEeDYIP8XLZTKkyJAiQ4oMKfRelE1oL1U30JyH3q+XvEfxHsV7FO9RutMgM4rMKDKjyIwiM4rMKNlDzF2MPdALpoL9PNMPAzAIB2AIhmEERmEMDmH3CIzzXJDjTBQp1CjU2IntK3mGDE2ifZoMnZDH1Bl5HNarELHEiCVGxg4RT4x4YuT+SZ58Usphs/qIOILEESSOIHEEedOOSy226rBVDw3ggZ1qjgyHyHCIDIfIcIgMh8hwCBUfEHuM2GPEHiP2GLHHiD3GPBQj3o/IcpAYYsQQlFlsOVB9DcVx/e5U0aUvwyuw3XzbVniXzFnkhH6fnOok8caJFyvM6AVUtRCKoBj0jK/jnCPOOeKcI845eZH3o5pxcVbhfeO+nbwRjdjwQhP4oBn80EIX7eKeVtgNbdAOHdAJfdgKwDj/g4wfM85i7ymUhlG6gNIzKI2iNKrjLWBeLjTXJiiGjaxbWqmeEVIoTaE0taQ0Jds5f/+cmOpcqHOhzoU6F+pcqHOhziV9usdTqHOhLoW6h/g5mOVu9cet3qigItVYrAMP/1eqqVnLIuL7fKnz/nFbJcJYNrBsYNnAsiEbWY0WKzFNfNOyDSqgEl6Uh6n/Bep/gVin6YELxJtBF4aozpd04VW68CoVilGhMBUKU6EwFQpToTAVCi/mAD+tsBvaoB06oBP6sBuAcf4HGT9Gzyz2MqSeWBs48lClTHzNc2RwdJ25a968zlED66iHrsxaPtrBTGGemcKGi4gMojGIxiCabPkNvATVHNeS/b082QX7oBt6oBcGuGeYcQwCWpkhU/zP1itCD6p6YYCZZ5jjMbDgMco8bKcvHOoL9jt5spWz26ACKqEaaqDW3I8w9sMADMIBGAJz7huB0aU58Ahre5DxE54hH3ovFr0j3tRyvD+i6p/hPULVJ6moQfxxKhpHdZwcxIk7LjVgxl6P0gbedg/jXu7tgn3QDT3Qq6tjMB/GmQ+ZmWEQDsAQDMMIjMIYBFjjgoyfmLkgU6bGG2i8ITs449EZukzdHkOt2Y1TnHkCxWdRe46zSc4mUZ3WVdt2W+XMatXoiq20X0ij1ECpgVIDpQZKDZQaKDVQaqDUQKmxXFEzp+tQcB4F/17ynMbznet+DdQSzcqe03hO4zmN5zSe03hO6+4YgdGlLgno3WUaz1ny+AM8Wv7HHhezfYZYZ8n2NV2DlRU8KNvfVYHV3I/plTvKOnmFFTvGnKGYKxRzhWKuuCI/WP5aKOCtL4QiKKafSpi/N+l931l2RRd5+st7vhDu/jpY+52tbeG9vuObg3N3WzW/Ddz/N8vmV4eFtcjO9ZX2Ztn3vWLR+9nspXX82j1r+KrlK1s4uvuqObPeRJFC0U09896aaa7rmeYRnvwcZaOsGzn4zte+y8nEN60h5rpRA9upvrl7qaf25k56h95NR6n/p9T/PPX/lPr367XgYb163fKyolU65putHsPqMawew+pBbdX9vXR/W813W3di3Yl15/ewfrv2P2vr2VQ0n1p/RledoKvC1HmSPb+TWlfRKQ7+rWf+L5Afy0/oxg3yNLvPIvm5/IIdTAn9WIqe59HzS3pxCyvSC6xIv5bfysv04StUp1ZeZUVoYK72yJ/Y0XjZzfjYyfjZwbSye2lj59LBruU9vqb3smb2sE72sYr1s4INsnoNsXKNsGqNsVqdZG4/JTNyWs79F4QaTCgAeJxjYGBgZACCW5OYjEH0wY2vamE0AEUyB1UAAHicbZM/aNNBHMXfu0ShdLAarG2KaGuMSVtNW1uJ+KsgWK3WP4s4dNHFUZCiOFVFqNZYjF1EIhajuAiCIC4OSqcaFYWi6KAiiBiLk1XEKb7v9RcIxeHDu1y+d/e99+6HZwAeAiQO8BEG3VFkOI2k+yCdRxv+YJB16BJpFtHBEjrxFVu5ARnMIct2LOUk2sUu0SnSoimcS4nWUFP+DGMSbbaP13vI+jPfYAnfI+BT1ZlOiAtiRr+/IEBZ4zFE+FZ6FoE7LJ0Xr/T/i1A/SR9jHS9hJd9hi+4Tdc8R5QPUay14CmsYR5/vOY7VfI2N6gPM45Bo4XX08hwS0gRzYhliflxEL2axCbOVH7yl8S90u5Oq1bz6TPh1VtchHdU+K7SuiG6No26Hzr+IOp6RDiDOgzp3L054LWFV6H2W4/LtJdbypxhTvdXcV395bI/s072G0Mwp+VrCZv87j/0sLOTBfq3tR1JzBd5FjxtWLw3aM48RzQfcoz2nkIksRxCSlO+t3vP/4O7IH8thIswhRBnUWw4oV8pijt91n2oGiyl5b1M+h1osB8tLPqq/Zu/7ItR/i8/BcsmFWQj5/1c59Ek/is+8ivU+A6uzHGoxX3Ly2HKoxXK4oTcgtb14WXdoQKM7j7Q7pnX2fizD6nsZ0Pi4fyPbQobENXFE3BSWxW47z83om9C3UYW3a5hWP8YTvd0ryEbjqv2GJpHBaKVgubvT8kPfld5ljL8Rc13Y+Q/p6ryG) format("woff")
}

body::-webkit-scrollbar {
    width: 8px;
    background: transparent
}

body::-webkit-scrollbar:horizontal {
    height: 8px
}

body::-webkit-scrollbar-thumb {
    border-radius: 2px;
    width: 6px;
    background: #e6e6e6
}

body::-webkit-scrollbar-thumb:hover {
    background: #ccc
}

body .td-checkbox {
    cursor: pointer
}

body .td-checkbox .td-checkbox__inner {
    width: 14px;
    height: 14px;
    background: #fff;
    border-color: #e6e6e6
}

body .td-checkbox .td-checkbox__inner:hover {
    border-color: var(--color-primary)
}

body .td-checkbox .td-checkbox__inner:checked:before {
    left: 2px;
    width: 7px;
    border-width: 2px;
    border-color: var(--color-primary)
}

body .td-checkbox.is-indeterminate .td-checkbox__inner {
    background: unset;
    border-color: var(--color-border)
}

body .td-checkbox.is-indeterminate .td-checkbox__inner:before {
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: var(--color-primary);
    border: unset;
    transform: translate3d(-50%, -50%, 0)
}

body .td-checkbox.is-indeterminate .td-checkbox__inner:hover {
    border-color: var(--color-primary)
}

body .td-checkbox.is-disabled {
    cursor: default
}

body .td-checkbox.is-disabled .td-checkbox__inner, body .td-checkbox.is-disabled .td-checkbox__inner:checked {
    background: #f5f5f5;
    border: 1px solid #e6e6e6
}

body .td-checkbox.is-disabled .td-checkbox__inner:checked:before {
    border-color: #ccc
}

body .td-checkbox.is-disabled.is-indeterminate .td-checkbox__inner {
    background: #f5f5f5;
    border: 1px solid #e6e6e6
}

body .td-checkbox.is-disabled.is-indeterminate .td-checkbox__inner:before {
    background: #ccc
}

body .td-icon-success:before {
    content: "\E7A8"
}

body .td-dialog__close {
    -webkit-app-region: no-drag
}

body .td-radio__inner, body .td-select-group, body .td-select-group:hover {
    border-color: var(--color-border)
}

body .td-radio__inner {
    width: 14px;
    height: 14px
}

body .td-radio__inner:checked {
    border-color: var(--color-border)
}

body .td-radio__inner:checked:after {
    width: 8px;
    height: 8px
}

body .td-table {
    background: unset
}

body .td-table th {
    height: 23px;
    border-bottom: unset
}

body .td-table td {
    height: 28px;
    border-bottom: unset
}

body .td-button.td-button--large {
    font-size: 14px
}

body .td-button.td-button--other {
    border-color: #ccccc0
}

body .td-button.td-button--other:hover {
    color: var(--color-primary)
}

body .td-button.td-button--other:active {
    background: var(--background-secondary);
    border-color: var(--color-primary)
}

.xly-line {
    width: 1px;
    height: 10px;
    margin: 0 4px;
    background: #e6e6e6
}

.xly-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%
}

.xly-empty i.xly-icon-logo {
    font-size: 120px;
    color: #e6e6e6
}

.xly-404, .xly-error {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%
}

.xly-error {
    flex-direction: column
}

.xly-error p {
    margin: 20px 0 22px;
    color: var(--color-secondary)
}

.xly-error .td-button {
    margin: 0 6px;
    width: 72px
}

.xly-error__button {
    display: flex
}

.xly-error__button .td-button.td-button--other {
    line-height: 23px
}

.xly-select {
    border: 1px solid var(--color-border);
    border-radius: 4px
}

body .td-cover .td-icon-error, body .td-cover .td-icon-warning {
    font-family: xly-icon !important
}

body .td-cover .td-icon-error:before, body .td-cover .td-icon-warning:before {
    content: "\E7A5"
}

body .td-cover .td-icon-success:before {
    font-family: xly-icon !important;
    content: "\E7B6"
}

body .td-cover.td-cover--message {
    justify-content: flex-end
}

body .td-cover .td-message {
    margin-bottom: 66px
}

body .td-cover .td-message__text {
    margin-left: 12px
}

body .td-cover .td-message--error {
    color: #fff;
    background: #e66056
}

body .td-cover .td-message--warning {
    color: #fff;
    background: #ff9700
}

body .td-cover .td-message--success {
    color: #fff;
    background: #38b878
}

body .td-button.td-button--other {
    font-size: 12px
}

body .td-button.td-button--other:hover {
    border-color: #ccc
}

body .td-button.td-button--other:active {
    border: 1px solid #c2d7ff
}

body .td-button.td-button--other.is-disabled {
    color: #ccc;
    background: transparent;
    border-color: #e6e6e6
}

body .td-icon-close:before {
    font-family: xly-icon;
    content: "\E768"
}

.xly-tips {
    left: 50%;
    bottom: 28px;
    padding: 0 12px;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    line-height: 30px;
    background: #46423c;
    transform: translateX(-50%);
    z-index: 2
}

.xly-more-drop, .xly-tips {
    display: none;
    position: absolute;
    box-shadow: 0 0 10px 0 rgba(26, 26, 26, .2);
    border-radius: 4px
}

.xly-more-drop {
    top: 24px;
    left: 0;
    min-width: 100px;
    padding: 6px 0;
    width: 76px;
    background: #fff
}

.xly-more-drop li {
    padding-left: 20px;
    height: 30px;
    line-height: 30px;
    cursor: default
}

.xly-more-drop li:hover {
    background: var(--background)
}

.xly-point {
    display: inline-block;
    width: 12px;
    vertical-align: top
}

.xly-point:before {
    content: "";
    animation: content 1.5s infinite both
}

@keyframes content {
    0% {
        content: ""
    }
    25% {
        content: "."
    }
    50% {
        content: ".."
    }
    75% {
        content: "..."
    }
}

@font-face {
    font-family: xly-icon;
    src: url(data:application/font-woff;base64,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) format("woff")
}

[class*=" xly-icon-"], [class^=xly-icon] {
    font-family: xly-icon !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.xly-icon-add:before {
    content: "\E74A"
}

.xly-icon-menu:before {
    content: "\E74B"
}

.xly-icon-search:before {
    content: "\E74C"
}

.xly-icon-nav-find:before {
    content: "\E74D"
}

.xly-icon-nav-message:before {
    content: "\E74E"
}

.xly-icon-nav-down:before {
    content: "\E74F"
}

.xly-icon-share:before {
    content: "\E750"
}

.xly-icon-theme:before {
    content: "\E751"
}

.xly-icon-nav-cloud:before {
    content: "\E752"
}

.xly-icon-link:before {
    content: "\E661"
}

.xly-icon-send:before {
    content: "\E63E"
}

.xly-icon-face:before {
    content: "\E642"
}

.xly-icon-img:before {
    content: "\E68F"
}

.xly-icon-maximize:before {
    content: "\E753"
}

.xly-icon-minimize:before {
    content: "\E754"
}

.xly-icon-close:before {
    content: "\E755"
}

.xly-icon-message:before {
    content: "\E7B4"
}

.xly-icon-download:before {
    content: "\E75A"
}

.xly-icon-pause:before {
    content: "\E759"
}

.xly-icon-praise:before {
    content: "\E758"
}

.xly-icon-note:before {
    content: "\E757"
}

.xly-icon-more:before {
    content: "\E756"
}

.xly-icon-add-tab:before {
    content: "\E75C"
}

.xly-icon-fav:before {
    content: "\E75D"
}

.xly-icon-delete-comment:before {
    content: "\E75E"
}

.xly-icon-comment:before {
    content: "\E75F"
}

.xly-icon-resize:before {
    content: "\E761"
}

.xly-icon-arrow-right:before {
    content: "\E762"
}

.xly-icon-rename:before {
    content: "\E763"
}

.xly-icon-file:before {
    content: "\E760"
}

.xly-icon-open:before {
    content: "\E764"
}

.xly-icon-restore:before {
    content: "\E765"
}

.xly-icon-clear:before {
    content: "\E766"
}

.xly-icon-play:before {
    content: "\E767"
}

.xly-icon-close-small:before {
    content: "\E768"
}

.xly-icon-help:before {
    content: "\E769"
}

.xly-icon-clear-1:before {
    content: "\E76A"
}

.xly-icon-upload:before {
    content: "\E76B"
}

.xly-icon-retry:before {
    content: "\E76C"
}

.xly-icon-logo:before {
    content: "\E76F"
}

.xly-icon-drag:before {
    content: "\E76D"
}

.xly-icon-transport:before {
    content: "\E76E"
}

.xly-icon-sequence:before {
    content: "\E770"
}

.xly-icon-success:before {
    content: "\E771"
}

.xly-icon-refresh:before {
    content: "\E772"
}

.xly-icon-copy:before {
    content: "\E773"
}

.xly-icon-arrow-down:before {
    content: "\E774"
}

.xly-icon-play-detail:before {
    content: "\E775"
}

.xly-icon-magnifier:before {
    content: "\E77C"
}

.xly-icon-collapse:before {
    content: "\E778"
}

.xly-icon-report-fill:before {
    content: "\E779"
}

.xly-icon-comment-fill:before {
    content: "\E77A"
}

.xly-icon-quote:before {
    content: "\E77B"
}

.xly-icon-hot:before {
    content: "\E777"
}

.xly-icon-delete:before {
    content: "\E776"
}

.xly-icon-sort:before {
    content: "\E77D"
}

.xly-icon-upload-total:before {
    content: "\E77E"
}

.xly-icon-time:before {
    content: "\E77F"
}

.xly-icon-user:before {
    content: "\E780"
}

.xly-icon-peak-upload:before {
    content: "\E781"
}

.xly-icon-speed:before {
    content: "\E782"
}

.xly-icon-peak-download:before {
    content: "\E783"
}

.xly-icon-file-1:before {
    content: "\E784"
}

.xly-icon-download-total:before {
    content: "\E785"
}

.xly-icon-luky:before {
    content: "\E786"
}

.xly-icon-qzone:before {
    content: "\E787"
}

.xly-icon-qq:before {
    content: "\E788"
}

.xly-icon-net:before {
    content: "\E696"
}

.xly-icon-disk:before {
    content: "\E78A"
}

.xly-icon-refresh-1:before {
    content: "\E789"
}

.xly-icon-arrow-right-1:before {
    content: "\E78C"
}

.xly-icon-arrow-left:before {
    content: "\E78B"
}

.xly-icon-history:before {
    content: "\E78E"
}

.xly-icon-search-1:before {
    content: "\E78F"
}

.xly-icon-close-search:before {
    content: "\E790"
}

.xly-icon-success-fill:before {
    content: "\E78D"
}

.xly-icon-minimize-small:before {
    content: "\E791"
}

.xly-icon-maximize-small:before {
    content: "\E793"
}

.xly-icon-magnify:before {
    content: "\E794"
}

.xly-icon-ratio:before {
    content: "\E795"
}

.xly-icon-minify:before {
    content: "\E796"
}

.xly-icon-reduce-small:before {
    content: "\E792"
}

.xly-icon-reduce:before {
    content: "\E797"
}

.xly-icon-eye:before {
    content: "\E79C"
}

.xly-icon-eye-open:before {
    content: "\E79D"
}

.xly-icon-arrow-down-1:before {
    content: "\E798"
}

.xly-icon-alipay:before {
    content: "\E79B"
}

.xly-icon-wechat:before {
    content: "\E799"
}

.xly-icon-weibo:before {
    content: "\E79A"
}

.xly-icon-parse:before {
    content: "\E79E"
}

.xly-icon-loading {
    color: var(--color-secondary);
    animation: loading 2s linear infinite both
}

.xly-icon-loading:before {
    content: "\E79F"
}

@keyframes loading {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.xly-icon-vip:before {
    content: "\E7A0"
}

.xly-icon-super:before {
    content: "\E7A1"
}

.xly-icon-time-1:before {
    content: "\E7A2"
}

.xly-icon-speed-1:before {
    content: "\E7A3"
}

.xly-icon-together:before {
    content: "\E7A4"
}

.xly-icon-note-bold:before {
    content: "\E7A5"
}

.xly-icon-skip:before {
    content: "\E7A6"
}

.xly-icon-replace:before {
    content: "\E7A7"
}

.xly-icon-cast-screen:before {
    content: "\E7A9"
}

.xly-icon-back:before {
    content: "\E7AB"
}

.xly-icon-ratio-optimal:before {
    content: "\E7AA"
}

.xly-icon-open-file:before {
    content: "\E7AD"
}

.xly-icon-fav-fill:before {
    content: "\E7AC"
}

.xly-icon-play-small:before {
    content: "\E7AE"
}

.xly-icon-file-small:before {
    content: "\E7AF"
}

.xly-icon-open-small:before {
    content: "\E7B0"
}

.xly-icon-personal:before {
    content: "\E7B2"
}

.xly-icon-fav-file:before {
    content: "\E7B3"
}

.xly-icon-security:before {
    content: "\E7B5"
}

.xly-icon-vip-1:before {
    content: "\E7B7"
}

.xly-icon-mobile:before {
    content: "\E7B8"
}

.xly-icon-thunder:before {
    content: "\E7B9"
}

.xly-icon-bird:before {
    content: "\E7BA"
}

.xly-icon-transport-up:before {
    content: "\E7BC"
}

.xly-icon-transport-down:before {
    content: "\E7BD"
}

.xly-icon-link-bold:before {
    content: "\E7BB"
}

.xly-icon-refresh-message:before {
    content: "\E7C1"
}

.xly-icon-face-message:before {
    content: "\E7C0"
}

.xly-icon-note-message:before {
    content: "\E7BF"
}

.xly-icon-shield:before {
    content: "\E7BE"
}

.xly-icon-user-message:before {
    content: "\E7C2"
}

.xly-icon-vip-message:before {
    content: "\E7C3"
}

.xly-icon-gift:before {
    content: "\E7C4"
}

.xly-icon-official:before {
    content: "\E7C5"
}

.xly-icon-clear-input:before {
    content: "\E7C6"
}

.xly-icon-wait:before {
    content: "\E7C7"
}

.xly-icon-create:before {
    content: "\E7CB"
}

.xly-icon-setting-bold:before {
    content: "\E7CD"
}

.xly-icon-arrow-both:before {
    content: "\E7C8"
}

.xly-icon-attention:before {
    content: "\E7CE"
}

.xly-icon-default:before {
    content: "\E7CF"
}

.xly-icon-secret:before {
    content: "\E7D0"
}

.xly-icon-browser:before {
    content: "\E7D1"
}

.xly-icon-recycle:before {
    content: "\E7D2"
}

.xly-icon-player-small:before {
    content: "\E7D3"
}

.xly-icon-code:before {
    content: "\E7CC"
}

.xly-icon-code-both:before {
    content: "\E7C9"
}

.xly-icon-invite:before {
    content: "\E7CA"
}

.xly-icon-share-1:before {
    content: "\E7D4"
}

body .td-dialog .td-dialog__close:hover {
    background-color: #ff7b5f
}

body .td-dialog h2 {
    color: #1a1a1a
}

body .td-dialog .xly-dialog__footer {
    position: relative;
    display: flex;
    justify-content: flex-end;
    width: 100%
}

body .td-dialog .xly-dialog__footer .xly-dialog-path__new {
    width: 90px
}

body .td-dialog .xly-dialog__footer-operate {
    position: absolute;
    left: 0;
    display: flex;
    align-items: center;
    height: 30px
}

body .td-dialog .xly-dialog__footer-link {
    color: var(--color-primary)
}

body .td-dialog .xly-dialog__button {
    display: flex
}

body .td-dialog .xly-dialog__button .td-button {
    width: 80px;
    font-size: 12px
}

body .xly-dialog-site {
    margin-top: 15px
}

body .xly-dialog-site .td-select {
    margin-top: 12px
}

body .xly-dialog-site .td-select.is-checked .xly-select__file {
    color: var(--color-primary)
}

body .xly-dialog-site .td-select.is-checked .xly-select__file:hover {
    color: var(--color-hover)
}

body .xly-dialog-site .td-select .td-checkbox {
    margin-left: 12px
}

body .xly-dialog-site .td-select .td-checkbox__label {
    margin: 0 12px;
    color: var(--color-primary)
}

body .xly-dialog-site .td-select .td-select__drop {
    margin-right: 34px
}

body .xly-dialog-site .xly-select__file {
    position: absolute;
    right: 8px;
    color: var(--color-primary)
}

body .xly-dialog-site .xly-select__size {
    margin-right: 10px;
    color: var(--color-secondary)
}

body .xly-dialog-site .xly-select__size.is-warn {
    color: #ff9700
}

body .xly-dialog-site .xly-select__size.is-danger {
    color: var(--color-danger)
}

body .xly-dialog-site .xly-select__link {
    position: absolute;
    top: 2px;
    left: 80px;
    width: calc(100% - 110px);
    line-height: 26px;
    text-indent: 5px;
    color: var(--color-primary);
    background: #fff
}

body .xly-dialog-site .td-input__inner {
    padding: 6px 10px
}

.xly-dialog-site__checkbox {
    margin: 12px 0 0
}

body .xly-dialog-popup {
    position: relative;
    width: 300px;
    height: 300px
}

.xly-dialog-popup__close {
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 32px;
    color: #fff
}

.xly-dialog-popup__close:hover {
    color: var(--color-danger)
}

.xly-dialog-popup__header {
    position: relative;
    height: 178px
}

.xly-dialog-popup__header h2 {
    position: absolute;
    top: 10px;
    left: 10px;
    color: #fff;
    font-size: 12px;
    z-index: 1
}

.xly-dialog-popup__header img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.xly-dialog-popup__header:before {
    position: absolute;
    width: 100%;
    height: 58px;
    opacity: .8;
    background-image: linear-gradient(180deg, #000, transparent);
    content: ""
}

.xly-dialog-popup__body {
    margin: 18px 18px 15px
}

.xly-dialog-popup__body h3 {
    margin-top: 26px;
    font-size: 15px;
    text-align: center
}

.xly-dialog-popup__text {
    -webkit-line-clamp: 2;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    text-align: center;
    font-size: 13px;
    line-height: 20px
}

.xly-dialog-popup__time {
    display: flex;
    align-items: center;
    margin-left: 6px;
    color: #fe3547
}

.xly-dialog-popup__time span {
    margin: 0 1px;
    width: 14px;
    height: 16px;
    line-height: 16px;
    font-family: dincond;
    font-size: 12px;
    text-align: center;
    color: #fff;
    background-image: linear-gradient(90deg, #fe2233, #fe4254);
    border-radius: 2px
}

.xly-dialog-popup__footer {
    position: absolute;
    left: 0;
    bottom: 20px;
    padding: 0 18px;
    width: 100%;
    display: flex;
    justify-content: center;
    box-sizing: border-box
}

body .xly-dialog-popup .td-button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 90px;
    font-size: 12px
}

body .xly-dialog-popup .td-button--vip {
    position: relative;
    width: 100%;
    height: 36px;
    font-size: 14px;
    color: #7d523a;
    background-color: #fcd68f;
    background-image: linear-gradient(119deg, rgba(255, 234, 173, 0), #ffeaad, transparent);
    background-repeat: no-repeat;
    background-size: 212px auto;
    background-position: 19px 0;
    border-radius: 18px
}

body .xly-dialog-popup--picture .xly-dialog-popup__header {
    height: 100%
}

body .xly-dialog-popup--text {
    height: 180px
}

body .xly-dialog-popup--text h2 {
    color: var(--color-default)
}

body .xly-dialog-popup--text h3 {
    margin-bottom: 8px;
    font-size: 16px;
    text-align: center
}

body .xly-dialog-popup--text h3 a {
    color: var(--color-primary)
}

body .xly-dialog-popup--text .xly-dialog-popup__header {
    margin-bottom: 24px;
    height: 18px
}

body .xly-dialog-popup--text .xly-dialog-popup__header:before {
    display: none
}

body .xly-dialog-popup--text .xly-dialog-popup__close {
    color: var(--color-secondary)
}

body .xly-dialog-popup--text .xly-dialog-popup__close:hover {
    color: #fff;
    background: var(--color-danger)
}

.xly-dialog-popup__user {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    height: 35px;
    color: #916146;
    font-size: 11px;
    background: var(--background);
    border-radius: 4px
}

.xly-dialog-popup__user p {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all
}

.xly-dialog-popup__user .is-warn {
    color: var(--color-danger)
}

.xly-dialog-popup__banner {
    margin-top: 16px
}

.xly-dialog-popup__banner img {
    height: 130px
}

.xly-dialog-popup__tag {
    position: absolute;
    top: 0;
    right: 16px;
    padding: 0 4px;
    line-height: 17px;
    color: #fff;
    font-size: 11px;
    background-image: linear-gradient(90deg, #fe2233, #fe4254);
    border-radius: 0 0 4px 4px
}

body .xly-dialog-popup--mutitext .xly-dialog-popup__header {
    height: 120px
}

body .xly-dialog-popup--mutitext .xly-dialog-popup__body {
    margin-top: 30px
}

body .xly-dialog-popup--vip .xly-dialog-popup__header {
    height: 18px
}

body .xly-dialog-popup--vip .xly-dialog-popup__header:before {
    display: none
}

body .xly-dialog-popup--vip .xly-dialog-popup__close {
    color: var(--color-secondary)
}

body .xly-dialog-popup--vip .xly-dialog-popup__close:hover {
    color: #fff;
    background: var(--color-danger)
}

body .xly-dialog-popup--vip h2 {
    color: var(--color-default)
}

body .xly-dialog-popup--message {
    height: 100px
}

body .xly-dialog-popup--message .xly-dialog-popup__header {
    height: 18px
}

body .xly-dialog-popup--message .xly-dialog-popup__header:before {
    display: none
}

body .xly-dialog-popup--message .xly-dialog-popup__close {
    color: var(--color-secondary)
}

body .xly-dialog-popup--message .xly-dialog-popup__close:hover {
    color: #fff;
    background: var(--color-danger)
}

body .xly-dialog-popup--message h2 {
    color: var(--color-default)
}

body .xly-dialog-popup--message .td-media {
    margin: 12px;
    height: 60px
}

body .xly-dialog-popup--message .td-media__object {
    width: 36px;
    height: 36px;
    margin: 0
}

body .xly-dialog-popup--message .td-media__object img {
    width: 36px;
    height: 36px;
    border-radius: 2px
}

body .xly-dialog-popup--message .xly-dialog-popup__title {
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all
}

body .xly-dialog-popup--message .xly-dialog-popup__title .is-warn {
    color: var(--color-danger)
}

body .xly-dialog-popup--message .xly-dialog-popup__text {
    color: var(--color-secondary);
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all
}

body .xly-dialog-popup--message a.xly-dialog-popup__text:hover {
    color: var(--color-primary)
}

body .xly-dialog-popup--message .td-media__content {
    margin-left: 12px;
    width: auto;
    flex: 1;
    overflow: hidden
}