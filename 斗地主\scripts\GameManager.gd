class_name GameManager
extends Node

# 游戏管理器 - 控制整个斗地主游戏流程

enum GameState { WAITING, BIDDING, PLAYING, GAME_OVER }

@export var game_state: GameState = GameState.WAITING
@export var current_player_index: int = 0
@export var landlord_index: int = -1
@export var last_played_cards: Array[Card] = []
@export var last_player_index: int = -1
@export var pass_count: int = 0

var players: Array[Player] = []
var card_manager: CardManager
var ui_manager: UIManager

signal game_started()
signal game_ended(winner: Player)
signal turn_changed(player: Player)
signal landlord_selected(player: Player)

func _ready():
	# 初始化游戏组件
	setup_game_components()
	setup_players()

	# 连接信号
	connect_signals()

	print("游戏管理器初始化完成")

	# 自动开始游戏
	call_deferred("start_new_game")

func setup_game_components():
	"""设置游戏组件"""
	card_manager = CardManager.new()
	add_child(card_manager)

	# UI管理器在场景中已经存在，获取引用
	ui_manager = $UIManager
	ui_manager.set_game_manager(self)

func setup_players():
	"""设置玩家"""
	players.clear()
	
	# 创建3个玩家：1个人类玩家 + 2个AI
	var human_player = Player.new()
	human_player.player_type = Player.PlayerType.HUMAN
	human_player.player_name = "玩家"
	human_player.position_index = 0
	add_child(human_player)
	players.append(human_player)
	
	var ai_player1 = Player.new()
	ai_player1.player_type = Player.PlayerType.AI
	ai_player1.player_name = "电脑1"
	ai_player1.position_index = 1
	add_child(ai_player1)
	players.append(ai_player1)
	
	var ai_player2 = Player.new()
	ai_player2.player_type = Player.PlayerType.AI
	ai_player2.player_name = "电脑2"
	ai_player2.position_index = 2
	add_child(ai_player2)
	players.append(ai_player2)

func connect_signals():
	"""连接信号"""
	card_manager.cards_dealt.connect(_on_cards_dealt)

	for player in players:
		player.cards_played.connect(_on_player_cards_played)
		player.turn_passed.connect(_on_player_turn_passed)

	# 连接UI信号
	ui_manager.play_cards_requested.connect(_on_play_cards_requested)
	ui_manager.pass_turn_requested.connect(_on_pass_turn_requested)
	ui_manager.restart_game_requested.connect(_on_restart_game_requested)

func start_new_game():
	"""开始新游戏"""
	print("开始新游戏")
	game_state = GameState.WAITING
	reset_game_state()
	
	# 发牌
	card_manager.deal_cards(players)
	
	# 开始叫地主阶段
	start_bidding_phase()

func reset_game_state():
	"""重置游戏状态"""
	current_player_index = 0
	landlord_index = -1
	last_played_cards.clear()
	last_player_index = -1
	pass_count = 0
	
	for player in players:
		player.role = Player.Role.FARMER
		player.set_current_player(false)

func start_bidding_phase():
	"""开始叫地主阶段"""
	print("开始叫地主阶段")
	game_state = GameState.BIDDING
	current_player_index = randi() % 3  # 随机选择起始玩家
	
	# 简化版本：随机选择地主
	landlord_index = randi() % 3
	players[landlord_index].role = Player.Role.LANDLORD
	
	# 给地主发底牌
	card_manager.give_landlord_cards(players[landlord_index])
	
	print("%s 成为地主" % players[landlord_index].player_name)
	landlord_selected.emit(players[landlord_index])
	
	# 开始游戏阶段
	start_playing_phase()

func start_playing_phase():
	"""开始游戏阶段"""
	print("开始游戏阶段")
	game_state = GameState.PLAYING
	current_player_index = landlord_index  # 地主先出牌
	
	update_current_player()
	game_started.emit()

func update_current_player():
	"""更新当前玩家"""
	for i in range(players.size()):
		players[i].set_current_player(i == current_player_index)
	
	var current_player = players[current_player_index]
	print("轮到 %s 出牌" % current_player.player_name)
	turn_changed.emit(current_player)
	
	# 如果是AI玩家，自动出牌
	if current_player.player_type == Player.PlayerType.AI:
		call_deferred("ai_play_turn")

func ai_play_turn():
	"""AI玩家回合"""
	var current_player = players[current_player_index]
	
	# 等待一段时间模拟思考
	await get_tree().create_timer(1.0).timeout
	
	# AI决策
	if current_player.ai_should_pass() and not last_played_cards.is_empty():
		_on_player_turn_passed()
	else:
		var cards_to_play = current_player.ai_make_decision()
		if not cards_to_play.is_empty():
			_on_player_cards_played(cards_to_play)
		else:
			_on_player_turn_passed()

func _on_cards_dealt():
	"""卡牌发放完成"""
	print("卡牌发放完成")
	ui_manager.update_all_players_display(players)

	# 排列所有玩家的手牌
	for player in players:
		ui_manager.arrange_player_cards(player)

func _on_player_cards_played(cards: Array[Card]):
	"""玩家出牌"""
	var current_player = players[current_player_index]
	print("%s 出牌: %s" % [current_player.player_name, card_manager.create_card_display_text(cards)])
	
	# 验证出牌是否合法
	if not is_valid_play(cards):
		print("出牌不合法")
		return
	
	# 更新游戏状态
	last_played_cards = cards
	last_player_index = current_player_index
	pass_count = 0
	
	# 移除已出的牌
	current_player.remove_cards(cards)
	
	# 检查是否获胜
	if current_player.has_won():
		end_game(current_player)
		return
	
	# 下一个玩家
	next_turn()

func _on_player_turn_passed():
	"""玩家过牌"""
	var current_player = players[current_player_index]
	print("%s 过牌" % current_player.player_name)
	
	pass_count += 1
	
	# 如果连续两个玩家过牌，清空上一手牌
	if pass_count >= 2:
		last_played_cards.clear()
		last_player_index = -1
		pass_count = 0
		print("清空上一手牌")
	
	next_turn()

func next_turn():
	"""下一个玩家回合"""
	current_player_index = (current_player_index + 1) % 3
	update_current_player()

func is_valid_play(cards: Array[Card]) -> bool:
	"""检查出牌是否合法"""
	if cards.is_empty():
		return false
	
	# 检查卡牌组合是否有效
	if not card_manager.is_valid_combination(cards):
		return false
	
	# 检查是否能压过上一手牌
	if not last_played_cards.is_empty():
		return card_manager.can_beat(cards, last_played_cards)
	
	return true

func end_game(winner: Player):
	"""结束游戏"""
	print("游戏结束，%s 获胜！" % winner.player_name)
	game_state = GameState.GAME_OVER
	
	# 计算得分
	calculate_scores(winner)
	
	game_ended.emit(winner)

func calculate_scores(winner: Player):
	"""计算所有玩家得分"""
	var multiplier = 1  # 可以根据游戏情况调整倍数
	
	for player in players:
		var is_winner = (player == winner)
		player.calculate_score(is_winner, multiplier)
		print("%s 得分: %d" % [player.player_name, player.score])

func get_current_player() -> Player:
	"""获取当前玩家"""
	if current_player_index >= 0 and current_player_index < players.size():
		return players[current_player_index]
	return null

func get_landlord() -> Player:
	"""获取地主"""
	if landlord_index >= 0 and landlord_index < players.size():
		return players[landlord_index]
	return null

func restart_game():
	"""重新开始游戏"""
	print("重新开始游戏")
	card_manager.reset_deck()
	start_new_game()

func can_play_cards() -> bool:
	"""检查当前是否可以出牌"""
	return game_state == GameState.PLAYING

func get_game_info() -> Dictionary:
	"""获取游戏信息"""
	return {
		"state": game_state,
		"current_player": get_current_player().player_name if get_current_player() else "",
		"landlord": get_landlord().player_name if get_landlord() else "",
		"last_played": card_manager.create_card_display_text(last_played_cards)
	}

func _on_play_cards_requested():
	"""处理出牌请求"""
	var current_player = get_current_player()
	if current_player and current_player.player_type == Player.PlayerType.HUMAN:
		if current_player.play_selected_cards():
			ui_manager.show_message("出牌成功", 1.0)
		else:
			ui_manager.show_message("出牌失败，请检查选择的牌", 1.0)

func _on_pass_turn_requested():
	"""处理过牌请求"""
	var current_player = get_current_player()
	if current_player and current_player.player_type == Player.PlayerType.HUMAN:
		current_player.pass_turn()

func _on_restart_game_requested():
	"""处理重新开始请求"""
	restart_game()
