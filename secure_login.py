"""
安全卡密验证系统
具有多重反逆向和加密保护机制
"""

import hashlib
import base64
import time
import os
import sys
import random
import threading
import tkinter as tk
from tkinter import ttk, messagebox
import ctypes
from ctypes import wintypes

class SecurityManager:
    """安全管理器 - 负责反逆向和加密保护"""
    
    def __init__(self):
        self._salt = b'\x8f\x3e\x9a\x7c\x2d\x5b\x1f\x4e'
        self._key_hash = None
        self._debug_detected = False
        self._tamper_detected = False
        self._init_security()
    
    def _init_security(self):
        """初始化安全机制"""
        # 启动反调试检测
        self._start_anti_debug()
        
        # 检测虚拟机环境
        self._check_vm_environment()
        
        # 生成动态密钥哈希
        self._generate_key_hash()
    
    def _start_anti_debug(self):
        """启动反调试检测"""
        def debug_check():
            while True:
                try:
                    # 检测调试器
                    if self._is_debugger_present():
                        self._debug_detected = True
                        self._trigger_security_response()
                    
                    # 检测进程监控
                    if self._check_suspicious_processes():
                        self._debug_detected = True
                        self._trigger_security_response()
                    
                    time.sleep(0.5)
                except:
                    pass
        
        thread = threading.Thread(target=debug_check, daemon=True)
        thread.start()
    
    def _is_debugger_present(self):
        """检测调试器是否存在"""
        try:
            # Windows API检测
            kernel32 = ctypes.windll.kernel32
            if kernel32.IsDebuggerPresent():
                return True
            
            # 检测远程调试器
            debug_flag = ctypes.c_bool()
            if kernel32.CheckRemoteDebuggerPresent(kernel32.GetCurrentProcess(), ctypes.byref(debug_flag)):
                if debug_flag.value:
                    return True
            
            return False
        except:
            return False
    
    def _check_suspicious_processes(self):
        """检测可疑进程"""
        suspicious_processes = [
            'ollydbg.exe', 'x64dbg.exe', 'windbg.exe', 'ida.exe', 'ida64.exe',
            'cheatengine.exe', 'processhacker.exe', 'procmon.exe', 'wireshark.exe',
            'fiddler.exe', 'httpanalyzer.exe', 'charles.exe'
        ]
        
        try:
            import psutil
            for proc in psutil.process_iter(['name']):
                if proc.info['name'].lower() in suspicious_processes:
                    return True
        except:
            pass
        
        return False
    
    def _check_vm_environment(self):
        """检测虚拟机环境"""
        vm_indicators = [
            'vmware', 'virtualbox', 'vbox', 'qemu', 'xen',
            'parallels', 'hyper-v', 'kvm'
        ]
        
        try:
            # 检查系统信息
            import platform
            system_info = platform.platform().lower()
            
            for indicator in vm_indicators:
                if indicator in system_info:
                    self._trigger_security_response()
                    return True
        except:
            pass
        
        return False
    
    def _generate_key_hash(self):
        """生成动态密钥哈希"""
        # 使用多重哈希和时间戳
        timestamp = str(int(time.time() / 3600))  # 小时级别的时间戳
        
        # 真实卡密: 555555
        real_key = "555555"
        
        # 多重加密
        step1 = hashlib.sha256((real_key + timestamp).encode()).hexdigest()
        step2 = hashlib.md5((step1 + self._salt.hex()).encode()).hexdigest()
        step3 = base64.b64encode(step2.encode()).decode()
        
        self._key_hash = step3
    
    def _trigger_security_response(self):
        """触发安全响应"""
        if not self._tamper_detected:
            self._tamper_detected = True
            # 清理敏感数据
            self._key_hash = None
            # 退出程序
            os._exit(1)
    
    def verify_key(self, input_key):
        """验证卡密"""
        if self._debug_detected or self._tamper_detected:
            return False
        
        if not input_key or len(input_key) != 6:
            return False
        
        # 重新生成哈希进行比较
        timestamp = str(int(time.time() / 3600))
        step1 = hashlib.sha256((input_key + timestamp).encode()).hexdigest()
        step2 = hashlib.md5((step1 + self._salt.hex()).encode()).hexdigest()
        step3 = base64.b64encode(step2.encode()).decode()
        
        return step3 == self._key_hash
    
    def get_encrypted_welcome(self):
        """获取加密的欢迎信息"""
        welcome_msg = "欢迎进入个人页面！"
        
        # 简单的XOR加密
        key = 0x5A
        encrypted = ''.join(chr(ord(c) ^ key) for c in welcome_msg)
        return base64.b64encode(encrypted.encode('utf-8')).decode()
    
    def decrypt_welcome(self, encrypted_msg):
        """解密欢迎信息"""
        try:
            decoded = base64.b64decode(encrypted_msg).decode('utf-8')
            key = 0x5A
            decrypted = ''.join(chr(ord(c) ^ key) for c in decoded)
            return decrypted
        except:
            return "解密失败"

class AntiTamperMixin:
    """反篡改混入类"""
    
    def __init__(self):
        self._checksum = self._calculate_checksum()
        self._start_integrity_check()
    
    def _calculate_checksum(self):
        """计算程序完整性校验和"""
        try:
            with open(__file__, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except:
            return None
    
    def _start_integrity_check(self):
        """启动完整性检查"""
        def integrity_check():
            while True:
                try:
                    current_checksum = self._calculate_checksum()
                    if current_checksum != self._checksum:
                        os._exit(1)
                    time.sleep(2)
                except:
                    pass
        
        thread = threading.Thread(target=integrity_check, daemon=True)
        thread.start()

class SecureLoginApp(AntiTamperMixin):
    """安全登录应用程序"""
    
    def __init__(self):
        AntiTamperMixin.__init__(self)
        
        self.security_manager = SecurityManager()
        self.root = None
        self.login_attempts = 0
        self.max_attempts = 3
        self.locked_until = 0
        
        # 添加假的卡密列表来迷惑逆向者
        self._fake_keys = [
            "123456", "888888", "666666", "111111", "999999",
            "000000", "654321", "112233", "445566", "778899"
        ]
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化用户界面"""
        self.root = tk.Tk()
        self.root.title("安全验证系统")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self._center_window()
        
        # 创建界面元素
        self._create_widgets()
        
        # 绑定事件
        self._bind_events()
    
    def _center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔐 安全验证系统", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明文本
        info_label = ttk.Label(main_frame, text="请输入您的卡密以访问个人页面",
                              font=("Arial", 10))
        info_label.pack(pady=(0, 10))
        
        # 卡密输入框架
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(input_frame, text="卡密:").pack(anchor=tk.W)
        
        self.key_var = tk.StringVar()
        self.key_entry = ttk.Entry(input_frame, textvariable=self.key_var,
                                  font=("Arial", 12), show="*", width=20)
        self.key_entry.pack(fill=tk.X, pady=(5, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.login_button = ttk.Button(button_frame, text="验证登录",
                                      command=self._verify_login)
        self.login_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="退出", command=self._exit_app).pack(side=tk.LEFT)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="", foreground="red")
        self.status_label.pack(pady=(10, 0))
        
        # 尝试次数显示
        self.attempts_label = ttk.Label(main_frame, text="", foreground="orange")
        self.attempts_label.pack()
    
    def _bind_events(self):
        """绑定事件"""
        self.key_entry.bind('<Return>', lambda e: self._verify_login())
        self.root.protocol("WM_DELETE_WINDOW", self._exit_app)
        
        # 添加反调试快捷键检测
        self.root.bind('<Control-c>', self._security_check)
        self.root.bind('<Control-v>', self._security_check)
        self.root.bind('<F12>', self._security_check)
    
    def _security_check(self, event=None):
        """安全检查"""
        # 检测到可疑操作
        self.security_manager._trigger_security_response()
    
    def _verify_login(self):
        """验证登录"""
        # 检查是否被锁定
        if time.time() < self.locked_until:
            remaining = int(self.locked_until - time.time())
            self.status_label.config(text=f"账户已锁定，请等待 {remaining} 秒")
            return
        
        input_key = self.key_var.get().strip()
        
        # 增加登录尝试次数
        self.login_attempts += 1
        
        # 验证卡密
        if self.security_manager.verify_key(input_key):
            self._login_success()
        else:
            self._login_failed()
    
    def _login_success(self):
        """登录成功"""
        self.status_label.config(text="验证成功！正在进入...", foreground="green")
        self.root.update()
        
        # 延迟一下增加真实感
        time.sleep(1)
        
        # 打开个人页面
        self._open_personal_page()
    
    def _login_failed(self):
        """登录失败"""
        remaining_attempts = self.max_attempts - self.login_attempts
        
        if remaining_attempts > 0:
            self.status_label.config(text="卡密错误！请重试", foreground="red")
            self.attempts_label.config(text=f"剩余尝试次数: {remaining_attempts}")
        else:
            # 锁定账户
            self.locked_until = time.time() + 300  # 锁定5分钟
            self.status_label.config(text="尝试次数过多，账户已锁定5分钟", foreground="red")
            self.attempts_label.config(text="")
            self.login_button.config(state=tk.DISABLED)
            
            # 5分钟后重新启用
            self.root.after(300000, self._unlock_account)
        
        # 清空输入框
        self.key_var.set("")
    
    def _unlock_account(self):
        """解锁账户"""
        self.login_attempts = 0
        self.locked_until = 0
        self.login_button.config(state=tk.NORMAL)
        self.status_label.config(text="账户已解锁，请重新尝试")
        self.attempts_label.config(text="")
    
    def _open_personal_page(self):
        """打开个人页面"""
        # 关闭登录窗口
        self.root.destroy()
        
        # 创建个人页面窗口
        personal_window = tk.Tk()
        personal_window.title("个人页面")
        personal_window.geometry("500x400")
        
        # 居中显示
        personal_window.update_idletasks()
        width = personal_window.winfo_width()
        height = personal_window.winfo_height()
        x = (personal_window.winfo_screenwidth() // 2) - (width // 2)
        y = (personal_window.winfo_screenheight() // 2) - (height // 2)
        personal_window.geometry(f'{width}x{height}+{x}+{y}')
        
        # 创建个人页面内容
        main_frame = ttk.Frame(personal_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 解密并显示欢迎信息
        encrypted_welcome = self.security_manager.get_encrypted_welcome()
        welcome_msg = self.security_manager.decrypt_welcome(encrypted_welcome)
        
        ttk.Label(main_frame, text="🎉 " + welcome_msg, 
                 font=("Arial", 18, "bold"), foreground="green").pack(pady=(0, 20))
        
        ttk.Label(main_frame, text="您已成功通过安全验证！", 
                 font=("Arial", 12)).pack(pady=(0, 10))
        
        # 个人信息区域
        info_frame = ttk.LabelFrame(main_frame, text="个人信息", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        ttk.Label(info_frame, text="用户ID: VIP-555555").pack(anchor=tk.W, pady=2)
        ttk.Label(info_frame, text="权限级别: 高级用户").pack(anchor=tk.W, pady=2)
        ttk.Label(info_frame, text="登录时间: " + time.strftime("%Y-%m-%d %H:%M:%S")).pack(anchor=tk.W, pady=2)
        ttk.Label(info_frame, text="有效期: 永久").pack(anchor=tk.W, pady=2)
        
        # 功能按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="查看详细信息", 
                  command=lambda: messagebox.showinfo("详细信息", "这里是您的详细个人信息")).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="退出登录", 
                  command=personal_window.destroy).pack(side=tk.LEFT)
        
        personal_window.mainloop()
    
    def _exit_app(self):
        """退出应用"""
        self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        except Exception as e:
            # 发生异常时安全退出
            os._exit(1)

def main():
    """主函数"""
    try:
        # 添加随机延迟防止时间攻击
        time.sleep(random.uniform(0.1, 0.5))
        
        # 检查运行环境
        if len(sys.argv) > 1:
            # 防止命令行参数注入
            os._exit(1)
        
        # 创建并运行应用
        app = SecureLoginApp()
        app.run()
        
    except KeyboardInterrupt:
        os._exit(1)
    except Exception:
        os._exit(1)

if __name__ == "__main__":
    main()
