(window.webpackJsonp=window.webpackJsonp||[]).push([[4],{683:function(e,t,n){"use strict";n.r(t);n(12),n(4),n(30),n(472);var i=n(33),a=n(40),r=n(3),o=function(){function e(){Object(i.a)(this,e),this.timerMap=new Map}return Object(a.a)(e,[{key:"markStart",value:function(e){this.timerMap.has(e)?console.warn("Timer ".concat(e," is existed.")):this.timerMap.set(e,window.performance.now())}},{key:"markEnd",value:function(e){if(this.timerMap.has(e)){var t=window.performance.now()-this.timerMap.get(e);this.timerMap.delete(e),r.a.callServerFunction("SendPerformanceItem","DriveSelectFile",t)}else console.warn("Timer ".concat(e," does not exists."))}}]),e}();t.default=new o}}]);