module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=983)}({0:function(e,t,n){"use strict";function r(e,t,n,r,o,i,s,a){var c,l="function"==typeof e?e.options:e;if(t&&(l.render=t,l.staticRenderFns=n,l._compiled=!0),r&&(l.functional=!0),i&&(l._scopeId="data-v-"+i),s?(c=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},l._ssrRegister=c):o&&(c=a?function(){o.call(this,this.$root.$options.shadowRoot)}:o),c)if(l.functional){l._injectStyles=c;var u=l.render;l.render=function(e,t){return c.call(t),u(e,t)}}else{var d=l.beforeCreate;l.beforeCreate=d?[].concat(d,c):[c]}return{exports:e,options:l}}n.d(t,"a",function(){return r})},1:function(e,t,n){e.exports=n(9)(137)},10:function(e,t,n){"use strict";var r=n(66),o=n(116),i=Object.prototype.toString;function s(e){return"[object Array]"===i.call(e)}function a(e){return null!==e&&"object"==typeof e}function c(e){return"[object Function]"===i.call(e)}function l(e,t){if(null!==e&&void 0!==e)if("object"!=typeof e&&(e=[e]),s(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:s,isArrayBuffer:function(e){return"[object ArrayBuffer]"===i.call(e)},isBuffer:o,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===i.call(e)},isFile:function(e){return"[object File]"===i.call(e)},isBlob:function(e){return"[object Blob]"===i.call(e)},isFunction:c,isStream:function(e){return a(e)&&c(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:l,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,o=arguments.length;r<o;r++)l(arguments[r],n);return t},extend:function(e,t,n){return l(t,function(t,o){e[o]=n&&"function"==typeof t?r(t,n):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},1078:function(e,t){},1080:function(e,t){},11:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(2),i=n(8),s=n(1).default.getLogger("XLStat");let a=i.default(o.join(__rootDir,"../bin/ThunderHelper.node"));function c(e=""){let t;if("string"==typeof e)t=e;else if(l(e)&&"object"==typeof e){let n=[];for(let t in e)l(e[t])&&n.push(t+"="+encodeURIComponent(e[t]));t=n.join(",")}return t}function l(e){return void 0!==e&&null!==e}!function(e){let t=null;function n(){return t||(t=a.xlstat4),t}function o(e,t="",o="",i=0,a=0,l=0,u=0,d="",h=0){return r(this,void 0,void 0,function*(){let r=0;do{if(void 0===e){r=1;break}let f=c(d);r="browser"===process.type?yield new Promise(s=>{r=n().asyncTrackEvent(e,t,o,i,a,l,u,f,h,e=>{s(e)})}):n().trackEvent(e,t,o,i,a,l,u,f,h),s.information(e,t,o,i,a,l,u,f,h)}while(0);return r})}function i(e,t=0){do{if(void 0===e)break;"browser"!==process.type&&n().trackClick(e,t)}while(0)}e.asyncTrackEvent=o,e.trackEvent=function(e,t="",n="",r=0,i=0,s=0,a=0,c="",l=0){o(e,t,n,r,i,s,a,c,l).catch()},e.trackEventEx=function(e,t="",n="",r=0){o(e,t,"",0,0,0,0,n,r).catch()},e.trackClick=i,e.trackShow=function(e,t=0){i(e,t)},e.setUserID=function(e=0,t=0){"browser"!==process.type&&n().setUserID(e,t)},e.initParam=function(e){return r(this,void 0,void 0,function*(){let t=-1;return t="browser"===process.type?yield new Promise(t=>{n().asyncInitParam(e,(e,n)=>{t(e?n:-1)})}):yield new Promise(t=>{n().initParamRemote(e,e=>{t(e)})})})},e.asyncUninit=function(e){return r(this,void 0,void 0,function*(){"browser"===process.type&&(yield new Promise(t=>{n().asyncUninit(e,()=>{t()})}))})},e.uninit=function(){"browser"===process.type&&n().waitFinish()}}(t.XLStatNS||(t.XLStatNS={}))},115:function(e,t,n){"use strict";var r=n(10),o=n(66),i=n(117),s=n(45);function a(e){var t=new i(e),n=o(i.prototype.request,t);return r.extend(n,i.prototype,t),r.extend(n,t),n}var c=a(s);c.Axios=i,c.create=function(e){return a(r.merge(s,e))},c.Cancel=n(72),c.CancelToken=n(133),c.isCancel=n(71),c.all=function(e){return Promise.all(e)},c.spread=n(134),e.exports=c,e.exports.default=c},116:function(e,t){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},1163:function(e,t){},117:function(e,t,n){"use strict";var r=n(45),o=n(10),i=n(128),s=n(129);function a(e){this.defaults=e,this.interceptors={request:new i,response:new i}}a.prototype.request=function(e){"string"==typeof e&&(e=o.merge({url:arguments[0]},arguments[1])),(e=o.merge(r,{method:"get"},this.defaults,e)).method=e.method.toLowerCase();var t=[s,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},o.forEach(["delete","get","head","options"],function(e){a.prototype[e]=function(t,n){return this.request(o.merge(n||{},{method:e,url:t}))}}),o.forEach(["post","put","patch"],function(e){a.prototype[e]=function(t,n,r){return this.request(o.merge(r||{},{method:e,url:t,data:n}))}}),e.exports=a},118:function(e,t,n){"use strict";var r=n(10);e.exports=function(e,t){r.forEach(e,function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])})}},119:function(e,t,n){"use strict";var r=n(10),o=n(67),i=n(69),s=n(120),a=n(121),c=n(46),l="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(122);e.exports=function(e){return new Promise(function(t,u){var d=e.data,h=e.headers;r.isFormData(d)&&delete h["Content-Type"];var f=new XMLHttpRequest,m="onreadystatechange",p=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in f||a(e.url)||(f=new window.XDomainRequest,m="onload",p=!0,f.onprogress=function(){},f.ontimeout=function(){}),e.auth){var w=e.auth.username||"",g=e.auth.password||"";h.Authorization="Basic "+l(w+":"+g)}if(f.open(e.method.toUpperCase(),i(e.url,e.params,e.paramsSerializer),!0),f.timeout=e.timeout,f[m]=function(){if(f&&(4===f.readyState||p)&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in f?s(f.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?f.response:f.responseText,status:1223===f.status?204:f.status,statusText:1223===f.status?"No Content":f.statusText,headers:n,config:e,request:f};o(t,u,r),f=null}},f.onerror=function(){u(c("Network Error",e,null,f)),f=null},f.ontimeout=function(){u(c("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",f)),f=null},r.isStandardBrowserEnv()){var _=n(123),v=(e.withCredentials||a(e.url))&&e.xsrfCookieName?_.read(e.xsrfCookieName):void 0;v&&(h[e.xsrfHeaderName]=v)}if("setRequestHeader"in f&&r.forEach(h,function(e,t){void 0===d&&"content-type"===t.toLowerCase()?delete h[t]:f.setRequestHeader(t,e)}),e.withCredentials&&(f.withCredentials=!0),e.responseType)try{f.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&f.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&f.upload&&f.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){f&&(f.abort(),u(e),f=null)}),void 0===d&&(d=null),f.send(d)})}},12:function(e,t){e.exports=require("events")},120:function(e,t,n){"use strict";var r=n(10),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,i,s={};return e?(r.forEach(e.split("\n"),function(e){if(i=e.indexOf(":"),t=r.trim(e.substr(0,i)).toLowerCase(),n=r.trim(e.substr(i+1)),t){if(s[t]&&o.indexOf(t)>=0)return;s[t]="set-cookie"===t?(s[t]?s[t]:[]).concat([n]):s[t]?s[t]+", "+n:n}}),s):s}},121:function(e,t,n){"use strict";var r=n(10);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function o(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=o(window.location.href),function(t){var n=r.isString(t)?o(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},122:function(e,t,n){"use strict";var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function o(){this.message="String contains an invalid character"}o.prototype=new Error,o.prototype.code=5,o.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,n,i=String(e),s="",a=0,c=r;i.charAt(0|a)||(c="=",a%1);s+=c.charAt(63&t>>8-a%1*8)){if((n=i.charCodeAt(a+=.75))>255)throw new o;t=t<<8|n}return s}},123:function(e,t,n){"use strict";var r=n(10);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,o,i,s){var a=[];a.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),r.isString(o)&&a.push("path="+o),r.isString(i)&&a.push("domain="+i),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},124:function(e,t,n){function r(){var e;try{e=t.storage.debug}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e}(t=e.exports=n(125)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n=this.useColors;if(e[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+e[0]+(n?"%c ":" ")+"+"+t.humanize(this.diff),!n)return;var r="color: "+this.color;e.splice(1,0,r,"color: inherit");var o=0,i=0;e[0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&"%c"===e&&(i=++o)}),e.splice(i,0,r)},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=r,t.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(r())},125:function(e,t,n){function r(e){var n;function r(){if(r.enabled){var e=r,o=+new Date,i=o-(n||o);e.diff=i,e.prev=n,e.curr=o,n=o;for(var s=new Array(arguments.length),a=0;a<s.length;a++)s[a]=arguments[a];s[0]=t.coerce(s[0]),"string"!=typeof s[0]&&s.unshift("%O");var c=0;s[0]=s[0].replace(/%([a-zA-Z%])/g,function(n,r){if("%%"===n)return n;c++;var o=t.formatters[r];if("function"==typeof o){var i=s[c];n=o.call(e,i),s.splice(c,1),c--}return n}),t.formatArgs.call(e,s),(r.log||t.log||console.log.bind(console)).apply(e,s)}}return r.namespace=e,r.enabled=t.enabled(e),r.useColors=t.useColors(),r.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),r.destroy=o,"function"==typeof t.init&&t.init(r),t.instances.push(r),r}function o(){var e=t.instances.indexOf(this);return-1!==e&&(t.instances.splice(e,1),!0)}(t=e.exports=r.debug=r.default=r).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){var n;t.save(e),t.names=[],t.skips=[];var r=("string"==typeof e?e:"").split(/[\s,]+/),o=r.length;for(n=0;n<o;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")));for(n=0;n<t.instances.length;n++){var i=t.instances[n];i.enabled=t.enabled(i.namespace)}},t.enabled=function(e){if("*"===e[e.length-1])return!0;var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(126),t.instances=[],t.names=[],t.skips=[],t.formatters={}},126:function(e,t){var n=1e3,r=60*n,o=60*r,i=24*o,s=365.25*i;function a(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var c,l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var a=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return a*s;case"days":case"day":case"d":return a*i;case"hours":case"hour":case"hrs":case"hr":case"h":return a*o;case"minutes":case"minute":case"mins":case"min":case"m":return a*r;case"seconds":case"second":case"secs":case"sec":case"s":return a*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}(e);if("number"===l&&!1===isNaN(e))return t.long?a(c=e,i,"day")||a(c,o,"hour")||a(c,r,"minute")||a(c,n,"second")||c+" ms":function(e){if(e>=i)return Math.round(e/i)+"d";if(e>=o)return Math.round(e/o)+"h";if(e>=r)return Math.round(e/r)+"m";if(e>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},127:function(e){e.exports={_args:[["axios@0.18.0","D:\\jenkins\\workspace\\ThunderPC_AP_Build_Pack\\channel\\trunk\\build\\app"]],_from:"axios@0.18.0",_id:"axios@0.18.0",_inBundle:!1,_integrity:"sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=",_location:"/axios",_phantomChildren:{},_requested:{type:"version",registry:!0,raw:"axios@0.18.0",name:"axios",escapedName:"axios",rawSpec:"0.18.0",saveSpec:null,fetchSpec:"0.18.0"},_requiredBy:["/","/@types/axios","/@xunlei/thunderx-login-main"],_resolved:"http://xnpm.repo.xunlei.cn/axios/-/axios-0.18.0.tgz",_spec:"0.18.0",_where:"D:\\jenkins\\workspace\\ThunderPC_AP_Build_Pack\\channel\\trunk\\build\\app",author:{name:"Matt Zabriskie"},browser:{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},bugs:{url:"https://github.com/axios/axios/issues"},bundlesize:[{path:"./dist/axios.min.js",threshold:"5kB"}],dependencies:{"follow-redirects":"^1.3.0","is-buffer":"^1.1.5"},description:"Promise based HTTP client for the browser and node.js",devDependencies:{bundlesize:"^0.5.7",coveralls:"^2.11.9","es6-promise":"^4.0.5",grunt:"^1.0.1","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.0.0","grunt-contrib-nodeunit":"^1.0.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^19.0.0","grunt-karma":"^2.0.0","grunt-ts":"^6.0.0-beta.3","grunt-webpack":"^1.0.18","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1",karma:"^1.3.0","karma-chrome-launcher":"^2.0.0","karma-coverage":"^1.0.0","karma-firefox-launcher":"^1.0.0","karma-jasmine":"^1.0.2","karma-jasmine-ajax":"^0.1.13","karma-opera-launcher":"^1.0.0","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^1.1.0","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.7","karma-webpack":"^1.7.0","load-grunt-tasks":"^3.5.2",minimist:"^1.2.0",sinon:"^1.17.4",typescript:"^2.0.3","url-search-params":"^0.6.1",webpack:"^1.13.1","webpack-dev-server":"^1.14.1"},homepage:"https://github.com/axios/axios",keywords:["xhr","http","ajax","promise","node"],license:"MIT",main:"index.js",name:"axios",repository:{type:"git",url:"git+https://github.com/axios/axios.git"},scripts:{build:"NODE_ENV=production grunt build",coveralls:"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",examples:"node ./examples/server.js",postversion:"git push && git push --tags",preversion:"npm test",start:"node ./sandbox/server.js",test:"grunt test && bundlesize",version:"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json"},typings:"./index.d.ts",version:"0.18.0"}},128:function(e,t,n){"use strict";var r=n(10);function o(){this.handlers=[]}o.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){r.forEach(this.handlers,function(t){null!==t&&e(t)})},e.exports=o},129:function(e,t,n){"use strict";var r=n(10),o=n(130),i=n(71),s=n(45),a=n(131),c=n(132);function l(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return l(e),e.baseURL&&!a(e.url)&&(e.url=c(e.baseURL,e.url)),e.headers=e.headers||{},e.data=o(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||s.adapter)(e).then(function(t){return l(e),t.data=o(t.data,t.headers,e.transformResponse),t},function(t){return i(t)||(l(e),t&&t.response&&(t.response.data=o(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},1293:function(e,t){},1295:function(e,t){},13:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=t.log=t.error=t.warn=t.info=t.trace=t.timeEnd=t.time=t.traceback=void 0;const r=n(2);let o,i=console;function s(e=5){let t=/at\s+(.*)\s+\((.*):(\d*):(\d*)\)/i,n=/at\s+()(.*):(\d*):(\d*)/i,o=(new Error).stack.split("\n").slice(e+1);o.shift();let i=[];return o.forEach((e,o)=>{let s=t.exec(e)||n.exec(e),a={};s&&5===s.length&&(a.method=s[1],a.path=s[2],a.line=s[3],a.pos=s[4],a.file=r.basename(a.path),i.push(a))}),i}if(o="renderer"===process.type?"[Renderer] [async-remote]:":"browser"===process.type?"[Browser] [async-remote]:":`[${process.type}] [async-remote]`,t.traceback=function(e=5){return s(e).map(e=>e.method+"@("+e.file+")").join(" <= ")},t.time=function(...e){i.time(...e)},t.timeEnd=function(...e){i.timeEnd(...e)},t.trace=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),i.trace(o,...e)},t.info=function(...e){let t=s(),n="anonymous";t[0]&&t[0].method&&(n=n),i.info(o,"["+n+"]",e.join(","))},t.warn=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),i.warn("<WARN>"+o,"["+n+"]",e.join(","))},t.error=function(...e){let t=s(),n="";t[0]&&t[0].method&&(n=n),i.error("<ERROR>"+o,"["+n+"]",e.join(","))},t.log=function(...e){i.log(o,...e)},t.assert=function(e,t){if(!e)throw new Error(t)},!process.env.DEBUG_ASYNC_REMOTE){let e=function(){};t.traceback=e,t.time=e,t.timeEnd=e,t.trace=e,t.info=e,t.warn=e,t.error=e,t.log=e,t.assert=e}},130:function(e,t,n){"use strict";var r=n(10);e.exports=function(e,t,n){return r.forEach(n,function(n){e=n(e,t)}),e}},131:function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},132:function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},133:function(e,t,n){"use strict";var r=n(72);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new r(e),t(n.reason))})}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e;return{token:new o(function(t){e=t}),cancel:e}},e.exports=o},134:function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},137:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(40),i=n(4),s=n(1),a=n(81),c=n(6),l=s.default.getLogger("FetchRes"),u={mock:"http://easy-mock.com/mock/59f0652c1bd72e7a888988ab/sl",test:"http://test.api-shoulei-ssl.xunlei.com",prod:"http://api-shoulei-ssl.xunlei.com"},d={"server error":"服务器异常","params error":"参数错误","login plz":"未登录","shield review":"风险账号","shield reject":"异常账号","not allowed":"无权限做此操作","not found":"未找到资源",exists:"已存在","too many":"超过操作上限"};function h(e){return(e=(e=encodeURIComponent(e)).replace(/\(/g,"%29")).replace(/\)/g,"%28")}function f({url:e,data:t,method:o="get"},s="prod"){return r(this,arguments,void 0,function*(){l.information("fetchFromApiProxy",arguments);try{const r=yield Promise.all([i.client.callServerFunction("GetUserID"),i.client.callServerFunction("GetSessionID"),i.client.callServerFunction("GetPeerID")]),[u,d,f]=r;l.information(r);let m={_h:Object.assign({"Account-Id":u,"Peer-Id":f,"User-Id":u,"Session-Id":d,"App-Type":"pc_xunlei"},t&&t._h||{})};t&&t._h&&delete t._h,t&&(t.peer_id=f);let p={accesskey:"pc.xunlei",nonce:Math.floor(*********Math.random()).toString(),timestamp:Math.floor((new Date).getTime()/1e3).toString()},w={};"get"===o&&t&&(w=t,t=null);for(let e in w)c.isObject(w[e])||(p[e]=w[e]);let g=[];for(let e in p)g.push(h(e)+"%3D"+h(p[e]));g.sort();let _=h(e),v=o.toUpperCase()+"&"+_+"&",y="";for(let e=0;e<g.length-1;++e)y+=g[e]+"%26";let b,C=v+(y+=g[g.length-1]);!t||"post"!==o&&"put"!==o||(C+="%26"+JSON.stringify(t)),l.information("message: ",C),b="test"===s?"c9879c94a55474304cca0abafb867653":"89917368930f3fea5bafebe704d6b623";let x=a.ToolsUtilitiesAWNS.encryptHmacBuffer("sha1",b,C,"base64");x=(x=x.replace(/\+/g,"-")).replace(/\//g,"_");let R=e+"?";for(let e in p)R+=h(e)+"="+h(p[e])+"&";R+="sig="+x,l.information("fetchFromApiProxy method",o,", uri",R,", data",t,", headers",m._h);const S=yield n(19);return S.defaults.adapter=n(17),S.defaults.headers["Content-Type"]="post"===o?"application/json":"application/x-www-form-urlencoded",S.request({method:o,url:R,data:t,headers:m._h}).then(e=>(l.information("fetchFromApiProxy success",e),e)).catch(e=>(l.information("fetchFromApiProxy failed",e),{error:e}))}catch(e){return{error:e}}})}function m({url:e,data:t,method:o="get"},s="prod"){return r(this,arguments,void 0,function*(){l.information("fetchFromApiProxy",arguments);try{const r=yield Promise.all([i.client.callServerFunction("GetUserID"),i.client.callServerFunction("GetSessionID"),i.client.callServerFunction("GetPeerID")]),[s,a,u]=r;l.information("fetchFromApiProxyNoSign",r);let d={_h:Object.assign({"Account-Id":s,"Peer-Id":u,"User-Id":s,"Session-Id":a,"App-Type":"pc_xunlei"},t&&t._h||{})};t&&t._h&&delete t._h,t&&(t.peer_id=u);let f={accesskey:"pc.xunlei",nonce:Math.floor(*********Math.random()).toString(),timestamp:Math.floor((new Date).getTime()/1e3).toString()},m={};"get"===o&&t&&(m=t,t=null);for(let e in m)c.isObject(m[e])||(f[e]=m[e]);let p=e;p.includes("?")?p+="&":p+="?";for(let e in f)p+=h(e)+"="+h(f[e])+"&";l.information("fetchFromApiProxy method",o,", uri",p,", data",t,", headers",d._h);const w=yield n(19);return w.defaults.adapter=n(17),w.defaults.headers["Content-Type"]="post"===o?"application/json":"application/x-www-form-urlencoded",w.request({method:o,url:p,data:t,headers:d._h}).then(e=>(l.information("fetchFromApiProxy success",e),e)).catch(e=>(l.information("fetchFromApiProxy failed",e),{error:e}))}catch(e){return{error:e}}})}function p({url:e,data:t,method:n="get"},o="prod",s=!0){return r(this,void 0,void 0,function*(){let r=u[o],a=yield i.client.callServerFunction("GetValue","ConfigFetchInterface","TestServer",0);return 1===a&&(r=u[o="test"]),l.information("当前fetchSlRes 是否测试服:",a," 远程地址:",r),s?f({url:`${r}${e}`,data:t,method:n},o):m({url:`${r}${e}`,data:t,method:n},o)})}function w(e,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"){return Array.apply(null,new Array(e)).map(function(){return t.charAt(Math.floor(Math.random()*t.length))}).join("")}function g(e,t=document.cookie){return null==t.match(new RegExp("(^"+e+"| "+e+")=([^;]*)"))?"":RegExp.$2}t.fetchFromApiProxy=f,t.fetchFromApiProxyNoSign=m,t.fetchSlRes=p,t.fetchPCRes=function({url:e,data:t,method:n="get"},o="prod"){return r(this,void 0,void 0,function*(){return p({url:e,data:t,method:n},o)})},t.fetchPCRequest=function({url:e,data:t,method:o="get"},s="prod"){return r(this,void 0,void 0,function*(){let r=u[s],a=yield i.client.callServerFunction("GetValue","ConfigFetchInterface","TestServer",0);1===a&&(r=u[s="test"]),l.information("当前fetchPCRequest 是否测试服:",a," 远程地址:",r);let c={_h:Object.assign({},t&&t._h||{})};t&&t._h&&delete t._h;const d=yield n(19);d.defaults.adapter=n(17),d.defaults.headers["Content-Type"]="post"===o?"application/json":"application/x-www-form-urlencoded";try{return d.request({method:o,url:`${r}${e}`,params:"get"===o&&t?t:{},data:"post"===o&&t?t:{},headers:c._h}).then(e=>(l.information("fetchRequest success",e),e)).catch(e=>(l.information("fetchRequest fail",e),{error:e}))}catch(e){return{error:e}}})},t.fetchRequest=function({url:e,data:t,method:o="get"}){return r(this,arguments,void 0,function*(){l.information("fetchRequest",arguments);try{const r=yield n(19);return r.defaults.adapter=n(17),r.defaults.headers["Content-Type"]="post"===o?"application/json":"application/x-www-form-urlencoded",r.request({method:o,url:e,params:"get"===o&&t?t:{},data:"post"===o&&t?t:{}}).then(e=>(l.information("fetchRequest success",e),e)).catch(e=>(l.information("fetchRequest fail",e),{error:e}))}catch(e){return{error:e}}})},t.fetchClientRes=function(e,...t){return r(this,void 0,void 0,function*(){return i.client.callServerFunction(e,...t)})},t.fetch=function(e,t){return r(this,void 0,void 0,function*(){const r=n(19);if(!t){let e=new o.Agent({rejectUnauthorized:!1});t={timeout:1e4,httpsAgent:e}}return r(e,t).then(e=>e.data)})},t.fetchCmtRes=function(e,t,o,s="prod"){return r(this,void 0,void 0,function*(){let r=u[s];t=`${r}${t}`;const a=yield Promise.all([i.client.callServerFunction("GetUserID"),i.client.callServerFunction("GetSessionID")]),[c,d]=a;let h=o&&o._h||[];delete o._h;let f={};"get"===e?(f=Object.assign({},o,{app_id:14,userid:c,session_id:d,v:"1.0",jsL2S:1,_:(new Date).getTime()}),o=null):o=Object.assign({},o,{app_id:14,userid:c,session_id:d,v:"1.0",jsL2S:1,_:(new Date).getTime()});const m=n(19).create();return m.defaults.timeout=1e4,m.defaults.adapter=n(17),c&&""!==c&&(m.defaults.headers["User-Id"]=c),d&&""!==d&&(m.defaults.headers["Session-Id"]=d),m.defaults.headers["Content-Type"]="post"===e?"application/json":"application/x-www-form-urlencoded",m.request({method:e,url:t,params:f,data:o,headers:h}).then(e=>{if(200===e.status&&e.data)return e.data;throw new Error(`请求评论接口 ${t} 失败`)}).catch(e=>{l.warning("fetchCmtRes error, catch axios res",e)})})},t.random=w,t.getCookie=g,t.fetchDataWithShoulei=function(e,t={},o={}){return r(this,void 0,void 0,function*(){e=`https://api-shoulei-ssl.xunlei.com${e}`;let s=yield function(e={},t={}){return r(this,void 0,void 0,function*(){const n=(t=Object.assign({},{method:e&&e._m||"get"},t)).method,r=!process.server&&document.cookie||"";let{userID:o=0,version:s,sessionID:c}=yield i.client.callServerFunction("GetUserInfoAw"),l=yield i.client.callServerFunction("GetPeerID");l=l||g("peerid",r)||g("deviceid",r);const u={userID:o,sessionID:c,version:s,guid:a.ToolsUtilitiesAWNS.genarateMd5(l||w(32)),peerId:l},d=[...e&&e._h||[]];let h;return delete e._h,delete e._m,"get"===n&&(h=e,e=null),{method:n,params:h,headerStrings:d,userInfo:u,data:e}})}(t,o),{method:c,params:u,userInfo:h}=s;const f=!process.server&&document.cookie||"",m=Object.assign({},{"Peer-Id":h.peerId,Guid:h.guid,"User-Id":h&&"0"!==h.userID?"":h.userID,"Session-Id":h.sessionID,"Product-Id":14,"Version-Code":"4.8.1","Account-Id":165,"Credit-Key":g("creditkey",f)},o.headers),p=n(19).create();return p.defaults.adapter=n(17),p.defaults.timeout=3e4,p.defaults.headers["Content-Type"]="post"===c?"application/json":"application/x-www-form-urlencoded",p({method:c,url:e,data:t,params:u,headers:m}).then(e=>("ok"!==e.data.result&&(e.data.errorText=d(e.data)),"shield reject"===e.data.result?Promise.resolve({response:e}):(e.data.result,e.data))).catch(e=>(l.warning("error",e),Promise.resolve(Object.assign({},e.response,{message:e.message}))))})}},14:function(e,t){e.exports=require("os")},15:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n,r,o,i,s,a,c,l,u,d,h,f,m,p,w,g,_,v,y,b,C;!function(e){e[e.Unkown=0]="Unkown",e[e.Create=1]="Create",e[e.InvaldParam=2]="InvaldParam",e[e.InvaldLink=3]="InvaldLink",e[e.InvaldConfig=4]="InvaldConfig",e[e.Timeout=5]="Timeout",e[e.VerifyData=6]="VerifyData",e[e.Forbidden=7]="Forbidden",e[e.RangeDispatch=8]="RangeDispatch",e[e.FilePathOverRanging=9]="FilePathOverRanging",e[e.FileCreate=201]="FileCreate",e[e.FileWrite=202]="FileWrite",e[e.FileRead=203]="FileRead",e[e.FileRename=204]="FileRename",e[e.FileFull=205]="FileFull",e[e.FileOccupied=211]="FileOccupied",e[e.FileAccessDenied=212]="FileAccessDenied",e[e.BtUploadExist=601]="BtUploadExist",e[e.ForbinddenResource=701]="ForbinddenResource",e[e.ForbinddenAccount=702]="ForbinddenAccount",e[e.ForbinddenArea=703]="ForbinddenArea",e[e.ForbinddenCopyright=704]="ForbinddenCopyright",e[e.ForbinddenReaction=705]="ForbinddenReaction",e[e.ForbinddenPorn=706]="ForbinddenPorn",e[e.DownloadSDKCrash=10001]="DownloadSDKCrash",e[e.torrentFileNotExist=10002]="torrentFileNotExist"}(t=e.TaskError||(e.TaskError={})),function(e){e[e.Unkown=-1]="Unkown",e[e.Success=0]="Success",e[e.QueryFailed=1]="QueryFailed",e[e.ServerError=2]="ServerError",e[e.ResourceNotFound=3]="ResourceNotFound",e[e.AuthorizingFailed=4]="AuthorizingFailed",e[e.ForbidByCopyright=5]="ForbidByCopyright",e[e.ForbidByPorNoGraphy=6]="ForbidByPorNoGraphy",e[e.ForbidByReactionary=7]="ForbidByReactionary",e[e.ForbidByOtherFilter=8]="ForbidByOtherFilter"}(n=e.DcdnStatusCode||(e.DcdnStatusCode={})),function(e){e[e.Begin=-1]="Begin",e[e.Unkown=0]="Unkown",e[e.StandBy=1]="StandBy",e[e.PreDownloading=2]="PreDownloading",e[e.StartWaiting=3]="StartWaiting",e[e.StartPending=4]="StartPending",e[e.Started=5]="Started",e[e.StopPending=6]="StopPending",e[e.Stopped=7]="Stopped",e[e.Succeeded=8]="Succeeded",e[e.Failed=9]="Failed",e[e.Seeding=10]="Seeding",e[e.DestroyPending=11]="DestroyPending",e[e.End=12]="End"}(r=e.TaskStatus||(e.TaskStatus={})),function(e){e[e.Begin=-1]="Begin",e[e.StandBy=0]="StandBy",e[e.Stopped=1]="Stopped",e[e.Started=2]="Started",e[e.Complete=3]="Complete",e[e.Forbidden=4]="Forbidden",e[e.Error=5]="Error",e[e.End=6]="End"}(o=e.BtFileStatus||(e.BtFileStatus={})),function(e){e[e.DispatchStrategyNone=0]="DispatchStrategyNone",e[e.DispatchStrategyOrigin=1]="DispatchStrategyOrigin",e[e.DispatchStrategyP2s=2]="DispatchStrategyP2s",e[e.DispatchStrategyP2p=4]="DispatchStrategyP2p",e[e.DispatchStrategyAll=-1]="DispatchStrategyAll"}(i=e.DispatchStrategy||(e.DispatchStrategy={})),function(e){e[e.Unkown=0]="Unkown",e[e.P2sp=1]="P2sp",e[e.Bt=2]="Bt",e[e.Emule=3]="Emule",e[e.Group=4]="Group",e[e.Magnet=5]="Magnet"}(s=e.TaskType||(e.TaskType={})),function(e){e[e.Invalid=0]="Invalid",e[e.P2sp=1]="P2sp",e[e.Emule=2]="Emule"}(a=e.TaskCfgType||(e.TaskCfgType={})),function(e){e.Unkown="Unkown",e.Downloading="Downloading",e.Completed="Completed",e.Recycle="Recycle"}(c=e.CategroyViewID||(e.CategroyViewID={})),function(e){e[e.Unknow=0]="Unknow",e[e.TaskCreated=1]="TaskCreated",e[e.InsertToCategoryView=2]="InsertToCategoryView",e[e.RemoveFromCategoryView=3]="RemoveFromCategoryView",e[e.StatusChanged=4]="StatusChanged",e[e.DetailChanged=5]="DetailChanged",e[e.ChannelInfoChanged=6]="ChannelInfoChanged",e[e.DcdnStatusChanged=7]="DcdnStatusChanged",e[e.TaskDescriptionChanged=8]="TaskDescriptionChanged",e[e.TaskUserRead=9]="TaskUserRead",e[e.TaskRenamed=10]="TaskRenamed",e[e.TaskMovedEnd=11]="TaskMovedEnd",e[e.TaskMovingStateChange=12]="TaskMovingStateChange",e[e.BtSubFileDetailChanged=13]="BtSubFileDetailChanged",e[e.BtSubFileLoaded=14]="BtSubFileLoaded",e[e.BtSubFileForbidden=15]="BtSubFileForbidden",e[e.BtSubFileDcdnStatusChanged=16]="BtSubFileDcdnStatusChanged",e[e.TaskCategoryMovedEnd=17]="TaskCategoryMovedEnd",e[e.GroupTaskSubFileDetailChanged=18]="GroupTaskSubFileDetailChanged",e[e.TaskDestroying=19]="TaskDestroying",e[e.TaskDestroyed=20]="TaskDestroyed"}(l=e.TaskEventType||(e.TaskEventType={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.VirtualId=2]="VirtualId",e[e.SrcTotal=3]="SrcTotal",e[e.SrcUsing=4]="SrcUsing",e[e.FileSize=5]="FileSize",e[e.ReceivedSize=6]="ReceivedSize",e[e.DownloadSize=7]="DownloadSize",e[e.TotalDownloadSize=8]="TotalDownloadSize",e[e.CreateTime=9]="CreateTime",e[e.CompletionTime=10]="CompletionTime",e[e.DownloadingPeriod=11]="DownloadingPeriod",e[e.Progress=12]="Progress",e[e.RecycleTime=13]="RecycleTime",e[e.FileCreated=14]="FileCreated",e[e.Forbidden=15]="Forbidden",e[e.CategoryId=16]="CategoryId",e[e.UserRead=17]="UserRead",e[e.OpenOnComplete=18]="OpenOnComplete",e[e.GroupTaskId=19]="GroupTaskId",e[e.DownloadSubTask=20]="DownloadSubTask",e[e.NameType=21]="NameType",e[e.OwnerProduct=22]="OwnerProduct",e[e.FileIndex=23]="FileIndex",e[e.NameFixed=24]="NameFixed",e[e.ValidDownloadSize=25]="ValidDownloadSize",e[e.RealDownloadSize=26]="RealDownloadSize",e[e.ResourceLegal=27]="ResourceLegal",e[e.TaskType=28]="TaskType",e[e.ErrorCode=29]="ErrorCode",e[e.PlayPosition=30]="PlayPosition",e[e.Duration=31]="Duration",e[e.NumberEnd=32]="NumberEnd",e[e.BooleanStart=4096]="BooleanStart",e[e.Destroyed=4097]="Destroyed",e[e.Background=4098]="Background",e[e.Moving=4099]="Moving",e[e.BooleanEnd=4100]="BooleanEnd",e[e.StringStart=8192]="StringStart",e[e.TaskName=8193]="TaskName",e[e.SavePath=8194]="SavePath",e[e.RelativePath=8195]="RelativePath",e[e.TaskUrl=8196]="TaskUrl",e[e.RefUrl=8197]="RefUrl",e[e.Cid=8198]="Cid",e[e.Gcid=8199]="Gcid",e[e.Cookie=8200]="Cookie",e[e.ProductInfo=8201]="ProductInfo",e[e.Origin=8202]="Origin",e[e.Description=8203]="Description",e[e.UserData=8204]="UserData",e[e.OriginName=8205]="OriginName",e[e.HTTPContentType=8206]="HTTPContentType",e[e.CategoryViewId=8207]="CategoryViewId",e[e.YunTaskId=8208]="YunTaskId",e[e.StringEnd=8209]="StringEnd",e[e.ObjectStart=12288]="ObjectStart",e[e.ObjectEnd=12289]="ObjectEnd"}(u=e.TaskAttribute||(e.TaskAttribute={})),function(e){e[e.UnKnown=0]="UnKnown",e[e.SrcTotal=1]="SrcTotal",e[e.SrcUsing=2]="SrcUsing",e[e.FileSize=4]="FileSize",e[e.ReceivedSize=8]="ReceivedSize",e[e.DownloadSize=16]="DownloadSize",e[e.CompletionTime=32]="CompletionTime",e[e.DownloadingPeriod=64]="DownloadingPeriod",e[e.Progress=128]="Progress",e[e.RecycleTime=256]="RecycleTime",e[e.FileCreated=512]="FileCreated",e[e.Forbidden=1024]="Forbidden",e[e.UserRead=2048]="UserRead",e[e.OpenOnComplete=4096]="OpenOnComplete",e[e.DownloadSubTask=8192]="DownloadSubTask",e[e.TaskName=16384]="TaskName",e[e.SavePath=32768]="SavePath",e[e.Cid=65536]="Cid",e[e.Gcid=131072]="Gcid",e[e.UserData=262144]="UserData",e[e.CategoryViewId=524288]="CategoryViewId",e[e.ErrorCode=1048576]="ErrorCode",e[e.TaskSpeed=2097152]="TaskSpeed",e[e.ChannelInfo=4194304]="ChannelInfo",e[e.ValidDownloadSize=8388608]="ValidDownloadSize",e[e.OriginName=16777216]="OriginName",e[e.HTTPContentType=33554432]="HTTPContentType",e[e.PlayPosition=67108864]="PlayPosition",e[e.Duration=134217728]="Duration",e[e.YunTaskId=268435456]="YunTaskId"}(d=e.TaskDetailChangedFlags||(e.TaskDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(h=e.BtSubFileDetailChangedFlags||(e.BtSubFileDetailChangedFlags={})),function(e){e[e.UnKnown=0]="UnKnown"}(f=e.GroupTaskSubFileDetailChangedFlags||(e.GroupTaskSubFileDetailChangedFlags={})),function(e){e[e.NumberStrart=0]="NumberStrart",e[e.TaskId=1]="TaskId",e[e.FileStatus=2]="FileStatus",e[e.DownloadSize=3]="DownloadSize",e[e.FileSize=4]="FileSize",e[e.EnableDcdn=5]="EnableDcdn",e[e.ErrorCode=6]="ErrorCode",e[e.DcdnStatus=7]="DcdnStatus",e[e.RealIndex=8]="RealIndex",e[e.FileOffset=9]="FileOffset",e[e.Visible=10]="Visible",e[e.Download=11]="Download",e[e.UserRead=12]="UserRead",e[e.PlayPosition=13]="PlayPosition",e[e.Duration=14]="Duration",e[e.NumberEnd=15]="NumberEnd",e[e.StringStart=4096]="StringStart",e[e.FinalName=4097]="FinalName",e[e.RelativePath=4098]="RelativePath",e[e.FileName=4099]="FileName",e[e.FilePath=4100]="FilePath",e[e.Cid=4101]="Cid",e[e.Gcid=4102]="Gcid",e[e.StringEnd=4103]="StringEnd"}(m=e.BtFileAttribute||(e.BtFileAttribute={})),function(e){e[e.P2spUrl=0]="P2spUrl",e[e.BtInfoId=1]="BtInfoId",e[e.EmuleFileHash=2]="EmuleFileHash",e[e.MagnetUrl=3]="MagnetUrl",e[e.GroupTaskName=4]="GroupTaskName"}(p=e.KeyType||(e.KeyType={})),function(e){e[e.NameInclude=1]="NameInclude",e[e.BtDisplayNameInclude=2]="BtDisplayNameInclude"}(w=e.SearchKeyType||(e.SearchKeyType={})),function(e){e[e.ExtEqual=1]="ExtEqual",e[e.NameLikeAndExtEqual=2]="NameLikeAndExtEqual",e[e.TaskTypeEqual=4]="TaskTypeEqual"}(g=e.FilterKeyType||(e.FilterKeyType={})),function(e){e[e.All=0]="All",e[e.CommonForeground=1]="CommonForeground",e[e.CommonBackground=2]="CommonBackground",e[e.Temporary=3]="Temporary",e[e.PreDownload=4]="PreDownload",e[e.PrivateForeground=5]="PrivateForeground"}(_=e.KeyFilter||(e.KeyFilter={})),function(e){e[e.Unknown=-1]="Unknown",e[e.LoadTaskBasic=0]="LoadTaskBasic",e[e.Create=1]="Create",e[e.Complete=2]="Complete",e[e.Recycle=3]="Recycle",e[e.Recover=4]="Recover",e[e.ReDownload=5]="ReDownload",e[e.MoveThoughCategory=6]="MoveThoughCategory"}(v=e.TaskInsertReason||(e.TaskInsertReason={})),function(e){e[e.Unknown=-1]="Unknown",e[e.Manual=0]="Manual",e[e.PauseAll=1]="PauseAll",e[e.DeleteTask=2]="DeleteTask",e[e.TaskJammed=3]="TaskJammed",e[e.LowSpeed=4]="LowSpeed",e[e.MaxDownloadReduce=5]="MaxDownloadReduce",e[e.MoveTask=6]="MoveTask",e[e.SelectDownloadLists=7]="SelectDownloadLists",e[e.PrivateLoginOut=8]="PrivateLoginOut",e[e.FreeDownload=9]="FreeDownload",e[e.Exit=10]="Exit"}(y=e.TaskStopReason||(e.TaskStopReason={})),function(e){e[e.RESOURCE_FROM_MEMBER=1]="RESOURCE_FROM_MEMBER",e[e.RESOURCE_FROM_OFFLINE=2]="RESOURCE_FROM_OFFLINE",e[e.RESOURCE_FROM_CRYSTAL_LARGE=4]="RESOURCE_FROM_CRYSTAL_LARGE",e[e.RESOURCE_FROM_CRYSTAL_SMALL=8]="RESOURCE_FROM_CRYSTAL_SMALL",e[e.RESOURCE_FROM_DCDN=16]="RESOURCE_FROM_DCDN",e[e.RESOURCE_FROM_FREEDCDN=32]="RESOURCE_FROM_FREEDCDN"}(b=e.XLResourceFrom||(e.XLResourceFrom={})),function(e){e[e.XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD=0]="XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD",e[e.XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING=1]="XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING",e[e.XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING=2]="XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING"}(C=e.XLDownloadStrategy||(e.XLDownloadStrategy={}))}(t.DownloadKernel||(t.DownloadKernel={}))},157:function(e,t,n){"use strict";var r=n(1078);n.n(r).a},16:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(3),i=n(2),s=n(1),a=n(18),c=n(25),l=s.default.getLogger("Thunder.Util"),u="Thunder Network\\Thunder7.9\\";function d(e){let t=e;return 0===e.indexOf('"')&&e.lastIndexOf('"')===e.length-1?t=e.substring(1,e.length-1):0===e.indexOf("'")&&e.lastIndexOf("'")===e.length-1&&(t=e.substring(1,e.length-1)),t}!function(e){function t(){let e=c.ThunderHelper.getSystemTempPath(),t=c.ThunderHelper.getLogicalDriveStrings(),n=0;for(let r=0;r<t.length;r++){if(c.ThunderHelper.getDriveType(t[r])===c.ThunderHelper.DriverType.DRIVE_FIXED){let o=c.ThunderHelper.getDriveInfo(t[r]);n<o.freeBytes&&t[r]!==e&&(n=o.freeBytes,e=t[r])}}return e.substring(0,1)+":\\迅雷下载"}function s(e){let t=(e.style.webkitTransform||getComputedStyle(e,"").getPropertyValue("-webkit-transform")||e.style.transform||getComputedStyle(e,"").getPropertyValue("transform")).match(/\-?[0-9]+\.?[0-9]*/g);return{x:parseInt(t&&(t[12]||t[4])||"0",10),y:parseInt(t&&(t[13]||t[5])||"0",10)}}function h(e){let t=!1;do{let n="",r="";if(/^[a-zA-Z]:\\/.test(e))n=e.slice(3);else{if(0!==e.indexOf("\\\\"))break;{let t=e.indexOf("\\",2);if(-1===t||t===e.length-1)break;if(""===(r=(n=e.slice(2)).substr(0,t-2)))break}}if(/[\*\"<>\?:\|]/i.test(n))break;if(e.length>256)break;if(""===r){t=!0;break}let o=r.indexOf(".ipv6-literal.net");-1!==o?(-1!==(o=(r=r.substr(0,o)).indexOf("%"))&&(r=r.substr(0,o)),r=r.replace(/\-/g,":"),/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/.test(r)&&(t=!0)):/(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))/.test(r)&&(t=!0)}while(0);return t}e.formatSize=function(e,t){0===t||(t=t||2);let n="0B";if("number"==typeof e&&e>0){let r=["B","KB","MB","GB","TB"],o=0,i=e;for(;i>=1e3&&!(o>=4);)i/=1024,o+=1;n=-1===String(i).indexOf(".")?i+r[o]:i.toFixed(t)+r[o]}return n},e.formatSizeCustom=function(e,t=2,n=5){let r="0B";if("number"==typeof e&&e>0){let o=["B","KB","MB","GB","TB"],i=0,s=e;for(;s>=1e3&&!(i>=4);)s/=1024,i+=1;if(-1===String(s).indexOf("."))r=s+o[i];else{let e=s.toFixed(t);e.length<=n?r="KB"!==o[i]&&"MB"!==o[i]||1===t?e+o[i]:s.toFixed(1)+o[i]:("."===(e=e.substr(0,n))[n-1]&&(e=e.substr(0,n-1)),r=e+o[i])}}return r},e.isDigital=function(e){let t=!1;return/^\d+$/.test(e)&&(t=!0),t},e.isAlpha=function(e){let t=!1;return/[A-Za-z]/.test(e)&&(t=!0),t},e.isUpperCase=function(e){let t=!1;return/[A-Z]/.test(e)&&(t=!0),t},e.isLowerCase=function(e){let t=!1;return/[a-z]/.test(e)&&(t=!0),t},e.isChinese=function(e){let t=!1;return/[\u4E00-\u9FA5]/.test(e)&&(t=!0),t},e.replaceNonDigital=function(e){return e.replace(/[^\d]/g,"")},e.replaceNonAlpha=function(e){return e.replace(/[^A-Za-z]/g,"")},e.replaceNonWord=function(e){return e.replace(/[^\W]/g,"")},e.getDefaultDownloadDir=t,e.getMaxFreeDriver=function(){return t().substring(0,1)},e.deepCopy=function(e){let t=JSON.stringify(e),n=null;try{n=JSON.parse(t)}catch(e){l.warning(e)}return n},e.swap=function(e,t,n){do{if(t<0||t>=e.length)break;if(n<0||n>=e.length)break;if(t===n)break;e[t]=e.splice(n,1,e[t])[0]}while(0);return e},e.compareNocase=function(e,t){let n=!1;do{if(void 0===e&&void 0===t){n=!0;break}if(void 0===e||void 0===t)break;if("string"!=typeof e||"string"!=typeof t)break;n=e.toLowerCase()===t.toLowerCase()}while(0);return n},e.parseCommandLine=function(e){let t=0,n="",r=!1,o=[],i=e.length;for(let s=0;s<i;s++){let a=e[s];if('"'!==a&&"'"!==a||(""===n?(r=!0,n=a):n===a&&(r=!1,n=""))," "!==a||r||s===i-1){if(s===i-1){let n=e.substring(t);""!==n.trim()&&o.push(d(n))}}else{let n=e.substring(t,s);""!==n.trim()&&o.push(d(n)),t=s+1}}return o},e.getThunderTempPath=function(e,t){return r(this,void 0,void 0,function*(){const r=yield Promise.resolve().then(()=>n(14));let o=i.join(r.tmpdir(),u);return t&&(o=i.join(o,t)),void 0!==e&&e&&(yield a.FileSystemAWNS.mkdirsAW(o)),o})},e.setQueryString=function(e,t){return Object.keys(t).forEach((n,r)=>{e+=0===r?"?":"&",e+=`${n}=${t[n]}`}),e},e.setQueryStringEx=function(e,t){for(let n in t)e+=-1===e.indexOf("?")?"?":"&",e+=`${n}=${t[n]}`;return e},e.getQueryString=function(e,t){return e&&t&&e.match(new RegExp(`(^${t}|[?|&]${t})=([^&#]+)`))?RegExp.$2:""},e.isClipboardTextFormatAvailable=function(){let e=!1,t=o.clipboard.availableFormats();for(let n of t)if("text/plain"===n){e=!0;break}return e},e.keywordsHighLight=function(e,t,n){if(!e)return"";if(!t)return e;if(0===e.length)return e;if(0===t.length)return e;let r=/\\/,o=t.split(" ");if(0===(o=o.filter(e=>e.trim().length>0)).length)return e;for(let t=0;t<o.length;t++)if(o[t].search(r)>0)return e;n=void 0===n||null===n?"#FF0000":n;let i="",s=["\\[","\\^","\\*","\\(","\\)","\\|","\\?","\\$","\\.","\\+"],a="",c="|";for(let e=0;e<o.length;e++){for(let t=0;t<s.length;t++){let n=new RegExp(s[t],"g");o[e]=o[e].replace(n,s[t])}e===o.length-1&&(c=""),a=a.concat(o[e],c)}let l=new RegExp(a,"gi");return i=e.replace(l,e=>'<span style= "color:'+n+'">'+e+"</span>")},e.isDef=function(e){return void 0!==e&&null!==e},e.isUndef=function(e){return void 0===e||null===e},e.setStyle=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style[t]=n})},e.setCSSProperties=function(e,t){Object.entries(t).forEach(([t,n])=>{e.style.setProperty(t,n)})},e.versionCompare=function(e,t){let n=e.split("."),r=t.split("."),o=0;for(let e=0;e<n.length;e++){if(Number(n[e])-Number(r[e])>0){o=1;break}if(Number(n[e])-Number(r[e])<0){o=-1;break}}return o},e.throttle=function(e,t){let n,r=0;return(...o)=>{const i=Date.now();clearTimeout(n),i-r>t?(e(...o),r=i):n=setTimeout(()=>{e(...o),r=i},t)}},e.debounce=function(e,t){let n=null;return(...r)=>{n&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}},e.getElementFixed=function(e){let t=e.offsetLeft,n=e.offsetTop,r=e.offsetParent;for(;null!==r;){let e=s(r);t+=r.offsetLeft+e.x,n+=r.offsetTop+e.y,r=r.offsetParent}return{x:t,y:n}},e.escapeHTML=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")},e.unescapeHTML=function(e){return e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")},e.isValidPath=h,e.isValidDownloadPath=function(e){return r(this,void 0,void 0,function*(){let t=!1;do{if(e.length<3)break;if("私人空间"===e){t=!0;break}if(c.ThunderHelper.getDriveType(e)===c.ThunderHelper.DriverType.DRIVE_REMOTE){t=!0;break}if(!h(e))break;if(!(yield a.FileSystemAWNS.dirExistsAW(e))&&!(yield a.FileSystemAWNS.mkdirsAW(e)))break;t=!0}while(0);return t})};let f=void 0;function m(e,t="normal 12px sans-serif"){f||(f=document.createElement("canvas"));let n=f.getContext("2d");return n.font=t,n.measureText(e).width}function p(e,t,n="normal 12px sans-serif",r=1){function o(e,t,r,i,s){let a=-1;do{if(e>t){a=t;break}let c=Math.round((e+t)/2),l=m(`${r.substr(0,c)}...${i}`,n);if(s===l){a=c;break}if(s>l){if(Math.round(s)===Math.round(l)){a=c;break}a=o(c+1,t,r,i,s)}else if(l>s){if(Math.round(s)===Math.round(l)){a=c-1;break}a=o(e,c-1,r,i,s)}}while(0);return a}let s=e;do{if(!t)break;if(!e)break;let a=t.offsetWidth*r;if(a>m(e,n))break;let c=i.extname(e);""!==c&&(c=c.substring(1));let l=e.substr(0,e.length-c.length-1);if(!l)break;let u=o(0,l.length,l,c,a);if(-1===u)break;s=`${l.substr(0,u-2*(r-1))}...${c}`}while(0);return s}e.getTextWidth=m,e.getOmitName=p,e.getOmitNameMultiLine=function(e,t,n){return p(e,t,"normal 12px microsoft yahei",2)},e.setTimeoutAw=function(e,t){return new Promise((n,r)=>{setTimeout(()=>{t&&t(),n()},e)})}}(t.ThunderUtil||(t.ThunderUtil={}))},17:function(e,t,n){"use strict";var r=n(10),o=n(67),i=n(69),s=n(44),a=n(40),c=n(70).http,l=n(70).https,u=n(34),d=n(48),h=n(127),f=n(46),m=n(68);e.exports=function(e){return new Promise(function(t,n){var p,w=e.data,g=e.headers;if(g["User-Agent"]||g["user-agent"]||(g["User-Agent"]="axios/"+h.version),w&&!r.isStream(w)){if(Buffer.isBuffer(w));else if(r.isArrayBuffer(w))w=new Buffer(new Uint8Array(w));else{if(!r.isString(w))return n(f("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",e));w=new Buffer(w,"utf-8")}g["Content-Length"]=w.length}var _=void 0;e.auth&&(_=(e.auth.username||"")+":"+(e.auth.password||""));var v=u.parse(e.url),y=v.protocol||"http:";if(!_&&v.auth){var b=v.auth.split(":");_=(b[0]||"")+":"+(b[1]||"")}_&&delete g.Authorization;var C="https:"===y,x=C?e.httpsAgent:e.httpAgent,R={path:i(v.path,e.params,e.paramsSerializer).replace(/^\?/,""),method:e.method,headers:g,agent:x,auth:_};e.socketPath?R.socketPath=e.socketPath:(R.hostname=v.hostname,R.port=v.port);var S,k=e.proxy;if(!k&&!1!==k){var E=y.slice(0,-1)+"_proxy",T=process.env[E]||process.env[E.toUpperCase()];if(T){var I=u.parse(T);if(k={host:I.hostname,port:I.port},I.auth){var M=I.auth.split(":");k.auth={username:M[0],password:M[1]}}}}if(k&&(R.hostname=k.host,R.host=k.host,R.headers.host=v.hostname+(v.port?":"+v.port:""),R.port=k.port,R.path=y+"//"+v.hostname+(v.port?":"+v.port:"")+R.path,k.auth)){var O=new Buffer(k.auth.username+":"+k.auth.password,"utf8").toString("base64");R.headers["Proxy-Authorization"]="Basic "+O}e.transport?S=e.transport:0===e.maxRedirects?S=C?a:s:(e.maxRedirects&&(R.maxRedirects=e.maxRedirects),S=C?l:c),e.maxContentLength&&e.maxContentLength>-1&&(R.maxBodyLength=e.maxContentLength);var D=S.request(R,function(r){if(!D.aborted){clearTimeout(p),p=null;var i=r;switch(r.headers["content-encoding"]){case"gzip":case"compress":case"deflate":i=i.pipe(d.createUnzip()),delete r.headers["content-encoding"]}var s=r.req||D,a={status:r.statusCode,statusText:r.statusMessage,headers:r.headers,config:e,request:s};if("stream"===e.responseType)a.data=i,o(t,n,a);else{var c=[];i.on("data",function(t){c.push(t),e.maxContentLength>-1&&Buffer.concat(c).length>e.maxContentLength&&n(f("maxContentLength size of "+e.maxContentLength+" exceeded",e,null,s))}),i.on("error",function(t){D.aborted||n(m(t,e,null,s))}),i.on("end",function(){var r=Buffer.concat(c);"arraybuffer"!==e.responseType&&(r=r.toString("utf8")),a.data=r,o(t,n,a)})}}});D.on("error",function(t){D.aborted||n(m(t,e,null,D))}),e.timeout&&!p&&(p=setTimeout(function(){D.abort(),n(f("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",D))},e.timeout)),e.cancelToken&&e.cancelToken.promise.then(function(e){D.aborted||(D.abort(),n(e))}),r.isStream(w)?w.pipe(D):D.end(w)})}},18:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(21),i=n(2),s=n(6),a=n(60),c=s.promisify,l=n(1).default.getLogger("Thunder.base.fs-utilities");!function(e){function t(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=c(o.access);try{yield n(e),t=!0}catch(e){l.information(e)}}return t})}function s(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=c(o.mkdir);try{yield n(e),t=!0}catch(e){l.warning(e)}}return t})}function u(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=c(o.rmdir);try{yield n(e),t=!0}catch(e){l.warning(e)}}return t})}function d(e){return r(this,void 0,void 0,function*(){let t=!1;if(void 0!==e){const n=c(o.unlink);try{yield n(e),t=!0}catch(e){l.warning(e)}}return t})}function h(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=c(o.readdir);try{t=yield n(e)}catch(e){l.warning(e)}}return t})}function f(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=c(o.lstat);try{t=yield n(e)}catch(e){l.warning(e)}}return t})}function m(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&void 0!==t){let r=i.join(e,t),o=yield f(r);n=null!==o&&o.isDirectory()?yield p(r):yield d(r)}return n})}function p(e){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e){if(yield t(e)){n=!0;let t=(yield h(e))||[];for(let r=0;r<t.length;r++)n=(yield m(e,t[r]))&&n;(n||0===t.length)&&(n=(yield u(e))&&n)}}return n})}function w(e){return r(this,void 0,void 0,function*(){let n=!1;return l.information("mkdirsAW",e),void 0!==e&&((yield t(e))?n=!0:i.dirname(e)===e?n=!1:(yield w(i.dirname(e)))&&(n=yield s(e))),n})}function g(e,n){return r(this,void 0,void 0,function*(){let r;if(e.toLowerCase()!==n.toLowerCase()&&(yield t(e))){let t=o.createReadStream(e),i=o.createWriteStream(n);r=new Promise(e=>{t.pipe(i).on("finish",()=>{e(!0)})})}else r=new Promise(e=>{e(!1)});return r})}e.readFileAW=function(e){return r(this,void 0,void 0,function*(){let t=null;if(void 0!==e){const n=c(o.readFile);try{t=yield n(e)}catch(e){l.warning(e)}}return t})},e.readLineAw=function(e){return r(this,void 0,void 0,function*(){let n=null;do{if(!e)break;if(!t(e))break;n=yield new Promise(t=>{let n=[];const r=o.createReadStream(e),i=a.createInterface({input:r});i.on("line",e=>{n.push(e)}),i.on("close",()=>{t(n)})})}while(0);return n})},e.writeFileAW=function(e,t){return r(this,void 0,void 0,function*(){let n=!1;if(void 0!==e&&null!==t){const r=c(o.writeFile);try{yield r(e,t),n=!0}catch(e){l.warning(e)}}return n})},e.existsAW=t,e.dirExistsAW=function(e){return r(this,void 0,void 0,function*(){let n=!1;do{if(!(n=yield t(e)))break;let r=yield f(e);if(!r)break;n=r.isDirectory()}while(0);return n})},e.mkdirAW=s,e.rmdirAW=u,e.unlinkAW=d,e.readdirAW=h,e.lstatAW=f,e.rmdirsAW=p,e.mkdirsAW=w,e.renameAW=function(e,t){return r(this,void 0,void 0,function*(){if(void 0!==e&&void 0!==t){const n=c(o.rename);try{yield n(e,t)}catch(e){l.warning(e)}}})},e.copyFileAW=g,e.copyDirsAW=function e(n,o){return r(this,void 0,void 0,function*(){let r=!1,s=yield f(n);if(s.isDirectory()){r=yield w(o);let a=(yield h(n))||[];for(let c=0;c<a.length;c++){let l=i.join(n,a[c]),u=i.join(o,a[c]);(r=yield t(l))&&(r=(s=yield f(l)).isDirectory()?yield e(l,u):yield g(l,u))}}return r})},e.mkdtempAW=function(){return r(this,void 0,void 0,function*(){let e=!1;const t=c(o.mkdtemp),r=(yield Promise.resolve().then(()=>n(14))).tmpdir();try{e=yield t(`${r}${i.sep}`)}catch(e){l.warning(e)}return e})},e.deleteEmptySubDirs=function(e,n){return r(this,void 0,void 0,function*(){let r=!0;e=i.normalize(e),n=i.normalize(n),e.length>3&&"\\"===e[e.length-1]&&(e=e.slice(0,e.length-1)),n.length>3&&"\\"===n[n.length-1]&&(n=n.slice(0,n.length-1));do{if(0!==e.indexOf(n)){r=!1;break}let o=e;for(;o!==n;){if((yield t(o))&&!(yield u(o))){r=!1;break}o=i.dirname(o)}}while(0);return r})},e.getFileSize=function e(n){return r(this,void 0,void 0,function*(){let r=0;do{if(!n)break;if(!(yield t(n)))break;let o=yield f(n);if(o)if(o.isDirectory()){let t=yield h(n);for(let o=0;o<t.length;o++)r+=(yield e(i.join(n,t[o])))}else r=o.size}while(0);return r})},e.isDirectoryEmptyAW=function(e,n=!0){return r(this,void 0,void 0,function*(){let r=!0;do{if(!e){r=!1;break}if(!(yield t(e))){r=n;break}let o=yield f(e);if(!o){r=!1;break}if(!o.isDirectory()){r=!1;break}if((yield h(e)).length>0){r=!1;break}}while(0);return r})}}(t.FileSystemAWNS||(t.FileSystemAWNS={}))},185:function(e,t,n){"use strict";var r=n(1163);n.n(r).a},19:function(e,t,n){e.exports=n(115)},2:function(e,t){e.exports=require("path")},20:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.channelRMNewTaskReadyForSetTaskData="RM_NEWTASK_READYRORSETTASKDATA",e.channelRMNewTaskSetTaskData="RM_NEWTASK_SETTASKDATA",e.channelRMPreNewTaskSetTaskData="RM_PRENEWTASK_SETTASKDATA",e.channelRMNewTaskCreateNewTask="RM_NEWTASK_CREATENEWTASK",e.channelRMNewTaskClose="RM_NEWTASK_CLOSE",e.channelRMPreNewTaskClose="RM_PRENEWTASK_CLOSE",e.channelRMNewTaskSetBTInfo="RM_NEWTASK_SETBTINFO",e.channelRMNewTaskDownloadTorrent="RM_NEW_TASK_DOWNLOAD_TORRENT",e.channelRMNewTaskCreateBtTask="RM_NEWTASK_CRATEBTTASK",e.channelRMNewTaskCancleMagnet="RM_NEWTASK_CANCLE_MAGNET",e.channelRMImportTorrent="RM_NEWTASK_IMPORT_TORRENT",e.channelRMGetConfigValueResolve="RM_GET_CONFIG_VALUE_RESOLVE",e.channelRMGetConfigValueReject="RM_GET_CONFIG_VALUE_REJECT",e.channelRMSetConfigValueReject="RM_SET_CONFIG_VALUE_REJECT",e.channelMRTrayMenuClick="MR_TRAY_MENU_CLICK",e.channelMRNewTaskMagnetTaskCreated="MR_NEW_TASK_MAGNET_TASK_CREATED",e.channelMRNewTaskDownloadTorrentResult="MR_NEW_TASK_DOWNLOAD_TORRENT_RESULT",e.channelMRNewTaskCreateNewTaskResult="MR_NEWTASK_CREATENEWTASK_RESULT",e.channelMRNewTaskCreateBtTaskResult="RM_NEWTASK_CRATEBTTASK_RESULT",e.channelMRGetConfigValue="MR_GET_CONFIG_VALUE",e.channelMRSetConfigValue="MR_SET_CONFIG_VALUE",e.channelRMCommitPlanTask="RM_PLANTASK_COMMIT",e.channelRMPerformePlanTask="RM_PLANTASK_PERFORME",e.channelRMProcessSend="RM_RENDER_PROCESS_INFO",e.channelRMGetPrivateSpaceInfo="RM_RENDER_GET_PRIVATE_SPACE_INFO",e.channelMRGetPrivateSpaceInfoResult="MR_RENDER_GET_PRIVATE_SPACE_INFO_RESULT",e.channelRMFileCopy="RM_FILE_COPY",e.channelRMFileMove="RM_FILE_MOVE",e.channelMRFileCopyResult="MR_FILE_COPY_RESULT",e.channelMRFileMoveResult="MR_FILE_MOVE_RESULT",e.channelRMGetSutitleByCid="RM_RENDER_GET_SUBTITLE_BY_CID",e.channelMRGetSutitleByCidResult="MR_RENDER_GET_SUBTITLE_BY_CID_RESULT",e.channelRMGetSutitleByName="RM_RENDER_GET_SUBTITLE_BY_NAME",e.channelMRGetSutitleByNameResult="MR_RENDER_GET_SUBTITLE_BY_NAME_RESULT",e.channelRMDownloadSutitle="RM_RENDER_DOWNLOAD_SUBTITLE",e.channelMRDownloadSutitleSuc="MR_RENDER_DOWNLOAD_SUBTITLE_SUCCESS",e.channelMRDownloadSutitleFail="MR_RENDER_DOWNLOAD_SUBTITLE_FAIL",e.channelRMGetDisplayName="RM_RENDER_GET_SUBTITLE_DISPLAYNAME",e.channelMRGetDisplayNameResult="MR_RENDER_GET_SUBTITLE_DISPLAYNAME_RESULT",e.channelMRBringWindowToTop="MR_RENDER_BRING_WINDOW_TO_TOP",e.channelRMDownloadXmp="RM_RENDER_DOWNLOAD_XMP",e.channelRMXmpFixBoxCreated="RM_RENDER_XMPFIXBOX_CREATED",e.channelMRFixXmpSuc="MR_RENDER_FIX_XMP_SUC",e.channelMRFixXMPFail="MR_RENDER_FIX_XMP_FAIL",e.channelRMDownloadXmpEx="RM_RENDER_DOWNLOAD_XMP_EX",e.channelRMSetPosition="RM_SET_POSITION",e.channelRMSetFoucs="RM_SET_FOCUS",e.channelRMCreateAddressDropWnd="RM_CREATE_ADDRESS_DROPWND",e.channelRMRefreshAddressDropWnd="RM_REFRESH_ADDRESS_DROPWND",e.channelRMSelectAddressDropItem="RM_SELECT_ADDRESS_DROPITEM",e.channelRMCreateSearchWindow="RM_CREATE_SEARCH_WINDOW",e.channelRMShowSearchWindow="RM_SHOW_SEARCH_WINDOW",e.channelRMAddressKeyDown="RM_ADDRESS_BAR_KEYDOWN",e.channelMRFWAddressKeyDown="MR_ADDRESS_FW_BAR_KEYDOWN",e.channelMRFWSelectAddressDropItem="MR_FW_SELECT_ADDRESS_DROPITEM",e.channelRMAddressDropWndKeyDown="RM_ADDRESS_DROPWND_KEYDOWN",e.channelRMClickMouse="RM_CLICK_MOUSE",e.channelMRSearchBarFocusChange="MR_SEARCHBAR_FOCUS_CHANGE",e.channelMRFWAddressDropWndKeyDown="MR_FW_ADDRESS_DROPWND_KEYDOWN",e.channelMRClickAddressDropItem="MR_CLICK_ADDRESS_DROPITEM",e.channelMRMainWndMax="MR_MAINWINDOW_MAX",e.channelMRMainWndMin="MR_MAINWINDOW_MIN",e.channelMRMainWndRestore="MR_MAINWINDOW_RESTORE",e.channelRMGetBrowserStartType="RM_GET_BROWSER_START_TYPE",e.channelMRGetBrowserStartTypeResult="MR_GET_BROWSER_START_TYPE_RESULT",e.channelRMExecute="RM_SHELL_EXECUTE",e.channelMRExecuteResult="MR_SHELL_EXECUTE_RESULT",e.channelMRAdTipsClick="MR_AD_TIPS_CLICK",e.channelMRNotificationMsg="MR_NOTIFICATION_MSG",e.channelRMSetProgressBar="RM_SET_PROGRESS_BAR",e.channelRMRoundWindow="RM_ROUND_WINDOW",e.channelMRShowOrHideWindow="MR_SHOW_OR_HIDE_WINDOW",e.channelMRSuspensionWindowShowOrHide="MR_SUSPENSION_WINDOW_SHOW_OR_HIDE",e.channelRMConfigInitFinished="RM_CONFIG_INIT_FINISHED",e.channelRMConfigValueChanged="RM_CONFIG_VALUE_CHANGED",e.channelRMIndividuationBrowserMsg="RM_INDIVIDUATION_BROWSER_MSG",e.channelMRIndividuationBrowserMsg="MR_INDIVIDUATION_BROWSER_MSG",e.channelRMSetEnvironmentVariable="RM_SET_ENVIRONMENT_VARIABLE",e.channelMREmbedPlayerPos="MR_EMBED_PLAYER_POSITION",e.channelRMUpdateLogEnviroment="RM_UPDATE_LOG_ENVIRONMENT",e.channelMRUpdateLogEnviroment="MR_UPDATE_LOG_ENVIRONMENT"}(t.ThunderChannelList||(t.ThunderChannelList={}))},21:function(e,t){e.exports=require("fs")},22:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=function(...e){},t.error=function(...e){},t.warning=function(...e){},t.critical=function(...e){},t.verbose=function(...e){},"development"===process.env.LOGGER_ENV&&(t.information=function(...e){console.log("information",e)},t.error=function(...e){console.log("error",e)},t.warning=function(...e){console.log("warning",e)},t.critical=function(...e){console.log("critical",e)},t.verbose=function(...e){console.log("verbose",e)})},23:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.msgIPCCommunicatorForward="ipc_communicator_forward",e.msgIPCSendToMain="ipc_send_to_main",e.msgIPCSendToRenderer="ipc_send_to_renderer",e.msgIPCRendererConnect="ipc_renderer_connect",e.msgIPCRendererDisconnect="ipc_renderer_disconnect",e.msgNCCallNativeFunction="nc_call_native_function",e.msgNCCheckNativeFunction="nc_check_native_function",e.msgNCCallJsFunctionById="nc_call_js_function_by_id",e.msgNCCallJsFunctionByName="nc_call_js_function_by_name",e.msgNCNativeFireEvent="nc_native_fire_event",e.msgNCNativeCallReady="nc_native_call_ready"}(t.CommonIPCMessage||(t.CommonIPCMessage={}))},230:function(e,t,n){"use strict";var r=n(1080);n.n(r).a},25:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(2),i=n(33),s=n(14),a=n(15),c=n(8).default(o.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){let t,n,o,l;function u(e){let t=e;return/^[a-zA-Z]:\\/.test(e)?t=e.slice(0,3):e&&"\\"!==e[e.length-1]&&(t=e+"\\"),t}!function(e){e[e.DRIVE_UNKNOWN=0]="DRIVE_UNKNOWN",e[e.DRIVE_NO_ROOT_DIR=1]="DRIVE_NO_ROOT_DIR",e[e.DRIVE_REMOVABLE=2]="DRIVE_REMOVABLE",e[e.DRIVE_FIXED=3]="DRIVE_FIXED",e[e.DRIVE_REMOTE=4]="DRIVE_REMOTE",e[e.DRIVE_CDROM=5]="DRIVE_CDROM",e[e.DRIVE_RAMDISK=6]="DRIVE_RAMDISK"}(t=e.DriverType||(e.DriverType={})),function(e){e[e.Unspecified=0]="Unspecified",e[e.HDD=3]="HDD",e[e.SSD=4]="SSD",e[e.SCM=5]="SCM"}(n=e.MediaType||(e.MediaType={})),function(e){e.HKEY_CLASSES_ROOT="HKEY_CLASSES_ROOT",e.HKEY_CURRENT_USER="HKEY_CURRENT_USER",e.HKEY_LOCAL_MACHINE="HKEY_LOCAL_MACHINE",e.HKEY_USERS="HKEY_USERS"}(o=e.RegistryHKey||(e.RegistryHKey={})),function(e){e[e.REG_NONE=0]="REG_NONE",e[e.REG_SZ=1]="REG_SZ",e[e.REG_EXPAND_SZ=2]="REG_EXPAND_SZ",e[e.REG_BINARY=3]="REG_BINARY",e[e.REG_DWORD=4]="REG_DWORD",e[e.REG_DWORD_LITTLE_ENDIAN=4]="REG_DWORD_LITTLE_ENDIAN",e[e.REG_DWORD_BIG_ENDIAN=5]="REG_DWORD_BIG_ENDIAN",e[e.REG_LINK=6]="REG_LINK",e[e.REG_MULTI_SZ=7]="REG_MULTI_SZ",e[e.REG_RESOURCE_LIST=8]="REG_RESOURCE_LIST",e[e.REG_FULL_RESOURCE_DESCRIPTOR=9]="REG_FULL_RESOURCE_DESCRIPTOR",e[e.REG_RESOURCE_REQUIREMENTS_LIST=10]="REG_RESOURCE_REQUIREMENTS_LIST",e[e.REG_QWORD=11]="REG_QWORD",e[e.REG_QWORD_LITTLE_ENDIAN=11]="REG_QWORD_LITTLE_ENDIAN"}(l=e.RegistryDataType||(e.RegistryDataType={})),e.getDriveType=function(e){return e=u(e),c.getDriveType(e)},e.getDriveInfo=function(e){return e=u(e),c.getDriveInfo(e)},e.getFreePartitionSpace=function(e){return e=u(e),c.getFreePartitionSpace(e)},e.getLogicalDriveStrings=function(){return c.getLogicalDriveStrings()},e.getPartitionSpace=function(e){return e=u(e),c.getPartitionSpace(e)},e.getSystemTempPath=function(){return c.getSystemTempPath()},e.getTaskTypeFromUrl=function(e){let t=c.getTaskTypeFromUrl(e);if(t===a.DownloadKernel.TaskType.Unkown&&function(e){e=e.toLowerCase();let t=!1;do{if("http://"===e.substr(0,"http://".length)){t=!0;break}if("https://"===e.substr(0,"https://".length)){t=!0;break}if("ftp://"===e.substr(0,"ftp://".length)){t=!0;break}}while(0);return t}(e)){let n=/:\/\/\[(.+?)\].*/.exec(e);n||(n=/.+?:\/\/.*?\[(.+?)\].*/.exec(e)),n&&n[1]&&i.isIPv6(n[1])&&(t=a.DownloadKernel.TaskType.P2sp)}return t},e.extractIcon=function(e,t){return c.extractIcon(e,t)},e.isWindow7=function(){return 1===c.isWin7()},e.isWindow8OrLater=function(){let e=!1;do{let t=s.release();if(!t)break;let n=t.indexOf(".",0);if(n<0)break;let r=t.substring(0,n);if(!r)break;let o=parseInt(r,10);o&&o>=8&&(e=!0)}while(0);return e},e.isWindows10=function(){let e=!1;do{let t=s.release();if(!t)break;if(0===t.indexOf("10.0.")){e=!0;break}}while(0);return e},e.compareStr=function(e,t){return c.compareStr(e,t)},e.getTickCount=function(){return c.getTickCount()},e.setScreenSaveActive=function(e,t){return c.setScreenSaveActive(e,t)},e.isSparseDriver=function(e){return e=u(e),c.isSparseDriver(e)},e.getAppList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{c.getAppList(t=>{e(t)})})})},e.isSSD=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{c.isSSD((t,n)=>{e(n)})})})},e.getMemoryInfo=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{c.getMemoryInfo((t,n)=>{e({totalPhy:t,totalVir:n})})})})},e.getHardDiskSpaceList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{c.getHardDiskSpaceList(t=>{e(t)})})})},e.getCpuList=function(){return r(this,void 0,void 0,function*(){return new Promise(e=>{c.getCpuList(t=>{e(t)})})})},e.getFixedDriveMediaType=function(e){return r(this,void 0,void 0,function*(){return new Promise(t=>{e.length>1&&(e=e.slice(0,1)),c.getDriveMediaType(e.toUpperCase(),(e,n)=>{t(n)})})})},e.sleep=function(e){return r(this,void 0,void 0,function*(){yield new Promise((t,n)=>{setTimeout(t,e)})})},e.getTextScale=function(){let e=100,t=c.readRegString(o.HKEY_CURRENT_USER,"SOFTWARE\\Microsoft\\Accessibility","TextScaleFactor");return t&&(e=Number(t)),isNaN(e)&&(e=100),e},e.getWindowRect=function(e){return e?c.getWindowRect(e):{x:0,y:0,width:0,height:0}},e.moveWindow=function(e,t){e&&c.moveWindow(e,t.x,t.y,t.width,t.height,!0)},e.getSystemDirectory=function(){return c.getSystemDirectory()},e.getVersionBlockString=function(e,t){return c.getVersionBlockString(e,t)},e.getOwnerName=function(e){return c.getOwnerName(e)},e.createRegKey=function(e,t){return c.createRegKey(e,t)},e.deleteRegKey=function(e,t){return c.deleteRegKey(e,t)},e.readRegString=function(e,t,n){return c.readRegString(e,t,n)},e.queryRegValue=function(e,t,n){return c.queryRegValue(e,t,n)},e.writeRegValue=function(e,t,n,r,o){return c.writeRegValue(e,t,n,r,o)},e.deleteRegValue=function(e,t,n){return c.deleteRegValue(e,t,n)}}(t.ThunderHelper||(t.ThunderHelper={}))},28:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),o=n(6),i=n(22),s=n(23);!function(e){e.mainProcessContext="main-process",e.mainRendererContext="main-renderer",e.mainPageWebviewRendererContext="main-page-webview-renderer",e.newTaskRendererContext="new-task-renderer",e.preNewTaskRendererContext="pre-new-task-renderer",e.loginRendererContext="login-renderer";class t{constructor(){this.isConnected=!1,this.isOnIPCEvent=!1,this.rendererInfos=[],this.listeners=new Map,t.intervalIPCModuleMsgs=[s.CommonIPCMessage.msgIPCRendererConnect,s.CommonIPCMessage.msgIPCRendererDisconnect]}onMessage(e,t){do{if(!o.isString(e)||0===e.length){i.error("msgName is null");break}if(o.isNullOrUndefined(t)){i.error("listener is null");break}this.listeners.has(e)?this.listeners.get(e).push(t):this.listeners.set(e,[t])}while(0)}getCommunicatorInfo(){return this.currInfo}getAllRenderer(){return this.rendererInfos}getCommunicatorInfoById(e){for(let t of this.rendererInfos)if(t.id===e)return t;return null}getCommunicatorInfoByContext(e){for(let t of this.rendererInfos)if(t.context===e)return t;return null}startListenIPCMessage(e){this.isOnIPCEvent||(this.isOnIPCEvent=!0,e&&this.ListenSendToMainMsg(),this.ListenSendToRendererMsg(e))}ListenSendToMainMsg(){r.ipcMain.on(s.CommonIPCMessage.msgIPCSendToMain,(e,t)=>{let n=void 0;do{if(o.isNullOrUndefined(t)){i.error("msgInfo is empty");break}if(!this.isConnected){i.warning("hasnot been connected yet");break}let r=t.msg.name;if(this.isIPCModuleIntervalMsg(r)){i.information(`IPC module interval msg : ${r}`);let o=this.handleIPCModuleIntervalMsg(e.sender,t);if(n=o[1],!o[0])break;i.information("need to dispatch msg:"+r)}o.isNullOrUndefined(n)?n=this.NotifyListener(t):this.NotifyListener(t)}while(0);o.isNullOrUndefined(n)||(e.returnValue=n),t=null})}ListenSendToRendererMsg(e){(e?r.ipcMain:r.ipcRenderer).on(s.CommonIPCMessage.msgIPCSendToRenderer,(t,n)=>{let r=void 0;do{if(o.isNullOrUndefined(n)){i.error("msgInfo is empty");break}if(!this.isConnected){i.warning("hasnot been connected yet");break}let s=n.msg.name;if(this.isIPCModuleIntervalMsg(s)){i.information(`IPC module interval msg : ${s}`);let e=this.handleIPCModuleIntervalMsg(t.sender,n);if(r=e[1],!e[0])break;i.information("need to dispatch msg:"+s)}e?(i.information("is main, handle forward msg"),this.handleForwardRendererToRendererMsg(n)):(i.information("is renderer, handle business msg"),o.isNullOrUndefined(r)?r=this.NotifyListener(n):this.NotifyListener(n))}while(0);o.isNullOrUndefined(r)||(t.returnValue=r),n=null})}isIPCModuleIntervalMsg(e){for(let n of t.intervalIPCModuleMsgs)if(e===n)return!0;return!1}handleIPCModuleIntervalMsg(e,t){let n=[!1,void 0];do{let r=t.msg.name;if(r===s.CommonIPCMessage.msgIPCRendererConnect){n=[!0,this.handleRendererConnectMsg(e,t)];break}if(r===s.CommonIPCMessage.msgIPCRendererDisconnect){n=[!0,this.handleRendererDisconnectMsg(e,t)];break}}while(0);return n}handleRendererConnectMsg(e,t){i.verbose(e),i.verbose(t)}handleRendererDisconnectMsg(e,t){i.verbose(e),i.verbose(t)}handleForwardRendererToRendererMsg(e){this.sendForwardRendererToRendererMsg(e)}sendForwardRendererToRendererMsg(e){i.verbose(e)}NotifyListener(e){let t=void 0,n=e.msg.name;if(this.listeners.has(n)){let r=this.listeners.get(n),o=!0;for(let n of r)o?(o=!1,t=n(e)):n(e)}return t}}e.Communicator=t}(t.CommonIPCBase||(t.CommonIPCBase={}))},29:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(14),o=n(2);t.getDefaultPrex=function(){return o.basename(process.execPath,".exe")},t.getSockPath=function(e){const t=r.tmpdir();let n=e;e||(n=o.basename(process.execPath,".exe"));let i=o.join(t,`${n}-xunlei-node-net-ipc-{FD196984-2591-4588-AA6F-5C8AC1266290}.sock`);return"win32"===process.platform&&(i="\\\\.\\pipe\\"+(i=(i=i.replace(/^\//,"")).replace(/\//g,"-"))),i},t.serverContextName="xunlei-node-net-ipc-server-{46105371-DE78-4442-B59F-FDA1D6D7D430}",t.mainProcessContext="main-process",t.mainRendererContext="main-renderer",t.isObjectEmpty=function(e){let t=!0;do{if(!e)break;if(0===Object.keys(e).length)break;t=!1}while(0);return t}},3:function(e,t){e.exports=require("electron")},30:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.information=((...e)=>{}),t.error=((...e)=>{}),t.warning=((...e)=>{}),t.critical=((...e)=>{}),t.verbose=((...e)=>{})},31:function(e,t,n){e.exports=n(9)(45)},32:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),o=n(8).default(r.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(){let e=!0;{0;let t=r.resolve("C:\\ETW_LOG\\log.ini");e="1"===o.readINI(t,"Log","enable")}return e}e.isDevToolsEnable=function(){return t()},e.isLogEnable=t,e.getLogOutput=function(){let e=process.env.TL_OUTPUT;do{if(e&&""!==e)break;let t=r.resolve("C:\\ETW_LOG\\log.ini");e=o.readINI(t,"Log","output")}while(0);return e}}(t.DevEnvHelperNS||(t.DevEnvHelperNS={}))},33:function(e,t){e.exports=require("net")},34:function(e,t){e.exports=require("url")},35:function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){let t,n;!function(e){e.require="AR_BROWSER_REQUIRE",e.builtIn="AR_BROWSER_GET_BUILTIN",e.global="AR_BROWSER_GET_GLOBAL",e.functionCall="AR_BROWSER_FUNCTION_CALL",e.construct="AR_BROWSER_CONSTRUCTOR",e.memberConstruct="AR_BROWSER_MEMBER_CONSTRUCTOR",e.memberCall="AR_BROWSER_MEMBER_CALL",e.memberSet="AR_BROWSER_MEMBER_SET",e.memberGet="AR_BROWSER_MEMBER_GET",e.currentWindow="AR_BROWSER_CURRENT_WINDOW",e.currentWebContents="AR_BROWSER_CURRENT_WEB_CONTENTS",e.clientWebContents="AR_BROWSER_CLIENT_WEB_CONTENTS",e.webContents="AR_BROWSER_WEB_CONTENTS",e.sync="AR_BROWSER_SYNC",e.contextRelease="AR_BROWSER_CONTEXT_RELEASE"}(t=e.browser||(e.browser={})),function(e){e.requireReturn="AR_RENDERER_REQUIRE_RETURN",e.getBuiltInReturn="AR_RENDERER_BUILTIN_RETURN",e.getGlobalReturn="AR_RENDERER_GLOBAL_RETURN",e.functionCallReturn="AR_RENDERER_FUNCTION_CALL_RETURN",e.memberConstructReturn="AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN",e.constructReturn="AR_RENDERER_CONSTRUCTOR_RETURN",e.memberCallReturn="AR_RENDERER_MEMBER_CALL_RETURN",e.memberSetReturn="AR_RENDERER_MEMBER_SET_RETURN",e.memberGetReturn="AR_RENDERER_MEMBER_GET_RETURN",e.currentWindowReturn="AR_BROWSER_CURRENT_WINDOW_RETURN",e.currentWebContentsReturn="AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN",e.clientWebContentsReturn="AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN",e.webContentsReturn="AR_RENDERER_WEB_CONTENTS_RETURN",e.syncReturn="AR_RENDERER_SYNC_RETURN",e.callback="AR_RENDERER_CALLBACK"}(n=e.renderer||(e.renderer={}))}(r||(r={})),t.default=r},36:function(e,t,n){"use strict";var r;!function(e){e.getRemoteObjectName=function(e){let t=typeof e;if("function"===t)t=e.name;else if("object"===t){let t=e.name;if("string"!=typeof t){let n=e.constructor;t=n?n.name:Object.toString.call(e)}}return t},e.isPromise=function(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}}(r||(r={})),e.exports=r},37:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),o=n(15);let i=["apk","pic","video","mp4","rmvb","wmv","mpg","mkv","mov","rm","avi","flv","doc","link","ppt","word","magnetic","music","pdf","rar","xls","txt","unknow","gif","ipa","ipsw","dll","chm","text","install","iso"];const s=".xv;.xlmv;.3gp;.3gp2;.3gpp;.3gpp2;.3mm;.3p2;.60d;.787;.aaf;.aep;.aepx;.aet;.aetx;.ajp;.ale;.amv;.amx;.arf;\n  .asf;.asx;.avb;.avd;.avi;.avp;.avs;.avs;.axm;.bdm;.bdmv;.bik;.bix;.bmk;.bnp;.box;.bs4;.bsf;.byu;.camproj;.camrec;.clpi;.cmmp;\n  .cmmtpl;.cmproj;.cmrec;.cpi;.cst;.cvc;.d2v;.d3v;.dat;.dav;.dce;.dck;.ddat;.dif;.dir;.divx;.dlx; .dmb;.dmsm;.dmsm3d;.dmss;.dnc;.dpg;\n  .dream;.dsy;.dv;.dv-avi;.dv4;.dvdmedia;.dvr-ms;.dvx;.dxr;.dzm;.dzp;.dzt;.edl;.evo;.eye;.f4p;.f4v;.fbr;.fbr;.fbz;.fcp;.flc;.flh;\n  .fli;.flv;.flx;.gfp;.gl;.grasp;.gts;.gvi;.gvp;.hdmov;.hkm;.ifo;.imovieproj;.imovieproject;.iva;.ivf;.ivr;.ivs;.izz;.izzy;.jts;.jtv;\n  .k3g;.lrec;.lsf;.lsx;.m15;.m1pg;.m1v;.m21;.m21;.m2a;.m2p;.m2t;.m2ts;.m2v;.m4e;.m4u;.m4v;.m75;.meta;.mgv;.mj2;.mjp;.mjpg;.mkv;.mmv;\n  .mnv;.mod;.modd;.moff;.moi;.moov;.mov;.movie;.mp21;.mp2v;.mp4;.mp4v;.mpe;.mpeg;.mpeg4;.mpf;.mpg;.mpg2;.mpgindex;.mpl;.mpls;\n  .mpsub;.mpv;.mpv2;.mqv;.msdvd;.msh;.mswmm;.mts;.mtv;.mvb;.mvc;.mvd;.mve;.mvp;.mvy;.mxf;.mys;.ncor;.nsv;.nuv;.nvc;.ogm;.ogv;.ogx;.osp;\n  .par;.pds;.pgi;.piv;.pjs;.pmf;.pns;.ppj;.prel;.pro;.prproj;.prtl;.psh;.pssd;.pva;.pvr;.pxv;.qt;.qtch;.qtl;.qtm;.qtz;\n  .r3d;.rcproject;.rdb;.rec;.rm;.rmd;.rmp;.rms;.rmvb;.roq;.rp;.rts;.rts;.rum;.rv;.sbk;.sbt;.scc;.scm;.scn;.screenflow;.sec;.seq;.sfd;\n  .sfvidcap;.smk;.sml;.smv;.spl;.ssm;.stl;.str;.stx;.svi;.swf;.swi;.swt;.tda3mt;.tivo;.tix;.tod;.tp;.tp0;.tpd;\n  .tpr;.trp;.ts;.tts;.tvs;.vc1;.vcpf;.vcr;.vcv;.vdo;.vdr;.veg;.vem;.vf;.vfw;.vfz;.vgz;.vid;.viewlet;.viv;.vivo;.vlab;.vob;.vp3;.vp6;.vp7;\n  .vro;.vs4;.vse;.vsp;.w32;.wcp;.webm;.wlmp;.wm;.wmd;.wmmp;.wmv;.wmx;.wp3;.wpl;.wtv;.wvx;.xfl;.xvid;.yuv;.zm1;.zm2;.zm3;.zmv;",a=".exe;.com;.bat;.msi;.apk;.ipa;.iso;.mds;.bin;.img;.ipsw;",c=".txt;.html;.htm;.shtml;.xhtml;.chm;.hlp;.inf;.rtf;.tex;.ltx;.doc;.docx;.wps;.ppt;.pptx;.odf;.pdf;.xls;.xlsx;.docm;.\n  dot;.dotm;.dotx;.email;.rp;.pps;.et;.ett;.xlt;.dbf;.prn;.csv;.mht;.mhtml;.xml;.wpt;.dps;.dpt;.pot;.ppsx;.epub;.mobi;.lit;.wdl;.ceb;.abm;\n  .pdg;.umb;.ibooks;",l=".aiff;.cue;.m3u;.au;.cdda;.raw;.wav;.flac;.tak;.mp3;.aac;.wma;.m4a;.mid;.mka;.mp2;.mpa;.mpc;.ape;.ofr;\n  .ogg;.ra;.wv;.tta;.ac3;.dts;.nsf;.mod;.s3m;.xm;.it;.vst;",u=".psd;.tga;.gif;.jpeg;.jpg;.jp2;.bmp;.ico;.pcx;.png;.pbm;.pgm;.ppm;.pnm;.pgf;.arw;.cr2;.crw;.dcr;.dng;.erf;.kdc;.mef;\n  .mos;.mrw;.nef;.nrw;.orf;.pef;.ptx;.r3d;.raf;.raw;.rw2;.srf;.srw;.x3f;.ras;.tiff;.tif;.wbmp;.ilbm;.lbm;.iff;.ico;",d=".zip;.zipx;.rar;.7z;.isz;.cab;.arj;.ace;.alz;.uue;.tar;.gz; .gzip;.tgz;.tpz;.bzip2;.bz2;.bz;.tbz;.tbz2;.xz;.txz;\n  .lzh;.lha;.zt;.az; .xpi;.jar;.wim;.swm;.rpm;.xar;.deb;.dmg;.hfs;.cpio;.lzma;.lzma86;.split;",h=".torrent;",f=".idx;.smi;.sub;.psb;.ssa;.ass;.usf;.ssf;.srt;.sup",m=".3gp;.asf;.avi;.divx;.f4v;.flv;.mkv;.mov;.movie;.mp4;.mpeg;.mpeg4;.mpg;.mpg2;.rm;.rmvb;.rp;.swf;.tp;.tp0;.ts;.wmv",p=".exe;.com;.bat;.msi",w=".wav;.flac;.mp3;.aac;.wma;.m4a;.mid;.ape;.ogg;.ra;.mod",g=".psd;.tga;.gif;.jpeg;.jpg;.jp2;.bmp;.ico;.pcx;.pdf;.png;.pbm;.pgm;.ppm;.pnm;.pgf;.arw;.cr2;.crw;.dcr;.dng;.erf;.kdc;\n  .mef;.mos;.mrw;.nef;.nrw;.orf;.pef;.ptx;.r3d;.raf;.raw;.rw2;.srf;.srw;.x3f;.ras;.tiff;.tif;.wbmp;.ilbm;.lbm;.iff;.ico",_=".txt;.html;.htm;.shtml;.xhtml;.chm;.hlp;.inf;.rtf;.tex;.ltx;.doc;.docx;.wps;.ppt;.pptx;.odf;.pdf;.xls;.xlsx;.docm;.dot;.dotm;.dotx;.email;.rp;.pps",v=".rar;.tar;.zip;.gzip;.cab;.uue;.arj;.bz2;.lzh;.jar;.ace;.iso;.7-zip;.7z",y=".asf;.mpg;.rmvb;.rm;.wmv;.avi;.mp4;.mpeg;.mkv;.mov;.ts;.flv;.3gp;.m2ts;",b=".exe;.com;.bat;.scr;.lnk;.pif;.wsh;",C=".iso;";!function(e){let t;function n(e){let n=t.Unkown,o=r.extname(e);return null!==o&&void 0!==o&&o.length>=2&&(o=o.toLowerCase()),void 0===o||""===o||o.length<2?n=t.Unkown:m.indexOf(o)>-1?n=t.Video:p.indexOf(o)>-1?n=t.Software:_.indexOf(o)>-1?n=t.Doc:w.indexOf(o)>-1?n=t.Music:g.indexOf(o)>-1?n=t.Pic:v.indexOf(o)>-1&&(n=t.Zip),o.length>1&&".z"===o.slice(0,2)&&/[0-9]+/.test(o.substring(2))&&(n=t.Zip),n}e.getTaskIcon=function(e,t,n){n=n||"xly-type-";let m="";do{if(t===o.DownloadKernel.TaskType.Bt){m="bt-file";break}if(t===o.DownloadKernel.TaskType.Group){m="group";break}m="unknown";let n=r.extname(e);if(""===n||n.length<2)break;let p=(n=n.toLowerCase()).substring(1);if(i.indexOf(p)>-1){m="doc"===n?"word":p;break}if(n+=";",s.indexOf(n)>-1){m="video";break}if(a.indexOf(n)>-1){m="install",[".mds;",".bin;",".img;"].indexOf(n)>-1&&(m="iso");break}if(c.indexOf(n)>-1){if(m="doc",[".htm;",".html;",".mht;"].indexOf(n)>-1){m="link";break}if(".docx;"===n){m="word";break}if(".xlsx;"===n){m="xls";break}if(".pptx;"===n){m="ppt";break}break}if(l.indexOf(n)>-1){m="music";break}if(u.indexOf(n)>-1){m="pic";break}if(d.indexOf(n)>-1){m="rar";break}if(h.indexOf(n)>-1){m="bt-link";break}if(f.indexOf(n)>-1){m="text";break}}while(0);return`${n}${m}`},function(e){e[e.Unkown=0]="Unkown",e[e.Video=1]="Video",e[e.Software=2]="Software",e[e.Doc=3]="Doc",e[e.Music=4]="Music",e[e.Pic=5]="Pic",e[e.Zip=6]="Zip",e[e.Bt=7]="Bt"}(t=e.FileExtType||(e.FileExtType={})),e.getTaskFileType=function(e,n){let o=t.Unkown;return void 0===n&&void 0!==e&&(n=r.extname(e)),null!==n&&void 0!==n&&n.length>=2&&(n=n.toLowerCase(),n+=";"),void 0===n||""===n||n.length<3?o=t.Unkown:s.indexOf(n)>-1?o=t.Video:a.indexOf(n)>-1?o=t.Software:c.indexOf(n)>-1?o=t.Doc:l.indexOf(n)>-1?o=t.Music:u.indexOf(n)>-1?o=t.Pic:d.indexOf(n)>-1?o=t.Zip:h.indexOf(n)>-1&&(o=t.Bt),n.length>1&&".z"===n.slice(0,2)&&/[0-9]+/.test(n.substring(2))&&(o=t.Zip),o},e.isVideoFileExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=r.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",y.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isSubtitleExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=r.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",f.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isExecutableExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=r.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",b.indexOf(n)>-1&&(t=!0)}while(0);return t},e.isIsoExt=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=r.extname(e);if(!(null!==n&&void 0!==n&&n.length>=2))break;n=n.toLowerCase(),n+=";",C.indexOf(n)>-1&&(t=!0)}while(0);return t},e.getGroupFileType=n,e.getDefaultGroupPrefix=function(e){let r="任务组";do{if(void 0===e||null===e||0===e.length)break;let o=[];for(let e=0;e<7;e++)o[e]=0;for(let t of e){let e=n(t);o[e]=o[e]+1}let i=t.Unkown;for(let e=1;e<o.length;e++)o[e]>o[i]&&(i=e);i===t.Video?r="视频任务组":i===t.Software?r="程序任务组":i===t.Music?r="音乐任务组":i===t.Pic?r="图片任务组":i===t.Doc?r="文档任务组":i===t.Zip&&(r="压缩包任务组")}while(0);return r},e.compareVersion=function(e,t){let n=-2;do{if(null===e||void 0===e||""===e||null===t||void 0===t||""===t)break;let r=0,o=0,i=0,s=0,a=0,c=0,l=0,u=0,d=e.split(/\./);if(null===d||void 0===d||d.length<3)break;if(r=Number(d[0]),o=Number(d[1]),i=Number(d[2]),null!==d[3]&&void 0!==d[3]&&""!==d[3]&&(s=Number(d[3])),null===(d=t.split(/\./))||void 0===d||d.length<3)break;if(a=Number(d[0]),c=Number(d[1]),l=Number(d[2]),null!==d[3]&&void 0!==d[3]&&""!==d[3]&&(u=Number(d[3])),a>r){n=1;break}if(a<r){n=-1;break}if(c>o){n=1;break}if(c<o){n=-1;break}if(l>i){n=1;break}if(l<i){n=-1;break}if(0!==s){if(u>s){n=1;break}if(u<s){n=-1;break}}n=0}while(0);return n}}(t.TaskUtilHelper||(t.TaskUtilHelper={}))},38:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(3),o=n(6),i=n(22),s=n(23),a=n(28);!function(e){class t extends a.CommonIPCBase.Communicator{constructor(){super()}initialize(e){this.currInfo={id:void 0,context:e,isMainCommunicator:!1}}connect(){this.isConnected?i.warning("has been connected"):(this.sendConnectMsgToMain(),this.isConnected=!0,this.startListenIPCMessage(!1))}disconnect(){this.isConnected?(this.isConnected=!1,this.sendDisconnectMsgToMain()):i.warning("hasnot been connected yet")}sendMessageToMain(e){this.sendIPCMsgToMain(e)}sendMessageToMainSync(e){return this.sendIPCMsgToMain(e,!0)}sendMessageToRenderer(e,t){this.sendIPCMsgToRenderer(e,t)}handleRendererConnectMsg(e,t){do{if(o.isNullOrUndefined(t)){i.error("msgInfo is null");break}let e=t.msg.args[0];if(o.isNullOrUndefined(e)){i.error("connectRendererInfo is null");break}i.information(`Renderer: new renderer will connect, id = ${e.id}, context = ${e.context}`),this.rendererInfos.push(e)}while(0)}handleRendererDisconnectMsg(e,t){do{if(o.isNullOrUndefined(t)){i.error("msgInfo is null");break}let e=t.msg.args[0];if(o.isNullOrUndefined(e)){i.error("disconnectRendererInfo is null");break}i.information(`renderer will disconnect, id = ${e.id}, context = ${e.context}`);for(let t=0;t<this.rendererInfos.length;++t)if(this.rendererInfos[t]===e){this.rendererInfos.splice(t,1);break}}while(0)}sendConnectMsgToMain(){let e=this.sendMessageToMainSync({name:s.CommonIPCMessage.msgIPCRendererConnect,args:[]});this.currInfo.id=e[0],this.rendererInfos=e[1]}sendDisconnectMsgToMain(){this.sendMessageToMain({name:s.CommonIPCMessage.msgIPCRendererDisconnect,args:[]})}sendIPCMsgToMain(e,t=!1){let n=void 0;do{if(o.isNullOrUndefined(e)){i.error("msg is null");break}n=(t?r.ipcRenderer.sendSync:r.ipcRenderer.send)(s.CommonIPCMessage.msgIPCSendToMain,{msg:e,senderInfo:this.currInfo})}while(0);return n}sendIPCMsgToRenderer(e,t){do{if(o.isNullOrUndefined(e)){i.error("rendererId is null");break}if(o.isNullOrUndefined(t)){i.error("msg is null");break}let n=[e].concat(t.args);t.args=n,r.ipcRenderer.send(s.CommonIPCMessage.msgIPCSendToRenderer,{msg:t,senderInfo:this.currInfo})}while(0)}}e.RendererCommunicator=t,e.rendererCommunicator=new t}(t.CommonIPCRenderer||(t.CommonIPCRenderer={}))},39:function(e,t){e.exports=require("crypto")},4:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(12),i=n(50),s=n(29),a=n(30);function c(e){a.information("on object freeer"),global.__xdasIPCClienInstance.notifyFreer(e.remoteId,e.callbackId)}let l=void 0;global.__xdasIPCClienInstance||(global.__xdasIPCClienInstance=new class extends o.EventEmitter{constructor(){super(),this.rid=0,this.apis={},this.singletonMap={},this.connectedMap={},this.retCallbackMap={},this.eventCallbackMaps={},this.contextCallbackMap={}}start(e,t,n,r){do{if(t||(t=s.getDefaultPrex()),this.singletonMap.hasOwnProperty(t.toLowerCase())){if(r)if(this.connectedMap.hasOwnProperty(t.toLowerCase()))r("connect");else{let e=this.singletonMap[t.toLowerCase()];e.on("error",e=>{r("error",e)}),e.on("connect",()=>{r("connect")}),e.on("end",()=>{let t=e.isInprocess();r("end",e.getContext(),n,t)})}break}if(global.__xdasPluginConfig&&global.__xdasPluginConfig.name?e={name:global.__xdasPluginConfig.name,version:global.__xdasPluginConfig.version}:void 0!==e&&null!==e||(e=this.parseContext()),!e){if(!this.client||!this.client.getContext())throw new Error("no suitable context for client, please specify context with start function");e={name:this.client.getContext().name,version:this.client.getContext().version}}if(e.name===s.serverContextName)throw new Error("client context must difference from server");if(n&&!this.client)throw new Error("connect to other product must start self firstly");global.__xdasPluginConfig||(global.__xdasPluginConfig=e);let o=new i.Client({context:e,socketPrex:t});this.singletonMap[t.toLowerCase()]=o,n||(this.client=o),o.on("message",e=>{if("fire_event"===e.action)this.fireServerEvent(o,e.name,[e.__context].concat(e.args));else if("client_context_freer"===e.action)do{let t=e.rid;if(t){if(!this.contextCallbackMap[t])break;delete this.contextCallbackMap[t]}}while(0);else if("call_client_by_id"===e.action)this.callFunctionById(o,e.rid,e.s_rid,e.args);else if("call_client_api"===e.action)this.callRegisterFunction(o,e);else if("check_client_function"===e.action){let t=e.method,n=!0;t&&this.apis&&this.apis[t]||(n=!1),this.sendAdapter(o,{s_rid:e.s_rid,action:"check_client_function_callback",success:!0,data:n})}else if(void 0!==e.success&&null!==e.success){let t=e;this.client===o&&this.emit("stat_call_function_back",o.getContext(),e);const n=this.retCallbackMap[t.rid].callback;if(n)if(t.success)do{if("remote_client_callback"===e.action&&e.__context&&e.__context.name&&e.__context.productId){let r=`${e.__context.productId}-${e.__context.name}`.toLowerCase();n(null,this.decodeParameter(t.data,r));break}n(null,t.data)}while(0);else n(t.error,t.data);delete this.retCallbackMap[t.rid]}}),o.on("error",e=>{r&&r("error",e),this.emit("socket-error",e,o.getContext(),n,o.isInprocess()),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),o.isInprocess()?(this.connectedMap[t.toLowerCase()]=o,r&&r("connect"),this.emit("connect",o.getContext(),n,!0)):o.on("connect",()=>{this.connectedMap[t.toLowerCase()]=o,r&&r("connect"),this.emit("connect",o.getContext(),n,!1)}),o.on("end",()=>{let e=o.isInprocess();a.information("server is ended, and this client emit end",t,n,e),r&&r("end",o.getContext(),n,e),this.emit("end",o.getContext(),n,e),delete this.singletonMap[t.toLowerCase()],delete this.connectedMap[t.toLowerCase()],n||(this.client=null)}),this.registry(o)}while(0)}registerFunctions(e){do{if(!e)break;let t=void 0;for(let n in e)if(this.apis.hasOwnProperty(n)){t=n;break}if(t)throw new Error(`try to coverd function ${t}`);this.apis=Object.assign({},this.apis,e)}while(0)}checkServerFunction(e){return r(this,void 0,void 0,function*(){return this.internalCheckServerFunction(this.client,e)})}callServerFunction(e,...t){return r(this,void 0,void 0,function*(){let n=null,r=yield this.callServerFunctionEx(e,...t);return r&&(n=r[0]),n})}callServerFunctionEx(e,...t){return this.internalCallServerFunctionEx(this.client,e,...t)}isRemoteClientExist(e){return this.internalIsRemoteClientExist(this.client,e)}checkRemoteFunction(e,t){return this.internalCheckRemoteFunction(this.client,e,t)}callRemoteClientFunction(e,t,...n){return this.internalCallRemoteClientFunction(this.client,e,t,...n)}notifyFreer(e,t){this.sendAdapter(this.client,{action:"client_context_freer",dst:e,rid:t})}callRemoteContextById(e,t,...n){this.sendAdapter(this.client,{dst:e,action:"call_remote_context_by_id",rid:t,args:n})}attachServerEvent(e,t){return this.internalAttachServerEvent(this.client,e,t)}detachServerEvent(e,t){this.internalDetachServerEvent(this.client,e,t)}broadcastEvent(e,...t){this.sendAdapter(this.client,{action:"broadcast",name:e,args:t})}crossCheckServerFunction(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalCheckServerFunction(n,t)}})}crossCallServerFunction(e,t,...n){return r(this,void 0,void 0,function*(){let r=null,o=yield this.crossCallServerFunctionEx(e,t,...n);return o&&(r=o[0]),r})}crossCallServerFunctionEx(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'funcName' was not provided");return this.internalCallServerFunctionEx(r,t,...n)}}crossIsRemoteClientExist(e,t){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let n=this.singletonMap[e.toLowerCase()];if(!n)throw new Error("Please call the 'start' interface first");return this.internalIsRemoteClientExist(n,t)}})}crossCheckRemoteFunction(e,t,n){return r(this,void 0,void 0,function*(){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCheckRemoteFunction(r,t,n)}})}crossCallRemoteClientFunction(e,t,n,...r){{if(!e)throw new Error("An argument for 'productId' was not provided");let o=this.singletonMap[e.toLowerCase()];if(!o)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'remoteId' was not provided");if(!n)throw new Error("An argument for 'funcName' was not provided");return this.internalCallRemoteClientFunction(o,t,n,...r)}}crossAttachServerEvent(e,t,n){let r=void 0;{if(!e)throw new Error("An argument for 'productId' was not provided");let o=this.singletonMap[e.toLowerCase()];if(!o)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");r=this.internalAttachServerEvent(o,t,n)}return r}crossDetachServerEvent(e,t,n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.internalDetachServerEvent(r,t,n)}}crossBroadcastEvent(e,t,...n){{if(!e)throw new Error("An argument for 'productId' was not provided");let r=this.singletonMap[e.toLowerCase()];if(!r)throw new Error("Please call the 'start' interface first");if(!t)throw new Error("An argument for 'eventName' was not provided");this.sendAdapter(r,{action:"broadcast",name:t,args:n,__context:Object.assign({},this.client.getContext())})}}registry(e){let t=this.getFullContextName(this.client);return new Promise((n,r)=>{do{if(!t){n(!1);break}let r=this.generateId();const o={alias:t,action:"register",rid:r};let i=(e,r)=>{e?(a.error("register error",e.message),n(r)):n(t)};this.retCallbackMap[r]=Object.assign({callback:i},o),this.sendAdapter(e,o)}while(0)})}getNow(){return Date.now()}sendAdapter(e,t){do{if(!t)break;let n=this.getNow();if(t.timestamp?t.timestamp=[...t.timestamp].concat(n):t.timestamp=[].concat(n),!t.__context){let n=e.getContext();n&&(t=Object.assign({__context:n},t))}e.isInprocess()?(a.information("send to server in process"),global.__xdasIPCServer.emit("message",t,e)):e.send(t)}while(0)}parseContext(){let e=void 0;do{let t="";for(let e=0;e<process.argv.length;e++){let n=process.argv[e];if(0===n.indexOf("--xdas-plugin-name=",0)){t=n.substr("--xdas-plugin-name=".length);break}}if(!t)break;e={name:t}}while(0);return e}generateId(){return this.rid++}getFullContextName(e,t){let n="";do{if(t===s.serverContextName){n=t;break}if(void 0===t){n=`${e.getContext().productId}-${e.getContext().name}`.toLowerCase();break}n=`${e.getContext().productId}-${t}`.toLowerCase()}while(0);return n}internalCheckServerFunction(e,t){return new Promise((n,r)=>{do{if(!e){n(!1);break}if(!t){n(!1);break}let r=this.generateId();const o={action:"check_server_function_exist",method:t,rid:r};let i=(e,t)=>{n(!e&&t)};this.retCallbackMap[r]=Object.assign({callback:i},o),this.sendAdapter(e,o)}while(0)})}internalCallServerFunctionEx(e,t,...n){return new Promise((r,o)=>{do{if(!e){r([null,"client doesn't ready"]);break}if(!t){r([null,"funcName is not specifed"]);break}e===this.client&&this.emit("stat_call_function",this.client.getContext(),t);let o=this.generateId();if(n)for(let e=0;e<n.length;e++)n[e]=this.convertFunction2IdEx(n[e]);const i={rid:o,method:t,args:n};let s=(t,n)=>{t?(a.error("callServerFunction error",t,e.getContext()),r([null,t])):r([n,void 0])};this.retCallbackMap[o]=Object.assign({callback:s},i),this.sendAdapter(e,i)}while(0)})}internalIsRemoteClientExist(e,t){return new Promise((n,r)=>{do{if(!t){n([!1,"remote client alias is not specifed"]);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){n([!0,"self is exist"]);break}let r=this.generateId();const o={dst:this.getFullContextName(e,t),action:"check_client_exist",rid:r};let i=(e,t)=>{n(e?[!1,e]:[t,"success"])};this.retCallbackMap[r]=Object.assign({callback:i},o),this.sendAdapter(e,o)}while(0)})}internalCheckRemoteFunction(e,t,n){return new Promise((r,o)=>{do{if(!e){r(!1);break}if(!t){r(!1);break}if(!n){r(!1);break}if(e===this.client&&t.toLowerCase()===e.getContext().name.toLowerCase()){r(!(!this.apis||!this.apis[n]));break}let o=this.generateId();const i={action:"check_client_function_exist",method:n,rid:o,src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t)};let s=(e,t)=>{r(!e&&t)};this.retCallbackMap[o]=Object.assign({callback:s},i),this.sendAdapter(e,i)}while(0)})}internalCallRemoteClientFunction(e,t,n,...r){return new Promise((o,i)=>{do{if(!e){o([null,"client doesn't ready"]);break}if(!t){o([null,"remote client alias is not specifed"]);break}if(!n){o([null,"funcName is not specifed"]);break}let i=(e,t)=>{e?(a.information("callRemoteClientFunction",e.message),o([null,e])):o([t,void 0])};if(r)for(let e=0;e<r.length;e++)r[e]=this.convertFunction2IdEx(r[e]);let s=this.generateId();const c={src:this.getFullContextName(this.client),dst:this.getFullContextName(e,t),action:"call_remote_client_api",method:n,args:r,rid:s};this.retCallbackMap[s]=Object.assign({callback:i},c),this.sendAdapter(e,c)}while(0)})}internalAttachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();this.eventCallbackMaps.hasOwnProperty(r)||(this.eventCallbackMaps[r]={}),this.eventCallbackMaps[r].hasOwnProperty(t)||(this.eventCallbackMaps[r][t]={}),s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"attach_event",name:t});let o=this.generateId();return this.eventCallbackMaps[r][t][o]=n,o}internalDetachServerEvent(e,t,n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;delete this.eventCallbackMaps[r][t][n],s.isObjectEmpty(this.eventCallbackMaps[r][t])&&this.sendAdapter(e,{action:"detach_event",name:t})}while(0)}fireServerEvent(e,t,...n){let r=e.getContext().productId.toLowerCase();do{if(!this.eventCallbackMaps.hasOwnProperty(r))break;if(!this.eventCallbackMaps[r].hasOwnProperty(t))break;let e=this.eventCallbackMaps[r][t];for(let t in e){let r=e[t];r&&r.apply(null,...n)}}while(0)}callFunctionById(e,t,n,...r){let o=void 0,i=!1;do{const s=this.contextCallbackMap[t];if(!s){a.error("the context function has been freeer",t),o={s_rid:n,action:"call_client_by_id_callback",success:!1,error:"the context function has been freeer"};break}let c=void 0,l=void 0;try{c=s.apply(null,...r)}catch(e){l=e.message;break}if(void 0===n||null===n)break;if(o={s_rid:n,action:"call_client_by_id_callback",success:!1},void 0!==l){o.error=l;break}if(c&&c.then){c.then(t=>{o.data=this.convertFunction2IdEx(t),o.success=!0,this.sendAdapter(e,o)}).catch(t=>{o.error=t instanceof Error?t.message:t,this.sendAdapter(e,o)}),i=!0;break}o.success=!0,o.data=this.convertFunction2IdEx(c)}while(0);!i&&o&&this.sendAdapter(e,o)}convertFunction2IdEx(e){let t=e;if("function"==typeof e){let n=this.generateId();this.contextCallbackMap[n]=e,t={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":n}}else if(e&&"object"==typeof e){t=Array.isArray(e)?[...e]:Object.assign({},e);for(let e in t){let n=t[e];if("function"==typeof n){let r=this.generateId();this.contextCallbackMap[r]=n,t[e]={"__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}":r}}else n&&"object"==typeof n&&(t[e]=this.convertFunction2IdEx(n))}}return t}decodeParameter(e,t){let n=e;do{if(!e)break;if(!t)break;if("object"!=typeof e)break;let r=e["__nodeipc_callback_id__{A9C9D760-14E8-42CB-A3CB-9C0A0DDFD732}"];if(r){n=((...e)=>{this.callRemoteContextById(t,r,...e)}),global.__xdasObjectLiftMonitor&&global.__xdasObjectLiftMonitor.setObjectFreer(n,{remoteId:t,callbackId:r},c);break}for(let n in e){let r=e[n];e[n]=this.decodeParameter(r,t)}}while(0);return n}callRegisterFunction(e,t){let n=void 0,r=!1;do{if(!t)break;let o=t.method;if(!o)break;let i=this.getNow();if(n={s_rid:t.s_rid,action:"remote_client_callback",success:!1,rid:t.rid,method:t.method,src:t.src,timestamp:t.timestamp?t.timestamp.concat(i):[].concat(i)},!this.apis||!this.apis[o]){n.error=`callRegisterFunction ${o} is undefined`;break}let s=void 0,a=this.decodeParameter(t.args,t.src);try{s=this.apis[o].apply(null,[t.src].concat(a))}catch(e){n.error=e.message;break}if(s&&s.then){s.then(t=>{n.data=this.convertFunction2IdEx(t),n.success=!0,this.sendAdapter(e,n)}).catch(t=>{n.error=t instanceof Error?t.message:t,this.sendAdapter(e,n)}),r=!0;break}n.success=!0,n.data=this.convertFunction2IdEx(s)}while(0);a.information("callRegisterFunction",n),!r&&n&&this.sendAdapter(e,n)}}),l=global.__xdasIPCClienInstance,t.client=l},40:function(e,t){e.exports=require("https")},41:function(e,t){e.exports=require("buffer")},42:function(e,t,n){"use strict";const r=n(13);if("renderer"===process.type){if(r.info("client running"),!global.__xdasAsyncRemoteExports){let e={};global.__xdasAsyncRemoteExports=e;let t=n(53);e.require=t.remoteRequire,e.getCurrentWebContents=t.getCurrentWebContents,e.getCurrentWindow=t.getCurrentWindow,e.Interest=t.Interest,e.global=new Proxy({},{get:(e,n,r)=>t.getGlobal(n)}),e.electron=new Proxy({},{get:(e,n,r)=>t.getBuiltin(n)}),Object.defineProperty(e,"currentWindow",{get:()=>t.getCurrentWindow()}),Object.defineProperty(e,"currentWebContents",{get:()=>t.getCurrentWebContents()}),Object.defineProperty(e,"process",{get:()=>t.getGlobal("process")}),Object.defineProperty(e,"webContents",{get:()=>t.getWebContents()})}}else if("browser"===process.type&&(r.info("server running"),!global.__xdasAsyncRemoteExports)){let e={};global.__xdasAsyncRemoteExports=e;const t=n(57);t.startServer(),e.getObjectRegistry=t.getObjectRegistry}e.exports=global.__xdasAsyncRemoteExports},43:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(6),o=n(22),i=n(23),s=n(28),a=n(38);!function(e){let t;!function(e){e[e.Unknown=-1]="Unknown",e[e.Success=0]="Success",e[e.FunctionNotExist=1]="FunctionNotExist",e[e.ParamaterError=2]="ParamaterError",e[e.CallFailed=3]="CallFailed"}(t=e.NativeCallErrorCode||(e.NativeCallErrorCode={}));class n{constructor(e,t,n){this.maxId=e,this.minId=t,this.invalidId=n}generateId(){return this.minId===this.maxId?this.invalidId:this.minId++}isInvalidId(e){return e===this.invalidId}}e.IdGenerator=n;const c=0;e.idGenerator=new n(1e7,1,c);class l{constructor(){this.jsCallbacks=new Map,this.jsReturnCallbacks=new Map,this.eventJsCallbakcs=new Map,this.jsRegisterFunctions=new Map,this.targetCommunitorInfo=a.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfoByContext(s.CommonIPCBase.mainRendererContext),this.bindMsgListeners(),this.notifyNativeCallReady()}CallNativeFunction(t,...n){do{if(r.isNullOrUndefined(t)||0===t.length){o.error("funcName is empty");break}if(!this.targetCommunitorInfo){o.error("CallNativeFunction but targetCommunitorInfo null");break}o.information("funcName = ",t),this.printArgs(n);let s=c;for(let t=0;t<n.length;++t)if(r.isFunction(n[t])){let r=e.idGenerator.generateId(),o=n[t];t===n.length-1?(this.jsReturnCallbacks.set(r,o),s=r,n.pop()):(this.jsCallbacks.set(r,o),n[t]=r)}a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:i.CommonIPCMessage.msgNCCallNativeFunction,args:[t,s].concat(n)})}while(0)}AttachNativeEvent(t,n){let i=void 0;do{if(r.isNullOrUndefined(t)||0===t.length){o.error("eventName is empty");break}if(r.isNullOrUndefined(n)){o.error("callback is empty");break}let s=e.idGenerator.generateId();if(e.idGenerator.isInvalidId(s)){o.error("id error");break}if(this.eventJsCallbakcs.has(t))this.eventJsCallbakcs.get(t).set(s,n);else{let e=new Map;e.set(s,n),this.eventJsCallbakcs.set(t,e)}i=s}while(0);return i}DetachNativeEvent(e,t){do{if(r.isNullOrUndefined(e)||0===e.length){o.error("eventName is empty");break}if(r.isNullOrUndefined(t)){o.error("callback is empty");break}if(!this.eventJsCallbakcs.has(e)){o.error(`event: ${e} doesnot have listener`);break}if(!this.eventJsCallbakcs.get(e).has(t)){o.error(`event: ${e} doesnot have the listener of id=${t}`);break}this.eventJsCallbakcs.get(e).delete(t)}while(0)}CheckNativeFunction(t,n){do{if(r.isNullOrUndefined(t)||0===t.length){o.error("funcName is empty");break}if(r.isNullOrUndefined(n)){o.error("callback is empty");break}if(!this.targetCommunitorInfo){o.error("CheckNativeFunction but targetCommunitorInfo null");break}o.information("funcName = ",t);let s=e.idGenerator.generateId();this.jsReturnCallbacks.set(s,n),a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:i.CommonIPCMessage.msgNCCheckNativeFunction,args:[t,s]})}while(0)}RegisterJSFunction(e,n){let i=t.ParamaterError;do{if(r.isNullOrUndefined(e)||0===e.length){o.error("funcName is empty");break}if(r.isNullOrUndefined(n)){o.error("jsFunc is empty");break}this.jsRegisterFunctions.set(e,n),i=t.Success}while(0);return i}bindMsgListeners(){a.CommonIPCRenderer.rendererCommunicator.onMessage(i.CommonIPCMessage.msgNCCallJsFunctionById,e=>{this.handleCallJsFunctionById(e.msg.args)}),a.CommonIPCRenderer.rendererCommunicator.onMessage(i.CommonIPCMessage.msgNCCallJsFunctionByName,e=>{this.handleCallJsFunctionByName(e.msg.args)}),a.CommonIPCRenderer.rendererCommunicator.onMessage(i.CommonIPCMessage.msgNCNativeFireEvent,e=>{this.handleNativeFireEvent(e.msg.args)})}handleCallJsFunctionById(t){do{let n=t[0];if(!r.isNumber(n)){o.error(`id error id = ${n}`);break}if(e.idGenerator.isInvalidId(n)){o.error(`id = ${n} invalid`);break}let i=null,s=0;if(this.jsCallbacks.has(n)&&(s=1,i=this.jsCallbacks.get(n)),this.jsReturnCallbacks.has(n)&&(s=2,i=this.jsReturnCallbacks.get(n)),0===s){o.error(`callbacks[${n}] is null`);break}t.splice(0,1),i.apply(null,t),2===s&&this.jsReturnCallbacks.delete(n)}while(0)}handleCallJsFunctionByName(e){do{let t=e[0];if(!r.isString(t)){o.error(`funcName error funcName = ${t}`);break}if(!this.jsRegisterFunctions.has(t)){o.error(`jsRegisterFunctions[${t}] is null`);break}e.splice(0,1),this.jsRegisterFunctions.get(t).apply(null,e)}while(0)}handleNativeFireEvent(e){do{let t=e[0];if(!r.isString(t)){o.warning(`eventName error eventName = ${t}`);break}if(!this.eventJsCallbakcs.has(t)){o.warning(`eventJsCallbakcs[${t}] is null`);break}e.shift(),this.eventJsCallbakcs.get(t).forEach((t,n,i)=>{o.information(`value = ${t}, key = ${n}, map = ${i}`),r.isNullOrUndefined(t)||t.apply(null,e)})}while(0)}notifyNativeCallReady(){do{if(!this.targetCommunitorInfo){o.error("notifyNativeCallReady but targetCommunitorInfo null");break}a.CommonIPCRenderer.rendererCommunicator.sendMessageToRenderer(this.targetCommunitorInfo.id,{name:i.CommonIPCMessage.msgNCNativeCallReady,args:[a.CommonIPCRenderer.rendererCommunicator.getCommunicatorInfo()]})}while(0)}printArgs(e){for(let t in e)o.information(`index ${t} = `,e[t])}}e.NativeCallImpl=l,e.nativeCall=new l}(t.NativeCallModule||(t.NativeCallModule={}))},44:function(e,t){e.exports=require("http")},45:function(e,t,n){"use strict";var r=n(10),o=n(118),i={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a,c={adapter:("undefined"!=typeof XMLHttpRequest?a=n(119):"undefined"!=typeof process&&(a=n(17)),a),transformRequest:[function(e,t){return o(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(s(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};c.headers={common:{Accept:"application/json, text/plain, */*"}},r.forEach(["delete","get","head"],function(e){c.headers[e]={}}),r.forEach(["post","put","patch"],function(e){c.headers[e]=r.merge(i)}),e.exports=c},46:function(e,t,n){"use strict";var r=n(68);e.exports=function(e,t,n,o,i){var s=new Error(e);return r(s,t,n,o,i)}},47:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(51),o=n(12);t.Parser=class extends o.EventEmitter{constructor(){super(),this.decoder=new r.StringDecoder("utf8"),this.jsonBuffer=""}encode(e){return JSON.stringify(e)+"\n"}feed(e){let t=this.jsonBuffer,n=0,r=(t+=this.decoder.write(e)).indexOf("\n",n);for(;r>=0;){const e=t.slice(n,r),o=JSON.parse(e);this.emit("message",o),n=r+1,r=t.indexOf("\n",n)}this.jsonBuffer=t.slice(n)}}},475:function(e,t,n){"use strict";n.r(t);var r=n(476),o=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);t.default=o.a},476:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,s=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(7),s=n(5),a=n(1),c=n(11),l=n(986),u=n(92),d=n(43),h=n(4),f=n(63),m=n(988),p=n(989),w=n(990),g=n(991),_=n(64),v=n(992),y=n(993),b=a.default.getLogger("search-drop");let C=class extends s.Vue{constructor(){super(...arguments),this.eventAddressKeyDown=-1,this.eventOnAddressBarInputChanged=-1,this.keyWord="",this.relatedKeyWord="",this.sites=[],this.sitesFetching=!1,this.recommends=[],this.commonWebsites=[],this.searchWindowX=0,this.searchWindowY=0,this.searchWindowWidth=0,this.switchOn=0,this.searchSuffix=[],this.searchSuffixCount=0,this.isShowed=!1,this.isSearchingLocal=!1,this.isSearchingPan=!1,this.selectIndex=-1,this.searchEngineUrl=void 0,this.searchRecommends={urls:[{url:"https://www.baidu.com",name:"百度",iconUrl:v,isRecommend:!0},{url:"http://www.xunlei.com",name:"迅雷",iconUrl:y,isRecommend:!0}]},this.latestSearchKeywords=[],this.isShowMore=!1,this.showPanel="download",this.matchBookMarks=[],this.matchHistorys=[],this.matchDownloadTasks=[],this.matchPanTasks=[],this.movieList=[],this.searchMovieList=[],this.movieConfig={yingyuan_dl_recommend:0,yingyuan_search_think:!1,yingyuan_search_wait:!1},this.userid="",this.vipInfo={},this.vipItemText="",this.vipItemIcon="",this.vipItemUrl="",this.vipItemColor="",this.vipItemReferfrom="",this.vipItemAidfrom="",this.showVipItemIcon=!1,this.isInSafeSpace=!1,this.isFirstShow=!0}get panPanelText(){return this.isInSafeSpace?"超级保险箱":"云盘"}get showVipItem(){return Boolean(this.vipItemText)&&Boolean(this.vipItemUrl)}get filterRecommendList(){return(this.movieList||[]).slice(0,4)}onShowVipItemChanged(e){e&&this.trackVipEvent("search_hotword_hover_vip_show").catch()}formatedTitle(e){let t=e;return this.keyWord.trim().split(" ").forEach(e=>{0!==e.length&&(e=e.replace(new RegExp("[\\(|\\)|\\[|\\]]","ig"),e=>"\\"+e),t=t.replace(new RegExp(e,"ig"),e=>'<span class="keyword">'+e+"</span>"))}),t}showMovieHomePage(e,t){let n="0";this.vipInfo&&"1"===this.vipInfo.isVip&&(n=this.vipInfo.vasType),c.XLStatNS.trackEvent("download_leftpanel",e,"",0,0,0,0,`is_login=${"0"===this.userid?0:1},userid=${this.userid},usertype=${n},referfrom=${t}`);let r=`https://video-vip-ssl.xunlei.com/index_pc.html?referfrom=${t}`;h.client.callServerFunction("OpenNewTab",r).catch(),this.hideDropDownWindow(!0)}openMoviePage(e,t,n){e||(e="https://video-vip-ssl.xunlei.com/index_pc.html?");let r=`${e}&referfrom=${n}`;h.client.callServerFunction("OpenNewTab",r).catch();let o="",i=this.filterRecommendList[t];"v_pc_xlx_ggong_search_wait_vipsp"!==n&&(i=this.searchMovieList[t]),o+=`video_type=${"movie"===i.category?"电影":"电视剧"},video_id=${i.aid},video_name=${i.work_name},position=${t+1};`;let s="0";this.vipInfo&&"1"===this.vipInfo.isVip&&(s=this.vipInfo.vasType);let a=encodeURIComponent(o),l="v_pc_xlx_ggong_search_wait_vipsp"===n?"search_wait_page_vip_special_click":"search_association_page_vip_special_click";c.XLStatNS.trackEvent("download_leftpanel",l,"",0,0,0,0,`is_login=${"0"===this.userid?0:1},userid=${this.userid},usertype=${s},referfrom=v_pc_xlx_ggong_search_wait_vipsp,${a}`),this.hideDropDownWindow(!0)}getTypeIconClass(e){return`is-type-${e%3+1}`}get matchBookMarksOrHistorys(){return 0!==this.matchBookMarks.length?this.matchBookMarks:this.matchHistorys}get isMatchBookMarks(){return 0!==this.matchBookMarks.length}get selectIndexOfCommonWebSites(){return this.selectIndex-this.filterRecomends.length}get selectIndexOflatestSearchKeywords(){return this.selectIndex-this.filterRecomends.length-this.filterCommonWebSites.length}get selectIndexOfRecomendKeywordSites(){return this.selectIndex-this.matchBookMarksOrHistorys.length}get selectIndexOfMatchDownloadTasks(){return this.isShowMore&&"download"===this.showPanel?this.selectIndex:this.selectIndex-this.matchBookMarksOrHistorys.length-this.filteredRecomendKeywordSites.length}get selectIndexOfMatchPanTasks(){return this.isShowMore&&"download"!==this.showPanel?this.selectIndex:this.selectIndex-this.matchBookMarksOrHistorys.length-this.filteredRecomendKeywordSites.length-Math.min(3,this.matchDownloadTasks.length)}get filteredRecomendKeywordSites(){let e=4-this.matchBookMarksOrHistorys.length;return this.sites.slice(0,e)}get filterRecomends(){return this.recommends.filter((e,t)=>t<6)}get filterCommonWebSites(){let e=this.commonWebsites.concat(this.searchRecommends.urls);for(;e.length>6;)for(let t=e.length-1;t>=0;t--){if(0===e[t].show){e.splice(t,1);break}if(void 0===e[t].show){e.splice(t,1);break}}return e}get filterLatestSearchKeywords(){return this.latestSearchKeywords.slice(0,6)}openSite(e){h.client.callServerFunction("OpenNewTab",this.sites[e].url,JSON.stringify({extData:JSON.stringify({search_from:"top_search_address-association_word"})})).catch(),this.hideDropDownWindow(!0),c.XLStatNS.trackEvent("download_leftpanel","search_association_page_word_url_click","",0,0,0,0,`association_type=word,association_word=${encodeURIComponent(this.sites[e].url)}`)}getSearchUrl(e){return o(this,void 0,void 0,function*(){void 0===this.searchEngineUrl&&(this.searchEngineUrl=yield h.client.callServerFunction("GetRemoteGlobalConfigValue","search","search_engine_url","https://www.baidu.com/s?tn=48021271_21_hao_pg&wd=$word$"));let t=this.searchEngineUrl;return this.relatedKeyWord?t.replace("$word$",encodeURIComponent(`${e} ${this.relatedKeyWord}`)):t.replace("$word$",encodeURIComponent(e))})}openRecommend(e){return o(this,void 0,void 0,function*(){let t="";t=null!==this.recommends[e].url&&void 0!==this.recommends[e].url&&""!==this.recommends[e].url?this.recommends[e].url:yield this.getSearchUrl(this.recommends[e].text),h.client.callServerFunction("OpenNewTab",t,JSON.stringify({extData:JSON.stringify({search_from:"top_search_address-hot_search"})})).catch();let n=`position=${e+1},content=${encodeURIComponent(this.recommends[e].text)}`;c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_hot_search_click","",0,0,0,0,n),this.hideDropDownWindow(!0)})}clearCommonWebsite(){h.client.callServerFunction("ClearCommonlySites"),this.commonWebsites=[],c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_clear_click","",0,0,0,0,"clickid=comment_website"),this.resizeSearchWindow()}openCommonWebsite(e){h.client.callServerFunction("OpenNewTab",e.url,JSON.stringify({extData:JSON.stringify({search_from:"top_search_address-comment_website"})})).catch(),this.hideDropDownWindow(!0);let t=`url=${encodeURIComponent(e.url)},website_type=${!0===e.isRecommend?"recommend_website":"comment_website"}`;c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_comment_website_click","",0,0,0,0,t)}onCommonWebsiteMenu(e){return o(this,void 0,void 0,function*(){let t=[{label:"删除",click:()=>{h.client.callServerFunction("DeleteCommonlySites",e.url);let t=this.commonWebsites.length%2!=0;for(let t=0;t<this.commonWebsites.length;t++)if(this.commonWebsites[t].url===e.url){this.commonWebsites.splice(t,1);break}t&&this.$nextTick(()=>{this.resizeSearchWindow("common delete")})}}],n=yield i.asyncRemoteCall.getCurrentWindow(),r=yield(yield i.asyncRemoteCall.getMenu()).buildFromTemplate(t);yield f.MenuSkinNS.setStyle(r,{}),yield r.popup(Object.assign({},{window:n}))})}hideDropDownWindow(e){h.client.detachServerEvent("AddressKeyDown",this.eventAddressKeyDown),h.client.detachServerEvent("OnAddressBarInputChanged",this.eventOnAddressBarInputChanged),h.client.broadcastEvent("StartClearTextTimer"),window.close()}resizeSearchWindow(e){b.information("resize reason <",e,">"),this.$nextTick(()=>o(this,void 0,void 0,function*(){let t=document.querySelector("#search-window").getBoundingClientRect(),n=Math.ceil(t.height);if(n<30)return h.client.broadcastEvent("ShowSearchWindow",!1),void(this.isShowed=!1);if(""===this.keyWord&&this.isInSafeSpace)return b.information("isInSafeSpace and resize hide: ",this.keyWord,this.isInSafeSpace),h.client.broadcastEvent("ShowSearchWindow",!1),void(this.isShowed=!1);let r=yield i.asyncRemoteCall.getCurrentWindow(),o=yield r.getBounds();b.information("resize bounds, height: ",n," x:",o.x," y:",o.y),this.isFirstShow?("recommend"===e&&(this.isFirstShow=!1),yield r.setBounds({height:Math.floor(n*_.FixTextScale.getTextScale()*_.FixTextScale.getTextScale()),width:Math.floor(460*_.FixTextScale.getTextScale()*_.FixTextScale.getTextScale()),x:o.x,y:o.y})):yield r.setBounds({height:Math.floor(n*_.FixTextScale.getTextScale()),width:Math.floor(460*_.FixTextScale.getTextScale()),x:o.x,y:o.y}),this.isShowed||(this.isShowed=!0,setTimeout(()=>{h.client.broadcastEvent("ShowSearchWindow",!0)},20))}))}handleSearch(e,t,n,r,i){return o(this,void 0,void 0,function*(){if(this.keyWord=e,this.selectIndex=-1,this.initRecommends().catch(),""!==e){if(l.SearchNS.hasChineseCharacter(e)){let t=[],n=yield this.getSearchUrl(e);t.splice(0,0,{url:n,text:"搜索 "+e,isSearch:!0,key:e}),1===this.switchOn?(this.sitesFetching=!0,l.SearchNS.getWords(e).then(e=>o(this,void 0,void 0,function*(){this.sitesFetching=!1;do{if(!e)break;if(e.keyWord!==this.keyWord){b.information("getWords back keyWord unmatch",e.keyWord,";",this.keyWord);break}let n=[],r="";if(200===e.status&&"ok"===e.data.result)for(let t=0;t<e.data.data.list.length&&(n.includes(e.data.data.list[t].title)||(n.push(e.data.data.list[t].title),!(n.length>2)));t++);let o="";for(let e of n)o=this.searchSuffixCount>0?e+" "+this.searchSuffix[Math.floor(Math.random()*this.searchSuffixCount)]:e,r=yield this.getSearchUrl(o),t.push({url:r,text:o,isSearch:!0,key:o});""===this.keyWord&&(t=[]),this.sites=t,this.resizeSearchWindow("sites get")}while(0)})).catch(e=>{this.sites=t,this.resizeSearchWindow("sites get")})):(this.sites=t,this.resizeSearchWindow("sites get"))}else{let t=u.URLHelperNS.isUrl(e),n=l.SearchNS.getMatchUrls(e);if(!t.ret){let t=yield this.getSearchUrl(e);n.splice(0,0,{url:t,text:"搜索 "+e,isSearch:!0,key:e})}n=n.slice(0,4),""===this.keyWord&&(n=[]),this.sites=n,this.resizeSearchWindow("sites get")}h.client.callServerFunction("SearchBookMark",this.keyWord).then(e=>{e&&0!==e.length?(this.matchBookMarks=e,this.matchHistorys=[],this.resizeSearchWindow("SearchBookMark")):(this.matchBookMarks=[],h.client.callServerFunction("SearchHistory",this.keyWord).then(e=>{e&&0!==e.length?this.matchHistorys=e:this.matchHistorys=[],this.resizeSearchWindow("SearchHistory")}))}),this.isSearchingLocal=!0,this.isSearchingPan=!0,h.client.callServerFunction("SearchTask",this.keyWord).then(e=>{this.matchDownloadTasks=Object.freeze(e.reverse()),setTimeout(()=>{this.resizeSearchWindow("SearchTask")},20),this.isSearchingLocal=!1}),h.client.callServerFunction("SearchPanTask",this.keyWord).then(e=>{e&&e.length>0?(this.matchPanTasks=e,this.matchPanTasks.sort((e,t)=>e.kind===t.kind?new Date(t.created_time).getTime()-new Date(e.created_time).getTime():"drive#folder"===e.kind?-1:"drive#folder"===t.kind?1:new Date(t.created_time).getTime()-new Date(e.created_time).getTime())):this.matchPanTasks=[],this.isSearchingPan=!1,this.resizeSearchWindow("SearchPanTask")}),this.movieConfig.yingyuan_search_think&&h.client.callServerFunction("SearchMovie",this.keyWord).then(e=>{if(e&&e.length>0?this.searchMovieList=e.slice(0,3):this.searchMovieList=[],this.searchMovieList.length>0){let e="";for(let t=0;t<this.searchMovieList.length;t++){let n=this.searchMovieList[t];e+=`video_type=${"movie"===n.category?"电影":"电视剧"},video_id=${n.aid},video_name=${n.work_name},position=${t+1};`}let t="0";this.vipInfo&&"1"===this.vipInfo.isVip&&(t=this.vipInfo.vasType);let n=encodeURIComponent(e);c.XLStatNS.trackEvent("download_leftpanel","search_association_page_vip_special_show","",0,0,0,0,`is_login=${"0"===this.userid?0:1},userid=${this.userid},usertype=${t},referfrom=v_pc_xlx_ggong_search_asso_vipsp,movielist=(${n})`)}this.resizeSearchWindow("Search_Movie")}).catch(),setTimeout(()=>{let e=`ishave_association_word=${!this.isInSafeSpace&&this.sites.length>0?1:0},ishave_association_website=${!this.isInSafeSpace&&this.matchBookMarksOrHistorys.length>0?1:0},`;e+=`ishave_download=${!this.isInSafeSpace&&this.matchDownloadTasks.length>0?1:0},ishave_download_more=${!this.isInSafeSpace&&this.matchDownloadTasks.length>3?1:0},`,e+=`ishave_yunpan=${!this.isInSafeSpace&&this.matchPanTasks.length>0?1:0},ishave_yunpan_more=${!this.isInSafeSpace&&this.matchPanTasks.length>3?1:0}`,e+=`is_safebox=${this.isInSafeSpace?1:0},is_safebox_show=${0!==this.matchPanTasks.length&&this.isInSafeSpace?1:0}`,c.XLStatNS.trackEvent("download_leftpanel","search_association_page_show","",0,0,0,0,e)},300)}else this.isShowMore=!1,this.sites=[];this.resizeSearchWindow("recommend")})}onChosenChange(e){let t=void 0,n=!1;do{if(""===this.keyWord){if(-1===e){e=0;break}e>=0&&e<this.filterRecomends.length?t=this.filterRecomends[e].url||this.filterRecomends[e].text:this.selectIndexOfCommonWebSites>=0&&this.selectIndexOfCommonWebSites<this.filterCommonWebSites.length?t=this.filterCommonWebSites[this.selectIndexOfCommonWebSites].url:this.selectIndexOflatestSearchKeywords>=0&&this.selectIndexOflatestSearchKeywords<this.latestSearchKeywords.length&&(t=this.filterLatestSearchKeywords[this.selectIndexOflatestSearchKeywords].keyword);break}if(-1===e){t=this.keyWord;break}if(this.isShowMore){t=this.keyWord,n=!0;break}e>=0&&e<this.matchBookMarksOrHistorys.length?t=this.matchBookMarksOrHistorys[e].url:this.selectIndexOfRecomendKeywordSites>=0&&this.selectIndexOfRecomendKeywordSites<this.filteredRecomendKeywordSites.length?t=this.filteredRecomendKeywordSites[this.selectIndexOfRecomendKeywordSites].text:(t=this.keyWord,n=!0)}while(0);b.information("chose change",t),t&&h.client.broadcastEvent("SelectAddressDropItem",t,n)}handleKeydownSelect(e){do{if(13===e){if(""===this.keyWord){h.client.broadcastEvent("AddressDropWndKeyDown",e),this.sendClickStat(),this.hideDropDownWindow();break}if(-1===this.selectIndex)break;this.isShowMore?("download"===this.showPanel?h.client.callServerFunction("SwitchDetail",this.matchDownloadTasks[this.selectIndex]).catch():h.client.callServerFunction("IpcRedirectCurrentDirectoryById",this.matchPanTasks[this.selectIndex].id),this.hideDropDownWindow()):(this.selectIndexOfMatchDownloadTasks>=0&&this.selectIndexOfMatchDownloadTasks<Math.min(3,this.matchDownloadTasks.length)?h.client.callServerFunction("SwitchDetail",this.matchDownloadTasks[this.selectIndexOfMatchDownloadTasks]).catch():this.selectIndexOfMatchPanTasks>=0&&this.selectIndexOfMatchPanTasks<Math.min(3,this.matchPanTasks.length)?h.client.callServerFunction("IpcRedirectCurrentDirectoryById",this.matchPanTasks[this.selectIndexOfMatchPanTasks].id):this.sendClickStat(),this.hideDropDownWindow());break}let t=0;if(t=""!==this.keyWord?this.isShowMore?"download"===this.showPanel?this.matchDownloadTasks.length:this.matchPanTasks.length:this.matchBookMarksOrHistorys.length+this.filteredRecomendKeywordSites.length+Math.min(3,this.matchDownloadTasks.length)+Math.min(3,this.matchPanTasks.length):0,40===e){if(this.selectIndex>=t-1){b.information("key down reach bottom");break}this.selectIndex=(this.selectIndex<0?-1:this.selectIndex)+1;break}if(38===e){if(this.selectIndex<0){b.information("key up reach top");break}this.selectIndex-=1;break}}while(0)}sendClickStat(){if(""===this.keyWord){if(this.recommends[this.selectIndex]){b.information("sendClickStat recommends",this.recommends[this.selectIndex].text);let e=`word=${encodeURIComponent(this.recommends[this.selectIndex].text)},button=${this.selectIndex+1}`;c.XLStatNS.trackEvent("clienttop","search_hotword_hover_click","",0,0,0,0,e)}}else if(this.selectIndex>=0&&this.selectIndex<this.sites.length){b.information("sendClickStat sites",this.sites[this.selectIndex].url);let e=`word=${encodeURIComponent(this.keyWord)},url=${this.sites[this.selectIndex].url},block=association`;c.XLStatNS.trackEvent("clienttop","clienttop_address_search_recommend_click","",0,0,0,0,e)}}handleKeydownHotkey(e){b.information("key down",e),13===e.keyCode&&h.client.broadcastEvent("AddressDropWndKeyDown",e.keyCode),this.handleKeydownSelect(e.keyCode)}initRecommends(){return o(this,void 0,void 0,function*(){do{if(this.recommends.length>0)break;return new Promise((e,t)=>{d.NativeCallModule.nativeCall.CallNativeFunction("GetSearchRecommends",(t,n)=>{do{if(this.recommends.length>0)break;if(null!==n&&void 0!==n&&""!==n)try{let e=JSON.parse(n.toString());for(let t=0;t<e.length;++t)this.recommends.push({id:t+1,text:e[t].title,isNew:e[t].isNew,url:e[t].url})}catch(e){b.warning(e)}}while(0);e()})})}while(0)})}initCommonly(){return o(this,void 0,void 0,function*(){let e=yield h.client.callServerFunction("GetCommonlySites");e&&(this.commonWebsites=e,this.$nextTick(()=>{if(""===this.keyWord){let e=`site_cnt=${this.commonWebsites.length}`;c.XLStatNS.trackEvent("clienttop","clienttop_address_search_recommend_show","",0,0,0,0,e)}}))})}mounted(){window.addEventListener("keydown",this.handleKeydownHotkey,!0),h.client.attachServerEvent("AddressKeyDown",(e,...t)=>{let n=t[0];this.handleKeydownSelect(n)})}created(){return o(this,void 0,void 0,function*(){document.body.style.overflowY="hidden",h.client.attachServerEvent("OnAddressBarInputChanged",(e,...t)=>{if(!t||t.length<5)return;let n=t[0],r=t[1],o=t[2],i=t[3],s=t[4];this.searchWindowX=r,this.searchWindowY=o,this.searchWindowWidth=i,this.handleSearch(n,r,o,i,s)});let e=yield h.client.callServerFunction("GetRemoteGlobalConfigValue","vip","yingyuan_search_dl",null);e&&void 0!==e.yingyuan_search_wait&&(this.movieConfig=e),h.client.attachServerEvent("OnHideDropDownWindow",this.hideDropDownWindow),this.switchOn=yield h.client.callServerFunction("GetConfigValue","SearchBar","EnableAssociativeWord",1);let t=yield h.client.callServerFunction("GetConfigValue","SearchBar","SearchSuffix","mp4,下载,ed2k,magnet,ftp,avi");this.searchSuffix=t.split(","),this.searchSuffixCount=this.searchSuffix.length,this.userid=yield h.client.callServerFunction("GetUserID"),this.vipInfo=yield h.client.callServerFunction("GetVipInfo");let n=yield h.client.callRemoteClientFunction("ThunderPanPluginWebview","IpcInSafeSpace");b.information("is In Safe Space: ",n),this.isInSafeSpace=n[0],this.isInSafeSpace||(yield Promise.all([this.initRecommends(),this.initSearchRecommend(),this.initCommonly(),this.initSearchKeywords(),this.initMovieList(),this.initVipItem()]),this.resizeSearchWindow("created"));let r=yield h.client.callServerFunction("GetAddressBarInputText");this.handleSearch(r),setTimeout(()=>{let e=this.filterCommonWebSites.some(e=>!0===e.isRecommend)?1:0,t=this.filterCommonWebSites.some(e=>void 0===e.isRecommend)?1:0,n=this.latestSearchKeywords.length>0?1:0,r="",o=0;for(let e in this.filterCommonWebSites)this.filterCommonWebSites[e].isRecommend&&(r+=`url${o++}=${this.filterCommonWebSites[e].url},`);c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_show","",0,0,0,0,`ishave_recommend_website=${e},ishave_comment_website=${t},ishave_recent_search=${n},recommend_website_url=${r}`),c.XLStatNS.trackEvent("download_leftpanel","search_comment_website_num","",0,0,0,0,`website_num=${this.filterCommonWebSites.length-o}`)},50)})}initSearchKeywords(){return o(this,void 0,void 0,function*(){this.latestSearchKeywords=yield h.client.callServerFunction("GetLatestSearchKeywords")})}initSearchRecommend(){return o(this,void 0,void 0,function*(){let e=yield h.client.callServerFunction("GetRemoteGlobalConfigValue","search","recommend");if(e){let t=e.urls;for(let e of t)e.isRecommend=!0;let n=(new Date).getTime(),r=t.filter(e=>!e.start_time||!e.end_time||e.start_time<=n&&e.end_time>=n);r.length>0&&this.$set(this.searchRecommends,"urls",r)}})}initMovieList(){return o(this,void 0,void 0,function*(){if(this.movieConfig.yingyuan_search_wait&&(this.movieList=yield h.client.callServerFunction("Recommend"),this.filterRecommendList.length>0)){let e="";for(let t=0;t<this.filterRecommendList.length;t++){let n=this.filterRecommendList[t];e+=`video_type=${"movie"===n.category?"电影":"电视剧"},video_id=${n.aid},video_name=${n.work_name},position=${t+1};`}let t="0";this.vipInfo&&"1"===this.vipInfo.isVip&&(t=this.vipInfo.vasType);let n=encodeURIComponent(e);c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_vip_special_show","",0,0,0,0,`is_login=${"0"===this.userid?0:1},userid=${this.userid},usertype=${t},referfrom=v_pc_xlx_ggong_search_wait_vipsp,movielist=(${n})`)}})}initVipItem(){return o(this,void 0,void 0,function*(){let e="xl11_vip_search_video",t=yield h.client.callServerFunction("GetVipLabelConfig",e),n=t&&t[e]?t[e]:null;if(n){let e=0,t=n[e];t&&(t.text&&(this.vipItemText=t.text),t.url&&(this.vipItemUrl=t.url),this.vipItemReferfrom=t.referfrom,this.vipItemAidfrom=t.aidfrom),(t=n[e=1])&&t.img&&(this.vipItemIcon=t.img),(t=n[e=2])&&t.text&&(this.vipItemColor=t.text)}})}trackVipEvent(e){return o(this,void 0,void 0,function*(){let t=0,n=0;try{let e=JSON.parse(yield h.client.callServerFunction("GetUserInfo",2));e&&(t=Number(e.vasType),n=Number(e.isVip))}catch(e){b.warning(e)}let r=`is_login=${(yield h.client.callServerFunction("IsLogined"))?1:0},is_vip=${n},vip_type=${t}`;c.XLStatNS.trackEvent("xlx_vip_event",e,"",0,0,0,0,r)})}onVipItemIconLoad(){b.information("onVipItemIconLoad"),this.showVipItemIcon=!0}handleVipItemClick(){return o(this,void 0,void 0,function*(){let e=this.vipItemUrl;-1!==e.indexOf("?")?e+=`&referfrom=${this.vipItemReferfrom}&aidfrom=${this.vipItemAidfrom}`:e+=`?referfrom=${this.vipItemReferfrom}&aidfrom=${this.vipItemAidfrom}`,yield this.trackVipEvent("search_hotword_hover_vip_click"),h.client.callServerFunction("OpenNewTab",e).catch(),this.hideDropDownWindow(!0)})}deleteSearchKeyword(e){return o(this,void 0,void 0,function*(){h.client.callServerFunction("DeleteSearchKeyword",e).catch(),this.latestSearchKeywords.splice(e,1),c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_delete_click","",0,0,0,0,"clickid=recent_search"),this.resizeSearchWindow()})}getUrlFirstChar(e){let t=new RegExp(/[\w|-]+(\.com.cn|\.net.cn|\.org.cn|\.edu\.cn|\.gov\.cn|\.com|\.net|\.cn|\.cc|\.tv|\.uk|\.jp|\.org|\.biz|\.edu|\.gov|\.mil|\.fr|\.au|\.de)/gi),n=e.search(t);return-1!==n?e.charAt(n).toUpperCase():e.split(".")[1].charAt(0).toUpperCase()||"X"}deleteCommonWebsite(e){h.client.callServerFunction("DeleteCommonlySites",e.url);for(let t=0;t<this.commonWebsites.length;t++)if(this.commonWebsites[t].url===e.url){this.commonWebsites.splice(t,1);break}c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_delete_click","",0,0,0,0,"clickid=comment_website"),this.resizeSearchWindow()}clearSearchKeyword(){h.client.callServerFunction("ClearSearchKeyword"),this.latestSearchKeywords=[],c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_clear_click","",0,0,0,0,"clickid=recent_search"),this.resizeSearchWindow()}onClickShowMore(e){this.isShowMore=!this.isShowMore,this.showPanel=e,this.selectIndex=-1,"download"===this.showPanel&&this.isShowMore?c.XLStatNS.trackEvent("download_leftpanel","search_association_page_download_click","",0,0,0,0,"position=associate_more"):this.isShowMore&&"thunder-pan"===this.showPanel&&c.XLStatNS.trackEvent("download_leftpanel","search_association_page_yunpan_click","",0,0,0,0,"position=associate_more"),setTimeout(()=>{this.resizeSearchWindow("showMore")},50)}openLastestSearch(e){return o(this,void 0,void 0,function*(){let t=this.latestSearchKeywords[e].url,n="";t?n=`type=search_url,search_url=${t}`:(t=yield this.getSearchUrl(this.latestSearchKeywords[e].keyword),n=`type=search_word,search_word=${encodeURIComponent(this.latestSearchKeywords[e].keyword)}`),h.client.callServerFunction("OpenNewTab",t,JSON.stringify({extData:JSON.stringify({search_from:"top_search_address-recent_search"})})).catch(),c.XLStatNS.trackEvent("download_leftpanel","search_wait_page_recent_search_click","",0,0,0,0,n),this.hideDropDownWindow(!0)})}openHistoryOrBookMarkSite(e){h.client.callServerFunction("OpenNewTab",e,JSON.stringify({extData:JSON.stringify({search_from:"top_search_address-association_website"})})).catch(),c.XLStatNS.trackEvent("download_leftpanel","search_association_page_word_url_click","",0,0,0,0,`association_type=website,association_url=${encodeURIComponent(e)}`)}};r([s.Prop()],C.prototype,"options",void 0),r([s.Watch("showVipItem")],C.prototype,"onShowVipItemChanged",null),r([s.Watch("selectIndex")],C.prototype,"onChosenChange",null),C=r([s.Component({components:{TaskItemPanel:m.default,TaskItemPanelMore:p.default,PanTaskItemPanel:w.default,PanTaskItemPanelMore:g.default}})],C),t.default=C},477:function(e,t,n){"use strict";n.r(t);var r=n(478),o=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);t.default=o.a},478:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,s=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5),i=n(691);let s=class extends o.Vue{constructor(){super(...arguments),this.isShowMore=!1}get headerText(){let e=this.taskIds.length>999?"999+":String(this.taskIds.length);return this.panelText+"("+e+")"}get panelText(){return"下载"}get filteredTaskIds(){return this.taskIds.slice(0,3)}showMore(){this.$emit("showMore","download")}};r([o.Prop()],s.prototype,"keyword",void 0),r([o.Prop({required:!0})],s.prototype,"taskIds",void 0),r([o.Prop({required:!0})],s.prototype,"select",void 0),s=r([o.Component({components:{TaskItem:i.default}})],s),t.default=s},479:function(e,t,n){"use strict";n.r(t);var r=n(480),o=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);t.default=o.a},48:function(e,t){e.exports=require("zlib")},480:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,s=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(5),s=n(4),a=n(91),c=n(16),l=n(37),u=n(11);let d=class extends i.Vue{constructor(){super(...arguments),this.taskInfo={},this.formatSize="",this.formatTime="",this.taskname="",this.iconType=""}onIsChosen(e){e&&this.$el.scrollIntoView(!1)}get formatedTitle(){let e=this.taskname;return this.keyword.trim().split(" ").forEach(t=>{0!==t.length&&(t=t.replace(new RegExp("[\\(|\\)|\\[|\\]|\\+|\\^|\\*|\\?|\\{|\\}|\\$]","ig"),e=>"\\"+e),e=e.replace(new RegExp(t,"ig"),e=>'<span class="keyword">'+e+"</span>"))}),e}onSelectTask(){return o(this,void 0,void 0,function*(){s.client.broadcastEvent("StartClearTextTimer"),yield s.client.callServerFunction("SwitchDetail",this.taskId),u.XLStatNS.trackEvent("download_leftpanel","search_association_page_download_click","",0,0,0,0,"position=associate_file"),window.close()})}mounted(){return o(this,void 0,void 0,function*(){this.taskInfo=yield s.client.callServerFunction("GetTaskBaseInfo2",this.taskId),this.formatSize=c.ThunderUtil.formatSize(this.taskInfo.fileSize),this.formatTime=a.TimeHelperNS.formatDate(new Date(this.taskInfo.createTime),"yyyy-MM-dd hh:mm"),this.taskname=this.taskInfo.taskName,0===this.taskInfo.taskType?this.iconType="xlx-icon-type-group":this.iconType=l.TaskUtilHelper.getTaskIcon(this.taskInfo.taskName,this.taskInfo.taskType)})}};r([i.Prop()],d.prototype,"keyword",void 0),r([i.Prop()],d.prototype,"taskId",void 0),r([i.Prop({default:!1})],d.prototype,"isChosen",void 0),r([i.Watch("isChosen")],d.prototype,"onIsChosen",null),d=r([i.Component({components:{}})],d),t.default=d},481:function(e,t,n){"use strict";n.r(t);var r=n(482),o=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);t.default=o.a},482:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,s=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5),i=n(691);let s=class extends o.Vue{constructor(){super(...arguments),this.isShowMore=!1}get headerText(){return this.panelText+"("+this.taskIds.length+")"}get panelText(){return"下载"}get filteredTaskIds(){return this.taskIds.slice(0,3)}showMore(){this.$emit("showMore")}};r([o.Prop()],s.prototype,"keyword",void 0),r([o.Prop({required:!0})],s.prototype,"taskIds",void 0),r([o.Prop({required:!0})],s.prototype,"select",void 0),s=r([o.Component({components:{TaskItem:i.default}})],s),t.default=s},483:function(e,t,n){"use strict";n.r(t);var r=n(484),o=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);t.default=o.a},484:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,s=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5),i=n(692);let s=class extends o.Vue{constructor(){super(...arguments),this.isShowMore=!1}get headerText(){let e=this.tasks.length>999?"999+":String(this.tasks.length);return this.panelText+"("+e+")"}get filteredTasks(){return this.tasks.slice(0,3)}showMore(){this.$emit("showMore","thunder-pan")}};r([o.Prop()],s.prototype,"keyword",void 0),r([o.Prop({required:!0})],s.prototype,"tasks",void 0),r([o.Prop({required:!0})],s.prototype,"select",void 0),r([o.Prop({default:"云盘"})],s.prototype,"panelText",void 0),s=r([o.Component({components:{PanTaskItem:i.default}})],s),t.default=s},485:function(e,t,n){"use strict";n.r(t);var r=n(486),o=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);t.default=o.a},486:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,s=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const i=n(5),s=n(91),a=n(16),c=n(4),l=n(37),u=n(11);let d=class extends i.Vue{constructor(){super(...arguments),this.formatSize="",this.formatTime="",this.taskname="",this.iconType=""}onIsChosen(e){e&&this.$el.scrollIntoView(!1)}get isFile(){return"drive#folder"!==this.task.kind}get formatedTitle(){let e=this.taskname;return this.keyword.trim().split(" ").forEach(t=>{0!==t.length&&(t=t.replace(new RegExp("[\\(|\\)|\\[|\\]|\\+|\\^|\\*|\\?|\\{|\\}|\\$]","ig"),e=>"\\"+e),e=e.replace(new RegExp(t,"ig"),e=>'<span class="keyword">'+e+"</span>"))}),e}onSelectTask(){return o(this,void 0,void 0,function*(){c.client.broadcastEvent("StartClearTextTimer"),yield c.client.callServerFunction("IpcRedirectCurrentDirectoryById",this.task.id),u.XLStatNS.trackEvent("download_leftpanel","search_association_page_yunpan_click","",0,0,0,0,"position=associate_file"),window.close()})}mounted(){return o(this,void 0,void 0,function*(){this.formatSize=a.ThunderUtil.formatSize(parseInt(this.task.size,10)),this.formatTime=s.TimeHelperNS.formatDate(new Date(this.task.created_time),"yyyy-MM-dd hh:mm"),this.taskname=this.task.name,"drive#file"===this.task.kind?this.iconType=l.TaskUtilHelper.getTaskIcon(this.task.name,void 0,"xly-type-"):this.iconType="xly-type-group"})}};r([i.Prop()],d.prototype,"keyword",void 0),r([i.Prop()],d.prototype,"task",void 0),r([i.Prop({default:!1})],d.prototype,"isChosen",void 0),r([i.Watch("isChosen")],d.prototype,"onIsChosen",null),d=r([i.Component({})],d),t.default=d},487:function(e,t,n){"use strict";n.r(t);var r=n(488),o=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,function(){return r[e]})}(i);t.default=o.a},488:function(e,t,n){"use strict";var r=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,s=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s};Object.defineProperty(t,"__esModule",{value:!0});const o=n(5),i=n(692);let s=class extends o.Vue{constructor(){super(...arguments),this.isShowMore=!1}get headerText(){return this.panelText+"("+this.tasks.length+")"}showMore(){this.$emit("showMore")}};r([o.Prop()],s.prototype,"keyword",void 0),r([o.Prop({required:!0})],s.prototype,"tasks",void 0),r([o.Prop({required:!0})],s.prototype,"select",void 0),r([o.Prop({default:"云盘"})],s.prototype,"panelText",void 0),s=r([o.Component({components:{PanTaskItem:i.default}})],s),t.default=s},49:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(2),o=n(6),i=n(1),s=n(8),a=n(32),c=n(3),l=n(20),u=s.default(r.join(__rootDir,"../bin/ThunderHelper.node"));function d(){"console"===process.env.TL_OUTPUT?i.default.outputLogger=i.outputLoggerConsole:i.default.outputLogger=function(){function e(e){return function(...t){u.printEtwLog(e,function(...e){return e.map(e=>o.inspect(e)).join(" ").replace(/%/g,"%%")}(...t))}}return{[i.LogLevel.Critical]:e(i.LogLevel.Critical),[i.LogLevel.Error]:e(i.LogLevel.Error),[i.LogLevel.Warning]:e(i.LogLevel.Warning),[i.LogLevel.Information]:e(i.LogLevel.Information),[i.LogLevel.Verbose]:e(i.LogLevel.Verbose)}}()}function h(){let e=a.DevEnvHelperNS.isLogEnable();"1"===process.env.TL_ENABLE!==e&&(process.env.TL_ENABLE=e?"1":"0",i.default.enableLogger=e,u.enableETWLogger(e));let t=a.DevEnvHelperNS.getLogOutput();t&&t!==process.env.TL_OUTPUT&&(process.env.TL_OUTPUT=t,d())}process.env.TL_ENABLE="0",i.default.enableLogger="1"===process.env.TL_ENABLE,d(),h(),"browser"===process.type?c.ipcMain.on(l.ThunderChannelList.channelRMUpdateLogEnviroment,()=>{c.BrowserWindow.getAllWindows().forEach(e=>{e.isDestroyed()||e.webContents.send(l.ThunderChannelList.channelMRUpdateLogEnviroment)}),h()}):"renderer"===process.type&&c.ipcRenderer.on(l.ThunderChannelList.channelMRUpdateLogEnviroment,()=>{h()})},5:function(e,t,n){e.exports=n(9)(213)},50:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(33),o=n(12),i=n(30),s=n(47),a=n(29);t.Client=class extends o.EventEmitter{constructor(e){if(e=e||{},super(),this.inprocess=!1,this.context=void 0,e.context&&(this.context=Object.assign({},e.context),this.context.productId=e.socketPrex),e.socket)this.socket=e.socket,this.bind();else if(global.__xdasIPCServer&&global.__xdasIPCServer.getProductId().toLowerCase()===e.socketPrex.toLowerCase())this.inprocess=!0;else{let t=a.getSockPath(e.socketPrex);this.socket=r.connect(t),this.bind()}}isInprocess(){return this.inprocess}getContext(){return this.context}bind(){const e=new s.Parser,t=this.socket;t.on("data",t=>{e.feed(t)}),t.on("connect",()=>{this.emit("connect")}),t.on("end",()=>{i.information("socket is ended"),this.socket=null,this.emit("end")}),t.on("error",e=>{this.socket=null,this.emit("error",e)}),e.on("message",e=>{this.emit("message",e)}),this.parser=e}send(e){if(this.socket)try{this.socket.write(this.parser.encode(e))}catch(e){i.error(e.message)}else i.information("This socket has been ended by the other party",this.context&&this.context.name)}}},51:function(e,t){e.exports=require("string_decoder")},52:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(14),i=(n(21),n(2)),s=n(6);let a=null;const c=n(11),l=n(3),u=n(1),d=n(8),h=n(20),f=n(7),m="xdas_profile_stat";let p="",w=void 0,g=null,_=void 0,v=null,y=d.default(i.join(__rootDir,"../bin/ThunderHelper.node")),b=new Set;function C(){return r(this,void 0,void 0,function*(){return new Promise(e=>r(this,void 0,void 0,function*(){void 0===_&&(null===v&&(v=new Promise(e=>{e(_=function(e){let t="";if(0===e.length&&"renderer"===process.type){let e=i.normalize(decodeURIComponent(window.location.href)),n=e.indexOf(process.resourcesPath);n=n>-1?n+process.resourcesPath.length+1:n;let r=e.length-1,o=e.indexOf("?"),s=e.indexOf("#");r=o>-1?Math.min(o-1,r):r,r=s>-1?Math.min(s-1,r):r,n>-1&&r>=n&&(t=e.substr(n,r-n+1))}return 0===t.length&&(t=0!==e.length?e:process.type),t=t.replace(/\||,|;/g,"_")}(""))})),_=yield v),e(_)}))})}function x(e){let t="";do{if(null===e||void 0===e)break;switch(typeof e){case"string":t=e;break;case"object":t=s.inspect(e)||"";break;case"number":case"boolean":t=e.toString()||""}}while(0);return t}function R(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function S(e){return r(this,void 0,void 0,function*(){return new Promise(t=>r(this,void 0,void 0,function*(){let r=void 0;null===a&&(a=yield Promise.resolve().then(()=>n(39)));let o=a.createHash("md5");null!==o&&(r=o.update(e).digest("hex")),t(r)}))})}function k(){return new Promise(e=>r(this,void 0,void 0,function*(){let t="";t=void 0===w?"browser"===process.type?function(){if(void 0===w){let e=process.argv.length,t=process.argv;for(let n=0;n<e;n++){let e=t[n];if(e.startsWith("-StartType:")){w=e.substring("-StartType:".length);break}}void 0===w&&(w="")}return w}():yield function(){return r(this,void 0,void 0,function*(){return null===g&&(g=new Promise(e=>{l.ipcRenderer.send(h.ThunderChannelList.channelRMGetBrowserStartType),l.ipcRenderer.once(h.ThunderChannelList.channelMRGetBrowserStartTypeResult,(t,n)=>{w=n,e(n),g=null})})),g})}():w,e(t)}))}function E(e,t,n,o){return r(this,void 0,void 0,function*(){let i=x(t),s=x(n),a=yield S(s),l=function(e){let t=new RegExp(R("file:///"),"g"),n=new RegExp(R(process.resourcesPath+"\\"),"g"),r=new RegExp(R(encodeURI(process.resourcesPath.replace(/\\/g,"/")+"/")),"g");return e.replace(t,"").replace(n,"").replace(r,"")}(x(o)),u=yield S(l),d=`${e}:${a}:${u}`;b.has(d)||(b.add(d),c.XLStatNS.trackEvent(m,"uncaught_exception","",0,0,0,0,`type=${e},business_name=${yield C()},code=${i},message_hash=${a},message=${encodeURI(s)},stack_hash=${u},stack=${encodeURI(l)}`)),function(e,t,n,o){return r(this,void 0,void 0,function*(){})}().catch()})}function T(e){console.error(e);let t=e||{};E("unhandledRejection",t.code,t instanceof Error?t.message:t,t.stack).catch()}!function(e){e.init=function(e){p=e},e.trackColdStartUpEvent=function(e){return r(this,void 0,void 0,function*(){let t=y.iSColdStartUp()?1:0,n=o.release(),r=y.performanceMonitorReporter.getProcessUptime(),i=yield k(),s=`key=${e},start_type=${i},cold_start_up=${t},os_version=${n},cost_time=${r}`;c.XLStatNS.trackEvent(m,"cold_start_up","",0,0,0,0,s)})}}(t.PerformanceMonitorStatNS||(t.PerformanceMonitorStatNS={})),function(){return r(this,void 0,void 0,function*(){if(process.on("uncaughtException",e=>{console.error(e);let t=e||{};E("uncaughtException",t.code,t.message,t.stack).catch()}),"browser"===process.type)process.on("unhandledRejection",(e,t)=>{T(e)}),l.ipcMain.on(h.ThunderChannelList.channelRMGetBrowserStartType,function(e){return r(this,void 0,void 0,function*(){let t=yield k();e.sender.send(h.ThunderChannelList.channelMRGetBrowserStartTypeResult,t)})});else if("browser"!==process.type){window.addEventListener("unhandledrejection",e=>{T(e&&e.reason||{})});let e=yield f.asyncRemoteCall.getCurrentWebContents();null!==e&&void 0!==e&&e.once("did-finish-load",()=>{(function(){return r(this,void 0,void 0,function*(){do{if("browser"===process.type)break;if(null===window.performance.timing||void 0===window.performance.timing)break;let e=y.iSColdStartUp()?1:0,t=o.release(),n=window.performance.timing,r=n.loadEventEnd-n.navigationStart,i=n.fetchStart-n.navigationStart,s=n.domainLookupEnd-n.domainLookupStart,a=n.connectEnd-n.connectStart,l=n.responseStart-n.requestStart,u=n.responseEnd-n.responseStart,d=n.domComplete-n.domLoading,h=yield k();c.XLStatNS.trackEvent(m,"page_load_time","",0,0,0,0,`start_type=${h},cold_start_up=${e},os_version=${t},load_time=${r},before_fetch_time=${i},domin_lookup_time=${s},connect_time=${a},first_response_time=${l},responseTime=${u},domTime=${d},process=${p}`)}while(0)})})().catch()})}u.default.hook("beforeLog",(e,t,...n)=>{e===u.LogLevel.Critical&&c.XLStatNS.trackEvent(m,"critical_error","",0,0,0,0,`module_name=${t},messages=${n}`)})})}().catch()},53:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWebContents=t.getCurrentWebContents=t.getCurrentWindow=t.getGlobal=t.getBuiltin=t.remoteRequire=t.Interest=void 0;const r=n(3),o=n(41),i=n(54),s=n(55),a=n(35),c=n(56),l=n(13),u=n(36),d=r.ipcRenderer,h=process.electronBinding("v8_util"),f=new i.default,m=h.createIDWeakMap(),p=h.getHiddenValue(global,"contextId");class w{constructor(e){if("object"==typeof e?(this.on="object"==typeof e.on?e.on:{},this.once="object"==typeof e.once?e.once:{}):(this.on={},this.once={}),!this.check())throw new Error("unexpected param")}check(){let e=!0;do{let t=Object.getOwnPropertyNames(this.on);if(t.forEach(t=>{"function"!=typeof this.on[t]&&(e=!1)}),!e)break;(t=Object.getOwnPropertyNames(this.once)).forEach(t=>{"function"!=typeof this.once[t]&&(e=!1)})}while(0);return e}}function g(e,t=new Set){const n=e=>{if(t.has(e))return{type:"value",value:null};let r=e;if(Array.isArray(e)){t.add(e);let n={type:"array",value:g(e,t)};return t.delete(e),n}if(ArrayBuffer.isView(r))return{type:"buffer",value:o.Buffer.from(e)};if(e instanceof Date)return{type:"date",value:e.getTime()};if(null!=e&&"object"==typeof e){if(u.isPromise(e))return{type:"promise",then:n(function(t,n){e.then(t,n)})};if(h.getHiddenValue(e,"__remote_id__"))return{type:"remote-object",id:h.getHiddenValue(e,"__remote_id__")};let r={type:e instanceof w?"interest":"object",name:e.constructor?e.constructor.name:"",members:[]};t.add(e);for(let t in e)r.members.push({name:t,value:n(e[t])});return t.delete(e),r}if("function"==typeof e){return{type:"function",id:f.add(e),location:h.getHiddenValue(e,"__remote_call_location__"),length:e.length}}return{type:"value",value:e}};return e.map(n)}function _(e,t,n){u.isPromise(e)?e.then(e=>{t(e)},e=>{n(e)}):t(e)}function v(e,t,n,r=!1){const o=t=>{if(e.hasOwnProperty(t.name)&&!r)return;let n,o={enumerable:t.enumerable,configurable:!0};if("method"===t.type){if(t.value.refId){if(m.has(t.value.refId)&&(n=m.get(t.value.refId)),null==n)throw new Error("member refId pointer to null"+t.value.refId+"name: "+t.name)}else n=x(t.value,e,t.name);o.get=(()=>n),o.set=(e=>n=e)}else"get"===t.type&&(o.get=(()=>n),t.writable&&(o.set=(e=>{n=e})),n=x(t.value));Object.defineProperty(e,t.name,o)};if(Array.isArray(n)){let e=n.length;for(let t=0;t<e;t++)o(n[t])}}function y(e,t,n){if(n){let t=x(n);Object.setPrototypeOf(e,t)}}function b(e,t){h.setHiddenValue(e,"__remote_id__",t)}function C(e){return h.getHiddenValue(e,"__remote_id__")}function x(e,t,n){const r={value:()=>e.value,array:()=>e.members.map(e=>x(e)),buffer:()=>o.Buffer.from(e.value),promise:()=>Promise.resolve({then:x(e.then)}),error:()=>(function(e){const t=(()=>"error"===e.type?new Error:{})();for(let n=0;n<e.members.length;n++){let{name:r,value:o}=e.members[n];t[r]=o}return t})(e),date:()=>new Date(e.value),exception:()=>{throw new Error(`${e.message}\n${e.stack}`)}};let i;return e.type in r?i=r[e.type]():e.refId?m.has(e.refId)?(h.addRemoteObjectRef(p,e.refId),i=m.get(e.refId)):(l.warn("[metaToValue] refId point to null"+e.refId),i="function"===e.type?()=>{}:{}):e.id?m.has(e.id)?(h.addRemoteObjectRef(p,e.id),v(i=m.get(e.id),e.id,e.members,!0),y(i,e.id,e.proto)):(i="function"===e.type?t?function(e,t,n){if(m.has(n.id))return m.get(n.id);let r=C(e),o=function(...e){throw Error("never should come to a proxied function")};Object.defineProperty(o,"name",{value:t,writable:!1,enumerable:!0});let i=new Proxy(o,{apply:(e,n,o)=>new Promise((e,i)=>{let l=C(n);if(l||(l=C(n.__remoteObj_)),!l)throw Error("is this function was a bound function?");let u=a.default.browser.memberCall,h=c.default(u),f=g(o);d.send(u,p,h,l,r,t,f),s.default.add(h,t=>{try{_(x(t),e,i)}catch(e){i(e)}})}),construct:(e,n,o)=>new Promise((e,o)=>{let i=a.default.browser.memberConstruct,l=c.default(i);d.send(i,p,l,r,t,g(n)),s.default.add(l,t=>{try{let n=x(t);e(n)}catch(e){o(e)}})})});return h.setHiddenValue(o,"__remote_id__",n.id),i}(t,n,e):function(e){let t=e.id;const n=function(...e){throw new Error("Should Never com to a remoteFunction PlaceHolder")};return b(n,t),new Proxy(n,{apply:(e,n,r)=>new Promise((e,o)=>{let i=a.default.browser.functionCall,l=c.default(i),u=C(n);d.send(i,p,l,u,t,g(r)),s.default.add(l,t=>{try{_(x(t),e,o)}catch(e){o(e)}})}),construct:(e,n,r)=>new Promise((e,r)=>{let o=a.default.browser.construct,i=c.default(o);d.send(o,p,i,t,g(n)),s.default.add(i,t=>{try{let n=x(t);e(n)}catch(e){r(e)}})})})}(e):{},h.setRemoteObjectFreer(i,p,e.id),m.set(e.id,i),h.setHiddenValue(i,"__remote_id__",e.id),h.addRemoteObjectRef(p,e.id),function(e){let t=C(e);Object.defineProperties(e,{__set:{enumerable:!1,writable:!1,value:function(n,r){if("function"==typeof r)throw new Error("set a function to a remote member is dangerous");return new Promise((o,i)=>{let l=a.default.browser.memberSet,u=c.default(l),h=g([r]);d.send(l,p,u,t,n,h),s.default.add(u,t=>{try{let s=x(t);e[n]=r,o(s)}catch(e){i(e)}})})}},__get:{enumerable:!1,writable:!1,value:function(n){return new Promise((r,o)=>{let i=a.default.browser.memberGet,l=c.default(i);d.send(i,p,l,t,n),s.default.add(l,t=>{try{const i=x(t);e[n]=i,r(i)}catch(e){o(e)}})})}},__sync:{enumerable:!1,writable:!1,value:function(){return new Promise((e,n)=>{let r=a.default.browser.sync,o=c.default(r);d.send(r,p,o,t),s.default.add(o,r=>{try{if(r.id!==t)throw Error("SYNC_RETURN: remote id not match");let o=x(r);e(o)}catch(e){n(e)}})})}}})}(i),v(i,e.id,e.members),y(i,e.id,e.proto),Object.defineProperty(i.constructor,"name",{value:e.name}),e.extendedMemberNames&&e.extendedMemberNames.forEach((e,t)=>{let n=i[e],r=i.__proto__;for(;r;){if(Object.prototype.hasOwnProperty.call(r,e)){delete r[e];break}r=r.__proto__}Object.defineProperty(i,e,{value:n,enumerable:!1,writable:!1,configurable:!0})})):l.error("no id of meta:",e),i}t.Interest=w;class R{constructor(...e){if(this.__resolved_=!1,this.__promise_=null,this.__remoteObj_=null,this.__what_="",this.__name_="","string"===typeof arguments[0]){let e=arguments[0],t=arguments[1];this.__what_=e,this.__name_=t||e,this.__resolved_=!1,this.__remoteObj_=null,this.__promise_=new Promise((n,r)=>{let o=this.getChannel(e),i=c.default(o);d.send(o,p,i,t),s.default.add(i,e=>{try{let t=x(e);this.__remoteObj_=t,this.__resolved_=!0,n(t)}catch(e){r(e)}})})}else this.__remoteObj_=arguments[0],this.__resolved_=!0,this.__promise_=null}getChannel(e){let t="";switch(e){case"module":t=a.default.browser.require;break;case"builtin":t=a.default.browser.builtIn;break;case"global":t=a.default.browser.global;break;case"current_window":t=a.default.browser.currentWindow;break;case"current_web_contents":t=a.default.browser.currentWebContents;break;case"client_web_contents":t=a.default.browser.clientWebContents;break;case"web_contents":t=a.default.browser.webContents}return t}__resolve(){let e=this.__promise_;if(null!==e);else{if(!this.__resolved_)throw Error("missing the promise for ayncnomously get remote object");e=new Promise((e,t)=>{e(this.__remoteObj_)}),this.__promise_=e}return e}__isResolved(){return this.__resolved_}}function S(e,t,n){try{s.default.invoke(t,n).remove(t)}finally{s.default.remove(t)}}d.on(a.default.renderer.requireReturn,S),d.on(a.default.renderer.getBuiltInReturn,S),d.on(a.default.renderer.getGlobalReturn,S),d.on(a.default.renderer.currentWindowReturn,S),d.on(a.default.renderer.currentWebContentsReturn,S),d.on(a.default.renderer.functionCallReturn,S),d.on(a.default.renderer.constructReturn,S),d.on(a.default.renderer.memberCallReturn,S),d.on(a.default.renderer.memberSetReturn,S),d.on(a.default.renderer.memberGetReturn,S),d.on(a.default.renderer.memberConstructReturn,S),d.on(a.default.renderer.callback,(e,t,n)=>{f.apply(t,x(n))}),d.on(a.default.renderer.syncReturn,S),d.on("ELECTRON_RENDERER_RELEASE_CALLBACK",(e,t)=>{l.info("[RELEASE_CALLBACK]: callbackId:",t),f.remove(t)}),process.on("exit",()=>{d.send(a.default.browser.contextRelease)});const k=["__resolve","__isResolved"],E=["__promise_","__resolved_","__remoteObj_","__name_","__what_"],T=e=>{if(!e.__isResolved())throw Error("Can not access the property of a remote module which has not Resolved yet.")};function I(e){const t=function(){};Object.defineProperty(t,"name",{value:e.__name_}),Object.defineProperty(t,"what",{enumerable:!1,value:e.__what_});let n=new Proxy(t,{getPrototypeOf:t=>(T(e),Reflect.getPrototypeOf(e.__remoteObj_)),setPrototypeOf:(e,t)=>{throw new Error("changing prototype of remote object is forbidden")},isExtensible:t=>(T(e),Reflect.isExtensible(e.__remoteObj_)),preventExtensions:t=>(T(e),Reflect.preventExtensions(e)),getOwnPropertyDescriptor:(t,n)=>(T(e),Reflect.getOwnPropertyDescriptor(e.__remoteObj_,n)),has:(t,n)=>(T(e),Reflect.has(e.__remoteObj_,n)),deleteProperty:(t,n)=>(T(t),Reflect.deleteProperty(e.__remoteObj_,n)),defineProperty:(t,n,r)=>(T(e),Reflect.defineProperty(e.__remoteObj_,n,r)),get:(t,n,r)=>{if("string"==typeof n){if(String.prototype.includes.call(E,n)){return e[n]}if(String.prototype.includes.call(k,n)){return e[n]}}return T(e),Reflect.get(e.__remoteObj_,n)},set:(t,n,r,o)=>(T(e),Reflect.set(e.__remoteObj_,n,r,o)),ownKeys:t=>(T(e),Reflect.ownKeys(e.__remoteObj_)),apply:(t,n,r)=>{T(e),Reflect.apply(e.__remoteObj_,n,r)},construct:(t,n,r)=>{if(T(e),"function"!=typeof e.__remoteObj_)throw Error("operator new ONLY used for function");return new Promise((t,r)=>{let o=a.default.browser.construct,i=c.default(o),l=h.getHiddenValue(e.__remoteObj_,"__remote_id__");d.send(o,p,i,l,g(n)),s.default.add(i,e=>{try{t(x(e))}catch(e){r(e)}})})}});return e.__promise_.then(e=>{let t=typeof e;if("function"===t||"object"===t){let t=C(e);t&&b(n,t)}}),n}t.remoteRequire=function(e){return I(new R("module",e))},t.getBuiltin=function(e){return I(new R("builtin",e))},t.getGlobal=function(e){return I(new R("global",e))},t.getCurrentWindow=function(){return I(new R("current_window"))},t.getCurrentWebContents=function(){return I(new R("current_web_contents"))},t.getWebContents=function(){return I(new R("web_contents"))}},54:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.callbacks={}}add(e){let t=r.getHiddenValue(e,"__remote_callback_id__");if(null!=t)return t;t=this.nextId-=1;const n=/at (.*)/gi,o=(new Error).stack;let i,s=n.exec(o);for(;null!==s;){const e=s[1];if(!e.includes("native")&&!e.includes("electron.asar")){i=/([^/^)]*)\)?$/gi.exec(e)[1];break}s=n.exec(o)}return this.callbacks[t]=e,r.setHiddenValue(e,"__remote_callback_id__",t),r.setHiddenValue(e,"__remote_call_location__",i),t}get(e){return this.callbacks[e]||function(){}}apply(e,...t){return this.get(e).apply(global,...t)}remove(e){const t=this.callbacks[e];t&&(r.deleteHiddenValue(t,"__remote_callback_id__"),delete this.callbacks[e])}}},55:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13);var o;!function(e){let t={};e.add=function(e,n,r){t[e]={func:n,thisArg:r}},e.invoke=function(n,...o){let i=t[n];return i?i.thisArg?i.func.apply(i.thisArg,...o):i.func(...o):r.error(`Cannot invoke function by unrecognize id. ${n}`),e},e.remove=function(e){delete t[e]}}(o||(o={})),t.default=o},56:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=0;t.default=function(e){return e?e.concat(".").concat(String(++r)):String(++r)}},564:function(e,t,n){"use strict";var r=n(1293);n.n(r).a},57:function(e,t,n){"use strict";const r=n(3),o=n(58),i=n(35),s=n(59),a=n(13),c=n(36),l=r.ipcMain,u=process.electronBinding("v8_util");let d=u.createDoubleIDWeakMap();const h=new o.default;function f(e,t,n,r,o,i){let s,c=!1,l=null,u=!1;do{try{s=t[r]}catch(e){c=!0}if(c)try{s=n.value[r],c=!1,n.meta.extendedMemberNames.push(r),u=!0}catch(e){a.error(`property ${r} untouchable, even try root[name]`)}if(c)break;let o=Object.getOwnPropertyDescriptor(t,r);if(void 0===o){a.warn(`descriptor of property ${r} is undefined`);break}l={name:r,enumerable:o.enumerable,writable:!1,type:"get"},void 0===o.get&&"function"==typeof s?l.type="method":((o.set||o.writable)&&(l.writable=!0),l.type="get"),u?(l.configurable=!0,l.value=w(e,s,i,!1,null)):l.value=w(e,s,i,!1,n)}while(0);return l}function m(e,t,n,r=null){let o=Object.getOwnPropertyNames(t);"function"==typeof t&&(o=o.filter(function(e){return!String.prototype.includes.call(s.propertiesOfFunction,e)}));let i=[];do{if(0===o.length)break;let s=o.length;for(let a=0;a<s;a++){let s=f(e,t,n,o[a],0,r);s&&i.push(s)}}while(0);return i}function p(e,t,n,r=null){let o=null,i=Object.getPrototypeOf(t);return o=null===i||i===Object.prototype||i===Function.prototype?null:w(e,i,r,!1,n)}function w(e,t,n=null,r=!1,o=null){n=null===n?{}:n;const i={type:typeof t};"object"===i.type&&(i.type=function(e,t){let n=typeof e;if("object"!==n)throw new Error("incorrect arg at index 0. non-object");return null===e?n="value":ArrayBuffer.isView(e)?n="buffer":Array.isArray(e)?n="array":e instanceof Error?n="error":e instanceof Date?n="date":c.isPromise(e)?n="promise":Object.prototype.hasOwnProperty.call(e,"callee")&&null!=e.length?n="array":t&&u.getHiddenValue(e,"simple")&&(n="value"),n}(t,r));do{if("object"===i.type||"function"===i.type){let r=h.getIdOfObject(t);if(r&&n[r]){i.refId=r,h.add(e,t);break}}"array"===i.type?i.members=t.map(t=>w(e,t,n)):"object"===i.type||"function"===i.type?(null==o&&(i.extendedMemberNames=[],o={value:t,meta:i}),i.name=t.constructor?t.constructor.name:"",i.id=h.add(e,t),n[i.id]=!0,i.members=m(e,t,o,n),i.proto=p(e,t,o,n)):"buffer"===i.type?i.value=Buffer.from(t):"promise"===i.type?(t.then(function(){},function(){}),i.then=w(e,function(e,n){t.then(e,n)})):"error"===i.type?(i.members=g(t),i.members.push({name:"name",value:t.name})):"date"===i.type?i.value=t.getTime():(i.type="value",i.value=t)}while(0);return i}function g(e){return Object.getOwnPropertyNames(e).map(t=>({name:t,value:e[t]}))}function _(e,t,n,o){const s=function(o){let c,l,f=0,m=0;switch(o.type){case"value":return o.value;case"remote-object":return h.get(o.id);case"array":return _(e,t,n,o.value);case"buffer":return Buffer.from(o.value);case"date":return new Date(o.value);case"promise":return Promise.resolve({then:s(o.then)});case"object":case"interest":{let e={};for(Object.defineProperty(e.constructor,"name",{value:o.name}),f=0,m=(l=o.members).length;f<m;f++)e[(c=l[f]).name]=s(c.value);return e}case"function":{const s=e.id,c=[n,o.id];if(a.info("renderer function id:"+c),d.has(c))return d.get(c);let l=function(...t){a.info("[CALLBACK] args",t);let n=[...t];e.isDestroyed()||s!==e.id?function(e,t,n){let o="Attempting to call a function in a renderer window that has been closed or released."+`\nFunction provided here: ${e.location}`;if(t.length>0&&t[0].sender&&t[0].sender instanceof r.webContents.constructor){const{sender:e}=t[0],r=e.eventNames().filter(t=>{let r=e.listeners(t),o=!1;return r.forEach(e=>{e===n&&(o=!0)}),o});r.length>0&&(o+=`\nRemote event names: ${r.join(", ")}`,r.forEach(t=>{Object.getPrototypeOf(e).removeListener.call(e,t,n)}))}a.warn(o)}(o,n,l):e.send(i.default.renderer.callback,o.id,w(e,n))};return Object.defineProperty(l,"length",{value:o.length}),u.setRemoteCallbackFreer(l,t,n,o.id,e),d.set(c,l),l}default:throw new TypeError(`Unknown type: ${o.type}`)}};return o.map(s)}function v(e,t,n,r){let o,i;try{return t.apply(n,r)}catch(e){return i=t.name,new Error(`Could not call remote function '${o=null!=i?i:"anonymous"}'. Check that the function signature is correct. Underlying error: ${e.message}`)}}function y(e){return{type:"exception",message:e.message,stack:e.stack||e}}function b(e){const t=new Error(e);throw Object.defineProperty(t,"code",{value:"EBADRPC"}),Object.defineProperty(t,"errno",{value:-72}),t}var C;!function(e){const t=(e,t,...n)=>{const r=e.sender;r.isDestroyed()?a.warn("webcontext is destroyed."):r.send(t,...n)};e.startServer=function(){l.on(i.default.browser.require,(e,n,r,o)=>{a.info(`[REQUIRE] module=${o} `);let s=process.mainModule.require(o),c=w(e.sender,s);t(e,i.default.renderer.requireReturn,r,c)}),l.on(i.default.browser.builtIn,(e,n,o,s)=>{a.info(`[BUILTIN]: property=${s} contextId=${n}`);let c=r[s],l=w(e.sender,c);a.info(`[BUILTIN]: returns remoteId:${l.id}, type: ${typeof c}`),t(e,i.default.renderer.getBuiltInReturn,o,l)}),l.on(i.default.browser.global,(e,n,r,o)=>{a.info(`[GLOBAL]: proerty:${o} contextId=${n}`);let s,c=global[o];s=w(e.sender,c),a.info(`[GLOBAL]: returns remoteid=${s.id}, obj=`+typeof c),t(e,i.default.renderer.getGlobalReturn,r,s)}),l.on(i.default.browser.currentWindow,(e,n,r,o)=>{a.info(`[CURRENT_WINDOW]: property=${o} contextId=${n}`);let s=e.sender.getOwnerBrowserWindow.call(e.sender),c=w(e.sender,s);a.info(`[CURRENT_WINDOW]: returns remoteid=${c.id}, obj=`+s),t(e,i.default.renderer.currentWindowReturn,r,c)}),l.on(i.default.browser.currentWebContents,(e,n,r,o)=>{t(e,i.default.renderer.currentWebContentsReturn,r,w(e.sender,e.sender))}),l.on(i.default.browser.webContents,(e,n,o,s)=>{a.info(`[WebContents]: proerty:${s} contextId=${n}`);let c,l=r.webContents;c=w(e.sender,l),a.info(`[WebContents]: returns remoteid=${c.id}, obj=`+typeof l),t(e,i.default.renderer.webContentsReturn,o,c)});const e=(e,t)=>{const n=(t,n)=>{t&&Object.getOwnPropertyNames(t).forEach(r=>{n?e.once(r,t[r]):e.on(r,t[r])})};t.on&&n(t.on,!1),t.once&&n(t.once,!0)};l.on(i.default.browser.construct,(n,r,o,s,c)=>{let l,u=null;try{a.info(`[CONSTRUCTOR]: remoteId=${s} `);let d=c.length>0?c[c.length-1]:null;c=_(n.sender,n.frameId,r,c);let f=h.get(s);null==f&&b(`Cannot call constructor on missing remote object ${s}`),d&&"interest"===d.type&&(u=c.pop());let m=new(Function.prototype.bind.apply(f,[null,...c]));m&&u&&e(m,u),l=w(n.sender,m,null,!1),a.info(`[CONSTRUCTOR]: returns remoteId =${l.id} name=${f.name} `)}catch(e){l=y(e)}finally{t(n,i.default.renderer.constructReturn,o,l)}}),l.on(i.default.browser.functionCall,function(e,n,r,o,s,c){let l;try{a.info(`[FUNCTION_CALL]: remoteId=${s}`),c=_(e.sender,e.frameId,n,c);let u=h.get(s);if(null==u)a.error(`Cannot call function on missing remote object ${s}`),l=w(e.sender,void 0);else{let t=o?h.get(o):global;if(t){let n=v(0,u,t,c);l=w(e.sender,n)}else a.error(`Cannot call function(${s}) on missing context(${o})`),l=w(e.sender,void 0)}a.info(`[FUNCTION_CALL]: name=${u.name}`)}catch(e){l=y(e)}finally{t(e,i.default.renderer.functionCallReturn,r,l)}}),l.on(i.default.browser.memberCall,function(e,n,r,o,s,c,l){let u;a.info(`[MEMBER_CALL]: thisArg=${o}, remoteId=${s}, method=${c}, args count=${l.length}`);try{l=_(e.sender,e.frameId,n,l);let d=h.get(s);null==d&&b(`Cannot call function '${c}' on missing remote object ${s}`);let f=o?h.get(o):d;if(f){let t=v(0,d[c],f,l);u=w(e.sender,t),a.info("[MEMBER_CALL]: return="+t)}else u=w(e.sender,void 0)}catch(e){u=y(e)}finally{t(e,i.default.renderer.memberCallReturn,r,u)}}),l.on(i.default.browser.memberGet,function(e,n,r,o,s){let c;try{a.info(`[MEMBER_GET]: remoteId=${o}, property=`,s);let n=h.get(o);null==n&&b(`Cannot get property '${Object.toString.call(s)}' on missing remote object ${o}`);let l=n[s];c=w(e.sender,l)}catch(e){c=y(e)}finally{t(e,i.default.renderer.memberGetReturn,r,c)}}),l.on(i.default.browser.memberSet,function(e,n,r,o,s,c){try{a.info(`[MEMBER_SET]: remoteId=${o}, property=`+s),c=_(e.sender,e.frameId,n,c);let l=h.get(o);null==l&&b(`Cannot set property '${Object.toString.call(s)}' on missing remote object ${o}`),l[s]=c[0],t(e,i.default.renderer.memberSetReturn,r,{type:"value",value:!0})}catch(n){t(e,i.default.renderer.memberSetReturn,r,y(n))}}),l.on(i.default.browser.memberConstruct,function(n,r,o,s,c,l){let u,d=null;try{a.info(`[MEMBER_CONSTRUCTOR]: regId=${s}, method=${c}`);let f=l.length>0?l[l.length-1]:null;l=_(n.sender,n.frameId,r,l);let m=h.get(s);null==m&&b(`Cannot call constructor '${c}' on missing remote object ${s}`),f&&"interest"===f.type&&(d=l.pop());let p=m[c],g=new(Function.prototype.bind.apply(p,[null,...l]));g&&d&&e(g,d),u=w(n.sender,g)}catch(e){u=y(e)}finally{t(n,i.default.renderer.memberConstructReturn,o,u)}}),l.on(i.default.browser.sync,function(e,n,r,o){let s=h.get(o);t(e,i.default.renderer.syncReturn,r,w(e.sender,s))}),l.on("ELECTRON_BROWSER_DEREFERENCE",function(e,t){let n=h.get(t);if(r.ipcMain.emit("log_to_renderer","ELECTRON_BROWSER_DEREFERENCE",t,typeof n),n){let r=n.name;r||(r=n.constructor?n.constructor.name:""),h.remove(e.sender.id,t)}else t<0&&a.warn("remote id reference to nothing:",t)}),l.on(i.default.browser.contextRelease,e=>{h.clear(e.sender.id)})},e.getObjectRegistry=function(){return h}}(C||(C={})),e.exports=C},571:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"search-window"}},[""===e.keyWord?n("div",{staticClass:"xly-search-drop"},[e.filterRecomends.length>0?n("div",{staticClass:"xly-search-1"},[e._m(0),e._v(" "),n("ul",{staticClass:"xly-search-1__items"},[e._l(e.filterRecomends,function(t,r){return n("li",{key:r,staticClass:"xly-search-1__item",class:{"is-chosen":e.selectIndex===r},on:{click:function(t){e.openRecommend(r)}}},[n("span",{staticClass:"xly-search-1__number"},[e._v(e._s(r+1))]),e._v(" "),n("span",{staticClass:"xly-search-1__title"},[e._v(e._s(t.text))])])}),e._v(" "),n("li",{directives:[{name:"show",rawName:"v-show",value:e.showVipItem,expression:"showVipItem"}],staticClass:"xly-search-1__item is-vip",on:{click:e.handleVipItemClick}},[n("span",{directives:[{name:"show",rawName:"v-show",value:e.showVipItemIcon,expression:"showVipItemIcon"}],staticClass:"xly-search-1__number"},[n("img",{attrs:{src:e.vipItemIcon},on:{load:e.onVipItemIconLoad}})]),e._v(" "),n("span",{directives:[{name:"show",rawName:"v-show",value:!e.showVipItemIcon,expression:"!showVipItemIcon"}],staticClass:"xly-search-1__number"},[e._v(e._s(e.filterRecomends.length+1))]),e._v(" "),n("span",{staticClass:"xly-search-1__title",style:{color:e.vipItemColor}},[e._v(e._s(e.vipItemText))])])],2)]):e._e(),e._v(" "),n("div",{staticClass:"xly-search-1"},[n("div",{staticClass:"xly-search-drop__title"},[n("h2",[e._v("常用网址")]),e._v(" "),e.commonWebsites.length>0?n("a",{attrs:{href:"javascript:;"},on:{click:e.clearCommonWebsite}},[e._v("清空")]):e._e()]),e._v(" "),n("ul",{staticClass:"xly-search-2__items"},e._l(e.filterCommonWebSites,function(t,r){return n("li",{key:r,staticClass:"xly-search-2__item",class:{"is-chosen":e.selectIndexOfCommonWebSites===r},on:{click:function(n){e.openCommonWebsite(t)}}},[t.iconUrl?n("div",{staticClass:"xly-search-2__icon",class:{"is-fixed":t.isRecommend}},[n("img",{attrs:{src:t.iconUrl,alt:t.name}}),e._v(" "),t.isRecommend?e._e():n("i",{staticClass:"xly-icon-close-search",on:{click:function(n){n.stopPropagation(),e.deleteCommonWebsite(t)}}})]):n("div",{staticClass:"xly-search-2__icon",class:e.getTypeIconClass(r)},[e._v(e._s(e.getUrlFirstChar(t.url))+"\n            "),n("i",{staticClass:"xly-icon-close-search",on:{click:function(n){n.stopPropagation(),e.deleteCommonWebsite(t)}}})]),e._v(" "),n("p",{staticClass:"xly-search-2__title"},[e._v(e._s(t.name||t.url))])])}))]),e._v(" "),e.filterLatestSearchKeywords.length>0?n("div",{staticClass:"xly-search-3"},[n("div",{staticClass:"xly-search-drop__title"},[n("h2",[e._v("最近搜索")]),e._v(" "),n("a",{attrs:{href:"javascript:;"},on:{click:e.clearSearchKeyword}},[e._v("清空")])]),e._v(" "),n("ul",{staticClass:"xly-search-3__items"},e._l(e.filterLatestSearchKeywords,function(t,r){return n("li",{key:r,staticClass:"xly-search-3__item",class:{"is-chosen":e.selectIndexOflatestSearchKeywords===r},on:{click:function(t){e.openLastestSearch(r)}}},[n("div",{staticClass:"xly-search-3__icon"},[t.url?n("i",{staticClass:"xly-icon-net"}):n("i",{staticClass:"xly-icon-search-1"})]),e._v(" "),n("p",{staticClass:"xly-search-3__title"},[e._v(e._s(t.url||t.keyword))]),e._v(" "),n("i",{staticClass:"xly-icon-close-search",on:{click:function(t){t.stopPropagation(),e.deleteSearchKeyword(r)}}})])}))]):e._e(),e._v(" "),e.movieConfig.yingyuan_search_wait&&e.filterRecommendList.length>0?n("div",{staticClass:"xly-search-6"},[n("div",{staticClass:"xly-search-drop__title"},[n("h2",[e._v("影视热榜")]),e._v(" "),n("a",{attrs:{href:"javascript:;"},on:{click:function(t){e.showMovieHomePage("search_wait_page_vip_special_more_click","v_pc_xlx_ggong_search_wait_vipsp")}}},[e._v("更多 "),n("i",{staticClass:"xly-icon-arrow-right-1"})])]),e._v(" "),n("ul",{staticClass:"xly-search-6__items"},e._l(e.filterRecommendList,function(t,r){return n("li",{key:t.xl_id||t.ctime,staticClass:"xly-search-6__item",attrs:{title:t.work_name||""},on:{click:function(n){e.openMoviePage(t.player_site,r,"v_pc_xlx_ggong_search_wait_vipsp")}}},[n("span",{staticClass:"xly-search-6__number"},[e._v(e._s(r+1))]),e._v(" "),n("div",{staticClass:"xly-search-6__image"},[n("img",{attrs:{src:t.hor_cover||t.cover||"",alt:t.work_name||""}})]),e._v(" "),n("p",{staticClass:"xly-search-6__title"},[e._v(e._s(t.work_name||""))])])}))]):e._e()]):e.isShowMore?n("div",{staticClass:"xly-search-drop"},["download"===e.showPanel?n("task-item-panel-more",{attrs:{keyword:e.keyWord,taskIds:e.matchDownloadTasks,select:e.selectIndexOfMatchDownloadTasks},on:{showMore:function(t){e.onClickShowMore("")}}}):n("pan-task-item-panel-more",{attrs:{panelText:e.panPanelText,keyword:e.keyWord,tasks:e.matchPanTasks,select:e.selectIndexOfMatchPanTasks},on:{showMore:function(t){e.onClickShowMore("")}}})],1):n("div",{staticClass:"xly-search-drop"},[n("div",{staticClass:"xly-search-4"},[n("ul",[e._l(e.matchBookMarksOrHistorys,function(t,r){return n("li",{key:r,staticClass:"xly-search-4__item",class:{"is-chosen":e.selectIndex===r},on:{click:function(n){e.openHistoryOrBookMarkSite(t.url)}}},[n("i",{class:e.isMatchBookMarks?"xly-icon-fav":"xly-icon-history"}),e._v(" "),n("div",{staticClass:"xly-search-4__content"},[t.name?n("p",{staticClass:"title"},[e._v("\n                "+e._s(t.name)+"\n            ")]):e._e(),e._v(" "),n("p",{staticClass:"link"},[e._v("\n                "+e._s(t.url)+"\n            ")])]),e._v(" "),n("span",{staticClass:"xly-search-4__type"},[e._v(e._s(e.isMatchBookMarks?"收藏夹":"历史记录"))])])}),e._v(" "),e._l(e.filteredRecomendKeywordSites,function(t,r){return n("li",{key:t.url,staticClass:"xly-search-4__item",class:{"is-chosen":e.selectIndexOfRecomendKeywordSites===r},on:{click:function(t){e.openSite(r)}}},[n("i",{staticClass:"xly-icon-search-1"}),e._v(" "),n("div",{staticClass:"xly-search-4__content"},[n("p",{staticClass:"title"},[e._v("\n                "+e._s(t.text)+"\n            ")]),e._v(" "),e.relatedKeyWord&&!t.text.includes(e.relatedKeyWord)?n("span",[e._v(e._s(e.relatedKeyWord))]):e._e()])])})],2)]),e._v(" "),n("div",{staticClass:"xly-search-5"},[0===e.matchDownloadTasks.length&&0===e.matchPanTasks.length?n("div",{staticClass:"xly-search-5__header"},[e.isSearchingLocal||e.isSearchingPan?n("h2",{staticClass:"is-loading"},[n("i",{staticClass:"xly-icon-loading"}),e._v(" "),n("p",[e._v("正在搜索本地/云盘文件...")])]):e.isSearchingLocal||e.isSearchingPan?e._e():n("h2",{staticClass:"is-loading"},[n("p",[e._v("没有搜索到相关文件")])])]):e._e(),e._v(" "),0===e.matchDownloadTasks.length||e.isInSafeSpace?e._e():n("task-item-panel",{attrs:{keyword:e.keyWord,taskIds:e.matchDownloadTasks,select:e.selectIndexOfMatchDownloadTasks},on:{showMore:e.onClickShowMore}}),e._v(" "),0!==e.matchPanTasks.length?n("pan-task-item-panel",{attrs:{panelText:e.panPanelText,keyword:e.keyWord,tasks:e.matchPanTasks,select:e.selectIndexOfMatchPanTasks},on:{showMore:e.onClickShowMore}}):e._e()],1),e._v(" "),!e.isInSafeSpace&&e.movieConfig.yingyuan_search_think&&e.searchMovieList.length>0?n("div",{staticClass:"xly-search-5"},[n("div",{staticClass:"xly-search-5__header"},[n("h2",[e._v(e._s("影院("+e.searchMovieList.length+")"))]),e._v(" "),n("a",{staticClass:"xly-search-drop__link",attrs:{href:"javascript:;"},on:{click:function(t){e.showMovieHomePage("search_association_page_vip_special_more_click","v_pc_xlx_ggong_search_asso_vipsp")}}},[e._v("更多 "),n("i",{staticClass:"xly-icon-arrow-right-1"})])]),e._v(" "),n("ul",{staticClass:"xly-search-recommend"},e._l(e.searchMovieList,function(t,r){return n("li",{key:t.xl_id||t.ctime,staticClass:"xly-search-recommend__item",on:{click:function(n){e.openMoviePage(t.player_site,r,"v_pc_xlx_ggong_search_asso_vipsp")}}},[n("div",{staticClass:"xly-search-recommend__image"},[n("img",{attrs:{src:t.hor_cover||t.cover||"",alt:t.work_name||""}})]),e._v(" "),n("p",{staticClass:"xly-search-recommend__text",domProps:{innerHTML:e._s(e.formatedTitle(t.work_name))}})])}))]):e._e()])])},o=[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"xly-search-drop__title"},[t("h2",[this._v("大家都在搜")])])}];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o})},58:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(13),o=-1*Math.pow(2,31),i=process.electronBinding("v8_util");t.default=class{constructor(){this.nextId=0,this.storage=new Map,this.owners=new Map}add(e,t){const n=this.saveToStorage(t),r=e.id;let o=this.owners.get(r);return o||(o=new Set,this.owners.set(r,o),this.registerDeleteListener(e,r)),o.has(n)||(o.add(n),this.storage.get(n).count++),n}getIdOfObject(e){return i.getHiddenValue(e,"__remote_id__")}get(e){const t=this.storage.get(e);if(void 0!==t)return t.object}remove(e,t){this.dereference(t);let n=this.owners.get(e);n&&n.delete(t)}clear(e){let t=this.owners.get(e);if(t){for(let e of t)this.dereference(e);this.owners.delete(e)}}getStorageSize(){return this.storage.size}saveToStorage(e){let t=i.getHiddenValue(e,"__remote_id__");if(!t){if((t=--this.nextId)<=o)throw new Error("object registry id overflow");this.storage.set(t,{object:e,count:0}),i.setHiddenValue(e,"__remote_id__",t)}return t}dereference(e){let t=this.storage.get(e);null!=t&&(t.count-=1,0===t.count&&(i.deleteHiddenValue(t.object,"__remote_id__"),this.storage.delete(e)))}registerDeleteListener(e,t){const n=e.getProcessId(),o=(i,s)=>{s===n&&(r.info("render-view-deleted: processid="+n),(()=>{r.info("before clear. objectsRegistry capacity="+this.storage.size,"owners size:"+this.owners.size)})(),e.removeListener("render-view-deleted",o),this.clear(t))};e.on("render-view-deleted",o)}}},59:function(e,t,n){"use strict";var r;!function(e){e.propertiesOfFunction=["length","name","arguments","caller","prototype","apply","bind","call","toString"]}(r||(r={})),e.exports=r},6:function(e,t){e.exports=require("util")},60:function(e,t){e.exports=require("readline")},61:function(e,t,n){e.exports=n(9)(216)},62:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(7),i=n(18),s=n(4),a=n(32);!function(e){function t(e,t){return r(this,void 0,void 0,function*(){if(null!==e){let n=e.webContents;(yield n.isDevToolsOpened())?yield n.closeDevTools():yield n.openDevTools(t)}})}e.openDevTool=t,e.enableDevTools=function(e){return r(this,void 0,void 0,function*(){window.addEventListener("keyup",n=>r(this,void 0,void 0,function*(){if("F12"===n.key&&n.ctrlKey)a.DevEnvHelperNS.isLogEnable()&&(yield t(yield o.asyncRemoteCall.getCurrentWindow(),e));else if(("t"===n.key||"T"===n.key)&&n.altKey&&a.DevEnvHelperNS.isLogEnable()){let e=document.getElementById("DevProcessPid");if(e)document.body.removeChild(e);else{(e=document.createElement("p")).id="DevProcessPid",e.style.position="absolute",e.style.left="0px",e.style.top="0px",e.style.width="100%",e.style.zIndex="10000",e.style.color="rgb(255,0,0)",document.body.appendChild(e);let t="process.pid:"+process.pid;t+="\r\nlocation.href:"+location.href,t+="\r\nprocess.argv:"+process.argv,e.innerText=t}}}),!0)})},e.enableDragOpenFile=function(e){void 0===e&&(e=!1),document.addEventListener("dragover",e=>(e.preventDefault(),e.stopPropagation(),!1),!0),document.addEventListener("drop",e=>r(this,void 0,void 0,function*(){e.preventDefault(),e.stopPropagation();let t=e.dataTransfer,n=t.files,r=t.items;if(void 0!==r&&null!==r&&r.length>0)for(let e=0;e<r.length;e++){let t=r[e];"string"===t.kind&&"text/uri-list"===t.type?t.getAsString(e=>{s.client.callServerFunction("DropOpenUrl",e).catch()}):t.kind}if(void 0!==n&&null!==n&&n.length>0)for(let e=0;e<n.length;e++){let t=n[e].path;void 0!==t&&null!==t&&""!==t&&(yield i.FileSystemAWNS.existsAW(t))&&s.client.callServerFunction("DropOpenFile",t).catch()}return!1}),!0)}}(t.ThunderToolsNS||(t.ThunderToolsNS={}))},63:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(7),i=n(1),s=n(16),a=i.default.getLogger("MenuSkinNS");!function(e){e.setStyle=function(e,t){return r(this,void 0,void 0,function*(){if(a.information("setStyle",e,t),null!==e){const{WindowPreferenceNS:e}=yield Promise.resolve().then(()=>n(73));let t={windowPreference:e.getWindowPreference()};a.information("skinOpts",t)}})},e.popEditableDefaultContextMenu=function(e,t,n){return r(this,void 0,void 0,function*(){let n=yield o.asyncRemoteCall.getCurrentWebContents();n.once("context-menu",(i,c)=>r(this,void 0,void 0,function*(){if(a.verbose(i),c.isEditable){let r=[{label:"撤销",enabled:c.editFlags.canUndo,click:()=>{n.undo()}},{type:"separator"},{label:"剪切",enabled:c.editFlags.canCut,click:()=>{n.cut()}},{label:"复制",enabled:c.editFlags.canCopy,click:()=>{n.copy()}},{label:"粘贴",enabled:c.editFlags.canPaste&&s.ThunderUtil.isClipboardTextFormatAvailable(),click:()=>{n.paste()}},{label:"删除",enabled:c.editFlags.canDelete,click:()=>{n.delete()}},{type:"separator"},{label:"全选",enabled:c.editFlags.canSelectAll,click:()=>{n.selectAll()}}];if(void 0!==e&&"function"==typeof e){let n=e(c);void 0!==n&&n.length>0&&(void 0===t?t=r.length:(t<0&&(t=r.length+1+t)<0&&(t=0),t>r.length&&(t=r.length)),r.splice(t,0,...n))}let i=yield(yield o.asyncRemoteCall.getMenu()).buildFromTemplate(r),a=yield o.asyncRemoteCall.getCurrentWindow();yield i.popup({window:a})}}))})}}(t.MenuSkinNS||(t.MenuSkinNS={}))},633:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"xly-search-5__header"},[n("h2",[e._v(e._s(e.headerText))]),e._v(" "),e.taskIds.length>3?n("a",{staticClass:"xly-search-drop__link",attrs:{href:"javascript:;"},on:{click:e.showMore}},[e._v("更多 "),n("i",{staticClass:"xly-icon-arrow-right-1"})]):e._e()]),e._v(" "),n("ul",{staticClass:"xly-search-5__items"},e._l(e.filteredTaskIds,function(t,r){return n("task-item",{key:t,class:{"is-chosen":e.select===r},attrs:{taskId:t,keyword:e.keyword}})}))])},o=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o})},634:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"xly-search-drop__header"},[n("a",{staticClass:"xly-search-drop__back xly-search-drop__link",attrs:{href:"javascript:;"},on:{click:e.showMore}},[n("i",{staticClass:"xly-icon-arrow-left"}),e._v(" 返回")]),e._v(" "),n("h2",[e._v(e._s(e.headerText))])]),e._v(" "),n("div",{staticClass:"xly-search-5"},[n("ul",{staticClass:"xly-search-5__items"},e._l(e.taskIds,function(t,r){return n("task-item",{key:t,class:{"is-chosen":e.select===r},attrs:{taskId:t,keyword:e.keyword,isChosen:e.select===r}})}))])])},o=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o})},635:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"xly-search-5__header"},[n("h2",[e._v(e._s(e.headerText))]),e._v(" "),e.tasks.length>3?n("a",{staticClass:"xly-search-drop__link",attrs:{href:"javascript:;"},on:{click:e.showMore}},[e._v("更多 "),n("i",{staticClass:"xly-icon-arrow-right-1"})]):e._e()]),e._v(" "),n("ul",{staticClass:"xly-search-5__items"},e._l(e.filteredTasks,function(t,r){return n("pan-task-item",{key:t.id,class:{"is-chosen":e.select===r},attrs:{task:t,keyword:e.keyword}})}))])},o=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o})},636:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"xly-search-drop__header"},[n("a",{staticClass:"xly-search-drop__back xly-search-drop__link",attrs:{href:"javascript:;"},on:{click:e.showMore}},[n("i",{staticClass:"xly-icon-arrow-left"}),e._v(" 返回")]),e._v(" "),n("h2",[e._v(e._s(e.headerText))])]),e._v(" "),n("div",{staticClass:"xly-search-5"},[n("ul",{staticClass:"xly-search-5__items"},e._l(e.tasks,function(t,r){return n("pan-task-item",{key:t.id,class:{"is-chosen":e.select===r},attrs:{task:t,keyword:e.keyword,isChosen:e.select===r}})}))])])},o=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o})},64:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.getTextScale=function(){return 1},e.fixWindowSize=function(e,t){return r(this,void 0,void 0,function*(){window.resizeTo(e,t)})},e.autoFixWindowSize=function(){},e.fixZoomFactory=function(){}}(t.FixTextScale||(t.FixTextScale={}))},646:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"xly-search-5__item",on:{click:e.onSelectTask}},[n("i",{staticClass:"xly-icon-type",class:e.iconType}),e._v(" "),n("div",{staticClass:"xly-search-5__content"},[n("p",{staticClass:"xly-search-5__title",domProps:{innerHTML:e._s(e.formatedTitle)}}),e._v(" "),n("div",{staticClass:"xly-search-5__info"},[n("p",{staticClass:"size"},[e._v(e._s(e.formatSize))]),e._v(" "),n("p",{staticClass:"time"},[e._v(e._s(e.formatTime))])])])])},o=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o})},647:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"xly-search-5__item",on:{click:e.onSelectTask}},[n("i",{staticClass:"xly-icon-type",class:e.iconType}),e._v(" "),n("div",{staticClass:"xly-search-5__content"},[n("p",{staticClass:"xly-search-5__title",domProps:{innerHTML:e._s(e.formatedTitle)}}),e._v(" "),n("div",{staticClass:"xly-search-5__info"},[e.isFile?n("p",{staticClass:"size"},[e._v(e._s(e.formatSize))]):e._e(),e._v(" "),n("p",{staticClass:"time"},[e._v(e._s(e.formatTime))])])])])},o=[];r._withStripped=!0,n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o})},65:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(4),o=n(1).default.getLogger("common/skin"),i=!0;let s=null;function a(e){if(!i||null===e||void 0===e)return;let t=localStorage.getItem("skin_body_classes");s&&s.classes===e.classes||(localStorage.removeItem("skin_body_classes"),e&&e.classes&&(document.body.classList.add(e.classes),localStorage.setItem("skin_body_classes",e.classes)),s&&s.classes?document.body.classList.remove(s.classes):t!==e.classes&&document.body.classList.remove(t),s=Object.freeze(Object.assign({},e)))}r.client.callServerFunction("GetSkinInfo").then(a).catch(e=>{o.warning(e)}),r.client.attachServerEvent("OnChangeSkin",(e,...t)=>{a(t[0])})},66:function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},67:function(e,t,n){"use strict";var r=n(46);e.exports=function(e,t,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},68:function(e,t,n){"use strict";e.exports=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e}},69:function(e,t,n){"use strict";var r=n(10);function o(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var i;if(n)i=n(t);else if(r.isURLSearchParams(t))i=t.toString();else{var s=[];r.forEach(t,function(e,t){null!==e&&void 0!==e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),s.push(o(t)+"="+o(e))}))}),i=s.join("&")}return i&&(e+=(-1===e.indexOf("?")?"?":"&")+i),e}},691:function(e,t,n){"use strict";n.r(t);var r=n(646),o=n(479);for(var i in o)"default"!==i&&function(e){n.d(t,e,function(){return o[e]})}(i);var s=n(0),a=Object(s.a)(o.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\search-renderer\\views\\task-item.vue",t.default=a.exports},692:function(e,t,n){"use strict";n.r(t);var r=n(647),o=n(485);for(var i in o)"default"!==i&&function(e){n.d(t,e,function(){return o[e]})}(i);var s=n(0),a=Object(s.a)(o.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\search-renderer\\views\\pan-task-item.vue",t.default=a.exports},7:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(1).default.getLogger("async-remote-call"),i=n(42),s=n(12),a=n(6);t.asyncRemoteCall=new class extends s.EventEmitter{constructor(){super(),this.mapObj=new Map,this.mapObjIniting=new Map,"renderer"!==process.type&&o.warning('can not import "renderer-process-call" module in non-renderer process',process.type)}getAppName(){return r(this,void 0,void 0,function*(){if(void 0===this.appName){let e=yield this.getApp();this.appName=yield e.getName()}return this.appName})}getAppVersion(){return r(this,void 0,void 0,function*(){if(void 0===this.appVersion){let e=yield this.getApp();this.appVersion=yield e.getVersion()}return this.appVersion})}getProcess(){return r(this,void 0,void 0,function*(){return i.global.process.__resolve()})}getIpcMain(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("ipcMain")})}getDialog(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("dialog")})}getApp(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("app")})}getShell(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("shell")})}getMenu(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("Menu")})}getScreen(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("screen")})}getBrowserWindow(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("BrowserWindow")})}getWebContents(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("webContents")})}getGlobalShortcut(){return r(this,void 0,void 0,function*(){return this.getCurrentObject("globalShortcut")})}getCurrentWebContents(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWebContents");return void 0===e&&(this.mapObjIniting.get("currentWebContents")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWebContents",t=>{e(t)})})):(this.mapObjIniting.set("currentWebContents",!0),e=yield i.getCurrentWebContents().__resolve(),this.mapObjIniting.set("currentWebContents",!1),this.emit("OnInitCurrentWebContents",e),this.listeners("OnInitCurrentWebContents").forEach(e=>{this.removeListener("OnInitCurrentWebContents",e)})),this.mapObj.set("currentWebContents",e)),e})}getCurrentWindow(){return r(this,void 0,void 0,function*(){let e=this.mapObj.get("currentWindow");return void 0===e&&(this.mapObjIniting.get("currentWindow")?e=yield new Promise(e=>r(this,void 0,void 0,function*(){this.on("OnInitCurrentWindow",t=>{e(t)})})):(this.mapObjIniting.set("currentWindow",!0),e=yield i.getCurrentWindow().__resolve(),this.mapObjIniting.set("currentWindow",!1),this.emit("OnInitCurrentWindow",e),this.listeners("OnInitCurrentWindow").forEach(e=>{this.removeListener("OnInitCurrentWindow",e)})),this.mapObj.set("currentWindow",e)),e})}getCurrentObject(e){return r(this,void 0,void 0,function*(){let t=this.mapObj.get(e);return a.isNullOrUndefined(t)&&(this.mapObjIniting.get(e)?t=yield new Promise(t=>r(this,void 0,void 0,function*(){this.on(e,e=>{t(e)})})):(this.mapObjIniting.set(e,!0),t=yield i.electron[e].__resolve(),this.mapObjIniting.set(e,!1),this.emit(e,t),this.listeners(e).forEach(t=>{this.removeListener(e,t)})),this.mapObj.set(e,t)),t})}}},70:function(e,t,n){var r=n(34),o=n(44),i=n(40),s=n(83),a=n(75).Writable,c=n(124)("follow-redirects"),l={GET:!0,HEAD:!0,OPTIONS:!0,TRACE:!0},u=Object.create(null);function d(e,t){a.call(this),e.headers=e.headers||{},this._options=e,this._redirectCount=0,this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var n=this;if(this._onNativeResponse=function(e){n._processResponse(e)},!e.pathname&&e.path){var r=e.path.indexOf("?");r<0?e.pathname=e.path:(e.pathname=e.path.substring(0,r),e.search=e.path.substring(r))}this._performRequest()}function h(e){var t={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(e).forEach(function(o){var i=o+":",a=n[i]=e[o],l=t[o]=Object.create(a);l.request=function(e,o){return"string"==typeof e?(e=r.parse(e)).maxRedirects=t.maxRedirects:e=Object.assign({protocol:i,maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},e),e.nativeProtocols=n,s.equal(e.protocol,i,"protocol mismatch"),c("options",e),new d(e,o)},l.get=function(e,t){var n=l.request(e,t);return n.end(),n}}),t}["abort","aborted","error","socket","timeout"].forEach(function(e){u[e]=function(t){this._redirectable.emit(e,t)}}),d.prototype=Object.create(a.prototype),d.prototype.write=function(e,t,n){this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,n)):(this.emit("error",new Error("Request body larger than maxBodyLength limit")),this.abort())},d.prototype.end=function(e,t,n){var r=this._currentRequest;e?this.write(e,t,function(){r.end(null,null,n)}):r.end(null,null,n)},d.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)},d.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},["abort","flushHeaders","getHeader","setNoDelay","setSocketKeepAlive","setTimeout"].forEach(function(e){d.prototype[e]=function(t,n){return this._currentRequest[e](t,n)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(d.prototype,e,{get:function(){return this._currentRequest[e]}})}),d.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(this._options.agents){var n=e.substr(0,e.length-1);this._options.agent=this._options.agents[n]}var o=this._currentRequest=t.request(this._options,this._onNativeResponse);for(var i in this._currentUrl=r.format(this._options),o._redirectable=this,u)i&&o.on(i,u[i]);if(this._isRedirect){var s=this._requestBodyBuffers;!function e(){if(0!==s.length){var t=s.pop();o.write(t.data,t.encoding,e)}else o.end()}()}},d.prototype._processResponse=function(e){var t=e.headers.location;if(t&&!1!==this._options.followRedirects&&e.statusCode>=300&&e.statusCode<400){if(++this._redirectCount>this._options.maxRedirects)return void this.emit("error",new Error("Max redirects exceeded."));var n,o=this._options.headers;if(307!==e.statusCode&&!(this._options.method in l))for(n in this._options.method="GET",this._requestBodyBuffers=[],o)/^content-/i.test(n)&&delete o[n];if(!this._isRedirect)for(n in o)/^host$/i.test(n)&&delete o[n];var i=r.resolve(this._currentUrl,t);c("redirecting to",i),Object.assign(this._options,r.parse(i)),this._isRedirect=!0,this._performRequest()}else e.responseUrl=this._currentUrl,this.emit("response",e),this._requestBodyBuffers=[]},e.exports=h({http:o,https:i}),e.exports.wrap=h},71:function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},72:function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},73:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(80),o=n(74);!function(e){e.getWindowPreference=function(e=!1){let t=o.default(),n={};return t&&t.colors&&"string"==typeof t.colors.colorPrimaryControl1&&(n.hoverBackgroundColor=e?parseInt(r.ColorUtilNS.rgbaStringToHexWith0xBegin(t.colors.colorPrimaryControl1),16):r.ColorUtilNS.rgbaStringToHexWith0xBegin(t.colors.colorPrimaryControl1)),n}}(t.WindowPreferenceNS||(t.WindowPreferenceNS={}))},74:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(3),i=n(1),s=n(4),a=i.default.getLogger("GetSkinInfo");let c;(function(){return r(this,void 0,void 0,function*(){"renderer"===process.type?(a.information("renderer process"),s.client.callServerFunction("GetSkinInfo").then(e=>{c=e,a.information("send OnChangeSkin",e)}).catch(e=>{a.warning(e)}),s.client.attachServerEvent("OnChangeSkin",(e,t)=>{c=t,a.information("send OnChangeSkin",t)})):"browser"===process.type&&(a.information("main process"),o.ipcMain.on("OnChangeSkin",(e,t)=>{a.information("OnChangeSkin",t),c=t}))})})().catch(e=>{a.information(e)}),t.default=function(){return c}},75:function(e,t){e.exports=require("stream")},8:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return require(e)}},80:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){function t(e){let t=e.toString(16).toUpperCase();return t.length<2&&(t="0"+t),t}function n(e,n,r,o){return"0x"+t(o)+t(e)+t(n)+t(r)}e.rgbaStringToHexWith0xBegin=function(e){if(void 0!==e){let t=e.split(",");return n(parseInt(t[0]||"0",10),parseInt(t[1]||"0",10),parseInt(t[2]||"0",10),parseInt(t[3]||"255",10))}},e.colorNumberToHex=t,e.rgbaToHexWith0xBegin=n}(t.ColorUtilNS||(t.ColorUtilNS={}))},81:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(39),i=n(21),s=n(18),a=n(1).default.getLogger("Thunder.base.tools-utilities"),c=n(2),l=n(8).default(c.join(__rootDir,"../bin/ThunderHelper.node"));!function(e){function t(e){return r(this,void 0,void 0,function*(){let t;return t=e&&(yield s.FileSystemAWNS.existsAW(e))?new Promise(t=>{l.asyncCalcuteFileMD5(e,(e,n)=>{e?(n=n.toUpperCase(),t(n)):t(void 0)})}):new Promise(e=>{e(void 0)})})}e.genarateMd5=function(e){let t=void 0,n=o.createHash("md5");return null!==n&&(t=n.update(e).digest("hex")),t},e.matchFileMd5=function(e,n){return r(this,void 0,void 0,function*(){let r=!1,o=yield t(e);return void 0!==o&&o===n.toUpperCase()&&(a.information("check full md5 sucessful"),r=!0),r})},e.calculateFileMd5Ex=function(e){return r(this,void 0,void 0,function*(){let t;if(e&&(yield s.FileSystemAWNS.existsAW(e))){let n=i.createReadStream(e),r=o.createHash("md5");n.on("data",e=>{r.update(e)}),t=new Promise(e=>{n.on("end",()=>{let t=r.digest("hex");t=t.toUpperCase(),e(t)})})}else t=new Promise(e=>{e(void 0)});return t})},e.calculateFileMd5=t,e.encryptBuffer=function(e,t){let n=null;try{let r=o.createCipheriv("aes-128-ecb",t,""),i=r.update(e),s=r.final();n=Buffer.concat([i,s])}catch(e){a.warning("encryptBuffer",e)}return n},e.decryptBuffer=function(e,t){let n=null;try{let r=o.createDecipheriv("aes-128-ecb",t,""),i=r.update(e),s=r.final();n=Buffer.concat([i,s])}catch(e){a.warning("decryptBuffer",e)}return n},e.encryptSha1Buffer=function(e){let t=null;try{t=o.createHash("sha1").update(e).digest("hex")}catch(e){a.warning("encryptSha1Buffer",e)}return t},e.encryptHmacBuffer=function(e,t,n,r="hex"){let i=null;try{i=o.createHmac(e,t).update(n,"utf8").digest(r)}catch(e){a.warning("encryptSha1Buffer",e)}return i},e.setForegroundWindow=function(e,t){return r(this,void 0,void 0,function*(){if(null!==e&&null!==t){let n=void 0,o=[];"renderer"===process.type?(n=yield t.getNativeWindowHandle(),o=yield t.getChildWindows()):(n=t.getNativeWindowHandle(),o=t.getChildWindows());let i=[];for(let e=0;e<o.length;++e){let t=o[e],n=!1;"renderer"===process.type?(yield t.isDestroyed())||(n=yield t.isAlwaysOnTop()):t.isDestroyed()||(n=t.isAlwaysOnTop()),n&&i.push(t)}let s=n.readUIntLE(0,4);e.setForegroundWindow(s)?a.information("setForegroundWindow sucessful"):a.information("setForegroundWindow failed"),i.forEach(e=>r(this,void 0,void 0,function*(){"renderer"===process.type?(yield e.isDestroyed())||e.setAlwaysOnTop(!0).catch():e.isDestroyed()||e.setAlwaysOnTop(!0)}))}})}}(t.ToolsUtilitiesAWNS||(t.ToolsUtilitiesAWNS={}))},83:function(e,t){e.exports=require("assert")},9:function(e,t){e.exports=vendor_0aff229d1d3a2d2be355},91:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){function t(e){const t=new Date(+e);return"Invalid Date"===t.toString()?"":`${t.getFullYear()}-${o(t.getMonth()+1,2)}-${o(t.getDate(),2)} ${o(t.getHours(),2)}:${o(t.getMinutes(),2)}`}function n(e){let t=new Date(+e);return"Invalid Date"===t.toString()?"":o(t.getHours(),2)+":"+o(t.getMinutes(),2)}function r(e,t){return((e=new Date(e).setHours(0,0,0,0))-(t=new Date(t).setHours(0,0,0,0)))/1e3/60/60/24}function o(e,t){let n=e.toString().length,r=e.toString();for(;n<t;)r="0"+r,n++;return r}e.formatDate=function(e,t){/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length)));let n={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds()};for(let e in n){let r=n[e]+"";new RegExp(`(${e})`).test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?r:("00"+(r=r)).substr(r.length)))}var r;if(/(f+)/.test(t)){let n=e.getMilliseconds()+"";t=t.replace(RegExp.$1,("000"+n).substr(n.length))}return t},e.getPubTime=function(e){const i=new Date(+e),s=(new Date).getTime(),a=s-1*e,c=r(s,i.getTime());let l="";if(a<36e5){let e=Math.floor(a/6e4);l=e>=1?e+"分钟前":"刚刚"}else l=a<864e5?Math.floor(a/36e5)+"小时前":1===c?"昨天"+n(e):2===c?"前天"+n(e):i.getFullYear()===(new Date).getFullYear()?`${o(i.getMonth()+1,2)}-${o(i.getDate(),2)} ${o(i.getHours(),2)}:${o(i.getMinutes(),2)}`:t(e);return l},e.createTimeFormat=t,e.timeFormat=n,e.dateDiff=r,e.pad=o,e.formatSeconds=function(e){let t="";do{if(e<=0){t="00:00:00";break}let n=Math.floor(e/3600),r=Math.floor(e/60)%60,o=Math.floor(e%60);t=n<10?"0"+n+":":n+":",t+=r<10?"0"+r+":":r+":",t+=o<10?"0"+o:""+o}while(0);return t},e.formatSecondsCustom=function(e){let t="";do{if(e<=0){t="00:00:00";break}let n=Math.floor(e/3600),r=Math.floor(e/60)%60,o=Math.floor(e%60);if(n<=99)t=n<10?"0"+n+":":n+":",t+=r<10?"0"+r+":":r+":",t+=o<10?"0"+o:""+o;else{let n=Math.floor(e/86400);t=`剩${n=n>999?999:n}天`}}while(0);return t},e.convertTimeToMinutes=function(e,t,n){return 3600*e+60*t+n},e.convertMinuteToTime=function(e){return[Math.floor(e/3600),Math.floor(e/60%60),Math.floor(e%60)]}}(t.TimeHelperNS||(t.TimeHelperNS={}))},92:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new n(function(t){t(e.value)}).then(s,a)}c((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0});const o=n(2),i=n(15),s=n(25),a=n(1);let c=null;const l=n(4),u=a.default.getLogger("url.helper");!function(e){let t=!1,a=["txt","url","nfo","lnk"];function d(e){let t=!0;if("string"==typeof e)if(40===e.length||32===e.length){for(let n=0;n<e.length;n++)if(e.charCodeAt(n)>127){t=!1;break}}else t=!1;else t=!1;return t}function h(e,t=!0){t=void 0===t||t;let n=!1;do{if("string"!=typeof e||""===e)break;let r=e.trim();if(""===r)break;if(s.ThunderHelper.getTaskTypeFromUrl(r)===i.DownloadKernel.TaskType.Unkown){if(!t)break;d(r)&&(n=!0)}else n=!0;n||r.match(/^file:\/\/\//)&&(n=!0)}while(0);return n}e.isMagnetCode=d,e.fixMagnetUrl=function(e){let t="";do{if("string"!=typeof e||""===e)break;if(""===(t=e.trim()))break;s.ThunderHelper.getTaskTypeFromUrl(t)===i.DownloadKernel.TaskType.Unkown&&d(t)&&(t="magnet:?xt=urn:btih:"+t)}while(0);return t},e.isUrlValid=h,e.isBirdKey=function(e){return e?e.match(/#[Xx]([0-9a-zA-Z])+#/g):null},e.isYunFetchBackTask=function(e){return e?e.match(/#\$ThunderPanFetchBack:([\s\S]+)\$#/):null},e.isCustomProtocol=function(e){return r(this,void 0,void 0,function*(){let t=!1;null===c&&(c=yield Promise.resolve().then(()=>n(34)));let r=c.parse(e);do{if(null===r||void 0===r)break;let e=r.protocol;if("magnet:"===e){t=!0;break}if("thunder:"===e){t=!0;break}if("ed2k:"===e){t=!0;break}}while(0);return t})},e.isP2spOrEmuleUrl=function(e){let t=!1;do{if(null===e||void 0===e||""===e)break;let n=e.trim();if(""===n)break;let r=s.ThunderHelper.getTaskTypeFromUrl(n);if(r===i.DownloadKernel.TaskType.P2sp||r===i.DownloadKernel.TaskType.Emule){t=!0;break}}while(0);return t},e.isSupportUrl=function(e){e=e.toLowerCase();let t=!1;do{if("http://"===e.substr(0,"http://".length)){t=!0;break}if("https://"===e.substr(0,"https://".length)){t=!0;break}if("ftp://"===e.substr(0,"ftp://".length)){t=!0;break}if("ed2k://"===e.substr(0,"ed2k://".length)){t=!0;break}if("thunder://"===e.substr(0,"thunder://".length)){t=!0;break}if("magnet:?"===e.substr(0,"magnet:?".length)){t=!0;break}}while(0);return t};let f=".edu;.gov;.mil;.hk;.tw;.com.tw;.com.tw;.aero;.biz;.coop;.info;.museum;.name;.pro;";f+=".com;.cn;.xin;.net;.top;.xyz;.wang;.shop;.site;.club;.cc;.fun;.online;.biz;.red;.link;.ltd;.mobi;.info;.org;",f+=".com.cn;.net.cn;.org.cn;.gov.cn;.name;.vip;.work;.tv;.co;.kim;.group;.tech;.store;.ren;.ink;.pub;.live;.wiki;.design;";const m=(f+=".me").split(";");function p(e){let t=!1;do{if(void 0===e||null===e||""===e)break;if(-1!==m.indexOf(e)){t=!0;break}}while(0);return t}function w(e){let t=null;do{if(void 0===e||null===e)break;if(""===(e=e.trim()))break;let n=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(e);if(null===n)break;u.information("url parse result ",n),t={};let r=["url","scheme","slash","host","port","path","query","hash"];for(let e=0;e<r.length;e++)t[r[e]]=n[e];u.information("url parse",t)}while(0);return t}e.isUsualDomainSuffix=p,e.urlParse=w,e.isUrl=function(e){let t=!1,n=e;do{if(void 0===e||null===e)break;if(h(e,!1)){t=!0;break}if(e.trim().match(/^file:\/\/\//)){t=!0;break}let r=w(e);if(null===r){u.information("url parse failed");break}if(void 0===r.host||null===r.host)break;if(void 0!==r.scheme){t=!0;break}let i=r.host.match(/^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/);if(i){if(u.information("url parse is ip",i),Number(i[1])<=0||Number(i[1])>255){t=!1;break}if(Number(i[2])<0||Number(i[2])>255){t=!1;break}if(Number(i[4])<0||Number(i[4])>255){t=!1;break}if(Number(i[4])<0||Number(i[4])>255){t=!1;break}t=!0,void 0===r.scheme&&void 0!==r.port&&(n=`http://${e}`);break}if(r.host.match(/\.{2,}/)){u.information("url parse has multi dot"),t=!1;break}let s=o.extname(r.host);if(""===s)break;if(p(s)){t=!0;break}}while(0);return u.information("url parse isUrl",t),{ret:t,suggest:n}},e.isFileNameValid=function(e){let t=!1;do{if(void 0===e)break;if(null===e)break;if(""===(e=e.trim()))break;if("."===e[0])break;if(e.match(/[\/\\"<>\?\*|]/))break;t=!0}while(0);return t},e.initBlackList=function(){return r(this,void 0,void 0,function*(){if(!t){let e=yield l.client.callServerFunction("GetConfigModules","SuffixBlackList",a);e&&(a=e,t=!0)}})},e.isSuffixNeedDownload=function(e){let t=!0;do{if(void 0===e)break;if(""===e||"."===e)break;if(e=e.toLowerCase(),-1!==a.indexOf(e)){t=!1;break}}while(0);return t}}(t.URLHelperNS||(t.URLHelperNS={}))},983:function(e,t,n){n(49),e.exports=n(984)},984:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(38);n(4).client.start({name:"searchRendererContext"}),r.CommonIPCRenderer.rendererCommunicator.initialize("searchRendererContext"),r.CommonIPCRenderer.rendererCommunicator.connect();const o=n(52),i=n(31);n(61);const s=n(985),a=n(62);n(65);const c=n(1).default.getLogger("SearchRenderer");o.PerformanceMonitorStatNS.init("search-renderer"),new i.default({components:{App:s.default},render:e=>e("app")}).$mount("#app"),a.ThunderToolsNS.enableDevTools().catch(e=>{c.warning(e)})},985:function(e,t,n){"use strict";n.r(t);var r=n(571),o=n(475);for(var i in o)"default"!==i&&function(e){n.d(t,e,function(){return o[e]})}(i);n(157),n(230),n(185),n(564),n(994);var s=n(0),a=Object(s.a)(o.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\search-renderer\\app.vue",t.default=a.exports},986:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(137),o=n(987).urls;!function(e){e.hasChineseCharacter=function(e){let t=!1,n=e.match(/[\u4E00-\u9FFF]/g);return null!==n&&n.length>0&&(t=!0),t},e.getMatchUrls=function(e){let t=[],n=e.toLowerCase();for(let r=0;r<o.length;r++){let i=o[r],s=i.url.indexOf(n,0);if(-1!==s?t.push({url:i.url,text:i.context,isSearch:!1}):-1!==(s=i.context.indexOf(e,0))&&t.push({url:i.url,text:i.context,isSearch:!1}),t.length>3)break}return t},e.getWords=function(e,t=4){return new Promise(n=>{r.fetchSlRes({url:"/searcher/associate_search_v2",data:{keyword:e,size:t}}).then(t=>{t.keyWord=e,n(t)}).catch()})},e.getShare=function(e,t=1,n=5,o="site"){return new Promise(i=>{r.fetchSlRes({url:"/xlppc.searcher.api/v1/pc/res_search",data:{keyword:e,page:t,pagelen:n,type:o}}).then(t=>{t.keyWord=e,i(t)}).catch()})},e.getMovie=function(e,t,n,o){return new Promise(i=>{r.fetchPCRes({url:"/tube/video2/lists",data:{name:encodeURIComponent(e),page:t,size:n,type:o},method:"post"}).then(t=>{t.keyWord=e,i(t)}).catch()})},e.getBlockFlag=function(){return r.fetchSlRes({url:"/ipsitter/api/pc_block",data:{}})}}(t.SearchNS||(t.SearchNS={}))},987:function(e){e.exports={urls:[{url:"www.baidu.com",context:"百度一下，你就知道"},{url:"www.sogou.com",context:"搜狗搜索引擎 - 上网从搜狗开始"},{url:"www.youdao.com",context:"有道首页"},{url:"www.bing.com",context:"微软必应搜索 - 全球搜索，有问必应"},{url:"www.google.com",context:"谷歌"},{url:"www.yahoo.com",context:"Yahoo"},{url:"www.so.com",context:"360搜索，SO靠谱"},{url:"www.qq.com",context:"腾讯首页"},{url:"www.sohu.com",context:"搜狐"},{url:"www.sina.com.cn",context:"新浪首页"},{url:"www.thepaper.cn",context:"澎湃新闻-专注时政与思想"},{url:"www.163.com",context:"网易"},{url:"www.toutiao.com",context:"今日头条》你关心的,才是头条! - TouTiao.com"},{url:"www.huxiu.com",context:"虎嗅网"},{url:"www.ifeng.com",context:"凤凰网"},{url:"tieba.baidu.com",context:"百度贴吧"},{url:"www.tianya.cn",context:"天涯社区_华人网上家园"},{url:"www.zhihu.com",context:"知乎 - 与世界分享你的知识、经验和见解"},{url:"www.youku.com",context:"优酷-中国领先视频网站,提供视频播放,视频发布,视频搜索 - 优酷视频"},{url:"www.tudou.com",context:"土豆_每个人都是生活的导演_在线视频观看,原创视频上传,海量视频搜索"},{url:"v.qq.com",context:"腾讯视频-中国领先的在线视频媒体平台,海量高清视频在线观看"},{url:"www.iqiyi.com",context:"爱奇艺-中国领先的视频门户，高清影视剧，网络视频在线观看"},{url:"tv.sohu.com",context:"搜狐视频-中国领先的综合视频网站,正版高清视频在线观看,原创视频上传,全网视频搜索"},{url:"www.acfun.cn",context:"AcFun弹幕视频网 - 认真你就输啦 (・ω・)ノ- ( ゜- ゜)つロ"},{url:"www.bilibili.com",context:"哔哩哔哩弹幕视频网 - ( ゜- ゜)つロ 乾杯~ - bilibili"},{url:"www.pptv.com",context:"PPTV聚力-始终和你同一频道,海量影视剧,高清体育直播,热点视频在线观看"},{url:"www.le.com",context:"乐视视频 - 乐视旗下专业的影视剧视频网站_高清视频在线观看"},{url:"www.cntv.cn",context:"央视网_世界就在眼前"},{url:"www.hupu.com/",context:"虎扑体育 - 你的体育全世界！"},{url:"sports.sina.com.cn",context:"新浪体育_新浪网"},{url:"sports.qq.com",context:"腾讯体育_腾讯网"},{url:"sports.sohu.com",context:"搜狐体育_搜狐网"},{url:"sports.163.com",context:"网易体育_有态度的体育门户"},{url:"sports.cntv.cn",context:"体育_央视网"},{url:"china.nba.com",context:"NBA中国官方网站"},{url:"www.titan24.com",context:"体坛网_体坛周报 足球周刊 扣篮 全体育官网"},{url:"www.zhibo8.cc",context:"直播吧-NBA直播丨NBA直播吧丨足球直播丨英超直播丨CCTV5在线直播丨CBA直播丨体育直播"},{url:"sports.ifeng.com",context:"凤凰体育_凤凰网"},{url:"www.lottery.gov.cn",context:"中国体彩网 - 体育彩票管理中心唯一指定官方网站"},{url:"www.xinhuanet.com/sports",context:"新华体育_新华网"},{url:"sports.youku.com/",context:"运动即生活 - 优酷体育频道 - 优酷视频"},{url:"sports.pptv.com",context:"体育频道-西甲-CBA-中超-亚冠-体育赛事视频24小时在线直播-PPTV聚力-始终和你同一频道"},{url:"www.lesports.com",context:"乐视体育–最专业的体育赛事平台"},{url:"www.xueqiu.com",context:"雪球"},{url:"finance.qq.com/",context:"财经频道_腾讯网"},{url:"finance.sina.com.cn/",context:"新浪财经_新浪网"},{url:"finance.163.com/",context:"网易财经-有态度的财经门户"},{url:"www.hexun.com",context:"和讯网-中国最大财经门户网站"},{url:"www.eastmoney.com",context:"东方财富网：中国财经第一门户"},{url:"www.stockstar.com",context:"证券之星_中国交易者第一门户_股票_基金_外汇_期货_黄金_行情"},{url:"finance.ifeng.com/",context:"凤凰财经-新主流财经门户-凤凰网"},{url:"www.cnfol.com",context:"中金在线首页：财经 _ 股票 _ 证券 _ 金融 _ 财经博客"},{url:"www.caijing.com.cn",context:"财经网 - CAIJING.COM.CN"},{url:"www.cnstock.com",context:"上海证券报·中国证券网-上市公司，财富管理，财经新闻，上证博客，网上营业部，中国资本市场信息披露平台"},{url:"www.jrj.com.cn",context:"金融界—投资者首选财经金融门户网站，以证券交易为核心的互联网综合理财平台"},{url:"fanyi.youdao.com",context:"在线翻译_有道"},{url:"fanyi.baidu.com",context:"百度翻译"},{url:"translate.google.cn",context:"谷歌翻译"},{url:"www.douban.com",context:"豆瓣"},{url:"www.mtime.com",context:"Mtime时光网：让电影遇见生活"},{url:"www.gewara.com",context:"上海电影票网上购买预订_上海电影院排片购票_最新上映电影票价_格瓦拉生活网"},{url:"www.onlinedown.net",context:"华军软件园-提供国内外最新免费软件、共享软件下载及发布的软件下载站，软件资讯、技巧、评测、教程等相关软件资讯！"},{url:"xiazai.zol.com.cn/",context:"ZOL下载-免费软件,绿色软件"},{url:"www.pc6.com",context:"pc6下载站 _ 官方软件下载基地_最安全的软件官方下载网站！"},{url:"www.skycn.com",context:"下载_下载软件_提供最新最安全的免费软件资源下载_天空软件下载"},{url:"www.crsky.com",context:"非凡软件站_CRSKY.com_旗舰软件下载站-免费软件、共享软件一网打尽!(原霏凡软件站)"},{url:"music.baidu.com",context:"百度音乐-听到极致"},{url:"y.qq.com",context:"QQ音乐 - 中国最新最全免费正版高品质音乐平台！"},{url:"www.1ting.com",context:"一听音乐网_歌曲大全: :免费网络在线正版音乐第一站"},{url:"www.9ku.com",context:"九酷音乐网|好听的歌|网络歌曲|dj舞曲|流行歌曲大全"},{url:"www.kugou.com",context:"酷狗音乐 和音乐在一起"},{url:"www.kuwo.cn",context:"正版音乐免费在线试听下载_酷我在线音乐播放器酷我音乐播放器"},{url:"www.xiami.com",context:"虾米音乐(xiami.com) - 阿里音乐旗下品牌 - 乐随心动"},{url:"www.yinyuetai.com",context:"音悦Tai-口袋·FAN-看好音乐"},{url:"www.douban.fm",context:"Filmstar - 豆瓣FM - Beta"},{url:"www.duomi.com",context:"多米音乐播放器官网下载,多米音乐手机版免费下载,多米音乐盒下载"},{url:"www.yue365.com",context:"365音乐网_音乐MP3歌曲免费下载试听的音乐网站"},{url:"music.163.com",context:"网易云音乐 听见好时光"},{url:"www.migu.cn",context:"咪咕文化科技有限公司"},{url:"www.musicradio.com.cn",context:"MusicRadio音乐之声"},{url:"music.weibo.com",context:"微博音乐人-发现明天的流行音乐"},{url:"www.bttiantang.com",context:"bt天堂 - bt电影下载 - BT天堂下载"},{url:"www.dytt8.net",context:"电影天堂_免费电影_迅雷电影下载"},{url:"www.dygang.com",context:"电影港_高清电影下载_720p高清_1080p高清"},{url:"www.gaoqing.la",context:"中国高清网 | 高清电影网|720P|1080P|蓝光原盘|磁力链迅雷下载高清的电影天堂"},{url:"www.piaohua.com",context:"飘花电影网_飘花迅雷电影下载网_最新电影_迅雷免费电影下载"},{url:"www.hdwan.net",context:"海盗湾中文网 | 最新720P、1080P高清电影种子下载网站,海盗湾"},{url:"www.66ys.tv",context:"66影视网-最新电影,最新电视剧,迅雷电影下载（无弹窗网站）"},{url:"www.6vhao.com",context:"最新电影下载，最新电视剧下载，高清电影下载，免费电影下载，6v电影下载网（旧版66影视）"},{url:"www.tiantiandy.com",context:"电影天堂_迅雷电影下载_最新免费电影下载_高清电影 - 天天电影网"},{url:"vod.cnzol.com",context:"飘花电影网_飘花迅雷电影下载网_最新高清电影_迅雷免费电影下载"},{url:"www.xlpu.cc",context:"最新电影下载_免费电影下载_高清电影 - 迅雷铺电影网"},{url:"www.poxiao.com",context:"破晓电影 最新电影 最新电视剧 迅雷电影下载 在线观看"},{url:"www.80s.tw",context:"80s手机电影网 - 最新MP4手机电影下载"},{url:"www.k5.cc",context:"在线吧手机电影网-最新电影下载_80s电影网_迅雷电影下载_MP4高清手机电影下载"},{url:"www.dexiazai.com",context:"DE下载站-分享最新高清电影迅雷下载资源"},{url:"www.taobao.com",context:"淘宝网 - 淘！我喜欢"},{url:"www.jd.com",context:"京东(JD.COM)-综合网购首选-正品低价、品质保障、配送及时、轻松购物！"},{url:"www.1688.com",context:"阿里巴巴1688.com - 全球领先的采购批发平台"},{url:"www.hc360.com",context:"慧聪网-中国领先的B2B电子商务平台，帮助所有企业做成生意!"},{url:"www.tmall.com",context:"天猫tmall.com--上天猫，就够了"},{url:"www.amazon.cn",context:"亚马逊-网上购物商城：要网购, 就来Z.cn!"},{url:"www.dangdang.com",context:"当当—网上购物中心：图书、母婴、美妆、家居、数码、家电、服装、鞋包等，正品低价，货到付款"},{url:"www.etao.com",context:"一淘网_最有影响力的消费者购物社区_优惠特卖 购物分享 海淘全球"},{url:"www.nuomi.com",context:"百度糯米"},{url:"www.meituan.com",context:"【美团网团购】吃喝玩乐好帮手，专业品质团购网"},{url:"www.dianping.com",context:"大众点评网_美食，生活，优惠券，团购"},{url:"www.alibaba.com",context:"Manufacturers, Suppliers, Exporters & Importers from the world's largest online B2B marketplace-Alibaba.com"},{url:"www.amazon.com",context:"Amazon.com: Online Shopping for Electronics, Apparel, Computers, Books, DVDs & more"},{url:"www.suning.com",context:"苏宁易购(Suning) -综合网上购物商城,正品行货,全国联保,货到付款！"},{url:"www.gome.com.cn",context:"国美在线-国美电器官方网上商城,中国领先专业的综合网购平台,正品低价、快速送达、安心服务!"},{url:"www.mogujie.com",context:"蘑菇街 - 我的买手街！"},{url:"www.zhe800.com",context:"【折800官网】精选商品折扣, 1折特卖,天天9.9包邮在折800网!"},{url:"www.kongfz.com",context:"孔夫子旧书网】网上买书：图书_书籍_古籍_二手书，网上卖书：网上书店_古旧书拍卖，全球最大的中文旧书网站"},{url:"www.vmall.com",context:"华为商城官网-提供华为手机"},{url:"ju.taobao.com/",context:"【聚划算】无所不能聚"},{url:"www.tuan88.com",context:"团800团购导航 - 专业团购网站大全,团购网优选"},{url:"www.yhd.com",context:"网上超市1号店，省力省钱省时间"},{url:"www.meilishuo.com",context:"美丽说首页 - 只做正确流行款; 独家冠名《奔跑吧兄弟3》"},{url:"www.lashou.com",context:"团购网-拉手网团购-超人气团购网站-团购上拉手"},{url:"www.bitauto.com",context:"易车网"},{url:"www.58.com",context:"58同城"},{url:"www.ganji.com",context:"赶集网"},{url:"www.weather.com.cn",context:"中国天气网-专业天气预报、气象服务门户"},{url:"www.autohome.com.cn",context:"汽车之家_我的汽车网站，我的汽车之家"},{url:"detail.zol.com.cn/",context:"IT数码产品报价大全_400个类别, 30万产品_中关村在线报价查询频道"},{url:"www.tianqi.com",context:"天气预报查询一周_天气预报15天查询_今天,明天,未来天气预报查询_天气网"},{url:"www.xcar.com.cn",context:"爱卡汽车_中国领先的汽车主题社区、汽车资讯、汽车论坛中心"},{url:"www.kuaidi100.com",context:"快递100-查快递,寄快递,上快递100"},{url:"www.tianqi2345.com",context:"全国天气预报15天_天气在线查询_天气观察网"},{url:"newcar.xcar.com.cn/",context:"【汽车报价|选车中心|选车工具】_爱卡汽车"},{url:"www.51job.com",context:"招聘网_人才网_找工作_求职_上前程无忧"},{url:"www.zhaopin.com",context:"招聘_求职_找工作_上智联招聘人才网"},{url:"www.renren.com",context:"人人网，中国领先的实名制SNS社交网络。加入人人网，找到老同学，结识新朋友"},{url:"www.babytree.com",context:"宝宝树 - 怀孕 育儿 大型育儿网站社区"},{url:"www.focus.cn",context:"搜狐焦点"},{url:"www.pcauto.com.cn",context:"太平洋汽车网_精准报价_权威评测_汽车世界,由此精彩"},{url:"www.ce.cn",context:"中国经济网——国家经济门户"},{url:"www.pcbaby.com.cn",context:"太平洋亲子网_健康育儿 品质生活"},{url:"www.ip138.com",context:"IP地址查询--手机号码查询归属地 | 邮政编码查询 | 长途电话区号 | 身份证号码验证在线查询网"},{url:"qzone.qq.com/",context:"QQ空间"},{url:"www.mama.cn",context:"妈妈网_官方网站_怀孕、育儿等知识交流平台_中国妈妈第一门户网站"},{url:"auto.sina.com.cn/",context:"新浪汽车 - 汽车生活源动力！"},{url:"auto.sohu.com/",context:"搜狐汽车_我的汽车网站，我的搜狐汽车"},{url:"auto.163.com/",context:"汽车首页_网易汽车_车生活.有态度"},{url:"www.8684.cn",context:"公交查询_公交线路查询_公交查询网_8684公交查询"},{url:"auto.qq.com/",context:"腾讯汽车_腾讯网"},{url:"business.sohu.com/",context:"搜狐财经-搜狐"},{url:"www.anjuke.com",context:"安居客"},{url:"www.douguo.com",context:"豆果美食_开启美味生活_中文美食菜谱分享网站，提供优质的家常菜谱大全、餐厅餐馆与美食网购信息"},{url:"www.csc.edu.cn",context:"欢迎访问国家留学网！"},{url:"www.yaolan.com",context:"摇篮网 - 中国最专业的育儿网站和母婴门户"},{url:"www.liepin.com",context:"【猎聘网】招聘_找工作_精英职业发展平台"},{url:"www.lagou.com",context:"拉勾网-最专业的互联网招聘平台"},{url:"www.10jqka.com.cn",context:"同花顺财经__让投资变得更简单"},{url:"price.pcauto.com.cn",context:"【汽车报价】汽车最新报价_汽车价格查询_太平洋汽车网"},{url:"www.meishichina.com",context:"美食天下|美食|菜谱大全|家常菜谱|美食社区-最大的中文美食网站"},{url:"www.baixing.com",context:"百姓网"},{url:"www.chinahr.com",context:"中华英才网"},{url:"www.kaixin001.com",context:"开心网"},{url:"www.weibo.com",context:"微博-随时随地发现新鲜事"},{url:"www.xinhuanet.com",context:"新华网_让新闻离你更近"},{url:"www.people.com.cn",context:"人民网"},{url:"www.hao123.com",context:"hao123_上网从这里开始"},{url:"www.china.com.cn",context:"中国网--网上中国"},{url:"blog.sina.com.cn/",context:"新浪博客首页_新浪网"},{url:"www.xilu.com",context:"西陆网-军事观察室、军事记实、军事科技:中国第一军事门户网站"},{url:"bbs.tianya.cn/",context:"天涯论坛_华人网上家园"},{url:"iask.sina.com.cn/",context:"爱问知识人 - 新浪旗下中文互动问答平台"},{url:"www.2345.com",context:"2345网址导航－开创中国百年品牌"},{url:"www.linkedin.com",context:"全球最大的职业社交网站 | 领英"},{url:"mt.sohu.com/",context:"公众平台首页-搜狐"},{url:"www.chinaz.com",context:"站长之家 - 中国站长站 - 站长资讯 | 我们致力于为中文网站提供动力！"},{url:"www.huanqiu.com",context:"环球网_全球生活新门户_环球时报旗下网站"},{url:"www.eastday.com",context:"东方网"},{url:"www.21cn.com",context:"21CN"},{url:"www.offcn.com",context:"公务员考试网"},{url:"weixin.qq.com/",context:"微信，是一个生活方式"},{url:"kf.qq.com/",context:"腾讯客服-首页"},{url:"blog.163.com/",context:"网易博客-网易"},{url:"www.msn.com",context:"MSN中文网：数字生活 白领门户; (MSN官方下载)"},{url:"mp.weixin.qq.com/",context:"微信公众平台"},{url:"www.19lou.com",context:"19楼(19lou.com) - 分享生活 温暖你我 - 杭州19楼"},{url:"www.qidian.com",context:"小说阅读_起点中文小说网|免费小说,玄幻小说,武侠小说,青春小说,小说网各类小说下载"},{url:"v.baidu.com/",context:"百度视频搜索——全球最大中文视频搜索引擎"},{url:"www.ku6.com",context:"酷6网 中国领先视频门户"},{url:"image.baidu.com/",context:"百度图片—全球最大中文图片库"},{url:"www.56.com",context:"56网_中国最大的原创视频分享网站,在线视频观看,原创视频上传,海量视频搜索 - 56.com"},{url:"www.pps.tv",context:"PPS影音-看你想看,高清影视剧,卫视直播,网络在线视频,免费视频"},{url:"movie.douban.com",context:"豆瓣电影"},{url:"movie.xunlei.com",context:"迅雷影评"},{url:"www.yxlady.com",context:"伊秀女性网-我们致力于最专业的女性时尚门户网站"},{url:"ent.qq.com/",context:"娱乐首页_腾讯网"},{url:"ent.sina.com.cn/",context:"新浪娱乐首页_娱乐新闻_新浪网"},{url:"yule.sohu.com/",context:"搜狐娱乐-搜狐"},{url:"ent.ifeng.com/",context:"凤凰娱乐_娱乐频道_凤凰网"},{url:"www.kankan.com",context:"响巢看看-中国领先的高清影视视频门户 最新电影和最新电视剧在线观看"},{url:"v.ifeng.com/",context:"凤凰视频-最具媒体价值的视频门户-凤凰网"},{url:"eladies.sina.com.cn/",context:"新浪女性_新浪网"},{url:"video.sina.com.cn/",context:"新浪视频_高清视频在线观看_新浪网"},{url:"ac.qq.com/",context:"动漫 - 腾讯动漫官方网站 - 首页"},{url:"www.xunlei.com",context:"迅雷-领先的众筹云计算服务商"},{url:"www.duowan.com",context:"多玩游戏网 | 多玩游戏 多交朋友"},{url:"www.17173.com",context:": :17173.com: :中国游戏第一门户站"},{url:"www.tgbus.com",context:": :TGbus.com: :中国游戏第一门户_电玩巴士_电视游戏_电子游戏_网络游戏_手机游戏_网页游戏"},{url:"www.52pk.com",context:"游戏第一综合门户_52pk游戏网"},{url:"www.178.com",context:"178游戏网 | 一起把游戏进行到底"},{url:"www.pcgames.com.cn",context:"太平洋游戏网_竞技游戏综合门户"},{url:"www.99danji.com",context:"单机游戏_单机游戏下载大全中文版下载_好玩的单机游戏下载基地 - 99单机游戏"},{url:"www.uuu9.com",context:"游久网 - 玩家喜爱的网络游戏资讯门户 - www.uuu9.com"},{url:"iwan.baidu.com/",context:"百度爱玩"},{url:"www.4399.com",context:"小游戏, 4399小游戏,小游戏大全,双人小游戏大全 - www.4399.com 中国领先的游戏平台"},{url:"games.sina.com.cn/",context:"新浪游戏_最新网游,手游,单机游戏资讯,排行,下载_最大中文游戏媒体"},{url:"games.qq.com/",context:"腾讯游戏频道—最有影响力的游戏媒体"},{url:"lol.qq.com/",context:"英雄联盟-LOL-官方网站-腾讯游戏-英雄，为你而战"},{url:"www.gamersky.com",context:"游民星空 - 大型单机游戏媒体 提供最具特色单机游戏资讯、下载"},{url:"www.7k7k.com",context:"小游戏, 7k7k小游戏,小游戏大全,双人小游戏 - www.7k7k.com"},{url:"www.18183.com",context:"手机游戏第一门户网站 www.18183.com"},{url:"www.yxdown.com",context:"单机游戏_单机游戏下载_单机游戏下载大全中文版下载_游迅网"},{url:"www.gamedog.cn",context:"手游第一门户_排行榜_游戏狗手游网"},{url:"www.ali213.net",context:"单机游戏_单机游戏下载_中国单机游戏门户_游侠网"},{url:"cf.qq.com/",context:"惊爆娱人节-穿越火线官方网站-腾讯游戏"},{url:"www.ptbus.com",context:"口袋巴士_手机游戏第一门户_安卓游戏_苹果游戏_手机游戏"},{url:"www.3366.com",context:"小游戏, 3366小游戏大全,双人小游戏, 3366小游戏快乐简单一点！ - www.3366.com"},{url:"www.91.com",context:"91.com_移动互联网第一平台_百度91门户"},{url:"www.doyo.cn",context:"最大最全的游戏基地_单机游戏下载_小游戏大全_逗游网"},{url:"www.eeyy.com",context:"一游网_网页游戏_好玩的网页游戏_网页游戏开服表"},{url:"www.sdo.com",context:"盛大游戏——盛大游戏娱乐平台"},{url:"www.07073.com",context:"07073游戏网-新开网页游戏开服表,最新网页游戏大全,好玩的网页游戏排行榜"},{url:"game.163.com/",context:"网易游戏官网_游戏热爱者"},{url:"www.265g.com",context:"网页游戏第一门户站: : : 265G.COM: : :"},{url:"www.5173.com",context:"网络游戏交易平台|装备交易|游戏币交易|帐号交易|点卡充值|代练服务-是国内最权威最安全的游戏交易平台-5173.com"},{url:"dnf.qq.com/",context:"地下城与勇士-DNF-官方网站-腾讯游戏-格斗网游王者之作, 300万同时在线"},{url:"game.weibo.com/",context:"微游戏-动漫游戏-新浪微博官方游戏平台"},{url:"www.xoyo.com",context:"金山游戏官方网站_金山逍遥网Xoyo.com"},{url:"www.yzz.cn",context:"网络游戏:叶子猪游戏网-最专业的网络游戏门户"},{url:"www.25pp.com",context:"苹果助手_苹果手机助手_PP苹果助手官方下载-PP助手官网"},{url:"www.wanmei.com",context:"完美世界 - 完美娱乐 世界同享"},{url:"bbs.duowan.com/",context:"多玩游戏论坛-第一游戏论坛，多玩游戏，多交朋友！"},{url:"www.61.com",context:"淘米网_妈妈放心_孩子欢喜"},{url:"qqgame.qq.com/",context:"QQ游戏_QQ游戏大全_游戏下载_QQ游戏官网"},{url:"www.huya.com",context:"虎牙直播-虎牙TV-中国领先的互动直播平台"},{url:"www.91danji.com",context:"单机游戏下载,好玩的单机游戏大全官网,单机游戏下载基地-91单机网"},{url:"xyq.163.com/",context:"《梦幻西游》电脑版官方网站 - 中国第一网游 网易西游题材扛鼎之作"},{url:"www.changyou.com",context:"畅游-ChangYou.com"},{url:"www.zongheng.com",context:"小说,小说网-纵横中文网|最热门的免费小说网"},{url:"www.xs8.cn",context:"言情小说吧 - (清晰)免费言情小说在线阅读和言情小说下载网（都市言情小说、穿越小说、古代言情小说） - www.xs8.cn"},{url:"www.xxsy.net",context:"小说,潇湘书院,言情小说,女性免费小说网站"},{url:"www.readnovel.com",context:"《小说阅读网》小说,言情小说,小说下载,校园小说,穿越小说,玄幻小说"},{url:"www.hongxiu.com",context:"小说 - 红袖添香小说网 - 最新言情小说,免费小说下载,好看的言情小说阅读网"},{url:"www.jjwxc.net",context:"言情小说_都市言情小说_免费言情小说在线阅读_晋江文学城"},{url:"www.17k.com",context:"小说-17K小说网|免费小说下载,最新最好看的小说网"},{url:"book.qq.com/",context:"腾讯文学 - 文字之美，感动心灵"},{url:"www.zhulang.com",context:"逐浪小说网_免费小说,玄幻小说,都市小说,修真武侠,军事历史阅读下载"},{url:"www.3gsc.com.cn",context:"小说,都市小说,好看的小说,小说网-3G小说网"},{url:"www.kanshu.com",context:"看书网 - 原创小说网站,(最新)小说免费阅读下载 好看的小说阅读网!"},{url:"chuangshi.qq.com",context:"小说_小说排行榜_免费小说下载网_好看的小说网络尽在创世中文网"},{url:"www.tadu.com",context:"小说,塔读文学小说网-好看的畅销小说,原创小说"},{url:"yuedu.163.com",context:"网易云阅读--海量新闻、精彩资讯、图书、免费小说、漫画，免费畅读"},{url:"dl.xunlei.com/",context:"迅雷产品中心"},{url:"vip.xunlei.com/",context:"迅雷会员 因快而乐"},{url:"niu.xunlei.com/",context:"【网页游戏】新开网页游戏开服表-迅雷牛X页游平台"},{url:"lixian.xunlei.com/",context:"迅雷离线下载，让下载飞起来"}]}},988:function(e,t,n){"use strict";n.r(t);var r=n(633),o=n(477);for(var i in o)"default"!==i&&function(e){n.d(t,e,function(){return o[e]})}(i);var s=n(0),a=Object(s.a)(o.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\search-renderer\\views\\task-item-panel.vue",t.default=a.exports},989:function(e,t,n){"use strict";n.r(t);var r=n(634),o=n(481);for(var i in o)"default"!==i&&function(e){n.d(t,e,function(){return o[e]})}(i);var s=n(0),a=Object(s.a)(o.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\search-renderer\\views\\task-item-panel-more.vue",t.default=a.exports},990:function(e,t,n){"use strict";n.r(t);var r=n(635),o=n(483);for(var i in o)"default"!==i&&function(e){n.d(t,e,function(){return o[e]})}(i);var s=n(0),a=Object(s.a)(o.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\search-renderer\\views\\pan-task-item-panel.vue",t.default=a.exports},991:function(e,t,n){"use strict";n.r(t);var r=n(636),o=n(487);for(var i in o)"default"!==i&&function(e){n.d(t,e,function(){return o[e]})}(i);var s=n(0),a=Object(s.a)(o.default,r.a,r.b,!1,null,null,null);a.options.__file="src\\search-renderer\\views\\pan-task-item-panel-more.vue",t.default=a.exports},992:function(e,t){e.exports="data:image/png;base64,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"},993:function(e,t){e.exports="data:image/png;base64,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"},994:function(e,t,n){"use strict";var r=n(1295);n.n(r).a}});
//# sourceMappingURL=renderer.js.map