# 安全卡密验证程序说明

## 🎯 程序概述

本项目包含三个不同安全级别的卡密验证程序，都使用相同的卡密 **555555**，但具有不同程度的反逆向和加密保护机制。

## 📁 文件说明

### 1. `simple_secure_login.py` - 简化版（推荐）
**特点**：基础安全保护，易于运行和理解
- ✅ 基础反调试检测
- ✅ 进程监控保护
- ✅ 时间攻击防护
- ✅ 多重哈希验证
- ✅ 账户锁定机制
- ✅ 加密消息传输

### 2. `secure_login.py` - 标准版
**特点**：中等安全级别，包含更多保护机制
- ✅ Windows API反调试
- ✅ 虚拟机检测
- ✅ 内存完整性检查
- ✅ 动态密钥生成
- ✅ 反篡改保护
- ✅ 多线程安全监控

### 3. `secure_app.py` + `advanced_security.py` - 高级版
**特点**：最高安全级别，需要高级安全模块支持
- ✅ 硬件断点检测
- ✅ 代码混淆保护
- ✅ 内存转储防护
- ✅ 单步调试检测
- ✅ 动态函数执行
- ✅ 高级加密算法

## 🔑 卡密信息

**统一卡密**: `555555`

所有程序都使用相同的6位数字卡密，但采用不同的验证算法和安全机制。

## 🚀 使用方法

### 快速开始（推荐）
```bash
python simple_secure_login.py
```

### 标准版本
```bash
python secure_login.py
```

### 高级版本（需要依赖）
```bash
python secure_app.py
```

## 🛡️ 安全特性详解

### 基础安全保护

#### 1. 反调试检测
- **进程监控**：检测常见调试工具（OllyDbg, x64dbg, IDA等）
- **时间检测**：监控程序执行时间异常
- **环境检测**：识别虚拟机和沙箱环境

#### 2. 密钥保护
- **多重哈希**：使用SHA256 + MD5 + Base64多重加密
- **时间戳混合**：结合时间戳防止重放攻击
- **盐值加密**：使用固定盐值增强安全性

#### 3. 账户安全
- **尝试限制**：最多3次登录尝试
- **自动锁定**：失败后锁定3-5分钟
- **会话管理**：生成唯一会话令牌

### 高级安全保护

#### 1. Windows API集成
```python
# 检测调试器
kernel32.IsDebuggerPresent()
kernel32.CheckRemoteDebuggerPresent()

# 内存保护
kernel32.VirtualAlloc()
kernel32.VirtualProtect()
```

#### 2. 代码混淆
```python
# 字符串混淆
obfuscated = CodeObfuscator.obfuscate_string("555555")

# 动态函数执行
execute_secure_function(compressed_func, globals_dict)
```

#### 3. 反分析技术
- **硬件断点检测**：检查调试寄存器Dr0-Dr7
- **内存断点检测**：监控内存访问异常
- **单步调试检测**：识别单步执行模式
- **时序分析**：检测执行时间异常

## 🔧 技术实现

### 密钥验证算法
```python
def verify_key(input_key):
    # 1. 基础验证
    if len(input_key) != 6 or not input_key.isdigit():
        return False
    
    # 2. 时间戳生成
    timestamp = str(int(time.time() / 3600))
    
    # 3. 多重哈希
    combined = input_key + timestamp + salt
    hash1 = hashlib.sha256(combined.encode()).hexdigest()
    hash2 = hashlib.md5(hash1.encode()).hexdigest()
    final_hash = base64.b64encode(hash2.encode()).decode()
    
    # 4. 比较验证
    return final_hash == expected_hash
```

### 反调试实现
```python
def anti_debug_check():
    # 检测调试器
    if kernel32.IsDebuggerPresent():
        trigger_protection()
    
    # 检测可疑进程
    for proc in psutil.process_iter():
        if proc.name().lower() in suspicious_processes:
            trigger_protection()
    
    # 时间检测
    if execution_time > threshold:
        trigger_protection()
```

### 加密保护
```python
def encrypt_data(data, key):
    key_hash = hashlib.md5(key.encode()).digest()
    encrypted = bytearray()
    
    for i, byte in enumerate(data.encode()):
        encrypted.append(byte ^ key_hash[i % len(key_hash)])
    
    return base64.b64encode(encrypted).decode()
```

## 📊 安全级别对比

| 功能 | 简化版 | 标准版 | 高级版 |
|------|--------|--------|--------|
| 基础反调试 | ✅ | ✅ | ✅ |
| 进程监控 | ✅ | ✅ | ✅ |
| 虚拟机检测 | ❌ | ✅ | ✅ |
| 硬件断点检测 | ❌ | ❌ | ✅ |
| 代码混淆 | ❌ | ❌ | ✅ |
| 内存保护 | ❌ | ✅ | ✅ |
| 动态执行 | ❌ | ❌ | ✅ |
| 文件完整性 | ❌ | ✅ | ✅ |

## ⚠️ 使用注意事项

### 系统要求
- **操作系统**：Windows 7/8/10/11
- **Python版本**：3.7+
- **依赖库**：psutil（可选）

### 运行环境
- **管理员权限**：建议以管理员身份运行
- **杀毒软件**：可能会误报，需要添加白名单
- **虚拟机**：高级版本可能在虚拟机中无法运行

### 安全提醒
1. **仅供学习**：本程序仅用于学习反逆向技术
2. **合法使用**：请遵守相关法律法规
3. **源码保护**：实际应用中应进一步混淆源码
4. **密钥管理**：生产环境中应使用更复杂的密钥

## 🔍 测试方法

### 正常测试
1. 运行程序
2. 输入正确卡密：`555555`
3. 验证是否能成功进入个人页面

### 安全测试
1. **错误密钥测试**：输入错误卡密，验证锁定机制
2. **调试器测试**：在调试器中运行，验证反调试功能
3. **进程监控测试**：运行可疑进程，验证检测功能

### 性能测试
- **启动时间**：程序启动到界面显示的时间
- **验证时间**：密钥验证的响应时间
- **内存占用**：程序运行时的内存使用量

## 🛠️ 自定义配置

### 修改卡密
```python
# 在相应文件中找到并修改
real_key = "555555"  # 改为您的卡密
```

### 调整安全级别
```python
# 修改尝试次数
max_attempts = 3  # 改为您想要的次数

# 修改锁定时间
lockout_time = 300  # 改为您想要的秒数
```

### 添加新的检测
```python
# 添加新的可疑进程
suspicious_processes.append("your_process.exe")

# 添加新的安全检查
def custom_security_check():
    # 您的检查逻辑
    return True
```

## 📈 扩展功能

### 可以添加的功能
- [ ] 网络验证
- [ ] 硬件指纹识别
- [ ] 更复杂的加密算法
- [ ] 日志记录系统
- [ ] 远程监控
- [ ] 自动更新机制

### 改进建议
- [ ] 使用更强的加密算法（AES-256）
- [ ] 实现代码虚拟化保护
- [ ] 添加反Hook检测
- [ ] 实现控制流混淆
- [ ] 添加反内存转储保护

## 📝 总结

这套安全卡密验证程序展示了从基础到高级的反逆向保护技术：

1. **渐进式安全**：三个版本提供不同级别的保护
2. **实用性强**：可以直接运行和学习
3. **技术全面**：涵盖多种反逆向技术
4. **易于扩展**：可以根据需要添加新功能

**推荐使用顺序**：简化版 → 标准版 → 高级版

根据您的需求和环境选择合适的版本使用。

---

**卡密**: `555555`  
**享受安全编程的乐趣！** 🔐✨
