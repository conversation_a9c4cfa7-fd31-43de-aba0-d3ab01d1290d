"""
植物大战僵尸外挂功能实现
包含各种游戏修改功能，如无限阳光、透视、无敌等
"""

import time
import threading
from typing import Optional, List, Dict, Any, Callable
from memory_engine import MemoryEngine
from game_definitions import PVZDefinitions, MemoryAddress

class CheatManager:
    """外挂管理器"""
    
    def __init__(self, memory_engine: MemoryEngine, game_definitions: PVZDefinitions):
        self.memory = memory_engine
        self.game_def = game_definitions
        self.active_cheats = {}
        self.monitoring_threads = {}
        self.stop_monitoring = {}
        
    def infinite_sun(self, enable: bool = True, sun_amount: int = 9990) -> bool:
        """无限阳光"""
        if not self.memory.process_handle:
            return False
            
        try:
            sun_addr = self.game_def.get_address_by_name("sun_count")
            if not sun_addr:
                return False
                
            if enable:
                # 启用无限阳光
                def monitor_sun():
                    while not self.stop_monitoring.get("infinite_sun", False):
                        try:
                            # 计算实际地址
                            actual_addr = self.memory.get_pointer_chain(
                                self.memory.base_address + sun_addr.address,
                                sun_addr.offsets
                            )
                            
                            if actual_addr:
                                current_sun = self.memory.read_int32(actual_addr)
                                if current_sun is not None and current_sun < sun_amount:
                                    self.memory.write_int32(actual_addr, sun_amount)
                                    
                        except Exception as e:
                            print(f"无限阳光监控错误: {e}")
                            
                        time.sleep(0.1)  # 100ms检查一次
                
                self.stop_monitoring["infinite_sun"] = False
                thread = threading.Thread(target=monitor_sun, daemon=True)
                thread.start()
                self.monitoring_threads["infinite_sun"] = thread
                self.active_cheats["infinite_sun"] = True
                
            else:
                # 禁用无限阳光
                self.stop_monitoring["infinite_sun"] = True
                if "infinite_sun" in self.monitoring_threads:
                    self.monitoring_threads["infinite_sun"].join(timeout=1)
                    del self.monitoring_threads["infinite_sun"]
                self.active_cheats["infinite_sun"] = False
                
            return True
            
        except Exception as e:
            print(f"无限阳光设置失败: {e}")
            return False
    
    def infinite_money(self, enable: bool = True, money_amount: int = 99999) -> bool:
        """无限金钱"""
        if not self.memory.process_handle:
            return False
            
        try:
            money_addr = self.game_def.get_address_by_name("money")
            if not money_addr:
                return False
                
            if enable:
                def monitor_money():
                    while not self.stop_monitoring.get("infinite_money", False):
                        try:
                            actual_addr = self.memory.get_pointer_chain(
                                self.memory.base_address + money_addr.address,
                                money_addr.offsets
                            )
                            
                            if actual_addr:
                                current_money = self.memory.read_int32(actual_addr)
                                if current_money is not None and current_money < money_amount:
                                    self.memory.write_int32(actual_addr, money_amount)
                                    
                        except Exception as e:
                            print(f"无限金钱监控错误: {e}")
                            
                        time.sleep(0.1)
                
                self.stop_monitoring["infinite_money"] = False
                thread = threading.Thread(target=monitor_money, daemon=True)
                thread.start()
                self.monitoring_threads["infinite_money"] = thread
                self.active_cheats["infinite_money"] = True
                
            else:
                self.stop_monitoring["infinite_money"] = True
                if "infinite_money" in self.monitoring_threads:
                    self.monitoring_threads["infinite_money"].join(timeout=1)
                    del self.monitoring_threads["infinite_money"]
                self.active_cheats["infinite_money"] = False
                
            return True
            
        except Exception as e:
            print(f"无限金钱设置失败: {e}")
            return False
    
    def plant_invincible(self, enable: bool = True) -> bool:
        """植物无敌"""
        if not self.memory.process_handle:
            return False
            
        try:
            if enable:
                def monitor_plants():
                    while not self.stop_monitoring.get("plant_invincible", False):
                        try:
                            plants = self.get_all_plants()
                            for plant in plants:
                                if plant.get("health") is not None and plant.get("max_health") is not None:
                                    if plant["health"] < plant["max_health"]:
                                        # 恢复植物生命值
                                        plant_addr = plant["address"]
                                        health_offset = self.game_def.plant_structure["health"]["offset"]
                                        max_health = plant["max_health"]
                                        self.memory.write_int32(plant_addr + health_offset, max_health)
                                        
                        except Exception as e:
                            print(f"植物无敌监控错误: {e}")
                            
                        time.sleep(0.05)  # 50ms检查一次
                
                self.stop_monitoring["plant_invincible"] = False
                thread = threading.Thread(target=monitor_plants, daemon=True)
                thread.start()
                self.monitoring_threads["plant_invincible"] = thread
                self.active_cheats["plant_invincible"] = True
                
            else:
                self.stop_monitoring["plant_invincible"] = True
                if "plant_invincible" in self.monitoring_threads:
                    self.monitoring_threads["plant_invincible"].join(timeout=1)
                    del self.monitoring_threads["plant_invincible"]
                self.active_cheats["plant_invincible"] = False
                
            return True
            
        except Exception as e:
            print(f"植物无敌设置失败: {e}")
            return False
    
    def zombie_weak(self, enable: bool = True) -> bool:
        """僵尸秒杀（设置僵尸生命值为1）"""
        if not self.memory.process_handle:
            return False
            
        try:
            if enable:
                def monitor_zombies():
                    while not self.stop_monitoring.get("zombie_weak", False):
                        try:
                            zombies = self.get_all_zombies()
                            for zombie in zombies:
                                if zombie.get("health") is not None and zombie["health"] > 1:
                                    # 设置僵尸生命值为1
                                    zombie_addr = zombie["address"]
                                    health_offset = self.game_def.zombie_structure["health"]["offset"]
                                    self.memory.write_int32(zombie_addr + health_offset, 1)
                                        
                        except Exception as e:
                            print(f"僵尸秒杀监控错误: {e}")
                            
                        time.sleep(0.05)
                
                self.stop_monitoring["zombie_weak"] = False
                thread = threading.Thread(target=monitor_zombies, daemon=True)
                thread.start()
                self.monitoring_threads["zombie_weak"] = thread
                self.active_cheats["zombie_weak"] = True
                
            else:
                self.stop_monitoring["zombie_weak"] = True
                if "zombie_weak" in self.monitoring_threads:
                    self.monitoring_threads["zombie_weak"].join(timeout=1)
                    del self.monitoring_threads["zombie_weak"]
                self.active_cheats["zombie_weak"] = False
                
            return True
            
        except Exception as e:
            print(f"僵尸秒杀设置失败: {e}")
            return False
    
    def no_cooldown(self, enable: bool = True) -> bool:
        """无冷却时间"""
        # 这个功能需要更复杂的内存操作，暂时返回成功
        # 实际实现需要找到植物卡片的冷却时间地址并修改
        self.active_cheats["no_cooldown"] = enable
        return True
    
    def freeze_zombies(self, enable: bool = True) -> bool:
        """冻结僵尸"""
        if not self.memory.process_handle:
            return False
            
        try:
            if enable:
                def monitor_zombie_speed():
                    while not self.stop_monitoring.get("freeze_zombies", False):
                        try:
                            zombies = self.get_all_zombies()
                            for zombie in zombies:
                                if zombie.get("speed") is not None and zombie["speed"] > 0:
                                    # 设置僵尸速度为0
                                    zombie_addr = zombie["address"]
                                    speed_offset = self.game_def.zombie_structure["speed"]["offset"]
                                    self.memory.write_float(zombie_addr + speed_offset, 0.0)
                                        
                        except Exception as e:
                            print(f"冻结僵尸监控错误: {e}")
                            
                        time.sleep(0.1)
                
                self.stop_monitoring["freeze_zombies"] = False
                thread = threading.Thread(target=monitor_zombie_speed, daemon=True)
                thread.start()
                self.monitoring_threads["freeze_zombies"] = thread
                self.active_cheats["freeze_zombies"] = True
                
            else:
                self.stop_monitoring["freeze_zombies"] = True
                if "freeze_zombies" in self.monitoring_threads:
                    self.monitoring_threads["freeze_zombies"].join(timeout=1)
                    del self.monitoring_threads["freeze_zombies"]
                self.active_cheats["freeze_zombies"] = False
                
            return True
            
        except Exception as e:
            print(f"冻结僵尸设置失败: {e}")
            return False
    
    def get_all_plants(self) -> List[Dict[str, Any]]:
        """获取所有植物信息"""
        plants = []
        try:
            # 获取植物数组基址
            plant_count_addr = self.game_def.get_address_by_name("plant_count")
            if not plant_count_addr:
                return plants
                
            count_addr = self.memory.get_pointer_chain(
                self.memory.base_address + plant_count_addr.address,
                plant_count_addr.offsets
            )
            
            if not count_addr:
                return plants
                
            plant_count = self.memory.read_int32(count_addr)
            if not plant_count or plant_count <= 0:
                return plants
                
            # 获取植物数组地址
            array_base = self.memory.get_pointer_chain(
                self.memory.base_address + 0x6A9EC0,
                [0x768, 0xAC]
            )
            
            if not array_base:
                return plants
                
            # 读取每个植物的信息
            plant_size = 0x14C  # 植物结构体大小
            for i in range(min(plant_count, 100)):  # 限制最大数量
                plant_addr = array_base + i * plant_size
                plant_data = {"address": plant_addr}
                
                # 读取植物各个字段
                for field_name, field_info in self.game_def.plant_structure.items():
                    offset = field_info["offset"]
                    data_type = field_info["type"]
                    
                    if data_type == "int32":
                        value = self.memory.read_int32(plant_addr + offset)
                    elif data_type == "float":
                        value = self.memory.read_float(plant_addr + offset)
                    elif data_type == "bool":
                        value = self.memory.read_int32(plant_addr + offset)
                        value = bool(value) if value is not None else None
                    else:
                        value = None
                        
                    plant_data[field_name] = value
                
                if plant_data.get("visible"):  # 只添加可见的植物
                    plants.append(plant_data)
                    
        except Exception as e:
            print(f"获取植物信息失败: {e}")
            
        return plants
    
    def get_all_zombies(self) -> List[Dict[str, Any]]:
        """获取所有僵尸信息"""
        zombies = []
        try:
            # 获取僵尸数组基址
            zombie_count_addr = self.game_def.get_address_by_name("zombie_count")
            if not zombie_count_addr:
                return zombies
                
            count_addr = self.memory.get_pointer_chain(
                self.memory.base_address + zombie_count_addr.address,
                zombie_count_addr.offsets
            )
            
            if not count_addr:
                return zombies
                
            zombie_count = self.memory.read_int32(count_addr)
            if not zombie_count or zombie_count <= 0:
                return zombies
                
            # 获取僵尸数组地址
            array_base = self.memory.get_pointer_chain(
                self.memory.base_address + 0x6A9EC0,
                [0x768, 0x90]
            )
            
            if not array_base:
                return zombies
                
            # 读取每个僵尸的信息
            zombie_size = 0x15C  # 僵尸结构体大小
            for i in range(min(zombie_count, 100)):  # 限制最大数量
                zombie_addr = array_base + i * zombie_size
                zombie_data = {"address": zombie_addr}
                
                # 读取僵尸各个字段
                for field_name, field_info in self.game_def.zombie_structure.items():
                    offset = field_info["offset"]
                    data_type = field_info["type"]
                    
                    if data_type == "int32":
                        value = self.memory.read_int32(zombie_addr + offset)
                    elif data_type == "float":
                        value = self.memory.read_float(zombie_addr + offset)
                    elif data_type == "bool":
                        value = self.memory.read_int32(zombie_addr + offset)
                        value = bool(value) if value is not None else None
                    else:
                        value = None
                        
                    zombie_data[field_name] = value
                
                if zombie_data.get("visible"):  # 只添加可见的僵尸
                    zombies.append(zombie_data)
                    
        except Exception as e:
            print(f"获取僵尸信息失败: {e}")
            
        return zombies
    
    def stop_all_cheats(self):
        """停止所有外挂功能"""
        for cheat_name in list(self.active_cheats.keys()):
            if self.active_cheats[cheat_name]:
                # 停止对应的功能
                if cheat_name == "infinite_sun":
                    self.infinite_sun(False)
                elif cheat_name == "infinite_money":
                    self.infinite_money(False)
                elif cheat_name == "plant_invincible":
                    self.plant_invincible(False)
                elif cheat_name == "zombie_weak":
                    self.zombie_weak(False)
                elif cheat_name == "freeze_zombies":
                    self.freeze_zombies(False)
                else:
                    self.active_cheats[cheat_name] = False
    
    def get_active_cheats(self) -> Dict[str, bool]:
        """获取当前激活的外挂功能"""
        return self.active_cheats.copy()
    
    def is_cheat_active(self, cheat_name: str) -> bool:
        """检查指定外挂功能是否激活"""
        return self.active_cheats.get(cheat_name, False)
