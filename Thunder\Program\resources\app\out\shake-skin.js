!function(t){var n={};function e(i){if(n[i])return n[i].exports;var o=n[i]={i,l:!1,exports:{}};return t[i].call(o.exports,o,o.exports,e),o.l=!0,o.exports}e.m=t,e.c=n,e.d=function(t,n,i){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:i})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(e.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var o in t)e.d(i,o,function(n){return t[n]}.bind(null,o));return i},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s=0)}([function(t,n,e){t.exports=e(1)},function(t,n){var e=document.createElement("div");e.id="fallContainer";var o=document.getElementById("app");o?document.body.insertBefore(e,o):document.body.appendChild(e);var a=void 0,r=function(t){this.id=document.getElementById(t.id),this.num=t.num,this.imgNum=t.imgNum,this.durationMax=t.durationMax,this.durationMin=t.durationMin,this.delay=t.delay,this.src=t.src,this.rotate=t.rotate||0,this.generateStyle(this.rotate),this.init()};r.prototype.randomInteger=function(t,n){return t+Math.floor(Math.random()*(n-t))},r.prototype.randomFloat=function(t,n){return t+Math.random()*(n-t)},r.prototype.generateStyle=function(t){var n=!1;void 0===a&&(n=!0,a=document.createElement("style")),a.innerHTML=`#fallContainer{position: fixed; width: 100vw; height: 100vh; left: 0;right: 0;top: 0;bottom: 0; overflow: hidden;z-index: 10000;pointer-events: none;}#fallContainer div{position: absolute; width: 32px; height: 32px; animation-iteration-count: 1, 1; animation-timing-function: linear, ease-in; animation-name: fade, drop; top: -50px;animation-fill-mode:both;}#fallContainer div:nth-child(n){width: 15px; height: 15px;}#fallContainer div:nth-child(2n){width: 22px; height: 22px;}#fallContainer div:nth-child(3n){width: 28px; height: 28px;}#fallContainer div:nth-child(4n){width: 32px; height: 32px;}#fallContainer > div > img{width: 100%;height: 100%;position: absolute; animation-iteration-count: infinite; animation-timing-function: ease-in-out; transform-origin: 50% 50%;}@keyframes fade{0%{opacity:1;} 90%{opacity:1;} 100%{opacity:0;}}@keyframes drop{0%{transform: translate(0px, -50px);} 100%{transform: translate(0px, 120vh);}}@keyframes fallAni1{0%{transform:rotate(-${t}deg);} 100%{transform:rotate(${t}deg);}}@keyframes fallAni2{0%{transform:scale(-1, 1) rotate(${t}deg);} 100%{transform:scale(-1, 1) rotate(-${t}deg);}}`,n&&document.body.appendChild(a)},r.prototype.init=function(){var t=document,n=t.body.clientWidth;for(i=0;i<this.num;i++){var e=t.createElement("div"),o=t.createElement("img"),a=Math.random()<.5?"fallAni1":"fallAni2 ",r=this.randomFloat(0,1)+"s";e.style.left=100*this.randomInteger(0,n)/n+"vw",fallDur=this.randomInteger(this.durationMin,this.durationMax)+"s",e.style.animationDuration=fallDur+", "+fallDur,e.style.animationDelay=r+", "+r;var d=this.randomInteger(50,100)/100;e.style.opacity=d,e.style.pointerEvents="auto",o.src=this.src,o.style.animationDuration=this.randomInteger(this.durationMin,this.durationMax)+"s",o.style.animationName=a,o.style.opacity=d,this.id.appendChild(e),e.appendChild(o)}},window.startCoolSkinShake=function(t){document.getElementById("fallContainer").innerHTML="",new r({id:"fallContainer",num:t.count,imgNum:{min:1,max:1},durationMax:t.durationMax?t.durationMax:10,durationMin:t.durationMin?t.durationMin:4,delay:{min:1,max:10},src:t.image,rotate:t.rotate})},window.stopCoolSkinShake=function(){document.getElementById("fallContainer").innerHTML=""}}]);
//# sourceMappingURL=shake-skin.js.map