# 植物大战僵尸内存工具

一个功能完整的植物大战僵尸游戏内存读取和修改工具，支持图形界面操作和多种外挂功能。

## 功能特性

### 🎮 游戏状态监控
- 实时显示阳光、金钱、关卡、波数等游戏信息
- 监控植物和僵尸数量统计
- 自动检测游戏进程状态

### 🔧 外挂功能
- **无限阳光** - 自动维持指定数量的阳光
- **无限金钱** - 自动维持指定数量的金钱
- **植物无敌** - 植物生命值自动恢复
- **僵尸秒杀** - 将僵尸生命值设为1
- **冻结僵尸** - 将僵尸移动速度设为0
- **无冷却时间** - 移除植物卡片冷却（开发中）

### 📊 实体监控
- 实时显示所有植物信息（类型、位置、生命值）
- 实时显示所有僵尸信息（类型、位置、生命值）
- 支持详细的实体数据查看

### 🛠️ 内存编辑
- 直接读取和修改内存地址
- 支持多种数据类型（int32、float、string、bytes）
- 十六进制地址输入支持
- 内存值实时验证

### 📝 日志系统
- 详细的操作日志记录
- 错误信息追踪
- 日志保存和清空功能

## 系统要求

- Windows 7/8/10/11
- Python 3.7 或更高版本
- 植物大战僵尸游戏（支持原版、年度版等）

## 安装和使用

### 方法一：使用启动脚本（推荐）

1. 下载所有文件到同一文件夹
2. 双击运行 `start_memory_tool.bat`
3. 脚本会自动检查依赖并启动程序

### 方法二：手动安装

1. 确保已安装 Python 3.7+
2. 安装依赖包：
   ```bash
   pip install psutil
   ```
3. 运行程序：
   ```bash
   python memory_tool.py
   ```

## 使用说明

### 1. 连接游戏

1. 启动植物大战僵尸游戏
2. 在工具中点击"附加进程"按钮
3. 等待连接成功（状态显示为绿色"已连接"）

### 2. 使用外挂功能

1. 切换到"外挂功能"标签页
2. 勾选需要的功能（如"无限阳光"）
3. 可以自定义阳光和金钱数量
4. 使用"启用所有外挂"快速开启所有功能

### 3. 监控游戏实体

1. 切换到"实体监控"标签页
2. 查看实时的植物和僵尸列表
3. 显示详细的位置和生命值信息

### 4. 内存编辑

1. 切换到"内存编辑"标签页
2. 输入内存地址（支持十六进制，如 0x6A9EC0）
3. 选择数据类型
4. 点击"读取"查看当前值
5. 输入新值并点击"写入"进行修改

## 配置文件

程序使用 `config.json` 文件保存配置：

```json
{
    "window": {
        "width": 900,
        "height": 700,
        "title": "植物大战僵尸内存工具 v1.0"
    },
    "game": {
        "process_name": "PlantsVsZombies.exe",
        "auto_attach": true,
        "scan_interval": 100
    },
    "cheats": {
        "default_sun_amount": 9990,
        "default_money_amount": 99999,
        "auto_enable": []
    }
}
```

## 注意事项

### ⚠️ 重要提醒

1. **管理员权限**：建议以管理员权限运行程序以获得最佳性能
2. **游戏版本**：主要支持原版植物大战僵尸，其他版本可能需要调整内存地址
3. **安全使用**：仅用于单机游戏，请勿用于在线游戏
4. **备份存档**：使用前建议备份游戏存档

### 🔧 故障排除

**问题：无法找到游戏进程**
- 确保游戏正在运行
- 检查进程名称是否正确（默认为 PlantsVsZombies.exe）
- 尝试以管理员权限运行工具

**问题：外挂功能不生效**
- 确保已成功连接到游戏进程
- 检查游戏版本是否兼容
- 查看日志窗口的错误信息

**问题：程序启动失败**
- 检查 Python 版本（需要 3.7+）
- 安装缺失的依赖包：`pip install psutil`
- 确保所有文件在同一目录下

## 文件结构

```
植物大战僵尸内存工具/
├── memory_tool.py          # 主程序文件
├── memory_engine.py        # 内存操作引擎
├── game_definitions.py     # 游戏参数定义
├── cheat_functions.py      # 外挂功能实现
├── utils.py               # 工具函数
├── config.json            # 配置文件
├── start_memory_tool.bat  # 启动脚本
└── README.md              # 说明文档
```

## 技术特性

- **多线程架构**：UI和内存操作分离，保证界面响应
- **安全内存操作**：使用Windows API进行安全的进程内存访问
- **实时监控**：高效的游戏状态实时监控系统
- **错误处理**：完善的异常处理和错误恢复机制
- **配置管理**：灵活的配置文件系统
- **日志记录**：详细的操作日志和调试信息

## 开发信息

- **开发语言**：Python 3.7+
- **GUI框架**：Tkinter
- **内存操作**：Windows API (ctypes)
- **进程管理**：psutil

## 免责声明

本工具仅供学习和研究目的使用。使用者应当：

1. 仅在单机环境下使用
2. 不得用于破坏游戏平衡或影响他人游戏体验
3. 承担使用本工具可能带来的所有风险
4. 遵守相关法律法规和游戏服务条款

开发者不对使用本工具造成的任何损失或后果承担责任。

## 版本历史

### v1.0 (当前版本)
- 基础内存读写功能
- 图形用户界面
- 主要外挂功能实现
- 实体监控系统
- 配置管理系统
- 日志记录功能

---

**享受游戏，合理使用！** 🌻🧟‍♂️
