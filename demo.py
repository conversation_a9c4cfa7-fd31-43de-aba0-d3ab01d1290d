"""
植物大战僵尸内存工具演示脚本
展示工具的主要功能和使用方法
"""

import time
import sys
from game_definitions import PVZDefinitions
from utils import DataFormatter

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🌻 植物大战僵尸内存工具演示 🧟‍♂️")
    print("=" * 60)
    print()

def demo_game_definitions():
    """演示游戏定义功能"""
    print("📋 游戏定义功能演示")
    print("-" * 30)
    
    game_def = PVZDefinitions()
    
    print(f"游戏进程名: {game_def.process_name}")
    from game_definitions import GameVersion
    print(f"支持的游戏版本: {[v.value for v in GameVersion]}")
    print()
    
    print("🌱 植物类型示例:")
    for i in range(5):
        plant_name = game_def.get_plant_type_name(i)
        print(f"  {i}: {plant_name}")
    print()
    
    print("🧟 僵尸类型示例:")
    for i in range(5):
        zombie_name = game_def.get_zombie_type_name(i)
        print(f"  {i}: {zombie_name}")
    print()
    
    print("📊 内存地址分类:")
    categories = game_def.get_all_categories()
    for category in categories:
        addresses = game_def.get_addresses_by_category(category)
        print(f"  {category}: {len(addresses)} 个地址")
    print()

def demo_memory_engine():
    """演示内存引擎功能"""
    print("🔧 内存引擎功能演示")
    print("-" * 30)
    
    print("内存引擎功能:")
    print("  ✓ 进程查找和附加")
    print("  ✓ 内存读写操作")
    print("  ✓ 数据类型转换")
    print("  ✓ 指针链解析")
    print("  ✓ 内存模式扫描")
    print()
    
    # 演示地址格式化
    print("地址格式化示例:")
    addresses = [0x6A9EC0, 0x400000, 0x12345678]
    for addr in addresses:
        formatted = DataFormatter.format_address(addr)
        print(f"  {addr} -> {formatted}")
    print()

def demo_data_formatting():
    """演示数据格式化功能"""
    print("📊 数据格式化功能演示")
    print("-" * 30)
    
    # 数字格式化
    print("数字格式化:")
    numbers = [1234, 1234567, 3.14159, 99.99]
    for num in numbers:
        formatted = DataFormatter.format_number(num)
        print(f"  {num} -> {formatted}")
    print()
    
    # 时间格式化
    print("时间格式化:")
    times = [30.5, 125.7, 3661.2]
    for t in times:
        formatted = DataFormatter.format_time(t)
        print(f"  {t}秒 -> {formatted}")
    print()
    
    # 字节格式化
    print("字节数据格式化:")
    data_samples = [
        b'\x48\x65\x6C\x6C\x6F',  # "Hello"
        b'\x00\x01\x02\x03\x04',
        b'\xFF\xFE\xFD\xFC'
    ]
    for data in data_samples:
        formatted = DataFormatter.format_bytes(data)
        print(f"  {data} -> {formatted}")
    print()

def demo_cheat_features():
    """演示外挂功能"""
    print("🎮 外挂功能演示")
    print("-" * 30)
    
    print("可用的外挂功能:")
    cheats = [
        ("infinite_sun", "无限阳光", "自动维持指定数量的阳光"),
        ("infinite_money", "无限金钱", "自动维持指定数量的金钱"),
        ("plant_invincible", "植物无敌", "植物生命值自动恢复"),
        ("zombie_weak", "僵尸秒杀", "将僵尸生命值设为1"),
        ("freeze_zombies", "冻结僵尸", "将僵尸移动速度设为0"),
        ("no_cooldown", "无冷却时间", "移除植物卡片冷却")
    ]
    
    for cheat_id, name, desc in cheats:
        print(f"  🔹 {name} ({cheat_id})")
        print(f"     {desc}")
    print()

def demo_memory_addresses():
    """演示内存地址信息"""
    print("🎯 内存地址信息演示")
    print("-" * 30)
    
    game_def = PVZDefinitions()
    
    print("重要的游戏内存地址:")
    important_addresses = ["sun_count", "money", "level", "wave_count"]
    
    for addr_name in important_addresses:
        addr_info = game_def.get_address_by_name(addr_name)
        if addr_info:
            print(f"  📍 {addr_info.description} ({addr_name})")
            print(f"     基址: {DataFormatter.format_address(addr_info.address)}")
            print(f"     偏移: {[hex(offset) for offset in addr_info.offsets]}")
            print(f"     类型: {addr_info.data_type}")
            print(f"     分类: {addr_info.category}")
            print()

def demo_entity_structures():
    """演示实体数据结构"""
    print("🏗️ 实体数据结构演示")
    print("-" * 30)
    
    game_def = PVZDefinitions()
    
    print("植物数据结构:")
    for field_name, field_info in list(game_def.plant_structure.items())[:5]:
        offset = field_info["offset"]
        data_type = field_info["type"]
        description = field_info["description"]
        print(f"  {field_name}: +{hex(offset)} ({data_type}) - {description}")
    print(f"  ... 共 {len(game_def.plant_structure)} 个字段")
    print()
    
    print("僵尸数据结构:")
    for field_name, field_info in list(game_def.zombie_structure.items())[:5]:
        offset = field_info["offset"]
        data_type = field_info["type"]
        description = field_info["description"]
        print(f"  {field_name}: +{hex(offset)} ({data_type}) - {description}")
    print(f"  ... 共 {len(game_def.zombie_structure)} 个字段")
    print()

def demo_usage_instructions():
    """演示使用说明"""
    print("📖 使用说明")
    print("-" * 30)
    
    print("启动工具的方法:")
    print("  1. 双击 start_memory_tool.bat (推荐)")
    print("  2. 运行 python memory_tool.py")
    print()
    
    print("使用步骤:")
    print("  1. 🎮 启动植物大战僵尸游戏")
    print("  2. 🔗 在工具中点击'附加进程'")
    print("  3. 📊 查看'游戏状态'标签页的实时数据")
    print("  4. 🎯 在'外挂功能'标签页启用需要的功能")
    print("  5. 👀 在'实体监控'标签页查看植物和僵尸")
    print("  6. ⚙️ 在'内存编辑'标签页进行高级操作")
    print()
    
    print("注意事项:")
    print("  ⚠️ 建议以管理员权限运行")
    print("  ⚠️ 仅支持单机游戏使用")
    print("  ⚠️ 使用前请备份游戏存档")
    print("  ⚠️ 主要支持原版植物大战僵尸")
    print()

def main():
    """主演示函数"""
    print_banner()
    
    demos = [
        ("游戏定义功能", demo_game_definitions),
        ("内存引擎功能", demo_memory_engine),
        ("数据格式化功能", demo_data_formatting),
        ("外挂功能", demo_cheat_features),
        ("内存地址信息", demo_memory_addresses),
        ("实体数据结构", demo_entity_structures),
        ("使用说明", demo_usage_instructions)
    ]
    
    for i, (title, demo_func) in enumerate(demos, 1):
        print(f"{i}. {title}")
        demo_func()
        
        if i < len(demos):
            input("按回车键继续下一个演示...")
            print()
    
    print("=" * 60)
    print("🎉 演示完成！")
    print()
    print("现在你可以:")
    print("  • 运行 python memory_tool.py 启动图形界面")
    print("  • 双击 start_memory_tool.bat 快速启动")
    print("  • 查看 README.md 了解详细使用说明")
    print()
    print("享受游戏，合理使用工具！🌻🧟‍♂️")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n\n演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
