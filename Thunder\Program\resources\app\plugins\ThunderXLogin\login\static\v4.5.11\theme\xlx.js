!function(e){function t(i){if(n[i])return n[i].exports;var r=n[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};t.m=e,t.c=n,t.d=function(e,n,i){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:i})},t.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t.h="076023064fa53798dcba",t.cn="xlx",t(t.s=254)}([function(e,t,n){"use strict";t.__esModule=!0,t["default"]=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t){var n=e.exports={version:"2.6.11"};"number"==typeof __e&&(__e=n)},function(e,t,n){var i=n(34)("wks"),r=n(22),o=n(1).Symbol,a="function"==typeof o;(e.exports=function(e){return i[e]||(i[e]=a&&o[e]||(a?o:r)("Symbol."+e))}).store=i},function(e,t,n){e.exports=!n(14)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t,n){var i=n(6);e.exports=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,n){var i=n(1),r=n(2),o=n(19),a=n(9),s=n(10),c=function(e,t,n){var l,u,d,f=e&c.F,p=e&c.G,_=e&c.S,g=e&c.P,h=e&c.B,v=e&c.W,m=p?r:r[t]||(r[t]={}),b=m.prototype,y=p?i:_?i[t]:(i[t]||{}).prototype;p&&(n=t);for(l in n)(u=!f&&y&&y[l]!==undefined)&&s(m,l)||(d=u?y[l]:n[l],m[l]=p&&"function"!=typeof y[l]?n[l]:h&&u?o(d,i):v&&y[l]==d?function(e){var t=function(t,n,i){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,i)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(d):g&&"function"==typeof d?o(Function.call,d):d,g&&((m.virtual||(m.virtual={}))[l]=d,e&c.R&&b&&!b[l]&&a(b,l,d)))};c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},function(e,t,n){var i=n(5),r=n(47),o=n(36),a=Object.defineProperty;t.f=n(4)?Object.defineProperty:function(e,t,n){if(i(e),t=o(t,!0),i(n),r)try{return a(e,t,n)}catch(s){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){var i=n(8),r=n(20);e.exports=n(4)?function(e,t,n){return i.f(e,t,r(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,n){"use strict";(function(t){function i(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){return function(){return t.apply(e,Array.prototype.slice.call(arguments,0))}}function o(e,t){return Array.prototype.slice.call(e,t||0)}function a(e,t){c(e,function(e,n){return t(e,n),!1})}function s(e,t){var n=l(e)?[]:{};return c(e,function(e,i){return n[i]=t(e,i),!1}),n}function c(e,t){if(l(e)){for(var n=0;n<e.length;n++)if(t(e[n],n))return e[n]}else for(var i in e)if(e.hasOwnProperty(i)&&t(e[i],i))return e[i]}function l(e){return null!=e&&"function"!=typeof e&&"number"==typeof e.length}function u(e){return e&&"[object Function]"==={}.toString.call(e)}function d(e){return e&&"[object Object]"==={}.toString.call(e)}var f=n(95),p=i(f),_=n(56),g=i(_),h=function(){return g["default"]?g["default"]:function(e,t,n,i){for(var r=1;r<arguments.length;r++)a(Object(arguments[r]),function(t,n){e[n]=t});return e}}(),v=function(){if(p["default"])return function(e,t,n,i){var r=o(arguments,1);return h.apply(this,[(0,p["default"])(e)].concat(r))};var e=function(){};return function(t,n,i,r){var a=o(arguments,1);return e.prototype=t,h.apply(this,[new e].concat(a))}}(),m=function(){return String.prototype.trim?function(e){return String.prototype.trim.call(e)}:function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}}(),b="undefined"!=typeof window?window:t;e.exports={assign:h,create:v,trim:m,bind:r,slice:o,each:a,map:s,pluck:c,isList:l,isFunction:u,isObject:d,Global:b}}).call(t,n(55))},function(e,t,n){e.exports={"default":n(72),__esModule:!0}},function(e,t,n){var i=n(41),r=n(29);e.exports=function(e){return i(r(e))}},function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0;var r=n(112),o=i(r),a=n(114),s=i(a),c="function"==typeof s["default"]&&"symbol"==typeof o["default"]?function(e){return typeof e}:function(e){return e&&"function"==typeof s["default"]&&e.constructor===s["default"]&&e!==s["default"].prototype?"symbol":typeof e};t["default"]="function"==typeof s["default"]&&"symbol"===c(o["default"])?function(e){return void 0===e?"undefined":c(e)}:function(e){return e&&"function"==typeof s["default"]&&e.constructor===s["default"]&&e!==s["default"].prototype?"symbol":void 0===e?"undefined":c(e)}},function(e,t){e.exports=!0},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t){e.exports={}},function(e,t,n){var i=n(21);e.exports=function(e,t,n){if(i(e),t===undefined)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,i){return e.call(t,n,i)};case 3:return function(n,i,r){return e.call(t,n,i,r)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(e===undefined?"":e,")_",(++n+i).toString(36))}},function(e,t,n){"use strict";(function(e){function n(){return window||e}function i(e){var t="";switch(e){case u.insideIframe:case u.outsideIframe:case u.node:t=e;break;default:t="default"}return t+"WebSdkGlobalObject_CA7FFF8A_0F5B_4654_822B_98B9E74F23DD"}function r(e){if(null===d){f=e;var t=i(e);d={},n()[t]=d}}function o(){if(null===d){var e=n();d=e[i(u.insideIframe)]||e[i(u.outsideIframe)]||e[i(u.pluginIndex)]}return d}function a(e){return o()[e]!==undefined}function s(e,t){o()[e]=t}function c(e){return o()[e]}function l(){return f}t.__esModule=!0,t.getGBObjName=i,t.init=r,t.hasAttr=a,t.setAttr=s,t.getAttr=c,t.getEnvType=l;var u=(t.gbAttrNames={config:"config",stat:"stat",platformInfo:"platformInfo",innerQuickLogin:"innerQuickLogin",clientFeatureApi:"clientFeatureApi"},t.gbEnvTypes={insideIframe:"insideIframe",outsideIframe:"outsideIframe",pluginIndex:"pluginIndex"}),d=null,f=undefined}).call(t,n(55))},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){for(var n=-1,i=null==e?0:e.length;++n<i&&!1!==t(e[n],n,e););return e}function o(e,t){for(var n=-1,i=null==e?0:e.length,r=Array(i);++n<i;)r[n]=t(e[n],n,e);return r}function a(e){return document.getElementById(e)}function s(){return(65536*(1+Math.random())|0).toString(16).substring(1)}function c(e){for(var t="",n=e/4,i=0;i<n;i++)t+=s();return t}function l(e,t){undefined;return t>e.length?new Array(t-e.length+1).join("0")+e:e}function u(e){return e.getFullYear().toString()+l((e.getMonth()+1).toString(),2)+l(e.getDate().toString(),2)}t.__esModule=!0,t.BrowserVersion=t.BrowserType=t.mThunder=t.isWeixin=t.isXlMac=t.isXlx=t.isXl9=t.isXlMB=t.isXlPC=t.isMobile=t.UA=undefined;var d=n(15),f=i(d),p=n(0),_=i(p);t.id=a,t.S4=s,t.Guid=c,t.dateToDateString=u;var g=n(62),h=function(){function e(){(0,_["default"])(this,e),this.every=this.genLoop(),this.some=this.genLoop(!1)}return e.prototype.forEach=function(e,t){return r(e,t)},e.prototype.map=function(e,t){return o(e,t)},e.prototype.genLoop=function(){var e=!(arguments.length>0&&arguments[0]!==undefined)||arguments[0];return function(t,n){for(var i=-1,r=null==t?0:t.length;++i<r;){var o=n(t[i],i,t);if(0==e&&1==o)return!0;if(1==e&&0==o)return!1}return e}},e.prototype.has=function(e,t){return null!=e&&hasOwnProperty.call(e,t)},e.prototype.isEmpty=function(e){return e!=undefined&&("string"!=typeof e||!e.length)},e.prototype.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},e.prototype.decode=function(e){try{e=decodeURIComponent(e)}catch(t){e=unescape(e)}return e},e.prototype.escape=function(e){return e.replace(/([.*+?\^${}()|\[\]\/\\])/g,"\\$1")},e.prototype.getCookie=function(e){var t=document.cookie.match(RegExp("(?:^|;\\s*)"+this.escape(e)+"=([^;]*)"));return t?t[1]:""},e}(),v=t.UA=navigator.userAgent.toLocaleLowerCase(),m=t.isMobile=/mobile|android|iphone|ipad|iemobile/i.test(v),b=(t.isXlPC=/[ ]thunder(x?)\/[\d.]*/.test(v)||!!window["native"]&&!m,t.isXlMB=(/thunder/.test(v)||"undefined"!=typeof XLJSWebViewBridgeExport)&&m,t.isXl9=(0,g.checkIsXl9)(),t.isXlx=(0,g.checkIsXlx)(),t.isXlMac=/\bmac\b/.test(v)&&!!window.WebViewJavascriptBridge,t.isWeixin=/micromessenger/.test(v),t.mThunder=function(){return"undefined"!=typeof window.webkit&&(0,f["default"])(window.webkit.messageHandlers)&&"undefined"!=typeof window.webkit.messageHandlers.XLJSWebViewBridgeExport?{type:"ios",event:"XLJSWebViewBridgeExport"}:"undefined"!=typeof window.webkit&&(0,f["default"])(window.webkit.messageHandlers)&&"undefined"!=typeof window.webkit.messageHandlers.XLJSWebViewBridge?{type:"ios",event:"XLJSWebViewBridge"}:"undefined"!=typeof window.XLJSWebViewBridgeExport?{type:"android",event:"XLJSWebViewBridgeExport"}:"undefined"!=typeof window.XLJSWebViewBridge&&{type:"android",event:"XLJSWebViewBridge"}},["Trident","Electron","Android.Thunder","IOS.Thunder","Thunder","MicroMessenger","360","MQQBrowser","QQBrowser","UCBrowser","UBrowser","Metasr","Edge","Firefox","Maxthon","Chrome","Safari","mobile"]),y=function(e,t){var n=navigator.mimeTypes;for(var i in n)if(n[i][e]==t)return!0;return!1},w=function(){for(var e=b.length-1,t=0;t<=e;t++){var n=b[t];if("360"!==n){if(v.indexOf(n.toLowerCase())>-1)return n}else if(y("type","application/vnd.chromium.remoting-viewer"))return n}return"unknown"}(),x=(t.BrowserType=function(){return(m?"Mobile-":"PC-")+("Trident"==w?"IE":w)}(),t.BrowserVersion=function(){var e=w.toLowerCase();if(e.indexOf("unknown")>0)return"unknown";if("trident"==e)return v.indexOf("gecko")>0?"IE/11":"IE/"+v.match(/msie (\d+)/)[1];"360"!=e&&"android.thunder"!=e||(e="chrome"),"ios.thunder"==e&&(e="mobile");var t=new RegExp(e+"[ /][\\d.]+"),n=v.match(t);return n&&n.length>=0?n[0]:"unknown"}(),new h);t["default"]=x},function(e,t,n){var i=n(48),r=n(35);e.exports=Object.keys||function(e){return i(e,r)}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,n){var i=n(8).f,r=n(10),o=n(3)("toStringTag");e.exports=function(e,t,n){e&&!r(e=n?e:e.prototype,o)&&i(e,o,{configurable:!0,value:t})}},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}function r(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:function(e){return e},n={};return g["default"].forEach(e,function(e){var i=e.split("="),r=i.shift(),o=i.join("=");n[r]=t(o)}),n}function o(e){return"object"===("undefined"==typeof HTMLElement?"undefined":(0,f["default"])(HTMLElement))?e instanceof HTMLElement:e&&"object"===(void 0===e?"undefined":(0,f["default"])(e))&&null!==e&&1===e.nodeType&&"string"==typeof e.nodeName}function a(e,t){var n;n="string"==typeof e?w["class"](e):e,o(n)?n.style.display=t:g["default"].forEach(n,function(e){e.style.display=t})}function s(e){if(document.all)e.click();else{var t=document.createEvent("MouseEvent");t.initEvent("click",!0,!0),e.dispatchEvent(t)}}function c(e){return 0!==e.clientWidth&&0!==e.clientHeight&&0!==e.style.opacity&&"hidden"!==e.style.visibility}t.__esModule=!0,t.TIMEOUT=undefined;var l=n(0),u=i(l),d=n(15),f=i(d);t._display=a,t._click=s,t._visible=c;var p=n(97),_=n(24),g=i(_),h=n(96),v=i(h),m=void 0,b=!1,y=(t.TIMEOUT="TIMEOUT",function(){function e(){(0,u["default"])(this,e),this.id=_.id,this.binders=[],this.bind=this.genBind(_.id,window),this.unbind=this.genUnBind(_.id),this.loadScript=this.genLoadScript(document),this.getJson=this.genGetJson(window,document),this.loadStyle=this.genLoadScript(document,"link",{rel:"stylesheet",type:"text/css",media:"all"}),this.isJumpkey=function(e){return!(!e||!e.length||192!==e.length)},this["class"]=function(e){return document.getElementsByClassName(e)},this.text=function(e,t){if(e){var n=!1;if(e.innerText!==m)n="innerText";else if(e.textContent!==m)n="textContent";else{if(e.value===m)throw new Error("not support dom innerText or textContent");n="value"}return t===m?e[n]:e[n]=t}},this.checkCss=function(e){return"css"==e.split(".").pop().split("?")[0]},this.checkMobile=function(e){return/^1[\d]{10}$/.exec(e)},this.checkOverseasMobile=function(e){return/^00[0-9]{8,15}$/.exec(e)},this.checkPassword=function(e){return/^(?![\d]+$)(?![a-zA-Z]+$)(?![\`\-\=\[\]\\\;\'\,\.\/\~!@#$%^&*()_+{}|:"<>?]+$)[\da-zA-Z\`\-\=\[\]\\\;\'\,\.\/\~!@#$%^&*()_+{}|:"<>?]{6,16}$/.exec(e)},this.checkMail=function(e){return/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.exec(e)},this.trim=function(e){return e.replace(/(^\s*)|(\s*$)/g,"")},this.getConfig=function(e){if((e=e.toUpperCase())in p.CONFIG)return p.CONFIG[e]},this.isClientLogin=function(){return!b&&p.CONFIG.SET_ROOT_DOMAIN?parent.xlQuickLogin.isClientLogin:b}}return e.prototype.randString=function(){return Math.random().toString(36).replace(/[^a-z0-9]+/g,"")},e.prototype.isSessionid=function(e){return!(!e||!e.length)},e.prototype.getCookie=function(e){var t=!(arguments.length>1&&arguments[1]!==undefined)||arguments[1],n=document.cookie,i=r(n.split("; "),function(e){return t&&e!==m&&(e=g["default"].decode(e)),e});if(e){var o=i[e];return o==m?"":o}return i},e.prototype.setCookie=function(e,t,n){var i,r=arguments.length>3&&arguments[3]!==undefined?arguments[3]:p.CONFIG.DOMAIN,o=arguments[4],a=arguments[5],n=!!n&&new Date((new Date).getTime()+n).toGMTString();i=e+"="+escape(t),i+="; path="+(o||"/"),i+="; domain="+r,a&&(i+="; secure"),n&&(i+="; expires="+n),document.cookie=i},e.prototype.delCookie=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:p.CONFIG.DOMAIN,n=arguments[2],i=arguments[3];this.setCookie(e,"",-6e4,t,n,i)},e.prototype.genBind=function(e,t){var n=this;return function(i,r,o,a){function s(e){e=e||t.event,e.target||(e.target=e.srcElement,e.preventDefault=function(){this.returnValue=!1},e.stopPropagation=function(){this.cancelBubble=!0}),!1===o.call(a||this,e)&&(e.preventDefault(),e.stopPropagation())}if("function"==typeof o){if("string"==typeof i&&(i=e(i)),!i)throw new Error("bind on an undefined target");var c=r.split(".").shift();n.binders.push({obj:i,handler:s,type:r}),i.attachEvent?i.attachEvent("on"+c,s):i.addEventListener&&i.addEventListener(c,s,!1)}}},e.prototype.genUnBind=function(e){var t=this;return function(n,i){if("string"==typeof n&&(n=e(n)),!n)throw new Error("unbind on an undefined target");var r,o,a,s,c,l;for(c=t.binders.length-1;c>=0;c--)r=t.binders[c],r.obj===n&&(o=r.type.split("."),a=o.shift(),s=o.length>0&&o.join("."),(r.type===i||i===a||!1!==s&&s===i)&&(t.binders.splice(c,1),l=r,n.detachEvent?n.detachEvent("on"+a,r.handler):n.removeEventListener&&n.removeEventListener(a,r.handler,!1)));return l}},e.prototype.genLoadScript=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"script",n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{type:"text/javascript",language:"javascript"};return function(i,r,o){var a=e.createElement(t),s=!1;a.href=i,a.src=i;for(var c in n)a[c]=n[c],a.setAttribute(c,n[c]);for(var l in o)a[l]=o[l],a.setAttribute(l,o[l]);return a.onload=a.onreadystatechange=function(){s||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(s=!0,"function"==typeof r&&r())},e.getElementsByTagName("head")[0].appendChild(a),a}},e.prototype.getUrlParams=function(e){return r(e.substring(e.indexOf("?")+1,-1==e.indexOf("#")?e.length:e.indexOf("#")).split("&"))},e.prototype.registerPost=function(e,t,n){var i,r,o,a,s=this,c="http://i."+p.CONFIG.DOMAIN+"/login/2.5/post_callback.html",l="_submitIframe_"+Math.round(1e3*Math.random());p.CONFIG.SET_ROOT_DOMAIN&&p.CONFIG.DOMAIN;!0!==p.CONFIG.ALL_HTTPS&&"https:"!==location.protocol||(c=c.replace("http","https")),n||(n=function(){});var u="_"+Math.round(1e16*Math.random());window[u]="string"==typeof n?window[n]:n,i=document.createElement("form"),i.id="_postFrom_"+Math.round(1e3*Math.random()),i.style.display="none",i.style.position="absolute",i.method="post",i.action=e+"?domain="+p.CONFIG.DOMAIN+"&iframeUrl="+encodeURIComponent(c)+"&callback="+u+"&csrf_token="+s.getCsrfToken(),i.target=l,i.enctype="application/x-www-form-urlencoded",i.acceptCharset="UTF-8",t.domain=p.CONFIG.DOMAIN,t.response="iframe";for(o in t)r=document.createElement("input"),r.name=o,r.value=t[o],r.type="hidden",i.appendChild(r);document.body.appendChild(i);try{a=document.createElement("<iframe name='' + iframeId + ''>")}catch(f){a=document.createElement("iframe"),a.name=l}a.id=l,a.style.display="none",i.appendChild(a),document.body.appendChild(a);var d="_remove"+u;window[d]=function(){a.parentNode.removeChild(a),i.parentNode.removeChild(i),a=null,i=null,window[u]=null,window[d]=null},i.submit()},e.prototype.getCsrfToken=function(){return(0,v["default"])(this.getCookie("deviceid").slice(0,32))},e.prototype.appendUri=function(e,t){var n,i=[];for(n in t)i.push(n+"="+t[n]);return i=i.join("&"),e=-1===e.indexOf("?")?e+"?"+i:e+"&"+i},e.prototype.inArray=function(e,t){if("object"!=(void 0===t?"undefined":(0,f["default"])(t)))return!1;var n;for(n in t)if(t[n]===e)return!0;return!1},e.prototype.delVerifies=function(){var e=this;e.delCookie("VERIFY_KEY",p.CONFIG.DOMAIN),e.delCookie("verify_type",p.CONFIG.DOMAIN),e.delCookie("check_n",p.CONFIG.DOMAIN),e.delCookie("check_e",p.CONFIG.DOMAIN),e.delCookie("logindetail",p.CONFIG.DOMAIN),e.delCookie("result",p.CONFIG.DOMAIN)},e.prototype.genGetJson=function(e,t){return function(n,i,r,o){var a=t.createElement("script"),s=t.getElementsByTagName("head")[0],c="jsonp"+(new Date).getTime(),l=[];o=o||"callback";for(var u in i)l.push(u+"="+i[u]);l.push(o+"="+c),n+=(n.indexOf("?")>=0?"&":"?")+l.join("&"),e[c]=function(t){"string"==typeof r?e[r].call(e,t):r.call(e,t);try{delete e[c]}catch(n){}s.removeChild(a)},a.src=n,s.insertBefore(a,s.firstChild)}},e.prototype.isMainlandPhone=function(e){return!e||"0086"===e},e}()),w=new y;t["default"]=w},function(e,t){e.exports=function(e){if(e==undefined)throw TypeError("Can't call method on  "+e);return e}},function(e,t){var n=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:n)(e)}},function(e,t,n){var i=n(34)("keys"),r=n(22);e.exports=function(e){return i[e]||(i[e]=r(e))}},function(e,t,n){var i=n(29);e.exports=function(e){return Object(i(e))}},function(e,t,n){var i=n(6),r=n(1).document,o=i(r)&&i(r.createElement);e.exports=function(e){return o?r.createElement(e):{}}},function(e,t,n){var i=n(2),r=n(1),o=r["__core-js_shared__"]||(r["__core-js_shared__"]={});(e.exports=function(e,t){return o[e]||(o[e]=t!==undefined?t:{})})("versions",[]).push({version:i.version,mode:n(16)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){var i=n(6);e.exports=function(e,t){if(!i(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!i(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,n){"use strict";t.__esModule=!0,t.GBHelper=undefined;var i=n(0),r=function(e){return e&&e.__esModule?e:{"default":e}}(i),o=n(23),a=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}(o);t.GBHelper=function(){function e(t){(0,r["default"])(this,e),this._attrName=t,this._target=undefined}return e.prototype.getTarget=function(){return this._target===undefined&&(a.hasAttr(this._attrName)?this._target=a.getAttr(this._attrName):this.setTarget({})),this._target},e.prototype.setTarget=function(e){a.setAttr(this._attrName,e),this._target=a.getAttr(this._attrName)},e.prototype.hasAttr=function(e){return this.getTarget()[e]!==undefined},e.prototype.setAttr=function(e,t){this.getTarget()[e]=t},e.prototype.getAttr=function(e){return this.getTarget()[e]},e}()},function(e,t,n){var i=n(5),r=n(87),o=n(35),a=n(31)("IE_PROTO"),s=function(){},c=function(){var e,t=n(33)("iframe"),i=o.length;for(t.style.display="none",n(59).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),c=e.F;i--;)delete c.prototype[o[i]];return c()};e.exports=Object.create||function(e,t){var n;return null!==e?(s.prototype=i(e),n=new s,s.prototype=null,n[a]=e):n=c(),t===undefined?n:r(n,t)}},function(e,t,n){e.exports={"default":n(105),__esModule:!0}},function(e,t,n){var i=n(17);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==i(e)?e.split(""):Object(e)}},function(e,t,n){"use strict";function i(e){var t,n;this.promise=new e(function(e,i){if(t!==undefined||n!==undefined)throw TypeError("Bad Promise constructor");t=e,n=i}),this.resolve=r(t),this.reject=r(n)}var r=n(21);e.exports.f=function(e){return new i(e)}},function(e,t,n){t.f=n(3)},function(e,t,n){var i=n(1),r=n(2),o=n(16),a=n(43),s=n(8).f;e.exports=function(e){var t=r.Symbol||(r.Symbol=o?{}:i.Symbol||{});"_"==e.charAt(0)||e in t||s(t,e,{value:a.f(e)})}},function(e,t,n){var i=n(30),r=Math.min;e.exports=function(e){return e>0?r(i(e),9007199254740991):0}},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}function r(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"",n=t;do{if(null===e||e===undefined)break;switch(void 0===e?"undefined":(0,_["default"])(e)){case"string":n=e;break;case"number":case"boolean":n=e.toString()}}while(!1);return n}function o(e){var t={};do{if(null===e||e===undefined){t={};break}switch(void 0===e?"undefined":(0,_["default"])(e)){case"object":for(var n in e)t[n]=o(e[n]);break;case"string":case"number":case"boolean":t=r(e,"")}}while(!1);return t}function a(e,t){var n=e;for(var i in t){var r=t[i];if("object"===(void 0===r?"undefined":(0,_["default"])(r))){var o=n[i];o===undefined||null===o?n[i]=r:n[i]=s(n[i],r)}else r!==undefined&&(n[i]=r)}return n}function s(e,t){return a(JSON.parse((0,f["default"])(e)),JSON.parse((0,f["default"])(t)))}function c(e){return e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()}function l(e){var t=null;try{t=JSON.parse(e)}catch(n){t=null}return t}function u(e){var t=undefined;try{t=(0,f["default"])(e)}catch(n){t=undefined}return t}t.__esModule=!0;var d=n(12),f=i(d),p=n(15),_=i(p);t.forceToString=r,t.forceJsonSimpleValueToString=o,t.combineJsonObject=s,t.dateToTimeString=c,t.parseJson=l,t.stringifyJson=u},function(e,t,n){e.exports=!n(4)&&!n(14)(function(){return 7!=Object.defineProperty(n(33)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){var i=n(10),r=n(13),o=n(75)(!1),a=n(31)("IE_PROTO");e.exports=function(e,t){var n,s=r(e),c=0,l=[];for(n in s)n!=a&&i(s,n)&&l.push(n);for(;t.length>c;)i(s,n=t[c++])&&(~o(l,n)||l.push(n));return l}},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}function r(e){return{0:"PC",1:"WEB",3:"WAP",4:"MAC",10:"ANDROID",12:"LINUX"}[e]||"PC"}function o(e){var t=window.APPID!=undefined?window.APPID:"";if(v["default"].getCookie("appinfo")){t=JSON.parse(v["default"].getCookie("appinfo")).appid}return(0,p["default"])({v:"202",appid:t,appname:u(),devicemodel:m.BrowserVersion,devicename:m.BrowserType,hl:x.gbConfig.getHL(),osversion:navigator.platform,providername:C,networktype:C,sdkversion:x.gbConfig.getSDKVersion(),clientversion:x.gbConfig.getAppVersion(),devicesign:s(),platform:d(),entrance:A.getAttr(S.gbOtherInfoAttrNames.showLoginWndSource),format:T?"json":"cookie"},e)}function a(e){var t=window.APPID!=undefined?window.APPID:"";if(v["default"].getCookie("appinfo")){t=JSON.parse(v["default"].getCookie("appinfo")).appid}return""!=t&&t!=undefined||(t=v["default"].getCookie("appidstack").split(",").pop()),(0,p["default"])({appid:t,appName:u(),deviceModel:m.BrowserVersion,deviceName:m.BrowserType,hl:x.gbConfig.getHL(),OSVersion:navigator.platform,provideName:C,netWorkType:C,providerName:C,sdkVersion:x.gbConfig.getSDKVersion(),clientVersion:x.gbConfig.getAppVersion(),protocolVersion:"300",devicesign:s(),entrance:A.getAttr(S.gbOtherInfoAttrNames.showLoginWndSource),platformVersion:E.PlatformMap(),fromPlatformVersion:E.PlatformMap(),format:T?"json":"cookie",timestamp:(new Date).getTime()},e)}function s(){var e=v["default"].getCookie("deviceid");return e&&e!=undefined&&e.length>20?e:w["default"].enabled&&w["default"].has("deviceid")&&w["default"].get("deviceid").length>20?w["default"].get("deviceid").replace(/'/g,""):""}function c(e){var t=window.APPID!=undefined?window.APPID:"";if(v["default"].getCookie("appinfo")){t=JSON.parse(v["default"].getCookie("appinfo")).appid}return(0,p["default"])({appid:t,appName:u(),deviceModel:m.BrowserVersion,deviceName:m.BrowserType,hl:x.gbConfig.getHL(),OSVersion:navigator.platform,providerName:C,netWorkType:C,sdkVersion:x.gbConfig.getSDKVersion(),protocolVersion:"300",clientVersion:x.gbConfig.getAppVersion(),devicesign:s(),platformVersion:E.PlatformMap(),format:T?"json":"cookie"},e)}function l(e){var t=window.APPID!=undefined?window.APPID:"";if(v["default"].getCookie("appinfo")){t=JSON.parse(v["default"].getCookie("appinfo")).appid}return(0,p["default"])({appid:t,appName:u(),deviceModel:m.BrowserVersion,deviceName:m.BrowserType,hl:x.gbConfig.getHL(),OSVersion:navigator.platform,providerName:C,netWorkType:C,sdkVersion:x.gbConfig.getSDKVersion(),protocolVersion:"3.1",clientVersion:x.gbConfig.getAppVersion(),devicesign:s(),platformVersion:d(),format:T?"json":"cookie"},e)}function u(){var e=void 0,t=null;v["default"].getCookie("appinfo")&&(t=JSON.parse(v["default"].getCookie("appinfo")));var n=window.APPNAME||!1;try{if(e=window.parent.location.host,n&&(e=n),t&&(e=t.appname||t["package"]),!/^(IOS|ANDROID|MiniProgram|PC|LINUX)-/.test(e)){var i="";i=m.isMobile?"WAP":(0,b.checkAsPCFlow)()?"PC":d(),e=i+"-"+e}}catch(r){e="unknown"}return e}function d(){return m.isMobile?"WAP":(0,b.checkAsPCFlow)()?"PC":r(new URL(window.location.href).searchParams.get("platformVersion")||new URL(window.parent.location.href).searchParams.get("platformVersion"))}t.__esModule=!0;var f=n(52),p=i(f),_=n(0),g=i(_);t.baseParams=o,t.baseParams2=a,t.deviceid=s,t.baseParamsBeta=c,t.baseParamsRegister=l,t.getHostCategoryAppName=u,t.Platform=d;var h=n(28),v=i(h),m=n(24),b=(i(m),n(62)),y=n(77),w=i(y),x=n(60),S=n(123),L=n(38),T=(m.isXlMac,"file:"===location.protocol),A=new L.GBHelper(S.gbAttrNames.otherInfo),k=function(){function e(){(0,g["default"])(this,e),this.version=m.BrowserVersion,this.type=m.BrowserType,this.platform=d(),this.appname=u()}return e.prototype.PlatformMap=function(){var e={PC:"0",WEB:"1",WAP:"3",MAC:"4",ANDROID:"10",LINUX:"12"};return e[this.platform]?e[this.platform]:1},e}(),E=new k;t["default"]=E;var C="NONE"},function(e,t,n){"use strict";var i=n(85)(!0);n(53)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:undefined,done:!0}:(e=i(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){"use strict";function i(){var e=location.host.split("."),t=e.length;return e[t-2]+"."+e[t-1]}function r(){var e=i();return"file:"===location.protocol?[!0,e]:(document.domain!=e&&(document.domain=e),[!0,e])}t.__esModule=!0,t.getDomain=i,t.checkDomainAllowed=r},function(e,t,n){"use strict";t.__esModule=!0;var i=n(56),r=function(e){return e&&e.__esModule?e:{"default":e}}(i);t["default"]=r["default"]||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}},function(e,t,n){"use strict";var i=n(16),r=n(7),o=n(58),a=n(9),s=n(18),c=n(86),l=n(27),u=n(88),d=n(3)("iterator"),f=!([].keys&&"next"in[].keys()),p=function(){return this};e.exports=function(e,t,n,_,g,h,v){c(n,t,_);var m,b,y,w=function(e){if(!f&&e in T)return T[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},x=t+" Iterator",S="values"==g,L=!1,T=e.prototype,A=T[d]||T["@@iterator"]||g&&T[g],k=A||w(g),E=g?S?w("entries"):k:undefined,C="Array"==t?T.entries||A:A;if(C&&(y=u(C.call(new e)))!==Object.prototype&&y.next&&(l(y,x,!0),i||"function"==typeof y[d]||a(y,d,p)),S&&A&&"values"!==A.name&&(L=!0,k=function(){return A.call(this)}),i&&!v||!f&&!L&&T[d]||a(T,d,k),s[t]=k,s[x]=p,g)if(m={values:S?k:w("values"),keys:h?k:w("keys"),entries:E},v)for(b in m)b in T||o(T,b,m[b]);else r(r.P+r.F*(f||L),t,m);return m}},function(e,t,n){n(89);for(var i=n(1),r=n(9),o=n(18),a=n(3)("toStringTag"),s="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),c=0;c<s.length;c++){var l=s[c],u=i[l],d=u&&u.prototype;d&&!d[a]&&r(d,a,l),o[l]=o.Array}},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(i){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){e.exports={"default":n(78),__esModule:!0}},function(e,t){},function(e,t,n){e.exports=n(9)},function(e,t,n){var i=n(1).document;e.exports=i&&i.documentElement},function(e,t,n){"use strict";t.__esModule=!0,t.gbConfig=undefined;var i=n(0),r=function(e){return e&&e.__esModule?e:{"default":e}}(i),o=n(23),a=n(38),s={sdkVersion:"sdkVersion",appId:"appId",appName:"appName",hostCategoryAppName:"hostCategoryAppName",appVersion:"appVersion",hL:"hL",analysisServer:"analysisServer",syncPC:"syncPC",clientFeature:"clientFeature"},c=function(){function e(){(0,r["default"])(this,e),this._gbHelper=new a.GBHelper(o.gbAttrNames.config)}return e.prototype.setSDKVersion=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.sdkVersion,t)},e.prototype.getSDKVersion=function(){return this._gbHelper.getAttr(s.sdkVersion)},e.prototype.setAppId=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.appId,t)},e.prototype.getAppId=function(){return this._gbHelper.getAttr(s.appId)},e.prototype.setAppName=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.appName,t)},e.prototype.getAppName=function(){return this._gbHelper.getAttr(s.appName)},e.prototype.setHostCategoryAppName=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.hostCategoryAppName,t)},e.prototype.getHostCategoryAppName=function(){return this._gbHelper.getAttr(s.hostCategoryAppName)},e.prototype.setAppVersion=function(e){var t="string"==typeof e&&e.length>0?e:"NONE";this._gbHelper.setAttr(s.appVersion,t)},e.prototype.getAppVersion=function(){return this._gbHelper.getAttr(s.appVersion)},e.prototype.setHL=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(s.hL,t)},e.prototype.getHL=function(){return this._gbHelper.getAttr(s.hL)},e.prototype.setAnalysisServer=function(e){var t=e;this._gbHelper.setAttr(s.analysisServer,t)},e.prototype.getAnalysisServer=function(){return this._gbHelper.getAttr(s.analysisServer)},e.prototype.setSyncPC=function(e){var t=!0===e;this._gbHelper.setAttr(s.syncPC,t)},e.prototype.getSyncPC=function(){return this._gbHelper.getAttr(s.syncPC)},e.prototype.setClientFeature=function(e){var t=!0===e;this._gbHelper.setAttr(s.clientFeature,t)},e.prototype.getClientFeature=function(){return this._gbHelper.getAttr(s.clientFeature)},e}();t.gbConfig=new c},function(e,t,n){var i=n(17),r=n(3)("toStringTag"),o="Arguments"==i(function(){return arguments}()),a=function(e,t){try{return e[t]}catch(n){}};e.exports=function(e){var t,n,s;return e===undefined?"Undefined":null===e?"Null":"string"==typeof(n=a(t=Object(e),r))?n:o?i(t):"Object"==(s=i(t))&&"function"==typeof t.callee?"Arguments":s}},function(e,t,n){"use strict";function i(){return d===undefined&&(d=/\bedge\b/.test(u)),d}function r(){return f===undefined&&(f=/[ ]thunder\/10.0([\d.]*)/.test(u)||/[ ]thunder( )?\/( )?9.([\d.]*)/.test(u)),f}function o(){return p===undefined&&(p=/\bthunder\/10.[1-9][\d.]*/.test(u)),p}function a(){return _===undefined&&(_=/\belectron\/\d+(\.\d+){2}\b/.test(u)),_}function s(){return g===undefined&&(g=/\btbc\/\d+(\.\d+){3}\b/.test(u)&&!!window["native"]),g}function c(){return h===undefined&&(h=o()||!r()&&s()),h}function l(){return v===undefined&&(v="file:"===location.protocol),v}t.__esModule=!0,t.checkIsEdge=i,t.checkIsXl9=r,t.checkIsXlx=o,t.checkIsXdas=a,t.checkIsTbc=s,t.checkAsPCFlow=c,t.checkIsLocal=l;var u=navigator.userAgent.toLocaleLowerCase(),d=undefined,f=undefined,p=undefined,_=undefined,g=undefined,h=undefined,v=undefined},function(e,t,n){var i=n(5),r=n(21),o=n(3)("species");e.exports=function(e,t){var n,a=i(e).constructor;return a===undefined||(n=i(a)[o])==undefined?t:r(n)}},function(e,t,n){var i,r,o,a=n(19),s=n(107),c=n(59),l=n(33),u=n(1),d=u.process,f=u.setImmediate,p=u.clearImmediate,_=u.MessageChannel,g=u.Dispatch,h=0,v={},m=function(){var e=+this;if(v.hasOwnProperty(e)){var t=v[e];delete v[e],t()}},b=function(e){m.call(e.data)};f&&p||(f=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return v[++h]=function(){s("function"==typeof e?e:Function(e),t)},i(h),h},p=function(e){delete v[e]},"process"==n(17)(d)?i=function(e){d.nextTick(a(m,e,1))}:g&&g.now?i=function(e){g.now(a(m,e,1))}:_?(r=new _,o=r.port2,r.port1.onmessage=b,i=a(o.postMessage,o,1)):u.addEventListener&&"function"==typeof postMessage&&!u.importScripts?(i=function(e){u.postMessage(e+"","*")},u.addEventListener("message",b,!1)):i="onreadystatechange"in l("script")?function(e){c.appendChild(l("script")).onreadystatechange=function(){c.removeChild(this),m.call(e)}}:function(e){setTimeout(a(m,e,1),0)}),e.exports={set:f,clear:p}},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(t){return{e:!0,v:t}}}},function(e,t,n){var i=n(5),r=n(6),o=n(42);e.exports=function(e,t){if(i(e),r(t)&&t.constructor===e)return t;var n=o.f(e);return(0,n.resolve)(t),n.promise}},function(e,t,n){var i=n(48),r=n(35).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return i(e,r)}},function(e,t,n){"use strict";function i(){function e(e){var t={};return this.each(function(e,n){t[n]=e}),t}return{dump:e}}e.exports=i},function(e,t,n){"use strict";function i(){function e(e,t,n){return l.on(t,a(this,n))}function t(e,t){l.off(t)}function n(e,t,n){l.once(t,a(this,n))}function i(e,t,n){var i=this.get(t);e(),l.fire(t,n,i)}function o(e,t){var n=this.get(t);e(),l.fire(t,undefined,n)}function c(e){var t={};this.each(function(e,n){t[n]=e}),e(),s(t,function(e,t){l.fire(t,undefined,e)})}var l=r();return{watch:e,unwatch:t,once:n,set:i,remove:o,clearAll:c}}function r(){return c(u,{_id:0,_subSignals:{},_subCallbacks:{}})}var o=n(11),a=o.bind,s=o.each,c=o.create,l=o.slice;e.exports=i;var u={_id:null,_subCallbacks:null,_subSignals:null,on:function(e,t){return this._subCallbacks[e]||(this._subCallbacks[e]={}),this._id+=1,this._subCallbacks[e][this._id]=t,this._subSignals[this._id]=e,this._id},off:function(e){var t=this._subSignals[e];delete this._subCallbacks[t][e],delete this._subSignals[e]},once:function(e,t){var n=this.on(e,a(this,function(){t.apply(this,arguments),this.off(n)}))},fire:function(e){var t=l(arguments,1);s(this._subCallbacks[e],function(e){e.apply(this,t)})}}},function(e,t,n){"use strict";function i(){return n(136),{}}e.exports=i},function(e,t,n){"use strict";function i(){function e(e,t,n,i){3==arguments.length&&(i=n,n=undefined);var r=this.get(t,n),o=i(r);this.set(t,o!=undefined?o:r)}return{update:e}}e.exports=i},function(e,t,n){var i=n(2),r=i.JSON||(i.JSON={stringify:JSON.stringify});e.exports=function(e){return r.stringify.apply(r,arguments)}},,function(e,t,n){"use strict";t.__esModule=!0,t.UserBehaviorsStatServer=t.userBehaviorsStatActions=undefined;var i=n(0),r=function(e){return e&&e.__esModule?e:{"default":e}}(i),o=n(142),a=n(46),s=t.userBehaviorsStatActions={loginPanelLoginClick:"loginPanelLoginClick",loginPanelPhoneClick:"loginPanelPhoneClick",loginPanelPhoneCode:"loginPanelPhoneCode",loginPanelHistorySelect:"loginPanelHistorySelect",loginPanelEmailShow:"loginPanelEmailShow",loginPanelEyeClick:"loginPanelEyeClick",loginPanelAccountClick:"loginPanelAccountClick",loginSuccess:"loginSuccess",loginFailure:"loginFailure",loginPanelForgetPasswordClick:"loginPanelForgetPasswordClick",loginPanelRegisterClick:"loginPanelRegisterClick",loginPanelQrClick:"loginPanelQrClick",loginPanelQrCodeShow:"loginPanelQrCodeShow",loginPanelQrCodeRefreshClick:"loginPanelQrCodeRefreshClick",loginPanelQrCodeRefresh:"loginPanelQrCodeRefresh",loginPanelQrCodeHover:"loginPanelQrCodeHover",loginPanelQrCodeAccoutClick:"loginPanelQrCodeAccoutClick",thrLoginClick:"thrLoginClick",websdkShow:"websdkShow",websdkClose:"websdkClose",registPanelShow:"registPanelShow",registPanelPhoneCode:"registPanelPhoneCode",registPanelRegistClick:"registPanelRegistClick",registPanelRegistSuccess:"registPanelRegistSuccess",registPanelRegistFail:"registPanelRegistFail",registPanelRegistAgree:"registPanelRegistAgree",registPanelLoginClick:"registPanelLoginClick",lastLoginType:"lastLoginType"};t.UserBehaviorsStatServer=function(){function e(t){(0,r["default"])(this,e),this._statServer=new o.StatServer(t,"websdk-user-behaviors2")}return e.prototype.setPublicData=function(e){return this._statServer.setPublicData(e)},e.prototype.stat=function(e){return this._statServer.stat(e)},e.prototype.statLoginResult=function(e,t){if(t){var n={extData:{}},i=n.extData,r=(new Date).getTime();switch("number"==typeof t.beginTime&&(r-=t.beginTime),i.costTime=r,t.type){case 0:n.action=e?s.loginSuccess:s.loginFailure,i.isAuto="number"==typeof t.isAuto?t.isAuto:0;break;case 1:n.action=e?s.registPanelRegistSuccess:s.registPanelRegistFail;break;default:n=null}null!==n&&(i.mode=(0,a.forceToString)(t.mode,""),e||"undefined"==typeof t.errorCode||(i.errorCode=(0,a.forceToString)(t.errorCode,"")),this.stat(n))}},e}()},function(e,t,n){var i=n(13),r=n(45),o=n(76);e.exports=function(e){return function(t,n,a){var s,c=i(t),l=r(c.length),u=o(a,l);if(e&&n!=n){for(;l>u;)if((s=c[u++])!=s)return!0}else for(;l>u;u++)if((e||u in c)&&c[u]===n)return e||u||0;return!e&&-1}}},function(e,t,n){var i=n(30),r=Math.max,o=Math.min;e.exports=function(e,t){return e=i(e),e<0?r(e+t,0):o(e,t)}},function(e,t,n){"use strict";var i=n(124),r=n(125),o=n(132);e.exports=i.createStore(r,o)},function(e,t,n){n(79),e.exports=n(2).Object.assign},function(e,t,n){var i=n(7);i(i.S+i.F,"Object",{assign:n(80)})},function(e,t,n){"use strict";var i=n(4),r=n(25),o=n(37),a=n(26),s=n(32),c=n(41),l=Object.assign;e.exports=!l||n(14)(function(){var e={},t={},n=Symbol(),i="abcdefghijklmnopqrst";return e[n]=7,i.split("").forEach(function(e){t[e]=e}),7!=l({},e)[n]||Object.keys(l({},t)).join("")!=i})?function(e,t){for(var n=s(e),l=arguments.length,u=1,d=o.f,f=a.f;l>u;)for(var p,_=c(arguments[u++]),g=d?r(_).concat(d(_)):r(_),h=g.length,v=0;h>v;)p=g[v++],i&&!f.call(_,p)||(n[p]=_[p]);return n}:l},function(e,t,n){var i=n(19),r=n(99),o=n(100),a=n(5),s=n(45),c=n(83),l={},u={},t=e.exports=function(e,t,n,d,f){var p,_,g,h,v=f?function(){return e}:c(e),m=i(n,d,t?2:1),b=0;if("function"!=typeof v)throw TypeError(e+" is not iterable!");if(o(v)){for(p=s(e.length);p>b;b++)if((h=t?m(a(_=e[b])[0],_[1]):m(e[b]))===l||h===u)return h}else for(g=v.call(e);!(_=g.next()).done;)if((h=r(g,m,_.value,t))===l||h===u)return h};t.BREAK=l,t.RETURN=u},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){var i=n(61),r=n(3)("iterator"),o=n(18);e.exports=n(2).getIteratorMethod=function(e){if(e!=undefined)return e[r]||e["@@iterator"]||o[i(e)]}},function(e,t,n){"use strict";function i(e,t){undefined;return t>e.length?new Array(t-e.length+1).join("0")+e:e}function r(e){return e.getFullYear().toString()+i((e.getMonth()+1).toString(),2)+i(e.getDate().toString(),2)}function o(e){var t=null;try{t=JSON.parse(e)}catch(n){t=null}return t}function a(e){var t=undefined;try{t=JSON.stringify(e)}catch(n){t=undefined}return t}function s(e,t,n){return typeof t===e?t:n}Object.defineProperty(t,"__esModule",{value:!0}),t.dateToDateString=r,t.parseJson=o,t.stringifyJson=a,t.forceGetTypeValue=s},function(e,t,n){var i=n(30),r=n(29);e.exports=function(e){return function(t,n){var o,a,s=String(r(t)),c=i(n),l=s.length;return c<0||c>=l?e?"":undefined:(o=s.charCodeAt(c),o<55296||o>56319||c+1===l||(a=s.charCodeAt(c+1))<56320||a>57343?e?s.charAt(c):o:e?s.slice(c,c+2):a-56320+(o-55296<<10)+65536)}}},function(e,t,n){"use strict";var i=n(39),r=n(20),o=n(27),a={};n(9)(a,n(3)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=i(a,{next:r(1,n)}),o(e,t+" Iterator")}},function(e,t,n){var i=n(8),r=n(5),o=n(25);e.exports=n(4)?Object.defineProperties:function(e,t){r(e);for(var n,a=o(t),s=a.length,c=0;s>c;)i.f(e,n=a[c++],t[n]);return e}},function(e,t,n){var i=n(10),r=n(32),o=n(31)("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=r(e),i(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?a:null}},function(e,t,n){"use strict";var i=n(90),r=n(82),o=n(18),a=n(13);e.exports=n(53)(Array,"Array",function(e,t){this._t=a(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=undefined,r(1)):"keys"==t?r(0,n):"values"==t?r(0,e[n]):r(0,[n,e[n]])},"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t,n,i){if(!(e instanceof t)||i!==undefined&&i in e)throw TypeError(n+": incorrect invocation!");return e}},function(e,t,n){var i=n(9);e.exports=function(e,t,n){for(var r in t)n&&e[r]?e[r]=t[r]:i(e,r,t[r]);return e}},function(e,t,n){var i=n(22)("meta"),r=n(6),o=n(10),a=n(8).f,s=0,c=Object.isExtensible||function(){return!0},l=!n(14)(function(){return c(Object.preventExtensions({}))}),u=function(e){a(e,i,{value:{i:"O"+ ++s,w:{}}})},d=function(e,t){if(!r(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!o(e,i)){if(!c(e))return"F";if(!t)return"E";u(e)}return e[i].i},f=function(e,t){if(!o(e,i)){if(!c(e))return!0;if(!t)return!1;u(e)}return e[i].w},p=function(e){return l&&_.NEED&&c(e)&&!o(e,i)&&u(e),e},_=e.exports={KEY:i,NEED:!1,fastKey:d,getWeak:f,onFreeze:p}},function(e,t,n){var i=n(26),r=n(20),o=n(13),a=n(36),s=n(10),c=n(47),l=Object.getOwnPropertyDescriptor;t.f=n(4)?l:function(e,t){if(e=o(e),t=a(t,!0),c)try{return l(e,t)}catch(n){}if(s(e,t))return r(!i.f.call(e,t),e[t])}},function(e,t,n){e.exports={"default":n(121),__esModule:!0}},function(e,t,n){"use strict";t.__esModule=!0;var i=function(){function e(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var s=1732584193,c=-271733879,l=-1732584194,u=271733878,d=0;e.length>d;d+=16){var f=s,p=c,_=l,g=u;s=n(s,c,l,u,e[d+0],7,-680876936),u=n(u,s,c,l,e[d+1],12,-389564586),l=n(l,u,s,c,e[d+2],17,606105819),c=n(c,l,u,s,e[d+3],22,-1044525330),s=n(s,c,l,u,e[d+4],7,-176418897),u=n(u,s,c,l,e[d+5],12,1200080426),l=n(l,u,s,c,e[d+6],17,-1473231341),c=n(c,l,u,s,e[d+7],22,-45705983),s=n(s,c,l,u,e[d+8],7,1770035416),u=n(u,s,c,l,e[d+9],12,-1958414417),l=n(l,u,s,c,e[d+10],17,-42063),c=n(c,l,u,s,e[d+11],22,-1990404162),s=n(s,c,l,u,e[d+12],7,1804603682),u=n(u,s,c,l,e[d+13],12,-40341101),l=n(l,u,s,c,e[d+14],17,-1502002290),c=n(c,l,u,s,e[d+15],22,1236535329),s=i(s,c,l,u,e[d+1],5,-165796510),u=i(u,s,c,l,e[d+6],9,-1069501632),l=i(l,u,s,c,e[d+11],14,643717713),c=i(c,l,u,s,e[d+0],20,-373897302),s=i(s,c,l,u,e[d+5],5,-701558691),u=i(u,s,c,l,e[d+10],9,38016083),l=i(l,u,s,c,e[d+15],14,-660478335),c=i(c,l,u,s,e[d+4],20,-405537848),s=i(s,c,l,u,e[d+9],5,568446438),u=i(u,s,c,l,e[d+14],9,-1019803690),l=i(l,u,s,c,e[d+3],14,-187363961),c=i(c,l,u,s,e[d+8],20,1163531501),s=i(s,c,l,u,e[d+13],5,-1444681467),u=i(u,s,c,l,e[d+2],9,-51403784),l=i(l,u,s,c,e[d+7],14,1735328473),c=i(c,l,u,s,e[d+12],20,-1926607734),s=r(s,c,l,u,e[d+5],4,-378558),u=r(u,s,c,l,e[d+8],11,-2022574463),l=r(l,u,s,c,e[d+11],16,1839030562),c=r(c,l,u,s,e[d+14],23,-35309556),s=r(s,c,l,u,e[d+1],4,-1530992060),u=r(u,s,c,l,e[d+4],11,1272893353),l=r(l,u,s,c,e[d+7],16,-155497632),c=r(c,l,u,s,e[d+10],23,-1094730640),s=r(s,c,l,u,e[d+13],4,681279174),u=r(u,s,c,l,e[d+0],11,-358537222),l=r(l,u,s,c,e[d+3],16,-722521979),c=r(c,l,u,s,e[d+6],23,76029189),s=r(s,c,l,u,e[d+9],4,-640364487),u=r(u,s,c,l,e[d+12],11,-421815835),l=r(l,u,s,c,e[d+15],16,530742520),c=r(c,l,u,s,e[d+2],23,-995338651),s=o(s,c,l,u,e[d+0],6,-198630844),u=o(u,s,c,l,e[d+7],10,1126891415),l=o(l,u,s,c,e[d+14],15,-1416354905),c=o(c,l,u,s,e[d+5],21,-57434055),s=o(s,c,l,u,e[d+12],6,1700485571),u=o(u,s,c,l,e[d+3],10,-1894986606),l=o(l,u,s,c,e[d+10],15,-1051523),c=o(c,l,u,s,e[d+1],21,-2054922799),s=o(s,c,l,u,e[d+8],6,1873313359),u=o(u,s,c,l,e[d+15],10,-30611744),l=o(l,u,s,c,e[d+6],15,-1560198380),c=o(c,l,u,s,e[d+13],21,1309151649),s=o(s,c,l,u,e[d+4],6,-145523070),u=o(u,s,c,l,e[d+11],10,-1120210379),l=o(l,u,s,c,e[d+2],15,718787259),c=o(c,l,u,s,e[d+9],21,-343485551),s=a(s,f),c=a(c,p),l=a(l,_),u=a(u,g)}return[s,c,l,u]}function t(e,t,n,i,r,o){return a(s(a(a(t,e),a(i,o)),r),n)}function n(e,n,i,r,o,a,s){return t(n&i|~n&r,e,n,o,a,s)}function i(e,n,i,r,o,a,s){return t(n&r|i&~r,e,n,o,a,s)}function r(e,n,i,r,o,a,s){return t(n^i^r,e,n,o,a,s)}function o(e,n,i,r,o,a,s){return t(i^(n|~r),e,n,o,a,s)}function a(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function s(e,t){return e<<t|e>>>32-t}function c(e){for(var t=[],n=(1<<d)-1,i=0;e.length*d>i;i+=d)t[i>>5]|=(e.charCodeAt(i/d)&n)<<i%32;return t}function l(e){for(var t=u?"0123456789ABCDEF":"0123456789abcdef",n="",i=0;4*e.length>i;i++)n+=t.charAt(e[i>>2]>>i%4*8+4&15)+t.charAt(e[i>>2]>>i%4*8&15);return n}var u=0,d=8;return function(t){return l(e(c(t),t.length*d))}}();window.md5=i,t["default"]=i},function(e,t,n){"use strict";t.__esModule=!0,t.CONFIG=undefined;var i=n(51),r=(0,i.getDomain)();t.CONFIG={LOGIN_ID:"",APP_NAME:"",APP_VERSION:"NONE",SET_ROOT_DOMAIN:!0,LOGIN_TYPE_COOKIE_NAME:"_x_t_",AUTO_LOGIN_COOKIE_NAME:"_x_a_",AUTO_LOGIN_EXPIRE_TIME:2592e3,LOGIN_TYPES:["account","mobile"],REGISTER_TYPES:["mobile"],UI_THEME:"embed",UI_TYPE:"embed",UI_TEXT:"",UI_STYLE:"",THIRD_LOGIN_DISPLAY:!0,RETRY_LOGIN_ON_SERVER_ERROR:!0,LOGIN_SUCCESS_FUNC:function(){location.reload()},REGISTER_SUCCESS_FUNC:function(){location.reload()},LOGIN_SUCCESS_URL:location.href,REGISTER_SUCCESS_URL:location.href,ON_UI_CHANGE:function(e){},POPUP_MASK:!0,POPUP_ALLOW_CLOSE:!0,POPUP_CLOSE_FUNC:function(){},POPUP_PRELOAD:!0,DEFUALT_BACKGROUND:"//i."+r+"/login/theme/popup/images/layer_bg.png",DEFUALT_UI:"login",IFRAME_ALLOW_TRANSPARENCY:!1,IFRAME_STYLE:"",IFRAME_ID:"loginIframe",LOGOUT_FUNC:function(){location.reload()},BIND_SUCCESS_FUNC:function(){location.reload()},LOGIN_BUTTON_TEXT:"",REGISTER_BUTTON_TEXT:"",REGISTER_STAT_DATA:"",DOMAIN:r,ALLOW_ACCOUNT_REGISTER_IDS:["vip_niu","niux_web","game"],VERSION:"2.5",DEBUG:!1,DEFAULT_ACCOUNT:"",THIRD_LOGIN_TARGET_PARENT:!1,ALERT_ERROR:!1,CHANGE_SIZE_FUNC:function(e){},LOGIN_EXT_FUNS:[],LOGOUT_EXT_FUNS:[],CLIENT_LOGIN_FUNS:[],CLIENTLOGIN:!1,INITED_FUNS:[],LOGIN_STATE_INITED_FUNS:[],USE_CDN:!1,SERVER_REGISTER:"https://zhuce."+r+"/regapi/",UI_COMPLETED_EXT_FUNS:[],THIRD_LOGIN_GROUP:["qq","weixin","sina"],THIRD_LOGIN_DEFAULT:["qq","weixin","sina"],DEFAULT_AVATAR:"",SERVER_LOGIN:["xluser-ssl."+r,"xluser2-ssl."+r,"xluser3-ssl."+r],SERVER_XLUSER:[],MESSAGE_CHANNEL_GSLB_QURERY_HOST_KEY:"agw-acc-ssl."+r,MESSAGE_CHANNEL_SERVER:"agw-acc-web-ssl."+r,ANALYSIS_SERVER:"",IS_HIT_BLOCK:!0,LOGIN_FAIL_FUNS:[],CAN_GSLB:!0,STATIC_DOMAIN:"i."+r,ISFRAUD:!0,CAN_QR_LOGIN:!1,ONLOAD:function(){},REGISTER_CHECKED:!1,TOAST:function(e){},isDOMAIN:!0,IS_SYNC_APP:!0,IS_SYNC_PC:!0,IS_SYNC_MAC:!1,CLIENT_FEATURE:!1,HL:"",SHOW_GSM:!1,REGISTER_WITH_LOGIN:!1}},function(e,t,n){"use strict";t.__esModule=!0,t.userBehaviorsStatServerLoader=t.UserBehaviorsStatServerLoader=undefined;var i=n(0),r=function(e){return e&&e.__esModule?e:{"default":e}}(i),o=n(46),a=n(140),s=n(141),c=n(74),l=t.UserBehaviorsStatServerLoader=function(){function e(){(0,r["default"])(this,e),this._userBehaviorsStatServer=null}return e.prototype.setPublicData=function(e){var t=e||{},n=(0,o.combineJsonObject)((0,s.getPublicData)(),t);this.get().setPublicData(n)},e.prototype.get=function(){if(null===this._userBehaviorsStatServer){var e=a.gbStatAttrNames.userBehaviors;if(!a.gbStat.hasAttr(e)){var t=new c.UserBehaviorsStatServer((0,s.getStatServerUrl)());a.gbStat.setAttr(e,t)}this._userBehaviorsStatServer=a.gbStat.getAttr(e)}return this._userBehaviorsStatServer},e}();t.userBehaviorsStatServerLoader=new l},function(e,t,n){var i=n(5);e.exports=function(e,t,n,r){try{return r?t(i(n)[0],n[1]):t(n)}catch(a){var o=e["return"];throw o!==undefined&&i(o.call(e)),a}}},function(e,t,n){var i=n(18),r=n(3)("iterator"),o=Array.prototype;e.exports=function(e){return e!==undefined&&(i.Array===e||o[r]===e)}},function(e,t,n){"use strict";var i=n(1),r=n(2),o=n(8),a=n(4),s=n(3)("species");e.exports=function(e){var t="function"==typeof r[e]?r[e]:i[e];a&&t&&!t[s]&&o.f(t,s,{configurable:!0,get:function(){return this}})}},function(e,t,n){var i=n(3)("iterator"),r=!1;try{var o=[7][i]();o["return"]=function(){r=!0},Array.from(o,function(){throw 2})}catch(a){}e.exports=function(e,t){if(!t&&!r)return!1;var n=!1;try{var o=[7],a=o[i]();a.next=function(){return{done:n=!0}},o[i]=function(){return a},e(o)}catch(a){}return n}},function(e,t,n){var i=n(17);e.exports=Array.isArray||function(e){return"Array"==i(e)}},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.HttpRequest=undefined;var r=n(40),o=i(r),a=n(0),s=i(a),c=t.HttpRequest=function(){function e(){(0,s["default"])(this,e)}return e.prototype.get=function(e,t){arguments.length>2&&arguments[2]!==undefined&&arguments[2];return this._request("GET",e,t,null)},e.prototype.post=function(e,t,n){return this._request("POST",e,t,n)},e.prototype._request=function(e,t,n){var i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:null;return new o["default"](function(r,o){var a=void 0;if(a=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),a.onreadystatechange=function(){if(4==a.readyState){var e={data:a.responseText,status:a.status};r(e)}},"GET"===e)a.open(e,t,!0),a.send(null);else{if(a.open(e,t,!0),n)for(var s in n)a.setRequestHeader(s,n[s]);a.send(i)}})},e}(),l=new c;t["default"]=l},function(e,t,n){n(57),n(50),n(54),n(106),n(110),n(111),e.exports=n(2).Promise},function(e,t,n){"use strict";var i,r,o,a,s=n(16),c=n(1),l=n(19),u=n(61),d=n(7),f=n(6),p=n(21),_=n(91),g=n(81),h=n(63),v=n(64).set,m=n(108)(),b=n(42),y=n(65),w=n(109),x=n(66),S=c.TypeError,L=c.process,T=L&&L.versions,A=T&&T.v8||"",k=c.Promise,E="process"==u(L),C=function(){},P=r=b.f,O=!!function(){try{var e=k.resolve(1),t=(e.constructor={})[n(3)("species")]=function(e){e(C,C)};return(E||"function"==typeof PromiseRejectionEvent)&&e.then(C)instanceof t&&0!==A.indexOf("6.6")&&-1===w.indexOf("Chrome/66")}catch(i){}}(),I=function(e){var t;return!(!f(e)||"function"!=typeof(t=e.then))&&t},N=function(e,t){if(!e._n){e._n=!0;var n=e._c;m(function(){for(var i=e._v,r=1==e._s,o=0;n.length>o;)!function(t){var n,o,a,s=r?t.ok:t.fail,c=t.resolve,l=t.reject,u=t.domain;try{s?(r||(2==e._h&&R(e),e._h=1),!0===s?n=i:(u&&u.enter(),n=s(i),u&&(u.exit(),a=!0)),n===t.promise?l(S("Promise-chain cycle")):(o=I(n))?o.call(n,c,l):c(n)):l(i)}catch(d){u&&!a&&u.exit(),l(d)}}(n[o++]);e._c=[],e._n=!1,t&&!e._h&&M(e)})}},M=function(e){v.call(c,function(){var t,n,i,r=e._v,o=D(e);if(o&&(t=y(function(){E?L.emit("unhandledRejection",r,e):(n=c.onunhandledrejection)?n({promise:e,reason:r}):(i=c.console)&&i.error&&i.error("Unhandled promise rejection",r)}),e._h=E||D(e)?2:1),e._a=undefined,o&&t.e)throw t.v})},D=function(e){return 1!==e._h&&0===(e._a||e._c).length},R=function(e){v.call(c,function(){var t;E?L.emit("rejectionHandled",e):(t=c.onrejectionhandled)&&t({promise:e,reason:e._v})})},j=function(e){var t=this;t._d||(t._d=!0,t=t._w||t,t._v=e,t._s=2,t._a||(t._a=t._c.slice()),N(t,!0))},F=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw S("Promise can't be resolved itself");(t=I(e))?m(function(){var i={_w:n,_d:!1};try{t.call(e,l(F,i,1),l(j,i,1))}catch(r){j.call(i,r)}}):(n._v=e,n._s=1,N(n,!1))}catch(i){j.call({_w:n,_d:!1},i)}}};O||(k=function(e){_(this,k,"Promise","_h"),p(e),i.call(this);try{e(l(F,this,1),l(j,this,1))}catch(t){j.call(this,t)}},i=function(e){this._c=[],this._a=undefined,this._s=0,this._d=!1,this._v=undefined,this._h=0,this._n=!1},i.prototype=n(92)(k.prototype,{then:function(e,t){var n=P(h(this,k));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=E?L.domain:undefined,this._c.push(n),this._a&&this._a.push(n),this._s&&N(this,!1),n.promise},"catch":function(e){return this.then(undefined,e)}}),o=function(){var e=new i;this.promise=e,this.resolve=l(F,e,1),this.reject=l(j,e,1)},b.f=P=function(e){return e===k||e===a?new o(e):r(e)}),d(d.G+d.W+d.F*!O,{Promise:k}),n(27)(k,"Promise"),n(101)("Promise"),a=n(2).Promise,d(d.S+d.F*!O,"Promise",{reject:function(e){var t=P(this);return(0,t.reject)(e),t.promise}}),d(d.S+d.F*(s||!O),"Promise",{resolve:function(e){return x(s&&this===a?k:this,e)}}),d(d.S+d.F*!(O&&n(102)(function(e){k.all(e)["catch"](C)})),"Promise",{all:function(e){var t=this,n=P(t),i=n.resolve,r=n.reject,o=y(function(){var n=[],o=0,a=1;g(e,!1,function(e){var s=o++,c=!1;n.push(undefined),a++,t.resolve(e).then(function(e){c||(c=!0,n[s]=e,--a||i(n))},r)}),--a||i(n)});return o.e&&r(o.v),n.promise},race:function(e){var t=this,n=P(t),i=n.reject,r=y(function(){g(e,!1,function(e){t.resolve(e).then(n.resolve,i)})});return r.e&&i(r.v),n.promise}})},function(e,t){e.exports=function(e,t,n){var i=n===undefined;switch(t.length){case 0:return i?e():e.call(n);case 1:return i?e(t[0]):e.call(n,t[0]);case 2:return i?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return i?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return i?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){var i=n(1),r=n(64).set,o=i.MutationObserver||i.WebKitMutationObserver,a=i.process,s=i.Promise,c="process"==n(17)(a);e.exports=function(){var e,t,n,l=function(){var i,r;for(c&&(i=a.domain)&&i.exit();e;){r=e.fn,e=e.next;try{r()}catch(o){throw e?n():t=undefined,o}}t=undefined,i&&i.enter()};if(c)n=function(){a.nextTick(l)};else if(!o||i.navigator&&i.navigator.standalone)if(s&&s.resolve){var u=s.resolve(undefined);n=function(){u.then(l)}}else n=function(){r.call(i,l)};else{var d=!0,f=document.createTextNode("");new o(l).observe(f,{characterData:!0}),n=function(){f.data=d=!d}}return function(i){var r={fn:i,next:undefined};t&&(t.next=r),e||(e=r,n()),t=r}}},function(e,t,n){var i=n(1),r=i.navigator;e.exports=r&&r.userAgent||""},function(e,t,n){"use strict";var i=n(7),r=n(2),o=n(1),a=n(63),s=n(66);i(i.P+i.R,"Promise",{"finally":function(e){var t=a(this,r.Promise||o.Promise),n="function"==typeof e;return this.then(n?function(n){return s(t,e()).then(function(){return n})}:e,n?function(n){return s(t,e()).then(function(){throw n})}:e)}})},function(e,t,n){"use strict";var i=n(7),r=n(42),o=n(65);i(i.S,"Promise",{"try":function(e){var t=r.f(this),n=o(e);return(n.e?t.reject:t.resolve)(n.v),t.promise}})},function(e,t,n){e.exports={"default":n(113),__esModule:!0}},function(e,t,n){n(50),n(54),e.exports=n(43).f("iterator")},function(e,t,n){e.exports={"default":n(115),__esModule:!0}},function(e,t,n){n(116),n(57),n(119),n(120),e.exports=n(2).Symbol},function(e,t,n){"use strict";var i=n(1),r=n(10),o=n(4),a=n(7),s=n(58),c=n(93).KEY,l=n(14),u=n(34),d=n(27),f=n(22),p=n(3),_=n(43),g=n(44),h=n(117),v=n(103),m=n(5),b=n(6),y=n(32),w=n(13),x=n(36),S=n(20),L=n(39),T=n(118),A=n(94),k=n(37),E=n(8),C=n(25),P=A.f,O=E.f,I=T.f,N=i.Symbol,M=i.JSON,D=M&&M.stringify,R=p("_hidden"),j=p("toPrimitive"),F={}.propertyIsEnumerable,U=u("symbol-registry"),B=u("symbols"),H=u("op-symbols"),q=Object.prototype,G="function"==typeof N&&!!k.f,V=i.QObject,J=!V||!V.prototype||!V.prototype.findChild,W=o&&l(function(){return 7!=L(O({},"a",{get:function(){return O(this,"a",{value:7}).a}})).a})?function(e,t,n){var i=P(q,t);i&&delete q[t],O(e,t,n),i&&e!==q&&O(q,t,i)}:O,X=function(e){var t=B[e]=L(N.prototype);return t._k=e,t},Q=G&&"symbol"==typeof N.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof N},Y=function(e,t,n){return e===q&&Y(H,t,n),m(e),t=x(t,!0),m(n),r(B,t)?(n.enumerable?(r(e,R)&&e[R][t]&&(e[R][t]=!1),n=L(n,{enumerable:S(0,!1)})):(r(e,R)||O(e,R,S(1,{})),e[R][t]=!0),W(e,t,n)):O(e,t,n)},$=function(e,t){m(e);for(var n,i=h(t=w(t)),r=0,o=i.length;o>r;)Y(e,n=i[r++],t[n]);return e},K=function(e,t){return t===undefined?L(e):$(L(e),t)},z=function(e){var t=F.call(this,e=x(e,!0));return!(this===q&&r(B,e)&&!r(H,e))&&(!(t||!r(this,e)||!r(B,e)||r(this,R)&&this[R][e])||t)},Z=function(e,t){if(e=w(e),t=x(t,!0),e!==q||!r(B,t)||r(H,t)){var n=P(e,t);return!n||!r(B,t)||r(e,R)&&e[R][t]||(n.enumerable=!0),n}},ee=function(e){for(var t,n=I(w(e)),i=[],o=0;n.length>o;)r(B,t=n[o++])||t==R||t==c||i.push(t);return i},te=function(e){for(var t,n=e===q,i=I(n?H:w(e)),o=[],a=0;i.length>a;)!r(B,t=i[a++])||n&&!r(q,t)||o.push(B[t]);return o};G||(N=function(){if(this instanceof N)throw TypeError("Symbol is not a constructor!");var e=f(arguments.length>0?arguments[0]:undefined),t=function(n){this===q&&t.call(H,n),r(this,R)&&r(this[R],e)&&(this[R][e]=!1),W(this,e,S(1,n))};return o&&J&&W(q,e,{configurable:!0,set:t}),X(e)},s(N.prototype,"toString",function(){return this._k}),A.f=Z,E.f=Y,n(67).f=T.f=ee,n(26).f=z,k.f=te,o&&!n(16)&&s(q,"propertyIsEnumerable",z,!0),_.f=function(e){return X(p(e))}),a(a.G+a.W+a.F*!G,{Symbol:N});for(var ne="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ie=0;ne.length>ie;)p(ne[ie++]);for(var re=C(p.store),oe=0;re.length>oe;)g(re[oe++]);a(a.S+a.F*!G,"Symbol",{"for":function(e){return r(U,e+="")?U[e]:U[e]=N(e)},keyFor:function(e){if(!Q(e))throw TypeError(e+" is not a symbol!");for(var t in U)if(U[t]===e)return t},useSetter:function(){J=!0},useSimple:function(){J=!1}}),a(a.S+a.F*!G,"Object",{create:K,defineProperty:Y,defineProperties:$,getOwnPropertyDescriptor:Z,getOwnPropertyNames:ee,getOwnPropertySymbols:te});var ae=l(function(){k.f(1)});a(a.S+a.F*ae,"Object",{getOwnPropertySymbols:function(e){return k.f(y(e))}}),M&&a(a.S+a.F*(!G||l(function(){var e=N();return"[null]"!=D([e])||"{}"!=D({a:e})||"{}"!=D(Object(e))})),"JSON",{stringify:function(e){for(var t,n,i=[e],r=1;arguments.length>r;)i.push(arguments[r++]);if(n=t=i[1],(b(t)||e!==undefined)&&!Q(e))return v(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!Q(t))return t}),i[1]=t,D.apply(M,i)}}),N.prototype[j]||n(9)(N.prototype,j,N.prototype.valueOf),d(N,"Symbol"),d(Math,"Math",!0),d(i.JSON,"JSON",!0)},function(e,t,n){var i=n(25),r=n(37),o=n(26);e.exports=function(e){var t=i(e),n=r.f;if(n)for(var a,s=n(e),c=o.f,l=0;s.length>l;)c.call(e,a=s[l++])&&t.push(a);return t}},function(e,t,n){var i=n(13),r=n(67).f,o={}.toString,a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],s=function(e){try{return r(e)}catch(t){return a.slice()}};e.exports.f=function(e){return a&&"[object Window]"==o.call(e)?s(e):r(i(e))}},function(e,t,n){n(44)("asyncIterator")},function(e,t,n){n(44)("observable")},function(e,t,n){n(122);var i=n(2).Object;e.exports=function(e,t){return i.create(e,t)}},function(e,t,n){var i=n(7);i(i.S,"Object",{create:n(39)})},function(e,t,n){"use strict";t.__esModule=!0;t.gbAttrNames={otherInfo:"otherInfo",platformInfo:"platformInfo"},t.gbOtherInfoAttrNames={showLoginWndSource:"showLoginWndSource"},t.gbPlatformInfoAttrNames={deviceSign:"deviceSign"}},function(e,t,n){"use strict";function i(e,t){var n={_seenPlugins:[],_namespacePrefix:"",_namespaceRegexp:null,_legalNamespace:/^[a-zA-Z0-9_\-]+$/,_storage:function(){if(!this.enabled)throw new Error("store.js: No supported storage has been added! Add one (e.g store.addStorage(require('store/storages/cookieStorage')) or use a build with more built-in storages (e.g https://github.com/marcuswestin/store.js/tree/master/dist/store.legacy.min.js)");return this._storage.resolved},_testStorage:function(e){try{var t="__storejs__test__";e.write(t,t);var n=e.read(t)===t;return e.remove(t),n}catch(i){return!1}},_assignPluginFnProp:function(e,t){var n=this[t];this[t]=function(){function t(){if(n)return l(arguments,function(e,t){i[t]=e}),n.apply(r,i)}var i=s(arguments,0),r=this,o=[t].concat(i);return e.apply(r,o)}},_serialize:function(e){return(0,o["default"])(e)},_deserialize:function(e,t){if(!e)return t;var n="";try{n=JSON.parse(e)}catch(i){n=e}return n!==undefined?n:t}},i=u(n,_);return l(e,function(e){i.addStorage(e)}),l(t,function(e){i.addPlugin(e)}),i}var r=n(12),o=function(e){return e&&e.__esModule?e:{"default":e}}(r),a=n(11),s=a.slice,c=a.pluck,l=a.each,u=a.create,d=a.isList,f=a.isFunction,p=a.isObject;e.exports={createStore:i};var _={version:"2.0.3",enabled:!1,addStorage:function(e){this.enabled||this._testStorage(e)&&(this._storage.resolved=e,this.enabled=!0)},addPlugin:function(e){var t=this;if(d(e))return void l(e,function(e){t.addPlugin(e)});if(!c(this._seenPlugins,function(t){return e===t})){if(this._seenPlugins.push(e),!f(e))throw new Error("Plugins must be function values that return objects");var n=e.call(this);if(!p(n))throw new Error("Plugins must return an object of function properties");l(n,function(n,i){if(!f(n))throw new Error("Bad plugin property: "+i+" from plugin "+e.name+". Plugins should only return functions.");t._assignPluginFnProp(n,i)})}},get:function(e,t){var n=this._storage().read(this._namespacePrefix+e);return this._deserialize(n,t)},set:function(e,t){return t===undefined?this.remove(e):(this._storage().write(this._namespacePrefix+e,this._serialize(t)),t)},remove:function(e){this._storage().remove(this._namespacePrefix+e)},each:function(e){var t=this;this._storage().each(function(n,i){e(t._deserialize(n),i.replace(t._namespaceRegexp,""))})},clearAll:function(){this._storage().clearAll()},hasNamespace:function(e){return this._namespacePrefix=="__storejs_"+e+"_"},namespace:function(e){if(!this._legalNamespace.test(e))throw new Error("store.js namespaces can only have alhpanumerics + underscores and dashes");var t="__storejs_"+e+"_";return u(this,{_namespacePrefix:t,_namespaceRegexp:t?new RegExp("^"+t):null})},createStore:function(e,t){return i(e,t)}}},function(e,t,n){"use strict";e.exports={localStorage:n(126),"oldFF-globalStorage":n(127),"oldIE-userDataStorage":n(128),cookieStorage:n(129),sessionStorage:n(130),memoryStorage:n(131)}},function(e,t,n){"use strict";function i(){return u.localStorage}function r(e){return i().getItem(e)}function o(e,t){return i().setItem(e,t)}function a(e){for(var t=i().length-1;t>=0;t--){var n=i().key(t);e(r(n),n)}}function s(e){return i().removeItem(e)}function c(){return i().clear()}var l=n(11),u=l.Global;e.exports={name:"localStorage",read:r,write:o,each:a,remove:s,clearAll:c}},function(e,t,n){"use strict";function i(e){return u[e]}function r(e,t){u[e]=t}function o(e){for(var t=u.length-1;t>=0;t--){var n=u.key(t);e(u[n],n)}}function a(e){return u.removeItem(e)}function s(){o(function(e,t){delete u[e]})}var c=n(11),l=c.Global;e.exports={name:"oldFF-globalStorage",read:i,write:r,each:o,remove:a,clearAll:s};var u=l.globalStorage},function(e,t,n){"use strict";function i(e,t){if(!_){var n=c(e);p(function(e){e.setAttribute(n,t),e.save(d)})}}function r(e){if(!_){var t=c(e),n=null;return p(function(e){n=e.getAttribute(t)}),n}}function o(e){p(function(t){for(var n=t.XMLDocument.documentElement.attributes,i=n.length-1;i>=0;i--){var r=n[i];e(t.getAttribute(r.name),r.name)}})}function a(e){var t=c(e);p(function(e){e.removeAttribute(t),e.save(d)})}function s(){p(function(e){var t=e.XMLDocument.documentElement.attributes;e.load(d);for(var n=t.length-1;n>=0;n--)e.removeAttribute(t[n].name);e.save(d)})}function c(e){return e.replace(/^\d/,"___$&").replace(g,"___")}var l=n(11),u=l.Global;e.exports={name:"oldIE-userDataStorage",write:i,read:r,each:o,remove:a,clearAll:s};var d="storejs",f=u.document,p=function(){if(!f||!f.documentElement||!f.documentElement.addBehavior)return null;var e,t,n;try{t=new ActiveXObject("htmlfile"),t.open(),t.write('<script>document.w=window<\/script><iframe src="/favicon.ico"></iframe>'),t.close(),e=t.w.frames[0].document,n=e.createElement("div")}catch(i){n=f.createElement("div"),e=f.body}return function(t){var i=[].slice.call(arguments,0);i.unshift(n),e.appendChild(n),n.addBehavior("#default#userData"),n.load(d),t.apply(this,i),e.removeChild(n)}}(),_=(u.navigator?u.navigator.userAgent:"").match(/ (MSIE 8|MSIE 9|MSIE 10)\./),g=new RegExp("[!\"#$%&'()*+,/\\\\:;<=>?@[\\]^`{|}~]","g")},function(e,t,n){"use strict";function i(e){if(!e||!c(e))return null;var t="(?:^|.*;\\s*)"+escape(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*((?:[^;](?!;))*[^;]?).*";return unescape(f.cookie.replace(new RegExp(t),"$1"))}function r(e){for(var t=f.cookie.split(/; ?/g),n=t.length-1;n>=0;n--)if(d(t[n])){var i=t[n].split("="),r=unescape(i[0]),o=unescape(i[1]);e(o,r)}}function o(e,t){e&&(f.cookie=escape(e)+"="+escape(t)+"; expires=Tue, 19 Jan 2038 03:14:07 GMT; path=/")}function a(e){e&&c(e)&&(f.cookie=escape(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/")}function s(){r(function(e,t){a(t)})}function c(e){return new RegExp("(?:^|;\\s*)"+escape(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(f.cookie)}var l=n(11),u=l.Global,d=l.trim;e.exports={name:"cookieStorage",read:i,write:o,each:r,remove:a,clearAll:s};var f=u.document},function(e,t,n){"use strict";function i(){return u.sessionStorage}function r(e){return i().getItem(e)}function o(e,t){return i().setItem(e,t)}function a(e){for(var t=i().length-1;t>=0;t--){var n=i().key(t);e(r(n),n)}}function s(e){return i().removeItem(e)}function c(){return i().clear()}var l=n(11),u=l.Global;e.exports={name:"sessionStorage",read:r,write:o,each:a,remove:s,clearAll:c}},function(e,t,n){"use strict";function i(e){return c[e]}function r(e,t){c[e]=t}function o(e){for(var t in c)c.hasOwnProperty(t)&&e(c[t],t)}function a(e){delete c[e]}function s(e){c={}}e.exports={name:"memoryStorage",read:i,write:r,each:o,remove:a,clearAll:s};var c={}},function(e,t,n){"use strict";e.exports={defaults:n(133),dump:n(68),events:n(69),observe:n(134),expire:n(135),json2:n(70),operations:n(137),update:n(71),"v1-backcompat":n(138)}},function(e,t,n){"use strict";function i(){function e(e,t){n=t}function t(e,t){var i=e();return i!==undefined?i:n[t]}var n={};return{defaults:e,get:t}}e.exports=i},function(e,t,n){"use strict";function i(){function e(e,t,n){var i=this.watch(t,n);return n(this.get(t)),i}function t(e,t){this.unwatch(t)}return{observe:e,unobserve:t}}var r=n(69);e.exports=[r,i]},function(e,t,n){"use strict";function i(){function e(e,t,n,o){return this.hasNamespace(r)||i.set(t,o),e()}function t(e,t){if(!this.hasNamespace(r)){i.get(t,Number.MAX_VALUE)<=(new Date).getTime()&&this.remove(t)}return e()}function n(e,t){return this.hasNamespace(r)||i.remove(t),e()}var i=this.namespace(r);return{set:e,get:t,remove:n}}var r="expire_mixin";e.exports=i},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{"default":e}}var _stringify=__webpack_require__(12),_stringify2=_interopRequireDefault(_stringify),_typeof2=__webpack_require__(15),_typeof3=_interopRequireDefault(_typeof2);"object"!==("undefined"==typeof JSON?"undefined":(0,_typeof3["default"])(JSON))&&(JSON={}),function(){function f(e){return e<10?"0"+e:e}function this_value(){return this.valueOf()}function quote(e){return rx_escapable.lastIndex=0,rx_escapable.test(e)?'"'+e.replace(rx_escapable,function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var n,i,r,o,a,s=gap,c=t[e];switch(c&&"object"===(void 0===c?"undefined":(0,_typeof3["default"])(c))&&"function"==typeof c.toJSON&&(c=c.toJSON(e)),"function"==typeof rep&&(c=rep.call(t,e,c)),void 0===c?"undefined":(0,_typeof3["default"])(c)){case"string":return quote(c);case"number":return isFinite(c)?String(c):"null";case"boolean":case"null":return String(c);case"object":if(!c)return"null";if(gap+=indent,a=[],"[object Array]"===Object.prototype.toString.apply(c)){for(o=c.length,n=0;n<o;n+=1)a[n]=str(n,c)||"null";return r=0===a.length?"[]":gap?"[\n"+gap+a.join(",\n"+gap)+"\n"+s+"]":"["+a.join(",")+"]",gap=s,r}if(rep&&"object"===(void 0===rep?"undefined":(0,_typeof3["default"])(rep)))for(o=rep.length,n=0;n<o;n+=1)"string"==typeof rep[n]&&(i=rep[n],(r=str(i,c))&&a.push(quote(i)+(gap?": ":":")+r));else for(i in c)Object.prototype.hasOwnProperty.call(c,i)&&(r=str(i,c))&&a.push(quote(i)+(gap?": ":":")+r);return r=0===a.length?"{}":gap?"{\n"+gap+a.join(",\n"+gap)+"\n"+s+"}":"{"+a.join(",")+"}",gap=s,r}}var rx_one=/^[\],:{}\s]*$/,rx_two=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rx_three=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rx_four=/(?:^|:|,)(?:\s*\[)+/g,rx_escapable=/[\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},Boolean.prototype.toJSON=this_value,Number.prototype.toJSON=this_value,String.prototype.toJSON=this_value);var gap,indent,meta,rep;"function"!=typeof _stringify2["default"]&&(meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(e,t,n){var i;if(gap="",indent="","number"==typeof n)for(i=0;i<n;i+=1)indent+=" ";else"string"==typeof n&&(indent=n);if(rep=t,t&&"function"!=typeof t&&("object"!==(void 0===t?"undefined":(0,_typeof3["default"])(t))||"number"!=typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){function walk(e,t){var n,i,r=e[t];if(r&&"object"===(void 0===r?"undefined":(0,_typeof3["default"])(r)))for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(i=walk(r,n),i!==undefined?r[n]=i:delete r[n]);return reviver.call(e,t,r)}var j;if(text=String(text),rx_dangerous.lastIndex=0,rx_dangerous.test(text)&&(text=text.replace(rx_dangerous,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),rx_one.test(text.replace(rx_two,"@").replace(rx_three,"]").replace(rx_four,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}()},function(e,t,n){"use strict";function i(){function e(e,t,n,i,r,o){return a.call(this,"push",arguments)}function t(e,t){return a.call(this,"pop",arguments)}function n(e,t){return a.call(this,"shift",arguments)}function i(e,t,n,i,r,o){return a.call(this,"unshift",arguments)}function r(e,t,n,i,r,a){var l=s(arguments,2);return this.update(t,{},function(e){if("object"!=(void 0===e?"undefined":(0,o["default"])(e)))throw new Error('store.assign called for non-object value with key "'+t+'"');return l.unshift(e),c.apply(Object,l)})}function a(e,t){var n,i=t[1],r=s(t,2);return this.update(i,[],function(t){n=Array.prototype[e].apply(t,r)}),n}return{push:e,pop:t,shift:n,unshift:i,assign:r}}var r=n(15),o=function(e){return e&&e.__esModule?e:{"default":e}}(r),a=n(11),s=a.slice,c=a.assign,l=n(71);e.exports=[l,i]},function(e,t,n){"use strict";function i(){return this.disabled=!this.enabled,{has:r,transact:o,clear:a,forEach:s,getAll:c,serialize:l,deserialize:u}}function r(e,t){return this.get(t)!==undefined}function o(e,t,n,i){null==i&&(i=n,n=null),null==n&&(n={});var r=this.get(t,n),o=i(r);this.set(t,o===undefined?r:o)}function a(e){return this.clearAll.call(this)}function s(e,t){return this.each.call(this,function(e,n){t(n,e)})}function c(e){return this.dump.call(this)}function l(e,t){return(0,f["default"])(t)}function u(e,t){if("string"!=typeof t)return undefined;try{return JSON.parse(t)}catch(n){return t||undefined}}var d=n(12),f=function(e){return e&&e.__esModule?e:{"default":e}}(d),p=n(68),_=n(70);e.exports=[p,_,i]},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.RequestServer=undefined;var r=n(12),o=i(r),a=n(40),s=i(a),c=n(0),l=i(c);t.RequestServer=function(){function e(t){(0,l["default"])(this,e),this._requester=t}return e.prototype.get=function(e,t,n,i){return this._request("GET",e,t,n,i)},e.prototype.post=function(e,t,n,i){return this._request("POST",e,t,n,i)},e.prototype._request=function(e,t,n,i,r){var a=this;return new s["default"](function(s,c){var l=t,u=null,d=r||{};"GET"===e?i&&(l+=a._toUrlParams(i)):u=d.stringifyJsonDatas?(0,o["default"])(i):i,a._requester[e.toLocaleLowerCase()](l,n,u).then(function(e){var t=e;if(d.parseJsonResult&&"string"==typeof e.data)try{t.data=JSON.parse(e.data)}catch(n){c(n)}s(t)})["catch"](function(e){c(e)})})},e.prototype._toUrlParams=function(e){var t="";if(e){var n=[];for(var i in e)n.push(i+"="+e[i]);t=n.join("&")}return t},e}()},function(e,t,n){"use strict";t.__esModule=!0,t.gbStat=t.gbStatAttrNames=undefined;var i=n(0),r=function(e){return e&&e.__esModule?e:{"default":e}}(i),o=n(23),a=n(38),s=(t.gbStatAttrNames={monitor:"monitor",userBehaviors:"userBehaviors"},function(){function e(){(0,r["default"])(this,e),this._gbHelper=new a.GBHelper(o.gbAttrNames.stat)}return e.prototype.hasAttr=function(e){return this._gbHelper.hasAttr(e)},e.prototype.setAttr=function(e,t){this._gbHelper.setAttr(e,t)},e.prototype.getAttr=function(e){return this._gbHelper.getAttr(e)},e}());t.gbStat=new s},function(e,t,n){"use strict";function i(){return{appId:(0,o.forceGetTypeValue)("string",a.gbConfig.getAppId(),""),appName:(0,o.forceGetTypeValue)("string",a.gbConfig.getAppName(),""),appVersion:(0,o.forceGetTypeValue)("string",a.gbConfig.getAppVersion(),""),deviceSign:(0,o.forceGetTypeValue)("string",(0,s.deviceid)(),""),sdkVersion:(0,o.forceGetTypeValue)("string",a.gbConfig.getSDKVersion(),""),platform:(0,o.forceGetTypeValue)("string",(0,s.Platform)(),"")}}function r(){return"https://"+a.gbConfig.getAnalysisServer()}t.__esModule=!0,t.getPublicData=i,t.getStatServerUrl=r;var o=n(84),a=n(60),s=n(49)},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.StatServer=undefined;var r=n(12),o=i(r),a=n(0),s=i(a),c=n(46),l=n(143),u=n(144),d=n(23),f=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}(d);t.StatServer=function(){function e(t,n){var i=arguments.length>2&&arguments[2]!==undefined&&arguments[2];(0,s["default"])(this,e),this._topic=n,this._preUrl=(t||"https://xluser-test-ssl.n0808.com")+"/analysis-report/v1/"+n+"?msg=",this._needEncrypt=!!i,this._requestServer=l.requestServerLoader.get(),this._publicData={}}return e.prototype._report=function(e){var t="",n=(0,o["default"])(e),i=u.base64ServerLoader.get().encode(n);i=i.replace(/\+/g,"-"),i=i.replace(/\//g,"_");var r=i.length,a=0;r>=2&&"="===i.charAt(r-1)&&(a="="===i.charAt(r-2)?2:1),t=i.substr(0,r-a);var s=this._preUrl+t,c=!0;if(f.getEnvType()!==f.gbEnvTypes.pluginIndex){var l=l||parent.xdas;l&&"function"==typeof l.fireStatEvent&&(c=!1,l.fireStatEvent(s))}c&&this._requestServer.get(s,null,null,null)["catch"](function(e){})},e.prototype.setPublicData=function(e){this._publicData=e},e.prototype.stat=function(e){var t=e||{},n=(0,c.combineJsonObject)({reportTime:(0,c.dateToTimeString)(new Date)},this._publicData);n=(0,c.combineJsonObject)(n,t),this._report(n)},e}()},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.requestServerLoader=t.RequestServerLoader=undefined;var r=n(0),o=i(r),a=n(104),s=i(a),c=n(139),l=t.RequestServerLoader=function(){function e(){(0,o["default"])(this,e),this._requestServer=new c.RequestServer(s["default"])}return e.prototype.get=function(){return this._requestServer},e}();t.requestServerLoader=new l},function(e,t,n){"use strict";t.__esModule=!0,t.base64ServerLoader=t.Base64ServerLoader=undefined;var i=n(0),r=function(e){return e&&e.__esModule?e:{"default":e}}(i),o=function(){function e(){(0,r["default"])(this,e)}return e.prototype.encode=function(e){return Base64.encode(e)},e}(),a=t.Base64ServerLoader=function(){function e(){(0,r["default"])(this,e),this._base64Server=new o}return e.prototype.get=function(){return this._base64Server},e}();t.base64ServerLoader=new a},,,,,function(e,t,n){"use strict";function i(e,t){var n=!1;return e&&(n=!!e.className.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))),n}function r(e,t){i(e,t)||(e.className+=" "+t)}function o(e,t){i(e,t)&&(e.className=e.className.replace(new RegExp("(\\s|^)"+t+"(\\s|$)")," "))}function a(e){return e.chidren||e.chidNodes}function s(e){return e.childElementCount||e.children.length}function c(e){return e.firstElementChild||e.firstChild}function l(e){return e.lastElementChild||e.lastChild}function u(e){return e.previousElementSibling||e.previousSibling}function d(e){return e.nextElementSibling||e.nextSibling}function f(e){if(e)if("createEvent"in document){var t=document.createEvent("HTMLEvents");t.initEvent("change",!1,!0),e.dispatchEvent(t)}else e.fireEvent("onchange")}t.__esModule=!0,t.hasClass=i,t.addClass=r,t.removeClass=o,t.getChildren=a,t.getChildCount=s,t.getFirstChild=c,t.getLastChild=l,t.getPreviousSibling=u,t.getNextSibling=d,t.dispatchChangeEvent=f},,,,,,,,function(e,t,n){"use strict";function i(e,t,n){xll.toast(e),t.innerHTML=e,t.style.display="",T&&clearTimeout(T),T=setTimeout(function(){t.style.display="none"},5e3),n&&n.focus()}function r(e){var t=e.target;t&&o(t)}function o(e){var t=e.id,n=L.id(t+"_l");return""===e.value?n.style.display="":n.style.display="none",n}function a(){function e(e){var t=o(e);L.bind(e,"keyup.placeholder",r),L.bind(e,"keydown.placeholder",r),L.bind(e,"change.placeholder",r),L.bind(e,"click.placeholder",r),L.bind(t,"selectstart.placeholder",function(){return!1})}var t=document.getElementsByTagName("label");t=[].slice.call(t,0),_.forEach(t,function(t){t.style.display="";var n=L.id(t.htmlFor),i="password"==n.type||"text"==n.type;n&&i&&e(n)})}function s(e,t){if(!e.hasOwnProperty(t))throw new Error(" ("+t+") 不存在");var n=e[t],i=function(e,t){for(var i in n[e]){var r=n[e][i];L.id(r)&&t(L.id(r))}},r=function(e,t){if(e){var n=e.className;t&&n.indexOf(" on")<0&&(e.className=n+" on"),t||(e.className=n.replace(" on",""))}};i("needHideDoms",function(e){e.style.display="none",r(e,!1)}),i("needShowDoms",function(e){e.style.display="block",r(e,!0)}),i("needHideTabs",function(e){return e.className=""}),i("needShowTabs",function(e){return e.className="on"})}function c(e){for(var t=void 0,n=void 0,i=["var r=[];"],r=/\{\s*([a-zA-Z\.\_0-9()]+)\s*\}/m,o=function(e){i.push("r.push('"+e.replace(/\'/g,"\\'").replace(/\n/g,"\\n").replace(/\r/g,"\\r")+"');")};n=r.exec(e);)n.index>0&&o(e.slice(0,n.index)),i.push("r.push(this."+n[1]+");"),e=e.substring(n.index+n[0].length);o(e),i.push("return r.join('');"),t=new Function(i.join("\n")),this.render=function(e){return t.apply(e)}}function l(e,t){var n=arguments.length>2&&arguments[2]!==undefined&&arguments[2];for(var i in t)"object"===(0,m["default"])(t[i])&&e[i]?l(e[i],t[i]):(!n&&e[i],e[i]=t[i]);return e}function u(){for(var e=["pr_agree","mr_agree","ar_agree"],t=0;t<e.length;t++)!function(t){L.bind(L.id(e[t]),"change.x",function(){var n=L.id(e[t].split("_")[0]+"_submit");L.id(e[t]).checked?n.className="":n.className="grey"})}(t)}function d(e){var t=e&&e.aLoginTurnMLogin?e.aLoginTurnMLogin:null,n=e&&e.accountInput?e.accountInput:null,i=e&&e.mLoginTurnALogin?e.mLoginTurnALogin:null,r=e&&e.mobileInput?e.mobileInput:null;t&&n&&i&&r&&(L.bind(t,"click",function(){0===r.value.length&&n.value.length>0&&L.checkMobile(n.value)&&(r.value=n.value)}),L.bind(i,"click",function(){0===n.value.length&&r.value.length>0&&(n.value=r.value)}))}function f(e){var t=!(!e||!e.dispatchChangeEventOn||!0!==e.dispatchChangeEventOn)&&e.dispatchChangeEventOn,n=e&&e.accountInput?e.accountInput:null,i=e&&e.alMailAssociation?e.alMailAssociation:null,r=e&&e.alMailAssociationUL?e.alMailAssociationUL:null,o=e&&e.hideMailAssociationDoms?e.hideMailAssociationDoms:null,a=["qq.com","163.com","139.com","126.com","sina.com","yeah.net","sohu.com","189.cn","hotmail.com","gmail.com"];n&&i&&r&&(L.bind(n,"keydown",function(e){9===e.keyCode&&(i.scrollTop=0,i.style.display="none")}),L.bind(n,"input",function(){var e=n.value,s="none"!==i.style.display,c=!1,l=e.indexOf("@");if(-1!=l){var u=a.length;if(0==y.getChildCount(r)){for(var d=0;d<u;d++)!function(e){var o=document.createElement("li");o.style.display="none";var a=document.createElement("span");a.className="login__name",o.appendChild(a),L.bind(o,"click",function(e){n.value=a.innerText,i.style.display="none",n.focus(),t&&y.dispatchChangeEvent(n)}),r.appendChild(o)}();if(L.bind(i,"click",function(e){e.stopPropagation()}),o)for(var d=0,f=o.length;d<f;d++){var p=o[d];p&&L.bind(p,"click",function(e){i.scrollTop=0,i.style.display="none"})}}for(var _=e.slice(0,l),g=e.slice(l+1),h=0,v=y.getFirstChild(r);h<u;h++){var m=a[h];if(0==g.length||0==m.indexOf(g)){var b=y.getFirstChild(v);b&&(b.innerText=_+"@"+m),v.style.display="",c=!0}else v.style.display="none";v=y.getNextSibling(v)}}c||(i.scrollTop=0),i.style.display=c?"":"none",!s&&c&&S.stat({action:w.userBehaviorsStatActions.loginPanelEmailShow})}))}function p(e){var t=e&&e.accountInput?e.accountInput:null,n=e&&e.mobileInput?e.mobileInput:null,i=e&&"function"==typeof e.turnMLoginFunc?e.turnMLoginFunc:null,r=e&&e.mLoginwarnDom?e.mLoginwarnDom:null,o=e&&"function"==typeof e.showErrorFunc?e.showErrorFunc:null,a=0;do{if(!(t&&n&&i&&r&&o))break;parent&&parent.xlQuickLogin&&"function"==typeof parent.xlQuickLogin.setLoginFailExt&&parent.xlQuickLogin.setLoginFailExt(function(e,s){3==s&&"3"==e&&L.checkMobile(t.value)&&4===++a&&(a=0,n.value=t.value,o&&o("如忘记密码，可使用快捷登录",r),i())})}while(!1)}function g(e){var t=e&&e.accountInput?e.accountInput:null,n=e&&e.passwordEye?e.passwordEye:null,i=e&&e.passwordEyeTip?e.passwordEyeTip:null,r=e&&"function"==typeof e.showPasswordEyeFunc?e.showPasswordEyeFunc:null,o=e&&"function"==typeof e.showPasswordEyeTipFunc?e.showPasswordEyeTipFunc:null;do{if(!(t&&n&&i&&r&&o))break;parent&&parent.xlQuickLogin&&"function"==typeof parent.xlQuickLogin.setLoginFailExt&&function(){var e=parent.xlQuickLogin.setLoginFailExt,t=e(function(n,i){3==i&&"3"==n&&(o(!0),r(!0),e(function(){},t))})}()}while(!1)}function h(e,t,n,i){xll.toast(e),t.innerHTML=e,t.style.display="";var r=A.warmDomMap,o="string"==typeof t.id?t.id:"",a=r[o];a?clearTimeout(a.timerId):a={dom:t},a.timerId=setTimeout(function(){t.style.display="none",delete r[a.dom.id]},i||0),n&&n.focus()}t.__esModule=!0,t.WX_APPID=t.VALIDATE_MOBILE_DOM=t.PASSWORD_CHANGE_DOM=undefined;var v=n(15),m=function(e){return e&&e.__esModule?e:{"default":e}}(v);t.showError=i,t.placeholder=r,t.initFocus=a,t.initView=s,t.Template=c,t.Concats=l,t.chkRegAgree=u,t.addAMLoginTrunAutoFill=d,t.addALoginMailAssociation=f,t.addALoginTurnMLoginFourthFail=p,t.addShowPasswordEyeTipWhileFirstFail=g,t.showError2=h;var b=n(149),y=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}(b),w=n(74),x=n(98),S=x.userBehaviorsStatServerLoader.get(),L=xlQuickLogin.Util,T=void 0,A={warmDomMap:{}};t.PASSWORD_CHANGE_DOM={needShowDoms:["password_box","password_change"],needHideDoms:["login_box","register_box","mobile_login","client_login","account_login","vaildate_mobile","qr_switch"],needShowTabs:[],needHideTabs:[]},t.VALIDATE_MOBILE_DOM={needShowDoms:["password_box","vaildate_mobile"],needHideDoms:["login_box","register_box","mobile_login","client_login","account_login","password_change","qr_switch"],needShowTabs:[],needHideTabs:[]},t.WX_APPID="wx4f4bd37fd477978b"},,,,,,,,function(e,t,n){"use strict";function i(e){var t={};for(var n in e){t[e[n]]=n}return t}function r(e,t){var n="",i=l[e];return i&&(n=i[t]||""),n}t.__esModule=!0,t.getUITypeNameById=r;var o={accountLogin:"1",mobileLogin:"2",qrLogin:"4"},a={mailRegister:"1",mobileRegister:"2",accountRegister:"3",appRegister:"4",mobileRegisterWithPassword:"5"},s=i(o),c=i(a),l={login:s,register:c}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}var r=n(12),o=i(r),a=n(52),s=i(a),c=n(165),l=n(157),u=n(149),d=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}(u),f=n(24),p=i(f),_=n(255),g=i(_),h=n(256),v=n(74),m=n(98),b=m.userBehaviorsStatServerLoader.get(),y=xlQuickLogin.Util,w=parent.xdas,x=[],S="",L="",T="",A="",k=!1,E={};try{E=store.get("xl_uit"),E=E?JSON.parse(E):{}}catch(I){E={}}var C={accountLogin:{title:"帐号登录",username:"请输入迅雷帐号",password:"请输入密码",btnLogin:"登录",autoLogin:"下次自动登录",forget:"忘记密码？",register:"注册帐号"},mobileLogin:{title:"手机快捷登录",gsm:"+86",mobile:"请输入手机号",btnCode:"获取验证码",code:"请输入短信验证码",tip:"出于安全考虑，请输入上图中字符并验证",tipBtn:"确定",btnLogin:"立即登录",autoLogin:"下次自动登录",forget:"忘记密码？"},qrLogin:{title:"扫码登录 更安全",tip:"请打开",apps:"手机微信、手机迅雷",tips:"扫码",desc1:"<p>打开 <em>手机迅雷APP</em></p><p>扫一扫登录</p>",desc2:"<p><em>扫码成功!</em></p><p>请在APP进行登录</p>",desc3:"<p>二维码已失效</p>",desc4:"若未安装APP请使用手机扫码安装",referer:"刷新二维码","default":"theme/xlx/images/<EMAIL>",help:"theme/xlx/images/<EMAIL>"},appRegister:{title:"",qr:""},ctLogin:{tip:"系统检测到您已登录迅雷帐号，点击头像登录"},phoneRegister:{phone:"请输入手机号",btnCode:"获取验证码",code:"短信验证码",password:"请输入6-16位登录密码",note:"出于安全考虑，请输入上图中字符并验证",btnOk:"确定",tip:"可跳过此步直接注册，稍后在安全中心设置密码",btnSum:"完成注册"},mailRegister:{mail:"请输入您的常用邮箱",password:"请输入6-16位登录密码"},register:{title:"注册迅雷帐号",phone:"注册迅雷帐号",mail:"邮箱注册",account:"帐号注册",accountLogin:"帐号登录",mobileLogin:"短信登录",app:"APP扫码注册",btnSubmit:"注册",btnReg:"直接注册",has:"已有迅雷帐号"},accountRegister:{name:"请输入帐号，支持字母、数字、下划线",password:"请输入6-16位登录密码",rePassword:"请输入确认密码"},tos:{tip:"同意",forget:"https://i.xunlei.com/xluser/validate/enter/findpwd_enter.html?purl=http://i.xunlei.com",link:'<a href="http://i.xunlei.com/tos.shtml" target="_blank" class="xl_tos_group">《迅雷用户协议》</a>及<a href="https://i.xunlei.com/xluser/privacy.html" target="_blank" class="xl_tos_group">《隐私协议》</a>'},captcha:{code:"请输入验证码"},hitblock:{title:"帐号登录",changeTip:"您的帐号存在安全风险，建议设置新密码",password:"请输入新密码",submitBtn:"确认",validateTip:"您的帐号存在安全风险，建议您在短信验证后修改密码",tip:"您的帐号<em>安全风险极高</em>，请使用手机登录",check:"您的帐号被临时冻结，请进行自主解封"}},P={accountLogin:{needShowDoms:["login_box","account_login"],needHideDoms:["register_box","mobile_login","client_login","qr_login"],needShowTabs:["al_tab"],needHideTabs:["ml_tab"]},mobileLogin:{needShowDoms:["login_box","mobile_login"],needHideDoms:["register_box","account_login","client_login","qr_login"],needShowTabs:["ml_tab"],needHideTabs:["al_tab"]},clientLogin:{needShowDoms:["login_box","client_login"],needHideDoms:["register_box","account_login","mobile_login","qr_login"],needShowTabs:["al_tab"],needHideTabs:["ml_tab"]},qrLogin:{needShowDoms:["login_box","qr_login"],needHideDoms:["register_box","account_login","client_login","mobile_login","lg_tabs","tl_div"],needShowTabs:["ml_tab"],needHideTabs:["al_tab"]},mailRegister:{needShowDoms:["register_box","mail_register"],needHideDoms:["login_box","mobile_register","mobile_register2","account_register","app_register"],needShowTabs:["mr_tab"],needHideTabs:["pr_tab","ar_tab","ir_tab"]},appRegister:{needShowDoms:["register_box","app_register"],needHideDoms:["login_box","mobile_register","mail_register","mobile_register2","account_register"],needShowTabs:["ir_tab"],needHideTabs:["mr_tab","ar_tab","pr_tab"]},mobileRegister:{needShowDoms:["register_box","mobile_register"],needHideDoms:["login_box","mail_register","mobile_register2","account_register","app_register"],needShowTabs:["pr_tab"],needHideTabs:["mr_tab","ar_tab","ir_tab"]},mobileRegister2:{needShowDoms:["register_box","mobile_register2","pr_pass_div"],needHideDoms:["login_box","mail_register","mobile_register","turnLoginDiv","account_register","app_register"],needShowTabs:["pr_tab"],needHideTabs:["mr_tab","ar_tab","ir_tab"]},accountRegister:{needShowDoms:["register_box","account_register"],needHideDoms:["login_box","mail_register","mobile_register","mobile_register2","app_register"],needShowTabs:["ar_tab"],needHideTabs:["mr_tab","pr_tab","ir_tab"]},passwordChange:l.PASSWORD_CHANGE_DOM,validateMobile:l.VALIDATE_MOBILE_DOM},O={openNewTabName:{thirdLogin:"WEB_SDK_THIRD_LOGIN_39bba395-37a6-4d9c-a20e-3f020ba6866f"}};xlQuickLogin.registerUI("xlx",function(){var e,t,n,i,r,a,u,_,m,I,N=[],M=-1,D=null,R=0;return e={supportClient:!1,supportRegister:!0,currentUIShowType:"",_enableQrLogin:!1,_enableThirdLogin:!1,_lastLoginPanelQrCodeHoverStatTime:0,_showPasswordTipTimerId:undefined,setAlertError:function(e){I=e?1:0},showUI:function(t){switch(m=m?(0,s["default"])({DEF_UIS_SHOW:P},m):P,e.currentUIShowType=t,t){case"accountLogin":e.showPasswordEyeTip(!1)}(0,l.initView)(m,t),e.onShowUI(t)},onShowUI:function(e){},showError:function(e,t,n){I?(alert(e),n&&n.focus()):(0,l.showError2)(e,t,n,5e3)},hideError:function(e){e.style.display="none"},getContainer:function(){var e=y.id("xls_quick_login");if(!e){e=document.createElement("div"),e.id="xls_quick_login",e.className="xls_box",e.style.display="none";var t=new l.Template(g["default"]),n=t.render(C);e.innerHTML=n,document.body.appendChild(e)}return e},setSmsButtonGrey:function(e,t){if(e){t=t||59;var n=y.text(e);y.text(e,n+t+"s"),e.className="verify_btn verify_grey_btn";var i=setInterval(function(){if(0===--t)return y.text(e,n),e.className="verify_btn",void clearInterval(i);y.text(e,n+t+"s")},1e3)}},_initConfig:function(t,n){var i={enableQrLogin2:t.indexOf("4")>=0};(0,h.combineEtcdConfig)(i,E);e._enableQrLogin=!1},_initBaseThemeConfig:function(e,t){var n={wholeDom:y.id("xls_quick_login"),turnBusinessDoms:{},accountLoginDoms:{},mobileLoginDoms:{},thirdLoginDoms:{},actions:{}},i=n.turnBusinessDoms,r=n.accountLoginDoms,o=n.mobileLoginDoms,a=n.thirdLoginDoms,s=n.actions,c=e.indexOf("1")>-1,l=e.indexOf("2")>-1;return c&&(r.accountInput=y.id("al_u"),r.alMailAssociation=y.id("al_mail_association"),r.alMailAssociationUL=y.id("al_mail_association_ul"),r.userDroparrow=y.id("login__arrow"),r.passwordEye=y.id("al_password_eye"),r.passwordEyeTip=y.id("al_password_eye_tip"),s.addALoginMailAssociation=!0,s.showPasswordEyeTipWhileFirstFail=!0),l&&(o.mobileInput=y.id("ml_m"),o.warnText=y.id("ml_warn")),c&&l&&(i.mLoginTurnALogin=y.id("al_tab"),i.aLoginTurnMLogin=y.id("ml_tab"),s.amLoginTrunAutoFill=!0,s.aLoginTurnMLoginFourthFail=!0),a.more=y.id("tl_arrow___"),n},_initBaseThemeActions:function(t){do{if(!t||!t.actions)break;var n=t.actions,i=t.turnBusinessDoms||{},r=t.accountLoginDoms||{},o=t.mobileLoginDoms||{},a=t.thirdLoginDoms||{};n.amLoginTrunAutoFill&&(0,l.addAMLoginTrunAutoFill)({aLoginTurnMLogin:i.aLoginTurnMLogin,accountInput:r.accountInput,mLoginTurnALogin:i.mLoginTurnALogin,mobileInput:o.mobileInput}),n.addALoginMailAssociation&&(0,l.addALoginMailAssociation)({dispatchChangeEventOn:!0,accountInput:r.accountInput,alMailAssociation:r.alMailAssociation,alMailAssociationUL:r.alMailAssociationUL,hideMailAssociationDoms:[t.wholeDom,a,r.userDroparrow]}),n.aLoginTurnMLoginFourthFail&&(0,l.addALoginTurnMLoginFourthFail)({accountInput:r.accountInput,mobileInput:o.mobileInput,turnMLoginFunc:e.tMobileLogin,mLoginwarnDom:o.warnText,showErrorFunc:e.showError}),n.showPasswordEyeTipWhileFirstFail&&(0,l.addShowPasswordEyeTipWhileFirstFail)({accountInput:r.accountInput,passwordEye:r.passwordEye,passwordEyeTip:r.passwordEyeTip,showPasswordEyeFunc:e.showPasswordEye,showPasswordEyeTipFunc:e.showPasswordEyeTip})}while(!1)},_initBaseTheme:function(t,n){var i=e._initBaseThemeConfig(t,n);e._initBaseThemeActions(i)},showPasswordEye:function(e){var t=!0===e,n=y.id("al_p");if(n){var i=!1,r=y.id("al_password_eye"),o=!1;"password"===n.type&&t?(n.type="text",o=!0,r&&d.addClass(r,"login__eye--show"),i=!0):"text"!==n.type||t||(n.type="password",o=!1,r&&d.removeClass(r,"login__eye--show"),i=!0),i&&b.stat({action:v.userBehaviorsStatActions.loginPanelEyeClick,extData:{isEye:o?1:0,showType:1}})}},showPasswordEyeTip:function(t){var n=!0===t;e._showPasswordTipTimerId&&(window.clearTimeout(e._showPasswordTipTimerId),e._showPasswordTipTimerId=undefined);var i=y.id("al_password_eye_tip");i&&(n?(i.style.display="block",e._showPasswordTipTimerId=setTimeout(function(){e._showPasswordTipTimerId=undefined,i.style.display="none"},3e3)):i&&(i.style.display="none"))},_initAccountLogin:function(){y.bind("al_password_eye","click.x",function(){var t=y.id("al_p");t&&e.showPasswordEye("password"===t.type)})},_initXLxTheme:function(t,n){t.indexOf("1")>-1&&e._initAccountLogin()},init:function(t,n,i){if(e._initConfig(t,n),!1 in document.createElement("input")&&setTimeout(function(){(0,l.initFocus)()},200),(0,l.chkRegAgree)(),y.bind(y.id("xls_quick_login"),"click.x",function(t){e._enableThirdLogin&&(_.icons2.style.display="none"),y.id("login-drop-users").style.display="none"}),y.bind("close","click.x",function(){b.stat({action:v.userBehaviorsStatActions.websdkClose}),w.cloLogin()}),e._initBaseTheme(t,n),e._initXLxTheme(t,n),""!==t&&"login"===i){var r=(0,c.getUITypeNameById)(i,t[0]);r&&e.showUI(r)}else{if(""===n||"register"!==i)return;var o=(0,c.getUITypeNameById)(i,n[0]);o&&e.showUI(o)}""!==t?(1===t.length?(y.id("al_tab").style.display="none",y.id("ml_tab").style.display="none"):(y.bind("al_tab","click.x",function(){b.stat({action:v.userBehaviorsStatActions.loginPanelAccountClick}),e.tAccountLogin()}),y.bind("ml_tab","click.x",function(){b.stat({action:v.userBehaviorsStatActions.loginPanelPhoneClick}),e.tMobileLogin()})),y.id("turnLoginDiv").style.display="",y.bind("turnAccountLogin","click.x",function(){b.stat({action:v.userBehaviorsStatActions.registPanelLoginClick}),e.tAccountLogin()}),y.bind("turnMobileLogin","click.x",function(){e.tMobileLogin()})):y.id("turnLoginDiv").style.display="none",b.stat({action:v.userBehaviorsStatActions.websdkShow,extData:{panelType:"register"===i?0:1}}),""!==n?(y.id("mr_tab").style.display="none",y.id("pr_tab").style.display="none",y.id("ar_tab").style.display="none",n.length>1?(0==n.indexOf("1")&&(y.id("mr_tab").style.display="",y.bind("mr_tab","click.x",function(){e.showUI("mailRegister")})),0==n.indexOf("2")&&(y.id("pr_tab").style.display="",y.bind("pr_tab","click.x",function(){e.showUI("mobileRegister")})),0==n.indexOf("3")&&(y.id("ar_tab").style.display="",y.bind("ar_tab","click.x",function(){e.showUI("accountRegister")}))):(0==n.indexOf("1")&&(y.id("mr_tab").style.display="",y.id("mr_tab").style.width="100%"),0==n.indexOf("2")&&(y.id("pr_tab").style.display="",y.id("pr_tab").style.width="100%"),0==n.indexOf("3")&&(y.id("ar_tab").style.display="",y.id("ar_tab").style.width="100%")),y.id("turnRegisterDiv").style.display="",y.bind("turnRegister","click.x",function(){0==n.indexOf("2")?(b.stat({action:v.userBehaviorsStatActions.loginPanelRegisterClick}),e.showUI("mobileRegister")):0==n.indexOf("1")?e.showUI("mailRegister"):0==n.indexOf("3")&&e.showUI("accountRegister"),y.id("tl_div_t").style.display="none",store.remove("usernick"),store.remove("userid")})):y.id("turnRegisterDiv").style.display="none",w.updateLoginWnd(!0),y.bind("to_qr","click.x",function(){b.stat({action:v.userBehaviorsStatActions.loginPanelQrClick}),xll.showUI("login","qrLogin"),y.id("to_qr").style.display="none",y.id("to_pc").style.display="block",y.id("lg_tabs").style.display="none",y.id("uesr-pic").style.display="none",e._enableThirdLogin&&(y.id("tl_div").style.display="none"),y.id("turnRgDiv").style.display="none"}),y.bind("to_pc","click.x",function(){b.stat({action:v.userBehaviorsStatActions.loginPanelQrCodeAccoutClick}),xll.showUI("login","accountLogin"),y.id("to_pc").style.display="none",y.id("to_qr").style.display="block",y.id("lg_tabs").style.display="block",y.id("uesr-pic").style.display="block",e._enableThirdLogin&&(y.id("tl_div").style.display="flex"),y.id("turnRgDiv").style.display="flex"}),e._enableQrLogin&&t.length>1&&(y.id("qr_switch").style.display="block"),y.bind("pr_agree","click.x",function(e){e&&e.target&&"boolean"==typeof e.target.checked&&b.stat({action:v.userBehaviorsStatActions.registPanelRegistAgree,extData:{isAgree:e.target.checked?1:0}})}),y.bind("qrcode-img","mouseenter",function(t){var n=(new Date).getTime();n-e._lastLoginPanelQrCodeHoverStatTime>=3e4&&(e._lastLoginPanelQrCodeHoverStatTime=n,b.stat({action:v.userBehaviorsStatActions.loginPanelQrCodeHover}))}),y.bind(y.id("al_forget"),"click.x",function(e){e.preventDefault(),e.stopPropagation(),b.stat({action:v.userBehaviorsStatActions.loginPanelForgetPasswordClick});var t=e.target.href;w.openNewTab({url:t,business:"forget"}),w.onLoginWndClose("forget")})},tAccountLogin:function(){e.showUI("accountLogin"),M>-1&&(t.al_usrimg.style.display="block"),store.set("usernick",S),store.set("userid",L)},tMobileLogin:function(){e.showUI("mobileLogin"),t.al_usrimg.style.display="none",store.remove("usernick"),store.remove("userid")},updateDropUsers:function(){A="",w.getXdasDatas("file","users",function(n){for(var i=n||[],r=0;r<i.length;r++)i[r].uName&&x.push(i[r]);if(!((R=x.length)<1)){var o=y.id("login-drop-users"),a=o.children[0];k||(k=!0,y.bind(y.id("login__arrow"),"click.x",function(e){e.preventDefault(),e.stopPropagation(),b.stat({action:v.userBehaviorsStatActions.loginPanelHistorySelect}),o.style.display="none"==o.style.display?"block":"none"})),N=x,t.al_usrimg.style.display="block",M=0;var s=x[0];e.setUserIn(s);for(var c="",l=0;l<x.length;l++){c+='<li id="login__name_'+l+'"><span class="login__name">'+x[l].uName+'</span><a href="#" class="login__close" title="删除"></a></li>'}a.innerHTML=c}})},setLoginDrop:function(){T=store.get("userid"),y.bind(t.input_username,"input.x",function(){store.set("xl_loginkey","0"),t.input_password.value="",t.al_usrimg.style.display="none",M=-1,store.remove("usernick")}),y.bind(t.input_password,"input.x",function(){store.set("xl_loginkey","0")});var n=y.id("login-drop-users"),i=n.children[0];n.style.display="none",this.updateDropUsers(),y.bind(y.id("login-drop-del"),"click.x",function(e){e.preventDefault(),y.id("login-pop-txt").innerHTML="确定要从列表中删除所有的帐号记录？",y.id("login-pop").style.display="block",M=-1}),y.bind(y.id("login-pop-submit"),"click.x",function(e){if(e.preventDefault(),store.remove("loginkey"),t.input_password.value="",t.input_username.value="",R-=1,M>-1&&R>0){for(var i=[],r=0;r<N.length;r++)N[r].uName!=D?i.push(N[r]):A=N[r].userid;N=i,y.id("login__name_"+M).style.display="none"}else N=[],y.unbind(y.id("login__arrow"),"click.x"),n.style.display="none",t.check_remember.checked=!0,A="all",k=!1;w.saveToXdas("file","users",(0,o["default"])(N),function(){}),y.id("login-pop").style.display="none",t.al_usrimg.style.display="none",M=-1,A!=T&&"all"!=A||w.saveToXdas("file","userinfo",(0,o["default"])({userid:""}),function(){})}),y.bind(y.id("login-pop-cancel"),"click.x",function(e){e.preventDefault(),y.id("login-pop").style.display="none"}),y.bind(i,"click.x",function(t){t.preventDefault();var n=""!=t.target.id?t.target.id:t.target.parentNode.id;M=n.replace(/[^0-9]/gi,""),"login__close"==t.target.className?(D=document.getElementById(n).childNodes[0].textContent,y.id("login-pop-txt").innerHTML="确定要从列表中删除该帐号记录？",y.id("login-pop").style.display="block"):e.setUserIn(x[M])})},setUserIn:function(e){t.input_username.value=e.uName,L=e.userid,w.getUserIMg(e.userid,function(e){e?(t.al_usrimg.src=e,t.al_usrimg.style.display="block"):(t.al_usrimg.style.display="none",M=-1)}),store.set("userid",e.userid),L=e.userid,store.set("usernick",e.usernick),store.set("uName",e.uName),S=e.usernick,y.id("login-drop-users").style.display="none",e.xl_autologin&&e.userid&&e.loginkey?(t.input_password.value="1234567890",store.set("xl_loginkey","1"),store.set("loginkey",e.loginkey)):(t.input_password.value="",store.set("xl_loginkey","0")),t.check_remember.checked=!!e.xl_autologin},initThirdLogin:function(t,n,i,r,o,a){var c=arguments.length>6&&arguments[6]!==undefined?arguments[6]:[];p["default"].isArray(a)&&a.length>0&&(e._enableThirdLogin=!0,w.getXdasDatas("file","lastLoginInfo",function(n){var i=undefined;n&&"string"==typeof n.nearlyThirdType&&n.nearlyThirdType.length>0&&(i=n.nearlyThirdType);var r=e.thirdLoginDoms(),u=xlQuickLogin.Util.getConfig("THIRD_LOGIN_TARGET_PARENT"),p=u?"_parent":"_blank",g=u?/\?/.test(t)?"&needrefresh=1":"?needrefresh=1":"";t+=g,t=encodeURIComponent(decodeURIComponent(t)),t+=o;for(var h="./theme/xlx/img/new/",m={qq:{c:"qq",title:"QQ",src:h+"qq.png"},weixin:{c:"weixin",title:"微信",src:h+"weixin.png"},sina:{c:"weibo",title:"新浪微博",src:h+"sina.png"},alipay:{c:"zfb",title:"支付宝",src:h+"alipay.png"},xiaomi:{c:"xiaomi",title:"小米",src:h+"xiaomi.png"},aq360:{c:"360",title:"360",src:h+"aq360.png"},renren:{c:"renren",title:"人人网",src:h+"renren.png"},tianyi:{c:"tianyi",title:"天翼",src:h+"tianyi.png"}},x={},S={},L=0;L<a.length;L++){var T=a[L];S[T]=m[T],x[S[T].c]=T}m=S;var A={},k={},E=-1;if("string"==typeof i){var C=x[i];C&&-1===(E=c.indexOf(C))&&(E=c.length-1,c[E]=C)}if(c&&c.length>0){for(var P=0,I=c.length;P<I;P++){var N=c[P];A[N]=m[N],delete m[N]}for(var M in m)k[M]=m[M]}else{var D=0;for(var M in m)D<4?A[M]=m[M]:k[M]=m[M],D+=1}var R,j,F="http://login.i.xunlei.com/connect_login.php?connect=",U="https://login-i-ssl.xunlei.com/thirdlogin?thirdType=";j=f.isMobile?"https://login-i-ssl.xunlei.com/thirdlogin?thirdAppid="+l.WX_APPID+"&thirdType=weixin&redirectUrl=":"https://login-i-ssl.xunlei.com/thirdlogin?thirdType=weixin&redirectUrl=";var B=[];for(R in A)B.push('<a id="icon_'+R+'" title="'+A[R].title+'" class="login_img" target="'+p+'" '),"weixin"===R?B.push('href="'+j+t+'">'):"renren"===R?B.push('href="'+F+A[R].c+"&redirect_url="+t+'">'):B.push('href="'+U+A[R].c+"&redirectUrl="+t+'">'),B.push('<img id="img_'+R+'" alt="'+A[R].title+'" src="'+A[R].src+'"></a>');var H=[],q="";for(R in k)q="",q+='<a id="icon_'+R+'" title="'+k[R].title+'" class="login_link" target="_blank" ',q+="xiaomi"===R?'href="https://open-api-auth.xunlei.com/platform?m=BindOauth&redirect_uri='+t+'">':"tianyi"===R?'href="https://login-i-ssl.xunlei.com/thirdlogin?thirdType=tianyi&redirectUrl='+t+'">':"renren"===R?'href="'+F+k[R].c+"&redirect_url="+t+'">':'href="'+U+k[R].c+"&redirectUrl="+t+'">',q+=k[R].title+"</a>",H.push(q);for(R in r)_=r[R],_.div.style.display="qrLogin"===e.currentUIShowType?"none":"",_.icons.innerHTML=B.join("")+" "+_.icons.innerHTML,_.icons2.innerHTML=H.join(' <span class="d_line">|</span> '),y.bind(_.arrow,"click.x",function(e){e.preventDefault(),e.stopPropagation();var t=y.id("tl_icons2"+e.target.id.split("__").pop());t.style.display="none"==t.style.display?"block":"none"});if(r.length>0&&E>-1){var G=y.id("last_third_login_type_tip");G&&(G.style.display="",b.stat({action:v.userBehaviorsStatActions.lastLoginType}),setTimeout(function(){G.style.display="none"},5e3));var V=y.id("last_third_login_tip_close");V&&y.bind(V,"click",function(e){e.preventDefault(),e.stopPropagation(),G&&(G.style.display="none")}),1===E&&d.addClass(G,"login-tooltip--second")}var J=(0,s["default"])({},A,k);for(var W in J)!function(e){var t=J[e],n=y.id("icon_"+e);n&&y.bind(n,"click.x",function(e){e.preventDefault(),e.stopPropagation();var n=e.target.href||!1;if(n){var i=t.c;b.stat({action:v.userBehaviorsStatActions.thrLoginClick,extData:{button:i}}),_.icons2.style.display="none",y.id("login-drop-users").style.display="none",w.openNewTab({url:n,name:O.openNewTabName.thirdLogin,business:"thirdLogin"}),w.onLoginWndClose("third",{thirdType:i,clickTime:(new Date).getTime()})}})}(W)}));for(var u=document.getElementsByClassName("xl_tos_group"),g=0,h=u.length;g<h;g++)y.bind(u[g],"click.x",function(e){e.preventDefault(),e.stopPropagation();var t=e.target.href;w.openNewTab({url:t,business:"agreement"})});this.setLoginDrop()},hideRememberCheckbox:function(){var t=e.accountLoginDoms(),n=e.mobileLoginDoms();t.check_remember.checked=!1,t.check_remember_div.style.display="none",n.check_remember.checked=!1,n.check_remember_div.style.display="none"},setText:function(e){var t=y.id("al_submit"),n=y.id("ml_submit"),i=y.id("mr_submit"),r=y.id("pr_submit"),o=y.id("ar_submit");"string"==typeof e.login_button_text&&e.login_button_text&&(t.innerHTML=e.login_button_text,t.title=e.login_button_text,n.innerHTML=e.login_button_text,n.title=e.login_button_text),"string"==typeof e.register_button_text&&e.register_button_text&&(i.innerHTML=e.register_button_text,i.title=e.register_button_text,r.innerHTML=e.register_button_text,r.title=e.register_button_text,o.innerHTML=e.register_button_text,o.title=e.register_button_text)},thirdLoginDoms:function(){return _||(_=[{arrow:y.id("tl_arrow___t"),div:y.id("tl_div_t"),icons:y.id("tl_icons_t"),icons2:y.id("tl_icons2_t")},{arrow:y.id("tl_arrow___"),div:y.id("tl_div"),icons:y.id("tl_icons"),icons2:y.id("tl_icons2_")}]),_},accountLoginDoms:function(){return t||(t={input_username:y.id("al_u"),input_password:y.id("al_p"),input_captcha:y.id("al_c"),warn:y.id("al_warn"),captcha_container:[y.id("al_c_div")],captcha_img:y.id("al_c_img"),captcha_fresh:[y.id("al_c_img")],check_remember:y.id("al_remember"),check_remember_div:y.id("al_remember_div"),button_submit:y.id("al_submit"),al_usrimg:y.id("al_usrimg")}),t},mobileLoginDoms:function(){return n||(n={input_mobile:y.id("ml_m"),input_mobile_label:y.id("ml_m_l"),input_code:y.id("ml_c"),button_getcode:y.id("ml_gc"),warn:y.id("ml_warn"),login_form:y.id("pl_form"),input_captcha:y.id("pl_c"),captcha_container:[y.id("pl_c_div")],captcha_img:y.id("pl_c_img"),captcha_fresh:[y.id("pl_c_img")],captcha_confirm:y.id("pl_gc"),check_remember:y.id("ml_remember"),check_remember_div:y.id("ml_remember_div"),button_submit:y.id("ml_submit")}),n},qrLoginDoms:function(){return i||(i={qrbox:y.id("qrcode-box"),img:y.id("qrcode-img"),referer:y.id("qrcode-referer"),flash:y.id("qrcode-flash"),succ:y.id("qrcode-succ"),desc1:y.id("qrcode-desc1"),desc2:y.id("qrcode-desc2"),desc3:y.id("qrcode-desc3")}),i},mailRegisterDoms:function(){return r||(r={input_mail:y.id("mr_m"),input_password:y.id("mr_p"),warn:y.id("mr_warn"),input_captcha:y.id("mr_c"),captcha_container:[y.id("mr_c_div")],captcha_img:y.id("mr_c_img"),captcha_fresh:[y.id("mr_c_img")],button_submit:y.id("mr_submit"),agree:y.id("mr_agree")}),r},mobileRegisterDoms:function(){return a||(a={input_mobile:y.id("pr_m"),input_code:y.id("pr_c"),button_getcode:y.id("pr_gc"),warn:y.id("pr_warn"),button_submit:y.id("pr_submit"),register_form:y.id("pr_form"),input_captcha:y.id("pr_captcha"),captcha_container:[y.id("pr_c_div")],captcha_img:y.id("pr_c_img"),captcha_fresh:[y.id("pr_c_img")],captcha_confirm:y.id("pr_ccb"),input_password:y.id("pr_p"),button_finish:y.id("pr_finish"),button_pass:y.id("pr_pass"),warn2:y.id("pr_warn2"),agree:y.id("pr_agree")}),a},accountRegisterDoms:function(){return u||(u={input_account:y.id("ar_a"),input_password:y.id("ar_p"),input_password2:y.id("ar_p2"),warn:y.id("ar_warn"),input_captcha:y.id("ar_c"),captcha_container:[y.id("ar_c_div")],captcha_img:y.id("ar_c_img"),captcha_fresh:[y.id("ar_c_img")],button_submit:y.id("ar_submit"),agree:y.id("ar_agree")}),u}}}())},function(e,t){e.exports='<div class=t_boxc style=position:relative> <a href=# class=login__close id=close title=关闭></a> <div id=show_panel_wrap style=display:none;width:100%;height:100%;position:absolute;z-index:1000;top:0;left:0> <iframe src="" frameborder=0 id=iframe_panel></iframe> </div> <div class=login-switch id=qr_switch><i class=quick id=to_qr></i><i class=static id=to_pc></i></div> <div id=password_box class=tb_wpb> <div class=login__uesr-pic> <img style=display:none alt=头像 id=al_usrimg_2> </div> <div class=hd> <h3 style=text-align:center> <a href=javascript:void(0) class=on>{ hitblock.title }</a> </h3> </div> <div class="bd bd_bg"> <div class=input_area id=password_change> <p class=p_texta>{ hitblock.changeTip }</p> <form action="" class=form_logoin> <div class=in_box> <input type=text value="" style=display:none> <input type=password value="" class=in_txt id=password_change_input placeholder=6-16位,含字母/数字/符号至少两种(除空格)> </div> <div class=in_box> <p id=password_change_error class="in_box_cite color_red error_block" style=top:0></p> </div> <div class="pay_btn password_change_btn" style=margin-top:35px> <a id=password_change_submit class="" title=确认 href=javascript:void(0)>确认</a> </div> </form> </div> <div class=input_area id=vaildate_mobile style=display:none> <p class=p_texta>{ hitblock.validateTip }</p> <form action="" class=form_logoin> <div class="in_box pl_hide_for_sms_captcha"> <label id=validate_mobile_value_l for=validate_mobile_value style=display:none>{ mobileLogin.mobile }</label> <input type=text value="" class=in_txt id=validate_mobile_value placeholder="{ mobileLogin.mobile }"> </div> <div class="in_box in_boxa" style=display:none id=validate_mobile_captcha_box> <input type=text value="" class=in_txt id=validate_mobile_captcha_value placeholder=输入验证码> <a href=javascript:void(0) title="" class=verify_img> <img id=validate_mobile_captcha_img height=44 width=90 src="" alt=""> </a> </div> <div class="in_box in_boxa pl_hide_for_sms_captcha"> <input type=text value="" class=in_txt id=validate_mobile_code placeholder=请输入短信验证码> <a href=javascript:void(0) title=获取验证码 class=verify_btn id=validate_mobile_get_code>获取验证码</a> </div> <div class=in_box> <p id=validate_mobile_error class="in_box_cite color_red error_block" style=top:0></p> </div> <div class="pay_btn password_change_btn" style=margin-top:35px> <a id=validate_mobile_submit class="" title=确认 href=javascript:void(0)>确认</a> </div> </form> </div> </div> </div> <div id=login_box class=tb_wpb> <div class=login__uesr-pic id=uesr-pic> <img style=display:none alt=头像 id=al_usrimg> </div> <div class=hd id=lg_tabs> <h3> <a id=al_tab href=javascript:void(0) class=on>{ accountLogin.title }</a> <a id=ml_tab href=javascript:void(0) class="">{ mobileLogin.title }</a> </h3> </div> <div class=safe_login_alert>{ hitblock.tip }</div> <div class="bd bd_bg"> <div class=input_area id=account_login> <form action="" class=form_logoin> <p class="in_box_cite color_red" id=al_warn style=display:none></p> <div class=in_box> <input type=text value="" class=in_txt autofocus=autofocus id=al_u placeholder="{ accountLogin.username }"> <span href=javascript:; class=login__arrow id=login__arrow></span> <div class=login-drop style=display:none id=login-drop-users> <ul> </ul> <a href=# class=link-clear id=login-drop-del>清除历史记录</a> </div> <div class=login-drop id=al_mail_association style=display:none> <p class=login-tips>请选择邮箱类型</p> <ul id=al_mail_association_ul> </ul> </div> </div> <div class=in_box> <input type=password value="" class=in_txt id=al_p placeholder="{ accountLogin.password }"> <a href=javascript:; class=login__eye id=al_password_eye></a> <div class="login-tooltip login-tooltip--password" id=al_password_eye_tip style=display:none> <p>已为您开启密码明文</p> </div> </div> <div class="in_box in_boxa" id=al_c_div> <input type=text value="" class=in_txt id=al_c placeholder="{ captcha.code }"> <div href=javascript:void(0) class=verify_img> <img id=al_c_img height=44 width=90 src="" alt=""> </div> </div> <div class="in_box in_boxa" style=display:block> <label id=al_remember_div for=al_remember class=cbox> <input type=checkbox id=al_remember name="" class=chk checked=checked />{ accountLogin.autoLogin }</label> </div> <div class=pay_btn> <a id=al_submit href=javascript:void(0)>{ accountLogin.btnLogin }</a> </div> </form> </div> <div class=input_area id=mobile_login style=display:none> <form action="" class=form_logoin id=pl_form> <p class="in_box_cite color_red" id=ml_warn></p> <div class="in_box pl_hide_for_sms_captcha"> <input type=text value="" class=in_txt id=ml_m placeholder=请输入手机号> </div> <div class="in_box in_boxa pl_hide_for_sms_captcha" style=""> <input type=text value="" class=in_txt id=ml_c placeholder="{ mobileLogin.code }"> <a href=javascript:void(0) title="{ mobileLogin.btnCode }" class="verify_btn verify_grey_btnverify_grey_btn" id=ml_gc>{ mobileLogin.btnCode }</a> </div> <div class="in_box in_boxa" id=pl_c_div> <input type=text value="" class=in_txt id=pl_c placeholder="{ captcha.code }"> <a href=javascript:void(0) title="" class=verify_img> <img id=pl_c_img height=44 width=112 src="" alt=""> </a> <p style=display:block;font-size:11px;padding-top:5px>{ mobileLogin.tip }</p> <div class=pay_btn> <a id=pl_gc>{ mobileLogin.tipBtn }</a> </div> </div> <div class="in_box in_boxa pl_hide_for_sms_captcha" style=display:block> <label id=ml_remember_div for=ml_remember class=cbox> <input type=checkbox id=ml_remember class=chk checked=checked />{ mobileLogin.autoLogin }</label> </div> <div class="pay_btn pl_hide_for_sms_captcha"> <a id=ml_submit href=javascript:void(0)>{ mobileLogin.btnLogin }</a> </div> </form> </div> <div class="input_area scan_box" id=qr_login> <div class=scan_hd> <p>{ qrLogin.title }</p> </div> <div class=scan_bd> <div class=qrcode-main id=qrcode-box> <div class=qrcode-img> <img src="{ qrLogin.default }" id=qrcode-img alt=二维码> <img class=qrcode-help src="{ qrLogin.help }" alt=指引> </div> <div class=qrcode-err id=qrcode-referer> <a href=javascript:void(0); class=qrcode-flash id=qrcode-flash>刷新二维码</a> </div> </div> <div class=qrcode-main style=display:none id=qrcode-succ> <i class=qrcode-succ></i> </div> <div class=qrcode-desc id=qrcode-desc1> { qrLogin.desc1 } </div> <div class=qrcode-desc id=qrcode-desc2> { qrLogin.desc2 } </div> <div class=qrcode-desc id=qrcode-desc3> { qrLogin.desc3 } </div> <div class=qrcode-tips> { qrLogin.desc4 } </div> </div> </div> <div class=ty_box style=display:none id=client_login> <p class=p_texta>{ ctLogin.tip }</p> <div class=pro_entra> <a href=javascript:void(0) class=pro_user title=""> <img id=cl_img> <p id=cl_nick class=pro_name></p> <span id=cl_u class=pro_num></span> </a> </div> </div> <div class=other_login_wp id=tl_div style=display:none> <span class=tit>其他登录</span> <div class=other_login id=tl_icons> <div class=login-tooltip id=last_third_login_type_tip style=display:none> <p>上次登录方式<a href=javascript:; class=login-tooltip__close id=last_third_login_tip_close></a></p> </div> </div> <div class=other_login2 id=tl_icons2_ style=display:none> </div> <a href=javascript:; class=link_more id=tl_arrow___> <i class=ico_more></i> </a> </div> <div class=login-res id=turnRgDiv> <p id=turnRegisterDiv class=pay_b_logo> <a id=turnRegister href=javascript:void(0) title="" class=text_cite>{ register.title }</a> </p> <a href="https://i.xunlei.com/xluser/validate/enter/findpwd_enter.html?purl=https://i.xunlei.com/xluser/login.html" title=忘记密码？ id=al_forget class=text_cite>忘记密码？</a> </div> </div> <div class=login-pop id=login-pop> <div class=login-pop__body> <span class=icon-note></span> <p id=login-pop-txt>确定要从列表中删除所有的帐号记录？</p> </div> <div class=login-pop__footer> <a href=javascript:; class=td-button id=login-pop-submit>确定</a> <a href=javascript:; class="td-button td-button--secondary" id=login-pop-cancel>取消</a> </div> </div> </div> <div id=register_box class=tb_wpb> <div class=hd> <h3> <a id=pr_tab href=javascript:void(0) class=on>{ register.phone }</a> <a id=mr_tab href=javascript:void(0)>{ register.mail } </a> <a id=ar_tab href=javascript:void(0)>{ register.account }</a> <a id=ir_tab href=javascript:void(0)>{ register.app }</a> </h3> </div> <div class="bd bd_bg"> <div class=input_area id=app_register style=display:none> <p class=tit>{ appRegister.title }</p> <p class=qr><img src="{ appRegister.qr }"/></p> </div> <div class=input_area id=mail_register> <form action="" class=form_logoin> <p class="in_box_cite color_red" id=mr_warn style=display:none></p> <div class=in_box> <label id=mr_m_l for=mr_m style=display:none>{ mailRegister.mail }</label> <input type=text value="" class=in_txt id=mr_m placeholder="{ mailRegister.mail }"> </div> <div class=in_box> <label id=mr_p_l for=mr_p style=display:none>{ mailRegister.password }</label> <input type=password value="" class=in_txt id=mr_p placeholder="{ mailRegister.password }"> </div> <div class="in_box in_boxa" id=mr_c_div> <label id=mr_c_l for=mr_c style=display:none>{ captcha.code }</label> <input type=text value="" class=in_txt id=mr_c placeholder="{ captcha.code }"> <div href=javascript:void(0) title="verify code" class=verify_img> <img id=mr_c_img height=44 width=90 src=""> </div> </div> <div class="in_box in_boxa" style=display:block> <label id=mr_agree_div for=mr_agree class=cbox> <input type=checkbox id=mr_agree name="" class=chk>{ tos.tip } { tos.link }</label> </div> <div class=pay_btn> <a id=mr_submit class=grey href=javascript:void(0)>{ register.btnSubmit }</a> </div> </form> </div> <div class=input_area id=account_register> <form action="" class=form_logoin> <p class="in_box_cite color_red" id=ar_warn style=display:none></p> <div class=in_box> <label id=ar_a_l for=ar_a style=display:none>{ accountRegister.name }</label> <input type=text value="" class=in_txt id=ar_a placeholder="{ accountRegister.name }"> </div> <div class=in_box> <label id=ar_p_l for=ar_p style=display:none>{ accountRegister.password }</label> <input type=password value="" class=in_txt id=ar_p placeholder="{ accountRegister.password }"> </div> <div class=in_box> <label id=ar_p2_l for=ar_p2 style=display:none>{ accountRegister.rePassword }</label> <input type=password value="" class=in_txt id=ar_p2 placeholder="{ accountRegister.rePassword }"> </div> <div class="in_box in_boxa" id=ar_c_div> <label id=ar_c_l for=ar_c style=display:none>{ captcha.code }</label> <input type=text value="" class=in_txt id=ar_c placeholder="{ captcha.code }"> <div href=javascript:void(0) title="verify code" class=verify_img> <img id=ar_c_img height=44 width=90 src=""> </div> </div> <div class="in_box in_boxa" style=display:block> <label id=ar_agree_div for=ar_agree class=cbox> <input type=checkbox id=ar_agree name="" class=chk>{ tos.tip }{ tos.link }</label> </div> <div class=pay_btn> <a id=ar_submit class=grey href=javascript:void(0)>{ register.btnSubmit }</a> </div> </form> </div> <div class=input_area id=mobile_register> <form action="" class=form_logoin id=pr_form> <p class="in_box_cite color_red" id=pr_warn></p> <div class="in_box pr_hide_for_sms_captcha"> <input type=text value="" class=in_txt id=pr_m placeholder="{ phoneRegister.phone }"> </div> <div class="in_box in_boxa pr_hide_for_sms_captcha"> <input type=text value="" class=in_txt id=pr_c placeholder="{ phoneRegister.code }"> <a href=javascript:void(0) class="verify_btn verify_grey_btnverify_grey_btn" id=pr_gc>{ phoneRegister.btnCode }</a> </div> <div class="in_box in_boxa" id=pr_c_div style=display:none> <input type=text value="" class=in_txt id=pr_captcha placeholder="{ captcha.code }"> <a href=javascript:void(0) title="" class=verify_img> <img id=pr_c_img height=44 width=112 src="" alt=""> </a> <p style=display:block;font-size:11px;padding-top:5px>{ phoneRegister.note }</p> <div class=pay_btn> <a id=pr_ccb href=javascript:void(0)>{ phoneRegister.ok }</a> </div> </div> <div class="in_box in_boxa xl_agree" style=display:block> <label id=pr_agree_div for=pr_agree class=cbox> <input type=checkbox id=pr_agree name="" class=chk>{ tos.tip }{ tos.link }</label> </div> <div class="pay_btn pr_hide_for_sms_captcha"> <a id=pr_submit class=grey href=javascript:void(0)>{ register.btnSubmit }</a> </div> </form> <div class=account> <a href=# id=turnAccountLogin>{ register.has }</a> </div> </div> <div class=input_area id=mobile_register2 style=display:none> <form action="" class=form_logoin> <p class="in_box_cite color_red" id=pr_warn2></p> <div class=in_box> <input type=text value="" class=in_txt id=pr_p placeholder="{ phoneRegister.password }"> <input type=text style=display:none> <p class=in_ttext>{ phoneRegister.tip }</p> </div> <div class=pay_btn> <a id=pr_finish href=javascript:void(0)>{ phoneRegister.btnSum }</a> </div> </form> </div> </div> <div id=pr_pass_div class=ty_tips style=display:none> <p class=""> <a id=pr_pass href=javascript:void(0)>{ register.btnReg }</a> </p> </div> <div id=turnLoginDiv class=ty_tips style=display:none> <p class="" style=display:none> <a id=turnMobileLogin title="" class="" href=javascript:void(0)>{ register.mobileLogin }</a> </p> </div> <div class=tl_t style="padding:0 28px;display:none"> <div class=other_login_wp id=tl_div_t> <span class=tit></span> <div class=other_login id=tl_icons_t> </div> <div class=other_login2 id=tl_icons2_t> </div> <a href=javascript:; class=link_more id=tl_arrow___t> <i class=ico_more></i> </a> </div> </div> </div> </div> '},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){undefined;return t!==undefined?t:e}function o(e,t){var n=JSON.parse((0,l["default"])(e));for(var i in t){var a=t[i];if("object"===(void 0===a?"undefined":(0,s["default"])(a))){var c=n[i];c===undefined||null===c?n[i]=JSON.parse((0,l["default"])(a)):n[i]=o(n[i],a)}else n[i]=r(n[i],a)}return n}t.__esModule=!0;var a=n(15),s=i(a),c=n(12),l=i(c);t.combineEtcdValue=r,t.combineEtcdConfig=o}]);