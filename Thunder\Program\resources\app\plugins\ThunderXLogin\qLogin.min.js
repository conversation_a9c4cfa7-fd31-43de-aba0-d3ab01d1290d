!function(e){function t(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var n={};t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t.h="076023064fa53798dcba",t.cn="qLogin",t(t.s=196)}([function(e,t,n){"use strict";t.__esModule=!0,t["default"]=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t){var n=e.exports={version:"2.6.11"};"number"==typeof __e&&(__e=n)},function(e,t,n){var r=n(34)("wks"),i=n(22),o=n(1).Symbol,u="function"==typeof o;(e.exports=function(e){return r[e]||(r[e]=u&&o[e]||(u?o:i)("Symbol."+e))}).store=r},function(e,t,n){e.exports=!n(14)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t,n){var r=n(6);e.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,n){var r=n(1),i=n(2),o=n(19),u=n(9),a=n(10),s=function(e,t,n){var c,f,l,p=e&s.F,d=e&s.G,h=e&s.S,g=e&s.P,_=e&s.B,v=e&s.W,y=d?i:i[t]||(i[t]={}),m=y.prototype,I=d?r:h?r[t]:(r[t]||{}).prototype;d&&(n=t);for(c in n)(f=!p&&I&&I[c]!==undefined)&&a(y,c)||(l=f?I[c]:n[c],y[c]=d&&"function"!=typeof I[c]?n[c]:_&&f?o(l,r):v&&I[c]==l?function(e){var t=function(t,n,r){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,r)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(l):g&&"function"==typeof l?o(Function.call,l):l,g&&((y.virtual||(y.virtual={}))[c]=l,e&s.R&&m&&!m[c]&&u(m,c,l)))};s.F=1,s.G=2,s.S=4,s.P=8,s.B=16,s.W=32,s.U=64,s.R=128,e.exports=s},function(e,t,n){var r=n(5),i=n(47),o=n(36),u=Object.defineProperty;t.f=n(4)?Object.defineProperty:function(e,t,n){if(r(e),t=o(t,!0),r(n),i)try{return u(e,t,n)}catch(a){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){var r=n(8),i=n(20);e.exports=n(4)?function(e,t,n){return r.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,n){"use strict";(function(t){function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){return function(){return t.apply(e,Array.prototype.slice.call(arguments,0))}}function o(e,t){return Array.prototype.slice.call(e,t||0)}function u(e,t){s(e,function(e,n){return t(e,n),!1})}function a(e,t){var n=c(e)?[]:{};return s(e,function(e,r){return n[r]=t(e,r),!1}),n}function s(e,t){if(c(e)){for(var n=0;n<e.length;n++)if(t(e[n],n))return e[n]}else for(var r in e)if(e.hasOwnProperty(r)&&t(e[r],r))return e[r]}function c(e){return null!=e&&"function"!=typeof e&&"number"==typeof e.length}function f(e){return e&&"[object Function]"==={}.toString.call(e)}function l(e){return e&&"[object Object]"==={}.toString.call(e)}var p=n(95),d=r(p),h=n(56),g=r(h),_=function(){return g["default"]?g["default"]:function(e,t,n,r){for(var i=1;i<arguments.length;i++)u(Object(arguments[i]),function(t,n){e[n]=t});return e}}(),v=function(){if(d["default"])return function(e,t,n,r){var i=o(arguments,1);return _.apply(this,[(0,d["default"])(e)].concat(i))};var e=function(){};return function(t,n,r,i){var u=o(arguments,1);return e.prototype=t,_.apply(this,[new e].concat(u))}}(),y=function(){return String.prototype.trim?function(e){return String.prototype.trim.call(e)}:function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}}(),m="undefined"!=typeof window?window:t;e.exports={assign:_,create:v,trim:y,bind:i,slice:o,each:u,map:a,pluck:s,isList:c,isFunction:f,isObject:l,Global:m}}).call(t,n(55))},function(e,t,n){e.exports={"default":n(72),__esModule:!0}},function(e,t,n){var r=n(41),i=n(29);e.exports=function(e){return r(i(e))}},function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0;var i=n(112),o=r(i),u=n(114),a=r(u),s="function"==typeof a["default"]&&"symbol"==typeof o["default"]?function(e){return typeof e}:function(e){return e&&"function"==typeof a["default"]&&e.constructor===a["default"]&&e!==a["default"].prototype?"symbol":typeof e};t["default"]="function"==typeof a["default"]&&"symbol"===s(o["default"])?function(e){return void 0===e?"undefined":s(e)}:function(e){return e&&"function"==typeof a["default"]&&e.constructor===a["default"]&&e!==a["default"].prototype?"symbol":void 0===e?"undefined":s(e)}},function(e,t){e.exports=!0},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t){e.exports={}},function(e,t,n){var r=n(21);e.exports=function(e,t,n){if(r(e),t===undefined)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){var n=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(e===undefined?"":e,")_",(++n+r).toString(36))}},function(e,t,n){"use strict";(function(e){function n(){return window||e}function r(e){var t="";switch(e){case f.insideIframe:case f.outsideIframe:case f.node:t=e;break;default:t="default"}return t+"WebSdkGlobalObject_CA7FFF8A_0F5B_4654_822B_98B9E74F23DD"}function i(e){if(null===l){p=e;var t=r(e);l={},n()[t]=l}}function o(){if(null===l){var e=n();l=e[r(f.insideIframe)]||e[r(f.outsideIframe)]||e[r(f.pluginIndex)]}return l}function u(e){return o()[e]!==undefined}function a(e,t){o()[e]=t}function s(e){return o()[e]}function c(){return p}t.__esModule=!0,t.getGBObjName=r,t.init=i,t.hasAttr=u,t.setAttr=a,t.getAttr=s,t.getEnvType=c;var f=(t.gbAttrNames={config:"config",stat:"stat",platformInfo:"platformInfo",innerQuickLogin:"innerQuickLogin",clientFeatureApi:"clientFeatureApi"},t.gbEnvTypes={insideIframe:"insideIframe",outsideIframe:"outsideIframe",pluginIndex:"pluginIndex"}),l=null,p=undefined}).call(t,n(55))},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function o(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}function u(e){return document.getElementById(e)}function a(){return(65536*(1+Math.random())|0).toString(16).substring(1)}function s(e){for(var t="",n=e/4,r=0;r<n;r++)t+=a();return t}function c(e,t){undefined;return t>e.length?new Array(t-e.length+1).join("0")+e:e}function f(e){return e.getFullYear().toString()+c((e.getMonth()+1).toString(),2)+c(e.getDate().toString(),2)}t.__esModule=!0,t.BrowserVersion=t.BrowserType=t.mThunder=t.isWeixin=t.isXlMac=t.isXlx=t.isXl9=t.isXlMB=t.isXlPC=t.isMobile=t.UA=undefined;var l=n(15),p=r(l),d=n(0),h=r(d);t.id=u,t.S4=a,t.Guid=s,t.dateToDateString=f;var g=n(62),_=function(){function e(){(0,h["default"])(this,e),this.every=this.genLoop(),this.some=this.genLoop(!1)}return e.prototype.forEach=function(e,t){return i(e,t)},e.prototype.map=function(e,t){return o(e,t)},e.prototype.genLoop=function(){var e=!(arguments.length>0&&arguments[0]!==undefined)||arguments[0];return function(t,n){for(var r=-1,i=null==t?0:t.length;++r<i;){var o=n(t[r],r,t);if(0==e&&1==o)return!0;if(1==e&&0==o)return!1}return e}},e.prototype.has=function(e,t){return null!=e&&hasOwnProperty.call(e,t)},e.prototype.isEmpty=function(e){return e!=undefined&&("string"!=typeof e||!e.length)},e.prototype.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},e.prototype.decode=function(e){try{e=decodeURIComponent(e)}catch(t){e=unescape(e)}return e},e.prototype.escape=function(e){return e.replace(/([.*+?\^${}()|\[\]\/\\])/g,"\\$1")},e.prototype.getCookie=function(e){var t=document.cookie.match(RegExp("(?:^|;\\s*)"+this.escape(e)+"=([^;]*)"));return t?t[1]:""},e}(),v=t.UA=navigator.userAgent.toLocaleLowerCase(),y=t.isMobile=/mobile|android|iphone|ipad|iemobile/i.test(v),m=(t.isXlPC=/[ ]thunder(x?)\/[\d.]*/.test(v)||!!window["native"]&&!y,t.isXlMB=(/thunder/.test(v)||"undefined"!=typeof XLJSWebViewBridgeExport)&&y,t.isXl9=(0,g.checkIsXl9)(),t.isXlx=(0,g.checkIsXlx)(),t.isXlMac=/\bmac\b/.test(v)&&!!window.WebViewJavascriptBridge,t.isWeixin=/micromessenger/.test(v),t.mThunder=function(){return"undefined"!=typeof window.webkit&&(0,p["default"])(window.webkit.messageHandlers)&&"undefined"!=typeof window.webkit.messageHandlers.XLJSWebViewBridgeExport?{type:"ios",event:"XLJSWebViewBridgeExport"}:"undefined"!=typeof window.webkit&&(0,p["default"])(window.webkit.messageHandlers)&&"undefined"!=typeof window.webkit.messageHandlers.XLJSWebViewBridge?{type:"ios",event:"XLJSWebViewBridge"}:"undefined"!=typeof window.XLJSWebViewBridgeExport?{type:"android",event:"XLJSWebViewBridgeExport"}:"undefined"!=typeof window.XLJSWebViewBridge&&{type:"android",event:"XLJSWebViewBridge"}},["Trident","Electron","Android.Thunder","IOS.Thunder","Thunder","MicroMessenger","360","MQQBrowser","QQBrowser","UCBrowser","UBrowser","Metasr","Edge","Firefox","Maxthon","Chrome","Safari","mobile"]),I=function(e,t){var n=navigator.mimeTypes;for(var r in n)if(n[r][e]==t)return!0;return!1},E=function(){for(var e=m.length-1,t=0;t<=e;t++){var n=m[t];if("360"!==n){if(v.indexOf(n.toLowerCase())>-1)return n}else if(I("type","application/vnd.chromium.remoting-viewer"))return n}return"unknown"}(),b=(t.BrowserType=function(){return(y?"Mobile-":"PC-")+("Trident"==E?"IE":E)}(),t.BrowserVersion=function(){var e=E.toLowerCase();if(e.indexOf("unknown")>0)return"unknown";if("trident"==e)return v.indexOf("gecko")>0?"IE/11":"IE/"+v.match(/msie (\d+)/)[1];"360"!=e&&"android.thunder"!=e||(e="chrome"),"ios.thunder"==e&&(e="mobile");var t=new RegExp(e+"[ /][\\d.]+"),n=v.match(t);return n&&n.length>=0?n[0]:"unknown"}(),new _);t["default"]=b},function(e,t,n){var r=n(48),i=n(35);e.exports=Object.keys||function(e){return r(e,i)}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,n){var r=n(8).f,i=n(10),o=n(3)("toStringTag");e.exports=function(e,t,n){e&&!i(e=n?e:e.prototype,o)&&r(e,o,{configurable:!0,value:t})}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:function(e){return e},n={};return g["default"].forEach(e,function(e){var r=e.split("="),i=r.shift(),o=r.join("=");n[i]=t(o)}),n}function o(e){return"object"===("undefined"==typeof HTMLElement?"undefined":(0,p["default"])(HTMLElement))?e instanceof HTMLElement:e&&"object"===(void 0===e?"undefined":(0,p["default"])(e))&&null!==e&&1===e.nodeType&&"string"==typeof e.nodeName}function u(e,t){var n;n="string"==typeof e?E["class"](e):e,o(n)?n.style.display=t:g["default"].forEach(n,function(e){e.style.display=t})}function a(e){if(document.all)e.click();else{var t=document.createEvent("MouseEvent");t.initEvent("click",!0,!0),e.dispatchEvent(t)}}function s(e){return 0!==e.clientWidth&&0!==e.clientHeight&&0!==e.style.opacity&&"hidden"!==e.style.visibility}t.__esModule=!0,t.TIMEOUT=undefined;var c=n(0),f=r(c),l=n(15),p=r(l);t._display=u,t._click=a,t._visible=s;var d=n(97),h=n(24),g=r(h),_=n(96),v=r(_),y=void 0,m=!1,I=(t.TIMEOUT="TIMEOUT",function(){function e(){(0,f["default"])(this,e),this.id=h.id,this.binders=[],this.bind=this.genBind(h.id,window),this.unbind=this.genUnBind(h.id),this.loadScript=this.genLoadScript(document),this.getJson=this.genGetJson(window,document),this.loadStyle=this.genLoadScript(document,"link",{rel:"stylesheet",type:"text/css",media:"all"}),this.isJumpkey=function(e){return!(!e||!e.length||192!==e.length)},this["class"]=function(e){return document.getElementsByClassName(e)},this.text=function(e,t){if(e){var n=!1;if(e.innerText!==y)n="innerText";else if(e.textContent!==y)n="textContent";else{if(e.value===y)throw new Error("not support dom innerText or textContent");n="value"}return t===y?e[n]:e[n]=t}},this.checkCss=function(e){return"css"==e.split(".").pop().split("?")[0]},this.checkMobile=function(e){return/^1[\d]{10}$/.exec(e)},this.checkOverseasMobile=function(e){return/^00[0-9]{8,15}$/.exec(e)},this.checkPassword=function(e){return/^(?![\d]+$)(?![a-zA-Z]+$)(?![\`\-\=\[\]\\\;\'\,\.\/\~!@#$%^&*()_+{}|:"<>?]+$)[\da-zA-Z\`\-\=\[\]\\\;\'\,\.\/\~!@#$%^&*()_+{}|:"<>?]{6,16}$/.exec(e)},this.checkMail=function(e){return/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.exec(e)},this.trim=function(e){return e.replace(/(^\s*)|(\s*$)/g,"")},this.getConfig=function(e){if((e=e.toUpperCase())in d.CONFIG)return d.CONFIG[e]},this.isClientLogin=function(){return!m&&d.CONFIG.SET_ROOT_DOMAIN?parent.xlQuickLogin.isClientLogin:m}}return e.prototype.randString=function(){return Math.random().toString(36).replace(/[^a-z0-9]+/g,"")},e.prototype.isSessionid=function(e){return!(!e||!e.length)},e.prototype.getCookie=function(e){var t=!(arguments.length>1&&arguments[1]!==undefined)||arguments[1],n=document.cookie,r=i(n.split("; "),function(e){return t&&e!==y&&(e=g["default"].decode(e)),e});if(e){var o=r[e];return o==y?"":o}return r},e.prototype.setCookie=function(e,t,n){var r,i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:d.CONFIG.DOMAIN,o=arguments[4],u=arguments[5],n=!!n&&new Date((new Date).getTime()+n).toGMTString();r=e+"="+escape(t),r+="; path="+(o||"/"),r+="; domain="+i,u&&(r+="; secure"),n&&(r+="; expires="+n),document.cookie=r},e.prototype.delCookie=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:d.CONFIG.DOMAIN,n=arguments[2],r=arguments[3];this.setCookie(e,"",-6e4,t,n,r)},e.prototype.genBind=function(e,t){var n=this;return function(r,i,o,u){function a(e){e=e||t.event,e.target||(e.target=e.srcElement,e.preventDefault=function(){this.returnValue=!1},e.stopPropagation=function(){this.cancelBubble=!0}),!1===o.call(u||this,e)&&(e.preventDefault(),e.stopPropagation())}if("function"==typeof o){if("string"==typeof r&&(r=e(r)),!r)throw new Error("bind on an undefined target");var s=i.split(".").shift();n.binders.push({obj:r,handler:a,type:i}),r.attachEvent?r.attachEvent("on"+s,a):r.addEventListener&&r.addEventListener(s,a,!1)}}},e.prototype.genUnBind=function(e){var t=this;return function(n,r){if("string"==typeof n&&(n=e(n)),!n)throw new Error("unbind on an undefined target");var i,o,u,a,s,c;for(s=t.binders.length-1;s>=0;s--)i=t.binders[s],i.obj===n&&(o=i.type.split("."),u=o.shift(),a=o.length>0&&o.join("."),(i.type===r||r===u||!1!==a&&a===r)&&(t.binders.splice(s,1),c=i,n.detachEvent?n.detachEvent("on"+u,i.handler):n.removeEventListener&&n.removeEventListener(u,i.handler,!1)));return c}},e.prototype.genLoadScript=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"script",n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{type:"text/javascript",language:"javascript"};return function(r,i,o){var u=e.createElement(t),a=!1;u.href=r,u.src=r;for(var s in n)u[s]=n[s],u.setAttribute(s,n[s]);for(var c in o)u[c]=o[c],u.setAttribute(c,o[c]);return u.onload=u.onreadystatechange=function(){a||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(a=!0,"function"==typeof i&&i())},e.getElementsByTagName("head")[0].appendChild(u),u}},e.prototype.getUrlParams=function(e){return i(e.substring(e.indexOf("?")+1,-1==e.indexOf("#")?e.length:e.indexOf("#")).split("&"))},e.prototype.registerPost=function(e,t,n){var r,i,o,u,a=this,s="http://i."+d.CONFIG.DOMAIN+"/login/2.5/post_callback.html",c="_submitIframe_"+Math.round(1e3*Math.random());d.CONFIG.SET_ROOT_DOMAIN&&d.CONFIG.DOMAIN;!0!==d.CONFIG.ALL_HTTPS&&"https:"!==location.protocol||(s=s.replace("http","https")),n||(n=function(){});var f="_"+Math.round(1e16*Math.random());window[f]="string"==typeof n?window[n]:n,r=document.createElement("form"),r.id="_postFrom_"+Math.round(1e3*Math.random()),r.style.display="none",r.style.position="absolute",r.method="post",r.action=e+"?domain="+d.CONFIG.DOMAIN+"&iframeUrl="+encodeURIComponent(s)+"&callback="+f+"&csrf_token="+a.getCsrfToken(),r.target=c,r.enctype="application/x-www-form-urlencoded",r.acceptCharset="UTF-8",t.domain=d.CONFIG.DOMAIN,t.response="iframe";for(o in t)i=document.createElement("input"),i.name=o,i.value=t[o],i.type="hidden",r.appendChild(i);document.body.appendChild(r);try{u=document.createElement("<iframe name='' + iframeId + ''>")}catch(p){u=document.createElement("iframe"),u.name=c}u.id=c,u.style.display="none",r.appendChild(u),document.body.appendChild(u);var l="_remove"+f;window[l]=function(){u.parentNode.removeChild(u),r.parentNode.removeChild(r),u=null,r=null,window[f]=null,window[l]=null},r.submit()},e.prototype.getCsrfToken=function(){return(0,v["default"])(this.getCookie("deviceid").slice(0,32))},e.prototype.appendUri=function(e,t){var n,r=[];for(n in t)r.push(n+"="+t[n]);return r=r.join("&"),e=-1===e.indexOf("?")?e+"?"+r:e+"&"+r},e.prototype.inArray=function(e,t){if("object"!=(void 0===t?"undefined":(0,p["default"])(t)))return!1;var n;for(n in t)if(t[n]===e)return!0;return!1},e.prototype.delVerifies=function(){var e=this;e.delCookie("VERIFY_KEY",d.CONFIG.DOMAIN),e.delCookie("verify_type",d.CONFIG.DOMAIN),e.delCookie("check_n",d.CONFIG.DOMAIN),e.delCookie("check_e",d.CONFIG.DOMAIN),e.delCookie("logindetail",d.CONFIG.DOMAIN),e.delCookie("result",d.CONFIG.DOMAIN)},e.prototype.genGetJson=function(e,t){return function(n,r,i,o){var u=t.createElement("script"),a=t.getElementsByTagName("head")[0],s="jsonp"+(new Date).getTime(),c=[];o=o||"callback";for(var f in r)c.push(f+"="+r[f]);c.push(o+"="+s),n+=(n.indexOf("?")>=0?"&":"?")+c.join("&"),e[s]=function(t){"string"==typeof i?e[i].call(e,t):i.call(e,t);try{delete e[s]}catch(n){}a.removeChild(u)},u.src=n,a.insertBefore(u,a.firstChild)}},e.prototype.isMainlandPhone=function(e){return!e||"0086"===e},e}()),E=new I;t["default"]=E},function(e,t){e.exports=function(e){if(e==undefined)throw TypeError("Can't call method on  "+e);return e}},function(e,t){var n=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:n)(e)}},function(e,t,n){var r=n(34)("keys"),i=n(22);e.exports=function(e){return r[e]||(r[e]=i(e))}},function(e,t,n){var r=n(29);e.exports=function(e){return Object(r(e))}},function(e,t,n){var r=n(6),i=n(1).document,o=r(i)&&r(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},function(e,t,n){var r=n(2),i=n(1),o=i["__core-js_shared__"]||(i["__core-js_shared__"]={});(e.exports=function(e,t){return o[e]||(o[e]=t!==undefined?t:{})})("versions",[]).push({version:r.version,mode:n(16)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){var r=n(6);e.exports=function(e,t){if(!r(e))return e;var n,i;if(t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;if("function"==typeof(n=e.valueOf)&&!r(i=n.call(e)))return i;if(!t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,n){"use strict";t.__esModule=!0,t.GBHelper=undefined;var r=n(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(r),o=n(23),u=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}(o);t.GBHelper=function(){function e(t){(0,i["default"])(this,e),this._attrName=t,this._target=undefined}return e.prototype.getTarget=function(){return this._target===undefined&&(u.hasAttr(this._attrName)?this._target=u.getAttr(this._attrName):this.setTarget({})),this._target},e.prototype.setTarget=function(e){u.setAttr(this._attrName,e),this._target=u.getAttr(this._attrName)},e.prototype.hasAttr=function(e){return this.getTarget()[e]!==undefined},e.prototype.setAttr=function(e,t){this.getTarget()[e]=t},e.prototype.getAttr=function(e){return this.getTarget()[e]},e}()},function(e,t,n){var r=n(5),i=n(87),o=n(35),u=n(31)("IE_PROTO"),a=function(){},s=function(){var e,t=n(33)("iframe"),r=o.length;for(t.style.display="none",n(59).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),s=e.F;r--;)delete s.prototype[o[r]];return s()};e.exports=Object.create||function(e,t){var n;return null!==e?(a.prototype=r(e),n=new a,a.prototype=null,n[u]=e):n=s(),t===undefined?n:i(n,t)}},function(e,t,n){e.exports={"default":n(105),__esModule:!0}},function(e,t,n){var r=n(17);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==r(e)?e.split(""):Object(e)}},function(e,t,n){"use strict";function r(e){var t,n;this.promise=new e(function(e,r){if(t!==undefined||n!==undefined)throw TypeError("Bad Promise constructor");t=e,n=r}),this.resolve=i(t),this.reject=i(n)}var i=n(21);e.exports.f=function(e){return new r(e)}},function(e,t,n){t.f=n(3)},function(e,t,n){var r=n(1),i=n(2),o=n(16),u=n(43),a=n(8).f;e.exports=function(e){var t=i.Symbol||(i.Symbol=o?{}:r.Symbol||{});"_"==e.charAt(0)||e in t||a(t,e,{value:u.f(e)})}},function(e,t,n){var r=n(30),i=Math.min;e.exports=function(e){return e>0?i(r(e),9007199254740991):0}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"",n=t;do{if(null===e||e===undefined)break;switch(void 0===e?"undefined":(0,h["default"])(e)){case"string":n=e;break;case"number":case"boolean":n=e.toString()}}while(!1);return n}function o(e){var t={};do{if(null===e||e===undefined){t={};break}switch(void 0===e?"undefined":(0,h["default"])(e)){case"object":for(var n in e)t[n]=o(e[n]);break;case"string":case"number":case"boolean":t=i(e,"")}}while(!1);return t}function u(e,t){var n=e;for(var r in t){var i=t[r];if("object"===(void 0===i?"undefined":(0,h["default"])(i))){var o=n[r];o===undefined||null===o?n[r]=i:n[r]=a(n[r],i)}else i!==undefined&&(n[r]=i)}return n}function a(e,t){return u(JSON.parse((0,p["default"])(e)),JSON.parse((0,p["default"])(t)))}function s(e){return e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()}function c(e){var t=null;try{t=JSON.parse(e)}catch(n){t=null}return t}function f(e){var t=undefined;try{t=(0,p["default"])(e)}catch(n){t=undefined}return t}t.__esModule=!0;var l=n(12),p=r(l),d=n(15),h=r(d);t.forceToString=i,t.forceJsonSimpleValueToString=o,t.combineJsonObject=a,t.dateToTimeString=s,t.parseJson=c,t.stringifyJson=f},function(e,t,n){e.exports=!n(4)&&!n(14)(function(){return 7!=Object.defineProperty(n(33)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){var r=n(10),i=n(13),o=n(75)(!1),u=n(31)("IE_PROTO");e.exports=function(e,t){var n,a=i(e),s=0,c=[];for(n in a)n!=u&&r(a,n)&&c.push(n);for(;t.length>s;)r(a,n=t[s++])&&(~o(c,n)||c.push(n));return c}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e){return{0:"PC",1:"WEB",3:"WAP",4:"MAC",10:"ANDROID",12:"LINUX"}[e]||"PC"}function o(e){var t=window.APPID!=undefined?window.APPID:"";if(v["default"].getCookie("appinfo")){t=JSON.parse(v["default"].getCookie("appinfo")).appid}return(0,d["default"])({v:"202",appid:t,appname:f(),devicemodel:y.BrowserVersion,devicename:y.BrowserType,hl:b.gbConfig.getHL(),osversion:navigator.platform,providername:A,networktype:A,sdkversion:b.gbConfig.getSDKVersion(),clientversion:b.gbConfig.getAppVersion(),devicesign:a(),platform:l(),entrance:S.getAttr(w.gbOtherInfoAttrNames.showLoginWndSource),format:N?"json":"cookie"},e)}function u(e){var t=window.APPID!=undefined?window.APPID:"";if(v["default"].getCookie("appinfo")){t=JSON.parse(v["default"].getCookie("appinfo")).appid}return""!=t&&t!=undefined||(t=v["default"].getCookie("appidstack").split(",").pop()),(0,d["default"])({appid:t,appName:f(),deviceModel:y.BrowserVersion,deviceName:y.BrowserType,hl:b.gbConfig.getHL(),OSVersion:navigator.platform,provideName:A,netWorkType:A,providerName:A,sdkVersion:b.gbConfig.getSDKVersion(),clientVersion:b.gbConfig.getAppVersion(),protocolVersion:"300",devicesign:a(),entrance:S.getAttr(w.gbOtherInfoAttrNames.showLoginWndSource),platformVersion:T.PlatformMap(),fromPlatformVersion:T.PlatformMap(),format:N?"json":"cookie",timestamp:(new Date).getTime()},e)}function a(){var e=v["default"].getCookie("deviceid");return e&&e!=undefined&&e.length>20?e:E["default"].enabled&&E["default"].has("deviceid")&&E["default"].get("deviceid").length>20?E["default"].get("deviceid").replace(/'/g,""):""}function s(e){var t=window.APPID!=undefined?window.APPID:"";if(v["default"].getCookie("appinfo")){t=JSON.parse(v["default"].getCookie("appinfo")).appid}return(0,d["default"])({appid:t,appName:f(),deviceModel:y.BrowserVersion,deviceName:y.BrowserType,hl:b.gbConfig.getHL(),OSVersion:navigator.platform,providerName:A,netWorkType:A,sdkVersion:b.gbConfig.getSDKVersion(),protocolVersion:"300",clientVersion:b.gbConfig.getAppVersion(),devicesign:a(),platformVersion:T.PlatformMap(),format:N?"json":"cookie"},e)}function c(e){var t=window.APPID!=undefined?window.APPID:"";if(v["default"].getCookie("appinfo")){t=JSON.parse(v["default"].getCookie("appinfo")).appid}return(0,d["default"])({appid:t,appName:f(),deviceModel:y.BrowserVersion,deviceName:y.BrowserType,hl:b.gbConfig.getHL(),OSVersion:navigator.platform,providerName:A,netWorkType:A,sdkVersion:b.gbConfig.getSDKVersion(),protocolVersion:"3.1",clientVersion:b.gbConfig.getAppVersion(),devicesign:a(),platformVersion:l(),format:N?"json":"cookie"},e)}function f(){var e=void 0,t=null;v["default"].getCookie("appinfo")&&(t=JSON.parse(v["default"].getCookie("appinfo")));var n=window.APPNAME||!1;try{if(e=window.parent.location.host,n&&(e=n),t&&(e=t.appname||t["package"]),!/^(IOS|ANDROID|MiniProgram|PC|LINUX)-/.test(e)){var r="";r=y.isMobile?"WAP":(0,m.checkAsPCFlow)()?"PC":l(),e=r+"-"+e}}catch(i){e="unknown"}return e}function l(){return y.isMobile?"WAP":(0,m.checkAsPCFlow)()?"PC":i(new URL(window.location.href).searchParams.get("platformVersion")||new URL(window.parent.location.href).searchParams.get("platformVersion"))}t.__esModule=!0;var p=n(52),d=r(p),h=n(0),g=r(h);t.baseParams=o,t.baseParams2=u,t.deviceid=a,t.baseParamsBeta=s,t.baseParamsRegister=c,t.getHostCategoryAppName=f,t.Platform=l;var _=n(28),v=r(_),y=n(24),m=(r(y),n(62)),I=n(77),E=r(I),b=n(60),w=n(123),O=n(38),N=(y.isXlMac,"file:"===location.protocol),S=new O.GBHelper(w.gbAttrNames.otherInfo),C=function(){function e(){(0,g["default"])(this,e),this.version=y.BrowserVersion,this.type=y.BrowserType,this.platform=l(),this.appname=f()}return e.prototype.PlatformMap=function(){var e={PC:"0",WEB:"1",WAP:"3",MAC:"4",ANDROID:"10",LINUX:"12"};return e[this.platform]?e[this.platform]:1},e}(),T=new C;t["default"]=T;var A="NONE"},function(e,t,n){"use strict";var r=n(85)(!0);n(53)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:undefined,done:!0}:(e=r(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){"use strict";function r(){var e=location.host.split("."),t=e.length;return e[t-2]+"."+e[t-1]}function i(){var e=r();return"file:"===location.protocol?[!0,e]:(document.domain!=e&&(document.domain=e),[!0,e])}t.__esModule=!0,t.getDomain=r,t.checkDomainAllowed=i},function(e,t,n){"use strict";t.__esModule=!0;var r=n(56),i=function(e){return e&&e.__esModule?e:{"default":e}}(r);t["default"]=i["default"]||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}},function(e,t,n){"use strict";var r=n(16),i=n(7),o=n(58),u=n(9),a=n(18),s=n(86),c=n(27),f=n(88),l=n(3)("iterator"),p=!([].keys&&"next"in[].keys()),d=function(){return this};e.exports=function(e,t,n,h,g,_,v){s(n,t,h);var y,m,I,E=function(e){if(!p&&e in N)return N[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},b=t+" Iterator",w="values"==g,O=!1,N=e.prototype,S=N[l]||N["@@iterator"]||g&&N[g],C=S||E(g),T=g?w?E("entries"):C:undefined,A="Array"==t?N.entries||S:S;if(A&&(I=f(A.call(new e)))!==Object.prototype&&I.next&&(c(I,b,!0),r||"function"==typeof I[l]||u(I,l,d)),w&&S&&"values"!==S.name&&(O=!0,C=function(){return S.call(this)}),r&&!v||!p&&!O&&N[l]||u(N,l,C),a[t]=C,a[b]=d,g)if(y={values:w?C:E("values"),keys:_?C:E("keys"),entries:T},v)for(m in y)m in N||o(N,m,y[m]);else i(i.P+i.F*(p||O),t,y);return y}},function(e,t,n){n(89);for(var r=n(1),i=n(9),o=n(18),u=n(3)("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),s=0;s<a.length;s++){var c=a[s],f=r[c],l=f&&f.prototype;l&&!l[u]&&i(l,u,c),o[c]=o.Array}},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(r){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){e.exports={"default":n(78),__esModule:!0}},function(e,t){},function(e,t,n){e.exports=n(9)},function(e,t,n){var r=n(1).document;e.exports=r&&r.documentElement},function(e,t,n){"use strict";t.__esModule=!0,t.gbConfig=undefined;var r=n(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(r),o=n(23),u=n(38),a={sdkVersion:"sdkVersion",appId:"appId",appName:"appName",hostCategoryAppName:"hostCategoryAppName",appVersion:"appVersion",hL:"hL",analysisServer:"analysisServer",syncPC:"syncPC",clientFeature:"clientFeature"},s=function(){function e(){(0,i["default"])(this,e),this._gbHelper=new u.GBHelper(o.gbAttrNames.config)}return e.prototype.setSDKVersion=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(a.sdkVersion,t)},e.prototype.getSDKVersion=function(){return this._gbHelper.getAttr(a.sdkVersion)},e.prototype.setAppId=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(a.appId,t)},e.prototype.getAppId=function(){return this._gbHelper.getAttr(a.appId)},e.prototype.setAppName=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(a.appName,t)},e.prototype.getAppName=function(){return this._gbHelper.getAttr(a.appName)},e.prototype.setHostCategoryAppName=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(a.hostCategoryAppName,t)},e.prototype.getHostCategoryAppName=function(){return this._gbHelper.getAttr(a.hostCategoryAppName)},e.prototype.setAppVersion=function(e){var t="string"==typeof e&&e.length>0?e:"NONE";this._gbHelper.setAttr(a.appVersion,t)},e.prototype.getAppVersion=function(){return this._gbHelper.getAttr(a.appVersion)},e.prototype.setHL=function(e){var t="string"==typeof e?e:"";this._gbHelper.setAttr(a.hL,t)},e.prototype.getHL=function(){return this._gbHelper.getAttr(a.hL)},e.prototype.setAnalysisServer=function(e){var t=e;this._gbHelper.setAttr(a.analysisServer,t)},e.prototype.getAnalysisServer=function(){return this._gbHelper.getAttr(a.analysisServer)},e.prototype.setSyncPC=function(e){var t=!0===e;this._gbHelper.setAttr(a.syncPC,t)},e.prototype.getSyncPC=function(){return this._gbHelper.getAttr(a.syncPC)},e.prototype.setClientFeature=function(e){var t=!0===e;this._gbHelper.setAttr(a.clientFeature,t)},e.prototype.getClientFeature=function(){return this._gbHelper.getAttr(a.clientFeature)},e}();t.gbConfig=new s},function(e,t,n){var r=n(17),i=n(3)("toStringTag"),o="Arguments"==r(function(){return arguments}()),u=function(e,t){try{return e[t]}catch(n){}};e.exports=function(e){var t,n,a;return e===undefined?"Undefined":null===e?"Null":"string"==typeof(n=u(t=Object(e),i))?n:o?r(t):"Object"==(a=r(t))&&"function"==typeof t.callee?"Arguments":a}},function(e,t,n){"use strict";function r(){return l===undefined&&(l=/\bedge\b/.test(f)),l}function i(){return p===undefined&&(p=/[ ]thunder\/10.0([\d.]*)/.test(f)||/[ ]thunder( )?\/( )?9.([\d.]*)/.test(f)),p}function o(){return d===undefined&&(d=/\bthunder\/10.[1-9][\d.]*/.test(f)),d}function u(){return h===undefined&&(h=/\belectron\/\d+(\.\d+){2}\b/.test(f)),h}function a(){return g===undefined&&(g=/\btbc\/\d+(\.\d+){3}\b/.test(f)&&!!window["native"]),g}function s(){return _===undefined&&(_=o()||!i()&&a()),_}function c(){return v===undefined&&(v="file:"===location.protocol),v}t.__esModule=!0,t.checkIsEdge=r,t.checkIsXl9=i,t.checkIsXlx=o,t.checkIsXdas=u,t.checkIsTbc=a,t.checkAsPCFlow=s,t.checkIsLocal=c;var f=navigator.userAgent.toLocaleLowerCase(),l=undefined,p=undefined,d=undefined,h=undefined,g=undefined,_=undefined,v=undefined},function(e,t,n){var r=n(5),i=n(21),o=n(3)("species");e.exports=function(e,t){var n,u=r(e).constructor;return u===undefined||(n=r(u)[o])==undefined?t:i(n)}},function(e,t,n){var r,i,o,u=n(19),a=n(107),s=n(59),c=n(33),f=n(1),l=f.process,p=f.setImmediate,d=f.clearImmediate,h=f.MessageChannel,g=f.Dispatch,_=0,v={},y=function(){var e=+this;if(v.hasOwnProperty(e)){var t=v[e];delete v[e],t()}},m=function(e){y.call(e.data)};p&&d||(p=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return v[++_]=function(){a("function"==typeof e?e:Function(e),t)},r(_),_},d=function(e){delete v[e]},"process"==n(17)(l)?r=function(e){l.nextTick(u(y,e,1))}:g&&g.now?r=function(e){g.now(u(y,e,1))}:h?(i=new h,o=i.port2,i.port1.onmessage=m,r=u(o.postMessage,o,1)):f.addEventListener&&"function"==typeof postMessage&&!f.importScripts?(r=function(e){f.postMessage(e+"","*")},f.addEventListener("message",m,!1)):r="onreadystatechange"in c("script")?function(e){s.appendChild(c("script")).onreadystatechange=function(){s.removeChild(this),y.call(e)}}:function(e){setTimeout(u(y,e,1),0)}),e.exports={set:p,clear:d}},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(t){return{e:!0,v:t}}}},function(e,t,n){var r=n(5),i=n(6),o=n(42);e.exports=function(e,t){if(r(e),i(t)&&t.constructor===e)return t;var n=o.f(e);return(0,n.resolve)(t),n.promise}},function(e,t,n){var r=n(48),i=n(35).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},function(e,t,n){"use strict";function r(){function e(e){var t={};return this.each(function(e,n){t[n]=e}),t}return{dump:e}}e.exports=r},function(e,t,n){"use strict";function r(){function e(e,t,n){return c.on(t,u(this,n))}function t(e,t){c.off(t)}function n(e,t,n){c.once(t,u(this,n))}function r(e,t,n){var r=this.get(t);e(),c.fire(t,n,r)}function o(e,t){var n=this.get(t);e(),c.fire(t,undefined,n)}function s(e){var t={};this.each(function(e,n){t[n]=e}),e(),a(t,function(e,t){c.fire(t,undefined,e)})}var c=i();return{watch:e,unwatch:t,once:n,set:r,remove:o,clearAll:s}}function i(){return s(f,{_id:0,_subSignals:{},_subCallbacks:{}})}var o=n(11),u=o.bind,a=o.each,s=o.create,c=o.slice;e.exports=r;var f={_id:null,_subCallbacks:null,_subSignals:null,on:function(e,t){return this._subCallbacks[e]||(this._subCallbacks[e]={}),this._id+=1,this._subCallbacks[e][this._id]=t,this._subSignals[this._id]=e,this._id},off:function(e){var t=this._subSignals[e];delete this._subCallbacks[t][e],delete this._subSignals[e]},once:function(e,t){var n=this.on(e,u(this,function(){t.apply(this,arguments),this.off(n)}))},fire:function(e){var t=c(arguments,1);a(this._subCallbacks[e],function(e){e.apply(this,t)})}}},function(e,t,n){"use strict";function r(){return n(136),{}}e.exports=r},function(e,t,n){"use strict";function r(){function e(e,t,n,r){3==arguments.length&&(r=n,n=undefined);var i=this.get(t,n),o=r(i);this.set(t,o!=undefined?o:i)}return{update:e}}e.exports=r},function(e,t,n){var r=n(2),i=r.JSON||(r.JSON={stringify:JSON.stringify});e.exports=function(e){return i.stringify.apply(i,arguments)}},function(e,t,n){"use strict";t.__esModule=!0;var r=n(51),i=(0,r.getDomain)();t["default"]={LOGIN_ID:"",APP_NAME:"",LOGIN_TYPE_COOKIE_NAME:"_x_t_",LOGIN_KEY_NAME:"loginkey",ALL_HTTPS:!1,SET_ROOT_DOMAIN:!0,AUTO_LOGIN_EXPIRE_TIME:2592e3,LOGIN_TYPES:"12",REGISTER_TYPES:"2",RETRY_LOGIN_ON_SERVER_ERROR:!0,SERVER_TIMEOUT:7e3,LOGIN_SUCCESS_URL:"",REGISTER_SUCCESS_URL:"",UI_THEME:"embed",UI_TYPE:"embed",UI_TEXT:"",UI_STYLE:!0,THIRD_LOGIN_DISPLAY:!0,DEFUALT_BACKGROUND:"",DEFUALT_UI:"login",LOGIN_BUTTON_TEXT:["登录","登录中..."],REGISTER_BUTTON_TEXT:["注册","注册..."],PROXY_URL:"http://test.kankan.com/proxy.html",DOMAIN:i,DEBUG:!0,DEFAULT_ACCOUNT:"",THIRD_LOGIN_TARGET_PARENT:!1,ALERT_ERROR:!1,USE_CDN:!1,SERVER_REGISTER:"https://zhuce."+i+"/regapi/",CDN_PATH:"",THIRD_LOGIN_GROUP:["qq","weixin","sina","alipay","xiaomi","aq360","renren","tianyi"],THIRD_LOGIN_DEFAULT:["qq","weixin","sina","alipay"],DEFAULT_AVATAR:"",REGISTER_WITH_LOGIN:!1}},,function(e,t,n){var r=n(13),i=n(45),o=n(76);e.exports=function(e){return function(t,n,u){var a,s=r(t),c=i(s.length),f=o(u,c);if(e&&n!=n){for(;c>f;)if((a=s[f++])!=a)return!0}else for(;c>f;f++)if((e||f in s)&&s[f]===n)return e||f||0;return!e&&-1}}},function(e,t,n){var r=n(30),i=Math.max,o=Math.min;e.exports=function(e,t){return e=r(e),e<0?i(e+t,0):o(e,t)}},function(e,t,n){"use strict";var r=n(124),i=n(125),o=n(132);e.exports=r.createStore(i,o)},function(e,t,n){n(79),e.exports=n(2).Object.assign},function(e,t,n){var r=n(7);r(r.S+r.F,"Object",{assign:n(80)})},function(e,t,n){"use strict";var r=n(4),i=n(25),o=n(37),u=n(26),a=n(32),s=n(41),c=Object.assign;e.exports=!c||n(14)(function(){var e={},t={},n=Symbol(),r="abcdefghijklmnopqrst";return e[n]=7,r.split("").forEach(function(e){t[e]=e}),7!=c({},e)[n]||Object.keys(c({},t)).join("")!=r})?function(e,t){for(var n=a(e),c=arguments.length,f=1,l=o.f,p=u.f;c>f;)for(var d,h=s(arguments[f++]),g=l?i(h).concat(l(h)):i(h),_=g.length,v=0;_>v;)d=g[v++],r&&!p.call(h,d)||(n[d]=h[d]);return n}:c},function(e,t,n){var r=n(19),i=n(99),o=n(100),u=n(5),a=n(45),s=n(83),c={},f={},t=e.exports=function(e,t,n,l,p){var d,h,g,_,v=p?function(){return e}:s(e),y=r(n,l,t?2:1),m=0;if("function"!=typeof v)throw TypeError(e+" is not iterable!");if(o(v)){for(d=a(e.length);d>m;m++)if((_=t?y(u(h=e[m])[0],h[1]):y(e[m]))===c||_===f)return _}else for(g=v.call(e);!(h=g.next()).done;)if((_=i(g,y,h.value,t))===c||_===f)return _};t.BREAK=c,t.RETURN=f},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){var r=n(61),i=n(3)("iterator"),o=n(18);e.exports=n(2).getIteratorMethod=function(e){if(e!=undefined)return e[i]||e["@@iterator"]||o[r(e)]}},function(e,t,n){"use strict";function r(e,t){undefined;return t>e.length?new Array(t-e.length+1).join("0")+e:e}function i(e){return e.getFullYear().toString()+r((e.getMonth()+1).toString(),2)+r(e.getDate().toString(),2)}function o(e){var t=null;try{t=JSON.parse(e)}catch(n){t=null}return t}function u(e){var t=undefined;try{t=JSON.stringify(e)}catch(n){t=undefined}return t}function a(e,t,n){return typeof t===e?t:n}Object.defineProperty(t,"__esModule",{value:!0}),t.dateToDateString=i,t.parseJson=o,t.stringifyJson=u,t.forceGetTypeValue=a},function(e,t,n){var r=n(30),i=n(29);e.exports=function(e){return function(t,n){var o,u,a=String(i(t)),s=r(n),c=a.length;return s<0||s>=c?e?"":undefined:(o=a.charCodeAt(s),o<55296||o>56319||s+1===c||(u=a.charCodeAt(s+1))<56320||u>57343?e?a.charAt(s):o:e?a.slice(s,s+2):u-56320+(o-55296<<10)+65536)}}},function(e,t,n){"use strict";var r=n(39),i=n(20),o=n(27),u={};n(9)(u,n(3)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=r(u,{next:i(1,n)}),o(e,t+" Iterator")}},function(e,t,n){var r=n(8),i=n(5),o=n(25);e.exports=n(4)?Object.defineProperties:function(e,t){i(e);for(var n,u=o(t),a=u.length,s=0;a>s;)r.f(e,n=u[s++],t[n]);return e}},function(e,t,n){var r=n(10),i=n(32),o=n(31)("IE_PROTO"),u=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=i(e),r(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?u:null}},function(e,t,n){"use strict";var r=n(90),i=n(82),o=n(18),u=n(13);e.exports=n(53)(Array,"Array",function(e,t){this._t=u(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=undefined,i(1)):"keys"==t?i(0,n):"values"==t?i(0,e[n]):i(0,[n,e[n]])},"values"),o.Arguments=o.Array,r("keys"),r("values"),r("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t,n,r){if(!(e instanceof t)||r!==undefined&&r in e)throw TypeError(n+": incorrect invocation!");return e}},function(e,t,n){var r=n(9);e.exports=function(e,t,n){for(var i in t)n&&e[i]?e[i]=t[i]:r(e,i,t[i]);return e}},function(e,t,n){var r=n(22)("meta"),i=n(6),o=n(10),u=n(8).f,a=0,s=Object.isExtensible||function(){return!0},c=!n(14)(function(){return s(Object.preventExtensions({}))}),f=function(e){u(e,r,{value:{i:"O"+ ++a,w:{}}})},l=function(e,t){if(!i(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!o(e,r)){if(!s(e))return"F";if(!t)return"E";f(e)}return e[r].i},p=function(e,t){if(!o(e,r)){if(!s(e))return!0;if(!t)return!1;f(e)}return e[r].w},d=function(e){return c&&h.NEED&&s(e)&&!o(e,r)&&f(e),e},h=e.exports={KEY:r,NEED:!1,fastKey:l,getWeak:p,onFreeze:d}},function(e,t,n){var r=n(26),i=n(20),o=n(13),u=n(36),a=n(10),s=n(47),c=Object.getOwnPropertyDescriptor;t.f=n(4)?c:function(e,t){if(e=o(e),t=u(t,!0),s)try{return c(e,t)}catch(n){}if(a(e,t))return i(!r.f.call(e,t),e[t])}},function(e,t,n){e.exports={"default":n(121),__esModule:!0}},function(e,t,n){"use strict";t.__esModule=!0;var r=function(){function e(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var a=1732584193,s=-271733879,c=-1732584194,f=271733878,l=0;e.length>l;l+=16){var p=a,d=s,h=c,g=f;a=n(a,s,c,f,e[l+0],7,-680876936),f=n(f,a,s,c,e[l+1],12,-389564586),c=n(c,f,a,s,e[l+2],17,606105819),s=n(s,c,f,a,e[l+3],22,-1044525330),a=n(a,s,c,f,e[l+4],7,-176418897),f=n(f,a,s,c,e[l+5],12,1200080426),c=n(c,f,a,s,e[l+6],17,-1473231341),s=n(s,c,f,a,e[l+7],22,-45705983),a=n(a,s,c,f,e[l+8],7,1770035416),f=n(f,a,s,c,e[l+9],12,-1958414417),c=n(c,f,a,s,e[l+10],17,-42063),s=n(s,c,f,a,e[l+11],22,-1990404162),a=n(a,s,c,f,e[l+12],7,1804603682),f=n(f,a,s,c,e[l+13],12,-40341101),c=n(c,f,a,s,e[l+14],17,-1502002290),s=n(s,c,f,a,e[l+15],22,1236535329),a=r(a,s,c,f,e[l+1],5,-165796510),f=r(f,a,s,c,e[l+6],9,-1069501632),c=r(c,f,a,s,e[l+11],14,643717713),s=r(s,c,f,a,e[l+0],20,-373897302),a=r(a,s,c,f,e[l+5],5,-701558691),f=r(f,a,s,c,e[l+10],9,38016083),c=r(c,f,a,s,e[l+15],14,-660478335),s=r(s,c,f,a,e[l+4],20,-405537848),a=r(a,s,c,f,e[l+9],5,568446438),f=r(f,a,s,c,e[l+14],9,-1019803690),c=r(c,f,a,s,e[l+3],14,-187363961),s=r(s,c,f,a,e[l+8],20,1163531501),a=r(a,s,c,f,e[l+13],5,-1444681467),f=r(f,a,s,c,e[l+2],9,-51403784),c=r(c,f,a,s,e[l+7],14,1735328473),s=r(s,c,f,a,e[l+12],20,-1926607734),a=i(a,s,c,f,e[l+5],4,-378558),f=i(f,a,s,c,e[l+8],11,-2022574463),c=i(c,f,a,s,e[l+11],16,1839030562),s=i(s,c,f,a,e[l+14],23,-35309556),a=i(a,s,c,f,e[l+1],4,-1530992060),f=i(f,a,s,c,e[l+4],11,1272893353),c=i(c,f,a,s,e[l+7],16,-155497632),s=i(s,c,f,a,e[l+10],23,-1094730640),a=i(a,s,c,f,e[l+13],4,681279174),f=i(f,a,s,c,e[l+0],11,-358537222),c=i(c,f,a,s,e[l+3],16,-722521979),s=i(s,c,f,a,e[l+6],23,76029189),a=i(a,s,c,f,e[l+9],4,-640364487),f=i(f,a,s,c,e[l+12],11,-421815835),c=i(c,f,a,s,e[l+15],16,530742520),s=i(s,c,f,a,e[l+2],23,-995338651),a=o(a,s,c,f,e[l+0],6,-198630844),f=o(f,a,s,c,e[l+7],10,1126891415),c=o(c,f,a,s,e[l+14],15,-1416354905),s=o(s,c,f,a,e[l+5],21,-57434055),a=o(a,s,c,f,e[l+12],6,1700485571),f=o(f,a,s,c,e[l+3],10,-1894986606),c=o(c,f,a,s,e[l+10],15,-1051523),s=o(s,c,f,a,e[l+1],21,-2054922799),a=o(a,s,c,f,e[l+8],6,1873313359),f=o(f,a,s,c,e[l+15],10,-30611744),c=o(c,f,a,s,e[l+6],15,-1560198380),s=o(s,c,f,a,e[l+13],21,1309151649),a=o(a,s,c,f,e[l+4],6,-145523070),f=o(f,a,s,c,e[l+11],10,-1120210379),c=o(c,f,a,s,e[l+2],15,718787259),s=o(s,c,f,a,e[l+9],21,-343485551),a=u(a,p),s=u(s,d),c=u(c,h),f=u(f,g)}return[a,s,c,f]}function t(e,t,n,r,i,o){return u(a(u(u(t,e),u(r,o)),i),n)}function n(e,n,r,i,o,u,a){return t(n&r|~n&i,e,n,o,u,a)}function r(e,n,r,i,o,u,a){return t(n&i|r&~i,e,n,o,u,a)}function i(e,n,r,i,o,u,a){return t(n^r^i,e,n,o,u,a)}function o(e,n,r,i,o,u,a){return t(r^(n|~i),e,n,o,u,a)}function u(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function a(e,t){return e<<t|e>>>32-t}function s(e){for(var t=[],n=(1<<l)-1,r=0;e.length*l>r;r+=l)t[r>>5]|=(e.charCodeAt(r/l)&n)<<r%32;return t}function c(e){for(var t=f?"0123456789ABCDEF":"0123456789abcdef",n="",r=0;4*e.length>r;r++)n+=t.charAt(e[r>>2]>>r%4*8+4&15)+t.charAt(e[r>>2]>>r%4*8&15);return n}var f=0,l=8;return function(t){return c(e(s(t),t.length*l))}}();window.md5=r,t["default"]=r},function(e,t,n){"use strict";t.__esModule=!0,t.CONFIG=undefined;var r=n(51),i=(0,r.getDomain)();t.CONFIG={LOGIN_ID:"",APP_NAME:"",APP_VERSION:"NONE",SET_ROOT_DOMAIN:!0,LOGIN_TYPE_COOKIE_NAME:"_x_t_",AUTO_LOGIN_COOKIE_NAME:"_x_a_",AUTO_LOGIN_EXPIRE_TIME:2592e3,LOGIN_TYPES:["account","mobile"],REGISTER_TYPES:["mobile"],UI_THEME:"embed",UI_TYPE:"embed",UI_TEXT:"",UI_STYLE:"",THIRD_LOGIN_DISPLAY:!0,RETRY_LOGIN_ON_SERVER_ERROR:!0,LOGIN_SUCCESS_FUNC:function(){location.reload()},REGISTER_SUCCESS_FUNC:function(){location.reload()},LOGIN_SUCCESS_URL:location.href,REGISTER_SUCCESS_URL:location.href,ON_UI_CHANGE:function(e){},POPUP_MASK:!0,POPUP_ALLOW_CLOSE:!0,POPUP_CLOSE_FUNC:function(){},POPUP_PRELOAD:!0,DEFUALT_BACKGROUND:"//i."+i+"/login/theme/popup/images/layer_bg.png",DEFUALT_UI:"login",IFRAME_ALLOW_TRANSPARENCY:!1,IFRAME_STYLE:"",IFRAME_ID:"loginIframe",LOGOUT_FUNC:function(){location.reload()},BIND_SUCCESS_FUNC:function(){location.reload()},LOGIN_BUTTON_TEXT:"",REGISTER_BUTTON_TEXT:"",REGISTER_STAT_DATA:"",DOMAIN:i,ALLOW_ACCOUNT_REGISTER_IDS:["vip_niu","niux_web","game"],VERSION:"2.5",DEBUG:!1,DEFAULT_ACCOUNT:"",THIRD_LOGIN_TARGET_PARENT:!1,ALERT_ERROR:!1,CHANGE_SIZE_FUNC:function(e){},LOGIN_EXT_FUNS:[],LOGOUT_EXT_FUNS:[],CLIENT_LOGIN_FUNS:[],CLIENTLOGIN:!1,INITED_FUNS:[],LOGIN_STATE_INITED_FUNS:[],USE_CDN:!1,SERVER_REGISTER:"https://zhuce."+i+"/regapi/",UI_COMPLETED_EXT_FUNS:[],THIRD_LOGIN_GROUP:["qq","weixin","sina"],THIRD_LOGIN_DEFAULT:["qq","weixin","sina"],DEFAULT_AVATAR:"",SERVER_LOGIN:["xluser-ssl."+i,"xluser2-ssl."+i,"xluser3-ssl."+i],SERVER_XLUSER:[],MESSAGE_CHANNEL_GSLB_QURERY_HOST_KEY:"agw-acc-ssl."+i,MESSAGE_CHANNEL_SERVER:"agw-acc-web-ssl."+i,ANALYSIS_SERVER:"",IS_HIT_BLOCK:!0,LOGIN_FAIL_FUNS:[],CAN_GSLB:!0,STATIC_DOMAIN:"i."+i,ISFRAUD:!0,CAN_QR_LOGIN:!1,ONLOAD:function(){},REGISTER_CHECKED:!1,TOAST:function(e){},isDOMAIN:!0,IS_SYNC_APP:!0,IS_SYNC_PC:!0,IS_SYNC_MAC:!1,CLIENT_FEATURE:!1,HL:"",SHOW_GSM:!1,REGISTER_WITH_LOGIN:!1}},,function(e,t,n){var r=n(5);e.exports=function(e,t,n,i){try{return i?t(r(n)[0],n[1]):t(n)}catch(u){var o=e["return"];throw o!==undefined&&r(o.call(e)),u}}},function(e,t,n){var r=n(18),i=n(3)("iterator"),o=Array.prototype;e.exports=function(e){return e!==undefined&&(r.Array===e||o[i]===e)}},function(e,t,n){"use strict";var r=n(1),i=n(2),o=n(8),u=n(4),a=n(3)("species");e.exports=function(e){var t="function"==typeof i[e]?i[e]:r[e];u&&t&&!t[a]&&o.f(t,a,{configurable:!0,get:function(){return this}})}},function(e,t,n){var r=n(3)("iterator"),i=!1;try{var o=[7][r]();o["return"]=function(){i=!0},Array.from(o,function(){throw 2})}catch(u){}e.exports=function(e,t){if(!t&&!i)return!1;var n=!1;try{var o=[7],u=o[r]();u.next=function(){return{done:n=!0}},o[r]=function(){return u},e(o)}catch(u){}return n}},function(e,t,n){var r=n(17);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.HttpRequest=undefined;var i=n(40),o=r(i),u=n(0),a=r(u),s=t.HttpRequest=function(){function e(){(0,a["default"])(this,e)}return e.prototype.get=function(e,t){arguments.length>2&&arguments[2]!==undefined&&arguments[2];return this._request("GET",e,t,null)},e.prototype.post=function(e,t,n){return this._request("POST",e,t,n)},e.prototype._request=function(e,t,n){var r=arguments.length>3&&arguments[3]!==undefined?arguments[3]:null;return new o["default"](function(i,o){var u=void 0;if(u=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),u.onreadystatechange=function(){if(4==u.readyState){var e={data:u.responseText,status:u.status};i(e)}},"GET"===e)u.open(e,t,!0),u.send(null);else{if(u.open(e,t,!0),n)for(var a in n)u.setRequestHeader(a,n[a]);u.send(r)}})},e}(),c=new s;t["default"]=c},function(e,t,n){n(57),n(50),n(54),n(106),n(110),n(111),e.exports=n(2).Promise},function(e,t,n){"use strict";var r,i,o,u,a=n(16),s=n(1),c=n(19),f=n(61),l=n(7),p=n(6),d=n(21),h=n(91),g=n(81),_=n(63),v=n(64).set,y=n(108)(),m=n(42),I=n(65),E=n(109),b=n(66),w=s.TypeError,O=s.process,N=O&&O.versions,S=N&&N.v8||"",C=s.Promise,T="process"==f(O),A=function(){},L=i=m.f,R=!!function(){try{var e=C.resolve(1),t=(e.constructor={})[n(3)("species")]=function(e){e(A,A)};return(T||"function"==typeof PromiseRejectionEvent)&&e.then(A)instanceof t&&0!==S.indexOf("6.6")&&-1===E.indexOf("Chrome/66")}catch(r){}}(),x=function(e){var t;return!(!p(e)||"function"!=typeof(t=e.then))&&t},F=function(e,t){if(!e._n){e._n=!0;var n=e._c;y(function(){for(var r=e._v,i=1==e._s,o=0;n.length>o;)!function(t){var n,o,u,a=i?t.ok:t.fail,s=t.resolve,c=t.reject,f=t.domain;try{a?(i||(2==e._h&&G(e),e._h=1),!0===a?n=r:(f&&f.enter(),n=a(r),f&&(f.exit(),u=!0)),n===t.promise?c(w("Promise-chain cycle")):(o=x(n))?o.call(n,s,c):s(n)):c(r)}catch(l){f&&!u&&f.exit(),c(l)}}(n[o++]);e._c=[],e._n=!1,t&&!e._h&&P(e)})}},P=function(e){v.call(s,function(){var t,n,r,i=e._v,o=k(e);if(o&&(t=I(function(){T?O.emit("unhandledRejection",i,e):(n=s.onunhandledrejection)?n({promise:e,reason:i}):(r=s.console)&&r.error&&r.error("Unhandled promise rejection",i)}),e._h=T||k(e)?2:1),e._a=undefined,o&&t.e)throw t.v})},k=function(e){return 1!==e._h&&0===(e._a||e._c).length},G=function(e){v.call(s,function(){var t;T?O.emit("rejectionHandled",e):(t=s.onrejectionhandled)&&t({promise:e,reason:e._v})})},M=function(e){var t=this;t._d||(t._d=!0,t=t._w||t,t._v=e,t._s=2,t._a||(t._a=t._c.slice()),F(t,!0))},U=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw w("Promise can't be resolved itself");(t=x(e))?y(function(){var r={_w:n,_d:!1};try{t.call(e,c(U,r,1),c(M,r,1))}catch(i){M.call(r,i)}}):(n._v=e,n._s=1,F(n,!1))}catch(r){M.call({_w:n,_d:!1},r)}}};R||(C=function(e){h(this,C,"Promise","_h"),d(e),r.call(this);try{e(c(U,this,1),c(M,this,1))}catch(t){M.call(this,t)}},r=function(e){this._c=[],this._a=undefined,this._s=0,this._d=!1,this._v=undefined,this._h=0,this._n=!1},r.prototype=n(92)(C.prototype,{then:function(e,t){var n=L(_(this,C));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=T?O.domain:undefined,this._c.push(n),this._a&&this._a.push(n),this._s&&F(this,!1),n.promise},"catch":function(e){return this.then(undefined,e)}}),o=function(){var e=new r;this.promise=e,this.resolve=c(U,e,1),this.reject=c(M,e,1)},m.f=L=function(e){return e===C||e===u?new o(e):i(e)}),l(l.G+l.W+l.F*!R,{Promise:C}),n(27)(C,"Promise"),n(101)("Promise"),u=n(2).Promise,l(l.S+l.F*!R,"Promise",{reject:function(e){var t=L(this);return(0,t.reject)(e),t.promise}}),l(l.S+l.F*(a||!R),"Promise",{resolve:function(e){return b(a&&this===u?C:this,e)}}),l(l.S+l.F*!(R&&n(102)(function(e){C.all(e)["catch"](A)})),"Promise",{all:function(e){var t=this,n=L(t),r=n.resolve,i=n.reject,o=I(function(){var n=[],o=0,u=1;g(e,!1,function(e){var a=o++,s=!1;n.push(undefined),u++,t.resolve(e).then(function(e){s||(s=!0,n[a]=e,--u||r(n))},i)}),--u||r(n)});return o.e&&i(o.v),n.promise},race:function(e){var t=this,n=L(t),r=n.reject,i=I(function(){g(e,!1,function(e){t.resolve(e).then(n.resolve,r)})});return i.e&&r(i.v),n.promise}})},function(e,t){e.exports=function(e,t,n){var r=n===undefined;switch(t.length){case 0:return r?e():e.call(n);case 1:return r?e(t[0]):e.call(n,t[0]);case 2:return r?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return r?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return r?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){var r=n(1),i=n(64).set,o=r.MutationObserver||r.WebKitMutationObserver,u=r.process,a=r.Promise,s="process"==n(17)(u);e.exports=function(){var e,t,n,c=function(){var r,i;for(s&&(r=u.domain)&&r.exit();e;){i=e.fn,e=e.next;try{i()}catch(o){throw e?n():t=undefined,o}}t=undefined,r&&r.enter()};if(s)n=function(){u.nextTick(c)};else if(!o||r.navigator&&r.navigator.standalone)if(a&&a.resolve){var f=a.resolve(undefined);n=function(){f.then(c)}}else n=function(){i.call(r,c)};else{var l=!0,p=document.createTextNode("");new o(c).observe(p,{characterData:!0}),n=function(){p.data=l=!l}}return function(r){var i={fn:r,next:undefined};t&&(t.next=i),e||(e=i,n()),t=i}}},function(e,t,n){var r=n(1),i=r.navigator;e.exports=i&&i.userAgent||""},function(e,t,n){"use strict";var r=n(7),i=n(2),o=n(1),u=n(63),a=n(66);r(r.P+r.R,"Promise",{"finally":function(e){var t=u(this,i.Promise||o.Promise),n="function"==typeof e;return this.then(n?function(n){return a(t,e()).then(function(){return n})}:e,n?function(n){return a(t,e()).then(function(){throw n})}:e)}})},function(e,t,n){"use strict";var r=n(7),i=n(42),o=n(65);r(r.S,"Promise",{"try":function(e){var t=i.f(this),n=o(e);return(n.e?t.reject:t.resolve)(n.v),t.promise}})},function(e,t,n){e.exports={"default":n(113),__esModule:!0}},function(e,t,n){n(50),n(54),e.exports=n(43).f("iterator")},function(e,t,n){e.exports={"default":n(115),__esModule:!0}},function(e,t,n){n(116),n(57),n(119),n(120),e.exports=n(2).Symbol},function(e,t,n){"use strict";var r=n(1),i=n(10),o=n(4),u=n(7),a=n(58),s=n(93).KEY,c=n(14),f=n(34),l=n(27),p=n(22),d=n(3),h=n(43),g=n(44),_=n(117),v=n(103),y=n(5),m=n(6),I=n(32),E=n(13),b=n(36),w=n(20),O=n(39),N=n(118),S=n(94),C=n(37),T=n(8),A=n(25),L=S.f,R=T.f,x=N.f,F=r.Symbol,P=r.JSON,k=P&&P.stringify,G=d("_hidden"),M=d("toPrimitive"),U={}.propertyIsEnumerable,D=f("symbol-registry"),j=f("symbols"),B=f("op-symbols"),V=Object.prototype,Y="function"==typeof F&&!!C.f,H=r.QObject,X=!H||!H.prototype||!H.prototype.findChild,W=o&&c(function(){return 7!=O(R({},"a",{get:function(){return R(this,"a",{value:7}).a}})).a})?function(e,t,n){var r=L(V,t);r&&delete V[t],R(e,t,n),r&&e!==V&&R(V,t,r)}:R,q=function(e){var t=j[e]=O(F.prototype);return t._k=e,t},J=Y&&"symbol"==typeof F.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof F},K=function(e,t,n){return e===V&&K(B,t,n),y(e),t=b(t,!0),y(n),i(j,t)?(n.enumerable?(i(e,G)&&e[G][t]&&(e[G][t]=!1),n=O(n,{enumerable:w(0,!1)})):(i(e,G)||R(e,G,w(1,{})),e[G][t]=!0),W(e,t,n)):R(e,t,n)},Q=function(e,t){y(e);for(var n,r=_(t=E(t)),i=0,o=r.length;o>i;)K(e,n=r[i++],t[n]);return e},z=function(e,t){return t===undefined?O(e):Q(O(e),t)},$=function(e){var t=U.call(this,e=b(e,!0));return!(this===V&&i(j,e)&&!i(B,e))&&(!(t||!i(this,e)||!i(j,e)||i(this,G)&&this[G][e])||t)},Z=function(e,t){if(e=E(e),t=b(t,!0),e!==V||!i(j,t)||i(B,t)){var n=L(e,t);return!n||!i(j,t)||i(e,G)&&e[G][t]||(n.enumerable=!0),n}},ee=function(e){for(var t,n=x(E(e)),r=[],o=0;n.length>o;)i(j,t=n[o++])||t==G||t==s||r.push(t);return r},te=function(e){for(var t,n=e===V,r=x(n?B:E(e)),o=[],u=0;r.length>u;)!i(j,t=r[u++])||n&&!i(V,t)||o.push(j[t]);return o};Y||(F=function(){if(this instanceof F)throw TypeError("Symbol is not a constructor!");var e=p(arguments.length>0?arguments[0]:undefined),t=function(n){this===V&&t.call(B,n),i(this,G)&&i(this[G],e)&&(this[G][e]=!1),W(this,e,w(1,n))};return o&&X&&W(V,e,{configurable:!0,set:t}),q(e)},a(F.prototype,"toString",function(){return this._k}),S.f=Z,T.f=K,n(67).f=N.f=ee,n(26).f=$,C.f=te,o&&!n(16)&&a(V,"propertyIsEnumerable",$,!0),h.f=function(e){return q(d(e))}),u(u.G+u.W+u.F*!Y,{Symbol:F});for(var ne="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),re=0;ne.length>re;)d(ne[re++]);for(var ie=A(d.store),oe=0;ie.length>oe;)g(ie[oe++]);u(u.S+u.F*!Y,"Symbol",{"for":function(e){return i(D,e+="")?D[e]:D[e]=F(e)},keyFor:function(e){if(!J(e))throw TypeError(e+" is not a symbol!");for(var t in D)if(D[t]===e)return t},useSetter:function(){X=!0},useSimple:function(){X=!1}}),u(u.S+u.F*!Y,"Object",{create:z,defineProperty:K,defineProperties:Q,getOwnPropertyDescriptor:Z,getOwnPropertyNames:ee,getOwnPropertySymbols:te});var ue=c(function(){C.f(1)});u(u.S+u.F*ue,"Object",{getOwnPropertySymbols:function(e){return C.f(I(e))}}),P&&u(u.S+u.F*(!Y||c(function(){var e=F();return"[null]"!=k([e])||"{}"!=k({a:e})||"{}"!=k(Object(e))})),"JSON",{stringify:function(e){for(var t,n,r=[e],i=1;arguments.length>i;)r.push(arguments[i++]);if(n=t=r[1],(m(t)||e!==undefined)&&!J(e))return v(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!J(t))return t}),r[1]=t,k.apply(P,r)}}),F.prototype[M]||n(9)(F.prototype,M,F.prototype.valueOf),l(F,"Symbol"),l(Math,"Math",!0),l(r.JSON,"JSON",!0)},function(e,t,n){var r=n(25),i=n(37),o=n(26);e.exports=function(e){var t=r(e),n=i.f;if(n)for(var u,a=n(e),s=o.f,c=0;a.length>c;)s.call(e,u=a[c++])&&t.push(u);return t}},function(e,t,n){var r=n(13),i=n(67).f,o={}.toString,u="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(e){try{return i(e)}catch(t){return u.slice()}};e.exports.f=function(e){return u&&"[object Window]"==o.call(e)?a(e):i(r(e))}},function(e,t,n){n(44)("asyncIterator")},function(e,t,n){n(44)("observable")},function(e,t,n){n(122);var r=n(2).Object;e.exports=function(e,t){return r.create(e,t)}},function(e,t,n){var r=n(7);r(r.S,"Object",{create:n(39)})},function(e,t,n){"use strict";t.__esModule=!0;t.gbAttrNames={otherInfo:"otherInfo",platformInfo:"platformInfo"},t.gbOtherInfoAttrNames={showLoginWndSource:"showLoginWndSource"},t.gbPlatformInfoAttrNames={deviceSign:"deviceSign"}},function(e,t,n){"use strict";function r(e,t){var n={_seenPlugins:[],_namespacePrefix:"",_namespaceRegexp:null,_legalNamespace:/^[a-zA-Z0-9_\-]+$/,_storage:function(){if(!this.enabled)throw new Error("store.js: No supported storage has been added! Add one (e.g store.addStorage(require('store/storages/cookieStorage')) or use a build with more built-in storages (e.g https://github.com/marcuswestin/store.js/tree/master/dist/store.legacy.min.js)");return this._storage.resolved},_testStorage:function(e){try{var t="__storejs__test__";e.write(t,t);var n=e.read(t)===t;return e.remove(t),n}catch(r){return!1}},_assignPluginFnProp:function(e,t){var n=this[t];this[t]=function(){function t(){if(n)return c(arguments,function(e,t){r[t]=e}),n.apply(i,r)}var r=a(arguments,0),i=this,o=[t].concat(r);return e.apply(i,o)}},_serialize:function(e){return(0,o["default"])(e)},_deserialize:function(e,t){if(!e)return t;var n="";try{n=JSON.parse(e)}catch(r){n=e}return n!==undefined?n:t}},r=f(n,h);return c(e,function(e){r.addStorage(e)}),c(t,function(e){r.addPlugin(e)}),r}var i=n(12),o=function(e){return e&&e.__esModule?e:{"default":e}}(i),u=n(11),a=u.slice,s=u.pluck,c=u.each,f=u.create,l=u.isList,p=u.isFunction,d=u.isObject;e.exports={createStore:r};var h={version:"2.0.3",enabled:!1,addStorage:function(e){this.enabled||this._testStorage(e)&&(this._storage.resolved=e,this.enabled=!0)},addPlugin:function(e){var t=this;if(l(e))return void c(e,function(e){t.addPlugin(e)});if(!s(this._seenPlugins,function(t){return e===t})){if(this._seenPlugins.push(e),!p(e))throw new Error("Plugins must be function values that return objects");var n=e.call(this);if(!d(n))throw new Error("Plugins must return an object of function properties");c(n,function(n,r){if(!p(n))throw new Error("Bad plugin property: "+r+" from plugin "+e.name+". Plugins should only return functions.");t._assignPluginFnProp(n,r)})}},get:function(e,t){var n=this._storage().read(this._namespacePrefix+e);return this._deserialize(n,t)},set:function(e,t){return t===undefined?this.remove(e):(this._storage().write(this._namespacePrefix+e,this._serialize(t)),t)},remove:function(e){this._storage().remove(this._namespacePrefix+e)},each:function(e){var t=this;this._storage().each(function(n,r){e(t._deserialize(n),r.replace(t._namespaceRegexp,""))})},clearAll:function(){this._storage().clearAll()},hasNamespace:function(e){return this._namespacePrefix=="__storejs_"+e+"_"},namespace:function(e){if(!this._legalNamespace.test(e))throw new Error("store.js namespaces can only have alhpanumerics + underscores and dashes");var t="__storejs_"+e+"_";return f(this,{_namespacePrefix:t,_namespaceRegexp:t?new RegExp("^"+t):null})},createStore:function(e,t){return r(e,t)}}},function(e,t,n){"use strict";e.exports={localStorage:n(126),"oldFF-globalStorage":n(127),"oldIE-userDataStorage":n(128),cookieStorage:n(129),sessionStorage:n(130),memoryStorage:n(131)}},function(e,t,n){"use strict";function r(){return f.localStorage}function i(e){return r().getItem(e)}function o(e,t){return r().setItem(e,t)}function u(e){for(var t=r().length-1;t>=0;t--){var n=r().key(t);e(i(n),n)}}function a(e){return r().removeItem(e)}function s(){return r().clear()}var c=n(11),f=c.Global;e.exports={name:"localStorage",read:i,write:o,each:u,remove:a,clearAll:s}},function(e,t,n){"use strict";function r(e){return f[e]}function i(e,t){f[e]=t}function o(e){for(var t=f.length-1;t>=0;t--){var n=f.key(t);e(f[n],n)}}function u(e){return f.removeItem(e)}function a(){o(function(e,t){delete f[e]})}var s=n(11),c=s.Global;e.exports={name:"oldFF-globalStorage",read:r,write:i,each:o,remove:u,clearAll:a};var f=c.globalStorage},function(e,t,n){"use strict";function r(e,t){if(!h){var n=s(e);d(function(e){e.setAttribute(n,t),e.save(l)})}}function i(e){if(!h){var t=s(e),n=null;return d(function(e){n=e.getAttribute(t)}),n}}function o(e){d(function(t){for(var n=t.XMLDocument.documentElement.attributes,r=n.length-1;r>=0;r--){var i=n[r];e(t.getAttribute(i.name),i.name)}})}function u(e){var t=s(e);d(function(e){e.removeAttribute(t),e.save(l)})}function a(){d(function(e){var t=e.XMLDocument.documentElement.attributes;e.load(l);for(var n=t.length-1;n>=0;n--)e.removeAttribute(t[n].name);e.save(l)})}function s(e){return e.replace(/^\d/,"___$&").replace(g,"___")}var c=n(11),f=c.Global;e.exports={name:"oldIE-userDataStorage",write:r,read:i,each:o,remove:u,clearAll:a};var l="storejs",p=f.document,d=function(){if(!p||!p.documentElement||!p.documentElement.addBehavior)return null;var e,t,n;try{t=new ActiveXObject("htmlfile"),t.open(),t.write('<script>document.w=window<\/script><iframe src="/favicon.ico"></iframe>'),t.close(),e=t.w.frames[0].document,n=e.createElement("div")}catch(r){n=p.createElement("div"),e=p.body}return function(t){var r=[].slice.call(arguments,0);r.unshift(n),e.appendChild(n),n.addBehavior("#default#userData"),n.load(l),t.apply(this,r),e.removeChild(n)}}(),h=(f.navigator?f.navigator.userAgent:"").match(/ (MSIE 8|MSIE 9|MSIE 10)\./),g=new RegExp("[!\"#$%&'()*+,/\\\\:;<=>?@[\\]^`{|}~]","g")},function(e,t,n){"use strict";function r(e){if(!e||!s(e))return null;var t="(?:^|.*;\\s*)"+escape(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*((?:[^;](?!;))*[^;]?).*";return unescape(p.cookie.replace(new RegExp(t),"$1"))}function i(e){for(var t=p.cookie.split(/; ?/g),n=t.length-1;n>=0;n--)if(l(t[n])){var r=t[n].split("="),i=unescape(r[0]),o=unescape(r[1]);e(o,i)}}function o(e,t){e&&(p.cookie=escape(e)+"="+escape(t)+"; expires=Tue, 19 Jan 2038 03:14:07 GMT; path=/")}function u(e){e&&s(e)&&(p.cookie=escape(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/")}function a(){i(function(e,t){u(t)})}function s(e){return new RegExp("(?:^|;\\s*)"+escape(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(p.cookie)}var c=n(11),f=c.Global,l=c.trim;e.exports={name:"cookieStorage",read:r,write:o,each:i,remove:u,clearAll:a};var p=f.document},function(e,t,n){"use strict";function r(){return f.sessionStorage}function i(e){return r().getItem(e)}function o(e,t){return r().setItem(e,t)}function u(e){for(var t=r().length-1;t>=0;t--){var n=r().key(t);e(i(n),n)}}function a(e){return r().removeItem(e)}function s(){return r().clear()}var c=n(11),f=c.Global;e.exports={name:"sessionStorage",read:i,write:o,each:u,remove:a,clearAll:s}},function(e,t,n){"use strict";function r(e){return s[e]}function i(e,t){s[e]=t}function o(e){for(var t in s)s.hasOwnProperty(t)&&e(s[t],t)}function u(e){delete s[e]}function a(e){s={}}e.exports={name:"memoryStorage",read:r,write:i,each:o,remove:u,clearAll:a};var s={}},function(e,t,n){"use strict";e.exports={defaults:n(133),dump:n(68),events:n(69),observe:n(134),expire:n(135),json2:n(70),operations:n(137),update:n(71),"v1-backcompat":n(138)}},function(e,t,n){"use strict";function r(){function e(e,t){n=t}function t(e,t){var r=e();return r!==undefined?r:n[t]}var n={};return{defaults:e,get:t}}e.exports=r},function(e,t,n){"use strict";function r(){function e(e,t,n){var r=this.watch(t,n);return n(this.get(t)),r}function t(e,t){this.unwatch(t)}return{observe:e,unobserve:t}}var i=n(69);e.exports=[i,r]},function(e,t,n){"use strict";function r(){function e(e,t,n,o){return this.hasNamespace(i)||r.set(t,o),e()}function t(e,t){if(!this.hasNamespace(i)){r.get(t,Number.MAX_VALUE)<=(new Date).getTime()&&this.remove(t)}return e()}function n(e,t){return this.hasNamespace(i)||r.remove(t),e()}var r=this.namespace(i);return{set:e,get:t,remove:n}}var i="expire_mixin";e.exports=r},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{"default":e}}var _stringify=__webpack_require__(12),_stringify2=_interopRequireDefault(_stringify),_typeof2=__webpack_require__(15),_typeof3=_interopRequireDefault(_typeof2);"object"!==("undefined"==typeof JSON?"undefined":(0,_typeof3["default"])(JSON))&&(JSON={}),function(){function f(e){return e<10?"0"+e:e}function this_value(){return this.valueOf()}function quote(e){return rx_escapable.lastIndex=0,rx_escapable.test(e)?'"'+e.replace(rx_escapable,function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var n,r,i,o,u,a=gap,s=t[e];switch(s&&"object"===(void 0===s?"undefined":(0,_typeof3["default"])(s))&&"function"==typeof s.toJSON&&(s=s.toJSON(e)),"function"==typeof rep&&(s=rep.call(t,e,s)),void 0===s?"undefined":(0,_typeof3["default"])(s)){case"string":return quote(s);case"number":return isFinite(s)?String(s):"null";case"boolean":case"null":return String(s);case"object":if(!s)return"null";if(gap+=indent,u=[],"[object Array]"===Object.prototype.toString.apply(s)){for(o=s.length,n=0;n<o;n+=1)u[n]=str(n,s)||"null";return i=0===u.length?"[]":gap?"[\n"+gap+u.join(",\n"+gap)+"\n"+a+"]":"["+u.join(",")+"]",gap=a,i}if(rep&&"object"===(void 0===rep?"undefined":(0,_typeof3["default"])(rep)))for(o=rep.length,n=0;n<o;n+=1)"string"==typeof rep[n]&&(r=rep[n],(i=str(r,s))&&u.push(quote(r)+(gap?": ":":")+i));else for(r in s)Object.prototype.hasOwnProperty.call(s,r)&&(i=str(r,s))&&u.push(quote(r)+(gap?": ":":")+i);return i=0===u.length?"{}":gap?"{\n"+gap+u.join(",\n"+gap)+"\n"+a+"}":"{"+u.join(",")+"}",gap=a,i}}var rx_one=/^[\],:{}\s]*$/,rx_two=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rx_three=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rx_four=/(?:^|:|,)(?:\s*\[)+/g,rx_escapable=/[\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},Boolean.prototype.toJSON=this_value,Number.prototype.toJSON=this_value,String.prototype.toJSON=this_value);var gap,indent,meta,rep;"function"!=typeof _stringify2["default"]&&(meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(e,t,n){var r;if(gap="",indent="","number"==typeof n)for(r=0;r<n;r+=1)indent+=" ";else"string"==typeof n&&(indent=n);if(rep=t,t&&"function"!=typeof t&&("object"!==(void 0===t?"undefined":(0,_typeof3["default"])(t))||"number"!=typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){function walk(e,t){var n,r,i=e[t];if(i&&"object"===(void 0===i?"undefined":(0,_typeof3["default"])(i)))for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(r=walk(i,n),r!==undefined?i[n]=r:delete i[n]);return reviver.call(e,t,i)}var j;if(text=String(text),rx_dangerous.lastIndex=0,rx_dangerous.test(text)&&(text=text.replace(rx_dangerous,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),rx_one.test(text.replace(rx_two,"@").replace(rx_three,"]").replace(rx_four,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}()},function(e,t,n){"use strict";function r(){function e(e,t,n,r,i,o){return u.call(this,"push",arguments)}function t(e,t){return u.call(this,"pop",arguments)}function n(e,t){return u.call(this,"shift",arguments)}function r(e,t,n,r,i,o){return u.call(this,"unshift",arguments)}function i(e,t,n,r,i,u){var c=a(arguments,2);return this.update(t,{},function(e){if("object"!=(void 0===e?"undefined":(0,o["default"])(e)))throw new Error('store.assign called for non-object value with key "'+t+'"');return c.unshift(e),s.apply(Object,c)})}function u(e,t){var n,r=t[1],i=a(t,2);return this.update(r,[],function(t){n=Array.prototype[e].apply(t,i)}),n}return{push:e,pop:t,shift:n,unshift:r,assign:i}}var i=n(15),o=function(e){return e&&e.__esModule?e:{"default":e}}(i),u=n(11),a=u.slice,s=u.assign,c=n(71);e.exports=[c,r]},function(e,t,n){"use strict";function r(){return this.disabled=!this.enabled,{has:i,transact:o,clear:u,forEach:a,getAll:s,serialize:c,deserialize:f}}function i(e,t){return this.get(t)!==undefined}function o(e,t,n,r){null==r&&(r=n,n=null),null==n&&(n={});var i=this.get(t,n),o=r(i);this.set(t,o===undefined?i:o)}function u(e){return this.clearAll.call(this)}function a(e,t){return this.each.call(this,function(e,n){t(n,e)})}function s(e){return this.dump.call(this)}function c(e,t){return(0,p["default"])(t)}function f(e,t){if("string"!=typeof t)return undefined;try{return JSON.parse(t)}catch(n){return t||undefined}}var l=n(12),p=function(e){return e&&e.__esModule?e:{"default":e}}(l),d=n(68),h=n(70);e.exports=[d,h,r]},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.RequestServer=undefined;var i=n(12),o=r(i),u=n(40),a=r(u),s=n(0),c=r(s);t.RequestServer=function(){function e(t){(0,c["default"])(this,e),this._requester=t}return e.prototype.get=function(e,t,n,r){return this._request("GET",e,t,n,r)},e.prototype.post=function(e,t,n,r){return this._request("POST",e,t,n,r)},e.prototype._request=function(e,t,n,r,i){var u=this;return new a["default"](function(a,s){var c=t,f=null,l=i||{};"GET"===e?r&&(c+=u._toUrlParams(r)):f=l.stringifyJsonDatas?(0,o["default"])(r):r,u._requester[e.toLocaleLowerCase()](c,n,f).then(function(e){var t=e;if(l.parseJsonResult&&"string"==typeof e.data)try{t.data=JSON.parse(e.data)}catch(n){s(n)}a(t)})["catch"](function(e){s(e)})})},e.prototype._toUrlParams=function(e){var t="";if(e){var n=[];for(var r in e)n.push(r+"="+e[r]);t=n.join("&")}return t},e}()},function(e,t,n){"use strict";t.__esModule=!0,t.gbStat=t.gbStatAttrNames=undefined;var r=n(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(r),o=n(23),u=n(38),a=(t.gbStatAttrNames={monitor:"monitor",userBehaviors:"userBehaviors"},function(){function e(){(0,i["default"])(this,e),this._gbHelper=new u.GBHelper(o.gbAttrNames.stat)}return e.prototype.hasAttr=function(e){return this._gbHelper.hasAttr(e)},e.prototype.setAttr=function(e,t){this._gbHelper.setAttr(e,t)},e.prototype.getAttr=function(e){return this._gbHelper.getAttr(e)},e}());t.gbStat=new a},function(e,t,n){"use strict";function r(){return{appId:(0,o.forceGetTypeValue)("string",u.gbConfig.getAppId(),""),appName:(0,o.forceGetTypeValue)("string",u.gbConfig.getAppName(),""),appVersion:(0,o.forceGetTypeValue)("string",u.gbConfig.getAppVersion(),""),deviceSign:(0,o.forceGetTypeValue)("string",(0,a.deviceid)(),""),sdkVersion:(0,o.forceGetTypeValue)("string",u.gbConfig.getSDKVersion(),""),platform:(0,o.forceGetTypeValue)("string",(0,a.Platform)(),"")}}function i(){return"https://"+u.gbConfig.getAnalysisServer()}t.__esModule=!0,t.getPublicData=r,t.getStatServerUrl=i;var o=n(84),u=n(60),a=n(49)},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.StatServer=undefined;var i=n(12),o=r(i),u=n(0),a=r(u),s=n(46),c=n(143),f=n(144),l=n(23),p=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}(l);t.StatServer=function(){function e(t,n){var r=arguments.length>2&&arguments[2]!==undefined&&arguments[2];(0,a["default"])(this,e),this._topic=n,this._preUrl=(t||"https://xluser-test-ssl.n0808.com")+"/analysis-report/v1/"+n+"?msg=",this._needEncrypt=!!r,this._requestServer=c.requestServerLoader.get(),this._publicData={}}return e.prototype._report=function(e){var t="",n=(0,o["default"])(e),r=f.base64ServerLoader.get().encode(n);r=r.replace(/\+/g,"-"),r=r.replace(/\//g,"_");var i=r.length,u=0;i>=2&&"="===r.charAt(i-1)&&(u="="===r.charAt(i-2)?2:1),t=r.substr(0,i-u);var a=this._preUrl+t,s=!0;if(p.getEnvType()!==p.gbEnvTypes.pluginIndex){var c=c||parent.xdas;c&&"function"==typeof c.fireStatEvent&&(s=!1,c.fireStatEvent(a))}s&&this._requestServer.get(a,null,null,null)["catch"](function(e){})},e.prototype.setPublicData=function(e){this._publicData=e},e.prototype.stat=function(e){var t=e||{},n=(0,s.combineJsonObject)({reportTime:(0,s.dateToTimeString)(new Date)},this._publicData);n=(0,s.combineJsonObject)(n,t),this._report(n)},e}()},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.requestServerLoader=t.RequestServerLoader=undefined;var i=n(0),o=r(i),u=n(104),a=r(u),s=n(139),c=t.RequestServerLoader=function(){function e(){(0,o["default"])(this,e),this._requestServer=new s.RequestServer(a["default"])}return e.prototype.get=function(){return this._requestServer},e}();t.requestServerLoader=new c},function(e,t,n){"use strict";t.__esModule=!0,t.base64ServerLoader=t.Base64ServerLoader=undefined;var r=n(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(r),o=function(){function e(){(0,i["default"])(this,e)}return e.prototype.encode=function(e){return Base64.encode(e)},e}(),u=t.Base64ServerLoader=function(){function e(){(0,i["default"])(this,e),this._base64Server=new o}return e.prototype.get=function(){return this._base64Server},e}();t.base64ServerLoader=new u},function(e,t,n){"use strict";t.__esModule=!0;var r=n(15),i=function(e){return e&&e.__esModule?e:{"default":e}}(r);t["default"]=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":(0,i["default"])(t))&&"function"!=typeof t?e:t}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0;var i=n(179),o=r(i),u=n(95),a=r(u),s=n(15),c=r(s);t["default"]=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":(0,c["default"])(t)));e.prototype=(0,a["default"])(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(o["default"]?(0,o["default"])(e,t):e.__proto__=t)}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){for(var n in e)if(e.hasOwnProperty(n)){var r=n+"="+encodeURIComponent(e[n]);t.push(r)}return t}t.__esModule=!0,t.report_helper=undefined;var o=n(52),u=r(o),a=n(0),s=r(a),c=n(28),f=r(c),l=n(97),p=n(51),d=n(49),h=(0,p.getDomain)(location.href),g=function(){function e(){(0,s["default"])(this,e),this.data=[],this.xrInt=!1}return e.prototype.add=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:3,n={url:"",errorcode:"",responsetime:"",retrynum:"0",serverip:"",cmdid:"",domain:h,b_type:l.CONFIG.LOGIN_ID,platform:"1",clientversion:""};for(var r in n)void 0!==e[r]&&(n[r]=e[r]);this.data.push(n),this.data.length>=t&&this.exec()},e.prototype.push=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=window.Xreport?window.Xreport:window.parent.Xreport;t&&(this.xrInt||(this.xrInt=!0,t.push({type:"conf",global:(0,u["default"])({},(0,d.baseParams2)(),{category:"i_login",server:"xluser-web-login-fail"})})),t.push({type:"now",data:e}))},e.prototype.exec=function(e){var t=this.data.length,n=[];if(0===t)return!0;n.push("cnt="+t);for(var r=0;r<t;++r){n=i(this.data[r],n)}var o="http://stat.login."+l.CONFIG.DOMAIN+":1800/report";return o=o+"?"+n.join("&"),"function"==typeof e?f["default"].getJson(o,{},e):this.image(o),this.data=[],!0},e.prototype.reportRegister=function(e){var t=[],n={regtype:"",errorcode:"",responsetime:"",domain:h,bustype:l.CONFIG.LOGIN_ID,platform:"1",clientversion:""};t.push("op=regStat"),t.push("response=json");for(var r in n)void 0!==e[r]&&(n[r]=e[r]);t=i(n,t);var o="https://zhuce."+l.CONFIG.DOMAIN+"/regapi/?"+t.join("&");this.image(o)},e.prototype.image=function(e){(new Image).src=e},e}(),_=new g;t.report_helper=function(e){_.push((0,u["default"])({},e,{action:"login"}))};t["default"]=_},,,function(e,t,n){e.exports=n(160)},function(e,t,n){"use strict";t.__esModule=!0;var r=n(40),i=function(e){return e&&e.__esModule?e:{"default":e}}(r);t["default"]=function(e){return function(){var t=e.apply(this,arguments);return new i["default"](function(e,n){function r(o,u){try{var a=t[o](u),s=a.value}catch(c){return void n(c)}if(!a.done)return i["default"].resolve(s).then(function(e){r("next",e)},function(e){r("throw",e)});e(s)}return r("next")})}}},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function o(e,t,n){var r,i=(0,E.baseParamsRegister)(t);switch("function"==typeof t&&(n=t),e){case"register":i.op="usernameReg",i.from=d["default"].LOGIN_ID,t.code||delete i.code,a(d["default"].SERVER_REGISTER,i,n);break;case"captcha":if(!t.img)throw new Error("post argument error");var o,s=t.img,c=t.t?t.t:g["default"].getCookie("verify_type");c=c||"";var f=u("captcha","/image?t="+c+"&cachetime="+(new Date).getTime(),!1),l=!1;s.onerror=function(){s.onerror=s.onload=s.onreadystatechange=null,clearTimeout(o),n&&n(1,"获取验证码失败，请手动刷新")},s.onload=s.onreadystatechange=function(){l||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(l=!0,clearTimeout(o),s.onerror=s.onload=s.onreadystatechange=null,n&&n(0,"刷新成功"))},o=setTimeout(function(){s.onerror=s.onload=s.onreadystatechange=null,n&&n(1,"获取验证码失败，请手动刷新")},A),s.src=f;break;case"mobilelogin":if(!t.mobile||!t.code)throw new Error("post argument error");i.op="mobileReg",i.from=d["default"].LOGIN_ID,i.mobile=t.mobile,i.code=t.code,i.regtype="mobileLogin",r=d["default"].SERVER_REGISTER,a(r,i,function(e){n&&n(e)});break;case"getsmscode":if(!t.mobile||!t.type)throw new Error("post argument error");i.op="sendSms",i.from=d["default"].LOGIN_ID,i.mobile=t.mobile,i.verifyCode=t.verifyCode,i.verifyKey=g["default"].getCookie("VERIFY_KEY"),i.verifyType="MEA",i.v=2,i.type="register"==t.type?1:2,r=d["default"].SERVER_REGISTER,g["default"].registerPost(r,i,function(e){n&&n(e)});break;case"checkbind":if(!t.account||!t.type)throw new Error("post argument error");i.op="checkBind",i.from=d["default"].LOGIN_ID,i.response="jsonp",i.account=t.account,i.type="mail"===t.type?4:1,r=d["default"].SERVER_REGISTER,P.jsonp(r,i,function(e){n&&n(e)});break;case"mobileregister":if(!t.mobile||!t.code)throw new Error("post argument error");i.op="mobileReg",i.from=d["default"].LOGIN_ID,i.mobile=t.mobile,i.code=t.code,r=d["default"].SERVER_REGISTER,a(r,i,function(e){n&&n(e)});break;case"mobileregisterpwd":if(!(t.mobile&&t.code&&t.password))throw new Error("post argument error");i.op="mobileRegPwd",i.from=d["default"].LOGIN_ID,i.mobile=t.mobile,i.code=t.code,i.pwd=t.password,r=d["default"].SERVER_REGISTER,a(r,i,function(e){n&&n(e)});break;case"setpassword":if(!t.password)throw new Error("post argument error");i.op="changePassword",i.from=d["default"].LOGIN_ID,i.pwd=t.password,r=d["default"].SERVER_REGISTER,g["default"].registerPost(r,i,function(e){n&&n(e)});break;case"mailregister":if(!t.mail||!t.password)throw new Error("post argument error");i.op="emailReg",i.from=d["default"].LOGIN_ID,i.email=t.mail,i.pwd=t.password,t.code&&(i.code=t.code),r=d["default"].SERVER_REGISTER,a(r,i,function(e){n&&n(e)});break;case"isNeedValidate":i.op="needValidate",i.from=d["default"].LOGIN_ID,i.response="jsonp",r=d["default"].SERVER_REGISTER,P.jsonp(r,i,function(e){n&&n(e)});break;case"registerCaptcha":if(!t.img)throw new Error("post argument error");var s=t.img,f=d["default"].SERVER_REGISTER+"?op=validateImg&from="+d["default"].LOGIN_ID+"&size=M&chachtime="+(new Date).getTime(),l=!1;s.onerror=function(){s.onerror=s.onload=s.onreadystatechange=null,clearTimeout(o),n&&n(1,"获取验证码失败，请手动刷新")},s.onload=s.onreadystatechange=function(){l||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(l=!0,s.onerror=s.onload=s.onreadystatechange=null,n&&n(0,"刷新成功"))},s.src=f;break;case"checkRegisterCaptcha":var p=t.code,r=d["default"].SERVER_REGISTER+"?op=CheckVerifyCode",i=[];i.code=p,i.key=g["default"].getCookie("VERIFY_KEY"),i.type="MEA",i.response="jsonp",P.jsonp(r,i,function(e){n&&n(e)});break;default:throw new Error("not support action: "+e)}}function u(e,t){var n=!(arguments.length>2&&arguments[2]!==undefined)||arguments[2],r=!1;switch(C.getEnvType()){case C.gbEnvTypes.outsideIframe:r=window.gslb;break;case C.gbEnvTypes.insideIframe:r=parent.gslb}var i=xlQuickLogin.PARAMS||xll.PARAMS||{};L=i.SERVER_LOGIN||x,r&&i.CAN_GSLB&&(L=r.gslbUse("login",x),R=r.gslbUse("captcha",R));var o="captcha"===e?R:L,u=m["default"].map(o,function(e){return"https://"+e+t});return n?u:u[0]}function a(e,t,n){var r=(new Date).getTime();g["default"].registerPost(e,t,function(e){v["default"].reportRegister({regtype:t.regtype?t.regtype:t.op,errorcode:e.result,responsetime:((new Date).getTime()-r)/1e3}),n(e)})}t.__esModule=!0,t.register=t.req=undefined;var s=n(0),c=i(s),f=n(164),l=r(f),p=n(73),d=i(p),h=n(28),g=i(h),_=n(147),v=i(_),y=n(24),m=i(y),I=n(62),E=n(49),b=n(153),w=i(b),O=n(147),N=n(51),S=n(23),C=r(S);d["default"].DOMAIN=(0,N.getDomain)();var T="."+d["default"].DOMAIN,A=(d["default"].RETRY_LOGIN_ON_SERVER_ERROR,700),L=["https://xluser-ssl","https://xluser2-ssl","https://xluser3-ssl"],R=["captcha-ssl."+d["default"].DOMAIN,"captcha2-ssl."+d["default"].DOMAIN,"https://captcha3-ssl."+d["default"].DOMAIN],x=["xluser-ssl."+d["default"].DOMAIN,"xluser2-ssl."+d["default"].DOMAIN,"xluser3-ssl."+d["default"].DOMAIN],F=function(){function e(){(0,c["default"])(this,e),this.baseUrl="/xluser.core.login/v3/"}return e.prototype.loginRequest=function(e,t,n,r,i){w["default"].post(t,n,{enctype:"application/x-www-form-urlencoded"}).then(function(e){return r(e)})},e.prototype.post=function(e,t,n){w["default"].post(e,t,O.report_helper).then(function(e){return n(e)})["catch"](function(e){return n(e)})},e.prototype.jsonp=function(e,t,n){var r=arguments.length>3&&arguments[3]!==undefined?arguments[3]:5e3,i=arguments.length>4&&arguments[4]!==undefined?arguments[4]:"callback";w["default"].jsonp(e,t,i,r,O.report_helper).then(function(e){return n(e)})["catch"](function(e){return n(e)})},e.prototype.loginkey=function(e,t){var n=this.baseUrl+"loginkey",r={},i=(0,E.baseParams2)();for(var o in i)r[o]=i[o];r.loginKey=e.loginkey,r.userName=e.userid,r.format="cookie";var a=u("login",n);this.post(a,r,function(e){!!t&&t(e)})},e.prototype.getuserinfo=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},n=this.baseUrl+"getuserinfo",r={},i=(0,E.baseParams2)(),o=g["default"].getUrlParams(window.location.href);for(var a in i)r[a]=o[a]?o[a]:i[a];r.userID=t.userID||g["default"].getCookie("userid"),r.sessionID=t.sessionID||g["default"].getCookie("sessionid"),r.vasid=t.vasid||"2,14,33,34,35",t.appid&&(r.appid=t.appid),t.appName&&(r.appName=t.appName),t.devicesign&&(r.devicesign=t.devicesign),r.format="jsonp";var s=u("login",n);this.jsonp(s,r,function(t){e(t)},A)},e.prototype.ping=function(e,t){var n,r=this.baseUrl+"ping",i=(0,E.baseParams2)(e);g["default"].delCookie("blogresult",T),n=u("login",r),this.post(n,i,function(e){t&&t(e.blogresult)})},e.prototype.logout=function(e){var t=this.baseUrl+"logout",n={userID:g["default"].getCookie("userid")||g["default"].getCookie("userID"),sessionID:g["default"].getCookie("sessionid")},r=(0,E.baseParams2)(n),i=u("login",t);this.post(i,r,function(t){l.deleteCookieUserInfo(),e&&e()},A)},e.prototype.sessionlogin=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=arguments[1],n=this.baseUrl+"sessionlogin",r=(0,E.baseParams2)(e);r.sessionType=(0,I.checkAsPCFlow)()?1:0,r.sessionFromAppid=window.APPID||"";var i=u("login",n);this.post(i,r,function(e){t&&t(e)})},e.prototype.jumplogin=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=arguments[1],n=this.baseUrl+"jumplogin",r=(0,E.baseParams2)(e),i=u("login",n);this.post(i,r,function(e){t&&t(e)})},e}(),P=new F;t.req=P,t.register=o},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:I,n=document.createElement("form");return n.style.display="none",n.method=e,n.enctype=t,n.encoding=t,n.acceptCharset="UTF-8",document.body.appendChild(n),n}function o(e){var t=e,n={errorCode:"blogresult",errorDesc:"errdesc",userID:"userid",loginKey:"loginkey",nickName:"usernick",sessionID:"sessionid",userName:"usrname",userNewNo:"usernewno",account:"score",verifyType:"VERIFY_KEY",errorIsRetry:"error_retry"};for(var r in n)if(e[r]!=undefined){var i=n[r];t[i]=e[r]}for(var o in t)store.set(o,t[o]);return t}t.__esModule=!0,t.Request=undefined;var u=n(150),a=r(u),s=n(151),c=r(s),f=n(40),l=r(f),p=n(0),d=r(p),h=n(73),g=r(h),_=n(28),v=r(_),y=n(24),m=r(y),I="multipart/form-data",E=t.Request=function(){function e(){(0,d["default"])(this,e)}return e.prototype.post=function(e,t){var n=this,r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null,i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:{enctype:I};return new l["default"](function(){var o=(0,c["default"])(a["default"].mark(function u(o,s){var c,f,l,p;return a["default"].wrap(function(u){for(;;)switch(u.prev=u.next){case 0:c=m["default"].isArray(e)?e:[e],f=c.length,l=0;case 3:if(!(l<f)){u.next=24;break}return u.prev=4,u.next=7,n._post(c[l],t,r,i);case 7:if((p=u.sent)&&"undefined"!=typeof p.error_retry&&0!=p.error_retry&&l!==f-1){u.next=11;break}return o(p),u.abrupt("break",24);case 11:u.next=19;break;case 13:if(u.prev=13,u.t0=u["catch"](4),l!==f-1){u.next=19;break}return s(u.t0),u.abrupt("break",24);case 19:return u.next=21,n._sleep(1e3);case 21:l++,u.next=3;break;case 24:case"end":return u.stop()}},u,n,[[4,13]])}));return function(e,t){return o.apply(this,arguments)}}())},e.prototype._post=function(e,t){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null,r=arguments.length>3&&arguments[3]!==undefined?arguments[3]:{enctype:I};r=r||{enctype:I};var i=r.enctype!==undefined?r.enctype:I,o=null,u=r.requestMonitorStatHelper?r.requestMonitorStatHelper:null;return u&&(u.newStat(),o=u.getStat()),this.request("POST",e,t,n,5e3,o,i)},e.prototype.get=function(e,t){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;return this.request("GET",e,t,n)},e.prototype.request_helper=function(e,t,n,r,i){var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0,u=arguments.length>6&&arguments[6]!==undefined?arguments[6]:null,a=t.length,s=t[o],c=o,f=this;if(c>=a)return r&&r("TIMEOUT");var l=(new Date).getTime();f[e](s,n).then(function(e){return r&&r(e)})["catch"](function(o){return u&&u({url:s,errorcode:o,responsetime:((new Date).getTime()-l)/1e3,retrynum:c}),c+=1,f.request_helper(e,t,n,r,i,c,u)})},e.prototype.jsonp=function(e,t){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"callback",r=this,i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:5e3,o=arguments.length>4&&arguments[4]!==undefined?arguments[4]:null;return m["default"].isArray(e)?new l["default"](function(o,u){return 0==e.length?u("TIMEOUT"):r.jsonp(e.shift(),t,n,i).then(o)["catch"](function(a){r.jsonp(e,t,n,i).then(o)["catch"](function(e){return u(e)})})}):new l["default"](function(u,a){var s=document.createElement("script"),c=document.getElementsByTagName("head")[0],f="jsonp"+(new Date).getTime()+parseInt(1e3*Math.random()),l=r.url_param(t),p=null;e+=(e.indexOf("?")>=0?"&":"?")+l+"&"+n+"="+f,window[f]=function(e){p&&clearTimeout(p),u(e);try{delete window[f]}catch(t){}c.removeChild(s)},i>0&&(p=setTimeout(function(){o&&o({url:e,method:"jsonp",errorcode:"timeout"}),a("TIMEOUT")},i)),s.onerror=function(){p&&clearTimeout(p),o&&o({url:e,method:"jsonp",errorcode:"error"}),a("TIMEOUT")},s.async="async",s.src=e,c.insertBefore(s,c.firstChild)})},e.prototype.request=function(e,t,n){var r=arguments.length>3&&arguments[3]!==undefined?arguments[3]:null,u=arguments.length>4&&arguments[4]!==undefined?arguments[4]:5e3,a=this,s=arguments.length>5&&arguments[5]!==undefined?arguments[5]:null,c=arguments.length>6&&arguments[6]!==undefined?arguments[6]:I;return new l["default"](function(f,l){function p(){if(s&&s.recordCostTime(),!I.onerror)return s&&s.recordTimeout(),l("TIMEOUT");E&&clearTimeout(E),I.onreadystatechange=I.onerror=I.onload=null;var n=v["default"].getCookie();if(_){var i=I.contentDocument.body;if(1!=i.childNodes.length)return r&&r({url:t,method:e,errorcode:"error"}),s&&s.recordBusinessStatusText("get json error"),void l("ERROR");var u=i.childNodes[0].innerHTML;try{n=o(JSON.parse(u))}catch(a){n={}}}if(s&&n&&s.recordBusinessStatus(n.blogresult),!n.blogresult&&"0"!=n.blogresult)return r&&r({url:t,method:e,errorcode:"error"}),l("ERROR");setTimeout(function(){I=null,d=null},500),v["default"].delCookie("blogresult",g["default"].DOMAIN),v["default"].delCookie("error_retry",g["default"].DOMAIN),f(n)}var d=i(e,c),h="f"+a.randomstring();d.target=h;var _="json"==n.format;if("GET"==e)t+=a.url_param(n);else for(var y in n){var m=document.createElement("textarea");m.name=y,m.value=n[y],d.appendChild(m)}document.body.appendChild(d);var I=a._create_iframe(h);d.appendChild(I);var E=void 0;if(u>0&&(E=setTimeout(function(){return r&&r({url:t,method:e,errorcode:"timeout"}),s&&(s.recordCostTime(),s.recordTimeout()),l("TIMEOUT")},u)),I.onerror=I.onload=p,I.onreadystatechange=function(e){"complete"==I.readyState&&p()},s&&(s.recordUrl(t),s.recordRequestTime()),"GET"===e)I.src=t;else{try{d.action=t}catch(b){d.setAttribute("action",t)}d.submit()}})},e.prototype.url_param=function(e){var t=[];for(var n in e)t.push(n+"="+e[n]);return t.join("&")},e.prototype._create_iframe=function(e){var t=document.createElement("iframe");return t.name=e,t.id=e,t.style.display="none",t},e.prototype.randomstring=function(){return Math.random().toString(36).replace(/[^a-z0-9]+/g,"")},e.prototype._sleep=function(){function e(e){return t.apply(this,arguments)}var t=(0,c["default"])(a["default"].mark(function n(e){return a["default"].wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new l["default"](function(t){window.setTimeout(function(){t()},e)}));case 1:case"end":return t.stop()}},n,this)}));return e}(),e}(),b=new E;window.request=b,t["default"]=b,e.exports=b},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});!function(e){e.UNREACHABLE="unreachable",e.LOCAL="local",e.CANCELLED="cancelled",e.UNKNOWN="unknown",e.INVALID_ARGUMENT="invalid_argument",e.DEADLINE_EXCEEDED="deadline_exceeded",e.NOT_FOUND="not_found",e.ALREADY_EXISTS="already_exists",e.PERMISSION_DENIED="permission_denied",e.UNAUTHENTICATED="unauthenticated",e.RESOURCE_EXHAUSTED="resource_exhausted",e.FAILED_PRECONDITION="failed_precondition",e.ABORTED="aborted",e.OUT_OF_RANGE="out_of_range",e.UNIMPLEMENTED="unimplemented",e.INTERNAL="internal",e.UNAVAILABLE="unavailable",e.DATA_LOSS="data_loss",e.CAPTCHA_REQUIRED="captcha_required",e.CAPTCHA_INVALID="captcha_invalid",e.INVALID_PASSWORD="invalid_password",e.INVALID_STATUS="invalid_status",e.USER_PENDING="user_pending",e.USER_BLOCKED="user_blocked",e.INVALID_VERIFICATION_CODE="invalid_verification_code",e.TWO_FACTOR_REQUIRED="two_factor_required",e.INVALID_TWO_FACTOR="invalid_two_factor",e.INVALID_TWO_FACTOR_RECOVERY="invalid_two_factor_recovery",e.UNDER_REVIEW="under_review",e.INVALID_REQUEST="invalid_request",e.UNAUTHORIZED_CLIENT="unauthorized_client",e.ACCESS_DENIED="access_denied",e.UNSUPPORTED_RESPONSE_TYPE="unsupported_response_type",e.INVALID_SCOPE="invalid_scope",e.INVALID_GRANT="invalid_grant",e.SERVER_ERROR="server_error",e.TEMPORARILY_UNAVAILABLE="temporarily_unavailable",e.INTERACTION_REQUIRED="interaction_required",e.LOGIN_REQUIRED="login_required",e.ACCOUNT_SELECTION_REQUIRED="account_selection_required",e.CONSENT_REQUIRED="consent_required",e.INVALID_REQUEST_URI="invalid_request_uri",e.INVALID_REQUEST_OBJECT="invalid_request_object",e.REQUEST_NOT_SUPPORTED="request_not_supported",e.REQUEST_URI_NOT_SUPPORTED="request_uri_not_supported",e.REGISTRATION_NOT_SUPPORTED="registration_not_supported"}(t.ErrorType||(t.ErrorType={}))},function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=n(158),o=n(154),u=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getAppInfo=function(){var e={error:o.ErrorType.NOT_FOUND};return Promise.reject(e)},t.prototype.getSessionUserInfo=function(){var e={error:o.ErrorType.NOT_FOUND};return Promise.reject(e)},t.prototype.setLoginInfo=function(e){var t={error:o.ErrorType.NOT_FOUND};return Promise.reject(t)},t.prototype.showLoginWindow=function(){var e={error:o.ErrorType.NOT_FOUND};return Promise.reject(e)},t.prototype.getAccessToken=function(){var e={error:o.ErrorType.NOT_FOUND};return Promise.reject(e)},t}(i.EventEmitter);t.NativeApi=u},,,function(e,t,n){"use strict";function r(e){console&&console.warn}function i(){i.init.call(this)}function o(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function u(e){return e._maxListeners===undefined?i.defaultMaxListeners:e._maxListeners}function a(e,t,n,i){var a,s,c;if(o(n),s=e._events,s===undefined?(s=e._events=Object.create(null),e._eventsCount=0):(s.newListener!==undefined&&(e.emit("newListener",t,n.listener?n.listener:n),s=e._events),c=s[t]),c===undefined)c=s[t]=n,++e._eventsCount;else if("function"==typeof c?c=s[t]=i?[n,c]:[c,n]:i?c.unshift(n):c.push(n),(a=u(e))>0&&c.length>a&&!c.warned){c.warned=!0;var f=new Error("Possible EventEmitter memory leak detected. "+c.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");f.name="MaxListenersExceededWarning",f.emitter=e,f.type=t,f.count=c.length,r(f)}return e}function s(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function c(e,t,n){var r={fired:!1,wrapFn:undefined,target:e,type:t,listener:n},i=s.bind(r);return i.listener=n,r.wrapFn=i,i}function f(e,t,n){var r=e._events;if(r===undefined)return[];var i=r[t];return i===undefined?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?h(i):p(i,i.length)}function l(e){var t=this._events;if(t!==undefined){var n=t[e];if("function"==typeof n)return 1;if(n!==undefined)return n.length}return 0}function p(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function d(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function h(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function g(e,t){return new Promise(function(n,r){function i(){o!==undefined&&e.removeListener("error",o),n([].slice.call(arguments))}var o;"error"!==t&&(o=function(n){e.removeListener(t,i),r(n)},e.once("error",o)),e.once(t,i)})}var _,v="object"==typeof Reflect?Reflect:null,y=v&&"function"==typeof v.apply?v.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};_=v&&"function"==typeof v.ownKeys?v.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var m=Number.isNaN||function(e){return e!==e};e.exports=i,e.exports.once=g,i.EventEmitter=i,i.prototype._events=undefined,i.prototype._eventsCount=0,i.prototype._maxListeners=undefined;var I=10;Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return I},set:function(e){if("number"!=typeof e||e<0||m(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");I=e}}),i.init=function(){this._events!==undefined&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||undefined},i.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||m(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},i.prototype.getMaxListeners=function(){return u(this)},i.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var r="error"===e,i=this._events;if(i!==undefined)r=r&&i.error===undefined;else if(!r)return!1;if(r){var o;if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var u=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw u.context=o,u}var a=i[e];if(a===undefined)return!1;if("function"==typeof a)y(a,this,t);else for(var s=a.length,c=p(a,s),n=0;n<s;++n)y(c[n],this,t);return!0},i.prototype.addListener=function(e,t){return a(this,e,t,!1)},i.prototype.on=i.prototype.addListener,i.prototype.prependListener=function(e,t){return a(this,e,t,!0)},i.prototype.once=function(e,t){return o(t),this.on(e,c(this,e,t)),this},i.prototype.prependOnceListener=function(e,t){return o(t),this.prependListener(e,c(this,e,t)),this},i.prototype.removeListener=function(e,t){var n,r,i,u,a;if(o(t),(r=this._events)===undefined)return this;if((n=r[e])===undefined)return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(i=-1,u=n.length-1;u>=0;u--)if(n[u]===t||n[u].listener===t){a=n[u].listener,i=u;break}if(i<0)return this;0===i?n.shift():d(n,i),1===n.length&&(r[e]=n[0]),r.removeListener!==undefined&&this.emit("removeListener",e,a||t)}return this},i.prototype.off=i.prototype.removeListener,i.prototype.removeAllListeners=function(e){var t,n,r;if((n=this._events)===undefined)return this;if(n.removeListener===undefined)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):n[e]!==undefined&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)"removeListener"!==(i=o[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(t!==undefined)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},i.prototype.listeners=function(e){return f(this,e,!0)},i.prototype.rawListeners=function(e){return f(this,e,!1)},i.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):l.call(e,t)},i.prototype.listenerCount=l,i.prototype.eventNames=function(){return this._eventsCount>0?_(this._events):[]}},,function(e,t,n){var r=function(){return this}()||Function("return this")(),i=r.regeneratorRuntime&&Object.getOwnPropertyNames(r).indexOf("regeneratorRuntime")>=0,o=i&&r.regeneratorRuntime;if(r.regeneratorRuntime=undefined,e.exports=n(161),i)r.regeneratorRuntime=o;else try{delete r.regeneratorRuntime}catch(u){r.regeneratorRuntime=undefined}},function(e,t){!function(t){"use strict";function n(e,t,n,r){var o=t&&t.prototype instanceof i?t:i,u=Object.create(o.prototype),a=new d(r||[]);return u._invoke=c(e,n,a),u}function r(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(r){return{type:"throw",arg:r}}}function i(){}function o(){}function u(){}function a(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function s(e){function t(n,i,o,u){var a=r(e[n],e,i);if("throw"!==a.type){var s=a.arg,c=s.value;return c&&"object"==typeof c&&y.call(c,"__await")?Promise.resolve(c.__await).then(function(e){t("next",e,o,u)},function(e){t("throw",e,o,u)}):Promise.resolve(c).then(function(e){s.value=e,o(s)},u)}u(a.arg)}function n(e,n){function r(){return new Promise(function(r,i){t(e,n,r,i)})}return i=i?i.then(r,r):r()}var i;this._invoke=n}function c(e,t,n){var i=N;return function(o,u){if(i===C)throw new Error("Generator is already running");if(i===T){if("throw"===o)throw u;return g()}for(n.method=o,n.arg=u;;){var a=n.delegate;if(a){var s=f(a,n);if(s){if(s===A)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===N)throw i=T,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=C;var c=r(e,t,n);if("normal"===c.type){if(i=n.done?T:S,c.arg===A)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=T,n.method="throw",n.arg=c.arg)}}}function f(e,t){var n=e.iterator[t.method];if(n===_){if(t.delegate=null,"throw"===t.method){if(e.iterator["return"]&&(t.method="return",t.arg=_,f(e,t),"throw"===t.method))return A;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return A}var i=r(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,A;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=_),t.delegate=null,A):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,A)}function l(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function p(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function d(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(l,this),this.reset(!0)}function h(e){if(e){var t=e[I];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function i(){for(;++n<e.length;)if(y.call(e,n))return i.value=e[n],i.done=!1,i;return i.value=_,i.done=!0,i};return r.next=r}}return{next:g}}function g(){return{value:_,done:!0}}var _,v=Object.prototype,y=v.hasOwnProperty,m="function"==typeof Symbol?Symbol:{},I=m.iterator||"@@iterator",E=m.asyncIterator||"@@asyncIterator",b=m.toStringTag||"@@toStringTag",w="object"==typeof e,O=t.regeneratorRuntime;if(O)return void(w&&(e.exports=O));O=t.regeneratorRuntime=w?e.exports:{},O.wrap=n;var N="suspendedStart",S="suspendedYield",C="executing",T="completed",A={},L={};L[I]=function(){return this};var R=Object.getPrototypeOf,x=R&&R(R(h([])));x&&x!==v&&y.call(x,I)&&(L=x);var F=u.prototype=i.prototype=Object.create(L);o.prototype=F.constructor=u,u.constructor=o,u[b]=o.displayName="GeneratorFunction",O.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},O.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,b in e||(e[b]="GeneratorFunction")),e.prototype=Object.create(F),e},O.awrap=function(e){return{__await:e}},a(s.prototype),s.prototype[E]=function(){return this},O.AsyncIterator=s,O.async=function(e,t,r,i){var o=new s(n(e,t,r,i));return O.isGeneratorFunction(t)?o:o.next().then(function(e){return e.done?e.value:o.next()})},a(F),F[b]="Generator",F[I]=function(){return this},F.toString=function(){return"[object Generator]"},O.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},O.values=h,d.prototype={constructor:d,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=_,this.done=!1,this.delegate=null,this.method="next",this.arg=_,this.tryEntries.forEach(p),!e)for(var t in this)"t"===t.charAt(0)&&y.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=_)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){function t(t,r){return o.type="throw",o.arg=e,n.next=t,r&&(n.method="next",n.arg=_),!!r}if(this.done)throw e;for(var n=this,r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],o=i.completion;if("root"===i.tryLoc)return t("end");if(i.tryLoc<=this.prev){var u=y.call(i,"catchLoc"),a=y.call(i,"finallyLoc");if(u&&a){if(this.prev<i.catchLoc)return t(i.catchLoc,!0);if(this.prev<i.finallyLoc)return t(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return t(i.catchLoc,!0)}else{if(!a)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return t(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&y.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,A):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),A},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),p(n),A}},"catch":function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;p(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:h(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=_),A}}}(function(){return this}()||Function("return this")())},function(e,t,n){e.exports={"default":n(167),__esModule:!0}},function(e,t,n){"use strict";function r(){return _===undefined&&(_=/\bedge\b/.test(h)),_}function i(){return v===undefined&&(v=/[ ]thunder\/10.0([\d.]*)/.test(h)||/[ ]thunder( )?\/( )?9.([\d.]*)/.test(h)),v}function o(){return y===undefined&&(y=/\bthunder\/10.[1-9][\d.]*/.test(h)),y}function u(){return m===undefined&&(m=/\bmac\b/.test(h)&&!!g.WebViewJavascriptBridge),m}function a(){return I===undefined&&(I=/\belectron\/\d+(\.\d+){2}\b/.test(h)),I}function s(){return E===undefined&&(E=/\btbc\/\d+(\.\d+){3}\b/.test(h)&&!!g["native"]),E}function c(){return b===undefined&&(b=!f()&&("undefined"!=typeof g.XLJSWebViewBridgeExport||"undefined"!=typeof g.XLJSWebViewBridge)),b}function f(){return w===undefined&&(w="undefined"!=typeof g.webkit&&(g.webkit.messageHandlers,!0)&&"undefined"!=typeof g.webkit.messageHandlers.XLJSWebViewBridgeExport||"undefined"!=typeof g.webkit&&(g.webkit.messageHandlers,!0)&&"undefined"!=typeof g.webkit.messageHandlers.XLJSWebViewBridge),w}function l(){return O===undefined&&(O="undefined"!=typeof g.XLAccountJsBridge),O}function p(){return o()||!i()&&s()}function d(){return N===undefined&&(N="file:"===location.protocol),N}Object.defineProperty(t,"__esModule",{value:!0});var h=navigator.userAgent.toLocaleLowerCase(),g=window,_=undefined,v=undefined,y=undefined,m=undefined,I=undefined,E=undefined,b=undefined,w=undefined,O=undefined,N=undefined;t.checkIsEdge=r,t.checkIsXl9=i,t.checkIsXlx=o,t.checkIsXlMac=u,t.checkIsXdas=a,t.checkIsTBC=s,t.checkIsOldAndroidMobileClient=c,t.checkIsOldIOSMobileClient=f,t.checkSupportAccountMobileSDK=l,t.checkIsNewTBC=p,t.checkIsLocal=d},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(){f["default"].forEach("VERIFY_KEY,blogresult,active,isspwd,score,downbyte,isvip,jumpkey,logintype,nickname,onlinetime,order,safe,downfile,sessionid,sex,upgrade,userid,usernewno,usernick,usertype,usrname,loginkey,xl_autologin".split(","),function(e){s["default"].delCookie(e,u["default"].DOMAIN)}),s["default"].delCookie(u["default"].LOGIN_TYPE_COOKIE_NAME,u["default"].DOMAIN)}t.__esModule=!0,t.deleteCookieUserInfo=i;var o=n(73),u=r(o),a=n(28),s=r(a),c=n(24),f=r(c)},,function(e,t,n){"use strict";var r=n(162),i=function(e){return e&&e.__esModule?e:{"default":e}}(r);!function(t){var r,o=t.Base64;if(void 0!==e&&e.exports)try{r=n(169).Buffer}catch(S){}var u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=function(e){for(var t={},n=0,r=e.length;n<r;n++)t[e.charAt(n)]=n;return t}(u),s=String.fromCharCode,c=function(e){if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?s(192|t>>>6)+s(128|63&t):s(224|t>>>12&15)+s(128|t>>>6&63)+s(128|63&t)}var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return s(240|t>>>18&7)+s(128|t>>>12&63)+s(128|t>>>6&63)+s(128|63&t)},f=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,l=function(e){return e.replace(f,c)},p=function(e){var t=[0,2,1][e.length%3],n=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0);return[u.charAt(n>>>18),u.charAt(n>>>12&63),t>=2?"=":u.charAt(n>>>6&63),t>=1?"=":u.charAt(63&n)].join("")},d=t.btoa?function(e){return t.btoa(e)}:function(e){return e.replace(/[\s\S]{1,3}/g,p)},h=r?function(e){return(e.constructor===r.constructor?e:new r(e)).toString("base64")}:function(e){return d(l(e))},g=function(e,t){return t?h(String(e)).replace(/[+\/]/g,function(e){return"+"==e?"-":"_"}).replace(/=/g,""):h(String(e))},_=function(e){return g(e,!0)},v=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),y=function(e){switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),n=t-65536;return s(55296+(n>>>10))+s(56320+(1023&n));case 3:return s((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return s((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},m=function(e){return e.replace(v,y)},I=function(e){var t=e.length,n=t%4,r=(t>0?a[e.charAt(0)]<<18:0)|(t>1?a[e.charAt(1)]<<12:0)|(t>2?a[e.charAt(2)]<<6:0)|(t>3?a[e.charAt(3)]:0),i=[s(r>>>16),s(r>>>8&255),s(255&r)];return i.length-=[0,0,2,1][n],i.join("")},E=t.atob?function(e){return t.atob(e)}:function(e){return e.replace(/[\s\S]{1,4}/g,I)},b=r?function(e){return(e.constructor===r.constructor?e:new r(e,"base64")).toString()}:function(e){return m(E(e))},w=function(e){return b(String(e).replace(/[-_]/g,function(e){return"-"==e?"+":"/"}).replace(/[^A-Za-z0-9\+\/]/g,""))},O=function(){var e=t.Base64;return t.Base64=o,e};if(t.Base64={VERSION:"2.1.9",atob:E,btoa:d,fromBase64:w,toBase64:g,utob:l,encode:g,encodeURI:_,btou:m,decode:w,noConflict:O},"function"==typeof i["default"]){var N=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}};t.Base64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",N(function(){return w(this)})),Object.defineProperty(String.prototype,"toBase64",N(function(e){return g(this,e)})),Object.defineProperty(String.prototype,"toBase64URI",N(function(){return g(this,!0)}))}}t.Meteor&&(Base64=t.Base64)}(window)},function(e,t,n){n(168);var r=n(2).Object;e.exports=function(e,t,n){return r.defineProperty(e,t,n)}},function(e,t,n){var r=n(7);r(r.S+r.F*!n(4),"Object",{defineProperty:n(8).f})},function(e,t,n){"use strict";(function(e){function r(){return o.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function i(e,t){if(r()<t)throw new RangeError("Invalid typed array length");return o.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=o.prototype):(null===e&&(e=new o(t)),e.length=t),e}function o(e,t,n){if(!(o.TYPED_ARRAY_SUPPORT||this instanceof o))return new o(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return c(this,e)}return u(this,e,t,n)}function u(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?p(e,t,n,r):"string"==typeof t?f(e,t,n):d(e,t)}function a(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function s(e,t,n,r){return a(t),t<=0?i(e,t):n!==undefined?"string"==typeof r?i(e,t).fill(n,r):i(e,t).fill(n):i(e,t)}function c(e,t){if(a(t),e=i(e,t<0?0:0|h(t)),!o.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function f(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!o.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|_(t,n);e=i(e,r);var u=e.write(t,n);return u!==r&&(e=e.slice(0,u)),e}function l(e,t){var n=t.length<0?0:0|h(t.length);e=i(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function p(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return t=n===undefined&&r===undefined?new Uint8Array(t):r===undefined?new Uint8Array(t,n):new Uint8Array(t,n,r),o.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=o.prototype):e=l(e,t),e}function d(e,t){if(o.isBuffer(t)){var n=0|h(t.length);return e=i(e,n),0===e.length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||K(t.length)?i(e,0):l(e,t);if("Buffer"===t.type&&$(t.data))return l(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function h(e){if(e>=r())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r().toString(16)+" bytes");return 0|e}function g(e){return+e!=e&&(e=0),o.alloc(+e)}function _(e,t){if(o.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case undefined:return H(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return q(e).length;default:if(r)return H(e).length;t=(""+t).toLowerCase(),r=!0}}function v(e,t,n){var r=!1;if((t===undefined||t<0)&&(t=0),t>this.length)return"";if((n===undefined||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return x(this,t,n);case"utf8":case"utf-8":return T(this,t,n);case"ascii":return L(this,t,n);case"latin1":case"binary":return R(this,t,n);case"base64":return C(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return F(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function y(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function m(e,t,n,r,i){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=o.from(t,r)),o.isBuffer(t))return 0===t.length?-1:I(e,t,n,r,i);if("number"==typeof t)return t&=255,o.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):I(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function I(e,t,n,r,i){function o(e,t){return 1===u?e[t]:e.readUInt16BE(t*u)}var u=1,a=e.length,s=t.length;if(r!==undefined&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;u=2,a/=2,s/=2,n/=2}var c;if(i){var f=-1;for(c=n;c<a;c++)if(o(e,c)===o(t,-1===f?0:c-f)){if(-1===f&&(f=c),c-f+1===s)return f*u}else-1!==f&&(c-=c-f),f=-1}else for(n+s>a&&(n=a-s),c=n;c>=0;c--){for(var l=!0,p=0;p<s;p++)if(o(e,c+p)!==o(t,p)){l=!1;break}if(l)return c}return-1}function E(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r))>i&&(r=i):r=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var u=0;u<r;++u){var a=parseInt(t.substr(2*u,2),16);if(isNaN(a))return u;e[n+u]=a}return u}function b(e,t,n,r){return J(H(t,e.length-n),e,n,r)}function w(e,t,n,r){return J(X(t),e,n,r)}function O(e,t,n,r){return w(e,t,n,r)}function N(e,t,n,r){return J(q(t),e,n,r)}function S(e,t,n,r){return J(W(t,e.length-n),e,n,r)}function C(e,t,n){return 0===t&&n===e.length?Q.fromByteArray(e):Q.fromByteArray(e.slice(t,n))}function T(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var o=e[i],u=null,a=o>239?4:o>223?3:o>191?2:1;if(i+a<=n){var s,c,f,l;switch(a){case 1:o<128&&(u=o);break;case 2:s=e[i+1],128==(192&s)&&(l=(31&o)<<6|63&s)>127&&(u=l);break;case 3:s=e[i+1],c=e[i+2],128==(192&s)&&128==(192&c)&&(l=(15&o)<<12|(63&s)<<6|63&c)>2047&&(l<55296||l>57343)&&(u=l);break;case 4:s=e[i+1],c=e[i+2],f=e[i+3],128==(192&s)&&128==(192&c)&&128==(192&f)&&(l=(15&o)<<18|(63&s)<<12|(63&c)<<6|63&f)>65535&&l<1114112&&(u=l)}}null===u?(u=65533,a=1):u>65535&&(u-=65536,r.push(u>>>10&1023|55296),u=56320|1023&u),r.push(u),i+=a}return A(r)}function A(e){var t=e.length;if(t<=Z)return String.fromCharCode.apply(String,e);for(var n="",r=0;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=Z));return n}function L(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function R(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function x(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=t;o<n;++o)i+=Y(e[o]);return i}function F(e,t,n){for(var r=e.slice(t,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function P(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function k(e,t,n,r,i,u){if(!o.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<u)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function G(e,t,n,r){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-n,2);i<o;++i)e[n+i]=(t&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function M(e,t,n,r){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-n,4);i<o;++i)e[n+i]=t>>>8*(r?i:3-i)&255}function U(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function D(e,t,n,r,i){return i||U(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),z.write(e,t,n,r,23,4),n+4}function j(e,t,n,r,i){return i||U(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),z.write(e,t,n,r,52,8),n+8}function B(e){if(e=V(e).replace(ee,""),e.length<2)return"";for(;e.length%4!=0;)e+="=";return e}function V(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function Y(e){return e<16?"0"+e.toString(16):e.toString(16)}function H(e,t){t=t||Infinity;for(var n,r=e.length,i=null,o=[],u=0;u<r;++u){if((n=e.charCodeAt(u))>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(u+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function X(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function W(e,t){for(var n,r,i,o=[],u=0;u<e.length&&!((t-=2)<0);++u)n=e.charCodeAt(u),r=n>>8,i=n%256,o.push(i),o.push(r);return o}function q(e){return Q.toByteArray(B(e))}function J(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}function K(e){return e!==e}var Q=n(170),z=n(171),$=n(172);t.Buffer=o,t.SlowBuffer=g,t.INSPECT_MAX_BYTES=50,o.TYPED_ARRAY_SUPPORT=e.TYPED_ARRAY_SUPPORT!==undefined?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(t){return!1}}(),t.kMaxLength=r(),o.poolSize=8192,o._augment=function(e){return e.__proto__=o.prototype,e},o.from=function(e,t,n){return u(null,e,t,n)},o.TYPED_ARRAY_SUPPORT&&(o.prototype.__proto__=Uint8Array.prototype,o.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&o[Symbol.species]===o&&Object.defineProperty(o,Symbol.species,{value:null,configurable:!0})),o.alloc=function(e,t,n){return s(null,e,t,n)},o.allocUnsafe=function(e){return c(null,e)},o.allocUnsafeSlow=function(e){return c(null,e)},o.isBuffer=function(e){return!(null==e||!e._isBuffer)},o.compare=function(e,t){if(!o.isBuffer(e)||!o.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,i=0,u=Math.min(n,r);i<u;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:r<n?1:0},o.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},o.concat=function(e,t){if(!$(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return o.alloc(0);var n;if(t===undefined)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=o.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var u=e[n];if(!o.isBuffer(u))throw new TypeError('"list" argument must be an Array of Buffers');u.copy(r,i),i+=u.length}return r},o.byteLength=_,o.prototype._isBuffer=!0,o.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},o.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},o.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},o.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?T(this,0,e):v.apply(this,arguments)},o.prototype.equals=function(e){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===o.compare(this,e)},o.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},o.prototype.compare=function(e,t,n,r,i){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(t===undefined&&(t=0),n===undefined&&(n=e?e.length:0),r===undefined&&(r=0),i===undefined&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,i>>>=0,this===e)return 0;for(var u=i-r,a=n-t,s=Math.min(u,a),c=this.slice(r,i),f=e.slice(t,n),l=0;l<s;++l)if(c[l]!==f[l]){u=c[l],a=f[l];break}return u<a?-1:a<u?1:0},o.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},o.prototype.indexOf=function(e,t,n){return m(this,e,t,n,!0)},o.prototype.lastIndexOf=function(e,t,n){return m(this,e,t,n,!1)},o.prototype.write=function(e,t,n,r){if(t===undefined)r="utf8",n=this.length,t=0;else if(n===undefined&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,r===undefined&&(r="utf8")):(r=n,n=undefined)}var i=this.length-t;if((n===undefined||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return E(this,e,t,n);case"utf8":case"utf-8":return b(this,e,t,n);case"ascii":return w(this,e,t,n);case"latin1":case"binary":return O(this,e,t,n);case"base64":return N(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},o.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Z=4096;o.prototype.slice=function(e,t){var n=this.length;e=~~e,t=t===undefined?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);var r;if(o.TYPED_ARRAY_SUPPORT)r=this.subarray(e,t),r.__proto__=o.prototype;else{var i=t-e;r=new o(i,undefined);for(var u=0;u<i;++u)r[u]=this[u+e]}return r},o.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||P(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r},o.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||P(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},o.prototype.readUInt8=function(e,t){return t||P(e,1,this.length),this[e]},o.prototype.readUInt16LE=function(e,t){return t||P(e,2,this.length),this[e]|this[e+1]<<8},o.prototype.readUInt16BE=function(e,t){return t||P(e,2,this.length),this[e]<<8|this[e+1]},o.prototype.readUInt32LE=function(e,t){return t||P(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},o.prototype.readUInt32BE=function(e,t){return t||P(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},o.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||P(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*t)),r},o.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||P(e,t,this.length);for(var r=t,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*t)),o},o.prototype.readInt8=function(e,t){return t||P(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},o.prototype.readInt16LE=function(e,t){t||P(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},o.prototype.readInt16BE=function(e,t){t||P(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},o.prototype.readInt32LE=function(e,t){return t||P(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},o.prototype.readInt32BE=function(e,t){return t||P(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},o.prototype.readFloatLE=function(e,t){return t||P(e,4,this.length),z.read(this,e,!0,23,4)},o.prototype.readFloatBE=function(e,t){return t||P(e,4,this.length),z.read(this,e,!1,23,4)},o.prototype.readDoubleLE=function(e,t){return t||P(e,8,this.length),z.read(this,e,!0,52,8)},o.prototype.readDoubleBE=function(e,t){return t||P(e,8,this.length),z.read(this,e,!1,52,8)},o.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){k(this,e,t,n,Math.pow(2,8*n)-1,0)}var i=1,o=0;for(this[t]=255&e;++o<n&&(i*=256);)this[t+o]=e/i&255;return t+n},o.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){k(this,e,t,n,Math.pow(2,8*n)-1,0)}var i=n-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+n},o.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,1,255,0),o.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},o.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,2,65535,0),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):G(this,e,t,!0),t+2},o.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,2,65535,0),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):G(this,e,t,!1),t+2},o.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,4,4294967295,0),o.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):M(this,e,t,!0),t+4},o.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,4,4294967295,0),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):M(this,e,t,!1),t+4},o.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);k(this,e,t,n,i-1,-i)}var o=0,u=1,a=0;for(this[t]=255&e;++o<n&&(u*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/u>>0)-a&255;return t+n},o.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);k(this,e,t,n,i-1,-i)}var o=n-1,u=1,a=0;for(this[t+o]=255&e;--o>=0&&(u*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/u>>0)-a&255;return t+n},o.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,1,127,-128),o.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},o.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,2,32767,-32768),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):G(this,e,t,!0),t+2},o.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,2,32767,-32768),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):G(this,e,t,!1),t+2},o.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,4,2147483647,-2147483648),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):M(this,e,t,!0),t+4},o.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||k(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):M(this,e,t,!1),t+4},o.prototype.writeFloatLE=function(e,t,n){return D(this,e,t,!0,n)},o.prototype.writeFloatBE=function(e,t,n){return D(this,e,t,!1,n)},o.prototype.writeDoubleLE=function(e,t,n){return j(this,e,t,!0,n)},o.prototype.writeDoubleBE=function(e,t,n){return j(this,e,t,!1,n)},o.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,u=r-n;if(this===e&&n<t&&t<r)for(i=u-1;i>=0;--i)e[i+t]=this[i+n];else if(u<1e3||!o.TYPED_ARRAY_SUPPORT)for(i=0;i<u;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+u),t);return u},o.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(r!==undefined&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!o.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=n===undefined?this.length:n>>>0,e||(e=0);var u;if("number"==typeof e)for(u=t;u<n;++u)this[u]=e;else{var a=o.isBuffer(e)?e:H(new o(e,r).toString()),s=a.length;for(u=0;u<n-t;++u)this[u+t]=a[u%s]}return this};var ee=/[^+\/0-9A-Za-z-_]/g}).call(t,n(55))},function(e,t,n){"use strict";function r(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function i(e){var t=r(e),n=t[0],i=t[1];return 3*(n+i)/4-i}function o(e,t,n){return 3*(t+n)/4-n}function u(e){var t,n,i=r(e),u=i[0],a=i[1],s=new p(o(e,u,a)),c=0,f=a>0?u-4:u;for(n=0;n<f;n+=4)t=l[e.charCodeAt(n)]<<18|l[e.charCodeAt(n+1)]<<12|l[e.charCodeAt(n+2)]<<6|l[e.charCodeAt(n+3)],s[c++]=t>>16&255,s[c++]=t>>8&255,s[c++]=255&t;return 2===a&&(t=l[e.charCodeAt(n)]<<2|l[e.charCodeAt(n+1)]>>4,s[c++]=255&t),1===a&&(t=l[e.charCodeAt(n)]<<10|l[e.charCodeAt(n+1)]<<4|l[e.charCodeAt(n+2)]>>2,s[c++]=t>>8&255,s[c++]=255&t),s}function a(e){return f[e>>18&63]+f[e>>12&63]+f[e>>6&63]+f[63&e]}function s(e,t,n){for(var r,i=[],o=t;o<n;o+=3)r=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(255&e[o+2]),i.push(a(r));return i.join("")}function c(e){for(var t,n=e.length,r=n%3,i=[],o=0,u=n-r;o<u;o+=16383)i.push(s(e,o,o+16383>u?u:o+16383));return 1===r?(t=e[n-1],i.push(f[t>>2]+f[t<<4&63]+"==")):2===r&&(t=(e[n-2]<<8)+e[n-1],i.push(f[t>>10]+f[t>>4&63]+f[t<<2&63]+"=")),i.join("")}t.byteLength=i,t.toByteArray=u,t.fromByteArray=c;for(var f=[],l=[],p="undefined"!=typeof Uint8Array?Uint8Array:Array,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h=0,g=d.length;h<g;++h)f[h]=d[h],l[d.charCodeAt(h)]=h;l["-".charCodeAt(0)]=62,l["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,n,r,i){var o,u,a=8*i-r-1,s=(1<<a)-1,c=s>>1,f=-7,l=n?i-1:0,p=n?-1:1,d=e[t+l];for(l+=p,o=d&(1<<-f)-1,d>>=-f,f+=a;f>0;o=256*o+e[t+l],l+=p,f-=8);for(u=o&(1<<-f)-1,o>>=-f,f+=r;f>0;u=256*u+e[t+l],l+=p,f-=8);if(0===o)o=1-c;else{if(o===s)return u?NaN:(d?-1:1)*Infinity;u+=Math.pow(2,r),o-=c}return(d?-1:1)*u*Math.pow(2,o-r)},t.write=function(e,t,n,r,i,o){var u,a,s,c=8*o-i-1,f=(1<<c)-1,l=f>>1,p=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:o-1,h=r?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===Infinity?(a=isNaN(t)?1:0,u=f):(u=Math.floor(Math.log(t)/Math.LN2),t*(s=Math.pow(2,-u))<1&&(u--,s*=2),t+=u+l>=1?p/s:p*Math.pow(2,1-l),t*s>=2&&(u++,s/=2),u+l>=f?(a=0,u=f):u+l>=1?(a=(t*s-1)*Math.pow(2,i),u+=l):(a=t*Math.pow(2,l-1)*Math.pow(2,i),u=0));i>=8;e[n+d]=255&a,d+=h,a/=256,i-=8);for(u=u<<i|a,c+=i;c>0;e[n+d]=255&u,d+=h,u/=256,c-=8);e[n+d-h]|=128*g}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";t.__esModule=!0,t.monitorStatServerLoader=t.MonitorStatServerLoader=undefined;var r=n(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(r),o=n(140),u=n(141),a=n(174),s=t.MonitorStatServerLoader=function(){function e(){(0,i["default"])(this,e),this._monitorStatServer=null}return e.prototype.setPublicData=function(){this.get().setPublicData((0,u.getPublicData)())},e.prototype.get=function(){if(null===this._monitorStatServer){var e=o.gbStatAttrNames.monitor;if(!o.gbStat.hasAttr(e)){var t=new a.MonitorStatServer((0,u.getStatServerUrl)());o.gbStat.setAttr(e,t)}this._monitorStatServer=o.gbStat.getAttr(e)}return this._monitorStatServer},e}();t.monitorStatServerLoader=new s},function(e,t,n){"use strict";t.__esModule=!0,t.MonitorStatServer=t.businessFlowKeys=t.monitorStatActions=undefined;var r=n(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(r),o=n(142),u=t.monitorStatActions={request:"request",pagePerformance:"pagePerformance",businessFlow:"businessFlow",initEnv:"initEnv"};t.businessFlowKeys={loadLoginPluginFile:"loadLoginPluginFile",loadLoginPlugin:"loadLoginPlugin",openLoginWnd:"openLoginWnd",initLoginWnd:"initLoginWnd",closeLoginWnd:"closeLoginWnd"},t.MonitorStatServer=function(){function e(t){(0,i["default"])(this,e),this._statServer=new o.StatServer(t,"websdk-monitor2")}return e.prototype.setPublicData=function(e){return this._statServer.setPublicData(e)},e.prototype.stat=function(e){return this._statServer.stat(e)},e.prototype.statRequest=function(e){var t={type:u.request,extData1:e};return this.stat(t)},e.prototype.statPagePerformance=function(e){var t={type:u.pagePerformance,extData2:e};return this.stat(t)},e.prototype.statBusinessFlow=function(e){var t={type:u.businessFlow,extData3:e};return this.stat(t)},e.prototype.statInitEnv=function(e){var t={type:u.initEnv,extData4:e};return this.stat(t)},e}()},,,function(e,t,n){"use strict";function r(){return f===undefined&&(f=!!o.checkIsNewTBC()||(!!o.checkSupportAccountMobileSDK()||(!!o.checkIsOldAndroidMobileClient()||!!o.checkIsXlMac()))),f}function i(){return l===undefined&&(l=o.checkIsNewTBC()?new u.TBCNativeApi:o.checkSupportAccountMobileSDK()?new a.MobileAccountSDKNativeApi:o.checkIsOldAndroidMobileClient()?new s.OldMobileNativeApi:o.checkIsXlMac()?new c.XLMacNativeApi:null),l}Object.defineProperty(t,"__esModule",{value:!0});var o=n(163),u=n(183),a=n(184),s=n(185),c=n(186),f=undefined,l=undefined;t.checkCanUseNativeApi=r,t.getNativeApi=i},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(){return O}function o(e){var t={},n=JSON.parse((0,c["default"])(e)),r=i();for(var o in n){var u=r[o];if(b["default"].isArray(u))for(var a=u,s=Array.isArray(a),f=0,a=s?a:(0,v["default"])(a);;){var l;if(s){if(f>=a.length)break;l=a[f++]}else{if(f=a.next(),f.done)break;l=f.value}var p=l;t[p]=n[o]}else t[u]=n[o]}return t}function u(e){}function a(e,t){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"login",r=xll.PARAMS.SERVER_LOGIN,i=xll.PARAMS.CAN_GSLB&&parent.gslb?parent.gslb.gslbUse(n,r,t):r;return i=b["default"].map(i,function(t){return"https://"+t+"/xluser.core.login/v3/"+e})}t.__esModule=!0,t.BusinessRequest=undefined;var s=n(12),c=r(s),f=n(40),l=r(f),p=n(52),d=r(p),h=n(0),g=r(h),_=n(187),v=r(_);t.json2CookieLoginData=o,t.cookie2JsonLoginData=u,t.getGslb=a;var y=n(104),m=r(y),I=n(139),E=n(24),b=r(E),w=n(49),O={account:"account",error:"error",errorCode:["errcode","blogresult"],error_description:"error_description",isCompressed:"iscompressed",isSetPassWord:"issetpassword",keepAliveMinPeriod:"keepaliveminperiod",keepAlivePeriod:"keepaliveperiod",loginKey:"loginkey",nickName:"usernick",platformVersion:"platformversion",protocolVersion:"protocolversion",secureKey:"securekey",sequenceNo:"sequenceno",sessionID:"sessionid",timestamp:"timestamp",userID:"userid",userName:"usrname",userNewNo:"usernewno",version:"version",vipList:"vipinfo",birthday:"birthday",city:"city",country:"country",imgURL:"imgurl",isSpecialNum:"isspecialnum",isSubAccount:"issubaccount",mobile:"mobile",order:"order",personalSign:"personalsign",province:"province",rank:"rank",registerDate:"registerdate",role:"role",sex:"sex",todayScore:"todayscore"};t.BusinessRequest=function(){function e(){(0,g["default"])(this,e),this._requestServer=new I.RequestServer(m["default"])}return e.prototype._formatRequestParams=function(e,t,n,r,i){var o={};return"POST"===e&&n&&(o.headers=(0,d["default"])({"Content-Type":"application/json"},n)),o.options=(0,d["default"])({stringifyJsonDatas:!0,parseJsonResult:!0},i),o},e.prototype.post=function(e,t,n,r){var i=this;if(b["default"].isArray(e))return new l["default"](function(o,u){return 0==e.length?void u("TIMEOUT"):i.post(e.shift(),t,n,r).then(o)["catch"](function(a){i.post(e,t,n,r).then(o)["catch"](function(e){return u(e)})})});var o=this._formatRequestParams("POST",e,t,n,r);return this._requestServer.post(e,o.headers,n,o.options)},e.prototype.getQrCode=function(e){var t=(0,d["default"])({},(0,w.baseParams2)({format:"json"}),e);return this.post(a("qrcode"),{},t)},e.prototype.getUserInfo=function(e){var t=(0,d["default"])({vasid:"2,14,33,34,35"},(0,w.baseParams2)({format:"json"}),e);return this.post(a("getuserinfo"),{},t)},e.prototype.sessionId2Token=function(e,t){var n={"Content-Type":"application/json; charset=UTF-8"},r={signin_token:t,provider:"access_endpoint"};return this.post(e+"/v1/auth/signin/token",n,r)},e}()},function(e,t,n){e.exports={"default":n(180),__esModule:!0}},function(e,t,n){n(181),e.exports=n(2).Object.setPrototypeOf},function(e,t,n){var r=n(7);r(r.S,"Object",{setPrototypeOf:n(182).set})},function(e,t,n){var r=n(6),i=n(5),o=function(e,t){if(i(e),!r(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,r){try{r=n(19)(Function.call,n(94).f(Object.prototype,"__proto__").set,2),r(e,[]),t=!(e instanceof Array)}catch(i){t=!0}return function(e,n){return o(e,n),t?e.__proto__=n:r(e,n),e}}({},!1):undefined),check:o}},function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=this&&this.__awaiter||function(e,t,n,r){function i(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,o){function u(e){try{s(r.next(e))}catch(t){o(t)}}function a(e){try{s(r["throw"](e))}catch(t){o(t)}}function s(e){e.done?n(e.value):i(e.value).then(u,a)}s((r=r.apply(e,t||[])).next())})},o=this&&this.__generator||function(e,t){function n(e){return function(t){return r([e,t])}}function r(n){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,o&&(u=2&n[0]?o["return"]:n[0]?o["throw"]||((u=o["return"])&&u.call(o),0):o.next)&&!(u=u.call(o,n[1])).done)return u;switch(o=0,u&&(n=[2&n[0],u.value]),n[0]){case 0:case 1:u=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,o=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(u=s.trys,!(u=u.length>0&&u[u.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!u||n[1]>u[0]&&n[1]<u[3])){s.label=n[1];break}if(6===n[0]&&s.label<u[1]){s.label=u[1],u=n;break}if(u&&s.label<u[2]){s.label=u[2],s.ops.push(n);break}u[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(r){n=[6,r],o=0}finally{i=u=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var i,o,u,a,s={label:0,sent:function(){if(1&u[0])throw u[1];return u[1]},trys:[],ops:[]};return a={next:n(0),"throw":n(1),"return":n(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a},u=this&&this.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),i=0,t=0;t<n;t++)for(var o=arguments[t],u=0,a=o.length;u<a;u++,i++)r[i]=o[u];return r};Object.defineProperty(t,"__esModule",{value:!0});var a=n(154),s=n(155),c=function(){function e(){}return Object.defineProperty(e.prototype,"native",{get:function(){return window["native"]},enumerable:!0,configurable:!0}),e.prototype.on=function(e,t){return this["native"].AttachNativeEvent(e,function(){"function"==typeof t&&t()}),this},e.prototype.off=function(e,t){return this},e.prototype.supportCheckFuncion=function(){return"function"==typeof this["native"].CheckNativeFunction},e.prototype.checkFunction=function(e){var t=this;if(this.supportCheckFuncion())return new Promise(function(n){t["native"].CheckNativeFunction(e,function(e){n({exist:0===e})})});var n={error:a.ErrorType.NOT_FOUND};return Promise.reject(n)},e.prototype.call=function(e){for(var t=this,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];return new Promise(function(r,i){var o;(o=t["native"]).CallNativeFunction.apply(o,u([e],n,[function(e,n){var o=t._toNAError(e);o?i(o):r(n)}]))})},e.prototype._toNAError=function(e){var t=null;if("number"==typeof e&&0!=e)switch(e){case 1:t={error:a.ErrorType.UNKNOWN};break;case 2:t={error:a.ErrorType.NOT_FOUND};break;case 3:t={error:a.ErrorType.INVALID_ARGUMENT};break;default:t={error:a.ErrorType.UNKNOWN}}return t},e}();t.TBCNativeCore=c;var f=function(){function e(e){this._native=e}return e.prototype.getTBCData=function(e,t){return i(this,void 0,void 0,function(){var n,r;return o(this,function(i){switch(i.label){case 0:return[4,this._native.call("LoadLoginData",e,t)];case 1:n=i.sent(),r=null;try{"string"==typeof n&&n.length>0&&(r=JSON.parse(n))}catch(o){r=null}return[2,r]}})})},e.prototype.setTBCData=function(e,t,n){return i(this,void 0,void 0,function(){return o(this,function(r){return[2,this._native.call("SaveLoginData",e,t,JSON.stringify(n))]})})},e}(),l=function(e){function t(){var t=e.call(this)||this;return t._nativeCore=new c,t._nativeCoreHelper=new f(t._nativeCore),t}return r(t,e),Object.defineProperty(t.prototype,"nativeCore",{get:function(){return this._nativeCore},enumerable:!0,configurable:!0}),t.prototype.init=function(){return i(this,void 0,void 0,function(){var e=this;return o(this,function(t){return this.nativeCore.on("OnUserLogin",function(){e.emit("login")}),this.nativeCore.on("OnUserLogout",function(){e.emit("logout")}),[2]})})},t.prototype.getAppInfo=function(){return i(this,void 0,void 0,function(){var e,t,n,r,u=this;return o(this,function(a){switch(a.label){case 0:return e={},t=function(){return i(u,void 0,void 0,function(){var t,n;return o(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this._nativeCoreHelper.getTBCData("memory","appinfo")];case 1:return t=r.sent(),t&&(t.appid!==undefined&&null!==t.appid&&(e.appId=t.appid),t["package"]!==undefined&&null!==t["package"]&&(e.noHostAppName=t["package"],e.appName="PC-"+e.noHostAppName)),[3,3];case 2:return n=r.sent(),[3,3];case 3:return[2]}})})},n=function(){return i(u,void 0,void 0,function(){var t,n;return o(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this._nativeCoreHelper.getTBCData("memory","deviceid")];case 1:return t=r.sent(),t&&t.id!==undefined&&null!==t.id&&(e.deviceSign=t.id),[3,3];case 2:return n=r.sent(),[3,3];case 3:return[2]}})})},r=function(){return i(u,void 0,void 0,function(){var t,n;return o(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this.nativeCore.call("GetDeviceID")];case 1:return t=r.sent(),t&&"string"==typeof t&&t.length>0&&(e.peerId=t),[3,3];case 2:return n=r.sent(),[3,3];case 3:return[2]}})})},[4,Promise.all([t(),n(),r()])];case 1:return a.sent(),[2,e]}})})},t.prototype.getSessionUserInfo=function(){return i(this,void 0,void 0,function(){var e,t;return o(this,function(n){switch(n.label){case 0:return[4,this._nativeCoreHelper.getTBCData("memory","userinfo")];case 1:return e=n.sent(),t={},e&&(e.sessionid!=undefined&&(t.sessionId=e.sessionid),e.userid!=undefined&&(t.userId=e.userid),e.loginkey!=undefined&&(t.loginKey=e.loginkey),e.usernick!=undefined&&(t.userNick=e.usernick),e.usrname!=undefined&&(t.userName=e.usrname),e.score!=undefined&&(t.score=e.score),e.upgrade!=undefined&&(t.upgrade=e.upgrade),e.order!=undefined&&(t.order=e.order),e.usernewno!=undefined&&(t.userNewno=e.usernewno),e.usertype!=undefined&&(t.userType=e.usertype),e.state!=undefined&&(t.state=e.state)),[2,t]}})})},t.prototype.setLoginInfo=function(e){return i(this,void 0,void 0,function(){var t;return o(this,function(n){switch(n.label){case 0:return t={userid:"",sessionid:"",uName:"",loginkey:"",xl_autologin:!1,loginType:5},e&&(e.loginKey&&(t.loginkey=e.loginKey),t.xl_autologin=!0===e.autoLogin,e.userId!==undefined&&null!==e.userId&&(t.userid=e.userId),e.sessionId!==undefined&&null!==e.sessionId&&(t.sessionid=e.sessionId),"string"==typeof t.userid&&t.userid.length>0&&"string"==typeof t.sessionid&&t.sessionid.length>0?t.loginType=5:t.loginType="1"),[4,this._nativeCoreHelper.setTBCData("memory","userinfo",t)];case 1:return n.sent(),[2]}})})},t.prototype.showLoginWindow=function(){return i(this,void 0,void 0,function(){return o(this,function(e){return[2,this.nativeCore.call("ShowLoginDlg")]})})},t}(s.NativeApi);t.TBCNativeApi=l},function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=this&&this.__awaiter||function(e,t,n,r){function i(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,o){function u(e){try{s(r.next(e))}catch(t){o(t)}}function a(e){try{s(r["throw"](e))}catch(t){o(t)}}function s(e){e.done?n(e.value):i(e.value).then(u,a)}s((r=r.apply(e,t||[])).next())})},o=this&&this.__generator||function(e,t){function n(e){return function(t){return r([e,t])}}function r(n){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,o&&(u=2&n[0]?o["return"]:n[0]?o["throw"]||((u=o["return"])&&u.call(o),0):o.next)&&!(u=u.call(o,n[1])).done)return u;switch(o=0,u&&(n=[2&n[0],u.value]),n[0]){case 0:case 1:u=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,o=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(u=s.trys,!(u=u.length>0&&u[u.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!u||n[1]>u[0]&&n[1]<u[3])){s.label=n[1];break}if(6===n[0]&&s.label<u[1]){s.label=u[1],u=n;break}if(u&&s.label<u[2]){s.label=u[2],s.ops.push(n);break}u[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(r){n=[6,r],o=0}finally{i=u=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var i,o,u,a,s={label:0,sent:function(){if(1&u[0])throw u[1];return u[1]},trys:[],ops:[]};return a={next:n(0),"throw":n(1),"return":n(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a};Object.defineProperty(t,"__esModule",{value:!0});var u=n(158),a=n(155),s=function(){function e(){this._listenerMap=new Map,this._callbackFunctionEvent=new u.EventEmitter,this._lastCallbackFunctionNameId=0,this._nativeWindow=window,this._injectCallbackObjectIndex=-1}return e.prototype.init=function(){this._native=this._nativeWindow.XLAccountJsBridge,this._nativeWindow.injectCallbackObjects_ffd89||(this._nativeWindow.injectCallbackObjects_ffd89=[]),this._injectCallbackObject={},this._nativeWindow.injectCallbackObjects_ffd89.push(this._injectCallbackObject),this._injectCallbackObjectIndex=this._nativeWindow.injectCallbackObjects_ffd89.length-1},Object.defineProperty(e.prototype,"native",{get:function(){return this._native},enumerable:!0,configurable:!0}),e.prototype.on=function(e,t){var n=this._getNewCallbackFunctionName();return this._injectCallbackObject[n]=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];t.apply(void 0,e)},this._addListener(e,t,n),this["native"].attachListener(e,this._generateInjectObjectCallbackFunctionName(n)),this},e.prototype.off=function(e,t){var n=this._removeListener(e,t);return delete this._injectCallbackObject[n],this["native"].detachListener(e,this._generateInjectObjectCallbackFunctionName(n)),this},e.prototype.supportCheckFuncion=function(){return"function"==typeof this["native"].checkFunction},e.prototype.checkFunction=function(e){var t=null,n=this._getNewCallbackFunctionName();return t=this._getNewCallFunctionPromise(n),this["native"].checkFunction(e,"",this._generateInjectObjectCallbackFunctionName(n)),t},e.prototype.call=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=null,i=this._getNewCallbackFunctionName();r=this._getNewCallFunctionPromise(i);var o=void 0!==t?JSON.stringify(t):"";return this["native"].callFunction(e,o,this._generateInjectObjectCallbackFunctionName(i)),r},e.prototype._addListener=function(e,t,n){var r;this._listenerMap.has(e)?r=this._listenerMap.get(e):(r=[],this._listenerMap.set(e,r)),r.push({functionName:n,listener:t})},e.prototype._removeListener=function(e,t){var n=undefined,r=this._listenerMap.get(e);if(r)for(var i=r.length,o=0;o<i;o++){var u=r[o];if(u&&u.listener===t){n=u.functionName,r.splice(o,1);break}}return n},e.prototype._getNewCallbackFunctionName=function(){var e="";do{e="callback"+(this._lastCallbackFunctionNameId++).toString()}while(this._injectCallbackObject[e]!==undefined);return e},e.prototype._generateInjectObjectCallbackFunctionName=function(e){return"window.injectCallbackObjects_ffd89["+this._injectCallbackObjectIndex+"]."+e},e.prototype._removeCallbackFunctionName=function(e){delete this._injectCallbackObject[e]},e.prototype._getNewCallFunctionPromise=function(e){var t=this;return this._injectCallbackObject[e]=function(n){t._callbackFunctionEvent.emit(e,n),t._removeCallbackFunctionName(e)},new Promise(function(n,r){t._callbackFunctionEvent.once(e,function(e){e&&e.error?r(e):n(e)})})},e}();t.MobileAccountSDKNativeCore=s;var c=function(e){function t(){var t=e.call(this)||this;return t._nativeCore=new s,t}return r(t,e),Object.defineProperty(t.prototype,"nativeCore",{get:function(){return this._nativeCore},enumerable:!0,configurable:!0}),t.prototype.init=function(){return i(this,void 0,void 0,function(){var e=this;return o(this,function(t){return this.nativeCore.init(),this.nativeCore.on("login",function(){e.emit("login")}),this.nativeCore.on("logout",function(){e.emit("logout")}),[2]})})},t.prototype.getAppInfo=function(){return i(this,void 0,void 0,function(){return o(this,function(e){return[2,this.nativeCore.call("getAppInfo")]})})},t.prototype.getSessionUserInfo=function(){return i(this,void 0,void 0,function(){return o(this,function(e){return[2,this.nativeCore.call("getSessionUserInfo")]})})},t.prototype.showLoginWindow=function(){return i(this,void 0,void 0,function(){return o(this,function(e){return[2,this.nativeCore.call("showLoginWindow")]})})},t.prototype.getAccessToken=function(){return i(this,void 0,void 0,function(){return o(this,function(e){return[2,this.nativeCore.call("getAccessToken")]})})},t}(a.NativeApi);t.MobileAccountSDKNativeApi=c},function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=this&&this.__awaiter||function(e,t,n,r){function i(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,o){function u(e){try{s(r.next(e))}catch(t){o(t)}}function a(e){try{s(r["throw"](e))}catch(t){o(t)}}function s(e){e.done?n(e.value):i(e.value).then(u,a)}s((r=r.apply(e,t||[])).next())})},o=this&&this.__generator||function(e,t){function n(e){return function(t){return r([e,t])}}function r(n){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,o&&(u=2&n[0]?o["return"]:n[0]?o["throw"]||((u=o["return"])&&u.call(o),0):o.next)&&!(u=u.call(o,n[1])).done)return u;switch(o=0,u&&(n=[2&n[0],u.value]),n[0]){case 0:case 1:u=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,o=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(u=s.trys,!(u=u.length>0&&u[u.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!u||n[1]>u[0]&&n[1]<u[3])){s.label=n[1];break}if(6===n[0]&&s.label<u[1]){s.label=u[1],u=n;break}if(u&&s.label<u[2]){s.label=u[2],s.ops.push(n);break}u[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(r){n=[6,r],o=0}finally{i=u=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var i,o,u,a,s={label:0,sent:function(){if(1&u[0])throw u[1];return u[1]},trys:[],ops:[]};return a={next:n(0),"throw":n(1),"return":n(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a};Object.defineProperty(t,"__esModule",{value:!0});var u,a=n(158),s=n(84),c=n(154),f=n(155),l=window;!function(e){e[e.ios=1]="ios",e[e.android=2]="android",e[e.unknown=3]="unknown"}(u=t.NativeType||(t.NativeType={}));var p=function(){function e(){this._native=null,this._nativeType=undefined,this._isSdkNative=!1,this._lastCallbackFunctionNameId=0,this._callbackFunctionEvent=new a.EventEmitter}return e.prototype.init=function(){this._initNative()},Object.defineProperty(e.prototype,"native",{get:function(){return this._native},enumerable:!0,configurable:!0}),e.prototype.isSdkNative=function(){return this._isSdkNative},e.prototype.on=function(e,t){return this["native"].AttachNativeEvent(e,function(){"function"==typeof t&&t()}),this},e.prototype.off=function(e,t){return this},e.prototype.supportCheckFuncion=function(){return!1},e.prototype.checkFunction=function(e){var t={error:c.ErrorType.NOT_FOUND};return Promise.reject(t)},e.prototype.call=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e[0],r="undefined"!=typeof e[1]?e[1]:"";return this._callNativeFunction(n,r)},e.prototype._getNewCallbackFunctionName=function(){var e="";do{e="OldClientNativeCallbackFunction"+this._lastCallbackFunctionNameId++}while(l[e]!==undefined);return e},e.prototype._getNewCallFunctionPromise=function(e){var t=this;return l[e]=function(n){t._callbackFunctionEvent.emit(e,n),l[e]=undefined},new Promise(function(n,r){t._callbackFunctionEvent.once(e,function(e){var i=s.parseJson(e),o=t._toNAError(i);o?r(o):n(i)})})},e.prototype._callNativeFunction=function(e,t){void 0===t&&(t="");var n=!1,r=null;if(this._nativeType!==u.ios&&this._nativeType!==u.android||(n=!0),n){var i=this._getNewCallbackFunctionName();switch(r=this._getNewCallFunctionPromise(i),this._nativeType){case u.ios:t=t||{},t=JSON.stringify(Object.assign(t,{actionName:e,jsCallBack:i})),this._native.postMessage(t);break;case u.android:t=t?JSON.stringify(t):t,this._native.sendMessage(e,t,i)}}return r},e.prototype._toNAError=function(e){c.ErrorType.UNKNOWN;return this._isSdkNative?!e||e&&e.errcode?{error:c.ErrorType.UNKNOWN,details:e.errcode}:null:!e||e&&e.internalError?{error:c.ErrorType.UNKNOWN,details:e.internalError}:null},e.prototype._initNative=function(){"undefined"==typeof l.webkit||(l.webkit.messageHandlers,0)||"undefined"==typeof l.webkit.messageHandlers.XLJSWebViewBridgeExport?"undefined"==typeof l.webkit||(l.webkit.messageHandlers,0)||"undefined"==typeof l.webkit.messageHandlers.XLJSWebViewBridge?"undefined"!=typeof l.XLJSWebViewBridgeExport?(this._nativeType=u.android,this._isSdkNative=!0,this._native=l.XLJSWebViewBridgeExport):"undefined"!=typeof l.XLJSWebViewBridge?(this._nativeType=u.android,this._isSdkNative=!1,this._native=l.XLJSWebViewBridge):(this._nativeType=u.unknown,this._isSdkNative=!1,this._native=null):(this._nativeType=u.ios,this._isSdkNative=!1,this._native=l.webkit.messageHandlers.XLJSWebViewBridge):(this._nativeType=u.ios,this._isSdkNative=!0,this._native=l.webkit.messageHandlers.XLJSWebViewBridgeExport)},e}();t.OldMobileNativeCore=p;var d=function(e){function t(){var t=e.call(this)||this;return t._nativeCore=new p,t}return r(t,e),Object.defineProperty(t.prototype,"nativeCore",{get:function(){return this._nativeCore},enumerable:!0,configurable:!0}),t.prototype.init=function(){return i(this,void 0,void 0,function(){return o(this,function(e){return this._nativeCore.init(),[2]})})},t.prototype.getAppInfo=function(){return i(this,void 0,void 0,function(){var e,t,t;return o(this,function(n){switch(n.label){case 0:return e={},this.nativeCore.isSdkNative?[4,this.nativeCore.call("nativeGetUserDeviceInfo","")]:[3,2];case 1:return t=n.sent(),t&&t.appid&&(e.appId=t.appid),[3,4];case 2:return[4,this.nativeCore.call("xlGetAppMetaData","")];case 3:t=n.sent(),t&&t.businessType&&(e.appId=t.businessType),n.label=4;case 4:return[2,e]}})})},t.prototype.getSessionUserInfo=function(){return i(this,void 0,void 0,function(){var e,t,t;return o(this,function(n){switch(n.label){case 0:return e={},this.nativeCore.isSdkNative?[4,this.nativeCore.call("nativeGetUserDeviceInfo","")]:[3,2];case 1:return t=n.sent(),t&&(e.userId=t.userid.toString(),e.sessionId=t.session),[3,4];case 2:return[4,this.nativeCore.call("xlGetUserInfo",{forceLogin:0,source:"sdk"})];case 3:t=n.sent(),t&&1==t.isLogin&&t.userInfo&&(e.userId=t.userInfo.userID.toString(),e.sessionId=t.userInfo.sessionID),n.label=4;case 4:return[2,e]}})})},t}(f.NativeApi);t.OldMobileNativeApi=d},function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=this&&this.__awaiter||function(e,t,n,r){function i(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||(n=Promise))(function(n,o){function u(e){try{s(r.next(e))}catch(t){o(t)}}function a(e){try{s(r["throw"](e))}catch(t){o(t)}}function s(e){e.done?n(e.value):i(e.value).then(u,a)}s((r=r.apply(e,t||[])).next())})},o=this&&this.__generator||function(e,t){function n(e){return function(t){return r([e,t])}}function r(n){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,o&&(u=2&n[0]?o["return"]:n[0]?o["throw"]||((u=o["return"])&&u.call(o),0):o.next)&&!(u=u.call(o,n[1])).done)return u;switch(o=0,u&&(n=[2&n[0],u.value]),n[0]){case 0:case 1:u=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,o=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(u=s.trys,!(u=u.length>0&&u[u.length-1])&&(6===n[0]||2===n[0])){s=0;continue}if(3===n[0]&&(!u||n[1]>u[0]&&n[1]<u[3])){s.label=n[1];break}if(6===n[0]&&s.label<u[1]){s.label=u[1],u=n;break}if(u&&s.label<u[2]){s.label=u[2],s.ops.push(n);break}u[2]&&s.ops.pop(),s.trys.pop();continue}n=t.call(e,s)}catch(r){n=[6,r],o=0}finally{i=u=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var i,o,u,a,s={label:0,sent:function(){if(1&u[0])throw u[1];return u[1]},trys:[],ops:[]};return a={next:n(0),"throw":n(1),"return":n(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a},u=this&&this.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),i=0,t=0;t<n;t++)for(var o=arguments[t],u=0,a=o.length;u<a;u++,i++)r[i]=o[u];return r};Object.defineProperty(t,"__esModule",{value:!0});var a=n(154),s=n(155),c=function(){function e(){}return Object.defineProperty(e.prototype,"native",{get:function(){return window.WebViewJavascriptBridge},enumerable:!0,configurable:!0}),e.prototype.on=function(e,t){return this["native"].addEventListener(e,function(e){"function"==typeof t&&t(e)}),this},e.prototype.off=function(e,t){return this},e.prototype.supportCheckFuncion=function(){return"function"==typeof this["native"].checkFunction},e.prototype.checkFunction=function(e){var t=this;if(this.supportCheckFuncion())return new Promise(function(n){t["native"].checkFunction(e,function(e){n({exist:e&&e.exist})})});var n={error:a.ErrorType.NOT_FOUND};return Promise.reject(n)},e.prototype.call=function(e){for(var t=this,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];return new Promise(function(r,i){var o;(o=t["native"]).callHandler.apply(o,u([e],n,[function(e,n){var o=t._toNAError(e);o?i(o):r(n)}]))})},e.prototype._toNAError=function(e){var t=null;return e&&(t={error:a.ErrorType.UNKNOWN},-999==e.code?t.error=a.ErrorType.NOT_FOUND:t.error=a.ErrorType.UNKNOWN,t.error_description=e.message,t.details=e),t},e}();t.XLMacNativeCore=c;var f=function(e){function t(){var t=e.call(this)||this;return t._nativeCore=new c,t}return r(t,e),Object.defineProperty(t.prototype,"nativeCore",{get:function(){return this._nativeCore},enumerable:!0,configurable:!0}),t.prototype.init=function(){return i(this,void 0,void 0,function(){var e=this;return o(this,function(t){return this.nativeCore.on("userChange",function(t){if(t)switch(t.state){case 0:e.emit("logout");break;case 2:e.emit("login")}}),[2]})})},t.prototype.getAppInfo=function(){return i(this,void 0,void 0,function(){var e,t,n,r=this;return o(this,function(u){switch(u.label){case 0:return e={},t=function(){return i(r,void 0,void 0,function(){var t,n,r,i;return o(this,function(o){switch(o.label){case 0:return o.trys.push([0,2,,3]),[4,this._callGetUserInfo()];case 1:return t=o.sent(),t&&(t.appId!==undefined&&null!==t.appId&&(e.appId=t.appId),t.appName!==undefined&&null!==t.appName&&("string"==typeof t.appName&&(n="PC-",0===(r=t.appName.indexOf(n))&&(e.noHostAppName=t.appName.substr(n.length))),e.appName=t.appName),t.deviceSign!==undefined&&null!==t.deviceSign&&(e.deviceSign=t.deviceSign)),[3,3];case 2:return i=o.sent(),[3,3];case 3:return[2]}})})},n=function(){return i(r,void 0,void 0,function(){var t,n;return o(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this.nativeCore.call("getPlatformInfo")];case 1:return t=r.sent(),t&&"string"==typeof t.peerId&&t.peerId.length>0&&(e.peerId=t.peerId),[3,3];case 2:return n=r.sent(),[3,3];case 3:return[2]}})})},[4,Promise.all([t(),n()])];case 1:return u.sent(),[2,e]}})})},t.prototype.getSessionUserInfo=function(){return i(this,void 0,void 0,function(){var e,t;return o(this,function(n){switch(n.label){case 0:return[4,this._callGetUserInfo()];case 1:return e=n.sent(),t={},e&&(e.userId!==undefined&&(t.userId=e.userId),e.sessionId!==undefined&&(t.sessionId=e.sessionId)),[2,t]}})})},t.prototype.setLoginInfo=function(e){return i(this,void 0,void 0,function(){return o(this,function(t){return[2,this.nativeCore.call("setLoginInfo",e)]})})},t.prototype.showLoginWindow=function(){return i(this,void 0,void 0,function(){return o(this,function(e){return[2,this.nativeCore.call("showLoginWindow")]})})},t.prototype._callGetUserInfo=function(){return i(this,void 0,void 0,function(){return o(this,function(e){return[2,this.nativeCore.call("getUserInfo")]})})},t}(s.NativeApi);t.XLMacNativeApi=f},function(e,t,n){e.exports={"default":n(188),__esModule:!0}},function(e,t,n){n(54),n(50),e.exports=n(189)},function(e,t,n){var r=n(5),i=n(83);e.exports=n(2).getIterator=function(e){var t=i(e);if("function"!=typeof t)throw TypeError(e+" is not iterable!");return r(t.call(e))}},function(e,t,n){"use strict";function r(e){return e.toLowerCase().replace(/[_\-]/g,"")}function i(e,t){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{not_allowd:{DEBUG:1,VERSION:1,ALLOW_ACCOUNT_REGISTER_IDS:1,IFRAME_ID:1},except:",UI_STYLE,LOGIN_ID,IFRAME_STYLE"};if("object"!==(void 0===t?"undefined":(0,u["default"])(t)))throw new Error("参数类型错误");var i=n.not_allowd,o=n.except,s=a(e),c=a(t),f={};for(var l in e)if(s(l)&&!(l in i)){var p=r(l),d=e[l];f[p]={origin_key:l,value:d,type:void 0===d?"undefined":(0,u["default"])(d)}}for(var h in t)if(c(h)){var g=r(h);if(g in f){var _=f[g].type,v=t[h],y=f[g].origin_key;if("boolean"===_&&-1===o.indexOf(y)&&(v=!!v),_!==(void 0===v?"undefined":(0,u["default"])(v))&&-1==o.indexOf(y))throw new Error("config key("+h+") error, type not match");"LOGIN_ID"===y&&(e.SET_LOGIN_ID=!0),"CHANGE_SIZE_FUNC"===y&&(e.SET_CHANGE_SIZE_FUNC=!0),e[y]=v}}if(!e.SET_LOGIN_ID)throw new Error("没有设置LOGIN_ID");return e}t.__esModule=!0;var o=n(15),u=function(e){return e&&e.__esModule?e:{"default":e}}(o);t.translateLower=r,t.initConfig=i;var a=function(e){return function(t){return e.hasOwnProperty(t)}}},,,function(e,t,n){var r=n(6);e.exports=function(e,t){if(!r(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},,,function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function o(){if("string"==typeof H.CONFIG.POPUP_MASK)return(0,V.id)(H.CONFIG.POPUP_MASK).style.display="",!1;H.CONFIG.POPUP_MASK="xl_login_mask_"+W["default"].randString(6);var e=(0,V.id)(H.CONFIG.POPUP_MASK);if(e)return e;e=document.createElement("div"),e.id=H.CONFIG.POPUP_MASK;var t=e.style;t.opacity="0.4",t.filter="alpha(opacity=40)",t.backgroundColor="black",t.position=oe?"absolute":"fixed",t.left="0",t.top="0",t.bottom="0",t.right="0",t.zIndex="999999",document.body.appendChild(e)}function u(){"popup"===H.CONFIG.UI_TYPE&&a()}function a(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:H.CONFIG.IFRAME_ID;(0,V.id)(e).style.display="none",s()}function s(){"string"==typeof H.CONFIG.POPUP_MASK&&((0,V.id)(H.CONFIG.POPUP_MASK).style.display="none")}function c(e){e?e.style.display="":xlQuickLogin.getLoginIframe(document.body),H.CONFIG.POPUP_MASK&&o()}function f(e){return function(t){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0,r=H.CONFIG[e],i=0;return n>0?(r[n-1]=t,i=n):i=r.push(t),i}}function l(e){return function(t){W["default"].setCookie(e,t,"",H.CONFIG.DOMAIN)}}function p(e){var t=e;if("string"==typeof e&&e.length>0){var n=e.length-1,r=e.indexOf("?"),i=e.indexOf("#");n=r>-1?Math.min(r-1,n):n,n=i>-1?Math.min(i-1,n):n,n=n>-1?n:e.length-1,t=e.substring(0,n+1)}return t}function d(e){return function(){return W["default"].getCookie(e)}}var h=n(52),g=i(h),_=n(40),v=i(_),y=n(150),m=i(y),I=n(151),E=i(I),b=n(145),w=i(b),O=n(146),N=i(O),S=n(12),C=i(S),T=n(0),A=i(T);n(166),n(96);var L=n(158),R=n(84),x=n(163),F=n(177),P=n(23),k=r(P),G=n(164),M=r(G),U=n(60),D=n(123),j=n(38),B=n(197),V=n(24),Y=i(V),H=n(97),X=n(28),W=i(X),q=n(152),J=n(49),K=n(77),Q=i(K),z=n(214),$=n(178),Z=n(173),ee=n(51),te=n(190);k.init(k.gbEnvTypes.outsideIframe);var ne=new $.BusinessRequest,re=null,ie=new j.GBHelper(k.gbAttrNames.clientFeatureApi);ie.setTarget(re),(0,ee.checkDomainAllowed)(),window.HASH=n.h,window.xlQuickLogin={},window.store=Q["default"];var oe=!(!("ActiveXObject"in window)||"XMLHttpRequest"in window),ue="file:"==location.protocol;U.gbConfig.setSDKVersion("v4.5.11");var ae=null,se=function(){function e(){(0,A["default"])(this,e)}return e.prototype.isLogined=function(){var e=W["default"].getCookie();return W["default"].isSessionid(e.sessionid)&&e.userid&&e.userid>0},e.prototype.ping=function(e,t){var n=void 0,r=null;if("function"==typeof e){r=e;var i=W["default"].getCookie();n={userID:i.userid,sessionID:i.sessionid}}else n=e,r=t;q.req.ping(n,r)},e.prototype.checkLoginValid=function(){var e=this;if(e.isLogined()){var t=function(e,t){if("1"==e||"5"==e||"3202"==e||"3203"==e||"3204"==e||"3207"==e){"5"==e?(W["default"].delCookie("sessionid",H.CONFIG.DOMAIN),window.location.reload()):q.req.logout(function(){H.CONFIG.LOGOUT_FUNC()});var n="3202"==e?"kickout":"timeout";try{!H.CONFIG.IS_SYNC_PC&&window["native"]&&window["native"].CallNativeFunction("SaveLoginData","memory","userinfo",(0,C["default"])({userid:"",sessionid:"",loginType:n}),function(e,t){})}catch(r){}}};(0,x.checkIsTBC)()&&H.CONFIG.IS_SYNC_PC&&window["native"]?window["native"].CallNativeFunction("FireLoginEvent","checkLoginValid",function(n,r){1===n&&e.ping(t)}):(0,V.mThunder)()&&re||e.ping(t)}},e.prototype.appidstack=function(){var e="appidstack";window.APPID=H.CONFIG.LOGIN_ID,window.APPNAME=H.CONFIG.APP_NAME||location.host;var t=H.CONFIG.LOGIN_ID,n=W["default"].getCookie(e),r=""==n||n==undefined?[]:n.split(","),i=r.slice().pop();r.length>=5&&r.shift(),i!=t&&(r.push(t),n=r.join(","),W["default"].setCookie(e,n,!1,H.CONFIG.DOMAIN))},e.prototype.ispopup=function(){return"popup"===H.CONFIG.UI_TYPE},e.prototype.domainError=function(){if(!ue&&!0!==H.CONFIG.SET_ROOT_DOMAIN)throw new Error("SET_ROOT_DOMAINB必须设置为true")},e.prototype.innerQuickLogin=function(){return window.frames[H.CONFIG.IFRAME_ID].xlQuickLogin},e.prototype.checkDevID=function(){return!!W["default"].getCookie("deviceid")},e}(),ce=new se,fe=!1,le={},pe=new j.GBHelper(D.gbAttrNames.otherInfo),de=function(){function e(){(0,A["default"])(this,e),this._onIFrameLoad=null,this._onIFrameClose=null,this._messageFunction=null,this._iframe=null}return e.prototype.setConfig=function(e){this._onIFrameLoad=e&&"function"==typeof e.onIFrameLoad?e.onIFrameLoad:null,this._onIFrameClose=e&&"function"==typeof e.onIFrameClose?e.onIFrameClose:null},e.prototype.load=function(e){var t=this;this._iframe=this._generateIFrame(e),window.addEventListener("message",this._messageFunction),this._afterIFrameLoad(this._iframe,function(e){"function"==typeof t._onIFrameLoad&&t._onIFrameLoad(e)}),document.body.appendChild(this._iframe)},e.prototype.close=function(e){this._close(e)},e.prototype.getIFrameContentWindow=function(){return this._iframe.contentWindow},e.prototype._close=function(e){window.removeEventListener("message",this._messageFunction),this._messageFunction=null,document.body.removeChild(this._iframe),"function"==typeof this._onIFrameClose&&this._onIFrameClose(e)},e.prototype._generateIFrame=function(e){var t=document.createElement("iframe"),n={src:e,id:"",display:"none"};for(var r in n)t.setAttribute(r,n[r]);return t.style.cssText=this._cssObjecttoCssText({width:"1px",height:"1px"}),t},e.prototype._afterIFrameLoad=function(e,t){e.onload=t,e.onreadystatechange=function(n){"complete"==e.readyState&&t(n)}},e.prototype._cssObjecttoCssText=function(e){var t=[];for(var n in e)t.push(n+":"+e[n]);return t.join(";")},e}(),he=function(e){function t(){(0,A["default"])(this,t);var n=(0,w["default"])(this,e.call(this));return n._loginStateInited=!1,n._iFrameWrap=null,n._iFrameWrap=new de,n}return(0,N["default"])(t,e),t.prototype.isLoginStateInited=function(){return this._loginStateInited},t.prototype.callLoginStateInitedFuns=function(){this._loginStateInited=!0,Y["default"].forEach(H.CONFIG.LOGIN_STATE_INITED_FUNS,function(e){return e()}),this.emit("after-login-state-inited")},t.prototype.login=function(){function e(e){return t.apply(this,arguments)}var t=(0,E["default"])(m["default"].mark(function n(e){return m["default"].wrap(function(t){for(;;)switch(t.prev=t.next){case 0:e=e||{},Y["default"].forEach(H.CONFIG.LOGIN_EXT_FUNS,function(t){return t(e.data)}),W["default"].delVerifies(),H.CONFIG.SET_ROOT_DOMAIN?xlQuickLogin.loginFunc(e.data):window.location.href=H.CONFIG.LOGIN_SUCCESS_URL;case 4:case"end":return t.stop()}},n,this)}));return e}(),t.prototype._oauthSignOut=function(){var e=this;return new v["default"](function(t){var n="i."+H.CONFIG.DOMAIN+"/xluser/oauth.html",r="https://"+n+"?sign_out=true&redirect_uri="+encodeURIComponent(window.location.href),i=!1;e._iFrameWrap.setConfig({onIFrameLoad:function(t){try{if(!i){var n=new URL(r),o=e._iFrameWrap.getIFrameContentWindow(),u=o.location;u.host+u.pathname===n.host+n.pathname&&(i=!0,e._iFrameWrap.close())}}catch(a){}},onIFrameClose:function(e){t()}}),setTimeout(function(){i||(i=!0,e._iFrameWrap.close())},5e3),e._iFrameWrap.load(r)})},t.prototype.logout=function(){function e(e){return t.apply(this,arguments)}var t=(0,E["default"])(m["default"].mark(function n(e){var t,r=this;return m["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e=e||{},"function"==typeof e.logoutFunc&&(H.CONFIG.LOGOUT_FUNC=e.logoutFunc),t=function(){var e=(0,E["default"])(m["default"].mark(function t(){return m["default"].wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new v["default"](function(e){xlQuickLogin.isLogined()?re?(M.deleteCookieUserInfo(),e()):q.req.logout(function(){e()}):(M.deleteCookieUserInfo(),e())}));case 1:case"end":return e.stop()}},t,r)}));return function(){return e.apply(this,arguments)}}(),n.next=5,t();case 5:if(e.disableOauthSignOut,!0){n.next=14;break}return n.prev=6,n.next=9,this._oauthSignOut();case 9:n.next=14;break;case 11:n.prev=11,n.t0=n["catch"](6);case 14:xlQuickLogin.logoutExtFun(),H.CONFIG.LOGOUT_FUNC();case 16:case"end":return n.stop()}},n,this,[[6,11]])}));return e}(),t.prototype.initGBOtherInfo=function(){var e=void 0;e=Q["default"].get("showLoginWndSource"),e=(0,R.forceGetTypeValue)("string",e,"NONE"),pe.setAttr(D.gbOtherInfoAttrNames.showLoginWndSource,e)},t}(L.EventEmitter),ge=new he;new j.GBHelper(k.gbAttrNames.innerQuickLogin).setTarget(ge),xlQuickLogin={version:U.gbConfig.getSDKVersion(),init:function(e){if(!0!==fe){var t=!0,n=(0,ee.checkDomainAllowed)(),r=(n[0],n[1]);H.CONFIG.DOMAIN=r,(0,te.initConfig)(H.CONFIG,e),H.CONFIG.isDOMAIN="e1b7d35f24218c6a77b0bf6f7ed6c5f4"==md5(H.CONFIG.DOMAIN),H.CONFIG.UI_TYPE="popup"===H.CONFIG.UI_THEME?"popup":H.CONFIG.UI_TYPE,H.CONFIG.ALL_HTTPS="https:"===location.protocol,"string"==typeof H.CONFIG.ANALYSIS_SERVER&&0===H.CONFIG.ANALYSIS_SERVER.length&&(H.CONFIG.isDOMAIN?H.CONFIG.ANALYSIS_SERVER="analysis-acc-ssl."+H.CONFIG.DOMAIN:H.CONFIG.ANALYSIS_SERVER=H.CONFIG.SERVER_LOGIN[0]),U.gbConfig.setAppId(H.CONFIG.LOGIN_ID),U.gbConfig.setAppName(H.CONFIG.APP_NAME),U.gbConfig.setHostCategoryAppName((0,J.getHostCategoryAppName)()),U.gbConfig.setAppVersion(H.CONFIG.APP_VERSION),U.gbConfig.setHL(H.CONFIG.HL),U.gbConfig.setAnalysisServer(H.CONFIG.ANALYSIS_SERVER),U.gbConfig.setSyncPC(H.CONFIG.IS_SYNC_PC),ge.initGBOtherInfo(),ae=Z.monitorStatServerLoader.get(),Z.monitorStatServerLoader.setPublicData(),ce.appidstack();var i=ue?"./":"//"+H.CONFIG.STATIC_DOMAIN;if(!window.Xreport){window.Xreport=[];var o=ue?i:i+"/login/lib/",u=o+"xreport.js?v=************";W["default"].loadScript(u)}if(H.CONFIG.CAN_GSLB&&!window.gslb){var s=ue?i:i+"/login/2.5/",c=s+"gslb.min.js?v=************";W["default"].loadScript(c,function(){H.CONFIG.CAN_GSLB&&window.gslb.fetch({host:[H.CONFIG.SERVER_LOGIN[0]]})})}H.CONFIG.CLIENT_FEATURE&&(V.isXlMac?H.CONFIG.IS_SYNC_MAC=!0:(0,x.checkIsNewTBC)()?H.CONFIG.IS_SYNC_PC=!0:((0,x.checkSupportAccountMobileSDK)()||(0,V.mThunder)())&&(H.CONFIG.IS_SYNC_APP=!0));var f=!1,l=!1,p=undefined;if(H.CONFIG.IS_SYNC_PC){var d=!1;if(V.isXl9)d=!0;else if(window.external)try{var h=window.external;if(h.GetUserID&&"unknown"==typeof h.JsCall){var g=h.JsCall("GETVERSIONBUILD");d=g&&g>0}}catch(m){}d&&(p="xl9api.min.js")}if(V.isXlMac&&H.CONFIG.IS_SYNC_MAC?(t=!1,l=!0,f=!0):(0,x.checkIsNewTBC)()&&H.CONFIG.IS_SYNC_PC?(t=!1,l=!0,f=!0):(0,x.checkSupportAccountMobileSDK)()&&H.CONFIG.IS_SYNC_APP?(t=!1,l=!0,f=!0):(0,V.mThunder)()&&H.CONFIG.IS_SYNC_APP&&(p="xlmapi.min.js"),p){var _=i+"/login/2.5/"+p+"?v=************";W["default"].loadScript(_)}H.CONFIG.CLIENT_FEATURE=l&&H.CONFIG.CLIENT_FEATURE,U.gbConfig.setClientFeature(H.CONFIG.CLIENT_FEATURE);var v=(H.CONFIG.ISFRAUD,undefined);if((f||U.gbConfig.getClientFeature())&&(0,F.checkCanUseNativeApi)()?(re=new B.ClientFeatureApi({util:W["default"],innerXLQuickLogin:ge,xll:xlQuickLogin}),ie.setTarget(re),re.init()["catch"](function(){v&&W["default"].loadScript(v)})["finally"](function(){ge.callLoginStateInitedFuns()})):v&&W["default"].loadScript(v),1!==H.CONFIG.CHANGE_SIZE_FUNC.length)throw new Error("CHANGE_SIZE_FUNC 必须有一个参数接收结果");if("popup"===H.CONFIG.UI_TYPE&&!H.CONFIG.SET_ROOT_DOMAIN)throw new Error("适用弹窗主题，SET_ROOT_DOMAIN必须设置为true");H.CONFIG.IFRAME_STYLE||("default"===H.CONFIG.UI_THEME?"popup"===H.CONFIG.UI_TYPE&&(H.CONFIG.IFRAME_STYLE={width:"406px",height:"466px",zIndex:"1999999",position:"fixed",top:"50%",left:"50%",marginLeft:"-203px",marginTop:"-233px"}):"embed"===H.CONFIG.UI_THEME?H.CONFIG.IFRAME_STYLE={width:"420px",height:"479px"}:"popup"===H.CONFIG.UI_THEME&&(H.CONFIG.IFRAME_STYLE={width:"420px",height:"395px",position:"fixed",left:"50%",top:"50%",marginLeft:"-210px",marginTop:"-250px",zIndex:"1000000"})),fe=!0;for(var y in H.CONFIG)",LOGIN_ID,LOGIN_TYPES,REGISTER_TYPES,UI_THEME,UI_STYLE,DEFUALT_UI,APP_NAME,SERVER_LOGIN,APP_VERSION,HL,".indexOf(","+y+",")>=0&&(le[y]=H.CONFIG[y]);le.HL=H.CONFIG.HL,le.SHOW_GSM=H.CONFIG.SHOW_GSM,"popup"===H.CONFIG.UI_TYPE&&H.CONFIG.POPUP_PRELOAD&&(this.getLoginIframe(document.body),a()),ce.checkLoginValid(),t&&ge.callLoginStateInitedFuns()}},getLoginIframe:function(e){if(!fe)throw new Error("please init first: xlQuickLogin.init()");if(!e)throw new Error("param error");var t,n,r,i,o=document.createElement("iframe"),u={},a=[],s="",c="";if(H.CONFIG.CAN_QR_LOGIN)if(H.CONFIG.LOGIN_TYPES){for(var f=!0,l=0,d=H.CONFIG.LOGIN_TYPES.length;l<d;l++){var h=H.CONFIG.LOGIN_TYPES[t];if("qrCode"===h){f=!1;break}}f&&H.CONFIG.LOGIN_TYPES.push("qrCode")}else H.CONFIG.LOGIN_TYPES=["qrCode"];if(H.CONFIG.LOGIN_TYPES)for(t in H.CONFIG.LOGIN_TYPES){var g=H.CONFIG.LOGIN_TYPES[t];"account"===g?c+="1":"mobile"===g?c+="2":"client"===g?c+="3":"qrCode"===g&&(c+="4")}if(H.CONFIG.REGISTER_TYPES){if(H.CONFIG.REGISTER_TYPES.length>2)throw new Error("最多只能填2种注册方式");for(t in H.CONFIG.REGISTER_TYPES)"mail"===H.CONFIG.REGISTER_TYPES[t]?s+="1":"mobile"===H.CONFIG.REGISTER_TYPES[t]?s+="2":"account"===H.CONFIG.REGISTER_TYPES[t]?s+="3":"app"===H.CONFIG.REGISTER_TYPES[t]?s+="4":"mobile_with_password"===H.CONFIG.REGISTER_TYPES[t]&&(s+="5")}if(u.r_d=H.CONFIG.SET_ROOT_DOMAIN?1:0,u.use_cdn=H.CONFIG.USE_CDN?1:0,H.CONFIG.SET_ROOT_DOMAIN)for(t in H.CONFIG)"LOGIN_TYPES"!==t?"REGISTER_TYPES"!==t?",LOGIN_ID,ALL_HTTPS,SET_ROOT_DOMAIN,AUTO_LOGIN_EXPIRE_TIME,LOGIN_TYPES,REGISTER_TYPES,UI_THEME,UI_STYLE,LOGIN_SUCCESS_URL,REGISTER_SUCCESS_URL,DEFUALT_BACKGROUND,DEFUALT_UI,LOGIN_BUTTON_TEXT,REGISTER_BUTTON_TEXT,THIRD_LOGIN_DISPLAY,ALERT_ERROR,THIRD_LOGIN_TARGET_PARENT,THIRD_LOGIN_GROUP,THIRD_LOGIN_DEFAULT,APP_NAME,UI_TYPE,UI_TEXT,DEFAULT_AVATAR,SERVER_LOGIN,IS_HIT_BLOCK,LOGIN_FAIL_FUNS,STATIC_DOMAIN,CAN_GSLB,SERVER_XLUSER,REGISTER_CHECKED,isDOMAIN,REGISTER_WITH_LOGIN,MESSAGE_CHANNEL_SERVER,IS_SYNC_PC,ANALYSIS_SERVER,MESSAGE_CHANNEL_GSLB_QURERY_HOST_KEY,CLIENT_FEATURE,".indexOf(","+t+",")>=0&&(le[t]=H.CONFIG[t]):le[t]=s:le[t]=c;else u.l_id=H.CONFIG.LOGIN_ID,u.l_aver=H.CONFIG.APP_VERSION||"",u.a_t=H.CONFIG.AUTO_LOGIN_EXPIRE_TIME?H.CONFIG.AUTO_LOGIN_EXPIRE_TIME:0,u.a_h=H.CONFIG.ALL_HTTPS?1:0,u.a_e=H.CONFIG.ALERT_ERROR?1:0,u.r_d=H.CONFIG.SET_ROOT_DOMAIN?1:0,u.d_ui=H.CONFIG.DEFUALT_UI,u.tl_display=H.CONFIG.THIRD_LOGIN_DISPLAY?1:0,u.theme=H.CONFIG.UI_THEME,u.style=H.CONFIG.UI_STYLE,u.l_b_text=H.CONFIG.LOGIN_BUTTON_TEXT?H.CONFIG.LOGIN_BUTTON_TEXT:"",u.r_b_text=H.CONFIG.REGISTER_BUTTON_TEXT?H.CONFIG.REGISTER_BUTTON_TEXT:"",u.ls_url=encodeURIComponent(H.CONFIG.LOGIN_SUCCESS_URL),u.rs_url=encodeURIComponent(H.CONFIG.REGISTER_SUCCESS_URL),u.l_types=c,u.r_types=s,u.df_act=H.CONFIG.DEFAULT_ACCOUNT,u.tl_tp=H.CONFIG.THIRD_LOGIN_TARGET_PARENT,u.tl_def=encodeURIComponent(H.CONFIG.THIRD_LOGIN_DEFAULT),u.l_nid=H.CONFIG.APP_NAME||"",u.tl_gp=encodeURIComponent(H.CONFIG.THIRD_LOGIN_GROUP),u.u_tp=H.CONFIG.UI_TYPE,u.u_tt=H.CONFIG.UI_TEXT,u.d_at=encodeURIComponent(H.CONFIG.DEFAULT_AVATAR),u.s_ls=encodeURIComponent(H.CONFIG.SERVER_LOGIN),u.h_bk=encodeURIComponent(H.CONFIG.IS_HIT_BLOCK),u.s_dn=encodeURIComponent(H.CONFIG.STATIC_DOMAIN),u.c_gslb=H.CONFIG.CAN_GSLB,u.s_us=encodeURIComponent(H.CONFIG.SERVER_XLUSER),u.hl=H.CONFIG.HL;for(t in u)a.push(t+"="+u[t]);n=location.protocol+"//i."+H.CONFIG.DOMAIN+"/login/index.html",H.CONFIG.isDOMAIN||(n=location.protocol+"//"+H.CONFIG.STATIC_DOMAIN+"/login/index.html"),ue&&(n="login/index.html"),n=n+"?"+a.join("&")+"&v=************&date="+(0,V.dateToDateString)(new Date)+"&refurl="+encodeURIComponent(p(document.location.href)),o.src=n,o.id=H.CONFIG.IFRAME_ID,o.name=H.CONFIG.IFRAME_ID,o.frameBorder=0,o.scrolling="no",le.onload=H.CONFIG.ONLOAD,1==H.CONFIG.IFRAME_ALLOW_TRANSPARENCY&&(o.allowTransparency=!0),r=o.style;for(t in H.CONFIG.IFRAME_STYLE)r[t]=H.CONFIG.IFRAME_STYLE[t];i="string"==typeof e?(0,V.id)(e):e,i.appendChild(o),"popup"===H.CONFIG.UI_TYPE&&W["default"].bind(document,"keypress.xl",function(e){"27"==e.keyCode&&xlQuickLogin.closeFunc()})},popup:function(){var e=this,t=(0,V.id)(H.CONFIG.IFRAME_ID);if("popup"!==H.CONFIG.UI_TYPE)throw new Error("该函数只适用弹窗主题");if(!0!==H.CONFIG.SET_ROOT_DOMAIN)throw new Error("使用弹窗主题，SET_ROOT_DOMAINB必须设置为true");if(this.isLogined())throw new Error("已有登录态，无法弹出登录框");!function(){var n=(0,E["default"])(m["default"].mark(function r(){return m["default"].wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!U.gbConfig.getClientFeature()||!re){e.next=12;break}return e.prev=1,e.next=4,re.showLoginWindow();case 4:e.next=10;break;case 6:e.prev=6,e.t0=e["catch"](1),c(t);case 10:e.next=13;break;case 12:c(t);case 13:case"end":return e.stop()}},r,e,[[1,6]])}));return function(){return n.apply(this,arguments)}}()()},bindMobile:function(){if(!0!==H.CONFIG.SET_ROOT_DOMAIN)throw new Error("使用绑定手机，SET_ROOT_DOMAINB必须设置为true");if(!this.isLogined())throw new Error("无登录态，无法绑定手机");var e=(0,V.id)("bindMobileIframe");e||(H.CONFIG.IFRAME_ID="bindMobileIframe",H.CONFIG.IFRAME_STYLE={width:"420px",height:"505px",position:oe?"absolute":"fixed",left:"50%",top:"50%",marginLeft:"-210px",marginTop:"-250px",zIndex:"1000000"},H.CONFIG.UI_THEME="bindMobile"),c(e)},bindSuccFunc:function(){H.CONFIG.BIND_SUCCESS_FUNC()},isLogined:function(){return ce.isLogined()},logout:function(e,t){ge.logout((0,g["default"])({logoutFunc:e},t))},nickname:d("usernick"),getVerifyKey:d("VERIFY_KEY"),loginFunc:function(e){H.CONFIG.LOGIN_SUCCESS_FUNC(e),u()},registerFunc:function(){H.CONFIG.REGISTER_SUCCESS_FUNC(),u()},uiChangeFunc:function(e){H.CONFIG.ON_UI_CHANGE(e)},closeFunc:function(){H.CONFIG.POPUP_ALLOW_CLOSE&&"popup"==H.CONFIG.UI_TYPE&&(a(),H.CONFIG.POPUP_CLOSE_FUNC())},closeBindFunc:function(){a("bindMobileIframe")},showUI:function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;if(ce.domainError(),"login"!==e&&"register"!==e&&"bindMobile"!==e)throw new Error("params error");if("register"===e&&H.CONFIG.REGISTER_TYPES===[])throw new Error("你的配置不支持注册");if("login"===e&&H.CONFIG.LOGIN_TYPES===[])throw new Error("你的配置不支持登录");ce.innerQuickLogin().showUI(e,t)},PARAMS:le,changeSizeFunc:function(e){H.CONFIG.SET_CHANGE_SIZE_FUNC&&H.CONFIG.CHANGE_SIZE_FUNC(e)},setLoginExt:f("LOGIN_EXT_FUNS"),setLogoutExt:f("LOGOUT_EXT_FUNS"),setInitedFuns:f("INITED_FUNS"),setLoginStateInitedFun:f("LOGIN_STATE_INITED_FUNS"),setUiCompletedFuns:f("UI_COMPLETED_EXT_FUNS"),setClientLoginFun:f("CLIENT_LOGIN_FUNS"),setLoginFailExt:f("LOGIN_FAIL_FUNS"),loginExtFun:function(e){ge.login({data:e})},logoutExtFun:function(){Y["default"].forEach(H.CONFIG.LOGOUT_EXT_FUNS,function(e){return e()})},loginFail:function(e,t){Y["default"].forEach(H.CONFIG.LOGIN_FAIL_FUNS,function(n){return n(e,t)})},setSessionId:l("sessionid"),setPeerId:l("peerid"),setUserId:l("userid"),setDeviceId:l("deviceid"),setUserInfo:function(e){var t;for(t in e)-1!==",isvip,usernick,upgrade,score,order,usrname,usernewno,userid,usertype,state,sessionid,loginkey".indexOf(t)&&W["default"].setCookie(t,e[t],"",H.CONFIG.DOMAIN)},isClientLogin:!1,setClientLogin:function(e){return H.CONFIG.CLIENTLOGIN=!!e},getClientLoginFun:function(e){Y["default"].forEach(H.CONFIG.CLIENT_LOGIN_FUNS,function(t){return t(e)})},getInitedFuns:function(){return H.CONFIG.INITED_FUNS},initedFuns:function(){Y["default"].forEach(H.CONFIG.INITED_FUNS,function(e){return e()})},setBackgroud:function(e){ce.domainError(),ce.innerQuickLogin().setBackgroud(e)},setSafePhoneLogin:function(e){ce.domainError(),ce.innerQuickLogin().setSafePhoneLogin(e)},hitLibraryAndUpdatePassword:function(e,t){ce.domainError(),ce.innerQuickLogin().hitLibraryAndUpdatePassword(e,t)},clientLoginFail:function(e){W["default"].delVerifies(),ce.domainError(),ce.innerQuickLogin().clientLoginFail(e)},showError:function(e){ce.innerQuickLogin().showError(e)},jsonp:function(e,t,n){var r=(0,J.baseParams)(t);q.req.jsonp(e,r,function(e){n&&n(e)})},isMobile:function(){return V.isMobile},isXlMB:function(){return V.isXlMB},isXlPC:function(){return V.isXlPC},xl9Client:function(){return V.isXl9},xlxClient:function(){return V.isXlx},isXlMac:function(){return V.isXlMac},mThunder:V.mThunder,sendMessageToApp:function(e,t,n){},xdasClient:function(){return(0,x.checkIsXdas)()},isTbcClient:function(){return(0,x.checkIsTBC)()},getUserInfo:function(e,t){q.req.getuserinfo(e,t)},jumplogin:function(e,t){q.req.jumplogin(e,t)},ping:ce.ping,loginFetch:function(e){},checkLoginValid:ce.checkLoginValid,toast:function(e){H.CONFIG.TOAST(e)},getUserTokenInfo:function(e){var t=null,n=!0,r=!1,i=W["default"].getCookie("sessionid"),o=W["default"].getCookie("userid");if(W["default"].isSessionid(i)&&"string"==typeof o&&o.length>0){var u=window.localStorage.getItem("user_token");if("string"==typeof u&&u.length>0){try{t=JSON.parse(u)}catch(a){}t&&t.user_id===o&&"number"==typeof t.expired&&t.expired>(new Date).getTime()&&(r=!0)}r||(n=!1,ne.sessionId2Token("https://"+H.CONFIG.SERVER_LOGIN[0],i).then(function(n){var r=n.data;r&&"number"==typeof r.expires_in&&(t={token_type:r.token_type,access_token:r.access_token,user_id:r.user_id,expired:new Date(Date.now()+1e3*(r.expires_in-60)).getTime()},window.localStorage.setItem("user_token",(0,C["default"])(t))),e&&e(t)})["catch"](function(n){e&&e(t)}))}n&&e&&e(t)},setXbaseLogoutUrl:function(e,t){(0,x.checkIsTBC)()&&H.CONFIG.IS_SYNC_PC&&window["native"]?window["native"].CallNativeFunction("FireLoginEvent","setXbaseLogoutUrl",(0,C["default"])({clientId:e,url:t}),function(e,t){}):V.isXlMac&&H.CONFIG.IS_SYNC_MAC&&window.WebViewJavascriptBridge.callHandler("setSignOutURI",{clientId:e,signOutURI:t},function(e){})}},Y["default"].forEach(["usrname","logintype","blogresult","usernick","upgrade","isvip","state","usernewno","score","userid","sessionid","usertype"],function(e){var t="get"+e.replace(/^\S/,function(e){return e.toUpperCase()});xlQuickLogin[t]=d(e)}),xlQuickLogin.Util=W["default"],xlQuickLogin.req=q.req,xlQuickLogin.login=ce,xlQuickLogin.businessRequest=new z.BusinessRequestExport},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}t.__esModule=!0,t.ClientFeatureApi=t.ClientFeatureApiInitState=undefined;var i=n(12),o=r(i),u=n(150),a=r(u),s=n(40),c=r(s),f=n(151),l=r(f),p=n(198),d=r(p),h=n(0),g=r(h),_=n(213),v=r(_),y=n(177),m=t.ClientFeatureApiInitState={unInit:"unInit",successful:"successful",failed:"failed"};t.ClientFeatureApi=function(){function e(t){(0,g["default"])(this,e),this._initState=m.unInit,this._isClientLogined=!1,this._promiseMap=new d["default"],this._nativeApi=null,this._util=null,this._innerXLQuickLogin=null,this._xll=null,this._util=t.util,this._innerXLQuickLogin=t.innerXLQuickLogin,this._xll=t.xll}return e.prototype.init=function(){function e(){return t.apply(this,arguments)}var t=(0,l["default"])(a["default"].mark(function n(){var e,t,r=this;return a["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e="init",t=this._promiseMap.get(e),t||(t=new c["default"](function(){var t=(0,l["default"])(a["default"].mark(function n(t,i){var o;return a["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:n.prev=0,n.t0=r._initState,n.next=n.t0===m.successful?4:n.t0===m.failed?6:8;break;case 4:return t(),n.abrupt("return");case 6:return i(),n.abrupt("return");case 8:return r._nativeApi=(0,y.getNativeApi)(),n.next=11,r._nativeApi.init();case 11:return n.next=13,r._initAppInfo();case 13:if(!(o=n.sent)){n.next=18;break}return r._bindEvent(),n.next=18,r._syncUserCredentialsFromPlatform();case 18:r._initState=o?m.successful:m.failed,r._promiseMap["delete"](e),t(),n.next=29;break;case 23:n.prev=23,n.t1=n["catch"](0),r._initState=m.failed,r._promiseMap["delete"](e),i(n.t1);case 29:case"end":return n.stop()}},n,r,[[0,23]])}));return function(e,n){return t.apply(this,arguments)}}()),this._promiseMap.set(e,t)),n.abrupt("return",t);case 4:case"end":return n.stop()}},n,this)}));return e}(),e.prototype._initAppInfo=function(){function e(){return t.apply(this,arguments)}var t=(0,l["default"])(a["default"].mark(function n(){var e,t,r;return a["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e=!1,n.next=3,this._nativeApi.getAppInfo();case 3:return t=n.sent,t&&("string"==typeof t.deviceSign&&t.deviceSign.length>0&&(this._util.setCookie("deviceid",t.deviceSign,864e5),window.store.set("deviceid",t.deviceSign),r={appid:t.appId,appname:t.appName,"package":t.noHostAppName},this._util.setCookie("appinfo",(0,o["default"])(r),864e5),window.store.set("appinfo",(0,o["default"])(r)),e=!0),"string"==typeof t.peerId&&t.peerId.length>0&&this._xll.setPeerId(t.peerId)),n.abrupt("return",e);case 7:case"end":return n.stop()}},n,this)}));return e}(),e.prototype._syncUserCredentialsFromPlatform=function(){function e(){return t.apply(this,arguments)}var t=(0,l["default"])(a["default"].mark(function n(){var e,t,r;return a["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,this._nativeApi.getSessionUserInfo();case 2:if(!((e=n.sent)&&this._util.isSessionid(e.sessionId)&&e.userId&&e.userId>0)){n.next=14;break}if(this._isClientLogined=!0,t=this._util.getCookie("userid"),r=this._util.getCookie("sessionid"),t==e.userId&&r==e.sessionId){n.next=12;break}return this._setWebSessionUserInfo(e),n.next=12,this._setWebLogin();case 12:n.next=17;break;case 14:if(!this._xll.isLogined()){n.next=17;break}return n.next=17,this._setWebLogout();case 17:case"end":return n.stop()}},n,this)}));return e}(),e.prototype._bindEvent=function(){var e=this;this._xll.setLoginExt(this._setClientLogin.bind(this)),this._xll.setLogoutExt(this._setClientLogout.bind(this)),this._nativeApi.on("logout",(0,l["default"])(a["default"].mark(function t(){return a["default"].wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e._isSuccessfullyInited){t.next=4;break}return t.abrupt("return");case 4:e._isClientLogined=!1,e._setWebLogout();case 6:case"end":return t.stop()}},t,e)}))),this._nativeApi.on("login",(0,l["default"])(a["default"].mark(function n(){var t;return a["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(e._isSuccessfullyInited){n.next=4;break}return n.abrupt("return");case 4:return e._isClientLogined=!0,n.next=7,e._nativeApi.getSessionUserInfo();case 7:if(!((t=n.sent)&&e._util.isSessionid(t.sessionId)&&t.userId&&t.userId>0)){n.next=14;break}return e._isClientLogined=!0,e._setWebSessionUserInfo(t),n.next=14,e._setWebLogin();case 14:case"end":return n.stop()}},n,e)})))},e.prototype._toWebSessionUserInfo=function(e){var t={userId:"userid",sessionId:"sessionid",loginKey:"loginkey",userNick:"usernick",userName:"usrname",score:"score",upgrade:"upgrade",order:"order",userNewno:"usernewno",userType:"usertype",state:"state"},n={};for(var r in t){var i=e[r];if(void 0!==i){n[t[r]]=i}}return n},e.prototype._setWebSessionUserInfo=function(e){var t=this._toWebSessionUserInfo(e);this._xll.setUserInfo(t)},e.prototype._setWebLogin=function(){return this._innerXLQuickLogin.login()},e.prototype._setWebLogout=function(){return this._innerXLQuickLogin.logout()},e.prototype._setClientLogin=function(){if(this._isSuccessfullyInited&&!this._isClientLogined){var e={};e.userId=this._util.getCookie("userid"),e.loginKey=this._util.getCookie("loginkey"),e.sessionId=this._util.getCookie("sessionid");var t=window.store.get("xl_autologin")||this._util.getCookie("xl_autologin");e.autoLogin=1==t,this._nativeApi.setLoginInfo(e)}},e.prototype._setClientLogout=function(){this._isSuccessfullyInited&&this._isClientLogined&&this._nativeApi.setLoginInfo({userId:"",sessionId:""})},e.prototype.showLoginWindow=function(){function e(){return t.apply(this,arguments)}var t=(0,l["default"])(a["default"].mark(function n(){return a["default"].wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.init();case 2:return e.abrupt("return",this._nativeApi.showLoginWindow());case 3:case"end":return e.stop()}},n,this)}));return e}(),(0,v["default"])(e,[{key:"initState",get:function(){return this.initState}},{key:"_isSuccessfullyInited",get:function(){return this._initState===m.successful}}]),e}()},function(e,t,n){e.exports={"default":n(199),__esModule:!0}},function(e,t,n){n(57),n(50),n(54),n(200),n(206),n(209),n(211),e.exports=n(2).Map},function(e,t,n){"use strict";var r=n(201),i=n(193);e.exports=n(202)("Map",function(e){return function(){return e(this,arguments.length>0?arguments[0]:undefined)}},{get:function(e){var t=r.getEntry(i(this,"Map"),e);return t&&t.v},set:function(e,t){return r.def(i(this,"Map"),0===e?0:e,t)}},r,!0)},function(e,t,n){"use strict";var r=n(8).f,i=n(39),o=n(92),u=n(19),a=n(91),s=n(81),c=n(53),f=n(82),l=n(101),p=n(4),d=n(93).fastKey,h=n(193),g=p?"_s":"size",_=function(e,t){var n,r=d(t);if("F"!==r)return e._i[r];for(n=e._f;n;n=n.n)if(n.k==t)return n};e.exports={getConstructor:function(e,t,n,c){var f=e(function(e,r){a(e,f,t,"_i"),e._t=t,e._i=i(null),e._f=undefined,e._l=undefined,e[g]=0,r!=undefined&&s(r,n,e[c],e)});return o(f.prototype,{clear:function(){for(var e=h(this,t),n=e._i,r=e._f;r;r=r.n)r.r=!0,r.p&&(r.p=r.p.n=undefined),delete n[r.i];e._f=e._l=undefined,e[g]=0},"delete":function(e){var n=h(this,t),r=_(n,e);if(r){var i=r.n,o=r.p;delete n._i[r.i],r.r=!0,o&&(o.n=i),i&&(i.p=o),n._f==r&&(n._f=i),n._l==r&&(n._l=o),n[g]--}return!!r},forEach:function(e){h(this,t);for(var n,r=u(e,arguments.length>1?arguments[1]:undefined,3);n=n?n.n:this._f;)for(r(n.v,n.k,this);n&&n.r;)n=n.p},has:function(e){return!!_(h(this,t),e)}}),p&&r(f.prototype,"size",{get:function(){return h(this,t)[g]}}),f},def:function(e,t,n){var r,i,o=_(e,t);return o?o.v=n:(e._l=o={i:i=d(t,!0),k:t,v:n,p:r=e._l,n:undefined,r:!1},e._f||(e._f=o),r&&(r.n=o),e[g]++,"F"!==i&&(e._i[i]=o)),e},getEntry:_,setStrong:function(e,t,n){c(e,t,function(e,n){this._t=h(e,t),this._k=n,this._l=undefined},function(){for(var e=this,t=e._k,n=e._l;n&&n.r;)n=n.p;return e._t&&(e._l=n=n?n.n:e._t._f)?"keys"==t?f(0,n.k):"values"==t?f(0,n.v):f(0,[n.k,n.v]):(e._t=undefined,f(1))},n?"entries":"values",!n,!0),l(t)}}},function(e,t,n){"use strict";var r=n(1),i=n(7),o=n(93),u=n(14),a=n(9),s=n(92),c=n(81),f=n(91),l=n(6),p=n(27),d=n(8).f,h=n(203)(0),g=n(4);e.exports=function(e,t,n,_,v,y){var m=r[e],I=m,E=v?"set":"add",b=I&&I.prototype,w={};return g&&"function"==typeof I&&(y||b.forEach&&!u(function(){(new I).entries().next()}))?(I=t(function(t,n){f(t,I,e,"_c"),t._c=new m,n!=undefined&&c(n,v,t[E],t)}),h("add,clear,delete,forEach,get,has,set,keys,values,entries,toJSON".split(","),function(e){var t="add"==e||"set"==e;e in b&&(!y||"clear"!=e)&&a(I.prototype,e,function(n,r){if(f(this,I,e),!t&&y&&!l(n))return"get"==e&&undefined;var i=this._c[e](0===n?0:n,r);return t?this:i})}),y||d(I.prototype,"size",{get:function(){return this._c.size}})):(I=_.getConstructor(t,e,v,E),s(I.prototype,n),o.NEED=!0),p(I,e),w[e]=I,i(i.G+i.W+i.F,w),y||_.setStrong(I,e,v),I}},function(e,t,n){var r=n(19),i=n(41),o=n(32),u=n(45),a=n(204);e.exports=function(e,t){var n=1==e,s=2==e,c=3==e,f=4==e,l=6==e,p=5==e||l,d=t||a;return function(t,a,h){for(var g,_,v=o(t),y=i(v),m=r(a,h,3),I=u(y.length),E=0,b=n?d(t,I):s?d(t,0):undefined;I>E;E++)if((p||E in y)&&(g=y[E],_=m(g,E,v),e))if(n)b[E]=_;else if(_)switch(e){case 3:return!0;case 5:return g;case 6:return E;case 2:b.push(g)}else if(f)return!1;return l?-1:c||f?f:b}}},function(e,t,n){var r=n(205);e.exports=function(e,t){return new(r(e))(t)}},function(e,t,n){var r=n(6),i=n(103),o=n(3)("species");e.exports=function(e){var t;return i(e)&&(t=e.constructor,"function"!=typeof t||t!==Array&&!i(t.prototype)||(t=undefined),r(t)&&null===(t=t[o])&&(t=undefined)),t===undefined?Array:t}},function(e,t,n){var r=n(7);r(r.P+r.R,"Map",{toJSON:n(207)("Map")})},function(e,t,n){var r=n(61),i=n(208);e.exports=function(e){return function(){if(r(this)!=e)throw TypeError(e+"#toJSON isn't generic");return i(this)}}},function(e,t,n){var r=n(81);e.exports=function(e,t){var n=[];return r(e,!1,n.push,n,t),n}},function(e,t,n){n(210)("Map")},function(e,t,n){"use strict";var r=n(7);e.exports=function(e){r(r.S,e,{of:function(){for(var e=arguments.length,t=new Array(e);e--;)t[e]=arguments[e];return new this(t)}})}},function(e,t,n){n(212)("Map")},function(e,t,n){"use strict";var r=n(7),i=n(21),o=n(19),u=n(81);e.exports=function(e){r(r.S,e,{from:function(e){var t,n,r,a,s=arguments[1];return i(this),t=s!==undefined,t&&i(s),e==undefined?new this:(n=[],t?(r=0,a=o(s,arguments[2],2),u(e,!1,function(e){n.push(a(e,r++))})):u(e,!1,n.push,n),new this(n))}})}},function(e,t,n){"use strict";t.__esModule=!0;var r=n(162),i=function(e){return e&&e.__esModule?e:{"default":e}}(r);t["default"]=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),(0,i["default"])(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}()},function(e,t,n){"use strict";t.__esModule=!0,t.BusinessRequestExport=undefined;var r=n(0),i=function(e){return e&&e.__esModule?e:{"default":e}}(r),o=n(178);t.BusinessRequestExport=function(){function e(){(0,i["default"])(this,e),this._businessRequest=new o.BusinessRequest}return e.prototype.post=function(e,t,n,r){return this._businessRequest.post(e,t,n,r)},e}()}]);